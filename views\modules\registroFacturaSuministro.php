<?php
//session_start();
	if(!$_SESSION["validar"])
		{	header("location:index.php?action=ingresar");	exit();		}/**/
	if(isset($_GET["action"]))
		{	if($_GET["action"] == "okFs")	{	echo "Registro Exitoso";	}	}
		/*ini_set('display_errors', 1);
		ini_set('display_startup_errors', 1);
		error_reporting(E_ALL);*/
?>
<script type="text/javascript">
	// ------CANCELA TODO LOS PRODUCTOS PEDIDO EN LA MESA
		function cancelarPedido()
			{
				if (confirm('confirme si elimina el pedido?'))
					{		setTimeout('location.href="views/modules/ajaxCancelarFactura.php"',500);		}
			//alert("Entro en el else de placa");
				/*$("#destino").load("views/modules/ajaxPermiso.php", function()
					{  	alert("recibidos los datos por ajax Monte");    		});*/
			}
	// ------CANCELA TODO LOS PRODUCTOS  MESA fin
	//------------------------- Nombre Buscar------------------------
	 function buscarPlaca1(inputString)
	 	{
		 if(inputString.length == 0)
		 	{	// Hide the suggestion box.
			 $('#suggestions').hide();
		 	}
		 else
		  	{	//alert("Entro en el else de placa");
			 $("#doss").load("views/modules/ajaxBuscarSuministroCompra.php", {placa: $("#dedo").val()}, function(){	});
		 	}
	 	}
	//------------------------- codigo Buscar-----------------------
		function buscarC(inputString)
		 {
			if(inputString.length == 0)
			 {	// Hide the suggestion box.
				$('#suggestions').hide();
			 }
			else
			  {	//alert("Entro en el else de placa");
				$("#dos").load("views/modules/ajaxBuscarSuministroCod.php", {placa: $("#suministros").val()}, function(){ });
			 }
		 }
	//------------------ Nombre cerrar-------------------
	// ------FACTURAR--------------
	 function guardarSuministro()
		{
		 var compra = document.calculo.compra.value;
		 var valorT = document.calculo.valorT.value;
		 var totalpagar = document.calculo.totalpagar.value;
		 var diferencia = parseInt(totalpagar)-parseInt(valorT);
		 if (diferencia==0)
		 	{
		 	 if (confirm('confirme Guardar Facturar?'+compra))
				{	//setTimeout('location.href="views/modules/ajaxFactura.php"',500);
					var compra = document.calculo.compra.value;
					//alert("enviando factura # "+diferencia+" nose");
					$("#destino").load("views/modules/ajaxCompraSuministro1.php", {}, function(){	});
				}
		 	}
		 else {alert("Tiene una Diferncia entre los producto y el valor de la factura de :"+diferencia);}
		}
	// ------FACTURAR FIN//
	// Función autocompletar --------------------------
	   function autocompletar() {
			 var minimo_letras = 2; // minimo letras visibles en el autocompletar
			 var palabra = $('#nombrepr').val();
			 //Contamos el valor del input mediante una condicional
			 if (palabra.length >= minimo_letras) {
			 $.ajax({
			 url: 'views/modules/ajaxAutocompleteS.php',
			 type: 'POST',
			 data: {palabra:palabra},
			 success:function(data){
			 $('#lista_id').show();
			 $('#lista_id').html(data);
			 }
			 });
			 } else {
			 //ocultamos la lista
			 $('#lista_id').hide();
			 }
		}

		// Funcion Mostrar valores
		function set_item(opciones,id) {
		 // Cambiar el valor del formulario input
		 $('#nombrepr').val(opciones);
		 $('#suministros').val(id);
		 buscarC(id);
		 // ocultar lista de proposiciones
		 $('#lista_id').hide();
		}
	// -------------------------------------------------
</script>
<style type="text/css">
	.etiqueta {
	 width: 120px;
	 float: left;
	 line-height: 28px;
	 font-size: 20px;
	}
	.input_container {
	 height: 30px;
	 float: left;
	}
	.input_container input {
	 height: 20px;
	 width: 260px;
	 padding: 3px;
	 border: 1px solid #cccccc;
	 border-radius: 0;
	}
	.input_container ul {
	 width: 270px;
	 border: 1px solid #eaeaea;
	 position: absolute;
	 z-index: 9;
	 background: #f3f3f3;
	 list-style: none;
	 margin-left: 5px;
	margin-top: -3px;
	}
	.input_container ul li {
	 padding: 2px;
	}
	.input_container ul li:hover {
	 background: #eaeaea;
	}
	#country_list_id {
	 display: none;
	}
</style>
<!--$_session['idcompra']  	$_session['proveedor']	$_session['valorT']	$_session['facturaN']     registroCsuministroController  -->
<div class="row">
	<div class="col-md-8 col-lg-9">
	  	<form name="calculo" method="post" >
	  		<div ><h3>
	  			Fecha Factura: <?php echo $_SESSION["fecha_factura"];?> <br>
	  			No factura :<?php echo $_SESSION["facturaN"];?>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Valor Factura: <?php echo $_SESSION["valorT"];?>
	  		</h3></div><br>
			<input type="hidden" name="compra" id="compra" value="<?=$_SESSION["proveedor_id"];?>" required>
			<input type="hidden" name="valorT" id="valorT" value="<?=$_SESSION["valorT"];?>" required>
		  	<h2>DETALLE DE FACTURA</h2>
		  	<div class="table-responsive">
				<table border="1" class="table table-hover">
					<thead>
						<tr>
							<th> CODIGO</th>
							<th>NOMBRE</th>
							<th>CANTIDAD</th>
							<th>PRECIO</th>
							<th>DESC%</th>
							<th>SIN IVA</th>
							<th>IVA%</th>
							<th>VALOR Ud</th>
							<th>TOTAL PAGAR</th>
							<th></th>
						</tr>
					</thead>
					<tbody>
						<?php
							$vistaFsuministro = new controllerFacturaCompraSuministro();
							$vistaFsuministro -> vistaFacturaSumistroPController();
							$vistaFsuministro -> borrarFacturaSumistroPController();
							//$vistaFsuministro -> actualizarFacturaSumistroController();
						?>
					</tbody>
				</table>
			</div>
			<span id="destino" > </span>
			<a class="btn btn-primary btn-lg"  role="button" onclick="guardarSuministro();" name="guardar" value="Guardar">Guardar &raquo;</a>
			<a class="btn btn-primary btn-lg"  role="button" onclick="cancelarPedido();" name="cancelar" value="cancelar">Cancelar &raquo;</a>
			<!--<input type="submit"  value="Facturar">-->
		</form>
	</div>
    <div class="col-md-4 col-lg-3" style="background-color:#FFC; padding:12px">

		<h2>AGREGAR SUMINISTRO</h2><br>
		<form method="post">
			<input type="hidden" name="nfactura" value="<?=$_SESSION["ver"];?>" required readonly> <br>
			<label>Codigo :&nbsp;&nbsp;&nbsp;&nbsp;</label><input type="text" name="suministros" id="suministros" autofocus="autofocus" onkeyup="buscarC(this.value);" required > <br><a href="registroSuministro" target="_blank"> Registrar Suministro</a><br>
			 <label>Cantidad :&nbsp;&nbsp;&nbsp;&nbsp;</label> <input type="text" name="cantidad" required> <br>
			  <div id="dos"></div><br>
			  <input type="submit" value="Enviar"><br>
				<H5>----Buscar Codigo-----</H5>
			<div class="input_container">
				<label>Nombre:</label>
				<input type="text" autocomplete="off" id="nombrepr" placeholder="SUMINISTRO" name="nombrepr" onkeyup="autocompletar()" >
				<ul id="lista_id"></ul><br>
			</div>
			&nbsp;&nbsp;&nbsp;&nbsp;
			<br>
			<div id="doss"></div>
			<br>
			<?php
			$registroFacturaSuministro = new controllerFacturaCompraSuministro();
			$registroFacturaSuministro -> registroFacturaSumistroPController();
			//	echo "<script>alert('factura este es el numero".$_SESSION["facturaN"]." que pasa')</script>";
		?>
			<br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br>
		</form>
    </div>
</div>