<?php

session_start();

if(!isset($_SESSION["validar"]) || !$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "models/conexion.php";

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Solucionar: Problemas con Pedidos</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
</head>
<body>

<div class="container">
    <h2>🔧 Solucionar: Problemas con Pedidos</h2>
    <hr>
    
    <div class="alert alert-warning">
        <h4>⚠️ Problemas a Solucionar:</h4>
        <ol>
            <li><strong>Trigger de historial:</strong> Error usuario_id NULL</li>
            <li><strong>Productos pendientes:</strong> Quedan en próximo pedido borrador</li>
            <li><strong>Cancelación de mesa:</strong> Verificar funcionamiento</li>
        </ol>
    </div>
    
    <?php
    if (isset($_POST['solucionar_todo'])) {
        try {
            $stmt = Conexion::conectar();
            $stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $stmt->beginTransaction();
            
            echo "<div class='alert alert-info'>";
            echo "<h5>🔄 Aplicando todas las soluciones...</h5>";
            echo "</div>";
            
            // SOLUCIÓN 1: Corregir trigger de historial
            echo "<p><strong>1. Corrigiendo trigger de historial...</strong></p>";
            
            // Eliminar trigger existente
            $stmt->exec("DROP TRIGGER IF EXISTS pedidos_historial_insert");
            echo "<p class='text-success'>✅ Trigger anterior eliminado</p>";
            
            // Crear trigger corregido
            $trigger_sql = "
            CREATE TRIGGER pedidos_historial_insert 
            AFTER UPDATE ON pedidos 
            FOR EACH ROW 
            BEGIN 
                IF OLD.estado != NEW.estado THEN 
                    INSERT INTO pedidos_historial (pedido_id, estado_anterior, estado_nuevo, usuario_id, observaciones) 
                    VALUES (
                        NEW.id, 
                        OLD.estado, 
                        NEW.estado, 
                        COALESCE(NEW.usuario_envio, NEW.usuario_entrega, 1),
                        CONCAT('Cambio de estado de ', OLD.estado, ' a ', NEW.estado)
                    ); 
                END IF; 
            END";
            
            $stmt->exec($trigger_sql);
            echo "<p class='text-success'>✅ Trigger corregido creado</p>";
            
            // SOLUCIÓN 2: Limpiar productos huérfanos
            echo "<p><strong>2. Limpiando productos huérfanos...</strong></p>";
            
            // Buscar productos asociados a pedidos que no existen o están en estado incorrecto
            $consulta_huerfanos = "
                DELETE pvm FROM producto_vendido_mesa pvm
                LEFT JOIN pedidos p ON pvm.pedidos_id = p.id
                WHERE p.id IS NULL OR p.estado IN ('facturado', 'cancelado')
            ";
            $stmt->exec($consulta_huerfanos);
            $huerfanos_eliminados = $stmt->rowCount();
            echo "<p class='text-success'>✅ {$huerfanos_eliminados} productos huérfanos eliminados</p>";
            
            // SOLUCIÓN 3: Verificar y corregir pedidos duplicados
            echo "<p><strong>3. Verificando pedidos duplicados...</strong></p>";
            
            $consulta_duplicados = "
                SELECT mesa_id, COUNT(*) as cantidad
                FROM pedidos 
                WHERE estado = 'borrador'
                GROUP BY mesa_id
                HAVING cantidad > 1
            ";
            $stmt_duplicados = $stmt->prepare($consulta_duplicados);
            $stmt_duplicados->execute();
            $duplicados = $stmt_duplicados->fetchAll(PDO::FETCH_ASSOC);
            
            if (count($duplicados) > 0) {
                foreach ($duplicados as $duplicado) {
                    $mesa_id = $duplicado['mesa_id'];
                    
                    // Mantener solo el pedido más reciente y cancelar los demás
                    $consulta_cancelar = "
                        UPDATE pedidos 
                        SET estado = 'cancelado' 
                        WHERE mesa_id = ? AND estado = 'borrador' 
                        AND id NOT IN (
                            SELECT * FROM (
                                SELECT id FROM pedidos 
                                WHERE mesa_id = ? AND estado = 'borrador' 
                                ORDER BY fecha_pedido DESC 
                                LIMIT 1
                            ) as temp
                        )
                    ";
                    $stmt_cancelar = $stmt->prepare($consulta_cancelar);
                    $stmt_cancelar->execute([$mesa_id, $mesa_id]);
                    
                    echo "<p class='text-warning'>⚠️ Mesa {$mesa_id}: Cancelados pedidos borrador duplicados</p>";
                }
            } else {
                echo "<p class='text-success'>✅ No se encontraron pedidos borrador duplicados</p>";
            }
            
            $stmt->commit();
            
            echo "<div class='alert alert-success'>";
            echo "<h5>🎉 ¡Todas las soluciones aplicadas exitosamente!</h5>";
            echo "<p>Los problemas han sido corregidos. Ahora el sistema debería funcionar correctamente.</p>";
            echo "</div>";
            
        } catch (Exception $e) {
            if ($stmt->inTransaction()) {
                $stmt->rollBack();
            }
            echo "<div class='alert alert-danger'>";
            echo "<h5>❌ Error al aplicar soluciones:</h5>";
            echo "<p>" . $e->getMessage() . "</p>";
            echo "</div>";
        }
    }
    ?>
    
    <div class="panel panel-danger">
        <div class="panel-heading">
            <h3 class="panel-title">🔍 Problema 1: Trigger de Historial</h3>
        </div>
        <div class="panel-body">
            <h5>❌ Error:</h5>
            <p><code>Column 'usuario_id' cannot be null</code></p>
            
            <h5>✅ Solución:</h5>
            <p>Modificar el trigger para usar <code>COALESCE(NEW.usuario_envio, NEW.usuario_entrega, 1)</code></p>
            
            <?php
            try {
                $stmt = Conexion::conectar()->prepare("SHOW TRIGGERS LIKE 'pedidos'");
                $stmt->execute();
                $triggers = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                $trigger_correcto = false;
                foreach ($triggers as $trigger) {
                    if ($trigger['Trigger'] == 'pedidos_historial_insert') {
                        $trigger_correcto = true;
                        break;
                    }
                }
                
                if ($trigger_correcto) {
                    echo "<div class='alert alert-info'>ℹ️ Trigger encontrado - necesita verificación</div>";
                } else {
                    echo "<div class='alert alert-warning'>⚠️ Trigger no encontrado - necesita creación</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-warning">
        <div class="panel-heading">
            <h3 class="panel-title">🔍 Problema 2: Productos Quedan Pendientes</h3>
        </div>
        <div class="panel-body">
            <h5>❌ Problema:</h5>
            <p>Al enviar un pedido, se crea automáticamente un nuevo pedido borrador y los productos quedan asociados a él.</p>
            
            <h5>✅ Solución:</h5>
            <p>Limpiar productos huérfanos y evitar la creación automática de pedidos borrador innecesarios.</p>
            
            <?php
            try {
                // Verificar productos huérfanos
                $stmt_huerfanos = Conexion::conectar()->prepare("
                    SELECT COUNT(*) as cantidad
                    FROM producto_vendido_mesa pvm
                    LEFT JOIN pedidos p ON pvm.pedidos_id = p.id
                    WHERE p.id IS NULL OR p.estado IN ('facturado', 'cancelado')
                ");
                $stmt_huerfanos->execute();
                $huerfanos = $stmt_huerfanos->fetch();
                
                if ($huerfanos['cantidad'] > 0) {
                    echo "<div class='alert alert-warning'>⚠️ {$huerfanos['cantidad']} productos huérfanos encontrados</div>";
                } else {
                    echo "<div class='alert alert-success'>✅ No hay productos huérfanos</div>";
                }
                
                // Verificar pedidos borrador duplicados
                $stmt_duplicados = Conexion::conectar()->prepare("
                    SELECT mesa_id, COUNT(*) as cantidad
                    FROM pedidos 
                    WHERE estado = 'borrador'
                    GROUP BY mesa_id
                    HAVING cantidad > 1
                ");
                $stmt_duplicados->execute();
                $duplicados = $stmt_duplicados->fetchAll(PDO::FETCH_ASSOC);
                
                if (count($duplicados) > 0) {
                    echo "<div class='alert alert-warning'>⚠️ " . count($duplicados) . " mesas con pedidos borrador duplicados</div>";
                    echo "<table class='table table-condensed'>";
                    echo "<thead><tr><th>Mesa</th><th>Pedidos Borrador</th></tr></thead>";
                    echo "<tbody>";
                    foreach ($duplicados as $duplicado) {
                        echo "<tr>";
                        echo "<td>Mesa {$duplicado['mesa_id']}</td>";
                        echo "<td>{$duplicado['cantidad']} pedidos</td>";
                        echo "</tr>";
                    }
                    echo "</tbody></table>";
                } else {
                    echo "<div class='alert alert-success'>✅ No hay pedidos borrador duplicados</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">🔍 Problema 3: Cancelación de Mesa</h3>
        </div>
        <div class="panel-body">
            <h5>✅ Estado:</h5>
            <p>La función de cancelación ya está implementada correctamente en <code>cancelarMesaConPedidosModel()</code></p>
            
            <?php
            try {
                // Verificar función de cancelación
                $reflection = new ReflectionClass('Datos');
                $methods = $reflection->getMethods();
                
                $cancelacion_disponible = false;
                foreach ($methods as $method) {
                    if ($method->getName() == 'cancelarMesaConPedidosModel') {
                        $cancelacion_disponible = true;
                        break;
                    }
                }
                
                if ($cancelacion_disponible) {
                    echo "<div class='alert alert-success'>✅ Función de cancelación disponible</div>";
                } else {
                    echo "<div class='alert alert-warning'>⚠️ Función de cancelación no encontrada</div>";
                }
                
                // Verificar pedidos cancelados recientes
                $stmt_cancelados = Conexion::conectar()->prepare("
                    SELECT COUNT(*) as cantidad
                    FROM pedidos 
                    WHERE estado = 'cancelado' 
                    AND fecha_entrega >= DATE_SUB(NOW(), INTERVAL 1 DAY)
                ");
                $stmt_cancelados->execute();
                $cancelados = $stmt_cancelados->fetch();
                
                echo "<p><strong>Pedidos cancelados en las últimas 24h:</strong> {$cancelados['cantidad']}</p>";
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-success">
        <div class="panel-heading">
            <h3 class="panel-title">🛠️ Aplicar Todas las Soluciones</h3>
        </div>
        <div class="panel-body">
            <h5>🔧 Acción Completa:</h5>
            <p>Hacer clic en el botón para aplicar todas las correcciones automáticamente.</p>
            
            <form method="POST">
                <button type="submit" name="solucionar_todo" class="btn btn-success btn-lg">
                    🔧 Solucionar Todos los Problemas
                </button>
            </form>
            
            <div class="alert alert-info" style="margin-top: 15px;">
                <h6>💡 Qué hace esta solución completa:</h6>
                <ol>
                    <li><strong>Corrige el trigger de historial</strong> para manejar valores NULL</li>
                    <li><strong>Limpia productos huérfanos</strong> asociados a pedidos inexistentes</li>
                    <li><strong>Cancela pedidos borrador duplicados</strong> manteniendo solo el más reciente</li>
                    <li><strong>Verifica la función de cancelación</strong> de mesas</li>
                </ol>
            </div>
            
            <div class="alert alert-warning">
                <h6>⚠️ Importante:</h6>
                <p>Esta operación modificará la base de datos. Se recomienda hacer una copia de seguridad antes de proceder.</p>
            </div>
        </div>
    </div>
    
    <hr>
    <div class="row">
        <div class="col-md-3">
            <a href="index.php?action=debug_problemas_pedidos" class="btn btn-primary btn-block">🔙 Debug Problemas</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=mesa" class="btn btn-info btn-block">🪑 Ver Mesas</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=registroPmesa&ida=10" class="btn btn-warning btn-block">🧪 Test Mesa 10</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=diagnostico" class="btn btn-success btn-block">📊 Diagnóstico</a>
        </div>
    </div>
</div>

</body>
</html>
