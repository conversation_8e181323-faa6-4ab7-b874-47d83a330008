<?php
	$registro = new ControllerDepartamentoCiudad();
	$departamento =$registro -> listaDepartamentosController();
	$registro -> registroCiudadController();
	if(isset($_GET["action"]))
		{	if($_GET["action"] == "okC")
				{	echo "Registro Exitoso";	}
		}
?>


<h1>REGISTRO DE CIUDAD</h1>
<form method="post">
	<?php 
		# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  Roles  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
			if($departamento=="error")
				{	echo "debe registrar el departamento"; }
			else{
					echo "<label>Id Departamento </label>";
					$resultt='<select name="departamento"  id="departamento">';
					$resultt.=' <option value="-1">Seleccione una departamento</option>';
					foreach ($departamento as $row => $item)
					 	{	$resultt.=' <option value="'.$item["id"].'">'.$item["nombre"].'</option>';	}	
					 $resultt.='</select>';
					 echo $resultt." <br>";
				}
		# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  End Roles  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
 	?>
	<!--input type="text" placeholder="idDepartamento" name="idDepartamentoCiudadRegistro" required--><br>	

	<label> Nombre Ciudad : </label>
	<input type="text" placeholder="nombre" name="nombreCiudadRegistro" required><br>	
	
	<input type="submit" value="Enviar">

</form>


