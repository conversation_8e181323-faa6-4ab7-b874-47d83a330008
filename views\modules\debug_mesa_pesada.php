<?php
// Debug para mesas con muchos pedidos
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('max_execution_time', 300); // 5 minutos
ini_set('memory_limit', '512M');

session_start();
if (!isset($_SESSION["usuario"])) {
    $_SESSION["usuario"] = 1;
    $_SESSION["tipo_usuario"] = 1;
    $_SESSION["perfil"] = "administrador";
}

require_once "../../models/conexion.php";
require_once "../../models/crudFacturaAja.php";

$mesaId = isset($_GET['mesa']) ? $_GET['mesa'] : 10;

echo "<h1>🔍 Debug Mesa con Muchos Pedidos - Mesa $mesaId</h1>";

try {
    $db = Conexion::conectar();
    
    echo "<h3>📊 Análisis de la Mesa</h3>";
    
    // 1. Contar pedidos por estado
    $sql = "SELECT estado, COUNT(*) as cantidad FROM pedidos WHERE mesa_id = ? GROUP BY estado ORDER BY cantidad DESC";
    $stmt = $db->prepare($sql);
    $stmt->bindParam(1, $mesaId, PDO::PARAM_INT);
    $stmt->execute();
    $estadosPedidos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<div class='alert alert-info'>";
    echo "<h4>📋 Pedidos por Estado:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Estado</th><th>Cantidad</th></tr>";
    $totalPedidos = 0;
    foreach ($estadosPedidos as $estado) {
        echo "<tr><td>{$estado['estado']}</td><td>{$estado['cantidad']}</td></tr>";
        $totalPedidos += $estado['cantidad'];
    }
    echo "</table>";
    echo "<p><strong>Total Pedidos:</strong> $totalPedidos</p>";
    echo "</div>";
    
    // 2. Productos para facturar
    echo "<h3>💰 Productos para Facturar</h3>";
    $tiempoInicio = microtime(true);
    
    $productosFacturar = DatosFacturaAja::obtenerProductosParaFacturar($mesaId);
    
    $tiempoFin = microtime(true);
    $tiempoConsulta = round(($tiempoFin - $tiempoInicio) * 1000, 2);
    
    echo "<div class='alert alert-warning'>";
    echo "<h4>⏱️ Rendimiento de Consulta:</h4>";
    echo "<p><strong>Tiempo:</strong> {$tiempoConsulta}ms</p>";
    echo "<p><strong>Productos encontrados:</strong> " . count($productosFacturar) . "</p>";
    echo "</div>";
    
    if (!empty($productosFacturar)) {
        // Calcular total
        $totalReal = 0;
        $totalDescuento = 0;
        $contadorProductos = 0;
        
        foreach ($productosFacturar as $producto) {
            $descuento = ($producto['preciopr'] * ($producto['descuentopvm'] / 100));
            $precioConDescuento = $producto['preciopr'] - $descuento;
            $subtotal = $precioConDescuento * $producto['cantidadpvm'];
            $totalReal += $subtotal;
            $totalDescuento += $descuento * $producto['cantidadpvm'];
            $contadorProductos++;
        }
        
        echo "<div class='alert alert-success'>";
        echo "<h4>💰 Resumen Financiero:</h4>";
        echo "<p><strong>Total Bruto:</strong> $" . number_format($totalReal + $totalDescuento) . "</p>";
        echo "<p><strong>Descuentos:</strong> -$" . number_format($totalDescuento) . "</p>";
        echo "<p><strong>Total Neto:</strong> $" . number_format($totalReal) . "</p>";
        echo "<p><strong>Productos:</strong> $contadorProductos</p>";
        echo "</div>";
        
        // 3. Test de facturación optimizada
        echo "<h3>🚀 Test de Facturación Optimizada</h3>";
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
        echo "<p>Esta facturación usará un proceso optimizado para mesas con muchos pedidos:</p>";
        echo "<ul>";
        echo "<li>✅ Procesamiento por lotes</li>";
        echo "<li>✅ Transacciones optimizadas</li>";
        echo "<li>✅ Timeout extendido</li>";
        echo "<li>✅ Memoria aumentada</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<button onclick='facturarOptimizada()' style='background: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; margin: 10px;'>";
        echo "🚀 Facturar Optimizada";
        echo "</button>";
        
        echo "<button onclick='analizarPedidos()' style='background: #17a2b8; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; margin: 10px;'>";
        echo "🔍 Analizar Pedidos Detallado";
        echo "</button>";
        
    } else {
        echo "<div class='alert alert-warning'>⚠️ No hay productos para facturar</div>";
    }
    
    // 4. Mostrar últimos pedidos
    echo "<h3>📋 Últimos 10 Pedidos</h3>";
    $sql = "SELECT p.*, COUNT(pvm.productos_id) as productos 
            FROM pedidos p 
            LEFT JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id 
            WHERE p.mesa_id = ? 
            GROUP BY p.id 
            ORDER BY p.fecha_pedido DESC 
            LIMIT 10";
    $stmt = $db->prepare($sql);
    $stmt->bindParam(1, $mesaId, PDO::PARAM_INT);
    $stmt->execute();
    $ultimosPedidos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($ultimosPedidos)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Número</th><th>Estado</th><th>Productos</th><th>Fecha</th></tr>";
        foreach ($ultimosPedidos as $pedido) {
            $colorFila = '';
            switch($pedido['estado']) {
                case 'facturado': $colorFila = 'background-color: #d4edda;'; break;
                case 'enviado': $colorFila = 'background-color: #fff3cd;'; break;
                case 'entregado': $colorFila = 'background-color: #d1ecf1;'; break;
                case 'borrador': $colorFila = 'background-color: #f8d7da;'; break;
            }
            echo "<tr style='$colorFila'>";
            echo "<td>{$pedido['id']}</td>";
            echo "<td>{$pedido['numero_pedido']}</td>";
            echo "<td>{$pedido['estado']}</td>";
            echo "<td>{$pedido['productos']}</td>";
            echo "<td>{$pedido['fecha_pedido']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<div id='resultado' style='margin-top: 20px; padding: 15px; background: #fff; border: 1px solid #ddd; border-radius: 5px;'></div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ Error: " . $e->getMessage() . "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<br><a href='index.php?action=registroPmesa&ida=$mesaId' style='background: #007bff; color: white; padding: 10px; text-decoration: none;'>🔙 Volver a Mesa $mesaId</a>";
?>

<script>
function facturarOptimizada() {
    const totalReal = <?=$totalReal ?? 0?>;
    
    if (totalReal === 0) {
        alert('No hay productos para facturar');
        return;
    }
    
    // Distribución automática de pago
    const efectivo = Math.floor(totalReal * 0.6);
    const tarjeta = Math.floor(totalReal * 0.4);
    
    const datos = {
        efectivo: efectivo,
        tarjeta: tarjeta,
        nequi: 0,
        daviplata: 0,
        bancolombia: 0,
        pago: 1,
        pcedula: '12345678',
        totalDescuento: 0,
        total: totalReal,
        propina: 0,
        mesa: <?=$mesaId?>,
        tipoTarjeta: 'credito',
        optimizada: true // Flag para proceso optimizado
    };
    
    if (confirm('¿Facturar mesa con ' + <?=count($productosFacturar ?? [])?> + ' productos?\n\nTotal: $' + totalReal.toLocaleString() + '\nEfectivo: $' + efectivo.toLocaleString() + '\nTarjeta: $' + tarjeta.toLocaleString())) {
        
        document.getElementById('resultado').innerHTML = '<div style="color: blue; font-weight: bold;">⏳ Procesando facturación optimizada... Esto puede tomar unos minutos.</div>';
        
        // Timeout extendido para mesas pesadas
        fetch('ajaxFactura.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams(datos),
            signal: AbortSignal.timeout(300000) // 5 minutos
        })
        .then(response => response.text())
        .then(text => {
            console.log('📄 Respuesta facturación optimizada:', text);
            
            let html = '<h4>🚀 Resultado Facturación Optimizada:</h4>';
            html += '<pre style="background: #f8f9fa; padding: 10px; border-radius: 5px; white-space: pre-wrap; max-height: 300px; overflow-y: auto;">' + text + '</pre>';
            
            if (text.includes('success') || text.includes('Pago realizado exitosamente')) {
                html += '<div style="color: green; font-weight: bold; font-size: 18px;">✅ ÉXITO: Facturación completada</div>';
                html += '<p>La mesa debería estar libre ahora. <a href="index.php?action=registroPmesa&ida=' + <?=$mesaId?> + '">🔄 Verificar mesa</a></p>';
            } else if (text.includes('error') || text.includes('Error')) {
                html += '<div style="color: red; font-weight: bold;">❌ ERROR en facturación</div>';
            } else {
                html += '<div style="color: orange; font-weight: bold;">⚠️ Respuesta inesperada</div>';
            }
            
            document.getElementById('resultado').innerHTML = html;
        })
        .catch(error => {
            console.error('❌ Error:', error);
            let errorMsg = '❌ Error de conexión: ' + error.message;
            if (error.name === 'TimeoutError') {
                errorMsg = '⏰ Timeout: La facturación está tomando más tiempo del esperado. Verifica manualmente si se completó.';
            }
            document.getElementById('resultado').innerHTML = '<div style="color: red; font-weight: bold;">' + errorMsg + '</div>';
        });
    }
}

function analizarPedidos() {
    document.getElementById('resultado').innerHTML = '<div style="color: blue;">🔍 Analizando pedidos detalladamente...</div>';
    
    fetch('debug_mesa_pesada.php?mesa=<?=$mesaId?>&accion=analizar', {
        method: 'GET'
    })
    .then(response => response.text())
    .then(text => {
        // Mostrar análisis detallado
        document.getElementById('resultado').innerHTML = '<h4>📊 Análisis Detallado:</h4>' + text;
    })
    .catch(error => {
        document.getElementById('resultado').innerHTML = '<div style="color: red;">❌ Error en análisis: ' + error.message + '</div>';
    });
}
</script>

<style>
.alert {
    padding: 15px;
    margin: 15px 0;
    border-radius: 5px;
    border: 1px solid transparent;
}
.alert-success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
.alert-warning { background-color: #fff3cd; border-color: #ffeaa7; color: #856404; }
.alert-info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
.alert-danger { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
table { width: 100%; margin: 10px 0; }
th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
th { background-color: #f2f2f2; }
</style>
