<?php
session_start();

// Simular sesión de administrador para pruebas
if (!isset($_SESSION["usuario"])) {
    $_SESSION["usuario"] = 1;
    $_SESSION["tipo_usuario"] = 1; // Administrador
    $_SESSION["mesa"] = 47; // Mesa de prueba
}

echo "<h2>🧪 Test de Cancelación de Pedidos y Productos</h2>";
echo "<p><strong>Mesa:</strong> " . $_SESSION["mesa"] . "</p>";
echo "<p><strong>Usuario:</strong> " . $_SESSION["usuario"] . " (Tipo: " . $_SESSION["tipo_usuario"] . ")</p>";

// Incluir archivos necesarios
require_once "../../models/conexion.php";
require_once "../../controllers/controllerEstadoPedidos.php";
require_once "../../models/crudEstadoPedidos.php";

echo "<h3>1. 📋 Pedidos Actuales de la Mesa</h3>";

try {
    $stmt = Conexion::conectar()->prepare("
        SELECT p.id, p.numero_pedido, p.estado, p.fecha_pedido,
               COUNT(ppm.productos_id) as total_productos
        FROM pedidos p
        LEFT JOIN pedido_productos_mesa ppm ON p.id = ppm.pedidos_id
        WHERE p.mesa_id = ? AND p.estado != 'facturado'
        GROUP BY p.id
        ORDER BY p.fecha_pedido DESC
    ");
    $stmt->bindParam(1, $_SESSION["mesa"], PDO::PARAM_INT);
    $stmt->execute();
    $pedidos = $stmt->fetchAll();

    if (!empty($pedidos)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Número</th><th>Estado</th><th>Productos</th><th>Fecha</th><th>Acciones</th></tr>";
        
        foreach ($pedidos as $pedido) {
            echo "<tr>";
            echo "<td>" . $pedido['id'] . "</td>";
            echo "<td>" . $pedido['numero_pedido'] . "</td>";
            echo "<td>" . $pedido['estado'] . "</td>";
            echo "<td>" . $pedido['total_productos'] . "</td>";
            echo "<td>" . $pedido['fecha_pedido'] . "</td>";
            echo "<td>";
            
            if ($pedido['estado'] == 'borrador' || $pedido['estado'] == 'enviado') {
                echo "<button onclick='testCancelarPedido(" . $pedido['id'] . ")' style='background: #dc3545; color: white; padding: 5px; margin: 2px;'>🗑️ Cancelar Pedido</button>";
            }
            
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "❌ No hay pedidos activos en la mesa<br>";
    }

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}

echo "<h3>2. 🍽️ Productos en Pedidos Activos</h3>";

try {
    $stmt = Conexion::conectar()->prepare("
        SELECT pvm.productos_id, pr.nombre as producto_nombre, pvm.cantidad, 
               pvm.fecha_hora, p.id as pedido_id, p.numero_pedido, p.estado
        FROM producto_vendido_mesa pvm
        JOIN productos pr ON pvm.productos_id = pr.id
        JOIN pedidos p ON pvm.pedidos_id = p.id
        WHERE pvm.mesas_id = ? AND p.estado != 'facturado'
        ORDER BY p.fecha_pedido DESC, pvm.fecha_hora DESC
    ");
    $stmt->bindParam(1, $_SESSION["mesa"], PDO::PARAM_INT);
    $stmt->execute();
    $productos = $stmt->fetchAll();

    if (!empty($productos)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Producto</th><th>Cantidad</th><th>Pedido</th><th>Estado</th><th>Fecha</th><th>Acciones</th></tr>";
        
        foreach ($productos as $producto) {
            echo "<tr>";
            echo "<td>" . $producto['producto_nombre'] . "</td>";
            echo "<td>" . $producto['cantidad'] . "</td>";
            echo "<td>" . $producto['numero_pedido'] . "</td>";
            echo "<td>" . $producto['estado'] . "</td>";
            echo "<td>" . $producto['fecha_hora'] . "</td>";
            echo "<td>";
            
            echo "<button onclick='testCancelarProducto(" . $producto['productos_id'] . ", " . $producto['pedido_id'] . ")' style='background: #ffc107; color: black; padding: 5px; margin: 2px;'>🗑️ Cancelar Producto</button>";
            
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "❌ No hay productos en pedidos activos<br>";
    }

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}

?>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
function testCancelarPedido(pedidoId) {
    if (confirm('🧪 TEST: ¿Cancelar pedido ' + pedidoId + '?')) {
        $.post("ajaxEstadoPedidos.php", {
            cancelar_pedido: true,
            pedido_id: pedidoId
        }, function(data) {
            try {
                var resultado = JSON.parse(data);
                alert('✅ Resultado: ' + resultado.status + '\n📝 Mensaje: ' + resultado.message);
                if (resultado.status === "success") {
                    location.reload();
                }
            } catch (e) {
                alert('❌ Error parseando respuesta: ' + data);
                console.error(e, data);
            }
        }).fail(function(xhr, status, error) {
            alert('❌ Error AJAX: ' + error);
        });
    }
}

function testCancelarProducto(productoId, pedidoId) {
    if (confirm('🧪 TEST: ¿Cancelar producto ' + productoId + ' del pedido ' + pedidoId + '?')) {
        $.post("ajaxPedidoVentas.php", {
            cancelarProducto: productoId,
            pedido_id: pedidoId
        }, function(data) {
            if (data.trim() === 'success') {
                alert('✅ Producto cancelado correctamente');
                location.reload();
            } else {
                alert('❌ Error al cancelar producto: ' + data);
            }
        }).fail(function(xhr, status, error) {
            alert('❌ Error AJAX: ' + error);
        });
    }
}

// Función para recargar la página
function recargarPagina() {
    location.reload();
}
</script>

<div style="margin: 20px 0; padding: 15px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px;">
    <h4>🔧 Controles de Prueba</h4>
    <button onclick="recargarPagina()" style="background: #28a745; color: white; padding: 10px; margin: 5px;">🔄 Recargar Página</button>
    <a href="../registroPmesa.php?ida=<?=$_SESSION['mesa']?>" style="background: #007bff; color: white; padding: 10px; margin: 5px; text-decoration: none; display: inline-block;">📋 Ir a Mesa <?=$_SESSION['mesa']?></a>
</div>

<div style="margin: 20px 0; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;">
    <h4>📝 Instrucciones de Prueba</h4>
    <ol>
        <li><strong>Cancelar Pedido:</strong> Haz clic en "🗑️ Cancelar Pedido" para cancelar un pedido completo</li>
        <li><strong>Cancelar Producto:</strong> Haz clic en "🗑️ Cancelar Producto" para eliminar un producto específico</li>
        <li><strong>Verificar Permisos:</strong> Solo administradores (tipo_usuario 1 y 3) pueden cancelar pedidos</li>
        <li><strong>Estados Válidos:</strong> Solo se pueden cancelar pedidos en estado 'borrador' o 'enviado'</li>
    </ol>
</div>
