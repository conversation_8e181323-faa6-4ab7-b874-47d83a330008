<?php

	$registroSuministroP = new controllerSuministroProducto();
	$productos = $registroSuministroP -> listaProductosController();
	$suministros = $registroSuministroP -> listaSuministrosController();

	$registroSuministroP -> registroSuministroPController();

	if(isset($_GET["action"]))
		{	
			if($_GET["action"] == "okSP")
			{echo "Registro Exitoso";	}

		}

?>

<h1>PRODUCTOS Y SUMINISTROS</h1>
<form method="post">
	<label> Nombre Producto</label>
	<!--input type="text" placeholder="proveedor_id" name="proveedor_idCsuministroRegistro" required><br-->
	<?php  
	 	# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  productos  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%% producto_id, id, cantidades
			if($productos=="error")
				{	echo "debe registrar el productos"; }
			else{
					echo "<label>Id productos </label>";
					$result='<select name="productos"  id="productos">';
					$result.=' <option value="-1">Seleccione una productos</option>';
					foreach ($productos as $row => $item)
					 	{	$result.=' <option value="'.$item["id"].'">'.$item["nombre"].'</option>';	}	
					 $result.='</select>';
					 echo $result." <br>";
			}
		# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  End productos  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%	 
	 
	 	# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  suministros  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
			if($suministros=="error")
				{	echo "debe registrar el suministros"; }
			else{
					echo "<label>Id suministros </label>";
					$result='<select name="suministros"  id="suministros">';
					$result.=' <option value="-1">Seleccione una suministros</option>';
					foreach ($suministros as $row => $item)
					 	{	$result.=' <option value="'.$item["id"].'">'.$item["nombre"].'</option>';	}	
					 $result.='</select>';
					 echo $result." <br>";
				}
		# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  End suministros  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
 	?>	
	<label> cantidades: </label>
	<input type="text" placeholder="cantidades" name="cantidades" required><br>	
	<input type="submit" value="Enviar">

</form>

<?php
	$registroPmesa = new MvcController();
	$productos_idPmesaRegistro = $registroPmesa -> listaProductosController();
	$mesas_idPmesaRegistro = $registroPmesa -> listaMesasController();
	$registroPmesa -> registroPmesaController();

	if(isset($_GET["action"]))
		{	if($_GET["action"] == "okPm")
			{echo "Registro Exitoso";	}
		}
?>

<script type="text/javascript">
	function buscar() 
		{	
			if (confirm('confirme si elimina el pedido?'))
				{	setTimeout('location.href="views/modules/ajaxPermiso.php"',500);}	

		//alert("Entro en el else de placa");
			/*$("#destino").load("views/modules/ajaxPermiso.php", function()
				{  	alert("recibidos los datos por ajax Monte");    		 });*/		
		} // placa

	function aMayusculas(obj,id)
		{    obj = obj.toUpperCase();
		    document.getElementById(id).value = obj;
		}
</script>

 <div class="row">
      <div class="col-md-8">
		  	<h2>PEDIDO</h1>
			<table border="1">				
				<thead>					
					<tr>						
						<th>PRODUCTOS COD.</th>				
						<th>PRODUCTOS</th>				
						<th>PRECIO</th>				
						<th>CANTIDAD</th>				
						<th>FECHA</th>					
						<th></th>
						<th></th>
					</tr>
				</thead>

				<tbody>					
					<?php
						$vistaPmesa = new MvcController();
						$vistaPmesa -> vistaPmesaController();
						$vistaPmesa -> borrarPmesaController();
					?>
				</tbody>
					<thead> 	
						<tr > <td colspan="6" ></td> </tr>
					</thead>
			</table>
			<p><a class="btn btn-primary btn-lg" href="#" role="button" onclick="buscar();" name="cancelar" value="cancelar">Cancelar &raquo;</a></p>
			<!-- <input type="button" onclick="buscar()" value="cancelar">-->
			
      </div>
      <div class="col-md-4">
			<h2>AGREGAR PRODUCTO</h1>
			<form method="post">
				<div class="col-md-2">
					
					<label> CANTIDAD: </label>
					<label> PRODUCTO: </label>
				</div>
				<div class="col-md-2">	
					<input type="text" placeholder="1" name="cantidadPmesaRegistro" value="1" required>
					<input type="hidden" placeholder="MESA" name="mesas_idPmesaRegistro" value="1" required>
					<input type="text" placeholder="PRODUCTO" name="productos_idPmesaRegistro" required>
								
										
									
					<input type="submit" value="Enviar">
				</div>	
			</form>
			<span id="destino"></span>
     </div>


  
