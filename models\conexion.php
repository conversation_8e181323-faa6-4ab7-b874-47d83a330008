<?php

class Conexion
{

	public static function conectar()
		{

			try{  //$link = new PDO("mysql:host=localhost;dbname=dxjeuujp_phila","dxjeuujp_phila","LkWv#,,kXm3R.02MC6", array(PDO::MYSQL_ATTR_INIT_COMMAND => 'SET NAMES \'UTF8\''));
				   	$link = new PDO("mysql:host=localhost;dbname=ewogjwfm_macarena","ewogjwfm_macarena",'qTb)k0+AUih}J&@eEt', array(PDO::MYSQL_ATTR_INIT_COMMAND => 'SET NAMES \'UTF8\''));
				  	//$link = new PDO("mysql:host=localhost;dbname=philadelphia","root","");
				 /* echo "Vamos bien Compadre";
				  echo '<script>alert("Entro a la BD");</script>';*/
					return $link;
				}
			catch (PDOException $e)
				{	 echo '<script>alert("No Entro a la BD");</script>';echo "Vamos Mal  no conecto";
				    print "¡Error!: " . $e->getMessage() . "<br/>";
				    die();
				}
		}

}

?>