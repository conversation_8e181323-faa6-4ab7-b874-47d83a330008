<?php
// Test completo del flujo de cierre de turno
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
if (!isset($_SESSION["usuario"])) {
    $_SESSION["usuario"] = 15;
    $_SESSION["tipo_usuario"] = 1;
    $_SESSION["perfil"] = "administrador";
}

require_once "../../models/conexion.php";

echo "<h1>🔄 Test Flujo Completo de Cierre de Turno</h1>";
echo "<p>Verificando todo el proceso desde el cierre hasta el logout</p>";

try {
    $db = Conexion::conectar();
    $usuario = $_SESSION["usuario"];
    
    echo "<h3>1. 👤 Estado Actual del Usuario</h3>";
    echo "<div class='alert alert-info'>";
    echo "<p><strong>Usuario ID:</strong> $usuario</p>";
    echo "<p><strong>Sesión Activa:</strong> " . (isset($_SESSION["validar"]) && $_SESSION["validar"] ? "✅ SÍ" : "❌ NO") . "</p>";
    echo "</div>";
    
    echo "<h3>2. 🔍 Verificar Turno Activo</h3>";
    
    // Buscar turno activo
    $sqlTurnoActivo = "SELECT id, persona_id, fecha_inicio, fecha_final, cantidad_inicio, cantidad_final 
                       FROM turnos_cajeros 
                       WHERE persona_id = ? AND fecha_final = '0000-00-00 00:00:00'
                       ORDER BY id DESC LIMIT 1";
    $stmtTurno = $db->prepare($sqlTurnoActivo);
    $stmtTurno->execute([$usuario]);
    $turnoActivo = $stmtTurno->fetch(PDO::FETCH_ASSOC);
    
    if ($turnoActivo) {
        echo "<div class='alert alert-success'>";
        echo "<h4>✅ Turno Activo Encontrado</h4>";
        echo "<p><strong>ID:</strong> {$turnoActivo['id']}</p>";
        echo "<p><strong>Inicio:</strong> {$turnoActivo['fecha_inicio']}</p>";
        echo "<p><strong>Cantidad Inicial:</strong> $" . number_format($turnoActivo['cantidad_inicio']) . "</p>";
        echo "</div>";
        
        echo "<h3>3. 🎯 Flujo de Cierre Recomendado</h3>";
        echo "<div class='alert alert-warning'>";
        echo "<h4>📋 Pasos del Proceso:</h4>";
        echo "<ol>";
        echo "<li><strong>Ir a Cierre:</strong> <a href='../../editarTurno' target='_blank'>editarTurno</a></li>";
        echo "<li><strong>Revisar Resumen:</strong> Ventas, ingresos, egresos, propinas</li>";
        echo "<li><strong>Hacer Clic 'Cerrar':</strong> Confirmar cantidad final</li>";
        echo "<li><strong>PDF se Abre:</strong> Automáticamente en nueva ventana</li>";
        echo "<li><strong>Sesión se Cierra:</strong> Automáticamente después de 3 segundos</li>";
        echo "<li><strong>Redirección:</strong> A página de login</li>";
        echo "</ol>";
        echo "</div>";
        
    } else {
        echo "<div class='alert alert-warning'>";
        echo "<h4>⚠️ No hay turno activo</h4>";
        echo "<p>Para probar el cierre completo, primero debe iniciar un turno.</p>";
        echo "</div>";
        
        // Mostrar último turno cerrado
        $sqlUltimoTurno = "SELECT id, persona_id, fecha_inicio, fecha_final, cantidad_inicio, cantidad_final 
                           FROM turnos_cajeros 
                           WHERE persona_id = ? AND fecha_final != '0000-00-00 00:00:00'
                           ORDER BY id DESC LIMIT 1";
        $stmtUltimo = $db->prepare($sqlUltimoTurno);
        $stmtUltimo->execute([$usuario]);
        $ultimoTurno = $stmtUltimo->fetch(PDO::FETCH_ASSOC);
        
        if ($ultimoTurno) {
            echo "<h3>📋 Último Turno Cerrado</h3>";
            echo "<div class='alert alert-info'>";
            echo "<p><strong>ID:</strong> {$ultimoTurno['id']}</p>";
            echo "<p><strong>Inicio:</strong> {$ultimoTurno['fecha_inicio']}</p>";
            echo "<p><strong>Fin:</strong> {$ultimoTurno['fecha_final']}</p>";
            echo "<p><strong>Cantidad Final:</strong> $" . number_format($ultimoTurno['cantidad_final']) . "</p>";
            echo "</div>";
        }
    }
    
    echo "<h3>4. 🧪 Tests Disponibles</h3>";
    echo "<div class='row'>";
    echo "<div class='col-md-4'>";
    echo "<div class='panel panel-primary'>";
    echo "<div class='panel-heading'><h4>📄 Test PDF</h4></div>";
    echo "<div class='panel-body'>";
    echo "<p>Probar la generación del PDF de turno</p>";
    echo "<a href='test_pdf_turno.php' target='_blank' class='btn btn-primary'>🧪 Test PDF</a>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-4'>";
    echo "<div class='panel panel-success'>";
    echo "<div class='panel-heading'><h4>🔄 Test Cierre</h4></div>";
    echo "<div class='panel-body'>";
    echo "<p>Verificar sistema de cierre de turno</p>";
    echo "<a href='test_cierre_turno.php' target='_blank' class='btn btn-success'>🧪 Test Cierre</a>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-4'>";
    echo "<div class='panel panel-warning'>";
    echo "<div class='panel-heading'><h4>🚪 Test Logout</h4></div>";
    echo "<div class='panel-body'>";
    echo "<p>Probar el cierre de sesión</p>";
    echo "<a href='../../salir' target='_blank' class='btn btn-warning'>🧪 Test Logout</a>";
    echo "</div></div></div>";
    echo "</div>";
    
    echo "<h3>5. 🔧 Correcciones Aplicadas</h3>";
    echo "<div class='alert alert-success'>";
    echo "<h4>✅ Mejoras Implementadas:</h4>";
    echo "<ul>";
    echo "<li><strong>Controlador de Cierre:</strong> JavaScript ejecuta antes que redirección</li>";
    echo "<li><strong>PDF de Turno:</strong> Corregido bucle y formato mejorado</li>";
    echo "<li><strong>Página de Logout:</strong> Mejor presentación y redirección automática</li>";
    echo "<li><strong>Flujo Completo:</strong> PDF → Logout → Login en secuencia correcta</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>6. 📊 Secuencia de Eventos</h3>";
    echo "<div class='timeline'>";
    echo "<div class='timeline-item'>";
    echo "<div class='timeline-marker'>1</div>";
    echo "<div class='timeline-content'>";
    echo "<h4>Usuario hace clic 'Cerrar Turno'</h4>";
    echo "<p>En la página editarTurno</p>";
    echo "</div></div>";
    
    echo "<div class='timeline-item'>";
    echo "<div class='timeline-marker'>2</div>";
    echo "<div class='timeline-content'>";
    echo "<h4>Sistema actualiza base de datos</h4>";
    echo "<p>fecha_final y cantidad_final en turnos_cajeros</p>";
    echo "</div></div>";
    
    echo "<div class='timeline-item'>";
    echo "<div class='timeline-marker'>3</div>";
    echo "<div class='timeline-content'>";
    echo "<h4>JavaScript abre PDF</h4>";
    echo "<p>window.open('pdfTurno', '_blank')</p>";
    echo "</div></div>";
    
    echo "<div class='timeline-item'>";
    echo "<div class='timeline-marker'>4</div>";
    echo "<div class='timeline-content'>";
    echo "<h4>Redirección automática (3 segundos)</h4>";
    echo "<p>window.location.href = 'index.php?action=salir'</p>";
    echo "</div></div>";
    
    echo "<div class='timeline-item'>";
    echo "<div class='timeline-marker'>5</div>";
    echo "<div class='timeline-content'>";
    echo "<h4>Sesión se cierra</h4>";
    echo "<p>session_unset() y session_destroy()</p>";
    echo "</div></div>";
    
    echo "<div class='timeline-item'>";
    echo "<div class='timeline-marker'>6</div>";
    echo "<div class='timeline-content'>";
    echo "<h4>Redirección final al login</h4>";
    echo "<p>Automática después de 3 segundos</p>";
    echo "</div></div>";
    echo "</div>";
    
    echo "<h3>7. 🎯 Resultado Esperado</h3>";
    echo "<div class='alert alert-info'>";
    echo "<h4>Al cerrar un turno correctamente:</h4>";
    echo "<ol>";
    echo "<li>✅ <strong>PDF se abre</strong> con el reporte del turno</li>";
    echo "<li>✅ <strong>Sesión se cierra</strong> automáticamente</li>";
    echo "<li>✅ <strong>Usuario regresa al login</strong> para iniciar nueva sesión</li>";
    echo "<li>✅ <strong>Turno queda cerrado</strong> en la base de datos</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>❌ Error en el test</h4>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>

<style>
.alert { margin: 15px 0; padding: 15px; border-radius: 5px; }
.alert-info { background-color: #d9edf7; border-color: #bce8f1; color: #31708f; }
.alert-success { background-color: #dff0d8; border-color: #d6e9c6; color: #3c763d; }
.alert-warning { background-color: #fcf8e3; border-color: #faebcc; color: #8a6d3b; }
.alert-danger { background-color: #f2dede; border-color: #ebccd1; color: #a94442; }
.btn { padding: 8px 16px; margin: 5px; text-decoration: none; border-radius: 3px; display: inline-block; color: white; }
.btn-primary { background-color: #337ab7; }
.btn-success { background-color: #5cb85c; }
.btn-warning { background-color: #f0ad4e; }
.row { display: flex; flex-wrap: wrap; margin: 0 -15px; }
.col-md-4 { flex: 0 0 33.333333%; padding: 0 15px; }
.panel { border: 1px solid #ddd; border-radius: 4px; margin-bottom: 20px; }
.panel-heading { padding: 10px 15px; background-color: #f5f5f5; border-bottom: 1px solid #ddd; }
.panel-primary .panel-heading { background-color: #337ab7; color: white; }
.panel-success .panel-heading { background-color: #5cb85c; color: white; }
.panel-warning .panel-heading { background-color: #f0ad4e; color: white; }
.panel-body { padding: 15px; }
.timeline { margin: 20px 0; }
.timeline-item { display: flex; margin-bottom: 20px; }
.timeline-marker { 
    width: 30px; height: 30px; border-radius: 50%; background-color: #337ab7; 
    color: white; display: flex; align-items: center; justify-content: center; 
    font-weight: bold; margin-right: 15px; flex-shrink: 0; 
}
.timeline-content { flex: 1; }
.timeline-content h4 { margin: 0 0 5px 0; color: #333; }
.timeline-content p { margin: 0; color: #666; }
</style>
