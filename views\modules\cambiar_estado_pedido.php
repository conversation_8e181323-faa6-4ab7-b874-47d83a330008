<?php

session_start();

if(!isset($_SESSION["validar"]) || !$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "models/conexion.php";

// Procesar cambio de estado si se envió el formulario
if ($_POST && isset($_POST['pedido_id']) && isset($_POST['nuevo_estado'])) {
    $pedido_id = $_POST['pedido_id'];
    $nuevo_estado = $_POST['nuevo_estado'];
    
    try {
        $stmt = Conexion::conectar()->prepare("
            UPDATE pedidos 
            SET estado = ?, fecha_envio = CASE WHEN ? = 'enviado' THEN NOW() ELSE fecha_envio END
            WHERE id = ?
        ");
        $stmt->execute([$nuevo_estado, $nuevo_estado, $pedido_id]);
        
        echo "<div class='alert alert-success'>✅ Estado del pedido actualizado a: <strong>{$nuevo_estado}</strong></div>";
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>❌ Error al actualizar: " . $e->getMessage() . "</div>";
    }
}

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Cambiar Estado de Pedidos</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
</head>
<body>

<div class="container">
    <h2>🔧 Cambiar Estado de Pedidos</h2>
    <hr>
    
    <div class="alert alert-warning">
        <strong>⚠️ Herramienta de Debug:</strong> Esta página permite cambiar manualmente el estado de los pedidos para pruebas.
    </div>
    
    <div class="panel panel-default">
        <div class="panel-heading">
            <h3 class="panel-title">📋 Todos los Pedidos Recientes</h3>
        </div>
        <div class="panel-body">
            <?php
            $stmt = Conexion::conectar()->prepare("
                SELECT p.id, p.numero_pedido, p.estado, p.mesa_id, m.nombre as mesa_numero,
                       p.fecha_pedido, p.fecha_envio, p.fecha_entrega,
                       COUNT(pvm.id) as total_productos
                FROM pedidos p
                LEFT JOIN mesas m ON p.mesa_id = m.id
                LEFT JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
                GROUP BY p.id, p.numero_pedido, p.estado, p.mesa_id, m.numero, p.fecha_pedido, p.fecha_envio, p.fecha_entrega
                ORDER BY p.fecha_pedido DESC
                LIMIT 20
            ");
            $stmt->execute();
            $pedidos = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (count($pedidos) > 0) {
                echo "<table class='table table-striped'>";
                echo "<thead>";
                echo "<tr>";
                echo "<th>ID</th>";
                echo "<th>Número</th>";
                echo "<th>Estado Actual</th>";
                echo "<th>Mesa</th>";
                echo "<th>Productos</th>";
                echo "<th>Fecha Pedido</th>";
                echo "<th>Fecha Envío</th>";
                echo "<th>Acciones</th>";
                echo "</tr>";
                echo "</thead>";
                echo "<tbody>";
                
                foreach ($pedidos as $pedido) {
                    $clase = '';
                    switch ($pedido['estado']) {
                        case 'borrador': $clase = 'info'; break;
                        case 'enviado': $clase = 'warning'; break;
                        case 'entregado': $clase = 'success'; break;
                        case 'facturado': $clase = 'primary'; break;
                        default: $clase = ''; break;
                    }
                    
                    echo "<tr class='{$clase}'>";
                    echo "<td>{$pedido['id']}</td>";
                    echo "<td><strong>{$pedido['numero_pedido']}</strong></td>";
                    echo "<td><span class='label label-default'>{$pedido['estado']}</span></td>";
                    echo "<td>{$pedido['mesa_numero']}</td>";
                    echo "<td>{$pedido['total_productos']}</td>";
                    echo "<td>" . date('d/m H:i', strtotime($pedido['fecha_pedido'])) . "</td>";
                    echo "<td>" . ($pedido['fecha_envio'] ? date('d/m H:i', strtotime($pedido['fecha_envio'])) : '-') . "</td>";
                    echo "<td>";
                    
                    // Formulario para cambiar estado
                    echo "<form method='post' style='display: inline-block;'>";
                    echo "<input type='hidden' name='pedido_id' value='{$pedido['id']}'>";
                    echo "<select name='nuevo_estado' class='form-control' style='width: 100px; display: inline-block; margin-right: 5px;'>";
                    echo "<option value='borrador'" . ($pedido['estado'] == 'borrador' ? ' selected' : '') . ">Borrador</option>";
                    echo "<option value='enviado'" . ($pedido['estado'] == 'enviado' ? ' selected' : '') . ">Enviado</option>";
                    echo "<option value='entregado'" . ($pedido['estado'] == 'entregado' ? ' selected' : '') . ">Entregado</option>";
                    echo "<option value='facturado'" . ($pedido['estado'] == 'facturado' ? ' selected' : '') . ">Facturado</option>";
                    echo "</select>";
                    echo "<button type='submit' class='btn btn-xs btn-primary'>Cambiar</button>";
                    echo "</form>";
                    
                    echo "</td>";
                    echo "</tr>";
                }
                
                echo "</tbody>";
                echo "</table>";
            } else {
                echo "<div class='alert alert-info'>No se encontraron pedidos.</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">🎯 Acciones Rápidas para Testing</h3>
        </div>
        <div class="panel-body">
            <h4>Para probar los pedidos pendientes:</h4>
            <ol>
                <li><strong>Asegúrate de que hay pedidos en estado "enviado"</strong> - Usa la tabla de arriba para cambiar estados</li>
                <li><strong>Verifica que los pedidos tienen productos</strong> - Los pedidos sin productos no aparecerán</li>
                <li><strong>Revisa las categorías de los productos</strong> - Deben coincidir con los filtros de bar/cocina/asados</li>
            </ol>
            
            <h4>Estados de Pedidos:</h4>
            <ul>
                <li><strong>borrador:</strong> Pedido en construcción, no aparece en pendientes</li>
                <li><strong>enviado:</strong> Pedido enviado a cocina/bar, aparece en pendientes</li>
                <li><strong>entregado:</strong> Pedido completado, aparece en entregados</li>
                <li><strong>facturado:</strong> Pedido facturado, aparece en historial</li>
            </ul>
            
            <div class="alert alert-info">
                <strong>💡 Tip:</strong> Si el pedido P000004 no aparece en pendientes, cámbialo a estado "enviado" usando la tabla de arriba.
            </div>
        </div>
    </div>
    
    <hr>
    <div class="row">
        <div class="col-md-3">
            <a href="pedidosCocinaPendientes" class="btn btn-warning btn-block">🍳 Ir a Cocina Pendientes</a>
        </div>
        <div class="col-md-3">
            <a href="pedidosBarPendientes" class="btn btn-info btn-block">🍺 Ir a Bar Pendientes</a>
        </div>
        <div class="col-md-3">
            <a href="views/modules/debug_pedido_P000004.php" class="btn btn-success btn-block">🔍 Debug P000004</a>
        </div>
        <div class="col-md-3">
            <a href="views/modules/test_flujo_completo_pendientes.php" class="btn btn-primary btn-block">🔄 Test Flujo</a>
        </div>
    </div>
</div>

</body>
</html>
