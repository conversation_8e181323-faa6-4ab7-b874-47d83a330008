<?php
// Test de integración del sistema de impresión por categorías
echo "<h1>🧪 Test Integración Sistema de Impresión</h1>";
echo "<p><strong>Probar la integración completa con el sistema de pedidos real</strong></p>";

require_once '../../models/conexion.php';
require_once 'componente_impresion_categorias.php';

// Verificar conexión a base de datos
try {
    $conexion = new Conexion();
    $pdo = $conexion->conectar();
    
    echo "<div style='background-color: #d4edda; padding: 10px; border-left: 4px solid #28a745; margin: 10px 0;'>";
    echo "<p>✅ <strong>Conexión a base de datos exitosa</strong></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
    echo "<h4>❌ Error de conexión</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
    exit;
}

// Verificar tablas necesarias
$tablas_necesarias = ['pedidos', 'pedido_productos_mesa', 'productos'];
$tablas_faltantes = [];

foreach ($tablas_necesarias as $tabla) {
    $stmt = $pdo->query("SHOW TABLES LIKE '$tabla'");
    if (!$stmt->fetch()) {
        $tablas_faltantes[] = $tabla;
    }
}

if (!empty($tablas_faltantes)) {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
    echo "<h4>❌ Tablas faltantes</h4>";
    echo "<p>Las siguientes tablas no existen: " . implode(', ', $tablas_faltantes) . "</p>";
    echo "<p>El sistema necesita estas tablas para funcionar correctamente.</p>";
    echo "</div>";
} else {
    echo "<div style='background-color: #d4edda; padding: 10px; border-left: 4px solid #28a745; margin: 10px 0;'>";
    echo "<p>✅ <strong>Todas las tablas necesarias están disponibles</strong></p>";
    echo "</div>";
}

// Crear tabla de logs si no existe
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS logs_impresion_pedidos (
            id INT AUTO_INCREMENT PRIMARY KEY,
            pedido_id INT NOT NULL,
            categoria VARCHAR(50) NOT NULL,
            metodo VARCHAR(100) NOT NULL DEFAULT 'ventana_impresion',
            success BOOLEAN NOT NULL DEFAULT TRUE,
            ip_cliente VARCHAR(45),
            datos_enviados TEXT,
            fecha_hora TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_pedido_categoria (pedido_id, categoria),
            INDEX idx_fecha (fecha_hora)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    echo "<div style='background-color: #d4edda; padding: 10px; border-left: 4px solid #28a745; margin: 10px 0;'>";
    echo "<p>✅ <strong>Tabla de logs verificada/creada</strong></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background-color: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 15px 0;'>";
    echo "<h4>⚠️ Advertencia</h4>";
    echo "<p>No se pudo crear la tabla de logs: " . $e->getMessage() . "</p>";
    echo "<p>El sistema funcionará pero no registrará logs de impresión.</p>";
    echo "</div>";
}

// Buscar pedidos existentes para probar
echo "<h2>📋 Pedidos Disponibles para Probar</h2>";

try {
    $stmt = $pdo->query("
        SELECT 
            p.id,
            p.numero_pedido,
            p.mesa_id,
            p.estado,
            p.fecha_pedido,
            COUNT(ppm.id) as total_productos
        FROM pedidos p
        LEFT JOIN pedido_productos_mesa ppm ON p.id = ppm.pedidos_id
        WHERE p.estado IN ('enviado', 'borrador')
        GROUP BY p.id
        HAVING total_productos > 0
        ORDER BY p.fecha_pedido DESC
        LIMIT 10
    ");
    
    $pedidos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($pedidos) {
        echo "<div style='background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<table style='width: 100%; border-collapse: collapse;'>";
        echo "<tr style='background-color: #bee5eb;'>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>ID</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Número</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Mesa</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Estado</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Productos</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Fecha</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Test</th>";
        echo "</tr>";
        
        foreach ($pedidos as $pedido) {
            echo "<tr>";
            echo "<td style='border: 1px solid #ddd; padding: 8px; font-weight: bold;'>{$pedido['id']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$pedido['numero_pedido']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$pedido['mesa_id']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$pedido['estado']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$pedido['total_productos']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . date('d/m H:i', strtotime($pedido['fecha_pedido'])) . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>";
            echo "<button onclick='probarPedido({$pedido['id']}, \"{$pedido['numero_pedido']}\", {$pedido['mesa_id']})' style='background-color: #007bff; color: white; padding: 4px 8px; border: none; border-radius: 3px; cursor: pointer; font-size: 11px;'>🧪 Probar</button>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
        
    } else {
        echo "<div style='background-color: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 15px 0;'>";
        echo "<h4>⚠️ No hay pedidos para probar</h4>";
        echo "<p>No se encontraron pedidos con productos en estado 'enviado' o 'borrador'.</p>";
        echo "<p>Puedes crear un pedido de prueba o ir a una mesa real para agregar productos.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
    echo "<h4>❌ Error obteniendo pedidos</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

// Área de prueba
echo "<h2>🧪 Área de Prueba</h2>";
echo "<div id='area_prueba' style='background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 15px 0;'>";
echo "<p>Selecciona un pedido de la tabla de arriba para probar los botones de impresión por categorías.</p>";
echo "</div>";

// JavaScript para las pruebas
echo "<script>
function probarPedido(pedidoId, numeroPedido, mesaId) {
    const areaPrueba = document.getElementById('area_prueba');
    
    areaPrueba.innerHTML = '<h4>🔄 Cargando componente de impresión...</h4>';
    
    // Hacer petición AJAX para obtener el componente
    fetch('ajax_cargar_componente_impresion.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            pedido_id: pedidoId,
            numero_pedido: numeroPedido,
            mesa_id: mesaId
        })
    })
    .then(response => response.text())
    .then(html => {
        areaPrueba.innerHTML = html;
    })
    .catch(error => {
        console.error('Error:', error);
        areaPrueba.innerHTML = '<h4>❌ Error cargando componente</h4><p>' + error.message + '</p>';
    });
}
</script>";

// Información del sistema
echo "<h2>💡 Información del Sistema Integrado</h2>";
echo "<div style='background-color: #e7f3ff; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>🔧 Componentes Creados:</h4>";
echo "<ul>";
echo "<li><strong>componente_impresion_categorias.php:</strong> Componente reutilizable para mostrar botones</li>";
echo "<li><strong>ajax_impresion_categoria.php:</strong> API para generar contenido de impresión</li>";
echo "<li><strong>logs_impresion_pedidos:</strong> Tabla para registrar todas las impresiones</li>";
echo "</ul>";

echo "<h4>🎯 Integración en registroPmesa.php:</h4>";
echo "<ol>";
echo "<li>Incluir el componente: <code>require_once 'views/modules/componente_impresion_categorias.php';</code></li>";
echo "<li>Después de mostrar cada pedido enviado, llamar: <code>mostrarBotonesImpresionCategorias(\$pedido_id, \$numero_pedido, \$mesa_numero);</code></li>";
echo "<li>Los botones aparecerán automáticamente solo para categorías que tengan productos</li>";
echo "</ol>";

echo "<h4>✅ Ventajas del Sistema Integrado:</h4>";
echo "<ul>";
echo "<li>🎯 <strong>Automático:</strong> Los botones aparecen automáticamente después de enviar pedidos</li>";
echo "<li>🔄 <strong>Dinámico:</strong> Solo muestra categorías que tienen productos</li>";
echo "<li>📱 <strong>Compatible:</strong> Funciona en móviles, tablets y PC</li>";
echo "<li>📊 <strong>Trazabilidad:</strong> Registra logs de todas las impresiones</li>";
echo "<li>🚀 <strong>Eficiente:</strong> Usa AJAX para cargar contenido sin recargar página</li>";
echo "</ul>";
echo "</div>";

// Botones de navegación
echo "<p style='margin-top: 30px;'>";
echo "<a href='demo_impresion_categorias.php' style='background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>← Demo</a>";
echo "<a href='index.php?action=registroPmesa&ida=1' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🪑 Mesa Real</a>";
echo "<a href='pantallaCocina?categoria=cocina' style='background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🍳 Pantalla Cocina</a>";
echo "</p>";
?>
