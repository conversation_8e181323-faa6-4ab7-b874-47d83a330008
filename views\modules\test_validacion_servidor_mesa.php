<!DOCTYPE html>
<html>
<head>
    <title>🛡️ Test Validación Servidor Mesa</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1000px; margin: 0 auto; }
        .card { background: #f8f9fa; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #007bff; }
        .success { border-left-color: #28a745; background: #d4edda; }
        .warning { border-left-color: #ffc107; background: #fff3cd; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .btn { padding: 15px 30px; margin: 10px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold; font-size: 16px; cursor: pointer; border: none; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        h1 { color: #007bff; text-align: center; }
        h2 { color: #495057; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .debug-info { background: #e9ecef; padding: 10px; border-radius: 3px; font-family: monospace; margin: 10px 0; }
        .form-group { margin: 15px 0; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input { width: 200px; padding: 8px; border: 1px solid #ccc; border-radius: 3px; }
        .result { background: white; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; margin: 10px 0; min-height: 50px; }
    </style>
</head>
<body>

<div class="container">
    <h1>🛡️ Test Validación Servidor Mesa</h1>
    <p style="text-align: center; font-size: 18px; color: #6c757d;">
        Verificación de validación de pagos en el servidor (ajaxFactura.php)
    </p>

    <div class="card success">
        <h2>✅ Validación del Servidor Implementada</h2>
        <p>He implementado validación obligatoria en <code>ajaxFactura.php</code>:</p>
        
        <ul>
            <li>✅ <strong>Validación obligatoria:</strong> Verifica suma de 5 formas de pago ≥ total</li>
            <li>✅ <strong>Sin bypass:</strong> No se puede evitar la validación desde el frontend</li>
            <li>✅ <strong>Mensaje detallado:</strong> Muestra exactamente cuánto falta y en qué formas</li>
            <li>✅ <strong>Log de errores:</strong> Registra intentos de pago insuficiente</li>
            <li>✅ <strong>Bloqueo total:</strong> No procesa la facturación si el pago es insuficiente</li>
        </ul>
    </div>

    <div class="card">
        <h2>🔧 Lógica Implementada en el Servidor</h2>
        <p>La validación en <code>ajaxFactura.php</code> funciona así:</p>
        
        <div class="debug-info">
// Calcular total pagado (sin incluir descuentos)
$totalPagado = $efectivo + $tarjeta + $nequi + $daviplata + $bancolombia;

// VALIDACIÓN OBLIGATORIA
if ($totalPagado < $totalCuenta) {
    $faltante = $totalCuenta - $totalPagado;
    
    // Mostrar error detallado
    echo "alert('❌ Pago insuficiente... Falta: $faltante')";
    echo "error_pago_insuficiente_mesa_$mesa";
    exit; // BLOQUEA la facturación
}

// Solo continúa si el pago es suficiente
        </div>
        
        <p><strong>Ahora es imposible facturar con pago insuficiente, incluso si se bypasea el JavaScript.</strong></p>
    </div>

    <div class="card warning">
        <h2>🧪 Test Directo del Servidor</h2>
        <p>Puedes probar la validación del servidor directamente:</p>
        
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div>
                <h4>📝 Datos de Prueba</h4>
                <div class="form-group">
                    <label>Mesa:</label>
                    <input type="number" id="mesa_test" value="5">
                </div>
                <div class="form-group">
                    <label>Total:</label>
                    <input type="number" id="total_test" value="50000">
                </div>
                <div class="form-group">
                    <label>Efectivo:</label>
                    <input type="number" id="efectivo_test" value="30000">
                </div>
                <div class="form-group">
                    <label>Tarjeta:</label>
                    <input type="number" id="tarjeta_test" value="15000">
                </div>
                <div class="form-group">
                    <label>Nequi:</label>
                    <input type="number" id="nequi_test" value="0">
                </div>
                <div class="form-group">
                    <label>Daviplata:</label>
                    <input type="number" id="daviplata_test" value="0">
                </div>
                <div class="form-group">
                    <label>Bancolombia:</label>
                    <input type="number" id="bancolombia_test" value="0">
                </div>
            </div>
            
            <div>
                <h4>🔧 Resultados</h4>
                <div id="resultado_calculo" class="debug-info">
                    Los cálculos aparecerán aquí...
                </div>
                
                <button class="btn btn-danger" onclick="testPagoInsuficiente()">
                    ❌ Test Pago Insuficiente
                </button>
                
                <button class="btn btn-success" onclick="testPagoCorrecto()">
                    ✅ Test Pago Correcto
                </button>
                
                <button class="btn btn-warning" onclick="testDirectoServidor()">
                    🛡️ Test Directo Servidor
                </button>
            </div>
        </div>
        
        <div id="resultado_test" class="result">
            Los resultados del test aparecerán aquí...
        </div>
    </div>

    <div class="card">
        <h2>📋 Instrucciones de Prueba Real</h2>
        <p>Para probar la validación en una mesa real:</p>
        
        <ol>
            <li><strong>Ve a Mesa 5:</strong> Haz clic en el botón abajo</li>
            <li><strong>Agrega productos:</strong> Si no hay productos en la mesa</li>
            <li><strong>Configura pago insuficiente:</strong>
                <ul>
                    <li>Total: $50,000</li>
                    <li>Efectivo: $30,000</li>
                    <li>Tarjeta: $15,000</li>
                    <li>Otros: $0</li>
                </ul>
            </li>
            <li><strong>Intenta facturar:</strong> Haz clic en "Facturar"</li>
            <li><strong>Verifica el error:</strong> Debe aparecer "Pago insuficiente... Falta: $5,000"</li>
            <li><strong>Verifica que NO facture:</strong> La mesa debe seguir con productos</li>
        </ol>
    </div>

    <div class="card error">
        <h2>🎯 Comportamiento Esperado</h2>
        
        <h4>❌ Con Pago Insuficiente:</h4>
        <div style="background: #f8d7da; padding: 10px; border-radius: 3px; margin: 5px 0;">
            <strong>1.</strong> Aparece alert: "❌ Pago insuficiente... Falta: $5,000"<br>
            <strong>2.</strong> NO se procesa la facturación<br>
            <strong>3.</strong> La mesa mantiene sus productos<br>
            <strong>4.</strong> No se genera factura<br>
            <strong>5.</strong> No se abre PDF
        </div>
        
        <h4>✅ Con Pago Correcto:</h4>
        <div style="background: #d4edda; padding: 10px; border-radius: 3px; margin: 5px 0;">
            <strong>1.</strong> Aparece alert: "✅ Facturación completada exitosamente"<br>
            <strong>2.</strong> Se procesa la facturación<br>
            <strong>3.</strong> La mesa se libera<br>
            <strong>4.</strong> Se genera factura<br>
            <strong>5.</strong> Se abre PDF automáticamente
        </div>
    </div>

    <div class="card">
        <h2>🔗 Enlaces de Prueba</h2>
        <p>Usa estos enlaces para probar:</p>
        
        <a href="../../index.php?action=registroPmesa&ida=5" class="btn btn-primary" target="_blank">
            🏠 Mesa 5 (Test Principal)
        </a>
        
        <a href="../../index.php?action=facturacionRapida" class="btn btn-success" target="_blank">
            🚀 Facturación Rápida (Comparar)
        </a>
        
        <a href="test_mesa_validacion_real.php" class="btn btn-warning" target="_blank">
            🧪 Test Frontend
        </a>
    </div>

</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
function calcularTest() {
    const total = parseInt(document.getElementById('total_test').value) || 0;
    const efectivo = parseInt(document.getElementById('efectivo_test').value) || 0;
    const tarjeta = parseInt(document.getElementById('tarjeta_test').value) || 0;
    const nequi = parseInt(document.getElementById('nequi_test').value) || 0;
    const daviplata = parseInt(document.getElementById('daviplata_test').value) || 0;
    const bancolombia = parseInt(document.getElementById('bancolombia_test').value) || 0;
    
    const totalPagado = efectivo + tarjeta + nequi + daviplata + bancolombia;
    const diferencia = totalPagado - total;
    
    let html = '<strong>Cálculo:</strong><br>';
    html += 'Total: $' + total.toLocaleString() + '<br>';
    html += 'Total Pagado: $' + totalPagado.toLocaleString() + '<br>';
    html += 'Diferencia: $' + diferencia.toLocaleString() + '<br><br>';
    
    if (diferencia < 0) {
        html += '<span style="color: red;">❌ PAGO INSUFICIENTE</span><br>';
        html += 'Falta: $' + Math.abs(diferencia).toLocaleString();
    } else if (diferencia === 0) {
        html += '<span style="color: green;">✅ PAGO EXACTO</span>';
    } else {
        html += '<span style="color: blue;">✅ PAGO CON CAMBIO</span><br>';
        html += 'Cambio: $' + diferencia.toLocaleString();
    }
    
    document.getElementById('resultado_calculo').innerHTML = html;
}

function testPagoInsuficiente() {
    document.getElementById('total_test').value = '50000';
    document.getElementById('efectivo_test').value = '30000';
    document.getElementById('tarjeta_test').value = '15000';
    document.getElementById('nequi_test').value = '0';
    document.getElementById('daviplata_test').value = '0';
    document.getElementById('bancolombia_test').value = '0';
    calcularTest();
}

function testPagoCorrecto() {
    document.getElementById('total_test').value = '50000';
    document.getElementById('efectivo_test').value = '30000';
    document.getElementById('tarjeta_test').value = '0';
    document.getElementById('nequi_test').value = '25000';
    document.getElementById('daviplata_test').value = '0';
    document.getElementById('bancolombia_test').value = '0';
    calcularTest();
}

function testDirectoServidor() {
    const datos = {
        mesa: document.getElementById('mesa_test').value,
        total: document.getElementById('total_test').value,
        efectivo: document.getElementById('efectivo_test').value,
        tarjeta: document.getElementById('tarjeta_test').value,
        nequi: document.getElementById('nequi_test').value,
        daviplata: document.getElementById('daviplata_test').value,
        bancolombia: document.getElementById('bancolombia_test').value,
        pago: 1,
        pcedula: 'TEST_SERVIDOR',
        propina: 0,
        totalDescuento: 0
    };
    
    document.getElementById('resultado_test').innerHTML = '⏳ Enviando test directo al servidor...';
    
    $.ajax({
        url: 'ajaxFactura.php',
        type: 'POST',
        data: datos,
        success: function(response) {
            console.log('Respuesta del servidor:', response);
            
            let html = '<h4>📡 Respuesta del Servidor:</h4>';
            html += '<div style="background: #e9ecef; padding: 10px; border-radius: 3px; font-family: monospace; white-space: pre-wrap; max-height: 200px; overflow-y: auto;">';
            html += response;
            html += '</div>';
            
            if (response.includes('error_pago_insuficiente')) {
                html += '<div style="background: #d4edda; color: #155724; padding: 10px; border-radius: 3px; margin-top: 10px;">';
                html += '<strong>✅ VALIDACIÓN FUNCIONANDO</strong><br>';
                html += 'El servidor rechazó correctamente el pago insuficiente';
                html += '</div>';
            } else if (response.includes('success_corregida')) {
                html += '<div style="background: #d1ecf1; color: #0c5460; padding: 10px; border-radius: 3px; margin-top: 10px;">';
                html += '<strong>✅ PAGO ACEPTADO</strong><br>';
                html += 'El servidor procesó correctamente el pago suficiente';
                html += '</div>';
            } else {
                html += '<div style="background: #fff3cd; color: #856404; padding: 10px; border-radius: 3px; margin-top: 10px;">';
                html += '<strong>⚠️ RESPUESTA INESPERADA</strong><br>';
                html += 'Revisa la respuesta del servidor arriba';
                html += '</div>';
            }
            
            document.getElementById('resultado_test').innerHTML = html;
        },
        error: function(xhr, status, error) {
            document.getElementById('resultado_test').innerHTML = 
                '<div style="background: #f8d7da; color: #721c24; padding: 10px; border-radius: 3px;">' +
                '<strong>❌ Error de Conexión</strong><br>' +
                'Status: ' + status + '<br>' +
                'Error: ' + error +
                '</div>';
        }
    });
}

// Calcular automáticamente al cargar y al cambiar valores
document.addEventListener('DOMContentLoaded', function() {
    calcularTest();
    
    // Agregar listeners para recalcular automáticamente
    ['mesa_test', 'total_test', 'efectivo_test', 'tarjeta_test', 'nequi_test', 'daviplata_test', 'bancolombia_test'].forEach(id => {
        document.getElementById(id).addEventListener('input', calcularTest);
    });
});
</script>

</body>
</html>
