<?php
//ini_set('display_errors', 1);
//ini_set('display_startup_errors', 1);
//error_reporting(E_ALL);
	$vistaUsuario = new controllerSuministro();
	$puntos = $vistaUsuario -> listaPuntosController();	

	//$vistaUsuario -> cargarSucursal1Controller();
	date_default_timezone_set("America/Bogota");
	//$fecha_actual=strftime("%Y-%m-%d %H:%M:%S");
	$cadena = array("January", "February","March","April", "May", "June","July","August","September", "October","November","December");
	$reemplazo = array("Enero", "Febrero","Marzo","Abril", "Mayo", "Junio","Julio","Agosto","Septiembre", "Octubre","Noviembre","Diciembre");
	$fecha_actual=strftime("%Y-%B-%d %H:%M:%S"); //%B Nombre completo del mes en Ingles <?=$fecha_final;
	$fecha_final=str_replace($cadena, $reemplazo, $fecha_actual);
?>
<h1>Productos </h1>
<div class="table-responsive">
	
	<table border="1" class="table table-hover">		
		<thead>			
			<tr>				
				<th>No</th>
				<th>CODIGO</th>
				<th>NOMBRES</th>								
				<th>TIPO</th>
				<th>TAMAÑO</th>
				<th>COLOR</th>
				<?php 
				 if ($_SESSION["tipo_usuario"]==1) 
					{
						echo '
						<th>PRECIO COMPRA</th>';
					}					
				 ?>
				<th>PRECIO VENTA</th>
				<th>UNIDADES</th>
				<?php 
				 if ($_SESSION["tipo_usuario"]==1) 
					{
						echo '<th>CANTIDAD</th>
						<th>Selecione</th>
						';
					}					
				 ?>				
				<th></th>				
				
			</tr>
		</thead>
		<tbody>			
		 <?php
			$vistaUsuario -> vistaSuministroController();
			$vistaUsuario -> borrarSuministroController();			
		 ?>
</div>
</form>
<?php

 if(isset($_GET["action"]))
	{	if($_GET["action"] == "cambio")		{	echo "Cambio Exitoso";	}	}

?>




