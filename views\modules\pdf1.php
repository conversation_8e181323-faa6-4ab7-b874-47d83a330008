<!DOCTYPE html>
<html>
    <head>
        <meta charset="UTF-8">
        <title></title>
        <script type="text/javascript">
            function imprimir() {
                if (window.print) {
                    window.print();
                } else {
                    alert("La función de impresion no esta soportada por su navegador.");
                }
            }
        </script>
    </head>
    <body onload="imprimir();">

<?php

ob_start();
//session_start();
    //1111111111111111111111111111111111111111111111111111111111

    //
    $producto=$_SESSION["productos"];//array
    $pedido=0;
	$cajero=0;
	$subtotal=$_SESSION["subtotal"];
	$total=$_SESSION["total"];
	$efectivo=0;
	$tarjeta=0;
	$tipoTarjeta=0;
	$cambio=0;
	$factura=0;
	$propina=$_SESSION["propina"];
	$totalCuenta=$total-$propina;
	$subTotal=$totalCuenta/1.08;
	$impoconsumo=$total-$subTotal;

	$mesa=$_SESSION["mesaPrefactura"];

    //restaurante111111111111111111111111111111111111111111111111

	if ($factura>0 and $factura<=1000) {
		$dian1="POS No. 13028003284316";
		$dian2="Fecha 2017-03-02";
		$dian3="autorizada del 1 al 1000";
	}else if ($factura>1000) {
		$dian1="POS No. 18762002707283";
		$dian2="Fecha 2017-03-27";
		$dian3="autorizada del 1001 al 50000.";
	}

	setlocale(LC_MONETARY, 'en_US');
	///////////////////////////////////////////////
	/////////////////////////////////////////////////// <img name ="lagarto" src="http://i.imgur.com/afC0L.jpg" alt="Notepad++" title="Notepad++ editor de texto">

$content='<center><div style="font-family: Consolas,monaco,monospace; align:center; width:260;">Hostal La Macarena</b> </div>
	<div style="font-family: Consolas,monaco,monospace; align:center; width:220;">	N.I.T. # 50937594-7 <br></div>
	<div style="font-family: Consolas,monaco,monospace; align:center; width:220;">	 Regimen No responsable de IVA</div><br>
	<div  style="font-family: Consolas,monaco,monospace;  align:Right; width:260;">
	Factura No: '.$factura.' <br> Fecha :'.date('d/m/Y').'<br> <br></div>
	<div style="font-family: Consolas,monaco,monospace;  align:center; width:220;" >Km 27 700M LA REVUELTA
<br>tel :  +57 301 7475781 <!--************--> 	</div>
	<div style="font-family: Consolas,monaco,monospace;  align:center; width:220;" >www.hostallamacarena.com<br>W ************	</div> </center>
	<hr>
		<table   style="font-family: Consolas,monaco,monospace; width:220;" >
			<thead>
				<tr>
					<td colspan="2"> Cod cajero:</td> <td colspan="2">'.$cajero.'</td>
				</tr>
				<tr>
					<td colspan="2"> <b>Mesa:</b></td> <td colspan="2"><b>'.$mesa.'</b></td>
				</tr>
				<tr>
					<td>Produc</td>
					<!--<td>Prec</td>-->
					<td align="center">Cant</td>
					<td  align="right">Total</td>
				</tr>
			</thead>
			<tbody>';
				$lista =$_SESSION["productos"];
				//echo "<script>alert('Controle Usuario ".$lista[0]["codigo"]." ');</script>";
				for ($i=0; $i < count($lista) ; $i++)
					{	$des=$lista[$i]["precio"]*($lista[$i]["descuento"]/100);
						//$desT=($lista[$i]["precio"]-$des)*$lista[$i]["cantidad"];
						$desT=$des*$lista[$i]["cantidad"];
						$content.='<tr>
							<td>'.$lista[$i]["nombre"].'</td>
							<!--<td>$'.number_format( $lista[$i]["precio"]).'</td>-->
							<td  align="center">'.$lista[$i]["cantidad"].'</td>
							<td align="right">$'.number_format( $lista[$i]["total"]).'</td>
						</tr>
						<!--tr>
							<td>Descuento</td>
							<td>$'.number_format($lista[$i]["descuento"]).'%</td>
							<td>$'.number_format($des).'</td>
							<td>$'.number_format($desT).'</td>

						</tr-->'
						;
					}
					$content.='
							<tr><td colspan="3">____________________________</td></tr>
							<tr>
								<td colspan="1" >Sub Total </td>
								<td colspan="2" align="right">$'.number_format( $subTotal).'</td>
							</tr>
							<tr>
								<td colspan="1" >Ipoconsumo </td>
								<td colspan="2" align="right">$'.number_format($impoconsumo ).'</td>
							</tr>
							<tr><td colspan="3">____________________________</td></tr>
							<tr>
								<td colspan="1" >Total </td>
								<td colspan="2" align="right">$'.number_format( $totalCuenta).'</td>
							</tr>
							<tr>
								<td colspan="1" >Propina</td>
								<td colspan="2" align="right">$'.number_format( $propina).'</td>
							</tr>
							<tr><td colspan="3">____________________________</td></tr>
							<tr>
								<td colspan1="1" >Total a pagar</td>
								<td colspan="2" align="right"><b>$'.number_format( $total).'</b></td>
							</tr>
					</table>
					<hr>
	<br>
	<center>
	<div style="font-family: Consolas,monaco,monospace; align:center; width:260;" >
				La propina es voluntaria <br>
				<br>

	</div></center>';
		echo($content);
?>
    </body>
</html>
