<?php

require_once "conexion.php";

class DatosEstadoPedidos {
    
    /*=============================================
    CREAR NUEVO PEDIDO CON ESTADO
    =============================================*/
    static public function crearPedidoModel($datos) {
        $stmt = Conexion::conectar()->prepare("
            INSERT INTO pedidos (mesero_id, mesa_id, facturado, cedula_cliente, fecha_pedido, estado, numero_pedido) 
            VALUES (?, ?, 'n', ?, NOW(), 'borrador', ?)
        ");
        
        // Generar número de pedido
        $numeroPedido = self::generarNumeroPedidoModel();
        
        $stmt->bindParam(1, $datos["mesero_id"], PDO::PARAM_INT);
        $stmt->bindParam(2, $datos["mesa_id"], PDO::PARAM_INT);
        $stmt->bindParam(3, $datos["cedula_cliente"], PDO::PARAM_STR);
        $stmt->bindParam(4, $numeroPedido, PDO::PARAM_STR);
        
        if($stmt->execute()) {
            return Conexion::conectar()->lastInsertId();
        } else {
            return "error";
        }
        //$stmt->close();
        $stmt = null;
    }
    
    /*=============================================
    GENERAR NUMERO DE PEDIDO
    =============================================*/
    static public function generarNumeroPedidoModel() {
        $stmt = Conexion::conectar()->prepare("SELECT MAX(id) as ultimo_id FROM pedidos");
        $stmt->execute();
        $resultado = $stmt->fetch();
        $ultimoId = $resultado['ultimo_id'] ? $resultado['ultimo_id'] : 0;
        //$stmt->close();
        $stmt = null;
        
        return 'P' . str_pad($ultimoId + 1, 6, '0', STR_PAD_LEFT);
    }
    
    /*=============================================
    OBTENER PEDIDO POR ID
    =============================================*/
    static public function obtenerPedidoPorIdModel($pedidoId) {
        $stmt = Conexion::conectar()->prepare("SELECT * FROM pedidos WHERE id = ?");
        $stmt->bindParam(1, $pedidoId, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetch();
    }

    /*=============================================
    OBTENER PEDIDOS POR MESA Y ESTADO
    =============================================*/
    static public function obtenerPedidosMesaEstadoModel($mesaId, $estado = null) {
        // RESTAURADO: Solo mostrar pedidos borrador para que la mesa se "desocupe" después del envío
        $sql = "
            SELECT p.id, p.numero_pedido, p.estado, p.fecha_pedido, p.fecha_envio,
                   p.fecha_entrega, m.numero as mesa_numero,
                   COALESCE(per.nombre, 'Sin asignar') as mesero_nombre,
                   (SELECT COUNT(*) FROM producto_vendido_mesa pvm WHERE pvm.pedidos_id = p.id) as total_productos
            FROM pedidos p
            LEFT JOIN mesas m ON p.mesa_id = m.id
            LEFT JOIN personas per ON p.mesero_id = per.id
            WHERE p.mesa_id = ? AND p.estado = 'borrador'
        ";

        if ($estado) {
            $sql .= " AND p.estado = ?";
        }

        $sql .= " ORDER BY p.fecha_pedido DESC";

        $stmt = Conexion::conectar()->prepare($sql);
        $stmt->bindParam(1, $mesaId, PDO::PARAM_INT);

        if ($estado) {
            $stmt->bindParam(2, $estado, PDO::PARAM_STR);
        }

        $stmt->execute();
        return $stmt->fetchAll();
        ////$stmt->close();
        $stmt = null;
    }
    
    /*=============================================
    OBTENER PEDIDO BORRADOR ACTIVO DE MESA
    =============================================*/
    static public function obtenerPedidoBorradorMesaModel($mesaId) {
        $stmt = Conexion::conectar()->prepare("
            SELECT id, numero_pedido, fecha_pedido 
            FROM pedidos 
            WHERE mesa_id = ? AND estado = 'borrador'
            ORDER BY fecha_pedido DESC LIMIT 1
        ");
        $stmt->bindParam(1, $mesaId, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetch();
        //$stmt->close();
        $stmt = null;
    }
    
    /*=============================================
    ENVIAR PEDIDO (CAMBIAR ESTADO A ENVIADO)
    =============================================*/
    static public function enviarPedidoModel($pedidoId, $usuarioId) {
        $stmt = Conexion::conectar()->prepare("
            UPDATE pedidos 
            SET estado = 'enviado', fecha_envio = NOW(), usuario_envio = ?
            WHERE id = ? AND estado = 'borrador'
        ");
        $stmt->bindParam(1, $usuarioId, PDO::PARAM_INT);
        $stmt->bindParam(2, $pedidoId, PDO::PARAM_INT);
        
        if($stmt->execute()) {
            return "success";
        } else {
            return "error";
        }
        //$stmt->close();
        $stmt = null;
    }
    
    /*=============================================
    MARCAR PEDIDO COMO ENTREGADO
    =============================================*/
    static public function entregarPedidoModel($pedidoId, $usuarioId) {
        $stmt = Conexion::conectar()->prepare("
            UPDATE pedidos 
            SET estado = 'entregado', fecha_entrega = NOW(), usuario_entrega = ?
            WHERE id = ? AND estado = 'enviado'
        ");
        $stmt->bindParam(1, $usuarioId, PDO::PARAM_INT);
        $stmt->bindParam(2, $pedidoId, PDO::PARAM_INT);
        
        if($stmt->execute()) {
            return "success";
        } else {
            return "error";
        }
        //$stmt->close();
        $stmt = null;
    }
    
    /*=============================================
    OBTENER PRODUCTOS DE UN PEDIDO
    =============================================*/
    static public function obtenerProductosPedidoModel($pedidoId) {
        $stmt = Conexion::conectar()->prepare("
            SELECT p.nombre, pvm.cantidad, p.categoria, pvm.nota, p.precio,
                   (pvm.cantidad * p.precio) as subtotal
            FROM producto_vendido_mesa pvm
            JOIN productos p ON pvm.productos_id = p.id
            WHERE pvm.pedidos_id = ?
            ORDER BY p.categoria, p.nombre
        ");
        $stmt->bindParam(1, $pedidoId, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
        //$stmt->close();
        $stmt = null;
    }
    
    /*=============================================
    VERIFICAR SI USUARIO PUEDE MODIFICAR PEDIDO
    =============================================*/
    static public function puedeModificarPedidoModel($pedidoId, $usuarioId, $rolUsuario) {
        // Los administradores pueden modificar cualquier pedido
        if ($rolUsuario == 'administrador') {
            return true;
        }
        
        // Obtener estado del pedido
        $stmt = Conexion::conectar()->prepare("
            SELECT estado, mesero_id 
            FROM pedidos 
            WHERE id = ?
        ");
        $stmt->bindParam(1, $pedidoId, PDO::PARAM_INT);
        $stmt->execute();
        $pedido = $stmt->fetch();
        ////$stmt->close();
        $stmt = null;
        
        if (!$pedido) {
            return false;
        }
        
        // Solo se pueden modificar pedidos en estado borrador
        if ($pedido['estado'] != 'borrador') {
            return false;
        }
        
        // El mesero puede modificar sus propios pedidos en borrador
        if ($pedido['mesero_id'] == $usuarioId) {
            return true;
        }
        
        return false;
    }
    
    /*=============================================
    REGISTRAR REIMPRESION
    =============================================*/
    static public function registrarReimpresionModel($datos) {
        $stmt = Conexion::conectar()->prepare("
            INSERT INTO pedidos_reimpresiones (pedido_id, categoria, usuario_id, motivo)
            VALUES (?, ?, ?, ?)
        ");
        $stmt->bindParam(1, $datos["pedido_id"], PDO::PARAM_INT);
        $stmt->bindParam(2, $datos["categoria"], PDO::PARAM_STR);
        $stmt->bindParam(3, $datos["usuario_id"], PDO::PARAM_INT);
        $stmt->bindParam(4, $datos["motivo"], PDO::PARAM_STR);
        
        if($stmt->execute()) {
            return "success";
        } else {
            return "error";
        }
        //$stmt->close();
        $stmt = null;
    }
    
    /*=============================================
    OBTENER HISTORIAL DE PEDIDO
    =============================================*/
    static public function obtenerHistorialPedidoModel($pedidoId) {
        $stmt = Conexion::conectar()->prepare("
            SELECT ph.*, per.nombre as usuario_nombre
            FROM pedidos_historial ph
            LEFT JOIN personas per ON ph.usuario_id = per.id
            WHERE ph.pedido_id = ?
            ORDER BY ph.fecha_cambio DESC
        ");
        $stmt->bindParam(1, $pedidoId, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
        ////$stmt->close();
        $stmt = null;
    }
    
    /*=============================================
    OBTENER PEDIDOS PENDIENTES POR CATEGORIA
    =============================================*/
    static public function obtenerPedidosPendientesCategoriaModel($categoria) {
        $stmt = Conexion::conectar()->prepare("
            SELECT DISTINCT p.id, p.numero_pedido, p.fecha_envio, m.nombre as mesa_numero,
                   COUNT(pvm.productos_id) as total_productos
            FROM pedidos p
            JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
            JOIN productos prod ON pvm.productos_id = prod.id
            JOIN mesas m ON p.mesa_id = m.id
            WHERE p.estado = 'enviado' AND prod.categoria = ?
            GROUP BY p.id
            ORDER BY p.fecha_envio ASC
        ");
        $stmt->bindParam(1, $categoria, PDO::PARAM_STR);
        $stmt->execute();
        return $stmt->fetchAll();
        //$stmt->close();
        $stmt = null;
    }
}
