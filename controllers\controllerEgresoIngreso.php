<?php
ob_start();
class controllerEgresoIngreso extends Mvc<PERSON>ontroller
 {
	#REGISTRO DE INGRESO id, motivo, cantidad, fecha_hora
	#------------------------------------
	 public function registroIngresoController()
		{	//session_start();
			//echo "<script>alert('controles va bien ".$_POST["motivoIngresoRegistro"]." -turno ".$_SESSION["turno"]."  ');</script>";
			if(isset($_POST["motivoIngresoRegistro"]))
			 {
				$datosController =array('turno'=>$_SESSION["turno"],
										'motivo'=>$_POST["motivoIngresoRegistro"],
										'punto_id'=>$_POST["puntos"],
										'cantidad'=>$_POST["cantidadIngresoRegistro"]);
				//echo "<script>alert('Entro Controller ".$datosController." no')</script>";
				$respuesta = DatosEgresoIngreso::registroIngresoModel($datosController, "ingresos");
				if($respuesta == "success")
					{
						$_SESSION["motivo"]=$_POST["motivoIngresoRegistro"];
						header("location:index.php?action=okI");
					}
				else
					{	echo "<script>alert('Intente denuevo no se pudo guardar')</script>";	}
			 }
		}
	#------------------------------------
	#VISTA DE INGRESO
	#------------------------------------
	 public function vistaIngresoController()
		{
			$respuesta = DatosEgresoIngreso::vistaIngresoModel("ingresos", $_SESSION["turno"]);
			foreach($respuesta as $row => $item)
			 {

					echo'<tr>
							<td>'.$item["imotivo"].'</td>
							<td>'.$item["icantidad"].'</td>
							<td>'.$item["ifecha_hora"].'</td>
							<td>'.$item["punnombre"].'</td>';
						if ($_SESSION["turno"]==$item["iturno_cajero_id"])
				 		 {
							echo'<td><a href="index.php?action=editarIngreso&id='.$item["iid"].'">Editar</a></td>
							<td><a href="index.php?action=ingreso&idBorrar='.$item["iid"].'">Borrar</a></td>';}
						else
						 {echo'<td></td><td></td>
						</tr>';}
				/*if ($_SESSION["tipo_usuario"]==1)
				 { }
				elseif ($_SESSION["punto_id"]==$item["punid"])
				 {
					echo'<tr>
							<td>'.$item["imotivo"].'</td>
							<td>'.$item["icantidad"].'</td>
							<td>'.$item["ifecha_hora"].'</td>
							<td>'.$item["punnombre"].'</td>';
						if ($_SESSION["turno"]==$item["iturno_cajero_id"])
				 		 {
							echo'<td><a href="index.php?action=editarIngreso&id='.$item["iid"].'">Editar</a></td>
							<td><a href="index.php?action=ingreso&idBorrar='.$item["iid"].'">Borrar</a></td>';}
						else
						 {echo'<td></td><td></td>
						</tr>';}
				 }*/
			 }
		}
	#------------------------------------
	#EDITAR INGRESO
	#------------------------------------
	 public function editarIngresoController()
		{
			$datosController = $_GET["id"];
			$respuesta = DatosEgresoIngreso::editarIngresoModel($datosController, "ingresos");
			echo' <input type="text" value="'.$respuesta["motivo"].'" name="motivoEditar" required>
			 <input type="text" value="'.$respuesta["cantidad"].'" name="cantidadEditar" required>
			 <input type="hidden" value="'.$respuesta["id"].'" name="idEditar" required>
				 <input type="submit" value="Actualizar">';
		}
	#-------------------------------------
	#ACTUALIZAR INGRESO
	#------------------------------------
	 public function actualizarIngresoController()
		{//echo "<script>alert('Entro Controller Actualizar Producto')</script>";
			if(isset($_POST["motivoEditar"]))
			 {
				$datosController = array(  "motivo"=>$_POST["motivoEditar"],
											"cantidad"=>$_POST["cantidadEditar"],
											"fecha_hora"=>$_POST["fecha_horaEditar"],
											"id"=>$_POST["idEditar"]);
				$respuesta = DatosEgresoIngreso::actualizarIngresoModel($datosController, "ingresos");
				if($respuesta == "success")
					{	header("location:index.php?action=cambioI");	}
				else
					{	echo "error";	}
			 }
		}
	#------------------------------------

	#BORRAR INGRESO
	#------------------------------------
		public function borrarIngresoController()
			{
				if(isset($_GET["idBorrar"]))
					{
						$datosController = $_GET["idBorrar"];
						$respuesta = DatosEgresoIngreso::borrarIngresoModel($datosController, "ingresos");
						if($respuesta == "success")
							{	header("location:index.php?action=ingreso");	}
					}
			}
#---------------------------------

##EGRESO
#---------------------------------
	#REGISTRO DE EGRESOid, motivo, cantidad, fecha_hora
	#------------------------------------
	 public function registroEgresoController()
		{//echo "<script>alert('controles va bien');</script>";			session_start();
			if(isset($_POST["motivoEgresoRegistro"]))
				{
					$datosController =array('turno'=>$_SESSION["turno"],
									'motivo'=>$_POST["motivoEgresoRegistro"],
									'punto_id'=>$_POST["puntos"],
									'cantidad'=>$_POST["cantidadEgresoRegistro"]);
					//echo "<script>alert('Entro Controller ".$datosController." no')</script>";
					$respuesta = DatosEgresoIngreso::registroEgresoModel($datosController, "egresos");
					if($respuesta == "success")
						{	header("location:index.php?action=okE");	}
					else
						{	echo "<script>alert('Intente denuevo no se pudo guardar')</script>";
							header("location:index.php?action=registroEgreso");
						}
				}
		}
	#------------------------------------

	#VISTA DE EGRESO
	#------------------------------------
	 public function vistaEgresoController()
		{
			$respuesta = DatosEgresoIngreso::vistaEgresoModel("egresos", $_SESSION["turno"]);
			$c=0;
			foreach($respuesta as $row => $item)
			 {
			 	$c++;
					echo'<tr>
							<td>'.$c.'</td>
							<td>'.$item["imotivo"].'</td>
							<td>'.$item["icantidad"].'</td>
							<td>'.$item["ifecha_hora"].'</td>
							<td>'.$item["punnombre"].'</td>';
						if ($_SESSION["turno"]==$item["iturno_cajero_id"])
				 		 {
							echo'<td><a href="index.php?action=editarEgreso&id='.$item["iid"].'">Editar</a></td>
							<td><a href="index.php?action=egreso&idBorrar='.$item["iid"].'">Borrar</a></td>';}
						else
						 {echo'<td></td><td></td>
						</tr>';}
				/*if ($_SESSION["tipo_usuario"]==1)
				 {
				 }
				elseif ($_SESSION["punto_id"]==$item["punid"])
				 {
					echo'<tr>
							<td>'.$item["imotivo"].'</td>
							<td>'.$item["icantidad"].'</td>
							<td>'.$item["ifecha_hora"].'</td>
							<td>'.$item["punnombre"].'</td>';
						if ($_SESSION["turno"]==$item["iturno_cajero_id"])
				 		 {
							echo'<td><a href="index.php?action=editarIngreso&id='.$item["iid"].'">Editar</a></td>
							<td><a href="index.php?action=ingreso&idBorrar='.$item["iid"].'">Borrar</a></td>';}
						else
						 {echo'<td></td><td></td>
						</tr>';}
				 }*/
			 }
		}
	#------------------------------------
	#EDITAR ENGRESO
	#------------------------------------
	 public function editarEgresoController()
		{
			$datosController = $_GET["id"];
			$respuesta = DatosEgresoIngreso::editarEgresoModel($datosController, "egresos");
			echo' <input type="text" value="'.$respuesta["motivo"].'" name="motivoEditar" required>
		 <input type="text" value="'.$respuesta["cantidad"].'" name="cantidadEditar" required>
		 <input type="hidden" value="'.$respuesta["id"].'" name="idEditar" required>
			 <input type="submit" value="Actualizar">';
		}
	#------------------------------------
	#ACTUALIZAR ENGRESO
	#------------------------------------
	 public function actualizarEgresoController()
		{//echo "<script>alert('Entro Controller Actualizar Producto')</script>";
			if(isset($_POST["motivoEditar"]))
			 {
				$datosController = array(  "motivo"=>$_POST["motivoEditar"],
											"cantidad"=>$_POST["cantidadEditar"],
											"fecha_hora"=>$_POST["fecha_horaEditar"],
											"id"=>$_POST["idEditar"]);
					$respuesta = DatosEgresoIngreso::actualizarEgresoModel($datosController, "egresos");
					if($respuesta == "success")
						{	header("location:index.php?action=cambioE");	}
					else
						{	echo "error";	}
				}
		}

	#BORRAR ENGRESO
	#------------------------------------
		public function borrarEgresoController()
			{
				if(isset($_GET["idBorrar"]))
					{
						$datosController = $_GET["idBorrar"];
						$respuesta = DatosEgresoIngreso::borrarEgresoModel($datosController, "egresos");
						if($respuesta == "success")
							{	header("location:index.php?action=egreso");	}
					}
			}
	#---------------------------------

 }
