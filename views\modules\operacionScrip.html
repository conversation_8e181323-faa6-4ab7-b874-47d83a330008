<html>
	<head> <PERSON><PERSON> </head>
	<title>Suma</title>
	<body>

		<script type=”text/javascript”>

			function mostrar()
				{
					var Num1=parseFloat (document.getElementById(‘Num1’).value);
					var Num2=parseFloat (document.getElementById(‘Num2’).value);
					var Resul= Num1 + Num2;
					alert(‘El Resultado de La Suma Es:’ + Resul);
				}

			function suma()
			{
			var N1=parseFloat (document.getElementById(‘N1’).value);
			var N2=parseFloat (document.getElementById(‘N2’).value);
			var Suma= N1 + N2;
			alert(‘El Resultado de La Suma Es:’ + Suma);
			}

			function Resta()
			{
			var N1=parseFloat (document.getElementById(‘N1’).value);
			var N2=parseFloat (document.getElementById(‘N2’).value);
			var Resta= N1 – N2;
			alert(‘El Resultado de La Resta Es:’ + Resta);
			}
			function Multiplicacion()
			{
			var N1=parseFloat (document.getElementById(‘N1’).value);
			var N2=parseFloat (document.getElementById(‘N2’).value);
			var Multi= N1 * N2;
			alert(‘El Resultado de La Multiplicacion Es:’ + Multi);
			}
			function Divicion()
			{
			var N1=parseFloat (document.getElementById(‘N1’).value);
			var N2=parseFloat (document.getElementById(‘N2’).value);
			var Divic= N1 / N2;
			alert(‘El Resultado de La Divicion Es:’ + Divic);
			}
		</script>

		<h1>Todas las Operaciones Matematicas</h1>
		<br>

		<h2>Suma de Dos Numeros</h2>
		<form>
		Ingrese el Primer Numero:
		<input type=”text” id=”Num1″><br>
		Ingrese El Segundo Numero:
		<input type=”text” id=”Num2″><br>
		<br>
		<br>
		<input type=”button” value=”Confirmar” onClick=”mostrar()”>
		</form>

		<form>

		<h2> Operaciones Matematicas</h2>
		Ingrese el Primer Numero:
		<input type=”text” id=”N1″><br>
		Ingrese El Segundo Numero:
		<input type=”text” id=”N2″><br>
		<br>
		<br>

		<input type=”button” value=”Suma” onClick=”suma()”>
		<input type=”button” value=”Resta” onClick=”Resta()”>
		<input type=”button” value=”Multiplicacion” onClick=”Multiplicacion()”>
		<input type=”button” value=”Divicion” onClick=”Divicion()”>
		</form>
	</body>
</html>

