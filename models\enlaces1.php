<?php 

class Paginas{
	
	public function enlacesPaginasModel($enlaces)

		{
			session_start();
			public function enlacesPaginasModel($enlaces)
		{
			session_start();
			if($enlaces == "ingresar" || $enlaces == "pagina1"  || $enlaces == "salir"  )
			{

				$module =  "views/modules/".$enlaces.".php";			
			}	

			else if($enlaces == "pagina1")
				{	$module =  "views/modules/pagina1.php"; }	

			else if($enlaces == "index")
				{	$module =  "views/modules/ingresar.php"; }

			else if($enlaces == "ok")
				{	$module =  "views/modules/registro.php"; }

			else if($enlaces == "fallo")
				{	$module =  "views/modules/ingresar.php"; }


			else if(  $_SESSION["tipo_usuario"]== 1)
				{			
							
							
							else if($enlaces == "cambio")
								{	$module =  "views/modules/usuarios.php"; }

					# Nav o Menu----------------------------------------------
						# Usuario----------------------------------------------
							else if($enlaces == "registro" && $_SESSION["tipo_usuario"]== 1)
								{	$module =  "views/modules/registro.php"; }

							else if($enlaces == "usuarioE" && $_SESSION["tipo_usuario"]== 1)
								{	$module =  "views/modules/usuarios.php"; }

							else if($enlaces == "usuario" && $_SESSION["tipo_usuario"]== 1)
								{	$module =  "views/modules/usuarioC.php"; }
						# Usuario----------------------------------------------

						# Producto----------------------------------------------
							else if($enlaces == "registroProducto"  || $enlaces == "Producto")
								{	$module =  "views/modules/registroProducto.php"; }

							else if($enlaces == "ingresarProducto")
								{	$module =  "views/modules/ingresarProducto.php"; }
							
							else if($enlaces == "productos")
								{	$module =  "views/modules/productos.php"; }
							
							else if($enlaces == "productoC")
								{	$module =  "views/modules/productosC.php"; }		
						# Producto----------------------------------------------

						# Transportador----------------------------------------------
							else if($enlaces == "registroTransportadora" ||  $enlaces == "TransportadoraR")
								{	$module =  "views/modules/registroTransportadora.php"; }

							else if($enlaces == "Transportadora")
								{	$module =  "views/modules/Transportadora.php"; }

							else if($enlaces == "TransportadoraC")
								{	$module =  "views/modules/TransportadoraC.php"; }

							else if($enlaces == "editarTransportadora")
								{	$module =  "views/modules/editarTransportadora.php"; }

							else if($enlaces == "editarTransportadora")
								{	$module =  "views/modules/editarTransportadora.php"; }
						# Transportador----------------------------------------------

						# Zona----------------------------------------------
							else if($enlaces == "registroZona" )
								{	$module =  "views/modules/registroZona.php"; }

							else if($enlaces == "Zona")
								{	$module =  "views/modules/Zona.php"; }

							else if($enlaces == "ZonaC")
								{	$module =  "views/modules/ZonaC.php"; }

							else if($enlaces == "editarZona")
								{	$module =  "views/modules/editarZona.php"; }
						# Zona----------------------------------------------

						# Orden----------------------------------------------
							else if($enlaces == "registroOrden" )
								{	$module =  "views/modules/registroOrden.php"; }

							else if($enlaces == "registroOrdenCarga" )
								{	$module =  "views/modules/registroOrdenCarga.php"; }

							else if($enlaces == "Orden")
								{	$module =  "views/modules/Orden.php"; }

							else if($enlaces == "OrdenC")
								{	$module =  "views/modules/OrdenC.php"; }
						# Orden----------------------------------------------


						# MULA----------------------------------------------
							else if($enlaces == "registroOrden" )
								{	$module =  "views/modules/registroOrden.php"; }

							else if($enlaces == "actualizarMula")
								{	$module =  "views/modules/actualizarMula.php"; }

							else if($enlaces == "editarMula")
								{	$module =  "views/modules/editarMula.php"; }
						# MULA----------------------------------------------		
					
						# Punto----------------------------------------------
							else if($enlaces == "registroPunto" )
								{	$module =  "views/modules/registroPunto.php"; }

							else if($enlaces == "editarPunto")
								{	$module =  "views/modules/editarPunto.php"; }

							else if($enlaces == "actualizarPunto")
								{	$module =  "views/modules/editarPunto.php"; }
						# Punto----------------------------------------------	

							else if($enlaces == "registroConductor")
								{	$module =  "views/modules/registroConductor.php"; }
					
							else if($enlaces == "usuarioEf" /*&& $admin== "1" */)
								{	$module =  "views/modules/usuarios.php"; }			

					# fin Nav o Menu----------------------------------------------

						}
				else if(  $_SESSION["tipo_usuario"]== 2)
						{
							# Nav o Menu----------------------------------------------
								# Producto----------------------------------------------
									else if($enlaces == "registroProducto"  || $enlaces == "Producto")
										{	$module =  "views/modules/registroProducto.php"; }

									else if($enlaces == "ingresarProducto")
										{	$module =  "views/modules/ingresarProducto.php"; }
									
									else if($enlaces == "productos")
										{	$module =  "views/modules/productos.php"; }
									
									else if($enlaces == "productoC")
										{	$module =  "views/modules/productosC.php"; }		
								# Producto----------------------------------------------
								# Transportador----------------------------------------------
									else if($enlaces == "registroTransportadora" ||  $enlaces == "TransportadoraR")
										{	$module =  "views/modules/registroTransportadora.php"; }

									else if($enlaces == "Transportadora")
										{	$module =  "views/modules/Transportadora.php"; }

									else if($enlaces == "TransportadoraC")
										{	$module =  "views/modules/TransportadoraC.php"; }

									else if($enlaces == "editarTransportadora")
										{	$module =  "views/modules/editarTransportadora.php"; }

									else if($enlaces == "editarTransportadora")
										{	$module =  "views/modules/editarTransportadora.php"; }
								# Transportador----------------------------------------------
								# Zona----------------------------------------------
									else if($enlaces == "registroZona" )
										{	$module =  "views/modules/registroZona.php"; }

									else if($enlaces == "Zona")
										{	$module =  "views/modules/Zona.php"; }

									else if($enlaces == "ZonaC")
										{	$module =  "views/modules/ZonaC.php"; }

									else if($enlaces == "editarZona")
										{	$module =  "views/modules/editarZona.php"; }
								# Zona----------------------------------------------
								# Orden----------------------------------------------
									else if($enlaces == "registroOrden" )
										{	$module =  "views/modules/registroOrden.php"; }

									else if($enlaces == "registroOrdenCarga" )
										{	$module =  "views/modules/registroOrdenCarga.php"; }

									else if($enlaces == "Orden")
										{	$module =  "views/modules/Orden.php"; }

									else if($enlaces == "OrdenC")
										{	$module =  "views/modules/OrdenC.php"; }
								# Orden----------------------------------------------
								# MULA----------------------------------------------
									else if($enlaces == "registroOrden" )
										{	$module =  "views/modules/registroOrden.php"; }

									else if($enlaces == "actualizarMula")
										{	$module =  "views/modules/actualizarMula.php"; }

									else if($enlaces == "editarMula")
										{	$module =  "views/modules/editarMula.php"; }
								# MULA----------------------------------------------								
								# Punto----------------------------------------------
									else if($enlaces == "registroPunto" )
										{	$module =  "views/modules/registroPunto.php"; }

									else if($enlaces == "editarPunto")
										{	$module =  "views/modules/editarPunto.php"; }

									else if($enlaces == "actualizarPunto")
										{	$module =  "views/modules/editarPunto.php"; }
								# Punto----------------------------------------------	

									else if($enlaces == "registroConductor")
										{	$module =  "views/modules/registroConductor.php"; }
							
									else if($enlaces == "usuarioEf" /*&& $admin== "1" */)
										{	$module =  "views/modules/usuarios.php"; }						

							# fin Nav o Menu----------------------------------------------

						}

			else if($enlaces == "principio")
					{	$module =  "views/modules/pagina.html"; }
			else{

				$module =  "views/modules/ingresar.php";

			}
			
			return $module;

		}

}

?>