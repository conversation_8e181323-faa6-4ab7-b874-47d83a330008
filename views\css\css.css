body {padding-top: 150px; /* Altura estándar de la navbar fija */}
@media (max-width: 767px) {
  body {padding-top: 60px; /* Ajuste si la navbar en móvil es más delgada */}
}
.todo
    {
        width: 100%;
        height: auto;
    }


@media screen and (max-width: 767px) {
  .todo { padding: 0; width: 100%; height: auto;}
}

.mayuscula{	text-transform: uppercase;  }
#estilo5{color:	#FFFF00;background:#0000FF;height:40px	}	
#estilo1{color:#FF0000;background:#020E61;height:100px	}	
#estilo2{color:#020E61;background:#FAD6A5;height:100px	}
#estilo3{color:#020E61;background:#ccc;height:40px	}
#estilo4{color:#020E61;background:#eee;height:40px	}
#foto{
   /* border: 3px solid #000;
    position: absolute;
    width: 100px;
    height: 100px;*/
    z-index: 2;
	}
#enla{
   /* position: relative;
    background-color: #336699;
    color: #fff;
    width: 100px;
    height: 100px;
    padding: 3px;
    text-align: center;*/
    z-index: 1;
    }
button, select, input
 {    border-radius: 10px 0px 10px 0px; }
textarea{border-radius: 10px 10px 10px 10px;}
input[type="text"]
    {   /*height: 10px;*/       width: 180px;    }    
textarea,select, td, input {padding:2px;}
.descuento {  width: 50px !important;}
input[type="password"]     
    {   width: 150px;}
input[type="date"]     
    {   width: 150px;}/**/
/* +++++++++++++++++++ Color estado Mesa  ++++++++++++++++++++++++*/
	.cero{ background-color: yellow;}
	.uno{  background-color: red;}
	.dos{  background-color: #848484 !important;}

/* +++++++++++++++++++ Color estado Mesa  ++++++++++++++++++++++++*/

.filaTotal {
    font:Arial, Helvetica, sans-serif;
    font-family:Arial, Helvetica, sans-serif;
    font-size:22px;
    text-decoration:none;
    color:#000;
    font-weight:bold;
    border:0;
    background-color:   #FFFF00;}

.filaTotal1 {
    font:Arial, Helvetica, sans-serif;
    font-family:Arial, Helvetica, sans-serif;
    font-size:26px;
    text-decoration:none;
    color:#000;
    font-weight:bold;
    border:0;
    background-color:   #00f0ff;
}
thead {
    font:Arial, Helvetica, sans-serif;
    font-family:Arial, Helvetica, sans-serif;
    font-size:14px;
    text-align: center;
    text-decoration:none;
    color:#fff;
    font-weight:bold;
    border:0;
    background-color:   #020E61;
    padding:5px;
}

tbody {
    font:Arial, Helvetica, sans-serif;
    font-family:Arial, Helvetica, sans-serif;
      font-weight:bold;
    border:0;
    background-color: #76b5cd; 

}
.orden
 {
     font:Arial, Helvetica, sans-serif;
    font-family:Arial, Helvetica, sans-serif;
    font-size:20px;
    font-weight:bold;
    border:1;
    background-color: #fff;
 }
.enlace {
    font:Arial, Helvetica, sans-serif;
    font-family:Arial, Helvetica, sans-serif;
    font-size:12px;
    text-align: center;
    text-decoration:none;
    color:#fff;
    font-weight:bold;

}
.nombre {
    font:Arial, Helvetica, sans-serif;
    font-family:Arial, Helvetica, sans-serif;
    font-size:14px;
    text-align: center;

    color:#fff;
    font-weight:bold;

}
caption { 
    display: table-caption;
    text-align: center;
}