<?php
require_once "conexion.php";
require_once "crudFacturaCompraSuministro.php";
class DatosComprasuministro extends Conexion
{
	#CONSULTAR tabla si, existe el proveedor con esa factura
	#------------------------------------------
	 public static function poveedorFacturaModel($dato, $dato1)
		{//echo "<script>alert('entro cruz 2 factura".$dato." guardar');</script>";
		 $consulta="SELECT * FROM compras_suministros WHERE proveedor_id =$dato AND numero_factura_compra= $dato1	  ";
		 $stmt = Conexion::conectar()->prepare($consulta);

		 echo "<br>".$consulta."<br>";
		 $stmt->execute();
		 $r= $stmt->fetch();
		 $c2=$stmt->rowCount();
		 //echo '<br> C2='.$c2.'<br>';
		 if ($c2>0)
		  	{ 	//echo "<script>alert(' if entro consulta ".$r['dcuenta'] ."')</script>";
		  	 return $r;
		  	}
		 else
		 	{//echo "<script>alert(' Error BD Buscar deuda')</script>";
		 	 return 0;
		    }
		 $stmt->close();
		}
	#------------------------------------------
	#CONSULTAR tabla temporal todo pendiente
	#------------------------------------------
	 public static function pendienteFactura1Model()
		{//echo "<script>alert('entro cruz 2 factura".$dato." guardar');</script>";
		 $consulta="SELECT pf.id AS pfid, pf.proveedor_id AS pfproveedor_id, pf.valor_factura AS pfvalor_factura, pf.fecha_factura AS pffecha_factura, pf.numero_factura AS pfnumero_factura, pf.pago AS pfpago, pf.abono AS pfabono, pf.suministro_id AS pfsuministro_id, pf.cantidad AS pfcantidad, pf.precio AS pfprecio, pf.precioV AS pfprecioV, pf.descuento AS pfdescuento, pf.facturado AS pffacturado, pf.precioU AS pfprecioU, pf.iva AS pfiva,
				s.id As sid, s.codigo AS scodigo, s.nombre AS snombre
			from pendiente_factura pf, suministros s
			where s.id=pf.suministro_id AND pf.cantidad>0 ";
		 $stmt = Conexion::conectar()->prepare($consulta);
		// echo "<br>".$consulta."<br>";
		 $stmt->execute();
		 $r= $stmt->fetchAll();
		 $c2=$stmt->rowCount();
		 //echo '<br> C2='.$c2.'<br>';
		 if ($c2>0)
		  	{ 	//echo "<script>alert(' if entro consulta ".$r['dcuenta'] ."')</script>";
		  	 return $r;
		  	}
		 else
		 	{//echo "<script>alert(' Error BD Buscar deuda')</script>";
		 	 return 0;
		    }
		 $stmt->close();
		}
	#------------------------------------------
	#CONSULTAR tabla temporal
	#------------------------------------------
	 public static function pendienteFacturaModel()
		{//echo "<script>alert('entro cruz 2 factura".$dato." guardar');</script>";
		 $consulta="SELECT * FROM pendiente_factura	  ";
		 $stmt = Conexion::conectar()->prepare($consulta);
		// echo "<br>".$consulta."<br>";
		 $stmt->execute();
		 $r= $stmt->fetch();
		 $c2=$stmt->rowCount();
		 //echo '<br> C2='.$c2.'<br>';
		 if ($c2>0)
		  	{ 	//echo "<script>alert(' if entro consulta ".$r['dcuenta'] ."')</script>";
		  	 return $r;
		  	}
		 else
		 	{//echo "<script>alert(' Error BD Buscar deuda')</script>";
		 	 return 0;
		    }
		 $stmt->close();
		}
	#------------------------------------------
	#REGISTRO DE COMPRA SUMINISTRO tabla temporal pendiente
	#-------------------------------------
	 public static function registroCsuministroPModel($datosModel, $tabla)
		{
					//echo "<script>alert('Entro CRUD ".$datosModel['fecha_hora']." no')</script>";
			date_default_timezone_set("America/Bogota");
			$fecha_in=strftime("%Y-%m-%d %H:%M:%S");//fecha local y hora

			//INSERT INTO pendiente_factura(id, proveedor_id, valor_factura, fecha_factura, numero_factura, pago, abono, suministro_id, cantidad, precio, descuento, facturado) VALUES ( 1, 2, '3', '4', 5, 6, 7, 8, 9, 10, 11, '12')
			$consulta="INSERT INTO $tabla (id, proveedor_id, valor_factura, fecha_factura, numero_factura, pago, abono) VALUES (1, ".$datosModel['proveedor_id'].", ".$datosModel['valor_compra'].", '".$datosModel['fecha_hora']."', '".$datosModel['numero_factura_compra']."', '".$datosModel['pago']."', ".$datosModel['abono'].")";

			//echo "<script>alert('Entro CRUD ".$consulta." no')</script>";
			//$stmt = Conexion::conectar()->prepare($consulta);
			$stmt = Conexion::conectar();
			$stmt->exec($consulta);
			/*$stmt->execute();
			$stmt->bindParam(":proveedor_id", $datosModel['proveedor_id'], PDO::PARAM_INT);
			$stmt->bindParam(":valor_compra", $datosModel['valor_compra'], PDO::PARAM_INT);
			$stmt->bindParam(":fecha_hora", $datosModel['fecha_hora'], PDO::PARAM_STR);
			$stmt->bindParam(":numero_factura_compra", $datosModel['numero_factura_compra'], PDO::PARAM_INT);
			$stmt->bindParam(":fecha_modificacion", $fecha_in, PDO::PARAM_STR);
			*/echo "<script>alert('Guardo')</script>";
			$ultimo_id=$stmt->lastInsertId();
			if($ultimo_id>0)
				{
					$respuesta = array('success' => "success" );
					//echo "<script>alert('Entro CRUD ".$respuesta['idcompra']." no')</script>";
					return $respuesta;
					//return "success";
				}
			else{ echo "<script>alert('No guardo')</script>"; return "error";	}
			$stmt->close();
		}
	#-----------------------------------
	#REGISTRO DE COMPRA SUMINISTRO
	#-------------------------------------
	 public static function registroCsuministroModel($datosModel, $tabla)
		{
			//echo "<script>alert('Entro CRUD ".$datosModel['fecha_hora']." no')</script>";
			date_default_timezone_set("America/Bogota");
			$fecha_in=strftime("%Y-%m-%d %H:%M:%S");//fecha local y hora

			//$consulta="INSERT INTO $tabla (proveedor_id, valor_compra, fecha_hora, numero_factura_compra, fecha_modificacion) VALUES (:proveedor_id, :valor_compra, :fecha_hora, :numero_factura_compra, :fecha_modificacion)";
			$consulta="INSERT INTO $tabla (proveedor_id, valor_compra, fecha_hora, numero_factura_compra, fecha_modificacion) VALUES ( ".$datosModel['proveedor_id'].", ".$datosModel['valor_compra'].", '".$datosModel['fecha_hora']."', '".$datosModel['numero_factura_compra']."', '$fecha_in' )";

			echo "<script>alert('Entro CRUD ".$consulta." no')</script>";
			//$stmt = Conexion::conectar()->prepare($consulta);
			$stmt = Conexion::conectar();
			$stmt->exec($consulta);
			/*$stmt->execute();
			$stmt->bindParam(":proveedor_id", $datosModel['proveedor_id'], PDO::PARAM_INT);
			$stmt->bindParam(":valor_compra", $datosModel['valor_compra'], PDO::PARAM_INT);
			$stmt->bindParam(":fecha_hora", $datosModel['fecha_hora'], PDO::PARAM_STR);
			$stmt->bindParam(":numero_factura_compra", $datosModel['numero_factura_compra'], PDO::PARAM_INT);
			$stmt->bindParam(":fecha_modificacion", $fecha_in, PDO::PARAM_STR);
			*/echo "<script>alert('Guardo')</script>";
			$ultimo_id=$stmt->lastInsertId();
			if($ultimo_id>0)
				{	echo "<script>alert('entro al if CRUD ')</script>";
					$consulta1="SELECT MAX(id) as max_mark FROM `compras_suministros`";
					$stmt = Conexion::conectar()->prepare($consulta1);
					$stmt->execute();
					$mayor=Datos::MaximoModel("compras_suministros");
					$respuesta = array('idcompra' =>$mayor['mayor'] ,
										'success' => "success" );
					//echo "<script>alert('Entro CRUD ".$respuesta['idcompra']." no')</script>";
					return $respuesta;
					//return "success";
				}
			else{ echo "<script>alert('No guardo')</script>"; return "error";	}
			$stmt->close();
		}
	#-----------------------------------
	#REGISTRO DE COMPRA SUMINISTRO tabla temporar Aja
	#-------------------------------------
	 public static function registroCsuministroAjaTModel($datosModel, $tabla)
		{ //echo "<script>alert('Entro CRUD compra ".$datosModel['id']." tipo de pago: ".$datosModel['numero_factura_compra']."')</script>";
			//nombre codigo cantidad referencia genero talla marca color diseno precioC precioV
			date_default_timezone_set("America/Bogota");
			$fecha_hora=strftime("%Y-%m-%d %H:%M:%S");//fecha local y hora
			$buscar = DatosComprasuministro::buscarFacturaCompra($datosModel['proveedor_id'], $datosModel['numero_factura_compra']);
			//echo "<br>buscar".$buscar."<br>";
			$stmt = Conexion::conectar();
			date_default_timezone_set("America/Bogota");
			$fecha_in=strftime("%Y-%m-%d %H:%M:%S");//fecha local y hora
		 try
			{	//echo "<script>alert('try compras_suministros' );</script>";
				$stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
				$stmt->beginTransaction();
				//echo "<script>alert('beginTransaction' );</script>";
				//// Inserta la factura//
			 if ($buscar==0)
				{
				 $consulta="INSERT INTO $tabla (proveedor_id, valor_compra, fecha_hora, pago, abono, numero_factura_compra, nombre, codigo, sid, precioC, cantidad, precioV, fecha)
				 VALUES (".$datosModel['proveedor_id'].", ".$datosModel['valor_compra'].", '".$datosModel['fecha_hora']."', ".$datosModel['pago'].", ".$datosModel['abono'].", ".$datosModel['numero_factura_compra'].",  '".$datosModel['nombre']."', ".$datosModel['codigo'].", ".$datosModel['id'].", ".$datosModel['precioC'].", ".$datosModel['cantidad'].", ".$datosModel['precioV'].", '".$fecha_in."')";
				 //echo "<br> --".$consulta."--<br> ";
				 //echo "<script>alert('consulta uno ".$consulta."' );</script>";
				 $stmt->exec($consulta);
				 $ultimo_id=$stmt->lastInsertId();	//ultimo factura
				}
			 else
			 	{ ///echo "<script>alert('Esta factura : ".$datosModel['numero_factura_compra'].", ya existe con este proveedor:".$buscar['nombre']." ' );</script>";
			 	 $ultimo_id=$buscar['id'];
			 	}
			 $stmt->commit();
			 //echo "<script>alert('fin de cruD try que pasa');</script>";
			 //echo "<script>alert('entro al if CRUD ')</script>";
			 $mayor=Datos::MaximoModel("compras_suministros");
			 $respuesta = array('idcompra' =>$mayor['mayor'] ,
			 					'success' => "success",
			 					'id' => $ultimo_id
			 				);
			 //echo "<script>alert('Entro CRUD ".$respuesta['idcompra']." success: ".$respuesta['success']."')</script>";
			 return $respuesta;
			 //return "success";
			 $stmt->close();
			}
		 catch (PDOException $e)
			{	echo "<script>alert('catch entro')</script>";
				  $stmt->rollBack();
				print "Error!: ".$e->getMessage()."</br>";
				return "Error!: ".$e->getMessage()."</br>";
			}
		}
	#-------------------------------------------
	#CONSULTAR tabla temporal
	#------------------------------------------
	 public static function temporalFacturaModen($dato, $dato1)
		{//echo "<script>alert('entro cruz 2 factura".$dato." guardar');</script>";
		 $consulta="SELECT * FROM compra_suministro	 WHERE compra_suministro_id = :id AND numero_factura_compra = :factura ";
		 $stmt = Conexion::conectar()->prepare($consulta);
		 $stmt->bindParam(":id", $dato, PDO::PARAM_INT);
		 $stmt->bindParam(":factura", $dato1, PDO::PARAM_INT);
		 //echo "<br>".$consulta."<br>";
		 if($stmt->execute())
			{	return $stmt->fetchAll();	}
		 else{	return "error";		}
		 $stmt->close();
		}
	#------------------------------------------
	#REGISTRO DE COMPRA SUMINISTRO Unificado
	#-------------------------------------
		public static function registroCompraSuministroModel($datosModel, $tabla)
			{
					//echo "<script>alert('Entro CRUD ".$datosModel['fecha_hora']." no')</script>";
				date_default_timezone_set("America/Bogota");
				$fecha_in=strftime("%Y-%m-%d %H:%M:%S");//fecha local y hora

				$consulta="INSERT INTO $tabla (proveedor_id, valor_compra, fecha_hora, numero_factura_compra, fecha_modificacion, credito) VALUES (:proveedor_id, :valor_compra, :fecha_hora, :numero_factura_compra, :fecha_modificacion, 'n')";
				//echo "<script>alert('Entro CRUD ".$consulta." no')</script>";
				$stmt = Conexion::conectar()->prepare($consulta);

				$stmt->bindParam(":proveedor_id", $datosModel['proveedor_id'], PDO::PARAM_INT);
				$stmt->bindParam(":valor_compra", $datosModel['valor_compra'], PDO::PARAM_INT);
				$stmt->bindParam(":fecha_hora", $datosModel['fecha_hora'], PDO::PARAM_STR);
				$stmt->bindParam(":numero_factura_compra", $datosModel['numero_factura_compra'], PDO::PARAM_INT);
				$stmt->bindParam(":fecha_modificacion", $fecha_in, PDO::PARAM_STR);
				//echo "<script>alert('Guardo')</script>";
				//$ultimo_id=$stmt->lastInsertId();
				//echo "<br>".$consulta."<br>";

			if($stmt->execute())
				{	//echo "<script>alert('entro al if CRUD ')</script>";
					$consulta1="SELECT MAX(id) as max_mark FROM `compras_suministros`";
					$stmt = Conexion::conectar()->prepare($consulta1);
					$stmt->execute();

					$mayor=Datos::MaximoModel("compras_suministros");

					$respuesta = array('idcompra' =>$mayor['mayor'] ,
										'success' => "success" );
					//echo "<script>alert('Entro CRUD ".$respuesta['idcompra']." no')</script>";
					return $respuesta;
					//return "success";
				}

			/*try{

					$consulta1="INSERT INTO creditos_proveedores(compras_suministro_id, abonos_proveedores_id, cuenta) VALUES (1,2,3)";
					$consulta1="INSERT INTO abonos_proveedores(turnos_cajeros_id,  , valor) VALUES (1,2,3)";


					$stmt->commit();
					//echo "<script>alert('fin de cruD try que pasa');</script>";
					return "success";
					$stmt->close();
				}
			catch (PDOException $e)
				{	echo "<script>alert('catch entro')</script>";
					  $stmt->rollBack();
					print "Error!: ".$e->getMessage()."</br>";
					return "Error!: ".$e->getMessage()."</br>";
				} */
		}
	#-------------------------------------------
	##REGISTRO DE COMPRA SUMINISTRO id, proveedor_id, valor_compra, fecha_hora, numero_factura_compra
	#-------------------------------------
		public static function registroCsuministro1Model($datosModel, $tabla)
		 {	//echo "<script>alert('Entro CRUD 1 ".$datosModel['fecha_hora']." tipo de pago2: ".$datosModel['pago']."  3".$datosModel['proveedor_id']." 4 ".$datosModel['valor_compra']." 5".$datosModel['abono']."  6".$datosModel['numero_factura_compra']."')</script>";
			$pago=n;
			$stmt = Conexion::conectar();
			date_default_timezone_set("America/Bogota");
			$fecha_in=strftime("%Y-%m-%d %H:%M:%S");//fecha local y hora

			//// Inserta el factura//
			$consulta="INSERT INTO $tabla (proveedor_id, valor_compra, fecha_hora, numero_factura_compra, fecha_modificacion, credito) VALUES (".$datosModel['proveedor_id'].", ".$datosModel['valor_compra'].", '".$datosModel['fecha_hora']."', ".$datosModel['numero_factura_compra'].", '".$fecha_in."', '".$pago."')";

			//echo " --".$consulta;
			$stmt->exec($consulta);
			// $ultimo_id=$stmt->lastInsertId();	//ultimo factura
			//echo "<script>alert('consulta ".$consulta."    -ultimo ".$ultimo_id." ' );</script>";
			return "success";
		 }
	#-------------------------------------------
	#CONSULTAR DETALLE COMPRA SUMINISTRO SEGUN EL ID COMPRA SUMINISTRO
	#------------------------------------------
	 public static function detalleFacturaModen($dato)
		{//echo "<script>alert('entro cruz 2 factura".$dato." guardar');</script>";
		 $consulta="SELECT suministro_id, compra_suministro_id, cantidad, precio FROM detalle_factura_suministro WHERE compra_suministro_id = :id ";
		 $stmt = Conexion::conectar()->prepare($consulta);
		 $stmt->bindParam(":id", $dato, PDO::PARAM_INT);
		 //echo "<br>".$consulta."<br>";
		 if($stmt->execute())
			{	return $stmt->fetchAll();	}
		 else{	return "error";		}
		 $stmt->close();
		}
	#------------------------------------------
	#BUSCAR FACTURA SUMINISTRO
	#----------------------------------------
	 public static function buscarFacturaCompra($datosModel, $datosModel1)
		{
		 $consulta = "SELECT id, proveedor_id, valor_compra, fecha_hora, numero_factura_compra, fecha_modificacion, credito FROM compras_suministros WHERE proveedor_id = :proveedor_id AND numero_factura_compra = :numero_factura_compra" ;
		 $stmt = Conexion::conectar()->prepare($consulta);
		 $stmt->bindParam(":proveedor_id", $datosModel, PDO::PARAM_INT);
		 $stmt->bindParam(":numero_factura_compra", $datosModel1, PDO::PARAM_STR);
		 //echo "<br>".$consulta."<br>";
		 $stmt->execute();
		 $r= $stmt->fetch();
		 $c2=$stmt->rowCount();
		 //echo '<br> C2='.$c2.'<br>';
		 if ($c2>0)
		  	{ 	//echo "<script>alert(' if entro consulta ".$r['dcuenta'] ."')</script>";
		  	 return $r;
		  	}
		 else
		 	{//echo "<script>alert(' Error BD Buscar deuda')</script>";
		 	 return 0;
		    }
		 $stmt->close();
		}
	#---------------------------------------
	#REGISTRO DE COMPRA SUMINISTRO unificado Aja tabla pendiente
	#-------------------------------------
	 public static function registroCsuministroAja1Model( $tabla)
		{ //echo "<script>alert('Entro CRUD compra ".$datosModel['id']." tipo de pago: ".$datosModel['numero_factura_compra']."')</script>";
			//nombre codigo cantidad referencia genero talla marca color diseno precioC precioV
			date_default_timezone_set("America/Bogota");
			$fecha_hora=strftime("%Y-%m-%d %H:%M:%S");//fecha local y hora
			$buscar = DatosComprasuministro::pendienteFactura1Model();
			//echo "<br>buscar".$buscar."<br>";
			$stmt = Conexion::conectar();
			date_default_timezone_set("America/Bogota");
			$fecha_in=strftime("%Y-%m-%d %H:%M:%S");//fecha local y hora
		 try
			{	//echo "<script>alert('try compras_suministros' );</script>";
				$stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
				$stmt->beginTransaction();
				//echo "<script>alert('beginTransaction' );</script>";
				//// Inserta la factura//
			 if ($buscar==0)
				{
					echo "<script>alert('No hay Registro q guardar! ' );</script>";
				}
			 else
			 	{//(`id`, `proveedor_id`, `valor_factura`, `fecha_factura`, `numero_factura`, `pago`, `abono`, `suministro_id`, `cantidad`, `precio`, `precioV`, `descuento`, `facturado`)
			 		$sw=0;
			 	 foreach ($buscar as $rows => $item)
			 		{ //echo "<script>alert('Entro al foreach' );</script>";
			 		 if ($sw==0)
			 			{
			 			 if ($item['pfpago']==1)
							 {	$pago='n';	 }
						 else
							 {	$pago='s';	 }
			 			 $consulta="INSERT INTO $tabla (proveedor_id, valor_compra, fecha_hora, numero_factura_compra, fecha_modificacion, credito) VALUES (".$item['pfproveedor_id'].", ".$item['pfvalor_factura'].", '".$item['pffecha_factura']."', '".$item['pfnumero_factura']."', '".$fecha_in."', '".$pago."')";
						 echo "<br> --".$consulta."--<br> ";
						 //echo "<script>alert('consulta uno ".$consulta."' );</script>";
						 $stmt->exec($consulta);
						 $ultimo_id=$stmt->lastInsertId();	//ultimo factura

						 //echo "<script>alert('consulta ".$consulta."    -ultimo ".$ultimo_id." ' );</script>";// Registra el credito

						 if ($item['pfpago']==2)
							{ //si tiene abono
							 if ($item['abono']!=0)
								{
								 	date_default_timezone_set("America/Bogota");
									$fecha_abono=strftime("%Y-%m-%d %H:%M:%S");//fecha local y hora
									//tabla abonos
									$consulta1="INSERT INTO abonos_proveedores(turnos_cajeros_id, fecha_abono, valor, descripcion) VALUES (".$_SESSION["turno"].",'".$fecha_in."',".$item['pfabono'].",'Abono')";
									echo "<br> --".$consulta1."--<br> ";
									$stmt->exec($consulta1);
									$ultimo_abono=$stmt->lastInsertId();	//ultimo factura
									$cuenta = $item['pfvalor_factura'] - $item['pfabono'];
									//tabla credito
									$consulta2="INSERT INTO creditos_proveedores(compras_suministro_id, abonos_proveedores_id, cuenta) VALUES ($ultimo_id,$ultimo_abono, $cuenta)";
									echo "<br> --".$consulta2."--<br> ";
									$stmt->exec($consulta2);
								}
							 else
								{
								 	$consulta2="INSERT INTO creditos_proveedores(compras_suministro_id,  cuenta) VALUES (".$ultimo_id.",".$item['pfvalor_factura'].")";
									echo "<br>Credito sin abono --".$consulta2."--<br> ";
									$stmt->exec($consulta2);
									$ultimo_idcr=$stmt->lastInsertId();
								}
						 	}
						 $sw=1;
						}
					 //Para Actualizar la cantidad de productos en la factura
					 //$consulta3 = "UPDATE suministros SET  cantidad=cantidad + ".$item["pfcantidad"].", precio =".$item["pfprecioU"].", iva= ".$item["pfiva"]." WHERE id = ".$item["pfsuministro_id"];

					  $consulta3 = "UPDATE sucursal su, suministros s  SET  su.cantidad=su.cantidad + ".$item["pfcantidad"].", s.precio =".$item["pfprecioU"].", s.iva= ".$item["pfiva"]." WHERE s.id=su.suministro_id and s.id = ".$item["pfsuministro_id"];

							//echo "<script>alert('insertar tabla venta o factura ".$consulta." ' );</script>";
					 //echo "<br> --".$consulta3."--<br> ".$item["pfprecioV"]."--<br> ";
					 $stmt->exec($consulta3);
					// $ultimoSuministro = $stmt->lastInsertId();	//ultimo id
					/* $consulta4 = "UPDATE  productos SET  precio =".$item["pfprecioV"]." WHERE codigo = ".$item["scodigo"];
					 echo "<br> Producto Precio:--".$item["pfprecioV"]."--<br> ";
					 echo "<br> --".$consulta4."--<br> ";
					 $stmt->exec($consulta4);*/
					/**/ //$ultimoProducto = $stmt->lastInsertId();	ultimo id
					 /// cerrar de suministro y producto
					 $consulta7="INSERT INTO detalle_factura_suministro (suministro_id, compra_suministro_id, cantidad, precio, descuento) VALUES (".$item["pfsuministro_id"].", ".$ultimo_id.",". $item['pfcantidad'].", ".$item['pfprecio'].", ".$item['pfdescuento'].")";
					 //echo "<br> --".$consulta7."--<br> ";
					 $stmt->exec($consulta7);
					}
				}
			 $consulta8 = "TRUNCATE TABLE pendiente_factura ";
			 echo "<br> --".$consulta8."--<br> ";
			 $stmt->exec($consulta8);
			 $stmt->commit();
			// echo "<script>alert('fin de cruD try que pasa');</script>";
			 //echo "<script>alert('entro al if CRUD ')</script>";DELETE FROM
			/* $consultaF="SELECT MAX(id) as max_mark FROM `compras_suministros`"; pendiente_factura
			 $stmt->exec($consultaF);
			 $mayor=Datos::MaximoModel("compras_suministros");'idcompra' =>$mayor['mayor'] ,, 'id' => $ultimo_id  */
			/* $respuesta = array(
			 					'success' => "success"
			 					);
			 //echo "<script>alert('Entro CRUD ".$respuesta['idcompra']." no')</script>";
			 return $respuesta;
			 */return "success";
			 $stmt->close();
			}
		 catch (PDOException $e)
			{	echo "<script>alert('catch entro')</script>";
				  $stmt->rollBack();
				print "Error!: ".$e->getMessage()."</br>";
				return "Error!: ".$e->getMessage()."</br>";
			}
		}
	#-------------------------------------------
	#REGISTRO DE COMPRA SUMINISTRO unificado
	#-------------------------------------
	 public static function registroCsuministro3Model($datosModel, $tabla, $detalle)
		{ //echo "<script>alert('Entro CRUD compra ".$datosModel['fecha_hora']." tipo de pago: ".$datosModel['pago']."')</script>";
			//nombre codigo cantidad referencia genero talla marca color diseno precioC precioV
			date_default_timezone_set("America/Bogota");
			$fecha_hora=strftime("%Y-%m-%d %H:%M:%S");//fecha local y hora
			if ($datosModel['pago']==1)
			 {
				$pago='n';
			 }
			else
			 {	//echo "<script>alert('Else credito' );</script>";
			 	$pago='s';
			 }
			$stmt = Conexion::conectar();
			date_default_timezone_set("America/Bogota");
			$fecha_in=strftime("%Y-%m-%d %H:%M:%S");//fecha local y hora
			try
			 {	//echo "<script>alert('try compras_suministros' );</script>";
				$stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
				$stmt->beginTransaction();
				//echo "<script>alert('beginTransaction' );</script>";
				//// Inserta la factura//
				$consulta="INSERT INTO $tabla (proveedor_id, valor_compra, fecha_hora, numero_factura_compra, fecha_modificacion, credito) VALUES (".$datosModel['proveedor_id'].", ".$datosModel['valor_compra'].", '".$datosModel['fecha_hora']."', ".$datosModel['numero_factura_compra'].", '".$fecha_in."', '".$pago."', ".$datosModel['descuento']." )";
				//echo "<br> --".$consulta."--<br> ";
				//echo "<script>alert('consulta uno ".$consulta."' );</script>";
				$stmt->exec($consulta);
				$ultimo_id=$stmt->lastInsertId();	//ultimo factura
				//echo "<script>alert('consulta ".$consulta."    -ultimo ".$ultimo_id." ' );</script>";
				// Registra el credito
				if ($datosModel['pago']==2)
				 { //si tiene abono
					if ($datosModel['abono']!=0)
					 {
					 	date_default_timezone_set("America/Bogota");
						$fecha_abono=strftime("%Y-%m-%d %H:%M:%S");//fecha local y hora
						//tabla abonos
						$consulta1="INSERT INTO abonos_proveedores(turnos_cajeros_id, fecha_abono, valor, descripcion) VALUES (".$_SESSION["turno"].",'".$fecha_abono."',".$datosModel['abono'].",'Abono')";
						////echo "<br> --".$consulta1."--<br> ";
						$stmt->exec($consulta1);
						$ultimo_abono=$stmt->lastInsertId();	//ultimo factura
						$cuenta = $datosModel['valor_compra'] - $datosModel['abono'];
						//tabla credito
						$consulta2="INSERT INTO creditos_proveedores(compras_suministro_id, abonos_proveedores_id, cuenta) VALUES ($ultimo_id,$ultimo_abono,$cuenta)";
						//echo "<br> --".$consulta2."--<br> ";
						$stmt->exec($consulta2);
					 }
					else
					 {
					 	$consulta2="INSERT INTO creditos_proveedores(compras_suministro_id,  cuenta) VALUES (".$ultimo_id.",".$datosModel['valor_compra'].")";
						//echo "<br> --".$consulta2."--<br> ";
						$stmt->exec($consulta2);
						$ultimo_id=$stmt->lastInsertId();
					 }
				 }
					 //cargar la factura pero ante debe tener creado el producto
					 for ($i=1; $i <=$datosModel['contadorFilas'] ; $i++)
					  { //echo "<script>alert('entra for de suministro producto ' );</script>";
					  	////////////////////////// Creacion de suministro y producto
					  		//insertar suministro en la tabla suministro
							//$consulta3 = "INSERT INTO suministros(codigo, tipo_suministro, nombre, cantidad, precio, cantidad_minima, referencia, genero, talla, marca, color, diseño, activo) VALUES (".$detalle[$i]["codigo"].", 1, '".$detalle[$i]["nombre"]."', ".$detalle[$i]["cantidad"].", ".$detalle[$i]["precioC"].",  1, '".$detalle[$i]["referencia"]."', '".$detalle[$i]["genero"]."', '".$detalle[$i]["talla"]."', '".$detalle[$i]["marca"]."', '".$detalle[$i]["color"]."', '".$detalle[$i]["diseno"]."', 's')";
							$consulta3 = "UPDATE suministros SET codigo = '".$detalle[$i]["codigo"]."', nombre = '".$detalle[$i]["nombre"]."', cantidad=cantidad + ".$detalle[$i]["cantidad"].", precio =".$detalle[$i]["precioC"]." WHERE id = ".$detalle[$i]["id"];
							//echo "<script>alert('insertar tabla venta o factura ".$consulta." ' );</script>";
							//echo "<br> --".$consulta3."--<br> ";
							$stmt->exec($consulta3);
							$ultimoSuministro = $stmt->lastInsertId();	//ultimo id

							//$consulta4 = "INSERT INTO productos (codigo, nombre, precio) VALUES (".$detalle[$i]["codigo"].",'".$detalle[$i]["nombre"]."',".$detalle[$i]["precioV"].")";
							$consulta4 = "UPDATE  productos SET  precio =".$detalle[$i]["precioV"]." WHERE codigo = ".$detalle[$i]["codigo"];
							//echo "<br> --".$consulta4."--<br> ";
							$stmt->exec($consulta4);
							$ultimoProducto = $stmt->lastInsertId();	//ultimo id
							/*$consulta5 = "INSERT INTO sucursal(suministro_id, cantidad)VALUES ( ".$ultimoSuministro.",  0)";
							//echo "<br> --".$consulta5."--<br> ";
							$stmt->exec($consulta5);

							$consulta6 = "INSERT INTO suministros_productos(producto_id, suministro_id, cantidades)VALUES (".$ultimoProducto.", ".$ultimoSuministro.", 1)";
							//echo "<br> --".$consulta6."--<br> ";
							$stmt->exec($consulta6);*/
					  	/// cerrar de suministro y producto
					 	 $consulta7="INSERT INTO detalle_factura_suministro (suministro_id, compra_suministro_id, cantidad, precio) VALUES (".$detalle[$i]["id"].", ".$ultimo_id.",". $detalle[$i]['cantidad'].", ".$detalle[$i]['precioC'].")";
					 	//echo "<br> --".$consulta7."--<br> ";
						$stmt->exec($consulta7);
					  }
					$stmt->commit();
					//echo "<script>alert('fin de cruD try que pasa');</script>";
							//echo "<script>alert('entro al if CRUD ')</script>";
							$consultaF="SELECT MAX(id) as max_mark FROM `compras_suministros`";
							$stmt->exec($consultaF);
							$mayor=Datos::MaximoModel("compras_suministros");
							$respuesta = array('idcompra' =>$mayor['mayor'] ,
												'success' => "success" );
							//echo "<script>alert('Entro CRUD ".$respuesta['idcompra']." no')</script>";
					return $respuesta;
					//return "success";
					$stmt->close();
				 }
				catch (PDOException $e)
					{	echo "<script>alert('catch entro')</script>";
						  $stmt->rollBack();
						print "Error!: ".$e->getMessage()."</br>";
						return "Error!: ".$e->getMessage()."</br>";
					}

		}
	#-------------------------------------------
	#VISTA COMPRA SUMINISTRO
	#-------------------------------------
	 public static function vistaCsuministroModel($tabla)
		{
			$consulta="SELECT cp.id, cp.proveedor_id, p.nombre as proveedor, cp.valor_compra, cp.fecha_hora, cp.numero_factura_compra, cp.fecha_modificacion FROM $tabla cp,proveedores p where p.id=cp.proveedor_id  order by cp.id DESC";
			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->execute();
			return $stmt->fetchAll();
			$stmt->close();
		}
	#-------------------------------------------
	#EDITAR COMPRA SUMINISTRO
	#-------------------------------------
	 public static function editarCsuministroModel($datosModel, $tabla)
		{
			$stmt = Conexion::conectar()->prepare("SELECT id, proveedor_id, valor_compra, fecha_hora, numero_factura_compra FROM $tabla WHERE id = :id");
			$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);
			$stmt->execute();
			return $stmt->fetch();
			$stmt->close();
		}
	#-------------------------------------
	#ACTUALIZAR COMPRA SUMINISTRO
	#-------------------------------------
	 public static function actualizarCsuministroModel($datosModel, $tabla)
		{ //echo "<script>alert('Entro Actualizar Producto')</script>";
			$stmt = Conexion::conectar()->prepare("UPDATE $tabla SET proveedor_id = :proveedor_id, valor_compra =:valor_compra, fecha_hora =:fecha_hora, numero_factura_compra =:numero_factura_compra WHERE id = :id");
			$stmt->bindParam(":proveedor_id", $datosModel["proveedor_id"], PDO::PARAM_STR);
			$stmt->bindParam(":valor_compra", $datosModel["valor_compra"], PDO::PARAM_INT);
			$stmt->bindParam(":fecha_hora", $datosModel["fecha_hora"], PDO::PARAM_INT);
			$stmt->bindParam(":numero_factura_compra", $datosModel["numero_factura_compra"], PDO::PARAM_INT);
			$stmt->bindParam(":id", $datosModel["id"], PDO::PARAM_INT);
			if($stmt->execute())
				{echo "<script>alert('Guardo Actualizar Producto')</script>";return "success";	}
			else{	return "error";			}
			$stmt->close();
		}
	#-------------------------------------
	#BORRAR COMPRA SUMINISTRO
	#------------------------------------
	 public static function borrarCsuministroModel($datosModel, $tabla)
		{
			$stmt = Conexion::conectar()->prepare("DELETE FROM $tabla WHERE id = :id");
			$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);
			if($stmt->execute())
				{	return "success";	}
			else{	return "error";		}
			$stmt->close();
		}
	#--------------------------------------------------
	#LISTA Proveedores
	#---------------------------------------
	 public static function listaProveedoresModel($tabla)
		{
			$stmt = Conexion::conectar()->prepare("SELECT id,nombre from $tabla  ");
			if($stmt->execute())
				{	return $stmt->fetchAll();	}
			else {	return "error";	}
			$stmt->close();
		}
	#---------------------------------------
}