<?php

session_start();

if(!isset($_SESSION["validar"]) || !$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "models/conexion.php";
require_once "models/crud.php";

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Test: Eliminar Entregas por Categoría</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
</head>
<body>

<div class="container">
    <h2>🧪 Test: Eliminar Entregas por Categoría</h2>
    <hr>
    
    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">📋 Nueva Función: eliminarEntregasCategoriaModel()</h3>
        </div>
        <div class="panel-body">
            <p><strong>Funcionalidad:</strong></p>
            <ul>
                <li>✅ Busca todos los pedidos de una mesa</li>
                <li>✅ Para cada pedido, ejecuta: <code>DELETE FROM pedidos_entregas_categoria WHERE pedido_id = $pedido_id</code></li>
                <li>✅ Registra logs detallados del proceso</li>
                <li>✅ Retorna "success" o "error"</li>
            </ul>
        </div>
    </div>
    
    <div class="panel panel-primary">
        <div class="panel-heading">
            <h3 class="panel-title">🔍 Estado Actual: Mesas con Entregas por Categoría</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                $stmt = Conexion::conectar()->prepare("
                    SELECT 
                        m.id as mesa_id,
                        m.nombre as mesa_nombre,
                        COUNT(DISTINCT p.id) as total_pedidos,
                        COUNT(pec.id) as total_entregas_categoria
                    FROM mesas m
                    LEFT JOIN pedidos p ON m.id = p.mesa_id
                    LEFT JOIN pedidos_entregas_categoria pec ON p.id = pec.pedido_id
                    WHERE m.id <= 10
                    GROUP BY m.id, m.nombre
                    HAVING total_entregas_categoria > 0
                    ORDER BY m.id
                ");
                $stmt->execute();
                $mesas = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (count($mesas) > 0) {
                    echo "<table class='table table-striped'>";
                    echo "<thead>";
                    echo "<tr>";
                    echo "<th>Mesa</th>";
                    echo "<th>Total Pedidos</th>";
                    echo "<th>Entregas por Categoría</th>";
                    echo "<th>Acciones</th>";
                    echo "</tr>";
                    echo "</thead>";
                    echo "<tbody>";
                    
                    foreach ($mesas as $mesa) {
                        echo "<tr>";
                        echo "<td><strong>{$mesa['mesa_nombre']}</strong></td>";
                        echo "<td>{$mesa['total_pedidos']}</td>";
                        echo "<td><span class='label label-info'>{$mesa['total_entregas_categoria']}</span></td>";
                        echo "<td>";
                        echo "<button onclick='testEliminarEntregas({$mesa['mesa_id']})' class='btn btn-sm btn-danger'>🧪 Test Eliminar</button>";
                        echo "</td>";
                        echo "</tr>";
                    }
                    
                    echo "</tbody>";
                    echo "</table>";
                } else {
                    echo "<div class='alert alert-info'>No hay mesas con entregas por categoría</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-warning">
        <div class="panel-heading">
            <h3 class="panel-title">🧪 Test Directo de la Función</h3>
        </div>
        <div class="panel-body">
            <form method="POST">
                <div class="form-group">
                    <label for="mesa_test">ID de Mesa para Test:</label>
                    <input type="number" name="mesa_test" id="mesa_test" class="form-control" min="1" max="50" required>
                    <small class="help-block">Ingrese el ID de la mesa para probar la eliminación de entregas por categoría</small>
                </div>
                <button type="submit" name="test_eliminar" class="btn btn-warning">
                    🧪 Test eliminarEntregasCategoriaModel()
                </button>
            </form>
            
            <?php
            if (isset($_POST['test_eliminar']) && !empty($_POST['mesa_test'])) {
                $mesa_id = (int)$_POST['mesa_test'];
                
                echo "<div class='alert alert-info'>";
                echo "<h4>🔄 Ejecutando test para Mesa ID: {$mesa_id}</h4>";
                echo "</div>";
                
                // Mostrar estado antes
                echo "<h5>📊 Estado ANTES:</h5>";
                try {
                    $stmt_antes = Conexion::conectar()->prepare("
                        SELECT 
                            p.id as pedido_id,
                            p.numero_pedido,
                            COUNT(pec.id) as entregas_categoria
                        FROM pedidos p
                        LEFT JOIN pedidos_entregas_categoria pec ON p.id = pec.pedido_id
                        WHERE p.mesa_id = ?
                        GROUP BY p.id, p.numero_pedido
                        ORDER BY p.id
                    ");
                    $stmt_antes->execute([$mesa_id]);
                    $pedidos_antes = $stmt_antes->fetchAll(PDO::FETCH_ASSOC);
                    
                    if (count($pedidos_antes) > 0) {
                        echo "<table class='table table-condensed'>";
                        echo "<thead><tr><th>Pedido ID</th><th>Número</th><th>Entregas Categoría</th></tr></thead>";
                        echo "<tbody>";
                        foreach ($pedidos_antes as $p) {
                            echo "<tr><td>{$p['pedido_id']}</td><td>{$p['numero_pedido']}</td><td>{$p['entregas_categoria']}</td></tr>";
                        }
                        echo "</tbody></table>";
                    } else {
                        echo "<p>No hay pedidos para esta mesa.</p>";
                    }
                    
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>Error al consultar estado antes: " . $e->getMessage() . "</div>";
                }
                
                // Ejecutar la función
                echo "<h5>🔄 Ejecutando eliminarEntregasCategoriaModel():</h5>";
                try {
                    $resultado = Datos::eliminarEntregasCategoriaModel($mesa_id);
                    
                    if ($resultado == "success") {
                        echo "<div class='alert alert-success'>";
                        echo "<h4>✅ Test exitoso</h4>";
                        echo "<p>La función eliminarEntregasCategoriaModel() se ejecutó correctamente.</p>";
                        echo "</div>";
                    } else {
                        echo "<div class='alert alert-danger'>";
                        echo "<h4>❌ Test falló</h4>";
                        echo "<p>La función retornó: {$resultado}</p>";
                        echo "</div>";
                    }
                    
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>";
                    echo "<h4>❌ Excepción en test</h4>";
                    echo "<p>Error: " . $e->getMessage() . "</p>";
                    echo "</div>";
                }
                
                // Mostrar estado después
                echo "<h5>📊 Estado DESPUÉS:</h5>";
                try {
                    $stmt_despues = Conexion::conectar()->prepare("
                        SELECT 
                            p.id as pedido_id,
                            p.numero_pedido,
                            COUNT(pec.id) as entregas_categoria
                        FROM pedidos p
                        LEFT JOIN pedidos_entregas_categoria pec ON p.id = pec.pedido_id
                        WHERE p.mesa_id = ?
                        GROUP BY p.id, p.numero_pedido
                        ORDER BY p.id
                    ");
                    $stmt_despues->execute([$mesa_id]);
                    $pedidos_despues = $stmt_despues->fetchAll(PDO::FETCH_ASSOC);
                    
                    if (count($pedidos_despues) > 0) {
                        echo "<table class='table table-condensed'>";
                        echo "<thead><tr><th>Pedido ID</th><th>Número</th><th>Entregas Categoría</th></tr></thead>";
                        echo "<tbody>";
                        foreach ($pedidos_despues as $p) {
                            echo "<tr><td>{$p['pedido_id']}</td><td>{$p['numero_pedido']}</td><td>{$p['entregas_categoria']}</td></tr>";
                        }
                        echo "</tbody></table>";
                    } else {
                        echo "<p>No hay pedidos para esta mesa.</p>";
                    }
                    
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>Error al consultar estado después: " . $e->getMessage() . "</div>";
                }
            }
            ?>
        </div>
    </div>
    
    <hr>
    <div class="row">
        <div class="col-md-4">
            <a href="index.php?action=test_cancelar_mesa" class="btn btn-primary btn-block">🔙 Test Cancelar Mesa</a>
        </div>
        <div class="col-md-4">
            <a href="index.php?action=mesa" class="btn btn-info btn-block">🏠 Volver a Mesas</a>
        </div>
        <div class="col-md-4">
            <a href="index.php?action=debug_estado_pedidos" class="btn btn-warning btn-block">📊 Debug Pedidos</a>
        </div>
    </div>
</div>

<script>
function testEliminarEntregas(mesaId) {
    if (confirm('🧪 TEST: ¿Eliminar entregas por categoría de la Mesa ' + mesaId + '?\n\n' +
               '⚠️ Esto eliminará todos los registros de pedidos_entregas_categoria\n' +
               '✅ Para todos los pedidos de esta mesa')) {
        
        // Crear formulario dinámico para enviar el test
        var form = document.createElement('form');
        form.method = 'POST';
        form.style.display = 'none';
        
        var input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'mesa_test';
        input.value = mesaId;
        
        var submit = document.createElement('input');
        submit.type = 'hidden';
        submit.name = 'test_eliminar';
        submit.value = '1';
        
        form.appendChild(input);
        form.appendChild(submit);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

</body>
</html>
