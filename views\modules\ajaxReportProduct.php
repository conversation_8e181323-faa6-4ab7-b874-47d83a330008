<?php
//echo "<script>alert('Entro al ajax Ventas ');</script>";
require_once "../../models/crud.php";
require_once "../../models/crudVistaVentas.php";
require_once "../../controllers/controller.php";
require_once "../../controllers/controllerVistaVentas.php";
session_start();
//echo "<script>alert('Hasta aqui todo bien ".$_POST['fecha1']." codigo=".$_POST['code']." ');</script>";
if(isset($_POST["fecha1"]) and $_POST['fecha1']!='' and isset($_POST["fecha2"]) and $_POST['fecha2']!='')  // !(empty($var))
	{	//echo "<script>alert('Si entro al ajax Ventas fecah 1 ".$_POST["fecha1"]." ');</script>";
		$fecha1 = $_POST["fecha1"];
		$fecha2 = $_POST["fecha2"];
		$code = $_POST["code"];
		//echo $queryString;
		$ajax=new controllerVistaVentas();
		$compras=$ajax->vistaReporCompraProductController($fecha1,$fecha2,$code);
		$ventas=$ajax->vistaReporVentaProductController($fecha1,$fecha2,$code);
	    $datos=$ajax->DatosProductoController($code);
		//echo "<script>alert('entro ajax ultimo pro')</script>";
		//echo "<div>Dios es Amor</div>";
		//
		echo '<table border="1">
				<thead>
					<tr>
						<td><b>Producto</b></td>
						<td><b>Laboratorio</b></td>
						<td><b>Codigo</b></td>
						<td><b>Precio</b></td>
						<td><b>Costo</b></td>
						<td><b>Cantidad Actual</b></td>
					</tr></thead>';

		echo'<tr>
					<td>'.$datos["nombre"].'</td>
					<td>'.$datos["laboratorio"].'</td>
					<td>'.$datos["codigo"].'</td>
					<td>'.$datos["precio"].'</td>
					<td>'.$datos["costo"].'</td>
					<td>'.$datos["cantidad"].'</td>
				</tr>
			</table> <br><br>';				
		
		if ($_SESSION["punto_id"]==1 or $_SESSION["usuario"]==1  or $_SESSION["usuario"]==15)
			{
				echo'
				<table border="1">
					<thead>
						<tr>
							<td><b>Cantidad Comprada</b></td>
							<td><b>Fecha</b></td>
							<td><b>Medicamento</b></td>
							<td><b>Laboratorio</b></td>' ;
			}
				echo'
						</tr></thead>';
		              setlocale(LC_MONETARY, 'en_US');
				 	foreach($compras as $row => $item)
					{
					 
							echo'<tr>
									<td>'.$item["cantidad"].'</td>
									<td align="center">'.$item["fecha"].'</td>
									<td align="right">'.$item["nombre"].'</td>
									<td align="right">'.$item["laboratorio"].'</td>	
								</tr>';
					}

					echo '</table>';

					echo' <br> <br>
				<table border="1">
					<thead>
						<tr>
							<td><b>Cantidad Vendida</b></td>
							<td><b>Fecha</b></td>
							<td><b>Medicamento</b></td>
							<td><b>Laboratorio</b></td>
						</tr></thead>';
		              $descuentoTotal=0;
		              $No=0;
		              $total=0;
				 	foreach($ventas as $rows => $ite)
					{
					 
						 $descuentoTotal+=$ite["descuento"];
						// $descuentoF+=$item["vdescuento"];
						 $total+=$ite["cantidad"];
						echo'<tr>
								<td>'.$ite["cantidad"].'</td>
								<td>'.$ite["fecha"].'</td>
								<td>'.$ite["nombre"].'</td>
								<td>'.$ite["laboratorio"].'</td>
							</tr>
							';
					}
					echo '<tr class="filaTotal">
							<td colspan="1" align=""><h3><b>'.$total.'</b></h3></td>
							<td colspan="3" ><b>Total</b></td>						
						 </tr>
					</table><br><br>';


			//--Start ---------------Punto que recibe el aumento de inventario ------------------------------------------------
			echo' <br> 
					<table border="1">
						<CAPTION class="filaTotal1">AUMENTO DE INVENTARIO</CAPTION>
						<thead>
							<tr>
								<td><b>No.</b></td>
								<td><b>RESPONSABLE</b></td>
								<td><b>OBSERVACIÓN</b></td>
								<td><b>Fecha</b></td>
								<td><b>Producto</b></td>
								<td><b>Cantidad</b></td>
							</tr></thead>';
			$aumento=$ajax->vistaReporAumentoInventarioProductController($fecha1,$fecha2,$code,1);

				$totalAumentoInventario=0;
			if ($aumento["result"]=='success') {
				$totalAumentoInventario=0;
				foreach($aumento["info"] as $row => $item)
					{ //$recibe=DatosComprasuministro::datosPuntoSucursalModel($item["recibe"]);
					  $totalAumentoInventario+=$item["cantidad"];
					 echo'
					 	<tr>
							<td>'.$item["id"].'</td>
							<td>'.$item["persona"].'</td>
							<td>'.$item["descripcion"].'</td>
							<td>'.$item["fecha"].'</td>
							<td>'.$item["producto"].'</td>
							<td>'.$item["cantidad"].'</td>
						</tr>';	
					}
					echo'<tr class="filaTotal">
									<td colspan="5">&nbsp;Total Aumento de Inventario</td>
									<td>'.$totalAumentoInventario.'</td>
																		
								</tr>
							</table> <hr>';
			}else if ($aumento["result"]=='error') {
				//echo $trasladoRecibe["datos"];
				echo'<tr class="filaTotal">
									<td colspan="5">&nbsp;Total Aumento de Inventario</td>
									<td>'.$totalAumentoInventario.'</td>
																		
								</tr>
							</table> <hr>';
			}
			//--End ---------------Punto que recibe el aumento de inventario ---------------------------------------------
					
			              
	}