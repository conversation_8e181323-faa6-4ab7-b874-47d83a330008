<?php
session_start();

echo "<!DOCTYPE html>";
echo "<html><head><title>Debug Productos AJAX</title></head><body>";
echo "<h2>🔧 Debug de Productos AJAX</h2>";

// Test 1: Verificar conexión
echo "<h3>📊 Test 1: Conexión a Base de Datos</h3>";
try {
    require_once "../../models/conexion.php";
    $db = Conexion::conectar();
    echo "<p style='color: green;'>✅ Conexión exitosa</p>";
    
    // Test 2: Verificar productos en la base de datos
    echo "<h3>📦 Test 2: Productos en Base de Datos</h3>";
    $sql = "SELECT COUNT(*) as total FROM productos";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<p><strong>Total productos:</strong> " . $result['total'] . "</p>";
    
    if ($result['total'] > 0) {
        // Mostrar algunos productos
        $sql = "SELECT id, nombre, precio FROM productos LIMIT 5";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $productos = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h4>Productos de ejemplo:</h4>";
        echo "<ul>";
        foreach ($productos as $producto) {
            echo "<li>ID: " . $producto['id'] . " - " . $producto['nombre'] . " - $" . number_format($producto['precio']) . "</li>";
        }
        echo "</ul>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error de conexión: " . $e->getMessage() . "</p>";
}

// Test 3: Simular búsqueda AJAX
echo "<h3>🔍 Test 3: Simulación de Búsqueda</h3>";
try {
    $termino = "a"; // Buscar productos que contengan 'a'
    
    $sql = "SELECT id, nombre, precio FROM productos 
            WHERE nombre LIKE :termino OR codigo LIKE :termino 
            ORDER BY nombre LIMIT 10";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([':termino' => "%$termino%"]);
    $productos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p><strong>Búsqueda de '$termino':</strong> " . count($productos) . " resultados</p>";
    
    if (count($productos) > 0) {
        echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
        foreach ($productos as $producto) {
            echo '<div style="padding: 5px; border-bottom: 1px solid #eee;">';
            echo '<strong>' . htmlspecialchars($producto['nombre']) . '</strong><br>';
            echo '<small>$' . number_format($producto['precio']) . '</small>';
            echo '</div>';
        }
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error en búsqueda: " . $e->getMessage() . "</p>";
}

// Test 4: Verificar ruta desde MVC
echo "<h3>🛣️ Test 4: Rutas desde MVC</h3>";
echo "<p><strong>Directorio actual:</strong> " . __DIR__ . "</p>";
echo "<p><strong>Archivo actual:</strong> " . __FILE__ . "</p>";

$rutas_probar = [
    "../../models/conexion.php" => "Ruta desde views/modules/",
    "models/conexion.php" => "Ruta desde raíz (MVC)"
];

foreach ($rutas_probar as $ruta => $descripcion) {
    if (file_exists($ruta)) {
        echo "<p style='color: green;'>✅ $descripcion: $ruta</p>";
    } else {
        echo "<p style='color: red;'>❌ $descripcion: $ruta</p>";
    }
}

?>

<h3>🧪 Test AJAX en Vivo</h3>
<p>Prueba la búsqueda AJAX aquí:</p>

<div style="margin: 20px 0;">
    <input type="text" id="test_busqueda" placeholder="Escribe para buscar productos..." style="padding: 10px; width: 300px;">
    <button onclick="testBusqueda()" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 3px;">Buscar</button>
</div>

<div id="resultados_test" style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; min-height: 50px;">
    Los resultados aparecerán aquí...
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
function testBusqueda() {
    const termino = document.getElementById('test_busqueda').value;
    
    if (termino.length < 1) {
        document.getElementById('resultados_test').innerHTML = 'Escribe al menos 1 carácter';
        return;
    }
    
    document.getElementById('resultados_test').innerHTML = '⏳ Buscando...';
    
    // Test con ruta desde views/modules/
    $.ajax({
        url: 'ajaxBuscarProductoRapido.php',
        type: 'POST',
        data: { termino: termino },
        success: function(response) {
            document.getElementById('resultados_test').innerHTML = 
                '<h4>✅ Respuesta AJAX:</h4><div style="border: 1px solid #ccc; padding: 10px; background: white;">' + response + '</div>';
        },
        error: function(xhr, status, error) {
            document.getElementById('resultados_test').innerHTML = 
                '<h4>❌ Error AJAX:</h4><p>Status: ' + status + '</p><p>Error: ' + error + '</p><p>Response: ' + xhr.responseText + '</p>';
        }
    });
}

// Test automático al cargar
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('test_busqueda').value = 'a';
    setTimeout(testBusqueda, 1000);
});
</script>

<br><br>
<a href="../../index.php?action=facturacionRapida" style="background: #28a745; color: white; padding: 10px; text-decoration: none; border-radius: 5px;">🚀 Ir a Facturación Rápida</a>
<a href="../../index.php?action=mesa" style="background: #6c757d; color: white; padding: 10px; text-decoration: none; border-radius: 5px;">🔙 Volver a Mesas</a>

</body>
</html>
