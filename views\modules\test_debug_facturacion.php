<?php
// Test de debug para facturación
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
if (!isset($_SESSION["usuario"])) {
    $_SESSION["usuario"] = 1;
    $_SESSION["tipo_usuario"] = 1;
    $_SESSION["perfil"] = "administrador";
}

require_once "../../models/conexion.php";
require_once "../../models/crudFacturaAja.php";

$mesaId = isset($_GET['mesa']) ? $_GET['mesa'] : 1;

echo "<h1>🔍 Debug Facturación - Mesa $mesaId</h1>";

try {
    echo "<h3>1. 📋 Productos Disponibles para Facturar</h3>";
    
    // Usar la nueva función
    $productosFacturar = DatosFacturaAja::obtenerProductosParaFacturar($mesaId);
    
    if (!empty($productosFacturar)) {
        echo "<div class='alert alert-success'>✅ Encontrados " . count($productosFacturar) . " producto(s) para facturar</div>";
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr><th>ID</th><th>Producto</th><th>Precio</th><th>Cantidad</th><th>Estado Pedido</th><th>Número Pedido</th></tr>";
        
        $totalFacturar = 0;
        foreach ($productosFacturar as $producto) {
            $subtotal = $producto['preciopr'] * $producto['cantidadpvm'];
            $totalFacturar += $subtotal;
            
            echo "<tr>";
            echo "<td>{$producto['idpr']}</td>";
            echo "<td>{$producto['nombrepr']}</td>";
            echo "<td>$" . number_format($producto['preciopr']) . "</td>";
            echo "<td>{$producto['cantidadpvm']}</td>";
            echo "<td>{$producto['estado']}</td>";
            echo "<td>{$producto['numero_pedido']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<div class='alert alert-info'>";
        echo "<h4>💰 Total a Facturar: $" . number_format($totalFacturar) . "</h4>";
        echo "</div>";
        
        echo "<h3>2. 🧪 Test de Facturación</h3>";
        echo "<button onclick='testFacturarDebug()' style='background: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;'>";
        echo "💰 Facturar con 5 Métodos (Debug)";
        echo "</button>";
        
    } else {
        echo "<div class='alert alert-warning'>⚠️ No hay productos disponibles para facturar</div>";
        echo "<p>Los productos deben estar en pedidos con estado 'enviado' o 'entregado'.</p>";
        
        // Mostrar todos los pedidos para debug
        echo "<h4>Debug: Todos los pedidos de la mesa</h4>";
        $db = Conexion::conectar();
        $sql = "SELECT * FROM pedidos WHERE mesa_id = ? ORDER BY fecha_pedido DESC";
        $stmt = $db->prepare($sql);
        $stmt->bindParam(1, $mesaId, PDO::PARAM_INT);
        $stmt->execute();
        $pedidos = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($pedidos)) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>ID</th><th>Número</th><th>Estado</th><th>Fecha</th></tr>";
            foreach ($pedidos as $p) {
                echo "<tr>";
                echo "<td>{$p['id']}</td>";
                echo "<td>{$p['numero_pedido']}</td>";
                echo "<td>{$p['estado']}</td>";
                echo "<td>{$p['fecha_pedido']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>No hay pedidos en la mesa.</p>";
        }
    }
    
    echo "<h3>3. 📊 Verificación de Datos</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h4>Datos que se enviarán:</h4>";
    echo "<ul>";
    echo "<li><strong>Mesa:</strong> $mesaId</li>";
    echo "<li><strong>Usuario:</strong> " . ($_SESSION['usuario'] ?? 'No definido') . "</li>";
    echo "<li><strong>Efectivo:</strong> $20,000</li>";
    echo "<li><strong>Tarjeta:</strong> $20,000</li>";
    echo "<li><strong>Nequi:</strong> $20,000</li>";
    echo "<li><strong>Daviplata:</strong> $20,000</li>";
    echo "<li><strong>Bancolombia:</strong> $20,000</li>";
    echo "<li><strong>Total Pagado:</strong> $100,000</li>";
    echo "<li><strong>Propina:</strong> $5,000</li>";
    echo "<li><strong>Total a Pagar:</strong> $105,000</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
    echo "Stack trace: " . $e->getTraceAsString() . "<br>";
}

echo "<br><a href='index.php?action=registroPmesa&ida=$mesaId' style='background: #007bff; color: white; padding: 10px; text-decoration: none;'>🔙 Volver a Mesa $mesaId</a>";
echo "<br><a href='test_debug_facturacion.php?mesa=$mesaId' style='background: #6c757d; color: white; padding: 10px; text-decoration: none; margin: 5px;'>🔄 Actualizar</a>";
?>

<script>
function testFacturarDebug() {
    console.log('🔍 Iniciando test de facturación debug');
    
    if (confirm('¿Proceder con facturación de debug?\n\nEsto facturará todos los productos enviados/entregados de la mesa.')) {
        
        // Datos de facturación
        const datosFactura = {
            efectivo: 20000,
            tarjeta: 20000,
            nequi: 20000,
            daviplata: 20000,
            bancolombia: 20000,
            pago: 1,
            pcedula: '12345678',
            totalDescuento: 0,
            total: 100000,
            propina: 5000,
            mesa: <?=$mesaId?>,
            tipoTarjeta: 'credito'
        };
        
        console.log('📋 Datos enviados:', datosFactura);
        
        // Mostrar loading
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '⏳ Procesando facturación...';
        button.disabled = true;
        
        // Enviar facturación
        fetch('ajaxFactura.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams(datosFactura)
        })
        .then(response => response.text())
        .then(text => {
            console.log('📄 Respuesta completa:', text);
            
            // Restaurar botón
            button.innerHTML = originalText;
            button.disabled = false;
            
            // Mostrar resultado detallado
            if (text.includes('success') || text.trim() === 'success') {
                alert('✅ ÉXITO: Facturación completada correctamente\n\nRespuesta: ' + text);
                setTimeout(() => location.reload(), 2000);
            } else if (text.includes('alert')) {
                // Extraer mensaje del alert
                const alertMatch = text.match(/alert\('([^']+)'\)/);
                const mensaje = alertMatch ? alertMatch[1] : text;
                alert('ℹ️ Mensaje del sistema:\n\n' + mensaje);
            } else if (text.includes('error') || text.includes('Error')) {
                alert('❌ ERROR:\n\n' + text);
            } else {
                alert('📄 Respuesta del servidor:\n\n' + text);
            }
        })
        .catch(error => {
            console.error('❌ Error:', error);
            
            // Restaurar botón
            button.innerHTML = originalText;
            button.disabled = false;
            
            alert('❌ Error de conexión: ' + error.message);
        });
    }
}
</script>

<style>
.alert {
    padding: 15px;
    margin: 15px 0;
    border-radius: 5px;
    border: 1px solid transparent;
}
.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}
.alert-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}
.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}
</style>
