<?php
// Test del Sistema de Impresión Híbrida
echo "<h1>🧪 Test del Sistema de Impresión Híbrida</h1>";
echo "<p><strong>Prueba la impresión desde portátil y celulares con detección automática</strong></p>";

// Obtener configuración
try {
    require_once '../../models/conexion.php';
    $conexion = new Conexion();
    $pdo = $conexion->conectar();
    
    // Obtener configuración del sistema
    $stmt = $pdo->query("SELECT clave, valor FROM config_impresion");
    $config_rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $config = [];
    foreach ($config_rows as $row) {
        $config[$row['clave']] = $row['valor'];
    }
    
    // Obtener impresoras
    $stmt = $pdo->query("SELECT * FROM impresoras WHERE activa = 1 ORDER BY nombre");
    $impresoras = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    echo "<p>⚠️ Error: " . $e->getMessage() . "</p>";
    exit;
}

// Detectar información del cliente
$ip_cliente = $_SERVER['REMOTE_ADDR'] ?? 'Desconocida';
$user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
$es_movil = preg_match('/Mobile|Android|iPhone|iPad/', $user_agent);
$es_red_local = strpos($ip_cliente, $config['red_local'] ?? '192.168.68') === 0;

echo "<div style='background-color: #e7f3ff; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0;'>";
echo "<h3>📱 Información del Dispositivo:</h3>";
echo "<ul>";
echo "<li><strong>IP Cliente:</strong> $ip_cliente</li>";
echo "<li><strong>Dispositivo:</strong> " . ($es_movil ? '📱 Móvil' : '🖥️ Computadora') . "</li>";
echo "<li><strong>Red:</strong> " . ($es_red_local ? '🏠 Local' : '🌐 Externa') . "</li>";
echo "<li><strong>User Agent:</strong> " . substr($user_agent, 0, 100) . "...</li>";
echo "</ul>";
echo "</div>";

// Mostrar configuración actual
echo "<h2>⚙️ Configuración Actual:</h2>";
echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<div style='display: grid; grid-template-columns: 1fr 1fr; gap: 20px;'>";

echo "<div>";
echo "<h4>🌐 Configuración de Red:</h4>";
echo "<ul>";
echo "<li><strong>Modo:</strong> {$config['modo_impresion']}</li>";
echo "<li><strong>IP Proxy:</strong> {$config['ip_proxy']}:{$config['puerto_proxy']}</li>";
echo "<li><strong>Red Local:</strong> {$config['red_local']}.x</li>";
echo "<li><strong>Timeout:</strong> {$config['timeout_conexion']}s</li>";
echo "</ul>";
echo "</div>";

echo "<div>";
echo "<h4>🖨️ Impresoras Activas:</h4>";
echo "<ul>";
foreach ($impresoras as $imp) {
    echo "<li><strong>{$imp['nombre']}:</strong> {$imp['ip']}:{$imp['puerto']}</li>";
}
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";

// Función JavaScript para testing
echo "<script>
class TestImpresionHibrida {
    constructor() {
        this.config = " . json_encode($config) . ";
        this.impresoras = " . json_encode($impresoras) . ";
        this.esRedLocal = " . ($es_red_local ? 'true' : 'false') . ";
        this.resultados = [];
    }
    
    async testConexionDirecta(impresora) {
        const url = `http://\${impresora.ip}:\${impresora.puerto}`;
        const datos = `=== TEST DIRECTO ===\\nImpresora: \${impresora.nombre}\\nFecha: \${new Date().toLocaleString()}\\n===================\\n\\n\\n`;
        
        try {
            const response = await fetch(url, {
                method: 'POST',
                body: datos,
                mode: 'no-cors',
                signal: AbortSignal.timeout(this.config.timeout_conexion * 1000)
            });
            
            return {
                success: true,
                metodo: 'directo',
                url: url,
                mensaje: 'Conexión directa exitosa'
            };
        } catch (error) {
            return {
                success: false,
                metodo: 'directo',
                url: url,
                error: error.message
            };
        }
    }
    
    async testConexionProxy(impresora) {
        const nombreImpresora = impresora.nombre.toLowerCase();
        const url = `http://\${this.config.ip_proxy}:\${this.config.puerto_proxy}/\${nombreImpresora}`;
        const datos = `=== TEST PROXY ===\\nImpresora: \${impresora.nombre}\\nFecha: \${new Date().toLocaleString()}\\n==================\\n\\n\\n`;
        
        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'text/plain'
                },
                body: datos,
                signal: AbortSignal.timeout(this.config.timeout_conexion * 1000)
            });
            
            const resultado = await response.json();
            
            return {
                success: resultado.success || response.ok,
                metodo: 'proxy',
                url: url,
                mensaje: resultado.respuesta || 'Conexión proxy exitosa',
                detalles: resultado
            };
        } catch (error) {
            return {
                success: false,
                metodo: 'proxy',
                url: url,
                error: error.message
            };
        }
    }
    
    async testImpresora(impresora) {
        const resultadoDiv = document.getElementById(`resultado-\${impresora.nombre.toLowerCase()}`);
        resultadoDiv.innerHTML = '🔄 Probando...';
        
        let resultados = [];
        
        // Decidir qué métodos probar según la configuración
        if (this.config.modo_impresion === 'directo' || 
            (this.config.modo_impresion === 'hibrido' && this.esRedLocal)) {
            
            resultadoDiv.innerHTML += '<br>📡 Probando conexión directa...';
            const resultadoDirecto = await this.testConexionDirecta(impresora);
            resultados.push(resultadoDirecto);
        }
        
        if (this.config.modo_impresion === 'proxy' || 
            (this.config.modo_impresion === 'hibrido' && (!this.esRedLocal || !resultados[0]?.success))) {
            
            resultadoDiv.innerHTML += '<br>🌐 Probando conexión proxy...';
            const resultadoProxy = await this.testConexionProxy(impresora);
            resultados.push(resultadoProxy);
        }
        
        // Mostrar resultados
        this.mostrarResultados(impresora, resultados, resultadoDiv);
        
        return resultados;
    }
    
    mostrarResultados(impresora, resultados, div) {
        let html = `<h5>📊 Resultados para \${impresora.nombre}:</h5>`;
        
        resultados.forEach((resultado, index) => {
            const icono = resultado.success ? '✅' : '❌';
            const color = resultado.success ? '#28a745' : '#dc3545';
            
            html += `
                <div style='border: 1px solid \${color}; padding: 10px; margin: 5px 0; border-radius: 5px; background-color: \${resultado.success ? '#d4edda' : '#f8d7da'}'>
                    <strong>\${icono} \${resultado.metodo.toUpperCase()}</strong><br>
                    <small><strong>URL:</strong> \${resultado.url}</small><br>
                    <small><strong>Resultado:</strong> \${resultado.success ? (resultado.mensaje || 'Éxito') : (resultado.error || 'Error')}</small>
                </div>
            `;
        });
        
        // Recomendación
        const exitoso = resultados.find(r => r.success);
        if (exitoso) {
            html += `<div style='background-color: #d1ecf1; padding: 10px; border-radius: 5px; margin-top: 10px;'>
                <strong>💡 Recomendación:</strong> Usar método <strong>\${exitoso.metodo}</strong>
            </div>`;
        } else {
            html += `<div style='background-color: #fff3cd; padding: 10px; border-radius: 5px; margin-top: 10px;'>
                <strong>⚠️ Ningún método funcionó.</strong> Verificar configuración.
            </div>`;
        }
        
        div.innerHTML = html;
    }
    
    async testTodasLasImpresoras() {
        const boton = document.getElementById('btn-test-todas');
        boton.disabled = true;
        boton.innerHTML = '🔄 Probando todas...';
        
        for (const impresora of this.impresoras) {
            await this.testImpresora(impresora);
            await new Promise(resolve => setTimeout(resolve, 1000)); // Pausa entre tests
        }
        
        boton.disabled = false;
        boton.innerHTML = '🧪 Probar Todas las Impresoras';
    }
}

const testHibrido = new TestImpresionHibrida();
</script>";

// Interfaz de testing
echo "<h2>🧪 Panel de Testing:</h2>";

echo "<div style='background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>🎯 Estrategia de Testing Automática:</h4>";

if ($es_red_local) {
    echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 10px 0;'>";
    echo "<h5>🏠 Estás en Red Local</h5>";
    echo "<p><strong>Estrategia:</strong> Probar conexión directa primero, luego proxy como fallback</p>";
    echo "</div>";
} else {
    echo "<div style='background-color: #d1ecf1; padding: 15px; border-left: 4px solid #17a2b8; margin: 10px 0;'>";
    echo "<h5>🌐 Estás en Red Externa</h5>";
    echo "<p><strong>Estrategia:</strong> Usar proxy principalmente, conexión directa como fallback</p>";
    echo "</div>";
}

echo "<button id='btn-test-todas' onclick='testHibrido.testTodasLasImpresoras()' style='background-color: #007bff; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold; margin: 10px 0;'>🧪 Probar Todas las Impresoras</button>";
echo "</div>";

// Tests individuales por impresora
echo "<h3>🖨️ Tests Individuales:</h3>";
echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 15px 0;'>";

foreach ($impresoras as $impresora) {
    $nombre_lower = strtolower($impresora['nombre']);
    
    echo "<div style='border: 1px solid #ccc; padding: 15px; border-radius: 8px; background-color: #fff;'>";
    echo "<h4>🖨️ {$impresora['nombre']}</h4>";
    echo "<p><strong>IP:</strong> {$impresora['ip']}:{$impresora['puerto']}</p>";
    
    echo "<button onclick='testHibrido.testImpresora(" . json_encode($impresora) . ")' style='background-color: #28a745; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer; margin: 5px 0;'>🧪 Probar {$impresora['nombre']}</button>";
    
    echo "<div id='resultado-$nombre_lower' style='margin-top: 10px; min-height: 50px;'>";
    echo "<p style='color: #666; font-style: italic;'>Haz clic en 'Probar' para ejecutar el test</p>";
    echo "</div>";
    
    echo "</div>";
}

echo "</div>";

// Instrucciones para celulares
echo "<h2>📱 Instrucciones para Testing desde Celular:</h2>";
echo "<div style='border: 1px solid #28a745; padding: 20px; margin: 15px 0; border-radius: 8px;'>";
echo "<h4>📲 Pasos para probar desde tu celular:</h4>";

echo "<ol>";
echo "<li><strong>Conectar celular a WiFi</strong> (misma red que las impresoras)</li>";
echo "<li><strong>Abrir navegador</strong> en el celular</li>";
echo "<li><strong>Ir a esta página:</strong></li>";
echo "<div style='background-color: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<code>https://macarena.anconclub.com/views/modules/test_impresion_hibrida.php</code>";
echo "</div>";
echo "<li><strong>Ejecutar tests</strong> desde el celular</li>";
echo "<li><strong>Comparar resultados</strong> con los del portátil</li>";
echo "</ol>";

echo "<h5>📋 QR Code para acceso rápido:</h5>";
echo "<p>Genera un QR con la URL de esta página para acceso rápido desde celular</p>";

echo "<h5>🔍 Qué esperar:</h5>";
echo "<ul>";
echo "<li><strong>Desde celular en WiFi:</strong> Conexión directa debería funcionar</li>";
echo "<li><strong>Desde celular con datos:</strong> Solo proxy debería funcionar</li>";
echo "<li><strong>Desde portátil en red local:</strong> Ambos métodos deberían funcionar</li>";
echo "</ul>";
echo "</div>";

// Log de resultados
echo "<h2>📊 Log de Resultados:</h2>";
echo "<div id='log-resultados' style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0; min-height: 100px; max-height: 300px; overflow-y: auto; border: 1px solid #ccc;'>";
echo "<p style='color: #666; font-style: italic;'>Los resultados de los tests aparecerán aquí...</p>";
echo "</div>";

echo "<script>
// Función para agregar al log
function agregarAlLog(mensaje) {
    const log = document.getElementById('log-resultados');
    const timestamp = new Date().toLocaleTimeString();
    log.innerHTML += `<div style='border-bottom: 1px solid #eee; padding: 5px 0;'><small style='color: #666;'>[${timestamp}]</small> ${mensaje}</div>`;
    log.scrollTop = log.scrollHeight;
}

// Interceptar resultados para el log
const originalMostrarResultados = testHibrido.mostrarResultados;
testHibrido.mostrarResultados = function(impresora, resultados, div) {
    originalMostrarResultados.call(this, impresora, resultados, div);
    
    resultados.forEach(resultado => {
        const estado = resultado.success ? '✅ ÉXITO' : '❌ FALLO';
        agregarAlLog(`<strong>${impresora.nombre}</strong> - ${resultado.metodo.toUpperCase()}: ${estado}`);
    });
};
</script>";

// Botones de navegación
echo "<p style='margin-top: 30px;'>";
echo "<a href='impresion_hibrida.php' style='background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>← Sistema Híbrido</a>";
echo "<a href='verificar_impresoras.php' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🖨️ Verificar Impresoras</a>";
echo "</p>";
?>
