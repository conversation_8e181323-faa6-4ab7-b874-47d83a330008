<?php
ob_start();
class MvcController
 {
	#LLAMADA A LA PLANTILLA
	##-------------------------------------
	 public function pagina()
		{
			if(!isset($_GET['action']) or $_GET['action']==null or $_GET['action']=="ingresar" or $_GET['action']=="pdf" or $_GET['action']=="pdf1" or $_GET['action']=="inde" or $_GET['action']=="buscar" or $_GET['action']=="pagina1")
				{	include "views/templat.php";}
		}
	 public function pagina2($usuario,$validar)
		 {	echo '<script type="text/javascript" charset="utf-8">
						    //---------- Start refrescar pagina -------------------
								document.addEventListener("DOMContentLoaded", function(){
					                // Invocamos cada 5 segundos ;)
					                const milisegundos = 10 *60000; //tiempo en milisegundos
					                setInterval(function(){
					                    // No esperamos la respuesta de la petición porque no nos importa
					                    //alert("Regrescando cada 5 segundos");
					                    fetch("./refrescar");
					                },milisegundos);
					            });
					        //---------- End refrescar pagina -------------------
					</script>';
		 	if ($usuario==1)
				{
					if ($_GET['action']=="pdf" or $_GET['action']=="pdf1" or $_GET['action']=="permiso" or $_GET['action']=="reporteExcel2")
					 {	include "views/template_report.php"; }
					else{include "views/template.php";}
				}
			else if ($usuario==2)
				{
					if ($_GET['action']=="pdf" or $_GET['action']=="pdf1" or $_GET['action']=="permiso")
						 {	include "views/templat.php"; }
					else{include "views/template1.php";}
				}
			else if ($usuario==10 or $usuario==4)
				{
					if ($_GET['action']=="pdf" or $_GET['action']=="pdf1" or $_GET['action']=="permiso")
						 {	include "views/templat.php"; }
					else{include "views/template2.php";}
				}
			else if ($usuario==3)
				{
					if ($_GET['action']=="pdf" or $_GET['action']=="pdf1" or $_GET['action']=="reporteExcel2")
						 {	include "views/template_report.php"; }
					else{include "views/template1.php";}
				}
			else{ include "views/templat.php";}//// ultima linea modificada José
		 }
	#-------------------------------------
	#ENLACES para navegar y verificar los enlaces
	##-------------------------------------
	 public function enlacesPaginasController()
		{	//session_start();
			$usuario=$_SESSION["tipo_usuario"];
			if(isset($_GET['action']) and $_GET['action']!='' and $_SESSION["validar"] == true)
				{	$enlaces = $_GET['action'];	}
			else { $enlaces = "index";}
			$respuesta = Paginas::enlacesPaginasModel($enlaces,$usuario);
			include $respuesta;
		}
	#----------------------------------------------
	#CANCELAR PEDIDO
	#------------------------------------
	 public function cancelar1Controller()
		{	//echo "<script>alert('controlle cancel ..');</script>";

			$respuesta =Datos::cancelar1Model();
		 if ($respuesta=="sucess")
			{	////////////////  Nuevo 17-01 10:24/ /
				echo "<script>alert('Exito, ..');</script>";
				/*$estado="";
				Datos::actualizaPmesaModel($estado, $_SESSION["mesa"], "mesas");*/
				///////////////////////////////////////////////////////////  /
				return "sucess";
			}
		 else 	{return "error";}
		}
	#----------------------------------------
	#CANCELAR PEDIDO (MEJORADO PARA CANCELAR PEDIDOS TAMBIÉN)
	#------------------------------------
	 public function cancelarController()
		{	//echo "<script>alert('controlle cancel ..');</script>";
			session_start();
			$mesa=$_SESSION["mesa"];

			// NUEVO: Eliminar entregas por categoría y cancelar pedidos antes de limpiar la mesa
			try {
				// 1. Eliminar entregas por categoría para todos los pedidos de la mesa
				$resultado_entregas = Datos::eliminarEntregasCategoriaModel($mesa);
				if ($resultado_entregas == "success") {
					error_log("cancelarController: Entregas por categoría eliminadas para mesa {$mesa}");
				} else {
					error_log("cancelarController: Error al eliminar entregas por categoría para mesa {$mesa}");
				}

				// 2. Cancelar pedidos activos (borrador y enviado)
				require_once "models/conexion.php";
				$stmt = Conexion::conectar();
				$consulta_pedidos = "UPDATE pedidos SET estado = 'cancelado', fecha_entrega = NOW() WHERE mesa_id = $mesa AND estado IN ('borrador', 'enviado')";
				$stmt->exec($consulta_pedidos);
				error_log("cancelarController: Pedidos cancelados para mesa {$mesa}");

			} catch (Exception $e) {
				error_log("cancelarController: Error al cancelar pedidos: " . $e->getMessage());
				// Continuar con la función original aunque falle esto
			}

			// Ejecutar la función original para limpiar productos y mesa
			$respuesta = Datos::cancelarModel($mesa);

		 if ($respuesta=="sucess")
			{	////////////////  Nuevo 17-01 10:24/ /
				echo "<script>alert('Mesa cancelada exitosamente. Pedidos cancelados.');</script>";
				/*$estado="";
				Datos::actualizaPmesaModel($estado, $_SESSION["mesa"], "mesas");*/
				///////////////////////////////////////////////////////////  /
				return "sucess";
			}
		 else 	{return "error";}
		}
	#----------------------------------------
	#CANCELAR PEDIDO CON NUEVA LÓGICA DE PEDIDOS (MEJORADO)
	#------------------------------------
	 public function cancelarConPedidosController()
		{
			session_start();
			$mesa=$_SESSION["mesa"];

			// Log para debug
			error_log("cancelarConPedidosController: Iniciando cancelación para mesa {$mesa}");

			// Primero cancelar pedidos, luego usar la función original
			try {
				// 1. Eliminar entregas por categoría para todos los pedidos de la mesa
				$resultado_entregas = Datos::eliminarEntregasCategoriaModel($mesa);
				if ($resultado_entregas == "success") {
					error_log("cancelarConPedidosController: Entregas por categoría eliminadas para mesa {$mesa}");
				} else {
					error_log("cancelarConPedidosController: Error al eliminar entregas por categoría para mesa {$mesa}");
				}

				// 2. Cancelar pedidos activos
				require_once "models/conexion.php";
				$stmt = Conexion::conectar();
				$consulta_pedidos = "UPDATE pedidos SET estado = 'cancelado', fecha_entrega = NOW() WHERE mesa_id = $mesa AND estado IN ('borrador', 'enviado')";
				$stmt->exec($consulta_pedidos);
				error_log("cancelarConPedidosController: Pedidos cancelados para mesa {$mesa}");

				// 3. Usar función original para limpiar productos y mesa
				$respuesta = Datos::cancelarModel($mesa);

			} catch (Exception $e) {
				error_log("cancelarConPedidosController: Error al cancelar pedidos: " . $e->getMessage());
				// Si falla, usar solo la función original
				$respuesta = Datos::cancelarModel($mesa);
			}

		 if ($respuesta == "success")
			{
				error_log("cancelarConPedidosController: Cancelación exitosa para mesa {$mesa}");
				echo "<script>alert('Mesa cancelada exitosamente. Todos los pedidos han sido cancelados.');</script>";
				return "sucess";
			}
		 else 	{
		 	error_log("cancelarConPedidosController: Error al cancelar mesa {$mesa}");
		 	return "error";
		 }
		}
	#----------------------------------------
	
	#------------------#LISTAS----------------------
	#buscar DE PRODUCTOS
	#------------------------------------
	 public function buscarProductoXController()
		{	$datosController = $_POST["producto"];
			$respuesta = Datos::buscarProductosModel($datosController,"productos");
			//if (isset($_POST["nombreEditar"]))
			if ($_POST["nombreEditar"])
				{
					echo' <input type="text" value="'.$respuesta["nombre"].'" name="nombreEditar" required>
				 <input type="text" value="'.$respuesta["precio"].'" name="precioEditar" required>
				 <input type="hidden" value="'.$respuesta["id"].'" name="idEditar" required>
					 <input type="submit" >';
				}
		}
	#---------------------------------------
	#LISTA Mesa
	#---------------------------------------
	 public function mesasController()
		{
			$respuesta = Datos::mesasModel("mesas");
			return 	$respuesta;
		}
	#---------------------------------------
	#LISTA Roles
	#---------------------------------------
	 public function listaRolesController()
		{
			$respuesta = Datos::listaRolesModel("roles");
			return 	$respuesta;
		}
	#---------------------------------------
	#LISTA Roles
	#---------------------------------------
	 public function listaUnidadesController()
		{
		 /**/$respuesta = Datos::listaUnidadesModel("unidades");
		 return 	$respuesta;
		}
	#---------------------------------------
	#TURNO ACTIVO
	#------------------------------------
	 public function turnoActivoController($nombrep)
		{
			$datosController = $nombrep;
			$respuesta = Datos::turnoActivoModel($datosController);
			return $respuesta;
		}
	#------------------------------------
	#Autocompletar Suministro y buscar su codigo
	#------------------------------------
	 public function buscarSuministroController($nombrep)
		{
		 $datosController = $nombrep;
		 $respuesta = Datos::buscarNombreSModel($datosController, "suministros");
		 return $respuesta;
		}
	#------------------------------------
	#LISTA  Meseros
	#---------------------------------------
	 public function listaMeserosController()
		{
			$datosController=$mesero;
			$respuesta = Datos::buscarRollModel($datosController,"personas");
			return 	$respuesta;
		}
	#---------------------------------------
	#LISTA Departamentos
	#---------------------------------------
	 public function listaDepartamentosController()
		{
			$respuesta = Datos::listaDepartamentosModel("departamentos");
			return 	$respuesta;
		}
	#---------------------------------------
	#LISTA Ciudades
	#---------------------------------------
	 public function listaCiudadesController()
		{
			$respuesta = Datos::listaCiudadesModel("ciudad");
			return 	$respuesta;
		}
	#---------------------------------------
	#LISTA Pedidos
	#---------------------------------------
	 public function listaPedidosController()
		{
			$respuesta = Datos::listaPedidosModel("pedidos");
			return 	$respuesta;
		}
	#---------------------------------------
	#LISTA Mesas
	#---------------------------------------
	 public function listaMesasController()
		{
			$respuesta = Datos::listaMesasModel("mesas");
			return 	$respuesta;
		}
	#---------------------------------------
	##LISTA PUNTOS O SUCURSALES
	#---------------------------------------
	 public function listaPuntosController()
		{
			$respuesta = Datos::listaPuntosModel("punto");
			return 	$respuesta;
		}
	#---------------------------------------
	##LISTA SUMINISTRO
	#---------------------------------------
	 public function listaSuministrosController()
		{
		 $respuesta = Datos::listaSuministrosModel("suministros");
		 return 	$respuesta;
		}
	#---------------------------------------
	##LISTA PRODUCTO
	#---------------------------------------
	 public function listaProductosController()
		{
		 $respuesta = Datos::listaProductosModel("suministros");
		 return 	$respuesta;
		}
	#---------------------------------------
	##LISTA SUMINISTRO1
	#---------------------------------------
	 public function listaSuministros1Controller()
		{
		 $respuesta = Datos::listaSuministrosModel("productos");
		  $f=0;
		 foreach ($respuesta as $row => $item) 
			{
			  if ($f==0) 
			 	{
			 	 $f1=$item["codigo"]+1;
			 	 break;
			 	}
			}
		 return $f1;		
		}
	#---------------------------------------
	##LISTA PRODUCTO1
	#---------------------------------------
	 public function listaProductos1Controller()
		{
		 $respuesta = Datos::listaProductosModel("productos");
		  $f=0;
		 foreach ($respuesta as $row => $item) 
			{
			  if ($f==0) 
			 	{
			 	 $f1=$item["codigo"]+1;
			 	 break;
			 	}
			}
		 return $f1;
		}
	#---------------------------------------
	#------------------------------------------------------------
 }
ob_flush();
