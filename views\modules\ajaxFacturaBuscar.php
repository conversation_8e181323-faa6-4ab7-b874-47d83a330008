<?php
session_start();
require_once "../../models/conexion.php";

// Verificar que se recibieron los datos
if (!isset($_POST['factura']) || !isset($_POST['pago'])) {
    echo "<div class='alert alert-danger'>Error: Datos incompletos</div>";
    exit;
}

$numeroFactura = $_POST['factura'];
$tipoPago = $_POST['pago']; // 1 = Efectivo, 2 = Crédito

try {
    $db = Conexion::conectar();
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Buscar la factura en la tabla ventas
    $sql = "SELECT 
                v.id as factura_id,
                v.pedidos_id,
                v.turno_cajero_id,
                v.valor,
                v.fecha_venta,
                v.subtotal,
                v.iva,
                v.total,
                v.efectivo,
                v.bancolombia,
                v.nequi,
                v.daviplata,
                v.valortarjeta,
                v.cambio,
                v.propina,
                v.mesa,
                p.numero_pedido,
                p.estado as estado_pedido
            FROM ventas v
            LEFT JOIN pedidos p ON v.pedidos_id = p.id
            WHERE v.id = ?";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([$numeroFactura]);
    $factura = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$factura) {
        echo "<div class='alert alert-warning'>
                <h4>❌ Factura No Encontrada</h4>
                <p>No se encontró la factura número <strong>$numeroFactura</strong> en el sistema.</p>
              </div>";
        exit;
    }
    
    // Verificar tipo de pago
    $esPagoEfectivo = ($factura['efectivo'] > 0 || $factura['bancolombia'] > 0 || $factura['nequi'] > 0 || $factura['daviplata'] > 0);
    $esPagoCredito = ($factura['valortarjeta'] > 0);
    
    if ($tipoPago == 1 && !$esPagoEfectivo) {
        echo "<div class='alert alert-warning'>
                <h4>⚠️ Tipo de Pago Incorrecto</h4>
                <p>La factura <strong>$numeroFactura</strong> no fue pagada en efectivo.</p>
                <p>Selecciona el tipo de pago correcto.</p>
              </div>";
        exit;
    }
    
    if ($tipoPago == 2 && !$esPagoCredito) {
        echo "<div class='alert alert-warning'>
                <h4>⚠️ Tipo de Pago Incorrecto</h4>
                <p>La factura <strong>$numeroFactura</strong> no fue pagada a crédito.</p>
                <p>Selecciona el tipo de pago correcto.</p>
              </div>";
        exit;
    }
    
    // CORREGIDO: Obtener productos de la factura AGRUPADOS (igual que en facturación)
    $sqlProductos = "SELECT
                        pr.id as producto_id,
                        pr.nombre as producto_nombre,
                        pr.codigo as producto_codigo,
                        SUM(ppm.cantidad) as cantidad,
                        ppm.precio,
                        AVG(ppm.descuento) as descuento,
                        (SUM(ppm.cantidad) * ppm.precio) as subtotal_producto,
                        ((SUM(ppm.cantidad) * ppm.precio) * (AVG(ppm.descuento) / 100)) as descuento_aplicado
                     FROM pedido_productos_mesa ppm
                     JOIN productos pr ON ppm.productos_id = pr.id
                     WHERE ppm.pedidos_id = ?
                     GROUP BY pr.id, pr.nombre, pr.codigo, ppm.precio, ppm.descuento
                     ORDER BY pr.nombre";
    
    $stmtProductos = $db->prepare($sqlProductos);
    $stmtProductos->execute([$factura['pedidos_id']]);
    $productos = $stmtProductos->fetchAll(PDO::FETCH_ASSOC);
    
    // Configurar variables de sesión para el PDF
    $_SESSION["pedidos_id"] = $factura['pedidos_id'];
    $_SESSION["turno_cajero_id"] = $factura['turno_cajero_id'];
    $_SESSION["subtotal"] = $factura['subtotal'];
    $_SESSION["total"] = $factura['total'];
    $_SESSION['idFactura'] = $factura['factura_id'];
    $_SESSION['propina'] = $factura['propina'];
    $_SESSION['mesaFactura'] = $factura['mesa'];
    $_SESSION["efectivo"] = $factura['efectivo'];
    $_SESSION["tarjeta"] = $factura['valortarjeta'];
    $_SESSION["bancolombia"] = $factura['bancolombia'];
    $_SESSION["nequi"] = $factura['nequi'];
    $_SESSION["daviplata"] = $factura['daviplata'];
    $_SESSION["cambio"] = $factura['cambio'];
    
    // Mostrar información de la factura
    echo "<div class='alert alert-success'>
            <h4>✅ Factura Encontrada</h4>
            <p><strong>Número:</strong> {$factura['factura_id']}</p>
            <p><strong>Fecha:</strong> {$factura['fecha_venta']}</p>
            <p><strong>Mesa:</strong> {$factura['mesa']}</p>
            <p><strong>Estado:</strong> {$factura['estado_pedido']}</p>
          </div>";
    
    // Mostrar detalles de pago
    echo "<div class='panel panel-info'>
            <div class='panel-heading'>
                <h4>💰 Detalles de Pago</h4>
            </div>
            <div class='panel-body'>
                <div class='row'>
                    <div class='col-md-6'>
                        <p><strong>Subtotal:</strong> $" . number_format($factura['subtotal']) . "</p>
                        <p><strong>IVA:</strong> $" . number_format($factura['iva']) . "</p>
                        <p><strong>Propina:</strong> $" . number_format($factura['propina']) . "</p>
                        <p><strong>Total:</strong> $" . number_format($factura['total']) . "</p>
                    </div>
                    <div class='col-md-6'>";
    
    if ($factura['efectivo'] > 0) {
        echo "<p><strong>Efectivo:</strong> $" . number_format($factura['efectivo']) . "</p>";
    }
    if ($factura['valortarjeta'] > 0) {
        echo "<p><strong>Tarjeta:</strong> $" . number_format($factura['valortarjeta']) . "</p>";
    }
    if ($factura['bancolombia'] > 0) {
        echo "<p><strong>Bancolombia:</strong> $" . number_format($factura['bancolombia']) . "</p>";
    }
    if ($factura['nequi'] > 0) {
        echo "<p><strong>Nequi:</strong> $" . number_format($factura['nequi']) . "</p>";
    }
    if ($factura['daviplata'] > 0) {
        echo "<p><strong>Daviplata:</strong> $" . number_format($factura['daviplata']) . "</p>";
    }
    if ($factura['cambio'] > 0) {
        echo "<p><strong>Cambio:</strong> $" . number_format($factura['cambio']) . "</p>";
    }
    
    echo "      </div>
                </div>
            </div>
          </div>";
    
    // Mostrar productos
    if (!empty($productos)) {
        echo "<div class='panel panel-default'>
                <div class='panel-heading'>
                    <h4>🛒 Productos Facturados</h4>
                </div>
                <div class='panel-body'>
                    <table class='table table-striped'>
                        <thead>
                            <tr>
                                <th>Código</th>
                                <th>Producto</th>
                                <th>Cantidad</th>
                                <th>Precio Unit.</th>
                                <th>Descuento</th>
                                <th>Subtotal</th>
                            </tr>
                        </thead>
                        <tbody>";
        
        $totalProductos = 0;
        foreach ($productos as $producto) {
            $subtotalConDescuento = $producto['subtotal_producto'] - $producto['descuento_aplicado'];
            $totalProductos += $subtotalConDescuento;
            
            echo "<tr>
                    <td>{$producto['producto_codigo']}</td>
                    <td>{$producto['producto_nombre']}</td>
                    <td>{$producto['cantidad']}</td>
                    <td>$" . number_format($producto['precio']) . "</td>
                    <td>{$producto['descuento']}%</td>
                    <td>$" . number_format($subtotalConDescuento) . "</td>
                  </tr>";
        }
        
        echo "      </tbody>
                        <tfoot>
                            <tr class='info'>
                                <th colspan='5'>Total Productos:</th>
                                <th>$" . number_format($totalProductos) . "</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
              </div>";
    }
    
    // Botón para reimprimir
    echo "<div class='text-center' style='margin: 20px 0;'>
            <a href='pdf' target='_blank' class='btn btn-primary btn-lg'>
                🖨️ Reimprimir Factura
            </a>
            <button onclick='window.print()' class='btn btn-info btn-lg' style='margin-left: 10px;'>
                🖨️ Imprimir Esta Página
            </button>
          </div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>
            <h4>❌ Error en la Búsqueda</h4>
            <p>Error: " . htmlspecialchars($e->getMessage()) . "</p>
          </div>";
    error_log("Error en ajaxFacturaBuscar.php: " . $e->getMessage());
}
?>
