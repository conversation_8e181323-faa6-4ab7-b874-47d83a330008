<?php
session_start();

echo "<!DOCTYPE html>";
echo "<html><head><title>Debug Ruta Facturación</title></head><body>";
echo "<h2>🔧 Debug de Ruta Facturación Rápida</h2>";

// Verificar sesión
echo "<h3>📊 Estado de Sesión:</h3>";
if (isset($_SESSION["validar"]) && $_SESSION["validar"]) {
    echo "<p style='color: green;'>✅ Sesión válida</p>";
    echo "<p><strong>Usuario:</strong> " . ($_SESSION["usuario"] ?? "No definido") . "</p>";
    echo "<p><strong>Tipo:</strong> " . ($_SESSION["tipo_usuario"] ?? "No definido") . "</p>";
    echo "<p><strong>Persona:</strong> " . ($_SESSION["persona"] ?? "No definido") . "</p>";
} else {
    echo "<p style='color: red;'>❌ Sesión no válida</p>";
    echo "<p>Necesitas iniciar sesión primero</p>";
}

// Verificar archivos
echo "<h3>📁 Verificación de Archivos:</h3>";
$archivos = [
    'facturacionRapida.php',
    'ajaxBuscarProductoRapido.php', 
    'ajaxFacturacionRapida.php'
];

foreach ($archivos as $archivo) {
    if (file_exists($archivo)) {
        echo "<p style='color: green;'>✅ $archivo existe</p>";
    } else {
        echo "<p style='color: red;'>❌ $archivo NO existe</p>";
    }
}

// Verificar .htaccess
echo "<h3>🔧 Verificación de .htaccess:</h3>";
$htaccess_path = "../../.htaccess";
if (file_exists($htaccess_path)) {
    echo "<p style='color: green;'>✅ .htaccess existe</p>";
    $htaccess_content = file_get_contents($htaccess_path);
    if (strpos($htaccess_content, 'RewriteEngine on') !== false) {
        echo "<p style='color: green;'>✅ RewriteEngine está activado</p>";
    } else {
        echo "<p style='color: red;'>❌ RewriteEngine NO está activado</p>";
    }
    
    if (strpos($htaccess_content, 'RewriteRule') !== false) {
        echo "<p style='color: green;'>✅ Reglas de reescritura encontradas</p>";
    } else {
        echo "<p style='color: red;'>❌ No se encontraron reglas de reescritura</p>";
    }
} else {
    echo "<p style='color: red;'>❌ .htaccess NO existe</p>";
}

// Verificar enlaces.php
echo "<h3>🛣️ Verificación de enlaces.php:</h3>";
$enlaces_path = "../../models/enlaces.php";
if (file_exists($enlaces_path)) {
    echo "<p style='color: green;'>✅ enlaces.php existe</p>";
    $enlaces_content = file_get_contents($enlaces_path);
    
    // Buscar facturacionRapida en diferentes secciones
    $busquedas = [
        'facturacionRapida' => 'Ruta facturacionRapida',
        'usuario==1' => 'Sección administrador',
        'usuario==3' => 'Sección cajero'
    ];
    
    foreach ($busquedas as $buscar => $descripcion) {
        $count = substr_count($enlaces_content, $buscar);
        if ($count > 0) {
            echo "<p style='color: green;'>✅ $descripcion encontrada ($count veces)</p>";
        } else {
            echo "<p style='color: red;'>❌ $descripcion NO encontrada</p>";
        }
    }
} else {
    echo "<p style='color: red;'>❌ enlaces.php NO existe</p>";
}

// Test de simulación de ruta
echo "<h3>🧪 Test de Simulación de Ruta:</h3>";
try {
    // Simular $_GET['action']
    $_GET['action'] = 'facturacionRapida';
    
    // Incluir el sistema de enlaces
    require_once "../../models/enlaces.php";
    
    $enlaces = new EnlacesPaginas();
    $usuario = $_SESSION["tipo_usuario"] ?? 1;
    $module = $enlaces->enlacesPaginasModel('facturacionRapida', $usuario);
    
    if ($module) {
        echo "<p style='color: green;'>✅ Ruta simulada exitosamente: $module</p>";
        
        // Verificar si el archivo del módulo existe
        $module_path = "../../$module";
        if (file_exists($module_path)) {
            echo "<p style='color: green;'>✅ Archivo del módulo existe: $module_path</p>";
        } else {
            echo "<p style='color: red;'>❌ Archivo del módulo NO existe: $module_path</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ La simulación de ruta falló</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error en simulación: " . $e->getMessage() . "</p>";
}

// Test directo del archivo
echo "<h3>🔗 Test Directo del Archivo:</h3>";
echo "<p>Probando acceso directo al archivo...</p>";

if (file_exists('facturacionRapida.php')) {
    echo "<p><a href='facturacionRapida.php' target='_blank' style='background: #28a745; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>📄 Abrir facturacionRapida.php</a></p>";
} else {
    echo "<p style='color: red;'>❌ No se puede probar - archivo no existe</p>";
}

// URLs de prueba
echo "<h3>🌐 URLs de Prueba:</h3>";
$base_url = "https://" . $_SERVER['HTTP_HOST'];

$urls = [
    "$base_url/index.php?action=facturacionRapida" => "Ruta con index.php (RECOMENDADA)",
    "$base_url/facturacionRapida" => "Ruta amigable (puede fallar)",
    "$base_url/views/modules/facturacionRapida.php" => "Archivo directo"
];

foreach ($urls as $url => $descripcion) {
    echo "<p><a href='$url' target='_blank' style='background: #007bff; color: white; padding: 8px 15px; text-decoration: none; border-radius: 3px; margin: 2px;'>🔗 $descripcion</a></p>";
}

// Información del servidor
echo "<h3>🖥️ Información del Servidor:</h3>";
echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace;'>";
echo "<p><strong>Servidor:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'No disponible') . "</p>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Document Root:</strong> " . ($_SERVER['DOCUMENT_ROOT'] ?? 'No disponible') . "</p>";
echo "<p><strong>Request URI:</strong> " . ($_SERVER['REQUEST_URI'] ?? 'No disponible') . "</p>";
echo "<p><strong>Mod Rewrite:</strong> " . (function_exists('apache_get_modules') && in_array('mod_rewrite', apache_get_modules()) ? 'Disponible' : 'No verificable') . "</p>";
echo "</div>";

?>

<h3>💡 Recomendaciones:</h3>
<div style="background: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107;">
    <h4>Para solucionar el problema:</h4>
    <ol>
        <li><strong>Usa la ruta con index.php:</strong> Es más confiable que la URL amigable</li>
        <li><strong>Verifica tu sesión:</strong> Asegúrate de estar logueado</li>
        <li><strong>Prueba el archivo directo:</strong> Si funciona, el problema está en las rutas</li>
        <li><strong>Revisa los logs del servidor:</strong> Pueden mostrar errores específicos</li>
    </ol>
    
    <h4>URL Recomendada:</h4>
    <p style="font-size: 18px; font-weight: bold;">
        <a href="<?php echo $base_url; ?>/index.php?action=facturacionRapida" target="_blank" style="color: #007bff;">
            <?php echo $base_url; ?>/index.php?action=facturacionRapida
        </a>
    </p>
</div>

<br>
<a href="../../index.php?action=mesa" style="background: #6c757d; color: white; padding: 10px; text-decoration: none; border-radius: 5px;">🔙 Volver a Mesas</a>

</body>
</html>
