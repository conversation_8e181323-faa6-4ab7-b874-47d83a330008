<?php
ob_start();
class controllerPedidoMesaVendido extends MvcController
{
	#REGISTRO DE PEDIDO MESA VENTA
	#------------------------------------
	 public function registroPmesaController()
		{
		 if(isset($_POST["codigo"]))
			{
				try {
					// Incluir el controlador de estados
					require_once "controllers/controllerEstadoPedidos.php";
					require_once "models/crudEstadoPedidos.php";

					$controllerEstado = new ControllerEstadoPedidos();

					// Obtener o crear pedido borrador para esta mesa
					$pedidoBorrador = $controllerEstado->obtenerPedidoBorradorController($_POST["mesas_idPmesaRegistro"]);

					if (!$pedidoBorrador) {
						echo "<script>alert('Error al crear pedido borrador')</script>";
						header("location:index.php?action=registroPmesa&ida=".$_SESSION["mesa"]);
						return;
					}

					// Verificar que el producto existe
					$producto = DatosPedidoMesaVendido::buscarProducto($_POST["codigo"], "productos");
					if (!$producto) {
						echo "<script>alert('Producto no encontrado con código: ".$_POST["codigo"]."')</script>";
						header("location:index.php?action=registroPmesa&ida=".$_SESSION["mesa"]);
						return;
					}

					$datosController = array(
						'codigo' => $_POST["codigo"],
						'mesas_id' => $_POST["mesas_idPmesaRegistro"],
						'cantidad' => $_POST["cantidadPmesaRegistro"],
						'descuento' => $_POST["descuentoP"],
						'nota' => $_POST["nota"],
						'cliente' => isset($_SESSION["clientep"]) ? $_SESSION["clientep"] : '0',
						'mesero' => $_SESSION["usuario"],
						'pedido_id' => $pedidoBorrador['id']
					);

					$respuesta = DatosPedidoMesaVendido::registroPmesaModel($datosController, "producto_vendido_mesa");

					if($respuesta == "success") {
						echo'<script>alert("Producto agregado al pedido '.$pedidoBorrador['numero_pedido'].'");</script>';
						header("location:index.php?action=registroPmesa&ida=".$_SESSION["mesa"]);
					} else {
						echo "<script>alert('Error al agregar producto: ".$respuesta."')</script>";
						header("location:index.php?action=registroPmesa&ida=".$_SESSION["mesa"]);
					}
				} catch (Exception $e) {
					echo "<script>alert('Error del sistema: ".$e->getMessage()."')</script>";
					header("location:index.php?action=registroPmesa&ida=".$_SESSION["mesa"]);
				}
			}
		}
	#------------------------------------------
	#datos Mesa Id
	#---------------------------------------
		public function vistaMesaIdController($id)
		{

			$respuesta = DatosPedidoMesaVendido::vistaMesaIdModel($id,"mesas");
			return $respuesta;
		}
	#------------------------------------
	#VISTA DE PEDIDO MESA VENTA  pr.id, pr.nombre,pr.precio, pvm.cantidad, pvm.fecha_hora
	#------------------------------------
	 public function vistaPmesaController()
		{	//session_start();
			$Total=0;
			$cantidaPoducto=0;
		 if ($_GET["ida"]>0 )
			{
				$_SESSION["mesa"]= $_GET["ida"];
			}
			$mesa = $_SESSION["mesa"];
		/*	$mesa = 1;*/
			$respuestaM = DatosPedidoMesaVendido::buscarClienteMesaModel($mesa,"mesas");
		 if ($respuestaM>0)
			{
				$_SESSION["clientep"]= $respuestaM['descripcion'];
			}

			// Para la vista de mesa, mostrar solo productos del pedido borrador actual (para edición)
			$respuesta = DatosPedidoMesaVendido::vistaPmesaModel($mesa,"producto_vendido_mesa");
			$totalDescuento=0;

			$formateador2 = new NumberFormatter( 'es_CO', NumberFormatter::CURRENCY );
			$formateador = new NumberFormatter( 'en_US', NumberFormatter::DECIMAL );

		 foreach($respuesta as $row => $item)
			{
			 $des=$item["preciopr"]*($item["descuentopvm"]/100)	;
			 $totalProducto=$item["preciopr"]*$item["cantidadpvm"];
			 $totalProductoDes=($item["preciopr"]-$des)*$item["cantidadpvm"];
			 echo'
			 	<tr>
					<td>'.$item["prcodigo"].'</td>
					<td>'.$item["nombrepr"].'</td>
					<td>'.$item["fechapvm"].'</td>
					<td>'.$formateador->format($item["preciopr"]).'</td><td>-'.$des.'</td><td>'.$item["cantidadpvm"].'</td>
					<td>'.$formateador->format($totalProductoDes).'</td>
					<td>
					<a href="#" onclick="aumentarCantidad('.$item["idpr"].', '.$item["idmesa"].',\''.$item["fechapvm"].'\');"><img src="img/sumar+.fw-min.png" width="20" height="20" alt=""></a>

					<a href="#" onclick="disminuir('.$item["idpr"].', '.$item["idmesa"].',\''.$item["fechapvm"].'\','.$item["cantidadpvm"].');"><img src="img/restar.fw-min.png" width="20" height="20" alt=""></a>

					<!--<a href="index.php?action=editarPmesa&fecha_hora='.$item["fechapvm"].'&idBorrar='.$item["idpr"].'"><img src="img/editar-min.png" width="20" height="20" alt=""></a>-->
					<a href="index.php?action=registroPmesa&fecha_horaBorrar='.$item["fechapvm"].'&idBorrar='.$item["idpr"].'"><img src="img/eliminar.jpeg" width="20" height="20" alt=""></a></td>
				</tr>';
				$Total=$Total+$totalProducto;
				$totalDescuento=$totalDescuento+$des;
				$cantidaPoducto=$cantidaPoducto+$item["cantidadpvm"];
				//$_SESSION[contador]= $_SESSION[contador]+1; <td>'.$item["descuento"].'</td>
			}

			// Obtener el total completo para facturación (incluye todos los pedidos)
			$totalFacturacion = $this->obtenerTotalFacturacionController($mesa);
			$TotalCompleto = $totalFacturacion['total'];
			$totalDescuentoCompleto = $totalFacturacion['totalDescuento'];
			$totaPagoCompleto = $totalFacturacion['totalPagar'];

			$propina=$totaPagoCompleto*0;//$propina=$totaPago*0.1;
			//$propina=$totaPago*0;
			// CORREGIDO: Actualizar el campo 'pagar' visible con el total real (SIN JQUERY)
			echo "<script>
						console.log('🔧 Ejecutando script para actualizar campo pagar');
						console.log('Total a asignar:', '".$totaPagoCompleto."');

						// Función para actualizar el campo
						function actualizarCampoPagar() {
							var campo = document.getElementById('pagar');
							if (campo) {
								console.log('✅ Campo pagar encontrado, valor anterior:', campo.value);
								campo.value = '".$totaPagoCompleto."';
								console.log('✅ Campo pagar actualizado, valor nuevo:', campo.value);

								// Aplicar formato si la función existe
								if (typeof formatCurrency === 'function') {
									formatCurrency(campo);
									console.log('✅ Formato aplicado, valor final:', campo.value);
								} else {
									console.log('⚠️ Función formatCurrency no disponible');
								}
							} else {
								console.log('❌ Campo pagar no encontrado');
							}
						}

						// Intentar actualizar cuando el DOM esté listo (SIN JQUERY)
						if (document.readyState === 'loading') {
							document.addEventListener('DOMContentLoaded', function() {
								console.log('📄 DOM ready (vanilla JS), intentando actualizar campo pagar');
								actualizarCampoPagar();
							});
						} else {
							console.log('📄 DOM ya está listo, actualizando inmediatamente');
							actualizarCampoPagar();
						}

						// También intentar después de un pequeño delay
						setTimeout(function() {
							console.log('⏰ Timeout ejecutado, intentando actualizar campo pagar');
							actualizarCampoPagar();
						}, 1000);
					</script>";

			echo ' 	<tr> <h4>
							<td colspan="4">Total (Solo Borrador)</td>
							<td colspan="1">-$ '.$totalDescuento.'</td>
							<td colspan="1"> $'.$formateador->format($Total).'</td>
							<td colspan="2"> $'.$formateador->format($Total-$totalDescuento).'</td>	</h4>
				   	</tr>
				   	<tr style="background-color: #d4edda;"> <h4>
							<td colspan="4"><strong>TOTAL A FACTURAR (Todos los Pedidos)</strong></td>
							<td colspan="1"><strong>-$ '.$totalDescuentoCompleto.'</strong><input type="hidden" readonly name="totalDescuento" id="totalDescuento" Value="'.$totalDescuentoCompleto.'"></td>
							<td colspan="1"><strong> $'.$formateador->format($TotalCompleto).'</strong><input type="hidden" readonly name="total" id="total" Value="'.$TotalCompleto.'"></td>
							<td colspan="2"><strong> $'.$formateador->format($totaPagoCompleto).'</strong></td>	</h4>
				   	</tr>
				   	<tr>
						<td colspan="6"><h5><b>Propina</b> </h5></td>
						<td colspan="3"><input type="text" placeholder="$" name="propina" id="propina" onkeyup="funcion_calcular();" Value="'.$propina.'">  </td>
					</tr>';
				   	//$mesa = $respuesta["idmesa"];
			}
	#------------------------------------
	#EDITAR PEDIDO MESA VENTA
	#------------------------------------
		public function editarPmesaController()
			{	//echo "<script>alert('controller ".$_GET["fecha_hora"]."')</script>";
				$datosController = $_GET["fecha_hora"];

				$respuesta = DatosPedidoMesaVendido::editarPmesaModel($datosController, "producto_vendido_mesa");
				echo'<input type="hidden" value="'.$respuesta["productos_id"].'" name="productos_idPmesaEditar" required>
				 <input type="hidden" value="'.$respuesta["mesas_id"].'" name="mesas_idPmesaEditar" required>
				 Cantidad :<input type="text" value="'.$respuesta["cantidad"].'" name="cantidadPmesaEditar" required><br>
				 Nota :<input type="text" value="'.$respuesta["nota"].'" name="nota" ><br>
				 <input type="hidden" value="'.$respuesta["fecha_hora"].'" name="fecha_horaPmesaEditar" required>

					 <input type="submit" value="Actualizar">';
			}
	#------------------------------------
	#Aumentar PEDIDO MESA VENTA
	#------------------------------------
	/*=============================================
	AGREGAR PRODUCTO A MESA - VERSIÓN MEJORADA
	=============================================*/
	public function addPmesaController($datos) {

	    try {
	        error_log("addPmesaController: Iniciando para mesa " . $datos["mesa_id"]);

	        // Incluir el controlador de estados
	        require_once "controllerEstadoPedidos.php";
	        require_once "../models/crudEstadoPedidos.php";

	        $controllerEstado = new ControllerEstadoPedidos();

	        // Obtener o crear pedido borrador para esta mesa
	        $pedidoBorrador = $controllerEstado->obtenerPedidoBorradorController($datos["mesa_id"]);

	        error_log("addPmesaController: Pedido borrador obtenido: " . print_r($pedidoBorrador, true));

	        if (!$pedidoBorrador) {
	            error_log("addPmesaController: No se pudo obtener/crear pedido borrador");
	            return "error";
	        }

	        // Agregar el pedido_id a los datos
	        $datos['pedido_id'] = $pedidoBorrador['id'];

	        error_log("addPmesaController: Agregando producto al pedido " . $pedidoBorrador['id']);

	        // Agregar el producto al pedido
	        $respuesta = DatosPedidoMesaVendido::addPmesaModel($datos, "producto_vendido_mesa");

	        error_log("addPmesaController: Resultado: " . $respuesta);

	        // NO imprimir automáticamente al agregar productos
	        // Solo se imprime cuando se envía el pedido

	        return $respuesta;

	    } catch (Exception $e) {
	        error_log("addPmesaController: Error: " . $e->getMessage());
	        return "error";
	    }
	}
	#------------------------------------
	#Aumentar Cantidad de Producto PEDIDO MESA VENTA
	#------------------------------------
		public function addCantidadPmesaController($datosController)
			{	
				$respuesta = DatosPedidoMesaVendido::addCantidadPmesaModel($datosController, "producto_vendido_mesa");
				return $respuesta; 
			}
	#------------------------------------
	#Disminuir PEDIDO MESA VENTA
	#------------------------------------
		public function disminuirPmesaController($datosController)
			{	
				$respuesta = DatosPedidoMesaVendido::disminuirPmesaModel($datosController, "producto_vendido_mesa");
				return $respuesta; 
			}
	#------------------------------------
	#ACTUALIZAR PEDIDO MESAVENTA
	#------------------------------------
		public function actualizarPmesaController()
			{	//echo "<script>alert('Entro Controller Actualizar Pedido')</script>";
				if(isset($_POST["productos_idPmesaEditar"], $_POST["mesas_idPmesaEditar"], $_POST["cantidadPmesaEditar"],$_POST["fecha_horaPmesaEditar"]))
					{	//echo "<script>alert('Entro Controller Actualizar IF')</script>";
						$datosController = array(  "productos_id"=>$_POST["productos_idPmesaEditar"],
													"mesas_id"=>$_POST["mesas_idPmesaEditar"],
													"cantidad"=>$_POST["cantidadPmesaEditar"],
													"nota"=>$_POST["nota"],
													"fecha_hora"=>$_POST["fecha_horaPmesaEditar"]	);
						$respuesta = DatosPedidoMesaVendido::actualizarPmesaModel($datosController, "producto_vendido_mesa");
						if($respuesta == "success")
							{	header("location:index.php?action=registroPmesa&ida=".$_SESSION["mesa"]);	}
									else
							{	echo "error";	}
					}
			}

	#BORRAR PEDIDO MESA VENTA
	#------------------------------------
	 public function borrarPmesaController()
		{	//echo "<script>alert('entro controle')</script>";
		 if(isset($_GET["idBorrar"]))
			{	//echo "<script>alert('entro controle IF')</script>";
			 $datosController =array('fecha_hora' => $_GET["fecha_horaBorrar"],
									'mesa' => $_SESSION["mesa"],
									'productos_id' => $_GET["idBorrar"]);
			 $respuesta = DatosPedidoMesaVendido::borrarPmesaModel($datosController, "producto_vendido_mesa");
			 if($respuesta == "success")
				{	header("location:index.php?action=registroPmesa&ida=".$_SESSION["mesa"]);	}
			}
		}

	#------------------------------------
	#OBTENER TOTAL PARA FACTURACIÓN (INCLUYE TODOS LOS PRODUCTOS ENTREGADOS)
	#------------------------------------
	public function obtenerTotalFacturacionController($mesa) {
		$productosEntregados = DatosPedidoMesaVendido::obtenerProductosEntregadosMesaModel($mesa);
		$total = 0;
		$totalDescuento = 0;

		foreach($productosEntregados as $item) {
			$des = $item["preciopr"] * ($item["descuentopvm"] / 100);
			$totalProducto = $item["preciopr"] * $item["cantidadpvm"];
			$total += $totalProducto;
			$totalDescuento += $des * $item["cantidadpvm"];
		}

		return array(
			'total' => $total,
			'totalDescuento' => $totalDescuento,
			'totalPagar' => $total - $totalDescuento,
			'productos' => $productosEntregados
		);
	}
	#---------------------------------
}
