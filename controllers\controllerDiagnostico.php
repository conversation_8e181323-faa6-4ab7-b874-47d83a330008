<?php

class ControllerDiagnostico {
    
    /*=============================================
    EJECUTAR DIAGNOSTICO COMPLETO
    =============================================*/
    public function ejecutarDiagnosticoController() {
        
        $resumen = DatosDiagnostico::obtenerResumenSistemaModel();
        
        // Calcular estado general
        $estadoGeneral = $this->calcularEstadoGeneralController($resumen);
        
        return array(
            "estado_general" => $estadoGeneral,
            "detalles" => $resumen,
            "timestamp" => date('Y-m-d H:i:s')
        );
    }
    
    /*=============================================
    CALCULAR ESTADO GENERAL DEL SISTEMA
    =============================================*/
    private function calcularEstadoGeneralController($resumen) {
        
        $errores = 0;
        $advertencias = 0;
        $exitosos = 0;
        
        // Verificar conexión
        if ($resumen['conexion']['status'] == 'error') {
            $errores++;
        } else {
            $exitosos++;
        }
        
        // Verificar estructura pedidos
        if ($resumen['estructura_pedidos']['status'] == 'error') {
            $errores++;
        } else {
            $exitosos++;
        }
        
        // Verificar tabla impresoras
        if ($resumen['tabla_impresoras']['status'] == 'error') {
            $errores++;
        } else {
            $exitosos++;
        }
        
        // Verificar tablas historial
        foreach ($resumen['tablas_historial'] as $tabla => $resultado) {
            if ($resultado['status'] == 'error') {
                $errores++;
            } else {
                $exitosos++;
            }
        }
        
        // Verificar triggers
        if ($resumen['triggers']['status'] == 'error') {
            $errores++;
        } elseif ($resumen['triggers']['status'] == 'warning') {
            $advertencias++;
        } else {
            $exitosos++;
        }
        
        // Verificar archivos
        foreach ($resumen['archivos'] as $archivo => $resultado) {
            if ($resultado['status'] == 'error') {
                $errores++;
            } else {
                $exitosos++;
            }
        }
        
        // Verificar configuración impresoras
        if ($resumen['configuracion_impresoras']['status'] == 'error') {
            $errores++;
        } elseif ($resumen['configuracion_impresoras']['status'] == 'warning') {
            $advertencias++;
        } else {
            $exitosos++;
        }

        // Verificar datos de prueba
        if ($resumen['datos_prueba']['status'] == 'error') {
            $errores++;
        } else {
            $exitosos++;
        }
        
        // Determinar estado general
        if ($errores > 0) {
            $estado = "error";
            $mensaje = "Sistema con errores críticos";
        } elseif ($advertencias > 0) {
            $estado = "warning";
            $mensaje = "Sistema funcional con advertencias";
        } else {
            $estado = "success";
            $mensaje = "Sistema completamente funcional";
        }
        
        return array(
            "status" => $estado,
            "message" => $mensaje,
            "errores" => $errores,
            "advertencias" => $advertencias,
            "exitosos" => $exitosos,
            "total_verificaciones" => $errores + $advertencias + $exitosos
        );
    }
    
    /*=============================================
    VERIFICAR SOLO CONEXION
    =============================================*/
    public function verificarConexionController() {
        return DatosDiagnostico::verificarConexionModel();
    }
    
    /*=============================================
    VERIFICAR SOLO ESTRUCTURA PEDIDOS
    =============================================*/
    public function verificarEstructuraPedidosController() {
        return DatosDiagnostico::verificarEstructuraPedidosModel();
    }
    
    /*=============================================
    VERIFICAR SOLO TABLA IMPRESORAS
    =============================================*/
    public function verificarTablaImpresorasController() {
        return DatosDiagnostico::verificarTablaImpresorasModel();
    }
    
    /*=============================================
    VERIFICAR SOLO TABLAS HISTORIAL
    =============================================*/
    public function verificarTablasHistorialController() {
        return DatosDiagnostico::verificarTablasHistorialModel();
    }
    
    /*=============================================
    VERIFICAR SOLO TRIGGERS
    =============================================*/
    public function verificarTriggersController() {
        return DatosDiagnostico::verificarTriggersModel();
    }
    
    /*=============================================
    VERIFICAR SOLO ARCHIVOS
    =============================================*/
    public function verificarArchivosController() {
        return DatosDiagnostico::verificarArchivosModel();
    }
    
    /*=============================================
    VERIFICAR SOLO CONFIGURACION IMPRESORAS
    =============================================*/
    public function verificarConfiguracionImpresorasController() {
        return DatosDiagnostico::verificarConfiguracionImpresorasModel();
    }
    
    /*=============================================
    GENERAR REPORTE HTML
    =============================================*/
    public function generarReporteHtmlController() {
        
        $diagnostico = $this->ejecutarDiagnosticoController();
        
        $html = '<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnóstico del Sistema Macarena</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .status-success { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-error { color: #dc3545; }
        .card { border: 1px solid #ddd; border-radius: 5px; margin: 10px 0; padding: 15px; }
        .card-success { border-left: 4px solid #28a745; }
        .card-warning { border-left: 4px solid #ffc107; }
        .card-error { border-left: 4px solid #dc3545; }
        .icon { font-size: 20px; margin-right: 10px; }
        .details { margin-top: 10px; font-size: 14px; color: #666; }
        .summary { background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Diagnóstico del Sistema Macarena</h1>
            <p>Generado el: ' . $diagnostico['timestamp'] . '</p>
        </div>';
        
        // Estado general
        $estadoGeneral = $diagnostico['estado_general'];
        $statusClass = 'status-' . $estadoGeneral['status'];
        
        $html .= '<div class="summary">
            <h2 class="' . $statusClass . '">Estado General: ' . $estadoGeneral['message'] . '</h2>
            <p>✅ Exitosos: ' . $estadoGeneral['exitosos'] . ' | ⚠️ Advertencias: ' . $estadoGeneral['advertencias'] . ' | ❌ Errores: ' . $estadoGeneral['errores'] . '</p>
        </div>';
        
        // Detalles de cada verificación
        $html .= $this->generarSeccionHtmlController("Conexión a Base de Datos", $diagnostico['detalles']['conexion']);
        $html .= $this->generarSeccionHtmlController("Estructura Tabla Pedidos", $diagnostico['detalles']['estructura_pedidos']);
        $html .= $this->generarSeccionHtmlController("Tabla Impresoras", $diagnostico['detalles']['tabla_impresoras']);
        
        // Tablas historial
        foreach ($diagnostico['detalles']['tablas_historial'] as $tabla => $resultado) {
            $html .= $this->generarSeccionHtmlController("Tabla " . ucfirst($tabla), $resultado);
        }
        
        $html .= $this->generarSeccionHtmlController("Triggers", $diagnostico['detalles']['triggers']);
        $html .= $this->generarSeccionHtmlController("Configuración Impresoras", $diagnostico['detalles']['configuracion_impresoras']);
        $html .= $this->generarSeccionHtmlController("Datos del Sistema", $diagnostico['detalles']['datos_prueba']);

        // Archivos
        $html .= '<h3>📁 Archivos del Sistema</h3>';
        foreach ($diagnostico['detalles']['archivos'] as $archivo => $resultado) {
            $html .= $this->generarSeccionHtmlController(basename($archivo), $resultado);
        }
        
        $html .= '</div></body></html>';
        
        return $html;
    }
    
    /*=============================================
    GENERAR SECCION HTML
    =============================================*/
    private function generarSeccionHtmlController($titulo, $resultado) {

        $cardClass = 'card-' . $resultado['status'];
        $icon = $resultado['status'] == 'success' ? '✅' : ($resultado['status'] == 'warning' ? '⚠️' : '❌');

        $html = '<div class="card ' . $cardClass . '">
            <h4><span class="icon">' . $icon . '</span>' . $titulo . '</h4>
            <p>' . $resultado['message'] . '</p>';

        // Agregar detalles si existen
        if (isset($resultado['columnas']) && is_array($resultado['columnas'])) {
            $html .= '<div class="details">Columnas: ' . implode(', ', $resultado['columnas']) . '</div>';
        }

        if (isset($resultado['total_impresoras'])) {
            $html .= '<div class="details">Total impresoras configuradas: ' . $resultado['total_impresoras'] . '</div>';
        }

        if (isset($resultado['size'])) {
            $html .= '<div class="details">Tamaño: ' . number_format($resultado['size'] / 1024, 2) . ' KB</div>';
        }

        if (isset($resultado['total_pedidos'])) {
            $html .= '<div class="details">Pedidos: ' . $resultado['total_pedidos'] . ' | Productos: ' . $resultado['total_productos'] . ' | Mesas: ' . $resultado['total_mesas'] . '</div>';
        }

        $html .= '</div>';

        return $html;
    }

    /*=============================================
    OBTENER INFORMACION DE SESION
    =============================================*/
    public function obtenerInformacionSesionController() {

        session_start();

        return array(
            "usuario_activo" => isset($_SESSION["usuario"]),
            "usuario" => isset($_SESSION["usuario"]) ? $_SESSION["usuario"] : 'No definido',
            "tipo_usuario" => isset($_SESSION["tipo_usuario"]) ? $_SESSION["tipo_usuario"] : 'No definido',
            "mesa" => isset($_SESSION["mesa"]) ? $_SESSION["mesa"] : 'No definida'
        );
    }

    /*=============================================
    OBTENER INFORMACION DEL SISTEMA
    =============================================*/
    public function obtenerInformacionSistemaController() {

        return array(
            "php_version" => phpversion(),
            "servidor" => $_SERVER['SERVER_SOFTWARE'],
            "fecha_hora" => date('Y-m-d H:i:s')
        );
    }

    /*=============================================
    PROBAR CONECTIVIDAD CON IMPRESORAS
    =============================================*/
    public function probarConectividadImpresorasController() {

        $configImpresoras = DatosDiagnostico::verificarConfiguracionImpresorasModel();
        $resultados = array();

        if ($configImpresoras['status'] == 'success' && isset($configImpresoras['impresoras'])) {
            foreach ($configImpresoras['impresoras'] as $imp) {
                $fp = @fsockopen($imp['ip'], $imp['puerto'], $errno, $errstr, 3);

                if ($fp) {
                    $resultados[] = array(
                        "categoria" => $imp['categoria'],
                        "ip" => $imp['ip'],
                        "puerto" => $imp['puerto'],
                        "conectada" => true,
                        "mensaje" => "Conectada"
                    );
                    fclose($fp);
                } else {
                    $resultados[] = array(
                        "categoria" => $imp['categoria'],
                        "ip" => $imp['ip'],
                        "puerto" => $imp['puerto'],
                        "conectada" => false,
                        "mensaje" => "Error - $errstr ($errno)"
                    );
                }
            }
        }

        return $resultados;
    }
}
