<?php
// AJAX simplificado para obtener pedidos de mesa
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Iniciar sesión si no está iniciada
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Cargar conexión
require_once "../../models/conexion.php";

// Log de inicio
error_log("AJAX Simple: Iniciando obtención de pedidos");

if(isset($_POST['obtener_pedidos_mesa'])) {
    
    try {
        $mesaId = $_POST['mesa_id'];
        error_log("AJAX Simple: Mesa ID recibido: " . $mesaId);
        
        // Consulta directa a la base de datos
        $db = Conexion::conectar();
        
        $sql = "
            SELECT p.id, p.numero_pedido, p.estado, p.fecha_pedido, p.fecha_envio, 
                   p.fecha_entrega, m.numero as mesa_numero, 
                   COALESCE(per.nombre, 'Sin asignar') as mesero_nombre,
                   (SELECT COUNT(*) FROM producto_vendido_mesa pvm WHERE pvm.pedidos_id = p.id) as total_productos
            FROM pedidos p
            LEFT JOIN mesas m ON p.mesa_id = m.id
            LEFT JOIN personas per ON p.mesero_id = per.id
            WHERE p.mesa_id = ?
            ORDER BY p.fecha_pedido DESC
        ";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([$mesaId]);
        $pedidos = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        error_log("AJAX Simple: Pedidos encontrados: " . count($pedidos));
        error_log("AJAX Simple: Datos: " . print_r($pedidos, true));
        
        // Devolver los pedidos
        header('Content-Type: application/json');
        echo json_encode($pedidos);
        
    } catch (Exception $e) {
        error_log("AJAX Simple: Error: " . $e->getMessage());
        header('Content-Type: application/json');
        echo json_encode(array('error' => $e->getMessage(), 'pedidos' => []));
    }
    exit;
}

// Si no es la acción correcta
header('Content-Type: application/json');
echo json_encode(array('error' => 'Acción no válida', 'post_data' => $_POST));
?>
