<?php
// Herramienta para actualizar configuración de impresoras
require_once "../../models/conexion.php";

echo "<h1>🔧 Actualizar Configuración de Impresoras</h1>";
echo "<p><strong>Fecha/Hora:</strong> " . date('Y-m-d H:i:s') . "</p>";

// Procesar formulario si se envió
if ($_POST) {
    try {
        $db = Conexion::conectar();
        
        if (isset($_POST['actualizar'])) {
            $categoria = $_POST['categoria'];
            $ip = $_POST['ip'];
            $puerto = $_POST['puerto'];
            
            $stmt = $db->prepare("UPDATE impresoras SET ip = ?, puerto = ? WHERE categoria = ?");
            $resultado = $stmt->execute([$ip, $puerto, $categoria]);
            
            if ($resultado) {
                echo "<div style='background-color: #d4edda; color: #155724; padding: 10px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 10px 0;'>";
                echo "✅ <strong>Impresora actualizada exitosamente:</strong> $categoria -> $ip:$puerto";
                echo "</div>";
            } else {
                echo "<div style='background-color: #f8d7da; color: #721c24; padding: 10px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 10px 0;'>";
                echo "❌ <strong>Error actualizando impresora</strong>";
                echo "</div>";
            }
        }
        
        if (isset($_POST['test_ip'])) {
            $ip_test = $_POST['ip_test'];
            $puerto_test = $_POST['puerto_test'] ?: 9100;
            
            echo "<h3>🔍 Resultado del Test de Conectividad:</h3>";
            echo "<div style='border: 1px solid #ccc; padding: 15px; margin: 10px 0;'>";
            echo "<p><strong>IP:</strong> $ip_test</p>";
            echo "<p><strong>Puerto:</strong> $puerto_test</p>";
            
            $start_time = microtime(true);
            $fp = @fsockopen($ip_test, $puerto_test, $errno, $errstr, 10);
            $end_time = microtime(true);
            $tiempo = round(($end_time - $start_time) * 1000, 2);
            
            if ($fp) {
                echo "<p>✅ <strong>CONECTADA</strong> - Tiempo: {$tiempo}ms</p>";
                fclose($fp);
                
                // Intentar enviar comando de prueba
                $fp2 = @fsockopen($ip_test, $puerto_test, $errno2, $errstr2, 5);
                if ($fp2) {
                    $test_command = chr(27) . chr(64) . "TEST CONEXION\n" . date('Y-m-d H:i:s') . "\n\n\n";
                    $enviado = fwrite($fp2, $test_command);
                    fclose($fp2);
                    
                    if ($enviado) {
                        echo "<p>📄 <strong>Comando de prueba enviado</strong> - Bytes: $enviado</p>";
                        echo "<p>💡 <strong>Esta IP está lista para usar</strong></p>";
                    }
                }
            } else {
                echo "<p>❌ <strong>NO CONECTADA</strong> - Error: $errstr ($errno)</p>";
                echo "<p>⏱️ Tiempo: {$tiempo}ms</p>";
                
                if ($errno == 111) {
                    echo "<p>🔍 <strong>Connection Refused:</strong> La impresora rechaza la conexión</p>";
                } elseif ($errno == 110) {
                    echo "<p>⏰ <strong>Timeout:</strong> No hay respuesta de la IP</p>";
                }
            }
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div style='background-color: #f8d7da; color: #721c24; padding: 10px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 10px 0;'>";
        echo "❌ <strong>Error:</strong> " . $e->getMessage();
        echo "</div>";
    }
}

// Obtener configuración actual
try {
    $db = Conexion::conectar();
    $stmt = $db->prepare("SELECT * FROM impresoras ORDER BY categoria");
    $stmt->execute();
    $impresoras = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>📋 Configuración Actual:</h2>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin-bottom: 20px;'>";
    echo "<tr style='background-color: #f8f9fa;'><th>Categoría</th><th>IP</th><th>Puerto</th><th>Estado</th></tr>";
    
    foreach ($impresoras as $imp) {
        // Test rápido de conectividad
        $fp = @fsockopen($imp['ip'], $imp['puerto'], $errno, $errstr, 3);
        $estado = $fp ? "✅ Conectada" : "❌ No conectada";
        if ($fp) fclose($fp);
        
        echo "<tr>";
        echo "<td><strong>" . strtoupper($imp['categoria']) . "</strong></td>";
        echo "<td>" . $imp['ip'] . "</td>";
        echo "<td>" . $imp['puerto'] . "</td>";
        echo "<td>$estado</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p>❌ Error obteniendo configuración: " . $e->getMessage() . "</p>";
}
?>

<h2>🔧 Actualizar Impresora:</h2>
<form method="POST" style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 10px 0;">
    <div style="margin-bottom: 15px;">
        <label><strong>Categoría:</strong></label><br>
        <select name="categoria" required style="padding: 5px; width: 200px;">
            <option value="bar">BAR</option>
            <option value="cocina">COCINA</option>
            <option value="asados">ASADOS</option>
        </select>
    </div>
    
    <div style="margin-bottom: 15px;">
        <label><strong>Nueva IP:</strong></label><br>
        <input type="text" name="ip" placeholder="192.168.68.XXX" required style="padding: 5px; width: 200px;">
    </div>
    
    <div style="margin-bottom: 15px;">
        <label><strong>Puerto:</strong></label><br>
        <input type="number" name="puerto" value="9100" required style="padding: 5px; width: 200px;">
    </div>
    
    <button type="submit" name="actualizar" style="background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
        🔄 Actualizar Impresora
    </button>
</form>

<h2>🧪 Test de IP:</h2>
<form method="POST" style="background-color: #e7f3ff; padding: 20px; border-radius: 5px; margin: 10px 0;">
    <div style="margin-bottom: 15px;">
        <label><strong>IP a probar:</strong></label><br>
        <input type="text" name="ip_test" placeholder="192.168.68.XXX" required style="padding: 5px; width: 200px;">
    </div>
    
    <div style="margin-bottom: 15px;">
        <label><strong>Puerto:</strong></label><br>
        <input type="number" name="puerto_test" value="9100" style="padding: 5px; width: 200px;">
    </div>
    
    <button type="submit" name="test_ip" style="background-color: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
        🔍 Probar Conectividad
    </button>
</form>

<h2>💡 Instrucciones para encontrar las IPs correctas:</h2>
<div style="background-color: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 10px 0;">
    <h4>Desde tu computadora (IP: **************):</h4>
    <ol>
        <li><strong>Escanear la red:</strong>
            <ul>
                <li>Abrir CMD como administrador</li>
                <li>Ejecutar: <code>for /l %i in (1,1,254) do @ping -n 1 -w 100 192.168.68.%i | find "TTL"</code></li>
                <li>Esto mostrará todas las IPs activas en tu red</li>
            </ul>
        </li>
        
        <li><strong>Verificar puerto 9100 en cada IP:</strong>
            <ul>
                <li>Para cada IP encontrada, ejecutar: <code>telnet [IP] 9100</code></li>
                <li>Si se conecta, esa es una impresora</li>
                <li>Ejemplo: <code>telnet ************** 9100</code></li>
            </ul>
        </li>
        
        <li><strong>Identificar cada impresora:</strong>
            <ul>
                <li>Enviar un comando de prueba para identificar la ubicación</li>
                <li>O revisar físicamente cada impresora</li>
            </ul>
        </li>
    </ol>
    
    <h4>IPs actuales configuradas:</h4>
    <ul>
        <li><strong>BAR:</strong> **************:9100</li>
        <li><strong>COCINA:</strong> **************:9100</li>
        <li><strong>ASADOS:</strong> **************:9100</li>
    </ul>
</div>

<p><a href="diagnostico" style="background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">← Volver al Diagnóstico</a></p>
