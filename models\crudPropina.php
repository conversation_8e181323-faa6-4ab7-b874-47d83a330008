<?php

	#EXTENSIÓN DE CLASES: Los objetos pueden ser extendidos, y pueden heredar propiedades y métodos. Para definir una clase como extensión, debo definir una clase padre, y se utiliza dentro de una clase hija.

	require_once "conexion.php";

	class DatosPropina extends Conexion
		{
			
			##PROPINA 
			#-------------------------------------------------
				#REGISTRO DE PROPINA SELECT id, SELECT id, venta_id, personas_id, cantidad, fecha FROM propinas FROM propinas
				#-------------------------------------
					public static function registroPropinaModel($datosModel, $tabla){
								echo "<script>alert('Entro CRUD ".$datosModel['fecha_hora']." no')</script>";	
						$consulta="INSERT INTO $tabla (SELECT venta_id, personas_id, cantidad, fecha FROM propinas) VALUES (SELECT :venta_id, :personas_id, :cantidad, :fecha FROM propinas)";
						//echo "<script>alert('Entro CRUD ".$consulta." no')</script>";	
						$stmt = Conexion::conectar()->prepare($consulta);
						$stmt->execute();			
						$stmt->bindParam(":venta_id", $datosModel['venta_id'], PDO::PARAM_INT);
						$stmt->bindParam(":personas_id", $datosModel['personas_id'], PDO::PARAM_INT);
						$stmt->bindParam(":cantidad", $datosModel['cantidad'], PDO::PARAM_INT);
						$stmt->bindParam(":fecha", $datosModel['fecha'], PDO::PARAM_STR);
						//echo "<script>alert('Guardo')</script>";			

						if($stmt->execute())
							{	return "success";	}
						else{ return "error";	}
						$stmt->close();
					}

				#VISTA PROPINA 
				#-------------------------------------

					public static function vistaPropinaModel($tabla)
						{
							$consulta="SELECT id, venta_id, personas_id, cantidad, fecha FROM $tabla";
							$stmt = Conexion::conectar()->prepare($consulta);	
							$stmt->execute();
							
							return $stmt->fetchAll();
							$stmt->close();
						}

				#EDITAR PROPINA 
				#-------------------------------------

					public static function editarPropinaModel($datosModel, $tabla)
						{
							$stmt = Conexion::conectar()->prepare("SELECT id, venta_id, personas_id, cantidad, fecha FROM $tabla WHERE id = :id");
							$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);	
							$stmt->execute();
							return $stmt->fetch();
							$stmt->close();
						}

				#ACTUALIZAR PROPINA 
				#-------------------------------------

					public static function actualizarPropinaModel($datosModel, $tabla)
						{ echo "<script>alert('Entro Actualizar Producto')</script>";	
							$stmt = Conexion::conectar()->prepare("UPDATE $tabla SET venta_id = :venta_id, personas_id =:personas_id, cantidad =:cantidad, fecha =:fecha WHERE id = :id");			
							$stmt->bindParam(":venta_id", $datosModel["venta_id"], PDO::PARAM_INT);
							$stmt->bindParam(":personas_id", $datosModel["personas_id"], PDO::PARAM_INT);
							$stmt->bindParam(":fecha_hora", $datosModel["fecha_hora"], PDO::PARAM_INT);
							$stmt->bindParam(":fecha", $datosModel["fecha"], PDO::PARAM_STR);
							$stmt->bindParam(":id", $datosModel["id"], PDO::PARAM_INT);
							if($stmt->execute())
								{echo "<script>alert('Guardo Actualizar Producto')</script>";return "success";	}
							else{	return "error";			}
							$stmt->close();
						}

				#BORRAR PROPINA 
				#------------------------------------
					public static function borrarPropinaModel($datosModel, $tabla)
					{
						$stmt = Conexion::conectar()->prepare("DELETE FROM $tabla WHERE id = :id");
						$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);
						if($stmt->execute())
							{	return "success";	}
						else{	return "error";		}
						$stmt->close();
					}			
			#--------------------------------------------------

		}