<!DOCTYPE html>
<html>
<head>
    <title>🧪 Test Validación de Pagos - Mesa</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1000px; margin: 0 auto; }
        .card { background: #f8f9fa; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #007bff; }
        .success { border-left-color: #28a745; background: #d4edda; }
        .warning { border-left-color: #ffc107; background: #fff3cd; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .btn { padding: 15px 30px; margin: 10px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold; font-size: 16px; cursor: pointer; border: none; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        h1 { color: #007bff; text-align: center; }
        h2 { color: #495057; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .form-group { margin: 15px 0; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input { width: 200px; padding: 8px; border: 1px solid #ccc; border-radius: 3px; }
        .result { background: white; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; margin: 10px 0; min-height: 50px; }
    </style>
</head>
<body>

<div class="container">
    <h1>🧪 Test Validación de Pagos - Mesa</h1>
    <p style="text-align: center; font-size: 18px; color: #6c757d;">
        Verificación de las correcciones en registroPmesa.php
    </p>

    <div class="card success">
        <h2>✅ Correcciones Implementadas</h2>
        <p>He implementado las siguientes correcciones en <code>registroPmesa.php</code>:</p>
        
        <ul>
            <li>✅ <strong>Validación de Pagos:</strong> Verifica que la suma de las 5 formas de pago sea ≥ al total</li>
            <li>✅ <strong>Mensaje Detallado:</strong> Muestra exactamente cuánto falta y en qué formas de pago</li>
            <li>✅ <strong>Confirmación Completa:</strong> Muestra resumen de pagos y cambio antes de facturar</li>
            <li>✅ <strong>Impresión Automática:</strong> Ejecuta scripts de la respuesta AJAX y abre PDF</li>
            <li>✅ <strong>Respaldo de Impresión:</strong> Si falla el script, abre PDF automáticamente</li>
        </ul>
    </div>

    <div class="card">
        <h2>🧪 Simulador de Validación</h2>
        <p>Prueba la lógica de validación de pagos aquí:</p>
        
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div>
                <h4>💰 Total a Pagar</h4>
                <div class="form-group">
                    <label>Total:</label>
                    <input type="number" id="total_test" value="50000" onchange="calcularValidacion()">
                </div>
            </div>
            
            <div>
                <h4>💳 Formas de Pago</h4>
                <div class="form-group">
                    <label>Efectivo:</label>
                    <input type="number" id="efectivo_test" value="0" onchange="calcularValidacion()">
                </div>
                <div class="form-group">
                    <label>Tarjeta:</label>
                    <input type="number" id="tarjeta_test" value="0" onchange="calcularValidacion()">
                </div>
                <div class="form-group">
                    <label>Nequi:</label>
                    <input type="number" id="nequi_test" value="0" onchange="calcularValidacion()">
                </div>
                <div class="form-group">
                    <label>Daviplata:</label>
                    <input type="number" id="daviplata_test" value="0" onchange="calcularValidacion()">
                </div>
                <div class="form-group">
                    <label>Bancolombia:</label>
                    <input type="number" id="bancolombia_test" value="0" onchange="calcularValidacion()">
                </div>
            </div>
        </div>
        
        <button class="btn btn-primary" onclick="testValidacion()">🧪 Test Validación</button>
        <button class="btn btn-warning" onclick="llenarEjemplo1()">📝 Ejemplo: Pago Insuficiente</button>
        <button class="btn btn-success" onclick="llenarEjemplo2()">📝 Ejemplo: Pago Correcto</button>
        
        <div id="resultado_validacion" class="result">
            Los resultados aparecerán aquí...
        </div>
    </div>

    <div class="card">
        <h2>🖨️ Test de Impresión</h2>
        <p>Verifica que la impresión automática funcione:</p>
        
        <button class="btn btn-primary" onclick="testImpresionScript()">🔧 Test Script de Impresión</button>
        <button class="btn btn-success" onclick="testPDFDirecto()">📄 Test PDF Directo</button>
        
        <div id="resultado_impresion" class="result">
            Los resultados de impresión aparecerán aquí...
        </div>
    </div>

    <div class="card warning">
        <h2>📋 Casos de Prueba</h2>
        <p>Prueba estos escenarios en una mesa real:</p>
        
        <h4>Caso 1: Pago Insuficiente</h4>
        <ul>
            <li>Total: $50,000</li>
            <li>Efectivo: $30,000</li>
            <li>Tarjeta: $15,000</li>
            <li><strong>Resultado esperado:</strong> Error - Falta $5,000</li>
        </ul>
        
        <h4>Caso 2: Pago Exacto</h4>
        <ul>
            <li>Total: $50,000</li>
            <li>Efectivo: $30,000</li>
            <li>Nequi: $20,000</li>
            <li><strong>Resultado esperado:</strong> Éxito - Cambio $0</li>
        </ul>
        
        <h4>Caso 3: Pago con Cambio</h4>
        <ul>
            <li>Total: $50,000</li>
            <li>Efectivo: $60,000</li>
            <li><strong>Resultado esperado:</strong> Éxito - Cambio $10,000</li>
        </ul>
    </div>

    <div class="card">
        <h2>🔗 Enlaces de Prueba</h2>
        <p>Usa estos enlaces para probar en el sistema real:</p>
        
        <a href="../../index.php?action=registroPmesa&ida=5" class="btn btn-primary" target="_blank">
            🏠 Mesa 5 (Test)
        </a>
        
        <a href="../../index.php?action=mesa" class="btn btn-success" target="_blank">
            📋 Lista de Mesas
        </a>
        
        <a href="test_impresion_factura.php" class="btn btn-warning" target="_blank">
            🖨️ Test de Impresión
        </a>
    </div>

</div>

<script>
function calcularValidacion() {
    const total = parseFloat(document.getElementById('total_test').value) || 0;
    const efectivo = parseFloat(document.getElementById('efectivo_test').value) || 0;
    const tarjeta = parseFloat(document.getElementById('tarjeta_test').value) || 0;
    const nequi = parseFloat(document.getElementById('nequi_test').value) || 0;
    const daviplata = parseFloat(document.getElementById('daviplata_test').value) || 0;
    const bancolombia = parseFloat(document.getElementById('bancolombia_test').value) || 0;
    
    const totalPagado = efectivo + tarjeta + nequi + daviplata + bancolombia;
    const diferencia = totalPagado - total;
    
    let html = '<h4>📊 Cálculo Automático:</h4>';
    html += '<p><strong>Total a pagar:</strong> $' + total.toLocaleString() + '</p>';
    html += '<p><strong>Total pagado:</strong> $' + totalPagado.toLocaleString() + '</p>';
    
    if (diferencia < 0) {
        html += '<p style="color: red;"><strong>Falta:</strong> $' + Math.abs(diferencia).toLocaleString() + '</p>';
        html += '<p style="color: red;">❌ Pago insuficiente</p>';
    } else if (diferencia === 0) {
        html += '<p style="color: green;"><strong>Cambio:</strong> $0</p>';
        html += '<p style="color: green;">✅ Pago exacto</p>';
    } else {
        html += '<p style="color: blue;"><strong>Cambio:</strong> $' + diferencia.toLocaleString() + '</p>';
        html += '<p style="color: green;">✅ Pago correcto con cambio</p>';
    }
    
    document.getElementById('resultado_validacion').innerHTML = html;
}

function testValidacion() {
    const total = parseFloat(document.getElementById('total_test').value) || 0;
    const efectivo = parseFloat(document.getElementById('efectivo_test').value) || 0;
    const tarjeta = parseFloat(document.getElementById('tarjeta_test').value) || 0;
    const nequi = parseFloat(document.getElementById('nequi_test').value) || 0;
    const daviplata = parseFloat(document.getElementById('daviplata_test').value) || 0;
    const bancolombia = parseFloat(document.getElementById('bancolombia_test').value) || 0;
    
    // Simular la lógica exacta de registroPmesa.php
    let totalPagado = efectivo + tarjeta + nequi + daviplata + bancolombia;
    
    let html = '<h4>🧪 Resultado del Test:</h4>';
    
    if (totalPagado < total) {
        let faltante = total - totalPagado;
        html += '<div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;">';
        html += '<strong>❌ Pago insuficiente</strong><br><br>';
        html += 'Total a pagar: $' + total.toLocaleString() + '<br>';
        html += 'Total pagado: $' + totalPagado.toLocaleString() + '<br>';
        html += 'Falta: $' + faltante.toLocaleString() + '<br><br>';
        html += 'Formas de pago:<br>';
        html += '• Efectivo: $' + efectivo.toLocaleString() + '<br>';
        html += '• Tarjeta: $' + tarjeta.toLocaleString() + '<br>';
        html += '• Nequi: $' + nequi.toLocaleString() + '<br>';
        html += '• Daviplata: $' + daviplata.toLocaleString() + '<br>';
        html += '• Bancolombia: $' + bancolombia.toLocaleString();
        html += '</div>';
    } else {
        let cambio = totalPagado - total;
        html += '<div style="background: #d4edda; color: #155724; padding: 15px; border-radius: 5px;">';
        html += '<strong>✅ Pago válido</strong><br><br>';
        html += 'Total: $' + total.toLocaleString() + '<br>';
        html += 'Pagado: $' + totalPagado.toLocaleString() + '<br>';
        html += 'Cambio: $' + cambio.toLocaleString() + '<br><br>';
        html += 'Formas de pago activas:<br>';
        if (efectivo > 0) html += '• Efectivo: $' + efectivo.toLocaleString() + '<br>';
        if (tarjeta > 0) html += '• Tarjeta: $' + tarjeta.toLocaleString() + '<br>';
        if (nequi > 0) html += '• Nequi: $' + nequi.toLocaleString() + '<br>';
        if (daviplata > 0) html += '• Daviplata: $' + daviplata.toLocaleString() + '<br>';
        if (bancolombia > 0) html += '• Bancolombia: $' + bancolombia.toLocaleString() + '<br>';
        html += '</div>';
    }
    
    document.getElementById('resultado_validacion').innerHTML = html;
}

function llenarEjemplo1() {
    // Ejemplo de pago insuficiente
    document.getElementById('total_test').value = 50000;
    document.getElementById('efectivo_test').value = 30000;
    document.getElementById('tarjeta_test').value = 15000;
    document.getElementById('nequi_test').value = 0;
    document.getElementById('daviplata_test').value = 0;
    document.getElementById('bancolombia_test').value = 0;
    calcularValidacion();
}

function llenarEjemplo2() {
    // Ejemplo de pago correcto
    document.getElementById('total_test').value = 50000;
    document.getElementById('efectivo_test').value = 30000;
    document.getElementById('tarjeta_test').value = 0;
    document.getElementById('nequi_test').value = 25000;
    document.getElementById('daviplata_test').value = 0;
    document.getElementById('bancolombia_test').value = 0;
    calcularValidacion();
}

function testImpresionScript() {
    document.getElementById('resultado_impresion').innerHTML = '⏳ Probando script de impresión...';
    
    // Simular la respuesta que viene del servidor
    const respuestaSimulada = '<script>alert("✅ Facturación completada exitosamente\\n\\nFactura: 123");window.open("views/modules/pdf.php","_blank");</script>success_corregida_mesa_5';
    
    // Ejecutar el script como lo hace registroPmesa.php
    const scriptMatch = respuestaSimulada.match(/<script>(.*?)<\/script>/);
    if (scriptMatch) {
        console.log('🖨️ Ejecutando script de impresión:', scriptMatch[1]);
        eval(scriptMatch[1]);
        
        document.getElementById('resultado_impresion').innerHTML = 
            '<div style="background: #d4edda; color: #155724; padding: 15px; border-radius: 5px;">' +
            '<strong>✅ Script ejecutado correctamente</strong><br>' +
            'Se debería haber abierto el PDF automáticamente' +
            '</div>';
    } else {
        document.getElementById('resultado_impresion').innerHTML = 
            '<div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;">' +
            '❌ No se encontró script en la respuesta' +
            '</div>';
    }
}

function testPDFDirecto() {
    document.getElementById('resultado_impresion').innerHTML = '📄 Abriendo PDF directo...';
    
    window.open('views/modules/pdf.php', '_blank');
    
    document.getElementById('resultado_impresion').innerHTML = 
        '<div style="background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px;">' +
        '<strong>📄 PDF abierto directamente</strong><br>' +
        'Verifica que se abra en nueva ventana' +
        '</div>';
}

// Calcular automáticamente al cargar
document.addEventListener('DOMContentLoaded', function() {
    calcularValidacion();
});
</script>

</body>
</html>
