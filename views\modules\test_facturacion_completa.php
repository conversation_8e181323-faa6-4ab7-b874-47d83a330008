<?php
require_once '../../models/conexion.php';

$mesa = $_GET['mesa'] ?? 2;
$total = $_GET['total'] ?? 0;

echo "<h2>🧪 Test: Facturación Completa - Mesa $mesa</h2>";

try {
    $db = Conexion::conectar();

    // 1. Verificar estado antes de facturar
    echo "<h3>📋 Estado Antes de Facturar:</h3>";

    $stmt = $db->prepare("SELECT * FROM pedidos WHERE mesa_id = ? ORDER BY fecha_pedido DESC");
    $stmt->execute([$mesa]);
    $pedidos = $stmt->fetchAll();
    if (!empty($pedidos)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Número</th><th>Estado</th><th>Facturado</th></tr>";

        foreach ($pedidos as $pedido) {
            $facturado = ($pedido['facturado'] == 's') ? '✅ SÍ' : '❌ NO';
            echo "<tr>";
            echo "<td>{$pedido['id']}</td>";
            echo "<td>{$pedido['numero_pedido']}</td>";
            echo "<td>{$pedido['estado']}</td>";
            echo "<td>$facturado</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No hay pedidos en esta mesa</p>";
    }

    // 2. Calcular total actual usando SQL directo
    $stmt = $db->prepare("
        SELECT pr.nombre, pvm.cantidad, pr.precio,
               (pvm.cantidad * pr.precio) as subtotal
        FROM productos pr
        JOIN producto_vendido_mesa pvm ON pr.id = pvm.productos_id
        JOIN pedidos p ON pvm.pedidos_id = p.id
        WHERE pvm.mesas_id = ? AND p.estado != 'cancelado'
    ");
    $stmt->execute([$mesa]);
    $productosFacturables = $stmt->fetchAll();

    $totalCalculado = 0;
    foreach ($productosFacturables as $producto) {
        $totalCalculado += $producto['subtotal'];
    }

    echo "<p><strong>💰 Total Calculado: \${$totalCalculado}</strong></p>";
    echo "<p><strong>💰 Total Recibido: \${$total}</strong></p>";

    if ($total != $totalCalculado) {
        echo "<p style='color: orange;'>⚠️ Los totales no coinciden. Usando total calculado.</p>";
        $total = $totalCalculado;
    }

    // 3. Simular proceso de facturación
    echo "<h3>🧾 Procesando Facturación...</h3>";

    if ($total > 0) {
        // Simular marcado como facturado
        echo "<div style='background: #e7f3ff; padding: 15px; border: 1px solid #007bff; margin: 10px 0;'>";
        echo "<h4>📄 FACTURA GENERADA</h4>";
        echo "<p><strong>Número de Factura:</strong> F" . date('Ymd') . "-" . str_pad($mesa, 3, '0', STR_PAD_LEFT) . "</p>";
        echo "<p><strong>Fecha:</strong> " . date('Y-m-d H:i:s') . "</p>";
        echo "<p><strong>Mesa:</strong> $mesa</p>";
        echo "<hr>";

        // Mostrar productos facturados
        foreach ($productosFacturables as $producto) {
            echo "<p>{$producto['nombre']} x{$producto['cantidad']} - \${$producto['precio']} = \${$producto['subtotal']}</p>";
        }

        echo "<hr>";
        echo "<p><strong>TOTAL FACTURADO: \${$total}</strong></p>";
        echo "</div>";

        // Simular actualización de estado
        echo "<h4>🔄 Actualizando Estados...</h4>";

        // Aquí simularíamos la actualización real de la base de datos
        echo "<p>✅ Pedidos marcados como facturados</p>";
        echo "<p>✅ Mesa liberada para nuevos pedidos</p>";
        echo "<p>✅ Factura registrada en el sistema</p>";

        // Mostrar estado después de facturar
        echo "<h3>📋 Estado Después de Facturar:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Número</th><th>Estado</th><th>Facturado</th></tr>";

        foreach ($pedidos as $pedido) {
            if ($pedido['estado'] != 'cancelado') {
                echo "<tr style='background-color: #d4edda;'>";
                echo "<td>{$pedido['id']}</td>";
                echo "<td>{$pedido['numero_pedido']}</td>";
                echo "<td>facturado</td>";
                echo "<td>✅ SÍ</td>";
                echo "</tr>";
            } else {
                echo "<tr>";
                echo "<td>{$pedido['id']}</td>";
                echo "<td>{$pedido['numero_pedido']}</td>";
                echo "<td>cancelado</td>";
                echo "<td>❌ NO</td>";
                echo "</tr>";
            }
        }
        echo "</table>";

        echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #28a745; margin: 10px 0;'>";
        echo "<h4>✅ FACTURACIÓN COMPLETADA EXITOSAMENTE</h4>";
        echo "<p>La mesa $mesa ha sido facturada por un total de \${$total}</p>";
        echo "<p>Todos los pedidos han sido marcados como facturados</p>";
        echo "<p>La mesa está lista para nuevos clientes</p>";
        echo "</div>";

    } else {
        echo "<p>❌ No se puede facturar sin productos</p>";
    }

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage();
}

echo "<hr>";
echo "<h3>🔄 Navegación</h3>";
echo "<a href='test_facturacion.php?mesa=$mesa' class='btn btn-primary'>Ver Diagnóstico Final</a>";
echo " | ";
echo "<a href='test_prefacturacion.php?mesa=$mesa' class='btn btn-warning'>Volver a Prefacturación</a>";
echo " | ";
echo "<a href='../../index.php?action=registroPmesa&ida=$mesa' class='btn btn-info'>Ir a Mesa $mesa</a>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}

table {
    border-collapse: collapse;
    width: 100%;
    margin: 10px 0;
}

th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #f0f0f0;
    font-weight: bold;
}

.btn {
    display: inline-block;
    padding: 8px 16px;
    margin: 5px;
    text-decoration: none;
    border-radius: 4px;
    color: white;
}

.btn-primary { background-color: #007bff; }
.btn-success { background-color: #28a745; }
.btn-warning { background-color: #ffc107; color: #212529; }
.btn-info { background-color: #17a2b8; }

h2, h3, h4 { color: #333; }
p { margin: 8px 0; }
hr { margin: 20px 0; }
</style>
