<?php

session_start();

if(!isset($_SESSION["validar"]) || !$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "models/conexion.php";

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Debug: Problemas con Pedidos</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
</head>
<body>

<div class="container">
    <h2>🔧 Debug: Problemas con Pedidos</h2>
    <hr>
    
    <div class="alert alert-warning">
        <h4>⚠️ Problemas Identificados:</h4>
        <ol>
            <li><strong>Al enviar pedido:</strong> Los productos quedan pendientes para el próximo pedido borrador</li>
            <li><strong>Error al facturar:</strong> Column 'usuario_id' cannot be null</li>
            <li><strong>Al cancelar mesa:</strong> No cambia el estado de los pedidos a 'cancelado'</li>
        </ol>
    </div>
    
    <div class="panel panel-danger">
        <div class="panel-heading">
            <h3 class="panel-title">🔍 Problema 1: Productos Quedan Pendientes</h3>
        </div>
        <div class="panel-body">
            <h5>❌ Descripción del problema:</h5>
            <p>Cuando se envía un pedido, los productos no se asocian correctamente al pedido enviado y quedan disponibles para el próximo pedido borrador.</p>
            
            <h5>🔍 Causa probable:</h5>
            <p>La función de envío de pedidos no está actualizando correctamente el campo <code>pedidos_id</code> en la tabla <code>producto_vendido_mesa</code>.</p>
            
            <?php
            try {
                echo "<h5>📊 Verificación de datos:</h5>";
                
                // Buscar mesas con productos
                $stmt = Conexion::conectar()->prepare("
                    SELECT 
                        m.id as mesa_id,
                        m.nombre as mesa_nombre,
                        COUNT(pvm.productos_id) as productos_count,
                        GROUP_CONCAT(DISTINCT p.estado) as estados_pedidos,
                        GROUP_CONCAT(DISTINCT p.id) as pedidos_ids
                    FROM mesas m
                    LEFT JOIN producto_vendido_mesa pvm ON m.id = pvm.mesas_id
                    LEFT JOIN pedidos p ON pvm.pedidos_id = p.id
                    WHERE pvm.productos_id IS NOT NULL
                    GROUP BY m.id, m.nombre
                    ORDER BY productos_count DESC
                    LIMIT 5
                ");
                $stmt->execute();
                $mesas_con_productos = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (count($mesas_con_productos) > 0) {
                    echo "<table class='table table-condensed'>";
                    echo "<thead>";
                    echo "<tr>";
                    echo "<th>Mesa</th>";
                    echo "<th>Productos</th>";
                    echo "<th>Estados Pedidos</th>";
                    echo "<th>IDs Pedidos</th>";
                    echo "<th>Problema</th>";
                    echo "</tr>";
                    echo "</thead>";
                    echo "<tbody>";
                    
                    foreach ($mesas_con_productos as $mesa) {
                        $problema = '';
                        $clase = '';
                        
                        if (strpos($mesa['estados_pedidos'], 'borrador') !== false && strpos($mesa['estados_pedidos'], 'enviado') !== false) {
                            $problema = '⚠️ Productos en borrador y enviado';
                            $clase = 'warning';
                        } elseif ($mesa['estados_pedidos'] == 'borrador') {
                            $problema = '✅ Solo borrador (normal)';
                            $clase = 'info';
                        } elseif ($mesa['estados_pedidos'] == 'enviado') {
                            $problema = '🔄 Solo enviado (normal)';
                            $clase = 'success';
                        } else {
                            $problema = '❓ Estados mixtos';
                            $clase = 'danger';
                        }
                        
                        echo "<tr class='{$clase}'>";
                        echo "<td>{$mesa['mesa_nombre']}</td>";
                        echo "<td>{$mesa['productos_count']}</td>";
                        echo "<td>{$mesa['estados_pedidos']}</td>";
                        echo "<td>{$mesa['pedidos_ids']}</td>";
                        echo "<td>{$problema}</td>";
                        echo "</tr>";
                    }
                    
                    echo "</tbody></table>";
                } else {
                    echo "<div class='alert alert-success'>✅ No hay mesas con productos actualmente</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-warning">
        <div class="panel-heading">
            <h3 class="panel-title">🔍 Problema 2: Error usuario_id NULL</h3>
        </div>
        <div class="panel-body">
            <h5>❌ Error específico:</h5>
            <div class="well">
                <code>SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'usuario_id' cannot be null</code>
            </div>
            
            <h5>🔍 Causa:</h5>
            <p>La tabla <code>pedidos_historial</code> requiere un <code>usuario_id</code> que no se está proporcionando en los triggers automáticos.</p>
            
            <?php
            try {
                echo "<h5>📊 Verificación de triggers:</h5>";
                $stmt_triggers = Conexion::conectar()->prepare("SHOW TRIGGERS LIKE 'pedidos'");
                $stmt_triggers->execute();
                $triggers = $stmt_triggers->fetchAll(PDO::FETCH_ASSOC);
                
                if (count($triggers) > 0) {
                    echo "<table class='table table-condensed'>";
                    echo "<thead><tr><th>Trigger</th><th>Evento</th><th>Timing</th></tr></thead>";
                    echo "<tbody>";
                    foreach ($triggers as $trigger) {
                        echo "<tr>";
                        echo "<td>{$trigger['Trigger']}</td>";
                        echo "<td>{$trigger['Event']}</td>";
                        echo "<td>{$trigger['Timing']}</td>";
                        echo "</tr>";
                    }
                    echo "</tbody></table>";
                } else {
                    echo "<div class='alert alert-info'>No se encontraron triggers</div>";
                }
                
                echo "<h5>📊 Estructura de pedidos_historial:</h5>";
                $stmt_estructura = Conexion::conectar()->prepare("DESCRIBE pedidos_historial");
                $stmt_estructura->execute();
                $campos = $stmt_estructura->fetchAll(PDO::FETCH_ASSOC);
                
                echo "<table class='table table-condensed'>";
                echo "<thead><tr><th>Campo</th><th>Tipo</th><th>Null</th><th>Default</th></tr></thead>";
                echo "<tbody>";
                foreach ($campos as $campo) {
                    $clase = ($campo['Field'] == 'usuario_id' && $campo['Null'] == 'NO') ? 'danger' : '';
                    echo "<tr class='{$clase}'>";
                    echo "<td><strong>{$campo['Field']}</strong></td>";
                    echo "<td>{$campo['Type']}</td>";
                    echo "<td>{$campo['Null']}</td>";
                    echo "<td>{$campo['Default']}</td>";
                    echo "</tr>";
                }
                echo "</tbody></table>";
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">🔍 Problema 3: Cancelación de Mesa</h3>
        </div>
        <div class="panel-body">
            <h5>✅ Estado actual:</h5>
            <p>La función <code>cancelarMesaConPedidosModel()</code> ya está implementada correctamente y SÍ cambia el estado de los pedidos a 'cancelado'.</p>
            
            <?php
            try {
                echo "<h5>📊 Verificación de pedidos cancelados:</h5>";
                $stmt_cancelados = Conexion::conectar()->prepare("
                    SELECT 
                        p.id,
                        p.numero_pedido,
                        p.mesa_id,
                        m.nombre as mesa_nombre,
                        p.estado,
                        p.fecha_entrega as fecha_cancelacion
                    FROM pedidos p
                    LEFT JOIN mesas m ON p.mesa_id = m.id
                    WHERE p.estado = 'cancelado'
                    ORDER BY p.fecha_entrega DESC
                    LIMIT 10
                ");
                $stmt_cancelados->execute();
                $cancelados = $stmt_cancelados->fetchAll(PDO::FETCH_ASSOC);
                
                if (count($cancelados) > 0) {
                    echo "<table class='table table-condensed'>";
                    echo "<thead>";
                    echo "<tr>";
                    echo "<th>Pedido</th>";
                    echo "<th>Mesa</th>";
                    echo "<th>Estado</th>";
                    echo "<th>Fecha Cancelación</th>";
                    echo "</tr>";
                    echo "</thead>";
                    echo "<tbody>";
                    
                    foreach ($cancelados as $cancelado) {
                        echo "<tr class='danger'>";
                        echo "<td>{$cancelado['numero_pedido']}</td>";
                        echo "<td>{$cancelado['mesa_nombre']}</td>";
                        echo "<td><span class='label label-danger'>{$cancelado['estado']}</span></td>";
                        echo "<td><small>{$cancelado['fecha_cancelacion']}</small></td>";
                        echo "</tr>";
                    }
                    
                    echo "</tbody></table>";
                } else {
                    echo "<div class='alert alert-info'>No hay pedidos cancelados recientemente</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-success">
        <div class="panel-heading">
            <h3 class="panel-title">🔧 Soluciones Propuestas</h3>
        </div>
        <div class="panel-body">
            <h5>✅ Solución 1: Corregir Envío de Pedidos</h5>
            <p>Modificar la función de envío para que NO cree un nuevo pedido borrador automáticamente.</p>
            
            <h5>✅ Solución 2: Corregir Trigger de Historial</h5>
            <p>Modificar el trigger <code>pedidos_historial_insert</code> para manejar valores NULL en usuario_id.</p>
            
            <h5>✅ Solución 3: Verificar Cancelación</h5>
            <p>La cancelación ya funciona correctamente. Verificar que se esté usando la función correcta.</p>
            
            <div class="alert alert-warning">
                <h6>⚠️ Acciones Recomendadas:</h6>
                <ol>
                    <li>Corregir el trigger de historial para permitir usuario_id NULL</li>
                    <li>Revisar la lógica de creación de pedidos borrador</li>
                    <li>Verificar que se use la función de cancelación correcta</li>
                </ol>
            </div>
        </div>
    </div>
    
    <hr>
    <div class="row">
        <div class="col-md-3">
            <a href="index.php?action=mesa" class="btn btn-primary btn-block">🪑 Ver Mesas</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=test_facturacion_estados" class="btn btn-warning btn-block">🧪 Test Estados</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=pantallaCocina" class="btn btn-info btn-block">👨‍🍳 Pantalla Cocina</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=diagnostico" class="btn btn-success btn-block">📊 Diagnóstico</a>
        </div>
    </div>
</div>

</body>
</html>
