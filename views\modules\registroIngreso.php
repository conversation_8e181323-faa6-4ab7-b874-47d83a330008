<?php
if ($_SESSION["turno"]==null)
	{
	 echo "<script>alert('Inicie turno')</script>";
	 header("location:registroTurno");
	}
	$registroIngreso = new controllerEgresoIngreso();
	$registroIngreso -> registroIngresoController();
	$puntos = $registroIngreso -> listaPuntosController();
	if(isset($_GET["action"]))
		{	if($_GET["action"] == "okI") 	{	echo "Registro Exitoso";	}		}
?>
<h1>REGISTRO DE INGRESO</h1>
<form method="post">
	<table>
		<thead>
		<tr > <td colspan="2" ></td> </tr>
	</thead>

	<tr>
		<td><label> Motivo : </label></td>
		<td><textarea placeholder="motivo" name="motivoIngresoRegistro" cols="30" rows="5" autofocus="autofocus" required></textarea></td>
	</tr>
	<tr>
		<td><label> Cantidad : </label></td>
		<td><input type="text" placeholder="cantidad" name="cantidadIngresoRegistro" style="width: 15em; " required></td>
	</tr>
	<tr>
		<td><label> Sucursal  : </label></td>
		<td>Principal<input type="hidden" placeholder="cantidad" name="puntos" id="puntos" value="1" style="width: 15em; " required>
			<?php
		# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  puntos  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

				/*if($puntos=="error")
				 {	echo "debe registrar el Sucursal"; }
				else
				 {	echo "<label></label>";
					$result='<select name="puntos"  id="puntos"  required>';
					$result.=' <option value="-1">Sucursal</option>';
					foreach ($puntos as $row => $item)
					 { $result.=' <option value="'.$item["id"].'">'.$item["nombre"].'</option>';
					  /*if ($_SESSION["tipo_usuario"]==1)
					   {  $result.=' <option value="'.$item["id"].'">'.$item["nombre"].'</option>';   }
					 elseif ($_SESSION["punto_id"]==$item["id"])
					  {	$result.=' <option value="'.$item["id"].'">'.$item["nombre"].'</option>'; }
					  }
					 $result.='</select>';
					 echo $result;
				 }*/

		# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  End puntos  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  max="<?=$_SESSION["fecha"];
	?>
		</td>
	</tr>
	<thead>
		<tr > <td colspan="2" ></td> </tr>
	</thead>
	</table><br>
	<input  type="submit" value="Enviar">
</form>


