
<?php
ob_start();
class controllerSuministroProducto extends MvcController
{
	#REGISTRO DE SUMINISTRO PRODUCTOS
	#------------------------------------
	 public function registroSuministroPController()
		{
		 // Debug: Verificar si hay datos POST
		 if (empty($_POST)) {
			 echo "<script>alert('❌ No se recibieron datos POST');</script>";
			 return;
		 }

		 // Debug: Mostrar todos los datos POST recibidos
		 echo "<script>console.log('POST recibido: " . json_encode($_POST) . "');</script>";

		 if(isset($_POST["productos"]) && isset($_POST["suministros"]) && isset($_POST["cantidades"]))
			{
				// Limpiar y validar datos
				$cantp = str_replace(",", ".", $_POST["cantidades"]);
				$producto_id = intval($_POST["productos"]);
				$codigo_suministro = trim($_POST["suministros"]);

				// Debug: Mostrar datos procesados
				echo "<script>console.log('Datos procesados - Producto ID: $producto_id, Código: $codigo_suministro, Cantidad: $cantp');</script>";

				// Validar que los datos no estén vacíos
				if ($producto_id > 0 && !empty($codigo_suministro) && $cantp > 0) {

					$datosController = array(
						'producto_id' => $producto_id,
						'codigo' => $codigo_suministro,
						'cantidades' => $cantp
					);

					// Debug: Mostrar datos que se envían al modelo
					echo "<script>console.log('Datos para modelo: " . json_encode($datosController) . "');</script>";

					try {
						$respuesta = DatosSuministroProducto::registroSuministroPModel($datosController, "suministros_productos");

						// Debug: Mostrar respuesta del modelo
						echo "<script>console.log('Respuesta del modelo: $respuesta');</script>";

						// Manejar diferentes tipos de respuesta
						switch($respuesta) {
							case "success":
								echo "<script>alert('✅ Suministro asignado correctamente'); window.location.href='index.php?action=registroReseta&id=".$producto_id."';</script>";
								break;
							case "error_suministro_no_encontrado":
								echo "<script>alert('❌ Error: No se encontró el suministro con código: ".$codigo_suministro."'); window.location.href='index.php?action=registroReseta&id=".$producto_id."';</script>";
								break;
							case "error_datos_incompletos":
								echo "<script>alert('❌ Error: Datos incompletos'); window.location.href='index.php?action=registroReseta&id=".$producto_id."';</script>";
								break;
							case "error_ejecucion":
								echo "<script>alert('❌ Error: Fallo en la ejecución de la consulta SQL'); window.location.href='index.php?action=registroReseta&id=".$producto_id."';</script>";
								break;
							case "error_excepcion":
								echo "<script>alert('❌ Error: Excepción durante el proceso'); window.location.href='index.php?action=registroReseta&id=".$producto_id."';</script>";
								break;
							default:
								echo "<script>alert('❌ Error desconocido: ".$respuesta."'); window.location.href='index.php?action=registroReseta&id=".$producto_id."';</script>";
								break;
						}
					} catch (Exception $e) {
						echo "<script>alert('❌ Excepción en controlador: " . $e->getMessage() . "'); window.location.href='index.php?action=registroReseta&id=".$producto_id."';</script>";
					}
				} else {
					echo "<script>alert('❌ Error: Datos inválidos.\\nProducto ID: $producto_id\\nCódigo: $codigo_suministro\\nCantidad: $cantp'); history.back();</script>";
				}
			} else {
				$productos = isset($_POST["productos"]) ? $_POST["productos"] : 'NO ENVIADO';
				$suministros = isset($_POST["suministros"]) ? $_POST["suministros"] : 'NO ENVIADO';
				$cantidades = isset($_POST["cantidades"]) ? $_POST["cantidades"] : 'NO ENVIADO';

				echo "<script>alert('❌ Error: Faltan campos requeridos.\\nProductos: $productos\\nSuministros: $suministros\\nCantidades: $cantidades'); history.back();</script>";
			}
		}
	#---------------------------------------------
	#VISTA DE SUMINISTRO PRODUCTOS
	#------------------------------------
	 public function vistaSuministroPController()
		{
		 $respuesta = DatosSuministroProducto::vistaSuministroPModel("productos");
		 foreach($respuesta as $row => $item)
			{
			 echo'
			 	<tr>
					<td>'.$item["codigo"].'</td>
					<td>'.$item["nombre"].'</td>
					<td><a href="index.php?action=suministroPdetalle&id='.$item["id"].'">Detalle</a></td>
				</tr>';
			}
		}
	#--------------------------------------
	#DETALLE SUMINISTRO PRODUCTOS
	#------------------------------------
	 public function detalleSuministroPController()
		{	//session_start();
		 $usuario=$_SESSION["tipo_usuario"];
		 if ($_GET["id"]>0 )
			{
			 $_SESSION["producto"]= $_GET["id"];
			}
			$datosController =  $_SESSION["producto"];
			//echo "<script>alert('Entro Controller ".$datosController." pro')</script>";
			$respuesta = DatosSuministroProducto::detalleSuministroPModel($datosController);
		 foreach($respuesta as $row => $item)
			{
				 echo'<tr>
							<td>'.$item["scodigo"].'</td>
							<td>'.$item["suministro"].'</td>
							<td>'.$item["idp"].'</td>
							<td>'.$item["ids"].'</td>
							<td>'.$item["cantidad"].'</td>';
				 if ($usuario==1)
					{
						echo '<td><a href="index.php?action=editarSuministroP&ids='.$item["ids"].'&idp= '.$datosController .'">Editar</a></td>
						<td><a href="index.php?action=registroReseta&idBorrar='.$item["ids"].'&prBorrar= '.$datosController .'">Borrar</a></td>';
					}

				 echo '</tr>';
			}
		}
	#------------------------------------------------
	#EDITAR SUMINISTRO PRODUCTOS
	#------------------------------------
	 public function editarSuministroPController()
		{	//echo "<script>alert('Entro controles  Suministro p')</script>";
		 $datosController  = array('producto_id' => $_GET["idp"],
									'suministro_id' => $_GET["ids"]);
			//echo "<script>alert('Entro CRUD ".$_GET["idp"]." Suministro p')</script>";
		 $respuesta = DatosSuministroProducto::editarSuministroPModel($datosController, "suministros_productos");
		 echo'
			 <input type="hidden" value="'.$respuesta["producto_id"].'" name="producto_idEditar" required>
			 <input type="hidden" value="'.$respuesta["suministro_id"].'" name="suministro_idEditar" required>
			 Cantidad<input type="text" value="'.$respuesta["cantidades"].'" name="cantidadesEditar" required>
				 <input type="submit" value="Actualizar">';
		}
	#------------------------------------
	#ACTUALIZAR SUMINISTRO PRODUCTOS
	#------------------------------------
	 public function actualizarSuministroPController()
		{	//echo "<script>alert('Entro Controller Actualizar Producto')</script>";
		 if(isset($_POST["producto_idEditar"]))
			{
				$datosController = array("producto_id"=>$_POST["producto_idEditar"],
										"suministro_id"=>$_POST["suministro_idEditar"],
										"cantidades"=>$_POST["cantidadesEditar"]);
				$respuesta = DatosSuministroProducto::actualizarSuministroPModel($datosController, "suministros_productos");
				if($respuesta == "success")
					{	header("location:index.php?action=registroReseta&id=".$_POST["producto_idEditar"]);	}
				else
					{	echo "error";	}
			}
		}
	#------------------------------------
	#BORRAR SUMINISTRO PRODUCTOS
	#------------------------------------
	 public function borrarSuministroPController()
		{ //echo "<br>S : ".$_GET["idBorrar"];
		 if(isset($_GET["prBorrar"]) && isset($_GET["idBorrar"]))
			{
				$datosController =  array('producto_id' => $_GET["prBorrar"],
										  'suministro_id' => $_GET["idBorrar"], );
				$respuesta = DatosSuministroProducto::borrarSuministroPModel($datosController, "suministros_productos");
			 if($respuesta == "success")
					{	header("location:index.php?action=registroReseta&id=".$_GET["prBorrar"]);	}
			}
		}
	#---------------------------------
	#listas
	#--------------------------------------------
		#LISTA Productos
		#---------------------------------------
		 public function listaProductosController()
			{
				$respuesta = Datos::listaProductosModel("productos");
				return 	$respuesta;
			}
		#---------------------------------------
		#LISTA Suministros
		#---------------------------------------
		 public function listaSuministrosController()
			{
				$respuesta = Datos::listaSuministrosModel("suministros");
				return 	$respuesta;
			}
		#---------------------------------------
	#-----------------------------------------

}
