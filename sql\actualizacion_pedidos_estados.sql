-- Script de actualización para sistema de estados de pedidos
-- Base de datos: ewogjwfm_macarena
-- Ejecutar en orden para evitar errores

-- Verificar que estamos en la base de datos correcta
USE ewogjwfm_macarena;

START TRANSACTION;

-- 1. Ag<PERSON>gar campos de estado y control a la tabla pedidos
ALTER TABLE `pedidos` 
ADD COLUMN `estado` ENUM('borrador', 'enviado', 'entregado', 'facturado') DEFAULT 'borrador' AFTER `facturado`,
ADD COLUMN `fecha_envio` DATETIME NULL AFTER `estado`,
ADD COLUMN `fecha_entrega` DATETIME NULL AFTER `fecha_envio`,
ADD COLUMN `usuario_envio` BIGINT(20) NULL AFTER `fecha_entrega`,
ADD COLUMN `usuario_entrega` BIGINT(20) NULL AFTER `usuario_envio`,
ADD COLUMN `numero_pedido` VARCHAR(20) NULL AFTER `usuario_entrega`,
ADD INDEX `idx_estado` (`estado`),
ADD INDEX `idx_fecha_envio` (`fecha_envio`),
ADD INDEX `idx_numero_pedido` (`numero_pedido`);

-- 2. Verificar si mesa_id existe, si no, agregarla
SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
AND table_name = 'pedidos' 
AND column_name = 'mesa_id';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE `pedidos` ADD COLUMN `mesa_id` BIGINT(20) NULL AFTER `mesero_id`, ADD INDEX `idx_mesa_id` (`mesa_id`)', 
    'SELECT "Column mesa_id already exists" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. Agregar fecha_pedido si no existe
SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
AND table_name = 'pedidos' 
AND column_name = 'fecha_pedido';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE `pedidos` ADD COLUMN `fecha_pedido` DATETIME DEFAULT CURRENT_TIMESTAMP AFTER `mesa_id`', 
    'SELECT "Column fecha_pedido already exists" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. Crear tabla para historial de estados de pedidos
CREATE TABLE IF NOT EXISTS `pedidos_historial` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `pedido_id` BIGINT(20) NOT NULL,
  `estado_anterior` ENUM('borrador', 'enviado', 'entregado', 'facturado') NULL,
  `estado_nuevo` ENUM('borrador', 'enviado', 'entregado', 'facturado') NOT NULL,
  `usuario_id` BIGINT(20) NOT NULL,
  `fecha_cambio` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `observaciones` TEXT NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_pedido_id` (`pedido_id`),
  INDEX `idx_fecha_cambio` (`fecha_cambio`),
  FOREIGN KEY (`pedido_id`) REFERENCES `pedidos` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

-- 5. Crear tabla para reimpresiones
CREATE TABLE IF NOT EXISTS `pedidos_reimpresiones` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `pedido_id` BIGINT(20) NOT NULL,
  `categoria` ENUM('bar', 'cocina', 'asados') NOT NULL,
  `usuario_id` BIGINT(20) NOT NULL,
  `fecha_reimpresion` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `motivo` VARCHAR(255) NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_pedido_id` (`pedido_id`),
  INDEX `idx_fecha_reimpresion` (`fecha_reimpresion`),
  FOREIGN KEY (`pedido_id`) REFERENCES `pedidos` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

-- 6. Migrar datos existentes
-- Actualizar pedidos que ya están facturados
UPDATE pedidos 
SET estado = 'facturado' 
WHERE facturado = 's';

-- Actualizar mesa_id para pedidos existentes si está vacío
UPDATE pedidos p 
SET mesa_id = (
    SELECT DISTINCT pvm.mesas_id 
    FROM producto_vendido_mesa pvm 
    WHERE pvm.pedidos_id = p.id 
    LIMIT 1
) 
WHERE p.mesa_id IS NULL OR p.mesa_id = 0;

-- 7. Generar números de pedido para registros existentes
UPDATE pedidos 
SET numero_pedido = CONCAT('P', LPAD(id, 6, '0'))
WHERE numero_pedido IS NULL;

-- 8. Agregar foreign keys si no existen
SET @fk_exists = 0;
SELECT COUNT(*) INTO @fk_exists 
FROM information_schema.key_column_usage 
WHERE table_schema = DATABASE() 
AND table_name = 'pedidos' 
AND constraint_name = 'fk_pedidos_mesa';

SET @sql = IF(@fk_exists = 0, 
    'ALTER TABLE `pedidos` ADD CONSTRAINT `fk_pedidos_mesa` FOREIGN KEY (`mesa_id`) REFERENCES `mesas` (`id`)', 
    'SELECT "Foreign key fk_pedidos_mesa already exists" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 9. Crear trigger para generar número de pedido automáticamente
DELIMITER $$
DROP TRIGGER IF EXISTS `generar_numero_pedido`$$
CREATE TRIGGER `generar_numero_pedido` 
BEFORE INSERT ON `pedidos` 
FOR EACH ROW 
BEGIN
    IF NEW.numero_pedido IS NULL THEN
        SET NEW.numero_pedido = CONCAT('P', LPAD((SELECT IFNULL(MAX(id), 0) + 1 FROM pedidos), 6, '0'));
    END IF;
END$$
DELIMITER ;

-- 10. Crear trigger para historial de cambios de estado
DELIMITER $$
DROP TRIGGER IF EXISTS `pedidos_historial_insert`$$
CREATE TRIGGER `pedidos_historial_insert` 
AFTER UPDATE ON `pedidos` 
FOR EACH ROW 
BEGIN
    IF OLD.estado != NEW.estado THEN
        INSERT INTO pedidos_historial (pedido_id, estado_anterior, estado_nuevo, usuario_id, observaciones)
        VALUES (NEW.id, OLD.estado, NEW.estado, NEW.usuario_envio, 
                CONCAT('Cambio de estado de ', OLD.estado, ' a ', NEW.estado));
    END IF;
END$$
DELIMITER ;

COMMIT;

-- Verificar la estructura actualizada
SELECT 'Estructura actualizada correctamente' as resultado;
DESCRIBE pedidos;
