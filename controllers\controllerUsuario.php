<style>
	.mayuscula
	{text-transform: uppercase;  }

	#estiloMula{color:#000;background:#fff;/*height:40px*/;	}
	.mula{color:#000;background:#fff;font-size: 12px;}
	.er{background:#ffff26;} /*ff7a4d - 00d9a3*/
	.programado{background:#00d9a3;}
	.listap{background:#e5e5e5 }
	.estees{background:#F7CBD9; }
</style>
<?php
ob_start();
class controllerUsuario extends MvcController
{
	#REGISTRO DE USUARIOS
	##-------------------------------------
	 public function registroUsuarioController()
		{	//echo '<script>alert("entro controle");</script>';
		 if(isset($_POST["nombresRegistro"]) && isset($_POST["apellidosRegistro"]) && isset($_POST["cedulaRegistro"]) && isset($_POST["ciudad"]) && isset($_POST["fechanacimientoRegistro"]) )
			{
			 if ($_POST["roles"]>0)
				{//echo '<script>alert("entro controle IF");</script>';
					$admin=$_POST["roles"];
					$datosController = array( "ciudad_id"=>$_POST["ciudad"],
											  "roles_id"=>$_POST["roles"],
											  "cedula"=>$_POST["cedulaRegistro"],
										      "nombre"=>$_POST["nombresRegistro"],
										      "apellidos"=>$_POST["apellidosRegistro"],
										      "fecha_nacimiento"=>$_POST["fechanacimientoRegistro"],
										      "direccion"=>$_POST["direccionRegistro"],
										      "telefono"=>$_POST["telefonoRegistro"],
										      "email"=>$_POST["emailRegistro"],
										      "activo"=>$_POST["activoRegistro"],
										      "usuario"=>$_POST["usuarioRegistr"],
										      "clave"=>$_POST["passwordRegistr"],
										      "pregunta"=>$_POST["preguntaRegistro"],
							     			  "respuesta"=>$_POST["respuestaRegistro"]);
					$respuesta = DatosUsuario::registroUsuarioModel($datosController, "personas");
				 if($respuesta == "success")
					{
					 if ($_SESSION["tipo_usuario"]==1)
					 	{
					 	 echo '<script>alert(" guardo");location.href="ok";</script>';
					 	}
					 elseif ($_SESSION["tipo_usuario"]==3)
					 	{
					 	 echo '<script>alert(" guardo");location.href="index.php?action=registroPmesa&ida='.$_SESSION["mesa"].'";</script>';
					 	}
					}
				 else{ echo '<script>alert("No se guardo intentelo nuevamente");</script>';	}
				}
			 else {	echo '<script>alert("Debe seleccionar un tipo de usuario");history.back(1);</script>'; }
			}
		}
	#------------------------------------------
	#INGRESO DE USUARIOS
	#-------------------------------------------------------------------------------------
	 public function ingresoUsuarioController()
		{
		 if(isset($_POST["usuarioIngreso"]) and isset($_POST["passwordIngreso"]))
			{
				$datosController = array( "usuario"=>$_POST["usuarioIngreso"],
									      "pass"=>$_POST["passwordIngreso"]);
				$respuesta = DatosUsuario::ingresoUsuarioModel($datosController, "personas");
			 if($respuesta["usuario"] == $_POST["usuarioIngreso"] && $respuesta["clave"] == $_POST["passwordIngreso"])
				{
					session_start();
					$_SESSION["validar"]=true;
					$_SESSION["usua"]=$_POST["usuarioIngreso"];
					$_SESSION["usuario"]=$respuesta["id"];
					$_SESSION["tipo_usuario"]=$respuesta["roles_id"];
					$_SESSION["persona"]=$respuesta["nombre"]."  ".$respuesta["apellidos"];
					$_SESSION["nombres"]=$respuesta["nombre"];
					$_SESSION["apellidos"]=$respuesta["apellidos"];
					$_SESSION["punto_id"]=$respuesta["punto_id"];
					/**	 * variables de session destruida	$_SESSION["mesa"]=1;	 */
					unset($_SESSION["idfactura"]);
					unset($_SESSION["devoluciones"]);
					unset($_SESSION["mesa"]);
					##################
					if ($respuesta["roles_id"]<4 )
						{
							header("location:registroTurno");
						}
					else {	if ($respuesta["roles_id"]==4)
							{
								header("location:cocina");
							}
							if ( $respuesta["roles_id"]==10)
							{
								header("location:mesa");
							}
							else{	header("location:ingresar");	}//
						}
				}
			else{ header("location:fallo"); }
		}
	}
	#------------------------------------
	#VISTA DE USUARIOS
	#---------------------------------------------------------------
	 public function vistaUsuariosController()
		{
		 $respuesta = DatosUsuario::vistaUsuariosModel("personas");
		 foreach($respuesta as $row => $item)
			{
				echo'<tr>
						<td>'.$item["ciudad_id"].'</td>
						<td>'.$item["roles_id"].'</td>
						<td>'.$item["cedula"].'</td>
						<td>'.$item["nombre"].'</td>
						<td>'.$item["apellidos"].'</td>
						<td>'.$item["fecha_nacimiento"].'</td>
						<td>'.$item["direccion"].'</td>
						<td>'.$item["telefono"].'</td>
						<td>'.$item["email"].'</td>
						<td>'.$item["fecha_registro"].'</td>
						<td>'.$item["activo"].'</td>
						<td>'.$item["usuario"].'</td>
						<td>'.$item["clave"].'</td>
						<td>'.$item["pregunta"].'</td>
						<td>'.$item["respuesta"].'</td>
						<td><a href="index.php?action=edita&id='.$item["id"].'">Editar</a></td>
					</tr>';
			}
		}
	#-----------------------------------------------
	#VISTA DE USUARIOS CONSULTA
	#---------------------------------------------------------------------------
	 public function vistaUsuarioController()
		{
		 $respuesta = DatosUsuario::vistaUsuarioModel("personas");
			#El constructor foreach proporciona un modo sencillo de iterar sobre arrays. foreach funciona sólo sobre arrays y objetos, y emitirá un error al intentar usarlo con una variable de un tipo diferente de datos o una variable no inicializada.
		 foreach($respuesta as $row => $item)
			{ if ($item["id"]!=1)
				{
				 	echo'<tr>
							<td>'.$item["rnombre"].'</td>
							<td>'.$item["pcedula"].'</td>
							<td>'.$item["pnombre"].'</td>
							<td>'.$item["papellidos"].'</td>
							<td>'.$item["pfecha_nacimiento"].'</td>
							<td>'.$item["pdireccion"].'</td>
							<td>'.$item["ptelefono"].'</td>
							<td>'.$item["pemail"].'</td>
							<td>'.$item["pfecha_registro"].'</td>
							<td>'.$item["pactivo"].'</td>
							<td>'.$item["pusuario"].'</td>
							<td>'.$item["pclave"].'</td>

							<td><a href="index.php?action=editar&id='.$item["pid"].'">Editar</a> <a href="index.php?action=usuarios&idBorrar='.$item["pid"].'">Borrar</a></td>
						</tr>';
				 }

			}
		}
	#-------------------------------------------------------------------
	#EDITAR USUARIO	<label> Ciudad : </label>
	#---------------------------------------------------------------------
	 public function editarUsuarioController()
		{
			$datosController = $_GET["id"];
			$respuesta = DatosUsuario::editarUsuarioModel($datosController, "personas");
			$registro = new ControllerUsuario();
			$roles=$registro->listaRolesController();
			echo'
					<input type="hidden" value="'.$respuesta["pciudad_id"].'" name="ciudadEditar" required><br>
					<table>
						<thead>
							<tr > <td colspan="2" ></td> </tr>
						</thead>
						<tr>
							<td><label> roles_id : </label></td>
							<td>
							';

							 # %%%%%%%%%%%%%%%%%%% Roles  %%%%%%%%%%%%%%%%%%%%%%%%%
								if($roles=="error")
									{	echo "debe registrar el Roll"; }
								else{
										echo "";
										$result='<select name="rolEditar"  id="rolEditar">';
										$result.=' <option value="'.$respuesta["rid"].'">'.$respuesta["rnombre"].'</option>';
										foreach ($roles as $row => $ite)
									 	 {
									 	 	if ($_SESSION["usuario"]==1)
									 	 	{
									 	 		$result.=' <option value="'.$ite["id"].'">'.$ite["nombre"].'</option>';
									 	 	}
									 	 	else
									 	 	 {
									 	 	 	if ($ite["id"]==11)
									 	 	 	 {		$result.=' <option value="'.$ite["id"].'">'.$ite["nombre"].'</option>'; 	 }
									 	 	 }

									 	 }
										 $result.='</select>';
										 echo $result." <br>";
									 	}

							 # %%%%%%%%%%%%%%%%%%%%%%%%%%%%  End Roles  %%%%%%%%%%%%%%%%%%%%%%%%%%%

							echo'</td>
						</tr>
						<tr>
							<td><label> cedula : </label></td>
							<td><input type="text" value="'.$respuesta["pcedula"].'" name="cedulaEditar" required></td>
						</tr>
						<tr>
							<td><label> nombre : </label></td>
							<td><input type="text" value="'.$respuesta["pnombre"].'" name="nombreEditar" required></td>
						</tr>
						<tr>
							<td><label> apellidos : </label></td>
							<td><input type="text" value="'.$respuesta["papellidos"].'" name="apellidosEditar" required></td>
						</tr>
						<tr>
							<td><label> fecha_nacimiento : </label></td>
							<td><input type="text" value="'.$respuesta["pfecha_nacimiento"].'" name="fechaNEditar" required></td>
						</tr>
						<tr>
							<td><label> direccion : </label></td>
							<td><input type="text" value="'.$respuesta["pdireccion"].'" name="direccionEditar" required></td>
						</tr>
						<tr>
							<td><label> telefono : </label></td>
							<td><input type="text" value="'.$respuesta["ptelefono"].'" name="telefonoEditar" required></td>
						</tr>
						<tr>
							<td><label> email : </label></td>
							<td><input type="text" value="'.$respuesta["pemail"].'" name="emailEditar" required></td>
						</tr>
						<tr>
							<td><label> activo : </label></td>
							<td><input type="text" value="'.$respuesta["pactivo"].'" name="activoEditar" ></td>
						</tr>
							<td><label> usuario : </label></td>
							<td><input type="text" value="'.$respuesta["pusuario"].'" name="usuarioEditar" ></td>
						</tr>
							<td><label> clave : </label></td>
							<td><input type="text" value="'.$respuesta["pclave"].'" name="claveEditar" ></td>

				 <input type="hidden" value="'.$respuesta["pfecha_registro"].'" name="fechaREditar" required><br>

				 <thead>
					<tr > <td colspan="2" ></td> </tr>
				</thead>
			</table>

			 	<input type="hidden" value="'.$respuesta["ppregunta"].'" name="preguntaEditar" ><br>
				 	<input type="hidden" value="'.$respuesta["prespuesta"].'" name="respuestaEditar" >	<br>
			 <input type="hidden" value="'.$respuesta["pid"].'" name="idEditar" required>
				 <input type="submit" value="Actualizar">';
		}
		/*<label> pregunta : </label><label> respuesta : </label>*/
	#-------------------------------------------------
	#ACTUALIZAR USUARIO
	#-----------------------------------------------------------------------------
	 public function actualizarUsuarioController()
		{	//echo '<script>alert("entro controle");</script>';
		 if(isset($_POST["usuarioEditar"]))
		 	{	//echo '<script>alert("entro controle IF");</script>';
			 $datosController = array( "ciudad_id"=>$_POST["ciudadEditar"],
							          "roles_id"=>$_POST["rolEditar"],
				                      "cedula"=>$_POST["cedulaEditar"],
				                      "nombre"=>$_POST["nombreEditar"],
				                      "apellidos"=>$_POST["apellidosEditar"],
				                      "fecha_nacimiento"=>$_POST["fechaNEditar"],
				                      "direccion"=>$_POST["direccionEditar"],
				                      "telefono"=>$_POST["telefonoEditar"],
				                      "email"=>$_POST["emailEditar"],
				                      "fecha_registro"=>$_POST["fechaREditar"],
				                      "activo"=>$_POST["activoEditar"],
				                      "usuario"=>$_POST["usuarioEditar"],
				                      "clave"=>$_POST["claveEditar"],
				                      "pregunta"=>$_POST["preguntaEditar"],
				                      "respuesta"=>$_POST["respuestaEditar"],
				                      "id"=>$_POST["idEditar"]);

			$respuesta = DatosUsuario::actualizarUsuarioModel($datosController, "personas");

			if($respuesta == "success")
				{	header("location:cambio");	}
			else{ echo "error";	}
		 	}
		}
	#-----------------------------------------------------------------------------------------
	#BORRAR USUARIO
	#-----------------------------------------------------------------------------
	 public function borrarUsuarioController()
		{
		 if(isset($_GET["idBorrar"]))
			{ //echo "<script>alert('entro controle".$_GET["idBorrar"]."');</script>";
				$datosController = $_GET["idBorrar"];
				//echo "<script>alert('entro controle".$datosController."');</script>";
				$respuesta = DatosUsuario::borrarUsuarioModel($datosController, "personas");
				if($respuesta == "success")
					{	header("location:usuarios");	}
			}
		}
	#----------------------------------------------


 }
