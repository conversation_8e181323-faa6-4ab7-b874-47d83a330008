<?php

	#EXTENSIÓN DE CLASES: Los objetos pueden ser extendidos, y pueden heredar propiedades y métodos. Para definir una clase como extensión, debo definir una clase padre, y se utiliza dentro de una clase hija.

	require_once "conexion.php";


	class VistaVentas extends Conexion
	 {
			#--------------------------------------------------------------------------------------------
			#vista Ventas INDI
			#------------------------------------------------------------------------------------------
			 public static function vistaVentas1Model($fecha1,$fecha2)
			 {
				try
				 {	$fechah1=$fecha1.' 00:00:01';
					$fechah2=$fecha2.' 23:59:01';
					$stmt = Conexion::conectar()->prepare("SELECT p.id as idp,p.nombre, sum(pm.cantidad) as cantp,pm.precio as precio,sum(pm.cantidad)*pm.precio as Subtotal_por_producto
									FROM ventas v,pedidos pd,pedido_productos_mesa pm,productos p
									WHERE v.pedidos_id=pd.id and pd.id=pm.pedidos_id and pm.productos_id=p.id
									and v.fecha_venta BETWEEN '$fechah1' and '$fechah2'
									group by pm.productos_id");
					$stmt->execute();
					return $stmt->fetchAll();
					$stmt->close();

				 }
				catch (Exception $e)
					{					//$stmt->rollBack();
						print "Error!: ".$e->getMessage()."</br>";
						return "Error!: ".$e->getMessage()."</br>";
					}
			 }
			#--------------------------------------------------------------------------------------------
			#vista Ventas Model
			#-----------------------------------------------
			 public static function vistaVentasGeneroModel($fecha1,$fecha2)
			 {
				try
				 {	$fechah1=$fecha1.' 00:00:01';
						$fechah2=$fecha2.' 23:59:01';
						echo "<script>alert('Si entro CRUD fecah 1 ".$_POST["fecha1"]." ');</script>";
						$consulta="SELECT p.id as idp,p.nombre, sum(pm.cantidad) as cantp,pm.precio as precio,sum(pm.cantidad)*pm.precio as Subtotal_por_producto,  pm.descuento AS pmdescuento
							FROM ventas v,pedidos pd,pedido_productos_mesa pm,productos p
							WHERE v.pedidos_id=pd.id and pd.id=pm.pedidos_id and pm.productos_id=p.id
							and v.fecha_venta BETWEEN '$fechah1' and '$fechah2'
							group by pm.productos_id";
						$stmt = Conexion::conectar()->prepare($consulta);
						echo "<br>--";
						$stmt->execute();
						return $stmt->fetchAll();
						$stmt->close();
				 }
				catch (Exception $e)
					{					//$stmt->rollBack();
						print "Error!: ".$e->getMessage()."</br>";
						return "Error!: ".$e->getMessage()."</br>";
					}
			 }
			#-----------------------------------------------------

			#vista Ventas Model
			#-----------------------------------------------
			 public static function vistaVentasModel($fecha1, $fecha2)
			 {
				try
				 {	//$fechah1=$fecha1.' 00:00:01';
						//$fechah2=$fecha2.' 23:59:01';
						//echo "<script>alert('Si entro CRUD fecah 1 ".$fecha1."--- fecha2 ".$fecha2."');</script>";
						$consulta="SELECT p.id as idp,p.nombre, sum(pm.cantidad) as cantp,pm.precio as precio, sum(pm.cantidad)*pm.precio as Subtotal_por_producto,  pm.descuento AS pmdescuento
							FROM ventas v,pedidos pd,pedido_productos_mesa pm,productos p
							WHERE v.pedidos_id=pd.id and pd.id=pm.pedidos_id and pm.productos_id=p.id
							and v.fecha_venta BETWEEN '$fecha1' and '$fecha2'
							group by pm.productos_id";
						$stmt = Conexion::conectar()->prepare($consulta);
						//echo "<br>-- ".$consulta." --<br>";
						$stmt->execute();
						return $stmt->fetchAll();
						$stmt->close();
				 }
				catch (Exception $e)
					{					//$stmt->rollBack();
						print "Error!: ".$e->getMessage()."</br>";
						return "Error!: ".$e->getMessage()."</br>";
					}
			 }
			#-----------------------------------------------------
			#vista Perdidas Model
			#------------------------------------------------------------------------------------------
				public static function vistaPerdidasModel($fecha1,$fecha2)
					{
						try {	$fechah1=$fecha1.' 00:00:01';
								$fechah2=$fecha2.' 23:59:01';
								$stmt = Conexion::conectar()->prepare("SELECT p.id as idp,s.nombre as nombre, sum(p.cantidad) as cantp,p.precio as precio,sum(p.cantidad*p.precio) as subtotal
									FROM perdidas p,suministros s WHERE s.id=p.suministros_id and fecha BETWEEN '$fechah1' and '$fechah2' group by p.suministros_id  ");
								$stmt->execute();
								return $stmt->fetchAll();
								$stmt->close();

							}
						catch (Exception $e)
							{					//$stmt->rollBack();
								print "Error!: ".$e->getMessage()."</br>";
								return "Error!: ".$e->getMessage()."</br>";
							}
					}
			#-------------------------------------------------------------------------------------------
			#vista Inventario Bodega
			#------------------------------------------------------------------------------------------
				public static function inventarioBodegaModel()
					{
						try {
								$stmt = Conexion::conectar()->prepare("SELECT nombre,cantidad,precio, (cantidad*precio) as subtotal FROM suministros where activo='s' group by id");
								$stmt->execute();
								return $stmt->fetchAll();
								$stmt->close();
							}
						catch (Exception $e)
							{					//$stmt->rollBack();
								print "Error!: ".$e->getMessage()."</br>";
								return "Error!: ".$e->getMessage()."</br>";
							}
					}
			#-----------------------------------------------------
			#vista Report Compra Product Model
			#---------------------------------------------------
			 public static function vistaReporCompraProductModel($fecha1,$fecha2,$code)
			 {
				try
				 {	$fechah1=$fecha1.' 00:00:01';
						$fechah2=$fecha2.' 23:59:01';
						//echo "<script>alert('Si entro CRUD fecah 1 ".$_POST["fecha1"]." ');</script>";
						$consulta="SELECT dfc.cantidad,cs.fecha_hora as fecha, p.nombre 
FROM productos p, suministros s, suministros_productos sp, sucursal su, detalle_factura_suministro dfc ,compras_suministros cs 
WHERE sp.producto_id=p.id and sp.suministro_id=s.id and su.suministro_id=s.id and dfc.suministro_id=s.id and cs.id=dfc.compra_suministro_id and s.codigo='$code' and cs.fecha_hora BETWEEN '$fecha1' and '$fecha2' group by cs.id";
						$stmt = Conexion::conectar()->prepare($consulta);
						//echo "<br>-- ".$consulta." --<br>";
						$stmt->execute();
						return $stmt->fetchAll();
						$stmt->close();
				 }
				catch (Exception $e)
					{					//$stmt->rollBack();
						print "Error!: ".$e->getMessage()."</br>";
						return "Error!: ".$e->getMessage()."</br>";
					}
			 }
			 #-----------------------------------------------------
			#vista Report Venta Product Model
			#---------------------------------------------------
			 public static function vistaReporVentaProductModel($fecha1,$fecha2,$code)
			 {
				try
				 {	$fechah1=$fecha1.' 00:00:01';
						$fechah2=$fecha2.' 23:59:01';
						//echo "<script>alert('Si entro CRUD fecah 1 ".$_POST["fecha1"]." ');</script>";
						$consulta="SELECT sum(pm.cantidad)*sp.cantidades as cantidad,v.fecha_venta as fecha, p.nombre FROM `productos` p, suministros s, suministros_productos sp, sucursal su, ventas v,pedidos pd,pedido_productos_mesa pm WHERE sp.producto_id=p.id and sp.suministro_id=s.id and su.suministro_id=s.id and v.pedidos_id=pd.id and pd.id=pm.pedidos_id and pm.productos_id=p.id and s.codigo='$code' and v.fecha_venta BETWEEN '$fechah1' and '$fechah2' group by pm.productos_id,v.fecha_venta";
						$stmt = Conexion::conectar()->prepare($consulta);
						//echo "<br>-- ".$consulta." --<br>";
						$stmt->execute();
						return $stmt->fetchAll();
						$stmt->close();
				 }
				catch (Exception $e)
					{					//$stmt->rollBack();
						print "Error!: ".$e->getMessage()."</br>";
						return "Error!: ".$e->getMessage()."</br>";
					}
			 }
			#---------------------------------------------------------------------------------
			# Datos Producto Model
			#----------------------------------------------------------------------------------
			 public static function DatosProductoModel($codigo)
				{

			 		$consulta="SELECT s.precio AS costo, s.nombre as nombre, s.codigo as codigo, su.cantidad as cantidad,p.precio as precio FROM productos p, suministros_productos sp, suministros s, sucursal su WHERE p.id=sp.producto_id and s.id=sp.suministro_id and su.suministro_id=s.id and s.codigo='$codigo' group by s.id ";

				 //echo $consulta;
				 $stmt = Conexion::conectar()->prepare($consulta);
				 $stmt->execute();
				 return $stmt->fetch();
				 $stmt->close();
				}
			#-------------------------------------------------------------------------------------------
			#vista Inventario Cocina
			#------------------------------------------------------------------------------------------
				public static function inventarioCocinaModel()
					{
						try {
								$stmt = Conexion::conectar()->prepare("SELECT s.nombre,su.cantidad,s.precio, sum(su.cantidad*s.precio) as subtotal FROM suministros s,sucursal su where s.id=su.suministro_id and s.activo='s' and s.tipo_suministro=2 group by s.id ");
								$stmt->execute();
								return $stmt->fetchAll();
								$stmt->close();
							}
						catch (Exception $e)
							{					//$stmt->rollBack();
								print "Error!: ".$e->getMessage()."</br>";
								return "Error!: ".$e->getMessage()."</br>";
							}
					}
			#-------------------------------------------------------------------------------------------
			#vista Inventario Cafeteria
			#------------------------------------------------------------------------------------------
				public static function inventarioCafeteriaModel()
					{
						try {
								$stmt = Conexion::conectar()->prepare("SELECT s.nombre,su.cantidad,s.precio, sum(su.cantidad*s.precio) as subtotal FROM suministros s,sucursal su where s.id=su.suministro_id and s.activo='s' and s.tipo_suministro=1 group by s.id ");
								$stmt->execute();
								return $stmt->fetchAll();
								$stmt->close();
							}
						catch (Exception $e)
							{					//$stmt->rollBack();
								print "Error!: ".$e->getMessage()."</br>";
								return "Error!: ".$e->getMessage()."</br>";
							}
					}
			#-----------------------------------------------------
			#vista Report Aumento de Inventario para un producto 
			#---------------------------------------------------
			 public static function vistaReporAumentoInventarioProductModel($fecha1,$fecha2,$code, $sucursal)
			 {
					if($sucursal==1){$ventas='ventas';}
					/*else if($sucursal==2){$ventas='ventas2';}
					else if($sucursal==3){$ventas='ventas3';}
					else if($sucursal==4){$ventas='ventas4';}
					else if($sucursal==5){$ventas='ventas5';}
					else if($sucursal==6){$ventas='ventas6';}
							else if($sucursal==7){$ventas='ventas7';}*/

				try
				 {	$fechah1=$fecha1.' 00:00:01';
						$fechah2=$fecha2.' 23:59:01';
						//echo "<script>alert('Si entro CRUD fecah 1 ".$_POST["fecha1"]." ');</script>";
						$consulta="SELECT ai.id as id, ai.descripcion as descripcion, ai.cantidad as cantidad,ai.fecha as fecha, p.nombre as producto, concat(ps.nombre,' ',ps.apellidos) as persona FROM productos p, suministros s, suministros_productos sp, sucursal su, aumentar_inventario ai, personas ps WHERE sp.producto_id=p.id and sp.suministro_id=s.id and su.suministro_id=s.id and ai.suministro_id=s.id and s.codigo='$code' and ai.punto_id=$sucursal and ai.punto_id=su.punto_id and ai.persona_id=ps.id and ai.fecha BETWEEN '$fechah1' and '$fechah2' group by s.id,ai.fecha ";
						$stmt = Conexion::conectar()->prepare($consulta);
						//echo "<br>-- ".$consulta." --<br>";
						$stmt->execute();

						$c2=$stmt->rowCount();
						//return $stmt->fetchAll();

						 if ($c2>0)
						  	{ 	//echo "<script>alert(' if entro consulta ".$consulta."')</script>";
						  		$r=$stmt->fetchAll();
						  	  	$datos=array('result' => 'success','info' => $r );
								return $datos;
						  	}
						 else
						 	{ //echo "<script>alert(' Error BD Buscar Traslado')</script>";
						 		$datos=array('result' => 'error','datos' => $consulta );
						 	    return $datos;
						 	}

						$stmt->close();
				 }
				catch (Exception $e)
					{					//$stmt->rollBack();
						print "Error!: ".$e->getMessage()."</br>";
						return "Error!: ".$e->getMessage()."</br>";
					}
			 }
			#-------------------------------------------------------------------------------------------
			# Costo Producto Model
			#---------------------------------------------------------------------------------------
				public static function CostoProductoModel($idproducto)
					{
						try {	$consulta="SELECT s.precio AS precio, sum(sp.cantidades*s.precio) as Valor_Compra_Producto
										FROM productos p, suministros_productos sp, suministros s
										WHERE p.id=sp.producto_id and s.id=sp.suministro_id and p.id=$idproducto";
								//echo $consulta;
								$stmt = Conexion::conectar()->prepare($consulta);
								$stmt->execute();
								return $stmt->fetch();
								$stmt->close();

							}
						catch (Exception $e)
							{					//$stmt->rollBack();
								print "Error!: ".$e->getMessage()."</br>";
								return "Error!: ".$e->getMessage()."</br>";
							}
					}
	 }