<?php
	

	if(isset($_GET["action"]))
		{	if($_GET["action"] == "okPm")
			{echo "Registro Exitoso";	}
		}
?>

<script type="text/javascript">
	
	// ------CANCELA TODO LOS PRODUCTOS PEDIDO EN LA MESA
		function cancelarPedido() 
			{	
				if (confirm('confirme si elimina el pedido?')) 
					{		setTimeout('location.href="views/modules/ajaxCancelarPedido.php"',500);		}			

			//alert("Entro en el else de placa");
				/*$("#destino").load("views/modules/ajaxPermiso.php", function()
					{  	alert("recibidos los datos por ajax Monte");    		 });*/		
			} 
	// ------CANCELA TODO LOS PRODUCTOS PEDIDO EN LA MESA fin 
	
	function funcion_calcular()
		{
			var efectivo = document.calculo.efectivo.value;
			var total = document.calculo.total.value;
			
			/*var precio = document.calculo.precio.value;
			var cantid = document.calculo.cantid.value;
			var importe = document.calculo.importe.value;*/
			var calculo = efectivo-total;
			//alert ('El cambio: ' + calculo);
			document.calculo.cambio.value = calculo;
		}
	 
	// ------CAMBIO O VUELTOS 
		function cambio() 
		 {	//alert("Vamos bien");
					var total=document.getElementById("total");
					var pago = document.getElementById("efectivo");
					var r= 0;
					r=pago.value-total.value;
					alert("El cambio es="+pago.value);
					document.getElementById("cambio").value=r;
		 }  
	// ------CANCELA TODO LOS PRODUCTOS PEDIDO EN LA MESA fin

		// ------FACTURAR--------------
		function facturar() 		
			{	
				if (confirm('confirme Facturar?')) 
					{		//setTimeout('location.href="views/modules/ajaxFactura.php"',500);	
								$("#destino").load("views/modules/ajaxFactura.php", {efectivo: $("#efectivo").val()}, function(){
        						 alert("recibidos los datos por ajax");		 
     						});	
					}				
			} 
	// ------FACTURAR FIN//	
	
			
</script>
	

	 <div class="row">
	      <div class="col-md-8">
			<!-- <form name="calculo" action="" method="post">
				<input type="text" name="precio">
				<input type="text" name="cantid">
				
				<input type="text" name="importe" value="3" readonly>
				<input type="button" value="Calcular" onclick="funcion_calcular()">
			</form> -->

	      	<form name="calculo" method="post" >

			  	<h2>PEDIDO</h2>
				<table border="1" class="table table-hover">				
					<thead>					
						<tr>						
							<th>PRODUCTOS COD.</th>				
							<th>PRODUCTOS</th>				
							<th>PRECIO</th>				
							<th>CANTIDAD</th>				
							<th>TOTAL</th>				
							<th>FECHA</th>					
							<th></th>
							<th></th>
						</tr>
					</thead>

					<tbody>					
						<?php
							$vistaPmesa = new MvcController();
							$vistaPmesa -> vistaPmesaController();
							$vistaPmesa -> borrarPmesaController();
						?>
						<tr>
							<td colspan="4"><h4>Recibo en efectivo  </h4></td> 
							<td colspan="4"><h4><input type="text" placeholder="$" name="efectivo" id="efectivo" onkeyup="funcion_calcular();" required>  </h4></td> 							
						</tr>
						<tr> 
							
							<td colspan="4"><h4>Cambio  </h4></td> 
							<td colspan="4"><h4><input type="text" placeholder="$" name="cambio" id="cambio" required>  </h4></td>
						</tr>
					</tbody>
					<thead> 	
						<tr > <td colspan="8" ></td> </tr>
					</thead>

				</table>
				<span id="destino" > </span>
				<p><a class="btn btn-primary btn-lg" href="#" role="button" onclick="cancelarPedido();" name="cancelar" value="cancelar">Cancelar &raquo;</a>
				<a class="btn btn-primary btn-lg" href="#" role="button" onclick="facturar();" name="factura" value="factura">Facturar &raquo;</a></p>
				<!-- <input type="button" onclick="buscar()" value="cancelar">-->
			 </form>
				
	      </div>
      <div class="col-md-4" style="background-color:#FFC; padding:12px">
			<h2>AGREGAR PRODUCTO</h2><br>
				</p>
			 
			
     </div>


  