<?php
	$registro = new controllerFactura();
	$pedidos_idFacturaRegistro=$registro -> listaPedidosController();
	$registro -> registroFacturaController();

	if(isset($_GET["action"]))
		{	if($_GET["action"] == "okF")
				{	echo "Registro Exitoso";	}

		}
		date_default_timezone_set("America/Bogota");
	$fecha_exit=strftime("%Y-%m-%d %H:%M:%S"); 
?>





<!--------------------------------------------------------- pasar lista
		<form action="post">												
		<script type="text/javascript"> 
		function pasar() { 
		    obj=document.getElementById('sel1'); 
		    if (obj.selectedIndex==-1) return; 
		    valor=obj.value; 
		    txt=obj.options[obj.selectedIndex].text; 
		    obj.options[obj.selectedIndex]=null; 
		    obj2=document.getElementById('sel2'); 
		    opc = new Option(txt,valor); 
		    eval(obj2.options[obj2.options.length]=opc);     
		} 
		</script> 
		 
		<select id="sel1" size="5"> 
		<option value="1">Uno</option> 
		<option value="2">Dos</option> 
		<option value="3">Tres</option> 
		<option value="4">Cuatro</option> 
		<option value="5">Cinco</option> 
		</select> 
		<input type="button" value="Pasar" onClick="pasar()"> 
		<select id="sel2" size="5"> 
		</select> 
		</form>
 -->

<!--  ------------------------------------------------ -->


	<?php
	$registroPmesa = new MvcController();
	$productos_idPmesaRegistro = $registroPmesa -> listaProductosController();
	$mesas_idPmesaRegistro = $registroPmesa -> listaMesasController();
	$registroPmesa -> registroPmesaController();

	if(isset($_GET["action"]))
		{	if($_GET["action"] == "okPm")
			{echo "Registro Exitoso";	}
		}
?>

<script type="text/javascript">
	
	// ------CANCELA TODO LOS PRODUCTOS PEDIDO EN LA MESA
		function cancelarPedido() 
			{	
				if (confirm('confirme si elimina el pedido?')) 
					{		setTimeout('location.href="views/modules/ajaxCancelarPedido.php"',500);		}			

			//alert("Entro en el else de placa");
				/*$("#destino").load("views/modules/ajaxPermiso.php", function()
					{  	alert("recibidos los datos por ajax Monte");    		 });*/		
			} 
	// ------CANCELA TODO LOS PRODUCTOS PEDIDO EN LA MESA fin
	 
	// ------ASIGNAR  PEDIDO EN LA MESA
		function asignarPedido() 
			{	
				if (confirm('tomar Pedido?')) 
					{		setTimeout('location.href="views/modules/ajaxAsignarPedido.php"',500);		}				
			} 
	// ------CANCELA TODO LOS PRODUCTOS PEDIDO EN LA MESA fin

	// ------FACTURAR--------------
		function facturar() 
			{	
				if (confirm('confirme Facturar?')) 
					{		setTimeout('location.href="views/modules/ajaxFacturar.php"',500);		}				
			} 
	// ------FACTURAR FIN
		function aMayusculas(obj,id)
			{    obj = obj.toUpperCase();
			    document.getElementById(id).value = obj;
			}
</script>


 <div class="row">
      <div class="col-md-8">
		  	<h2>PEDIDO</h1>
			<table border="1" class="table table-hover">				
				<thead>					
					<tr>						
						<th>PRODUCTOS COD.</th>				
						<th>PRODUCTOS</th>				
						<th>PRECIO</th>				
						<th>CANTIDAD</th>				
						<th>TOTAL</th>				
						<th>FECHA</th>					
						<th></th>
						<th></th>
					</tr>
				</thead>

				<tbody>					
					<?php
						$vistaPmesa = new MvcController();
						$vistaPmesa -> vistaPmesaController();
						$vistaPmesa -> borrarPmesaController();
					?>
				</tbody>
				<thead> 	
					<tr > <td colspan="8" ></td> </tr>
				</thead>

			</table>
			<p><a class="btn btn-primary btn-lg" href="#" role="button" onclick="cancelarPedido();" name="cancelar" value="cancelar">Cancelar &raquo;</a>
			<a class="btn btn-primary btn-lg" href="#" role="button" onclick="facturar();" name="cancelar" value="cancelar">Facturar &raquo;</a></p>
			<!-- <input type="button" onclick="buscar()" value="cancelar">-->
			
      </div>
      <div class="col-md-4">
			<h2>REGISTRO DE FACTURA</h2>

<form method="post">

	<!-- pedidos_id, turno_cajero_id, valor, fecha_venta, subtotal, iva, total, efectivo, debito, credito, cambio -->
	
	<?php 
				# 	# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  Pedido %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
				if($pedidos_idFacturaRegistro=="error")
					{	echo "debe registrar el pedido"; }
				else{
						echo "<label>Id pedido </label>";
						$result='<select name="pedidos_idFacturaRegistro"  id="pedidos_idFacturaRegistro">';
						$result.=' <option value="-1">Seleccione una pedidos_idFacturaRegistro</option>';
						foreach ($pedidos_idFacturaRegistro as $row => $item)
						 	{	$result.=' <option value="'.$item["id"].'">'.$item["id"].'</option>';	}	
						 $result.='</select>';
						 echo $result." <br>";
					}
			# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  End pedido  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
	?>


	<!--label> pedidos_id : </label>
	<input type="text" placeholder="pedidos_id" name="pedidos_idFacturaRegistro" required><br readonly  -->
	<label> turno_cajero_id : </label>
	<input type="text" name="turno_cajero_idFacturaRegistro" required><br>
	<br><label> valor </label>
	<input type="text" placeholder="valor" name="valorFacturaRegistro"><br>
	
	<label> fecha_venta : </label>
	<input id="datetime" type="datetime-local"  placeholder="valor" name="fecha_ventaFacturaRegistro" required><br>
	<label> subtotal </label>
	<input type="text"  name="subtotalFacturaRegistro" ><br>	

	<label> IVA: </label>
	<input type="text" placeholder="IVA" name="ivaFacturaRegistro" ><br>	
	<label> total: </label>
	<input type="text" placeholder="total" name="totalFacturaRegistro" ><br>	
	<label> efectivo: </label>
	<input type="text" placeholder="efectivo" name="efectivoFacturaRegistro" ><br>	
	<label> debito: </label>
	<input type="text" placeholder="debito" name="debitoFacturaRegistro" ><br>	
	<label> credito: </label>
	<input type="text" placeholder="credito" name="creditoFacturaRegistro" ><br>	
	<label> cambio: </label>
	<input  type="text" placeholder="cambio" name="cambioFacturaRegistro" ><br>	
	
	<input type="submit" value="Enviar">
     </div>

<!--  ------------------------------------------------ -->

