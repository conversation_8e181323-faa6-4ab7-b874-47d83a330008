<?php
// Verificar que sea una petición POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    exit('Method Not Allowed');
}

// Verificar que tenga el parámetro correcto
if (!isset($_POST['cancelar'])) {
    http_response_code(400);
    exit('Bad Request');
}

require_once "../../models/crud.php";
require_once "../../controllers/controller.php";

ini_set("session.cookie_lifetime","28800");
ini_set("session.gc_maxlifetime","28800");

// Log para debug
error_log("ajaxCancelarPedidoConPedidos: Iniciando proceso de cancelación via AJAX");

try {
    $ajax = new MvcController();
    $r = $ajax->cancelarConPedidosController();

    if ($r == "sucess") {
        error_log("ajaxCancelarPedidoConPedidos: Cancelación exitosa");
        http_response_code(200);
        echo json_encode([
            'success' => true,
            'message' => 'Mesa cancelada exitosamente'
        ]);
    } else {
        error_log("ajaxCancelarPedidoConPedidos: Error en cancelación");
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Error al cancelar la mesa'
        ]);
    }
} catch (Exception $e) {
    error_log("ajaxCancelarPedidoConPedidos: Excepción - " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error interno del servidor'
    ]);
}
?>
