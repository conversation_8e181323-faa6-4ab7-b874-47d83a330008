<?php

require_once "conexion.php";

class DatosPedidosCategorias {
    
    /*=============================================
    OBTENER PEDIDOS PENDIENTES POR CATEGORÍA (NUEVA LÓGICA)
    =============================================*/
    static public function obtenerPedidosPendientesModel($categoria) {
        try {
            // Nueva lógica: usar tabla pedidos_entregas_categoria
            $stmt = Conexion::conectar()->prepare("
                SELECT DISTINCT
                    p.id as pedido_id,
                    p.numero_pedido,
                    p.fecha_envio,
                    p.mesa_id,
                    m.nombre as mesa_numero,
                    COUNT(pvm.productos_id) as total_productos,
                    GROUP_CONCAT(
                        CONCAT(pr.nombre, ' x', pvm.cantidad)
                        ORDER BY pr.nombre
                        SEPARATOR ', '
                    ) as productos_detalle,
                    SUM(pvm.cantidad * pr.precio) as total_precio,
                    pec.estado as estado_categoria
                FROM pedidos p
                INNER JOIN mesas m ON p.mesa_id = m.id
                INNER JOIN pedidos_entregas_categoria pec ON p.id = pec.pedido_id
                INNER JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
                INNER JOIN productos pr ON pvm.productos_id = pr.id
                WHERE p.estado = 'enviado'
                AND pec.categoria = :categoria
                AND pec.estado = 'pendiente'
                AND pr.categoria = :categoria
                GROUP BY p.id, p.numero_pedido, p.fecha_envio, p.mesa_id, m.nombre, pec.estado
                ORDER BY p.fecha_envio ASC
            ");
            $stmt->bindParam(":categoria", $categoria, PDO::PARAM_STR);
            $stmt->execute();

            return $stmt->fetchAll(PDO::FETCH_ASSOC);

        } catch (Exception $e) {
            error_log("Error en obtenerPedidosPendientesModel: " . $e->getMessage());
            return [];
        }
    }
    
    /*=============================================
    OBTENER PEDIDOS ENTREGADOS DEL DÍA POR CATEGORÍA (NUEVA LÓGICA)
    =============================================*/
    static public function obtenerPedidosEntregadosDiaModel($categoria) {
        try {
            // Nueva lógica: usar tabla pedidos_entregas_categoria
            $stmt = Conexion::conectar()->prepare("
                SELECT DISTINCT
                    p.id as pedido_id,
                    p.numero_pedido,
                    p.fecha_envio,
                    pec.fecha_entrega,
                    p.mesa_id,
                    m.nombre as mesa_numero,
                    COUNT(pvm.productos_id) as total_productos,
                    GROUP_CONCAT(
                        CONCAT(pr.nombre, ' x', pvm.cantidad)
                        ORDER BY pr.nombre
                        SEPARATOR ', '
                    ) as productos_detalle,
                    SUM(pvm.cantidad * pr.precio) as total_precio,
                    CONCAT(u.nombres, ' ', u.apellidos) as usuario_entrega
                FROM pedidos p
                INNER JOIN mesas m ON p.mesa_id = m.id
                INNER JOIN pedidos_entregas_categoria pec ON p.id = pec.pedido_id
                INNER JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
                INNER JOIN productos pr ON pvm.productos_id = pr.id
                LEFT JOIN personas u ON pec.usuario_entrega = u.id
                WHERE pec.categoria = :categoria
                AND pec.estado = 'entregado'
                AND DATE(pec.fecha_entrega) = CURDATE()
                AND pr.categoria = :categoria
                GROUP BY p.id, p.numero_pedido, p.fecha_envio, pec.fecha_entrega, p.mesa_id, m.nombre, u.nombres, u.apellidos
                ORDER BY pec.fecha_entrega DESC
            ");

            $stmt->bindParam(":categoria", $categoria, PDO::PARAM_STR);
            $stmt->execute();

            return $stmt->fetchAll(PDO::FETCH_ASSOC);

        } catch (Exception $e) {
            error_log("Error en obtenerPedidosEntregadosDiaModel: " . $e->getMessage());
            return [];
        }
    }
    
    /*=============================================
    MARCAR PEDIDO COMO ENTREGADO POR CATEGORÍA (NUEVA LÓGICA)
    =============================================*/
    static public function marcarPedidoEntregadoCategoriaModel($pedido_id, $categoria, $usuario_id) {
        try {
            // Log para debug
            error_log("marcarPedidoEntregadoCategoriaModel: Iniciando para pedido_id={$pedido_id}, categoria={$categoria}, usuario_id={$usuario_id}");

            // Verificar que el pedido existe y está en estado 'enviado'
            $check_stmt = Conexion::conectar()->prepare("SELECT id, estado FROM pedidos WHERE id = ?");
            $check_stmt->execute([$pedido_id]);
            $pedido = $check_stmt->fetch(PDO::FETCH_ASSOC);

            if (!$pedido) {
                error_log("marcarPedidoEntregadoCategoriaModel: Pedido {$pedido_id} no encontrado");
                return "error_pedido_no_encontrado";
            }

            if ($pedido['estado'] !== 'enviado') {
                error_log("marcarPedidoEntregadoCategoriaModel: Pedido {$pedido_id} no está en estado 'enviado', estado actual: {$pedido['estado']}");
                return "error_estado_incorrecto";
            }

            // Verificar que existe el registro en pedidos_entregas_categoria
            $check_categoria_stmt = Conexion::conectar()->prepare("
                SELECT id, estado FROM pedidos_entregas_categoria
                WHERE pedido_id = ? AND categoria = ?
            ");
            $check_categoria_stmt->execute([$pedido_id, $categoria]);
            $entrega_categoria = $check_categoria_stmt->fetch(PDO::FETCH_ASSOC);

            if (!$entrega_categoria) {
                error_log("marcarPedidoEntregadoCategoriaModel: No existe registro para pedido {$pedido_id} categoria {$categoria}");
                return "error_categoria_no_encontrada";
            }

            if ($entrega_categoria['estado'] === 'entregado') {
                error_log("marcarPedidoEntregadoCategoriaModel: Categoría {$categoria} del pedido {$pedido_id} ya está entregada");
                return "error_ya_entregado";
            }

            error_log("marcarPedidoEntregadoCategoriaModel: Procediendo con UPDATE de categoría");

            // Marcar la categoría como entregada
            $stmt = Conexion::conectar()->prepare("
                UPDATE pedidos_entregas_categoria
                SET estado = 'entregado',
                    fecha_entrega = NOW(),
                    usuario_entrega = :usuario_id
                WHERE pedido_id = :pedido_id
                AND categoria = :categoria
                AND estado = 'pendiente'
            ");

            $stmt->bindParam(":pedido_id", $pedido_id, PDO::PARAM_INT);
            $stmt->bindParam(":categoria", $categoria, PDO::PARAM_STR);
            $stmt->bindParam(":usuario_id", $usuario_id, PDO::PARAM_INT);

            $execute_result = $stmt->execute();
            $affected_rows = $stmt->rowCount();

            error_log("marcarPedidoEntregadoCategoriaModel: Execute result={$execute_result}, affected_rows={$affected_rows}");

            if ($execute_result && $affected_rows > 0) {
                error_log("marcarPedidoEntregadoCategoriaModel: UPDATE exitoso, registrando historial");

                // Registrar en historial
                $historial_result = self::registrarHistorialModel(
                    $pedido_id,
                    'enviado',
                    'entregado_' . $categoria,
                    $usuario_id,
                    "Categoría {$categoria} marcada como entregada"
                );

                error_log("marcarPedidoEntregadoCategoriaModel: Historial result={$historial_result}");

                // El trigger se encargará de marcar el pedido completo como entregado si todas las categorías están listas

                return "success";
            } else {
                error_log("marcarPedidoEntregadoCategoriaModel: UPDATE falló - execute_result={$execute_result}, affected_rows={$affected_rows}");
                return "error_update_failed";
            }

        } catch (Exception $e) {
            error_log("marcarPedidoEntregadoCategoriaModel: Excepción capturada: " . $e->getMessage());
            error_log("marcarPedidoEntregadoCategoriaModel: Stack trace: " . $e->getTraceAsString());
            return "error_exception";
        }
    }

    /*=============================================
    MARCAR PEDIDO COMO ENTREGADO (FUNCIÓN LEGACY - MANTENER COMPATIBILIDAD)
    =============================================*/
    static public function marcarPedidoEntregadoModel($pedido_id, $usuario_id) {
        // Esta función ahora redirige a la nueva lógica
        // Por defecto, intentamos determinar la categoría desde el contexto
        // Si no se puede determinar, marcamos todas las categorías

        try {
            // Obtener todas las categorías del pedido
            $stmt = Conexion::conectar()->prepare("
                SELECT DISTINCT categoria
                FROM pedidos_entregas_categoria
                WHERE pedido_id = ? AND estado = 'pendiente'
            ");
            $stmt->execute([$pedido_id]);
            $categorias = $stmt->fetchAll(PDO::FETCH_COLUMN);

            if (empty($categorias)) {
                return "error_no_categorias_pendientes";
            }

            // Marcar todas las categorías como entregadas
            $success_count = 0;
            foreach ($categorias as $categoria) {
                $result = self::marcarPedidoEntregadoCategoriaModel($pedido_id, $categoria, $usuario_id);
                if ($result === "success") {
                    $success_count++;
                }
            }

            return $success_count > 0 ? "success" : "error";

        } catch (Exception $e) {
            error_log("marcarPedidoEntregadoModel (legacy): " . $e->getMessage());
            return "error";
        }
    }
    
    /*=============================================
    REGISTRAR HISTORIAL DE CAMBIOS
    =============================================*/
    static public function registrarHistorialModel($pedido_id, $estado_anterior, $estado_nuevo, $usuario_id, $observaciones = null) {
        try {
            $stmt = Conexion::conectar()->prepare("
                INSERT INTO pedidos_historial 
                (pedido_id, estado_anterior, estado_nuevo, usuario_id, observaciones) 
                VALUES (:pedido_id, :estado_anterior, :estado_nuevo, :usuario_id, :observaciones)
            ");
            
            $stmt->bindParam(":pedido_id", $pedido_id, PDO::PARAM_INT);
            $stmt->bindParam(":estado_anterior", $estado_anterior, PDO::PARAM_STR);
            $stmt->bindParam(":estado_nuevo", $estado_nuevo, PDO::PARAM_STR);
            $stmt->bindParam(":usuario_id", $usuario_id, PDO::PARAM_INT);
            $stmt->bindParam(":observaciones", $observaciones, PDO::PARAM_STR);
            
            return $stmt->execute();
            
        } catch (Exception $e) {
            error_log("Error en registrarHistorialModel: " . $e->getMessage());
            return false;
        }
    }
    
    /*=============================================
    OBTENER PRODUCTOS ESPECÍFICOS DE UN PEDIDO POR CATEGORÍA
    =============================================*/
    static public function obtenerProductosPedidoCategoriaModel($pedido_id, $categoria) {
        try {
            $stmt = Conexion::conectar()->prepare("
                SELECT 
                    pr.id as producto_id,
                    pr.nombre as producto_nombre,
                    pr.precio,
                    pvm.cantidad,
                    pvm.nota,
                    (pvm.cantidad * pr.precio) as subtotal
                FROM producto_vendido_mesa pvm
                INNER JOIN productos pr ON pvm.productos_id = pr.id
                WHERE pvm.pedidos_id = :pedido_id
                AND pr.categoria = :categoria
                ORDER BY pr.nombre
            ");
            
            $stmt->bindParam(":pedido_id", $pedido_id, PDO::PARAM_INT);
            $stmt->bindParam(":categoria", $categoria, PDO::PARAM_STR);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Error en obtenerProductosPedidoCategoriaModel: " . $e->getMessage());
            return [];
        }
    }
    
    /*=============================================
    OBTENER ESTADÍSTICAS DEL DÍA POR CATEGORÍA (NUEVA LÓGICA)
    =============================================*/
    static public function obtenerEstadisticasDiaModel($categoria) {
        try {
            // Nueva lógica: usar tabla pedidos_entregas_categoria
            $stmt = Conexion::conectar()->prepare("
                SELECT
                    COUNT(CASE WHEN pec.estado = 'pendiente' THEN 1 END) as pendientes,
                    COUNT(CASE WHEN pec.estado = 'entregado' AND DATE(pec.fecha_entrega) = CURDATE() THEN 1 END) as entregados_hoy,
                    COALESCE(SUM(CASE WHEN pec.estado = 'entregado' AND DATE(pec.fecha_entrega) = CURDATE() THEN pvm.cantidad * pr.precio ELSE 0 END), 0) as total_ventas_hoy
                FROM pedidos_entregas_categoria pec
                INNER JOIN pedidos p ON pec.pedido_id = p.id
                INNER JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
                INNER JOIN productos pr ON pvm.productos_id = pr.id
                WHERE pec.categoria = :categoria
                AND pr.categoria = :categoria
                AND p.estado IN ('enviado', 'entregado')
            ");
            $stmt->bindParam(":categoria", $categoria, PDO::PARAM_STR);
            $stmt->execute();

            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            // Asegurar que los valores no sean null
            return [
                'pendientes' => (int)($result['pendientes'] ?? 0),
                'entregados_hoy' => (int)($result['entregados_hoy'] ?? 0),
                'total_ventas_hoy' => (float)($result['total_ventas_hoy'] ?? 0)
            ];

        } catch (Exception $e) {
            error_log("Error en obtenerEstadisticasDiaModel: " . $e->getMessage());
            return [
                'pendientes' => 0,
                'entregados_hoy' => 0,
                'total_ventas_hoy' => 0
            ];
        }
    }
}

?>
