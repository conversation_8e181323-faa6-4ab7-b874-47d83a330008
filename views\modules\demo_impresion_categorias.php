<?php
// Demo del sistema de impresión por categorías - Sin dependencias de BD
echo "<h1>🎯 Demo Sistema de Impresión por Categorías</h1>";
echo "<p><strong>Demostración del nuevo sistema de botones dinámicos</strong></p>";

// Datos de ejemplo simulados (sin base de datos)
$pedido_demo = [
    'id' => 999,
    'numero_pedido' => 'DEMO001',
    'mesa_numero' => '5',
    'fecha_pedido' => date('Y-m-d H:i:s'),
    'estado' => 'enviado'
];

$productos_demo = [
    // Productos de BAR
    [
        'nombre' => 'Cerveza Corona',
        'categoria_producto' => 'bebidas',
        'categoria_impresora' => 'bar',
        'cantidad' => 2,
        'precio' => 8000,
        'subtotal' => 16000
    ],
    [
        'nombre' => 'Whisky Etiqueta Negra',
        'categoria_producto' => 'licores',
        'categoria_impresora' => 'bar',
        'cantidad' => 1,
        'precio' => 15000,
        'subtotal' => 15000
    ],
    
    // Productos de COCINA
    [
        'nombre' => 'Bandeja Paisa',
        'categoria_producto' => 'comidas',
        'categoria_impresora' => 'cocina',
        'cantidad' => 1,
        'precio' => 25000,
        'subtotal' => 25000
    ],
    [
        'nombre' => 'Sancocho de Gallina',
        'categoria_producto' => 'sopas',
        'categoria_impresora' => 'cocina',
        'cantidad' => 2,
        'precio' => 18000,
        'subtotal' => 36000
    ],
    
    // Productos de ASADOS
    [
        'nombre' => 'Churrasco',
        'categoria_producto' => 'carnes',
        'categoria_impresora' => 'asados',
        'cantidad' => 1,
        'precio' => 35000,
        'subtotal' => 35000
    ],
    [
        'nombre' => 'Costillas BBQ',
        'categoria_producto' => 'parrilla',
        'categoria_impresora' => 'asados',
        'cantidad' => 1,
        'precio' => 28000,
        'subtotal' => 28000
    ]
];

// Función para agrupar productos por categoría
function agruparPorCategoria($productos) {
    $categorias = [];
    
    foreach ($productos as $producto) {
        $cat = $producto['categoria_impresora'];
        
        if (!isset($categorias[$cat])) {
            $categorias[$cat] = [
                'nombre' => obtenerNombreCategoria($cat),
                'productos' => [],
                'total_productos' => 0,
                'total_precio' => 0
            ];
        }
        
        $categorias[$cat]['productos'][] = $producto;
        $categorias[$cat]['total_productos'] += $producto['cantidad'];
        $categorias[$cat]['total_precio'] += $producto['subtotal'];
    }
    
    return $categorias;
}

function obtenerNombreCategoria($categoria) {
    $nombres = [
        'bar' => '🍺 BAR',
        'cocina' => '🍳 COCINA',
        'asados' => '🥩 ASADOS'
    ];
    return $nombres[$categoria] ?? strtoupper($categoria);
}

function generarTicket($pedido, $productos, $categoria) {
    $contenido = "";
    
    // Encabezado
    $contenido .= "MESA: " . $pedido['mesa_numero'] . "\n";
    $contenido .= "PEDIDO: " . $pedido['numero_pedido'] . "\n";
    $contenido .= "CATEGORIA: " . strtoupper($categoria) . "\n";
    $contenido .= "FECHA: " . date('d/m/Y H:i', strtotime($pedido['fecha_pedido'])) . "\n";
    $contenido .= "ESTADO: " . strtoupper($pedido['estado']) . "\n";
    $contenido .= "--------------------------------\n";
    
    // Productos
    $total_categoria = 0;
    foreach ($productos as $producto) {
        $cantidad = $producto['cantidad'];
        $nombre = substr($producto['nombre'], 0, 25);
        $subtotal = $producto['subtotal'];
        $total_categoria += $subtotal;
        
        $contenido .= sprintf("%-25s %2d\n", $nombre, $cantidad);
        $contenido .= sprintf("  $%s\n", number_format($subtotal, 0));
    }
    
    // Total de la categoría
    $contenido .= "--------------------------------\n";
    $contenido .= sprintf("TOTAL %s: $%s\n", strtoupper($categoria), number_format($total_categoria, 0));
    $contenido .= "--------------------------------\n";
    
    // Pie
    $contenido .= "Hora impresion: " . date('H:i:s') . "\n";
    
    return $contenido;
}

// Agrupar productos por categoría
$categorias = agruparPorCategoria($productos_demo);

echo "<div style='background-color: #d1ecf1; padding: 15px; border-left: 4px solid #17a2b8; margin-bottom: 20px;'>";
echo "<h4>📋 Información del Pedido Demo</h4>";
echo "<p><strong>Pedido:</strong> {$pedido_demo['numero_pedido']}</p>";
echo "<p><strong>Mesa:</strong> {$pedido_demo['mesa_numero']}</p>";
echo "<p><strong>Total productos:</strong> " . count($productos_demo) . "</p>";
echo "<p><strong>Categorías:</strong> " . count($categorias) . "</p>";
echo "<p><strong>Tu IP:</strong> " . $_SERVER['REMOTE_ADDR'] . "</p>";
echo "</div>";

// Procesar impresión
if (isset($_POST['imprimir_categoria'])) {
    $categoria_seleccionada = $_POST['categoria'];
    
    echo "<div style='border: 1px solid #007bff; padding: 20px; margin: 15px 0; border-radius: 8px;'>";
    echo "<h4>🖨️ Preparando impresión para: " . obtenerNombreCategoria($categoria_seleccionada) . "</h4>";
    
    // Filtrar productos de la categoría seleccionada
    $productos_categoria = array_filter($productos_demo, function($p) use ($categoria_seleccionada) {
        return $p['categoria_impresora'] === $categoria_seleccionada;
    });
    
    if (!empty($productos_categoria)) {
        $contenido_impresion = generarTicket($pedido_demo, $productos_categoria, $categoria_seleccionada);
        
        echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0;'>";
        echo "<h5>✅ Contenido Generado</h5>";
        echo "<p><strong>Productos incluidos:</strong> " . count($productos_categoria) . "</p>";
        echo "</div>";
        
        // Mostrar contenido que se va a imprimir
        echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h5>📄 Contenido a Imprimir:</h5>";
        echo "<pre style='background-color: white; padding: 10px; border: 1px solid #ddd; font-family: monospace; white-space: pre-wrap;'>";
        echo htmlspecialchars($contenido_impresion);
        echo "</pre>";
        echo "</div>";
        
        // JavaScript para abrir ventana de impresión
        echo "<script>
        function abrirVentanaImpresion() {
            const contenido = `" . addslashes($contenido_impresion) . "`;
            const ventana = window.open('', '_blank', 'width=400,height=600,scrollbars=yes');
            
            ventana.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Impresión Demo - {$categoria_seleccionada}</title>
                    <style>
                        body { 
                            font-family: 'Courier New', monospace; 
                            font-size: 12px; 
                            margin: 20px; 
                            white-space: pre-wrap;
                        }
                        @media print {
                            body { margin: 0; }
                        }
                    </style>
                </head>
                <body>
                    \${contenido}
                    <script>
                        window.onload = function() {
                            window.print();
                        };
                    </scr\ipt>
                </body>
                </html>
            `);
            
            ventana.document.close();
        }
        </script>";
        
        echo "<div style='text-align: center; margin: 20px 0;'>";
        echo "<button onclick='abrirVentanaImpresion()' style='background-color: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold; font-size: 16px;'>🖨️ ABRIR VENTANA DE IMPRESIÓN</button>";
        echo "</div>";
        
        echo "<div style='background-color: #e7f3ff; padding: 15px; border-left: 4px solid #007bff; margin: 15px 0;'>";
        echo "<h5>💡 Instrucciones:</h5>";
        echo "<ol>";
        echo "<li>Se abrirá una nueva ventana con el contenido</li>";
        echo "<li>La ventana se enviará automáticamente a imprimir</li>";
        echo "<li>Selecciona tu impresora en el diálogo</li>";
        echo "<li>Confirma la impresión</li>";
        echo "</ol>";
        echo "</div>";
        
    } else {
        echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
        echo "<h5>❌ Error</h5>";
        echo "<p>No hay productos para la categoría seleccionada</p>";
        echo "</div>";
    }
    
    echo "</div>";
}

// Mostrar categorías disponibles con botones
echo "<h2>📂 Categorías Disponibles para Impresión</h2>";

foreach ($categorias as $categoria => $info) {
    $color_categoria = [
        'bar' => '#17a2b8',
        'cocina' => '#28a745', 
        'asados' => '#dc3545'
    ];
    
    $color = $color_categoria[$categoria] ?? '#6c757d';
    
    echo "<div style='background-color: white; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 5px solid $color; box-shadow: 0 2px 4px rgba(0,0,0,0.1);'>";
    
    echo "<h4 style='color: $color; margin-bottom: 15px;'>{$info['nombre']}</h4>";
    
    echo "<div style='margin-bottom: 15px;'>";
    echo "<p><strong>Productos:</strong> {$info['total_productos']} items</p>";
    echo "<p><strong>Total:</strong> $" . number_format($info['total_precio'], 0) . "</p>";
    echo "</div>";
    
    // Lista de productos
    echo "<div style='background-color: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 15px;'>";
    echo "<h6>📋 Productos incluidos:</h6>";
    echo "<ul style='margin: 5px 0; padding-left: 20px;'>";
    foreach ($info['productos'] as $producto) {
        echo "<li>{$producto['nombre']} x{$producto['cantidad']} - $" . number_format($producto['subtotal'], 0) . "</li>";
    }
    echo "</ul>";
    echo "</div>";
    
    // Botón de impresión
    echo "<form method='POST' style='margin: 0;'>";
    echo "<input type='hidden' name='categoria' value='$categoria'>";
    echo "<button type='submit' name='imprimir_categoria' style='background-color: $color; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold; width: 100%;'>🖨️ IMPRIMIR {$info['nombre']}</button>";
    echo "</form>";
    
    echo "</div>";
}

// Información del sistema
echo "<h2>💡 Información del Sistema</h2>";
echo "<div style='background-color: #e7f3ff; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>🔧 Cómo Funciona este Demo:</h4>";
echo "<ol>";
echo "<li><strong>Datos simulados:</strong> Usa productos de ejemplo sin base de datos</li>";
echo "<li><strong>Agrupación automática:</strong> Separa productos por BAR, COCINA, ASADOS</li>";
echo "<li><strong>Botones dinámicos:</strong> Solo muestra categorías que tienen productos</li>";
echo "<li><strong>Impresión real:</strong> Abre ventana de impresión del navegador</li>";
echo "<li><strong>Compatible:</strong> Funciona en cualquier dispositivo</li>";
echo "</ol>";

echo "<h4>✅ Ventajas del Sistema:</h4>";
echo "<ul>";
echo "<li>🎯 <strong>Control manual:</strong> El operario decide qué y cuándo imprimir</li>";
echo "<li>🚀 <strong>Simplicidad:</strong> Solo botones, sin configuraciones</li>";
echo "<li>🔒 <strong>Confiabilidad:</strong> Usa impresión nativa del navegador</li>";
echo "<li>📱 <strong>Universal:</strong> Funciona en móviles, tablets y PC</li>";
echo "<li>⚡ <strong>Velocidad:</strong> Sin dependencias de red</li>";
echo "</ul>";

echo "<h4>🎯 En Producción:</h4>";
echo "<ul>";
echo "<li>Los datos vendrían de la base de datos real</li>";
echo "<li>Se integraría con el sistema de pedidos existente</li>";
echo "<li>Se registrarían logs de impresión</li>";
echo "<li>Se podría reimprimir cualquier categoría</li>";
echo "</ul>";
echo "</div>";

// Botones de navegación
echo "<p style='margin-top: 30px;'>";
echo "<a href='test_impresion_pedido_categorias.php' style='background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>← Test con BD</a>";
echo "<a href='test_impresion_socket.php' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🔌 Test Sockets</a>";
echo "<a href='index.php?action=registroPmesa&ida=1' style='background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🪑 Mesa Real</a>";
echo "</p>";
?>
