<?php
// Test específico para verificar el cálculo de cambio en registroPmesa.php
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
if (!isset($_SESSION["usuario"])) {
    $_SESSION["usuario"] = 1;
    $_SESSION["tipo_usuario"] = 1;
    $_SESSION["perfil"] = "administrador";
}

$mesaId = isset($_GET['mesa']) ? $_GET['mesa'] : 1;
$_SESSION["mesa"] = $mesaId;

echo "<h1>🧮 Test Cálculo Cambio en registroPmesa.php - Mesa $mesaId</h1>";
echo "<p>Este test verifica que el cálculo de cambio funcione igual que en la interfaz real.</p>";

// Simular la estructura exacta de registroPmesa.php
?>

<div class="row">
    <div class="col-md-9">
        <form name="calculo" method="post">
            <div><h3>Facturar Venta</h3></div><br>
            Cliente No Cedula: <input type="text" name="pcedula" value="12345678" id="pcedula"><br>
            <input type="text" id="mesa" name="mesa" value="<?=$mesaId?>" readonly>
            
            <div align="right">
                <label>Tipo de Pago:</label>
                <select name="pago" id="pago">
                    <option value="1">Efectivo</option>
                    <option value="2">Credito</option>
                </select>
            </div>
            
            <table border="1" class="table table-hover">
                <thead>
                    <tr>
                        <th>SIMULACIÓN</th>
                        <th>PRODUCTOS</th>
                        <th>PARA</th>
                        <th>FACTURAR</th>
                        <th>TEST</th>
                        <th>CAMBIO</th>
                        <th>TOTAL</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Simular productos -->
                    <tr>
                        <td>001</td>
                        <td>Producto Test 1</td>
                        <td>2025-01-01</td>
                        <td>$25,000</td>
                        <td>0</td>
                        <td>2</td>
                        <td>$50,000</td>
                        <td></td>
                    </tr>
                    
                    <!-- Total calculado automáticamente -->
                    <tr style="background-color: #d4edda;">
                        <td colspan="4"><strong>TOTAL A FACTURAR (Simulado)</strong></td>
                        <td colspan="1"><strong>-$0</strong><input type="hidden" name="totalDescuento" id="totalDescuento" value="0"></td>
                        <td colspan="1"><strong>$50,000</strong><input type="hidden" name="total" id="total" value="50000"></td>
                        <td colspan="2"><strong>$50,000</strong></td>
                    </tr>

                    <tr>
                        <td colspan="6"><h5><b>Total a Pagar</b></h5></td>
                        <td colspan="3"><input type="text" placeholder="$" name="pagar" id="pagar" onkeyup="funcion_calcular();" value="0" readonly></td>
                    </tr>
                    <tr>
                        <td colspan="6"><h5><b>Recibo en efectivo</b></h5></td>
                        <td colspan="3"><input type="text" placeholder="$" name="efectivo" id="efectivo" onkeyup="funcion_calcular();" value="0"></td>
                    </tr>
                    <tr>
                        <td colspan="4"><b>Bold</b> <input type="text" placeholder="$" name="tarjeta" id="tarjeta" onkeyup="funcion_calcular();" value="0"></td>
                        <td colspan="2"><label><b>Bancolombia</b></label></td>
                        <td colspan="3"><input type="text" placeholder="$" name="bancolombia" id="bancolombia" onkeyup="funcion_calcular();" value="0"></td>
                    </tr>
                    <tr>
                        <td colspan="4"><b>Nequi</b> <input type="text" placeholder="$" name="nequi" id="nequi" onkeyup="funcion_calcular();" value="0"></td>
                        <td colspan="2"><label>Daviplata</label></td>
                        <td colspan="3"><input type="text" placeholder="$" name="daviplata" id="daviplata" onkeyup="funcion_calcular();" value="0"></td>
                    </tr>
                    <tr>
                        <td colspan="6"><h5><b>Propina</b></h5></td>
                        <td colspan="3"><input type="text" placeholder="$" name="propina" id="propina" onkeyup="funcion_calcular();" value="0"></td>
                    </tr>
                    <tr>
                        <td colspan="6"><h5><b>Cambio</b></h5></td>
                        <td colspan="2"><input type="text" placeholder="$" name="cambio" id="cambio" readonly></td>
                    </tr>
                </tbody>
            </table>
            
            <div id="resultado" style="background: #f8f9fa; padding: 15px; margin: 15px 0; border-radius: 5px;"></div>
            
            <h3>🧪 Tests Rápidos</h3>
            <button type="button" onclick="testPagoExacto()" style="background: #28a745; color: white; padding: 10px; margin: 5px;">✅ Pago Exacto</button>
            <button type="button" onclick="testPagoConVuelto()" style="background: #17a2b8; color: white; padding: 10px; margin: 5px;">💰 Con Vuelto</button>
            <button type="button" onclick="testPagoInsuficiente()" style="background: #dc3545; color: white; padding: 10px; margin: 5px;">❌ Insuficiente</button>
            <button type="button" onclick="test5Metodos()" style="background: #6f42c1; color: white; padding: 10px; margin: 5px;">🎯 5 Métodos</button>
            <button type="button" onclick="limpiar()" style="background: #6c757d; color: white; padding: 10px; margin: 5px;">🧹 Limpiar</button>
        </form>
    </div>
</div>

<script type="text/javascript">
// Función formatCurrency EXACTA de registroPmesa.php
function formatCurrency(input) {
  if (!input || !input.value) {
    return 0;
  }
  
  let cleanValue = input.value.replace(/\D/g, '');
  let value = parseInt(cleanValue);
  
  if (isNaN(value)) {
    value = 0;
  }
  
  let options = { style: 'currency', currency: 'COP', minimumFractionDigits: 0, maximumFractionDigits: 0 };
  let formatter = new Intl.NumberFormat('es-CO', options);
  input.value = formatter.format(value);

  return value;
}

// Función funcion_calcular EXACTA de registroPmesa.php
function funcion_calcular() {
    // CORREGIDO: Lógica original de cálculo de cambio
    let efectivo = document.getElementById('efectivo') ? formatCurrency(document.getElementById('efectivo')) : 0;
    let propina = document.getElementById('propina') ? formatCurrency(document.getElementById('propina')) : 0;
    let tarjeta = document.getElementById('tarjeta') ? formatCurrency(document.getElementById('tarjeta')) : 0;
    let nequi = document.getElementById('nequi') ? formatCurrency(document.getElementById('nequi')) : 0;
    let daviplata = document.getElementById('daviplata') ? formatCurrency(document.getElementById('daviplata')) : 0;
    let bancolombia = document.getElementById('bancolombia') ? formatCurrency(document.getElementById('bancolombia')) : 0;
    
    var totalDescuento = document.calculo && document.calculo.totalDescuento ? parseInt(document.calculo.totalDescuento.value) || 0 : 0;
    var total = document.calculo && document.calculo.total ? parseInt(document.calculo.total.value) || 0 : 0;
    
    // Asegurar que todos los valores sean números válidos
    efectivo = isNaN(efectivo) ? 0 : efectivo;
    propina = isNaN(propina) ? 0 : propina;
    tarjeta = isNaN(tarjeta) ? 0 : tarjeta;
    nequi = isNaN(nequi) ? 0 : nequi;
    daviplata = isNaN(daviplata) ? 0 : daviplata;
    bancolombia = isNaN(bancolombia) ? 0 : bancolombia;
    totalDescuento = isNaN(totalDescuento) ? 0 : totalDescuento;
    total = isNaN(total) ? 0 : total;
    
    // RESTAURADO: Cálculo original del cambio
    // Cambio = Total Pagado - (Total Cuenta + Propina)
    var totalPagado = efectivo + tarjeta + nequi + daviplata + bancolombia + totalDescuento;
    var totalAPagar = total + propina;
    var cambio = totalPagado - totalAPagar;
    
    // Actualizar campo "Total a Pagar"
    if (document.calculo && document.calculo.pagar) {
        document.calculo.pagar.value = totalAPagar;
        if (document.getElementById('pagar')) {
            document.getElementById('pagar').value = totalAPagar;
            formatCurrency(document.getElementById('pagar'));
        }
    }
    
    // Actualizar campo "Cambio"
    if (document.calculo && document.calculo.cambio) {
        document.calculo.cambio.value = cambio;
        if (document.getElementById('cambio')) {
            document.getElementById('cambio').value = cambio;
            formatCurrency(document.getElementById('cambio'));
        }
    }
    
    // Mostrar resultado detallado
    mostrarResultado(totalPagado, totalAPagar, cambio);
}

function mostrarResultado(totalPagado, totalAPagar, cambio) {
    let html = '<h4>📊 Cálculo en Tiempo Real:</h4>';
    html += '<p><strong>Total Pagado:</strong> $' + totalPagado.toLocaleString() + '</p>';
    html += '<p><strong>Total a Pagar:</strong> $' + totalAPagar.toLocaleString() + '</p>';
    html += '<p><strong>Cambio:</strong> $' + cambio.toLocaleString();
    
    if (cambio > 0) {
        html += ' <span style="color: green; font-weight: bold;">✅ (Hay vuelto)</span>';
    } else if (cambio < 0) {
        html += ' <span style="color: red; font-weight: bold;">❌ (Falta dinero)</span>';
    } else {
        html += ' <span style="color: blue; font-weight: bold;">✅ (Pago exacto)</span>';
    }
    html += '</p>';
    
    document.getElementById('resultado').innerHTML = html;
}

// Tests predefinidos
function testPagoExacto() {
    limpiar();
    document.getElementById('efectivo').value = '55000';
    document.getElementById('propina').value = '5000';
    funcion_calcular();
}

function testPagoConVuelto() {
    limpiar();
    document.getElementById('efectivo').value = '60000';
    document.getElementById('propina').value = '5000';
    funcion_calcular();
}

function testPagoInsuficiente() {
    limpiar();
    document.getElementById('efectivo').value = '40000';
    document.getElementById('propina').value = '5000';
    funcion_calcular();
}

function test5Metodos() {
    limpiar();
    document.getElementById('efectivo').value = '11000';
    document.getElementById('tarjeta').value = '11000';
    document.getElementById('nequi').value = '11000';
    document.getElementById('daviplata').value = '11000';
    document.getElementById('bancolombia').value = '11000';
    document.getElementById('propina').value = '5000';
    funcion_calcular();
}

function limpiar() {
    document.getElementById('efectivo').value = '0';
    document.getElementById('tarjeta').value = '0';
    document.getElementById('nequi').value = '0';
    document.getElementById('daviplata').value = '0';
    document.getElementById('bancolombia').value = '0';
    document.getElementById('propina').value = '0';
    funcion_calcular();
}

// Ejecutar cálculo inicial
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(function() {
        funcion_calcular();
    }, 500);
});
</script>

<style>
.table { width: 100%; border-collapse: collapse; }
.table th, .table td { padding: 8px; border: 1px solid #ddd; }
.table-hover tbody tr:hover { background-color: #f5f5f5; }
</style>

<br><a href="index.php?action=registroPmesa&ida=<?=$mesaId?>" style="background: #007bff; color: white; padding: 10px; text-decoration: none;">🔙 Volver a Mesa <?=$mesaId?></a>
<br><a href="test_registropmesa_cambio.php?mesa=<?=$mesaId?>" style="background: #6c757d; color: white; padding: 10px; text-decoration: none; margin: 5px;">🔄 Actualizar</a>
