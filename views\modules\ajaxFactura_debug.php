<?php
// Versión de debug de ajaxFactura.php para diagnosticar error 500
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// OPTIMIZACIÓN PARA MESAS PESADAS
ini_set('max_execution_time', 300); // 5 minutos
ini_set('memory_limit', '512M');

echo "<!-- DEBUG: Iniciando ajaxFactura_debug.php -->\n";

try {
    echo "<!-- DEBUG: Incluyendo archivos -->\n";
    
    require_once "../../models/crud.php";
    echo "<!-- DEBUG: crud.php incluido -->\n";
    
    require_once "../../models/crudFacturaAja.php";
    echo "<!-- DEBUG: crudFacturaAja.php incluido -->\n";
    
    require_once "../../controllers/controller.php";
    echo "<!-- DEBUG: controller.php incluido -->\n";
    
    require_once "../../controllers/controllerFacturaAja.php";
    echo "<!-- DEBUG: controllerFacturaAja.php incluido -->\n";

    // Log para debug de mesas pesadas
    error_log("FACTURACIÓN DEBUG INICIADA - Mesa: " . ($_POST['mesa'] ?? 'N/A') . " - Timestamp: " . date('Y-m-d H:i:s'));
    echo "<!-- DEBUG: Log inicial registrado -->\n";

    // COMPATIBILIDAD: Manejar tanto el formato original como el nuevo
    $nequi = isset($_POST['nequi']) ? $_POST['nequi'] : 0;
    $daviplata = isset($_POST['daviplata']) ? $_POST['daviplata'] : 0;
    $bancolombia = isset($_POST['bancolombia']) ? $_POST['bancolombia'] : 0;
    $tipoTarjeta = isset($_POST['tipoTarjeta']) ? $_POST['tipoTarjeta'] : 'credito';
    $optimizada = isset($_POST['optimizada']) ? $_POST['optimizada'] : false;

    $queryString = $_POST['efectivo'] ?? 0;
    $tarjeta = $_POST['tarjeta'] ?? 0;
    $queryString1 = $_POST['pago'] ?? 1;
    $queryString2 = $_POST['pcedula'] ?? '';
    $queryString3 = $_POST['total'] ?? 0;
    $totalDescuento = isset($_POST['totalDescuento']) ? $_POST['totalDescuento'] : 0;
    $propina = isset($_POST['propina']) ? $_POST['propina'] : 0;
    $mesa = $_POST['mesa'] ?? 0;

    echo "<!-- DEBUG: Variables asignadas -->\n";
    echo "<!-- DEBUG: Mesa: $mesa, Total: $queryString3, Efectivo: $queryString -->\n";

    if($queryString1 == 1) {
        echo "<!-- DEBUG: Procesando pago en efectivo -->\n";
        
        $totalCuenta = $queryString3;
        $totalPagado = $queryString + $tarjeta + $nequi + $daviplata + $bancolombia + $totalDescuento;
        
        echo "<!-- DEBUG: Total cuenta: $totalCuenta, Total pagado: $totalPagado -->\n";
        
        error_log("FACTURACIÓN DEBUG - Mesa: $mesa, Total: $totalCuenta, Pagado: $totalPagado");

        if($totalPagado >= $totalCuenta) {
            echo "<!-- DEBUG: Validación de pago exitosa -->\n";
            
            $tiempoInicio = microtime(true);
            error_log("PROCESAMIENTO DEBUG INICIADO - Mesa: $mesa");

            try {
                echo "<!-- DEBUG: Creando controlador -->\n";
                $ajax = new controllerFacturaAja();
                echo "<!-- DEBUG: Controlador creado exitosamente -->\n";
                
                echo "<!-- DEBUG: Llamando facturaajaxController -->\n";
                $resultado = $ajax->facturaajaxController($queryString, $nequi, $daviplata, $bancolombia, $tarjeta, $queryString1, $queryString2, $propina, $mesa);
                echo "<!-- DEBUG: facturaajaxController completado -->\n";

                $tiempoFin = microtime(true);
                $tiempoTotal = round(($tiempoFin - $tiempoInicio) * 1000, 2);
                error_log("FACTURACIÓN DEBUG COMPLETADA - Mesa: $mesa, Tiempo: {$tiempoTotal}ms");

                // Respuesta exitosa explícita
                if ($optimizada) {
                    echo "success_debug_optimizada_mesa_$mesa";
                } else {
                    echo "success_debug_mesa_$mesa";
                }

            } catch (Exception $e) {
                echo "<!-- DEBUG: Error en controlador: " . $e->getMessage() . " -->\n";
                echo "<!-- DEBUG: Stack trace: " . $e->getTraceAsString() . " -->\n";
                error_log("ERROR DEBUG EN FACTURACIÓN - Mesa: $mesa, Error: " . $e->getMessage());
                error_log("ERROR DEBUG Stack trace: " . $e->getTraceAsString());
                echo "<script>alert('Error debug en facturación: " . addslashes($e->getMessage()) . "')</script>";
            }
        } else {
            echo "<!-- DEBUG: Pago insuficiente -->\n";
            $faltante = $totalCuenta - $totalPagado;
            error_log("PAGO DEBUG INSUFICIENTE - Mesa: $mesa, Falta: $faltante");
            echo "<script>alert('Pago insuficiente. Total: $" . number_format($totalCuenta) . ", Pagado: $" . number_format($totalPagado) . ", Falta: $" . number_format($faltante) . "')</script>";
        }
    } else {
        echo "<!-- DEBUG: Procesando pago a crédito -->\n";
        echo "<script>alert('Pago a crédito en modo debug')</script>";
        $ajax = new controllerFacturaAja();
        $ajax->facturaajaxController($queryString, $nequi, $daviplata, $bancolombia, $tarjeta, $queryString1, $queryString2, $propina, $mesa);
    }

} catch (ParseError $e) {
    echo "<!-- DEBUG: Error de sintaxis: " . $e->getMessage() . " -->\n";
    error_log("ERROR DEBUG SINTAXIS: " . $e->getMessage());
    http_response_code(500);
    echo "Error de sintaxis: " . $e->getMessage();
} catch (Error $e) {
    echo "<!-- DEBUG: Error fatal: " . $e->getMessage() . " -->\n";
    error_log("ERROR DEBUG FATAL: " . $e->getMessage());
    http_response_code(500);
    echo "Error fatal: " . $e->getMessage();
} catch (Exception $e) {
    echo "<!-- DEBUG: Excepción: " . $e->getMessage() . " -->\n";
    error_log("ERROR DEBUG EXCEPCIÓN: " . $e->getMessage());
    http_response_code(500);
    echo "Excepción: " . $e->getMessage();
}

echo "<!-- DEBUG: Finalizando ajaxFactura_debug.php -->\n";
?>
