<?php
// Test completo del sistema de impresión
echo "<h1>🖨️ Test Completo del Sistema de Impresión</h1>";
echo "<p><strong>Pruebas exhaustivas del sistema híbrido de impresión</strong></p>";

// Habilitar reporte de errores
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    require_once '../../models/conexion.php';
    $conexion = new Conexion();
    $pdo = $conexion->conectar();
    
    echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0;'>";
    echo "<h4>✅ Conexión a base de datos exitosa</h4>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
    echo "<h4>❌ Error de conexión a base de datos</h4>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
    exit;
}

// Verificar configuración del sistema híbrido
echo "<h2>🔧 1. Verificación del Sistema Híbrido</h2>";

try {
    // Verificar tabla config_impresion
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM config_impresion");
    $config_count = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    if ($config_count > 0) {
        echo "<p>✅ <strong>Tabla config_impresion:</strong> Configurada ($config_count registros)</p>";
        
        // Mostrar configuración actual
        $stmt = $pdo->query("SELECT clave, valor FROM config_impresion ORDER BY clave");
        $configs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div style='background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h5>📋 Configuración Actual:</h5>";
        echo "<table style='width: 100%; border-collapse: collapse;'>";
        foreach ($configs as $config) {
            echo "<tr>";
            echo "<td style='border: 1px solid #ddd; padding: 8px; font-weight: bold;'>{$config['clave']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$config['valor']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
    } else {
        echo "<p>❌ <strong>Tabla config_impresion:</strong> No configurada</p>";
    }
    
    // Verificar tabla categoria_impresora
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM categoria_impresora");
    $categoria_count = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    if ($categoria_count > 0) {
        echo "<p>✅ <strong>Tabla categoria_impresora:</strong> Configurada ($categoria_count mapeos)</p>";
    } else {
        echo "<p>❌ <strong>Tabla categoria_impresora:</strong> No configurada</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ <strong>Error verificando sistema híbrido:</strong> " . $e->getMessage() . "</p>";
}

// Verificar impresoras disponibles
echo "<h2>🖨️ 2. Impresoras Disponibles</h2>";

try {
    $stmt = $pdo->query("SELECT id, categoria, ip, puerto FROM impresoras ORDER BY categoria");
    $impresoras = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($impresoras) {
        echo "<div style='background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<table style='width: 100%; border-collapse: collapse;'>";
        echo "<tr style='background-color: #ffeaa7;'>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Categoría</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>IP</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Puerto</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Test Conectividad</th>";
        echo "</tr>";
        
        foreach ($impresoras as $impresora) {
            echo "<tr>";
            echo "<td style='border: 1px solid #ddd; padding: 8px; font-weight: bold;'>{$impresora['categoria']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$impresora['ip']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$impresora['puerto']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>";
            
            // Test de conectividad básico
            $fp = @fsockopen($impresora['ip'], $impresora['puerto'], $errno, $errstr, 5);
            if ($fp) {
                echo "<span style='color: green;'>✅ Conectada</span>";
                fclose($fp);
            } else {
                echo "<span style='color: red;'>❌ No conectada ($errno: $errstr)</span>";
            }
            
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
    } else {
        echo "<p>❌ <strong>No hay impresoras configuradas</strong></p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ <strong>Error verificando impresoras:</strong> " . $e->getMessage() . "</p>";
}

// Test de impresión por categoría
echo "<h2>🧪 3. Test de Impresión por Categoría</h2>";

if (isset($_POST['test_categoria'])) {
    $categoria = $_POST['categoria_test'];
    $contenido = $_POST['contenido_test'];
    
    echo "<div style='border: 1px solid #007bff; padding: 20px; margin: 15px 0; border-radius: 8px;'>";
    echo "<h4>🔄 Probando impresión en categoría: $categoria</h4>";
    
    try {
        // Buscar impresora para la categoría
        $stmt = $pdo->prepare("SELECT ip, puerto FROM impresoras WHERE categoria = :categoria");
        $stmt->bindParam(":categoria", $categoria, PDO::PARAM_STR);
        $stmt->execute();
        $impresora = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($impresora) {
            echo "<p>✅ <strong>Impresora encontrada:</strong> {$impresora['ip']}:{$impresora['puerto']}</p>";
            
            // Intentar conexión
            $fp = @fsockopen($impresora['ip'], $impresora['puerto'], $errno, $errstr, 10);
            if ($fp) {
                echo "<p>✅ <strong>Conexión exitosa</strong></p>";
                
                // Enviar contenido de prueba
                $datos_impresion = "\x1B\x40"; // Reset de impresora
                $datos_impresion .= "\x1B\x61\x01"; // Centrar texto
                $datos_impresion .= "=== TEST MACARENA ===\n";
                $datos_impresion .= "\x1B\x61\x00"; // Alinear izquierda
                $datos_impresion .= "Categoria: $categoria\n";
                $datos_impresion .= "Fecha: " . date('Y-m-d H:i:s') . "\n";
                $datos_impresion .= "Contenido:\n";
                $datos_impresion .= $contenido . "\n";
                $datos_impresion .= "===================\n\n\n";
                $datos_impresion .= "\x1D\x56\x42\x00"; // Cortar papel
                
                $resultado = fwrite($fp, $datos_impresion);
                fclose($fp);
                
                if ($resultado) {
                    echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0;'>";
                    echo "<h5>✅ ¡Impresión Exitosa!</h5>";
                    echo "<p>Se enviaron $resultado bytes a la impresora</p>";
                    echo "</div>";
                } else {
                    echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
                    echo "<h5>❌ Error al enviar datos</h5>";
                    echo "</div>";
                }
                
            } else {
                echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
                echo "<h5>❌ Error de conexión</h5>";
                echo "<p>No se pudo conectar a {$impresora['ip']}:{$impresora['puerto']}</p>";
                echo "<p>Error: $errno - $errstr</p>";
                echo "</div>";
            }
            
        } else {
            echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
            echo "<h5>❌ Impresora no encontrada</h5>";
            echo "<p>No hay impresora configurada para la categoría: $categoria</p>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
        echo "<h5>❌ Excepción durante la prueba</h5>";
        echo "<p>Error: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    
    echo "</div>";
}

// Formulario de test de impresión
echo "<form method='POST' style='background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>🎯 Probar Impresión:</h4>";

echo "<div style='margin: 15px 0;'>";
echo "<label><strong>Categoría de Impresora:</strong></label><br>";
echo "<select name='categoria_test' required style='padding: 8px; border: 1px solid #ccc; border-radius: 3px; width: 200px;'>";
echo "<option value=''>Seleccionar...</option>";
echo "<option value='bar'>Bar</option>";
echo "<option value='cocina'>Cocina</option>";
echo "<option value='asados'>Asados</option>";
echo "</select>";
echo "</div>";

echo "<div style='margin: 15px 0;'>";
echo "<label><strong>Contenido a Imprimir:</strong></label><br>";
echo "<textarea name='contenido_test' rows='5' cols='50' placeholder='Contenido de prueba...' required style='padding: 8px; border: 1px solid #ccc; border-radius: 3px;'>Mesa 1
Producto: Cerveza Corona
Cantidad: 2
Precio: $8.000
Total: $16.000</textarea>";
echo "</div>";

echo "<button type='submit' name='test_categoria' style='background-color: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold;'>🖨️ Probar Impresión</button>";
echo "</form>";

// Test de sistema híbrido
echo "<h2>🔄 4. Test del Sistema Híbrido</h2>";

if (isset($_POST['test_hibrido'])) {
    $categoria_producto = $_POST['categoria_producto_test'];
    
    echo "<div style='border: 1px solid #007bff; padding: 20px; margin: 15px 0; border-radius: 8px;'>";
    echo "<h4>🔄 Probando sistema híbrido para categoría: $categoria_producto</h4>";
    
    try {
        // Buscar mapeo de categoría
        $stmt = $pdo->prepare("SELECT impresora FROM categoria_impresora WHERE categoria = :categoria AND activo = 1");
        $stmt->bindParam(":categoria", $categoria_producto, PDO::PARAM_STR);
        $stmt->execute();
        $mapeo = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($mapeo) {
            echo "<p>✅ <strong>Mapeo encontrado:</strong> $categoria_producto → {$mapeo['impresora']}</p>";
            
            // Buscar impresora
            $stmt = $pdo->prepare("SELECT ip, puerto FROM impresoras WHERE categoria = :categoria");
            $stmt->bindParam(":categoria", $mapeo['impresora'], PDO::PARAM_STR);
            $stmt->execute();
            $impresora = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($impresora) {
                echo "<p>✅ <strong>Impresora encontrada:</strong> {$impresora['ip']}:{$impresora['puerto']}</p>";
                
                // Simular impresión híbrida
                echo "<p>🔄 <strong>Simulando sistema híbrido...</strong></p>";
                
                // Método 1: Intento directo
                echo "<p>📡 <strong>Método 1:</strong> Impresión directa...</p>";
                $fp = @fsockopen($impresora['ip'], $impresora['puerto'], $errno, $errstr, 5);
                if ($fp) {
                    echo "<p>✅ Impresión directa exitosa</p>";
                    fclose($fp);
                } else {
                    echo "<p>❌ Impresión directa falló: $errno - $errstr</p>";
                    
                    // Método 2: Fallback a proxy
                    echo "<p>🔄 <strong>Método 2:</strong> Fallback a proxy...</p>";
                    
                    // Obtener configuración del proxy
                    $stmt = $pdo->query("SELECT valor FROM config_impresion WHERE clave = 'ip_proxy'");
                    $ip_proxy = $stmt->fetch(PDO::FETCH_ASSOC)['valor'] ?? '***********';
                    
                    $stmt = $pdo->query("SELECT valor FROM config_impresion WHERE clave = 'puerto_proxy'");
                    $puerto_proxy = $stmt->fetch(PDO::FETCH_ASSOC)['valor'] ?? '3000';
                    
                    echo "<p>📡 Intentando conectar al proxy: $ip_proxy:$puerto_proxy</p>";
                    
                    // Simular llamada al proxy
                    $datos_proxy = array(
                        'impresora' => $mapeo['impresora'],
                        'contenido' => "Test híbrido para $categoria_producto",
                        'timestamp' => time()
                    );
                    
                    echo "<p>📤 Datos para proxy: " . json_encode($datos_proxy) . "</p>";
                    echo "<p>⚠️ <strong>Nota:</strong> En producción, aquí se haría una llamada HTTP al proxy</p>";
                }
                
            } else {
                echo "<p>❌ <strong>Error:</strong> No se encontró impresora para {$mapeo['impresora']}</p>";
            }
            
        } else {
            echo "<p>❌ <strong>Error:</strong> No hay mapeo configurado para la categoría $categoria_producto</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ <strong>Error:</strong> " . $e->getMessage() . "</p>";
    }
    
    echo "</div>";
}

// Formulario de test híbrido
echo "<form method='POST' style='background-color: #f0f8ff; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>🔄 Probar Sistema Híbrido:</h4>";

echo "<div style='margin: 15px 0;'>";
echo "<label><strong>Categoría de Producto:</strong></label><br>";
echo "<select name='categoria_producto_test' required style='padding: 8px; border: 1px solid #ccc; border-radius: 3px; width: 200px;'>";
echo "<option value=''>Seleccionar...</option>";

// Obtener categorías de productos
try {
    $stmt = $pdo->query("SELECT DISTINCT categoria FROM categoria_impresora WHERE activo = 1 ORDER BY categoria");
    $categorias = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($categorias as $cat) {
        echo "<option value='{$cat['categoria']}'>{$cat['categoria']}</option>";
    }
} catch (Exception $e) {
    echo "<option value='Bebidas'>Bebidas</option>";
    echo "<option value='Comidas'>Comidas</option>";
    echo "<option value='Carnes'>Carnes</option>";
}

echo "</select>";
echo "</div>";

echo "<button type='submit' name='test_hibrido' style='background-color: #28a745; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold;'>🔄 Probar Sistema Híbrido</button>";
echo "</form>";

// Información de red
echo "<h2>🌐 5. Información de Red</h2>";
echo "<div style='background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h5>📡 Configuración de Red:</h5>";
echo "<ul>";
echo "<li><strong>IP del servidor:</strong> " . $_SERVER['SERVER_ADDR'] . "</li>";
echo "<li><strong>IP del cliente:</strong> " . $_SERVER['REMOTE_ADDR'] . "</li>";
echo "<li><strong>User Agent:</strong> " . substr($_SERVER['HTTP_USER_AGENT'], 0, 100) . "...</li>";
echo "</ul>";

echo "<h5>🔧 Recomendaciones:</h5>";
echo "<ul>";
echo "<li>Para <strong>impresión directa</strong>: El dispositivo debe estar en la red 192.168.68.x</li>";
echo "<li>Para <strong>impresión por proxy</strong>: El proxy debe estar ejecutándose en ***********:3000</li>";
echo "<li>Las impresoras deben estar <strong>encendidas y conectadas</strong> a la red</li>";
echo "</ul>";
echo "</div>";

// Botones de navegación
echo "<p style='margin-top: 30px;'>";
echo "<a href='diagnostico' style='background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>← Diagnóstico General</a>";
echo "<a href='views/modules/test_sistema_hibrido.php' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🧪 Test Sistema Híbrido</a>";
echo "<a href='pantallaCocina?categoria=bar' style='background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🍺 Pantalla Bar</a>";
echo "</p>";
?>
