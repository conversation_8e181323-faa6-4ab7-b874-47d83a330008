<!DOCTYPE html>
<html>
<head>
    <title>Test Simple Final - Mesa 10</title>
</head>
<body>

<h1>🔍 Test Simple Final - Mesa 10</h1>

<p>Este test usa el controlador original con mejor manejo de errores para identificar el problema exacto.</p>

<div style="background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;">
    <h4>✅ Problema de Rutas Solucionado:</h4>
    <p>El error anterior era por rutas incorrectas en el controlador debug.</p>
    <p>Este test usa el controlador original con logging mejorado.</p>
</div>

<button onclick="testSimpleFinal()" style="background: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;">
🔍 Test Simple Final
</button>

<div id="resultado" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border: 1px solid #ddd; border-radius: 5px;"></div>

<script>
function testSimpleFinal() {
    document.getElementById('resultado').innerHTML = '<div style="color: blue;">⏳ Ejecutando test simple final...</div>';
    
    fetch('ajaxFactura_simple.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            efectivo: 50000,
            bancolombia: 0,
            nequi: 0,
            daviplata: 0,
            tarjeta: 0,
            pago: 1,
            pcedula: 'MARCO',
            totalDescuento: 0,
            total: 50000,
            propina: 0,
            mesa: 10,
            tipoTarjeta: 'credito',
            optimizada: true
        })
    })
    .then(response => {
        console.log('Status:', response.status);
        console.log('Status Text:', response.statusText);
        return response.text();
    })
    .then(text => {
        console.log('Respuesta completa:', text);
        
        let html = '<h4>📄 Resultado del Test Simple Final:</h4>';
        html += '<p><strong>Status:</strong> ' + (text ? 'Respuesta recibida' : 'Sin respuesta') + '</p>';
        
        // Extraer comentarios de debug simple
        const simpleMatches = text.match(/<!-- SIMPLE: [^>]+ -->/g);
        if (simpleMatches) {
            html += '<h5>🔍 Pasos del Test Simple:</h5>';
            html += '<ul>';
            simpleMatches.forEach(match => {
                const mensaje = match.replace(/<!-- SIMPLE: /, '').replace(/ -->/, '');
                html += '<li>' + mensaje + '</li>';
            });
            html += '</ul>';
        }
        
        // Buscar errores específicos
        if (text.includes('ERROR') || text.includes('Error')) {
            html += '<div style="background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;">';
            html += '<h5 style="color: #721c24;">❌ Error Detectado:</h5>';
            
            // Extraer mensajes de error
            const errorMatches = text.match(/Error [^:]*: [^<\n]+/g);
            if (errorMatches) {
                errorMatches.forEach(error => {
                    html += '<p style="color: #721c24;"><strong>' + error + '</strong></p>';
                });
            }
            html += '</div>';
        }
        
        // Buscar alertas JavaScript
        if (text.includes('alert(')) {
            html += '<div style="background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;">';
            html += '<h5 style="color: #856404;">⚠️ Alertas del Sistema:</h5>';
            
            const alertMatches = text.match(/alert\('([^']+)'\)/g);
            if (alertMatches) {
                alertMatches.forEach(alert => {
                    const mensaje = alert.replace(/alert\('/, '').replace(/'\)/, '');
                    html += '<p style="color: #856404;"><strong>' + mensaje + '</strong></p>';
                });
            }
            html += '</div>';
        }
        
        html += '<h5>📄 Respuesta Completa:</h5>';
        html += '<pre style="background: #fff; padding: 10px; border: 1px solid #ddd; border-radius: 3px; white-space: pre-wrap; max-height: 400px; overflow-y: auto;">' + text + '</pre>';
        
        if (text.includes('success_simple')) {
            html += '<div style="color: green; font-weight: bold; font-size: 18px;">✅ ÉXITO: Facturación completada</div>';
            html += '<p>¡La mesa 10 ha sido facturada exitosamente!</p>';
        } else if (text.includes('error_simple')) {
            html += '<div style="color: red; font-weight: bold; font-size: 18px;">❌ ERROR en facturación</div>';
            html += '<p>Se identificó el error específico en el proceso.</p>';
        } else if (text.includes('Error') || text.includes('error')) {
            html += '<div style="color: red; font-weight: bold; font-size: 18px;">❌ ERROR detectado</div>';
        } else {
            html += '<div style="color: orange; font-weight: bold; font-size: 18px;">⚠️ Respuesta inesperada</div>';
        }
        
        document.getElementById('resultado').innerHTML = html;
    })
    .catch(error => {
        console.error('Error completo:', error);
        
        let html = '<h4>❌ Error en Test Simple:</h4>';
        html += '<p><strong>Error:</strong> ' + error.message + '</p>';
        html += '<p>Error de conexión o timeout.</p>';
        
        document.getElementById('resultado').innerHTML = html;
    });
}
</script>

<h3>📋 Información del Test:</h3>
<div style="background: #d1ecf1; padding: 15px; border-radius: 5px;">
    <h4>🔍 Este test:</h4>
    <ul>
        <li><strong>Usa el controlador original</strong> - Sin problemas de rutas</li>
        <li><strong>Captura output del controlador</strong> - Ve qué produce</li>
        <li><strong>Manejo de errores mejorado</strong> - Try-catch completo</li>
        <li><strong>Logging detallado</strong> - Cada paso registrado</li>
        <li><strong>Timeout extendido</strong> - 5 minutos para mesas pesadas</li>
    </ul>
</div>

<br><a href="index.php?action=registroPmesa&ida=10" style="background: #007bff; color: white; padding: 10px; text-decoration: none;">🔙 Volver a Mesa 10</a>

</body>
</html>
