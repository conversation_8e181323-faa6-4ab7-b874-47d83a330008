<?php
// Configuración de Proxy Dinámico para Impresoras
echo "<h1>🔄 Proxy Dinámico para Impresoras</h1>";
echo "<p><strong>IP Pública Actual:</strong> *********** (puede cambiar)</p>";

// Verificar si se puede actualizar la IP
if (isset($_POST['actualizar_ip'])) {
    $nueva_ip = $_POST['nueva_ip'];
    
    if (!empty($nueva_ip) && filter_var($nueva_ip, FILTER_VALIDATE_IP)) {
        try {
            require_once '../../models/conexion.php';
            $conexion = new Conexion();
            $pdo = $conexion->conectar();
            
            // Actualizar todas las impresoras con la nueva IP
            $stmt = $pdo->prepare("UPDATE impresoras SET ip = ? WHERE nombre IN ('COCINA', 'BAR', 'ASADOS')");
            $stmt->execute([$nueva_ip]);
            
            echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0;'>";
            echo "<h4>✅ IP Actualizada Exitosamente</h4>";
            echo "<p>Todas las impresoras ahora apuntan a: <strong>$nueva_ip:3000</strong></p>";
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
            echo "<h4>❌ Error al actualizar</h4>";
            echo "<p>Error: " . $e->getMessage() . "</p>";
            echo "</div>";
        }
    } else {
        echo "<div style='background-color: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 15px 0;'>";
        echo "<h4>⚠️ IP inválida</h4>";
        echo "<p>Por favor ingresa una IP válida</p>";
        echo "</div>";
    }
}

echo "<div style='background-color: #d1ecf1; padding: 15px; border-left: 4px solid #bee5eb; margin: 20px 0;'>";
echo "<h3>📋 Configuración Actual:</h3>";

try {
    require_once '../../models/conexion.php';
    $conexion = new Conexion();
    $pdo = $conexion->conectar();
    
    $stmt = $pdo->query("SELECT * FROM impresoras ORDER BY nombre");
    $impresoras = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($impresoras) {
        echo "<table style='width: 100%; border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr style='background-color: #f8f9fa;'>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Impresora</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>IP Configurada</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Puerto</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Estado</th>";
        echo "</tr>";
        
        foreach ($impresoras as $imp) {
            $estado_color = $imp['activa'] ? '#28a745' : '#dc3545';
            $estado_texto = $imp['activa'] ? '✅ Activa' : '❌ Inactiva';
            
            echo "<tr>";
            echo "<td style='border: 1px solid #ddd; padding: 8px; font-weight: bold;'>{$imp['nombre']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$imp['ip']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$imp['puerto']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px; color: $estado_color;'>$estado_texto</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p>⚠️ Error al consultar impresoras: " . $e->getMessage() . "</p>";
}
echo "</div>";

// Formulario para actualizar IP
echo "<div style='border: 1px solid #007bff; padding: 20px; margin: 15px 0; border-radius: 8px;'>";
echo "<h3>🔄 Actualizar IP del Proxy</h3>";
echo "<p>Cuando tu IP pública cambie, usa este formulario para actualizar la configuración:</p>";

echo "<form method='POST' style='background-color: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<label><strong>Nueva IP Pública:</strong></label><br>";
echo "<input type='text' name='nueva_ip' value='***********' placeholder='ej: ***********' style='width: 200px; padding: 8px; margin: 5px 0; border: 1px solid #ccc; border-radius: 3px;'><br>";
echo "<button type='submit' name='actualizar_ip' style='background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin-top: 10px; cursor: pointer;'>🔄 Actualizar IP</button>";
echo "</form>";

echo "<h4>💡 Cómo obtener tu IP actual:</h4>";
echo "<ul>";
echo "<li><strong>Comando:</strong> <code>curl ifconfig.me</code></li>";
echo "<li><strong>Web:</strong> <a href='https://whatismyipaddress.com' target='_blank'>whatismyipaddress.com</a></li>";
echo "<li><strong>Google:</strong> Buscar \"mi ip\"</li>";
echo "</ul>";
echo "</div>";

// Código del proxy para Node.js
echo "<div style='border: 1px solid #28a745; padding: 20px; margin: 15px 0; border-radius: 8px;'>";
echo "<h3>🖥️ Código del Proxy (Node.js)</h3>";
echo "<p>Guarda este código como <strong>proxy-impresoras.js</strong> en tu portátil:</p>";

echo "<pre style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; font-size: 12px;'>";
echo htmlspecialchars('const express = require("express");
const net = require("net");
const cors = require("cors");

const app = express();
const PORT = 3000;

// Middleware
app.use(cors());
app.use(express.raw({ type: "*/*", limit: "10mb" }));
app.use(express.json());

// Configuración de impresoras
const IMPRESORAS = {
    "cocina": { ip: "**************", puerto: 9100 },
    "bar": { ip: "**************", puerto: 9100 },
    "asados": { ip: "**************", puerto: 9100 }
};

// Función para enviar a impresora
function enviarAImpresora(impresora, datos, res) {
    const config = IMPRESORAS[impresora];
    if (!config) {
        return res.status(404).json({ error: `Impresora ${impresora} no encontrada` });
    }

    const client = new net.Socket();
    let respuesta = "";

    client.setTimeout(5000); // 5 segundos timeout

    client.connect(config.puerto, config.ip, () => {
        console.log(`✅ Conectado a ${impresora} (${config.ip}:${config.puerto})`);
        
        if (datos && datos.length > 0) {
            client.write(datos);
            console.log(`📄 Enviado ${datos.length} bytes a ${impresora}`);
        }
    });

    client.on("data", (data) => {
        respuesta += data.toString();
    });

    client.on("close", () => {
        console.log(`🔌 Conexión cerrada con ${impresora}`);
        res.json({ 
            success: true, 
            impresora: impresora,
            ip: config.ip,
            respuesta: respuesta || "Enviado correctamente"
        });
    });

    client.on("error", (err) => {
        console.error(`❌ Error con ${impresora}:`, err.message);
        res.status(500).json({ 
            error: `Error conectando a ${impresora}: ${err.message}`,
            impresora: impresora,
            ip: config.ip
        });
    });

    client.on("timeout", () => {
        console.error(`⏰ Timeout con ${impresora}`);
        client.destroy();
        res.status(408).json({ 
            error: `Timeout conectando a ${impresora}`,
            impresora: impresora,
            ip: config.ip
        });
    });
}

// Rutas para cada impresora
app.all("/cocina", (req, res) => {
    enviarAImpresora("cocina", req.body, res);
});

app.all("/bar", (req, res) => {
    enviarAImpresora("bar", req.body, res);
});

app.all("/asados", (req, res) => {
    enviarAImpresora("asados", req.body, res);
});

// Ruta de estado
app.get("/status", (req, res) => {
    res.json({
        proxy: "Proxy de Impresoras Macarena",
        version: "1.0",
        impresoras: IMPRESORAS,
        timestamp: new Date().toISOString()
    });
});

// Ruta principal
app.get("/", (req, res) => {
    res.json({
        mensaje: "Proxy de Impresoras Macarena funcionando",
        rutas: ["/cocina", "/bar", "/asados", "/status"],
        impresoras: Object.keys(IMPRESORAS)
    });
});

// Iniciar servidor
app.listen(PORT, "0.0.0.0", () => {
    console.log(`🚀 Proxy de impresoras ejecutándose en puerto ${PORT}`);
    console.log(`📡 Accesible desde: http://***********:${PORT}`);
    console.log(`🖨️ Impresoras configuradas:`, Object.keys(IMPRESORAS));
});');
echo "</pre>";

echo "<h4>📦 Instalación y ejecución:</h4>";
echo "<ol>";
echo "<li><strong>Instalar Node.js:</strong> <a href='https://nodejs.org' target='_blank'>nodejs.org</a></li>";
echo "<li><strong>Crear carpeta:</strong> <code>mkdir proxy-macarena && cd proxy-macarena</code></li>";
echo "<li><strong>Instalar dependencias:</strong> <code>npm init -y && npm install express cors</code></li>";
echo "<li><strong>Guardar el código</strong> como <code>proxy-impresoras.js</code></li>";
echo "<li><strong>Ejecutar:</strong> <code>node proxy-impresoras.js</code></li>";
echo "</ol>";
echo "</div>";

// Configuración automática
echo "<div style='border: 1px solid #ffc107; padding: 20px; margin: 15px 0; border-radius: 8px;'>";
echo "<h3>⚙️ Configuración Automática</h3>";
echo "<p>Haz clic para configurar automáticamente las impresoras con tu IP actual:</p>";

echo "<form method='POST' style='margin: 10px 0;'>";
echo "<input type='hidden' name='nueva_ip' value='***********'>";
echo "<button type='submit' name='actualizar_ip' style='background-color: #ffc107; color: #212529; padding: 15px 30px; border: none; border-radius: 5px; font-weight: bold; cursor: pointer;'>⚡ Configurar Proxy Automáticamente</button>";
echo "</form>";

echo "<p><strong>Esto configurará:</strong></p>";
echo "<ul>";
echo "<li>COCINA → ***********:3000/cocina</li>";
echo "<li>BAR → ***********:3000/bar</li>";
echo "<li>ASADOS → ***********:3000/asados</li>";
echo "</ul>";
echo "</div>";

// Script para detectar IP automáticamente
echo "<div style='border: 1px solid #6f42c1; padding: 20px; margin: 15px 0; border-radius: 8px;'>";
echo "<h3>🤖 Detección Automática de IP</h3>";
echo "<p>Script para detectar automáticamente cuando cambie tu IP:</p>";

echo "<button onclick='detectarIP()' style='background-color: #6f42c1; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>🔍 Detectar IP Actual</button>";
echo "<div id='ip-resultado' style='margin-top: 10px;'></div>";

echo "<script>
async function detectarIP() {
    const resultado = document.getElementById('ip-resultado');
    resultado.innerHTML = '🔄 Detectando IP...';
    
    try {
        const response = await fetch('https://api.ipify.org?format=json');
        const data = await response.json();
        const ipActual = data.ip;
        
        resultado.innerHTML = `
            <div style='background-color: #e7f3ff; padding: 10px; border-radius: 5px; margin-top: 10px;'>
                <strong>IP Detectada:</strong> ${ipActual}<br>
                <form method='POST' style='margin-top: 10px;'>
                    <input type='hidden' name='nueva_ip' value='${ipActual}'>
                    <button type='submit' name='actualizar_ip' style='background-color: #007bff; color: white; padding: 8px 16px; border: none; border-radius: 3px;'>
                        🔄 Actualizar con esta IP
                    </button>
                </form>
            </div>
        `;
    } catch (error) {
        resultado.innerHTML = `
            <div style='background-color: #f8d7da; padding: 10px; border-radius: 5px; margin-top: 10px;'>
                ❌ Error al detectar IP: ${error.message}
            </div>
        `;
    }
}
</script>";
echo "</div>";

// Botones de navegación
echo "<p style='margin-top: 30px;'>";
echo "<a href='configurar_proxy_impresoras.php' style='background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>← Configurar Proxy</a>";
echo "<a href='verificar_impresoras.php' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🖨️ Verificar Impresoras</a>";
echo "</p>";
?>
