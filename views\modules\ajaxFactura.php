<?php
// Versión mejorada basada en Simple V2 que funciona para todas las mesas
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// CONFIGURACIÓN PARA MESAS PESADAS
ini_set('max_execution_time', 600); // 10 minutos
ini_set('memory_limit', '1024M'); // 1GB

session_start();

try {
    error_log("FACTURACIÓN MEJORADA INICIADA - Mesa: " . ($_POST['mesa'] ?? 'N/A') . " - Timestamp: " . date('Y-m-d H:i:s'));

    // Verificar sesión
    if (!isset($_SESSION["usuario"])) {
        $_SESSION["usuario"] = 15; // Usuario por defecto
        $_SESSION["tipo_usuario"] = 1;
        $_SESSION["perfil"] = "administrador";
    }

    // Incluir archivos necesarios
    require_once "../../models/conexion.php";
    require_once "../../models/crudFacturaAja.php";
    require_once "../../models/crud.php"; // Para productoListaSuministroModel

    // Validar datos POST
    if (!isset($_POST['mesa']) || !isset($_POST['total']) || !isset($_POST['pago'])) {
        throw new Exception("Datos POST incompletos");
    }

    // Extraer datos POST
    $mesa = (int)$_POST['mesa'];
    $totalCuenta = (float)$_POST['total'];
    $tipoPago = (int)$_POST['pago'];
    $cedula = $_POST['pcedula'] ?? '';
    $propina = (float)($_POST['propina'] ?? 0);

    // Métodos de pago
    $efectivo = (float)($_POST['efectivo'] ?? 0);
    $tarjeta = (float)($_POST['tarjeta'] ?? 0);
    $nequi = (float)($_POST['nequi'] ?? 0);
    $daviplata = (float)($_POST['daviplata'] ?? 0);
    $bancolombia = (float)($_POST['bancolombia'] ?? 0);
    $totalDescuento = (float)($_POST['totalDescuento'] ?? 0);

    // CORREGIDO: Calcular total pagado sin incluir totalDescuento (que es descuento, no pago)
    $totalPagado = $efectivo + $tarjeta + $nequi + $daviplata + $bancolombia;

    error_log("FACTURACIÓN MEJORADA - Mesa: $mesa, Total: $totalCuenta, Pagado: $totalPagado");
    error_log("VALIDACIÓN PAGOS - Efectivo: $efectivo, Tarjeta: $tarjeta, Nequi: $nequi, Daviplata: $daviplata, Bancolombia: $bancolombia");

    // VALIDACIÓN OBLIGATORIA: Verificar que la suma de las 5 formas de pago sea igual o mayor al total
    if ($totalPagado < $totalCuenta) {
        $faltante = $totalCuenta - $totalPagado;
        error_log("PAGO INSUFICIENTE - Mesa: $mesa, Total: $totalCuenta, Pagado: $totalPagado, Falta: $faltante");

        $mensaje = "❌ Pago insuficiente\\n\\n";
        $mensaje .= "Total a pagar: $" . number_format($totalCuenta) . "\\n";
        $mensaje .= "Total pagado: $" . number_format($totalPagado) . "\\n";
        $mensaje .= "Falta: $" . number_format($faltante) . "\\n\\n";
        $mensaje .= "Formas de pago recibidas:\\n";
        if ($efectivo > 0) $mensaje .= "• Efectivo: $" . number_format($efectivo) . "\\n";
        if ($tarjeta > 0) $mensaje .= "• Tarjeta: $" . number_format($tarjeta) . "\\n";
        if ($nequi > 0) $mensaje .= "• Nequi: $" . number_format($nequi) . "\\n";
        if ($daviplata > 0) $mensaje .= "• Daviplata: $" . number_format($daviplata) . "\\n";
        if ($bancolombia > 0) $mensaje .= "• Bancolombia: $" . number_format($bancolombia) . "\\n";

        echo "<script>alert('$mensaje')</script>";
        echo "error_pago_insuficiente_mesa_$mesa";
        exit;
    }

    // Conectar a la base de datos
    $db = Conexion::conectar();
    $db->beginTransaction();

    try {
        error_log("FACTURACIÓN CORREGIDA - Iniciando transacción");

        // 1. Obtener productos para facturar
        $productos = DatosFacturaAja::obtenerProductosParaFacturar($mesa);
        $totalProductos = count($productos);
        error_log("FACTURACIÓN CORREGIDA - Productos obtenidos: $totalProductos");

        if ($totalProductos == 0) {
            throw new Exception("No hay productos para facturar");
        }


        // 2. Crear nuevo pedido de facturación
    $mesero = $productos[0]["mesero"] ?? $_SESSION["usuario"];
    $fecha = date('Y-m-d H:i:s');

    $sql = "INSERT INTO pedidos (mesero_id, facturado, cedula_cliente, fecha_pedido) VALUES (?, 'n', ?, ?)";
    $stmt = $db->prepare($sql);
    $stmt->execute([$mesero, $cedula, $fecha]);
    $pedidoId = $db->lastInsertId();

        error_log("FACTURACIÓN CORREGIDA - Pedido creado: $pedidoId");

    // 3. Insertar productos en lotes para eficiencia
    $sqlProducto = "INSERT INTO pedido_productos_mesa (productos_id, mesas_id, pedidos_id, cantidad, precio, valor_productos, descuento, codigo_descuento) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
    $stmtProducto = $db->prepare($sqlProducto);

    $totalCalculado = 0;
    foreach ($productos as $producto) {
        $descuento = ($producto["preciopr"] * ($producto["descuentopvm"] / 100));
        $precioConDescuento = $producto["preciopr"] - $descuento;
        $subtotal = $precioConDescuento * $producto["cantidadpvm"];
        $totalCalculado += $subtotal;

        // CORREGIDO: Usar precio como valor_productos (costo no es esencial para facturación)
        $valorProducto = $producto["preciopr"];

        $stmtProducto->execute([
            $producto["idpr"],
            $producto["idmesa"],
            $pedidoId,
            $producto["cantidadpvm"],
            $producto["preciopr"],
            $valorProducto,
            $producto["descuentopvm"],
            $producto["pvmcodigo_descuento"]
        ]);
    }

    error_log("FACTURACIÓN CORREGIDA - Productos insertados, Total calculado: $totalCalculado");

    // 3.5. NUEVO: Descontar inventario de suministros
    error_log("FACTURACIÓN CORREGIDA - Iniciando descuento de inventario");
    $listaActualizar = array();
    $j = 0;

    foreach ($productos as $producto) {
        try {
            // Obtener suministros del producto
            $consultaSuministro = Datos::productoListaSuministroModel($producto["idpr"]);
            error_log("INVENTARIO - Producto {$producto['nombrepr']} (ID: {$producto['idpr']}) tiene " . count($consultaSuministro) . " suministros");

            foreach ($consultaSuministro as $suministro) {
                // Calcular cantidad total a descontar
                $cantidadTotalS = $suministro["spcantidad"] * $producto["cantidadpvm"];

                error_log("INVENTARIO - Suministro ID: {$suministro['sid']}, Cantidad actual: {$suministro['sucantidad']}, A descontar: $cantidadTotalS");

                // Buscar si el suministro ya está en la lista
                $encontrado = false;
                for ($c = 0; $c < $j; $c++) {
                    if ($suministro["sid"] == $listaActualizar[$c]["sid"]) {
                        $listaActualizar[$c]["cantidadTotalS"] -= $cantidadTotalS;
                        $encontrado = true;
                        break;
                    }
                }

                // Si no se encontró, agregar nuevo suministro a la lista
                if (!$encontrado) {
                    $listaActualizar[$j]["sid"] = $suministro["sid"];
                    $listaActualizar[$j]["cantidadTotalS"] = $suministro["sucantidad"] - $cantidadTotalS;
                    $j++;
                }
            }
        } catch (Exception $e) {
            error_log("INVENTARIO ERROR - Producto {$producto['nombrepr']}: " . $e->getMessage());
        }
    }

    // Actualizar cantidades en la tabla sucursal
    for ($c = 0; $c < $j; $c++) {
        try {
            $idSuministro = $listaActualizar[$c]['sid'];
            $cantidad = max(0, $listaActualizar[$c]['cantidadTotalS']); // No permitir cantidades negativas

            $consultaInventario = "UPDATE sucursal SET cantidad = ? WHERE suministro_id = ?";
            $stmtInventario = $db->prepare($consultaInventario);
            $stmtInventario->execute([$cantidad, $idSuministro]);

            error_log("INVENTARIO ACTUALIZADO - Suministro ID: $idSuministro, Nueva cantidad: $cantidad");
        } catch (Exception $e) {
            error_log("INVENTARIO ERROR UPDATE - Suministro ID: {$listaActualizar[$c]['sid']}: " . $e->getMessage());
        }
    }

    error_log("FACTURACIÓN CORREGIDA - Inventario actualizado, $j suministros procesados");

    // 4. Crear venta
    $turnoSql = "SELECT id FROM turnos_cajeros WHERE persona_id = ? AND fecha_final = '0000-00-00 00:00:00' ORDER BY id DESC LIMIT 1";
    $turnoStmt = $db->prepare($turnoSql);
    $turnoStmt->execute([$_SESSION["usuario"]]);
    $turno = $turnoStmt->fetchColumn() ?: 1;

    $iva = 8;
    $totalFinal = $totalCalculado + $propina;
    $cambio = $totalPagado - $totalFinal;

    $ventaSql = "INSERT INTO ventas (pedidos_id, turno_cajero_id, valor, fecha_venta, subtotal, iva, total, efectivo, bancolombia, nequi, daviplata, valortarjeta, cambio, propina, mesa) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    $ventaStmt = $db->prepare($ventaSql);
    $ventaStmt->execute([
        $pedidoId, $turno, $totalCalculado, $fecha, $totalCalculado, $iva, $totalFinal,
        $efectivo, $bancolombia, $nequi, $daviplata, $tarjeta, $cambio, $propina, $mesa
    ]);

    $facturaId = $db->lastInsertId();
    error_log("FACTURACIÓN MEJORADA - Venta creada: $facturaId");

    // 5. Actualizar estado de pedidos a facturado
    $updatePedidos = "UPDATE pedidos SET estado = 'facturado', fecha_entrega = NOW() WHERE mesa_id = ? AND estado IN ('borrador', 'enviado', 'entregado')";
    $updateStmt = $db->prepare($updatePedidos);
    $updateStmt->execute([$mesa]);

    error_log("FACTURACIÓN MEJORADA - Pedidos marcados como facturados");

    // 6. Limpiar mesa
    $db->exec("DELETE FROM producto_vendido_mesa WHERE mesas_id = $mesa");
    $db->exec("DELETE FROM cocina WHERE mesa_id = $mesa");
    $db->exec("UPDATE mesas SET descripcion = '', estado = '' WHERE id = $mesa");

    error_log("FACTURACIÓN MEJORADA - Mesa limpiada");

    // 7. Configurar variables de sesión
    $_SESSION["pedidos_id"] = $pedidoId;
    $_SESSION["turno_cajero_id"] = $turno;
    $_SESSION["subtotal"] = $totalCalculado;
    $_SESSION["total"] = $totalFinal;
    $_SESSION['idFactura'] = $facturaId;
    $_SESSION['propina'] = $propina;
    $_SESSION['mesaFactura'] = $mesa;
    $_SESSION["efectivo"] = $efectivo;
    $_SESSION["tarjeta"] = $tarjeta;
    $_SESSION["bancolombia"] = $bancolombia;
    $_SESSION["nequi"] = $nequi;
    $_SESSION["daviplata"] = $daviplata;
    $_SESSION["cambio"] = $cambio;

    // 8. IMPORTANTE: Configurar productos para pdf.php
    $listaProductos = array();
    $i = 0;
    foreach ($productos as $producto) {
        $descuento = ($producto["preciopr"] * ($producto["descuentopvm"] / 100));
        $precioConDescuento = $producto["preciopr"] - $descuento;
        $totalProducto = $precioConDescuento * $producto["cantidadpvm"];

        $listaProductos[$i] = array(
            'codigo' => $producto["idpr"],
            'nombre' => $producto["nombrepr"],
            'precio' => $producto["preciopr"],
            'cantidad' => $producto["cantidadpvm"],
            'descuento' => $producto["descuentopvm"],
            'total' => $totalProducto
        );
        $i++;
    }
    $_SESSION["productos"] = $listaProductos;

    error_log("FACTURACIÓN CORREGIDA - Productos configurados para PDF: " . count($listaProductos));

    $db->commit();

    error_log("FACTURACIÓN MEJORADA COMPLETADA - Mesa: $mesa, Factura: $facturaId");

        // Respuesta de éxito
        echo '<script>alert("✅ Facturación completada exitosamente\\n\\nMesa ' . $mesa . ' liberada\\nFactura: ' . $facturaId . '");window.open("pdf","_blank");location.href ="mesa";</script>';
        echo "success_corregida_mesa_$mesa";

    } catch (Exception $e) {
        $db->rollBack();
        error_log("FACTURACIÓN CORREGIDA ERROR: " . $e->getMessage());
        echo "<script>alert('Error en facturación: " . addslashes($e->getMessage()) . "')</script>";
        echo "error_corregida: " . $e->getMessage();
    }

} catch (Exception $e) {
    error_log("FACTURACIÓN CORREGIDA ERROR GENERAL: " . $e->getMessage());
    echo "<script>alert('Error general: " . addslashes($e->getMessage()) . "')</script>";
    echo "error_general: " . $e->getMessage();
}
?>