<?php
	require_once "../../models/crud.php";
	require_once "../../models/crudFacturaAja.php";
	require_once "../../models/crudCompraSuministro.php";
	require_once "../../controllers/controller.php";
	require_once "../../controllers/controllerCompraSuministro.php";
	ini_set("session.cookie_lifetime","28800");
	ini_set("session.gc_maxlifetime","28800");
	$registroCsuministro = new controllerCompraSuministro();
	$registroCsuministro -> registroCompraSuministroAjaController();
	//echo "<script>alert('entro al ajax ss Factura')</script>";
	//echo "<script>alert('entro al ajax   tipo de pago: ".$_POST['pago']." efectivo: ".$_POST['efectivo']." total: ".$_POST['total']."Cedula: ".$_POST['pcedula']."')</script>";
?>
