<?php

require_once "conexion.php";

class DatosPedidosCategoriasSimple {
    
    /*=============================================
    OBTENER PEDIDOS PENDIENTES POR CATEGORÍA - VERSIÓN SIMPLE
    =============================================*/
    static public function obtenerPedidosPendientesModel($categoria) {
        try {
            // Versión simplificada: si es cocina, mostrar TODO lo que no sea explícitamente bar o asados
            if ($categoria == 'cocina') {
                $stmt = Conexion::conectar()->prepare("
                    SELECT DISTINCT
                        p.id as pedido_id,
                        p.numero_pedido,
                        p.fecha_envio,
                        p.mesa_id,
                        m.nombre as mesa_numero,
                        COUNT(pvm.id) as total_productos,
                        GROUP_CONCAT(
                            CONCAT(pr.nombre, ' x', pvm.cantidad) 
                            ORDER BY pr.nombre 
                            SEPARATOR ', '
                        ) as productos_detalle,
                        SUM(pvm.cantidad * pr.precio) as total_precio
                    FROM pedidos p
                    INNER JOIN mesas m ON p.mesa_id = m.id
                    INNER JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
                    INNER JOIN productos pr ON pvm.productos_id = pr.id
                    WHERE p.estado = 'enviado'
                    GROUP BY p.id, p.numero_pedido, p.fecha_envio, p.mesa_id, m.nombre
                    ORDER BY p.fecha_envio ASC
                ");
            } else {
                // Para bar y asados, usar filtro específico
                $stmt = Conexion::conectar()->prepare("
                    SELECT DISTINCT
                        p.id as pedido_id,
                        p.numero_pedido,
                        p.fecha_envio,
                        p.mesa_id,
                        m.numero as mesa_numero,
                        COUNT(pvm.id) as total_productos,
                        GROUP_CONCAT(
                            CONCAT(pr.nombre, ' x', pvm.cantidad) 
                            ORDER BY pr.nombre 
                            SEPARATOR ', '
                        ) as productos_detalle,
                        SUM(pvm.cantidad * pr.precio) as total_precio
                    FROM pedidos p
                    INNER JOIN mesas m ON p.mesa_id = m.id
                    INNER JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
                    INNER JOIN productos pr ON pvm.productos_id = pr.id
                    WHERE p.estado = 'enviado'
                    AND pr.categoria = :categoria
                    GROUP BY p.id, p.numero_pedido, p.fecha_envio, p.mesa_id, m.nombre
                    ORDER BY p.fecha_envio ASC
                ");
                $stmt->bindParam(":categoria", $categoria, PDO::PARAM_STR);
            }
            
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Error en obtenerPedidosPendientesModel: " . $e->getMessage());
            return [];
        }
    }
    
    /*=============================================
    OBTENER ESTADÍSTICAS DEL DÍA POR CATEGORÍA - VERSIÓN SIMPLE
    =============================================*/
    static public function obtenerEstadisticasDiaModel($categoria) {
        try {
            if ($categoria == 'cocina') {
                // Para cocina, contar TODOS los pedidos
                $stmt = Conexion::conectar()->prepare("
                    SELECT 
                        COUNT(DISTINCT CASE WHEN p.estado = 'enviado' THEN p.id END) as pendientes,
                        COUNT(DISTINCT CASE WHEN p.estado = 'entregado' AND DATE(p.fecha_entrega) = CURDATE() THEN p.id END) as entregados_hoy,
                        SUM(CASE WHEN p.estado = 'entregado' AND DATE(p.fecha_entrega) = CURDATE() THEN pvm.cantidad * pr.precio ELSE 0 END) as total_ventas_hoy
                    FROM pedidos p
                    INNER JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
                    INNER JOIN productos pr ON pvm.productos_id = pr.id
                ");
            } else {
                // Para bar y asados, usar filtro específico
                $stmt = Conexion::conectar()->prepare("
                    SELECT 
                        COUNT(DISTINCT CASE WHEN p.estado = 'enviado' THEN p.id END) as pendientes,
                        COUNT(DISTINCT CASE WHEN p.estado = 'entregado' AND DATE(p.fecha_entrega) = CURDATE() THEN p.id END) as entregados_hoy,
                        SUM(CASE WHEN p.estado = 'entregado' AND DATE(p.fecha_entrega) = CURDATE() THEN pvm.cantidad * pr.precio ELSE 0 END) as total_ventas_hoy
                    FROM pedidos p
                    INNER JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
                    INNER JOIN productos pr ON pvm.productos_id = pr.id
                    WHERE CASE
                        WHEN :categoria = 'bar' THEN pr.categoria IN ('bebidas', 'cervezas', 'licores', 'vinos', 'cocteles', 'refrescos', 'jugos', 'agua', 'bar')
                        WHEN :categoria = 'asados' THEN pr.categoria IN ('carnes', 'parrilla', 'asados', 'pescados', 'mariscos')
                    END
                ");
                $stmt->bindParam(":categoria", $categoria, PDO::PARAM_STR);
            }
            
            $stmt->execute();
            return $stmt->fetch(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Error en obtenerEstadisticasDiaModel: " . $e->getMessage());
            return [
                'pendientes' => 0,
                'entregados_hoy' => 0,
                'total_ventas_hoy' => 0
            ];
        }
    }
}

?>
