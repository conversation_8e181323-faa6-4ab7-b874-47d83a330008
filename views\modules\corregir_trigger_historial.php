<?php

session_start();

if(!isset($_SESSION["validar"]) || !$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "models/conexion.php";

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Corregir: <PERSON><PERSON> de <PERSON>l</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
</head>
<body>

<div class="container">
    <h2>🔧 Corregir: Tri<PERSON> de <PERSON>l</h2>
    <hr>
    
    <div class="alert alert-warning">
        <h4>⚠️ Problema Identificado:</h4>
        <p><strong>Error:</strong> Column 'usuario_id' cannot be null en tabla pedidos_historial</p>
        <p><strong>Causa:</strong> El trigger automático no maneja correctamente valores NULL para usuario_id</p>
    </div>
    
    <?php
    if (isset($_POST['corregir_trigger'])) {
        try {
            $stmt = Conexion::conectar();
            $stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            echo "<div class='alert alert-info'>";
            echo "<h5>🔄 Corrigiendo trigger...</h5>";
            echo "</div>";
            
            // 1. Eliminar el trigger existente
            echo "<p>1. Eliminando trigger existente...</p>";
            $stmt->exec("DROP TRIGGER IF EXISTS pedidos_historial_insert");
            echo "<p class='text-success'>✅ Trigger eliminado</p>";
            
            // 2. Crear el trigger corregido
            echo "<p>2. Creando trigger corregido...</p>";
            $trigger_sql = "
            CREATE TRIGGER pedidos_historial_insert 
            AFTER UPDATE ON pedidos 
            FOR EACH ROW 
            BEGIN 
                IF OLD.estado != NEW.estado THEN 
                    INSERT INTO pedidos_historial (pedido_id, estado_anterior, estado_nuevo, usuario_id, observaciones) 
                    VALUES (
                        NEW.id, 
                        OLD.estado, 
                        NEW.estado, 
                        COALESCE(NEW.usuario_envio, NEW.usuario_entrega, 1),
                        CONCAT('Cambio de estado de ', OLD.estado, ' a ', NEW.estado)
                    ); 
                END IF; 
            END";
            
            $stmt->exec($trigger_sql);
            echo "<p class='text-success'>✅ Trigger corregido creado</p>";
            
            echo "<div class='alert alert-success'>";
            echo "<h5>🎉 ¡Trigger corregido exitosamente!</h5>";
            echo "<p>Ahora el trigger usa COALESCE para manejar valores NULL en usuario_id.</p>";
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>";
            echo "<h5>❌ Error al corregir trigger:</h5>";
            echo "<p>" . $e->getMessage() . "</p>";
            echo "</div>";
        }
    }
    ?>
    
    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">🔍 Análisis del Problema</h3>
        </div>
        <div class="panel-body">
            <h5>❌ Trigger Problemático (Actual):</h5>
            <div class="well">
                <code>
                INSERT INTO pedidos_historial (..., usuario_id, ...)<br>
                VALUES (..., NEW.usuario_envio, ...)
                </code>
                <p><small>❌ Falla cuando NEW.usuario_envio es NULL</small></p>
            </div>
            
            <h5>✅ Trigger Corregido (Propuesto):</h5>
            <div class="well">
                <code>
                INSERT INTO pedidos_historial (..., usuario_id, ...)<br>
                VALUES (..., COALESCE(NEW.usuario_envio, NEW.usuario_entrega, 1), ...)
                </code>
                <p><small>✅ Usa COALESCE para manejar valores NULL</small></p>
            </div>
            
            <h5>💡 Cómo funciona COALESCE:</h5>
            <ul>
                <li>Intenta usar <code>NEW.usuario_envio</code> primero</li>
                <li>Si es NULL, usa <code>NEW.usuario_entrega</code></li>
                <li>Si ambos son NULL, usa <code>1</code> como valor por defecto</li>
            </ul>
        </div>
    </div>
    
    <div class="panel panel-warning">
        <div class="panel-heading">
            <h3 class="panel-title">📊 Estado Actual del Trigger</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                echo "<h5>🔍 Triggers actuales en tabla pedidos:</h5>";
                $stmt = Conexion::conectar()->prepare("SHOW TRIGGERS LIKE 'pedidos'");
                $stmt->execute();
                $triggers = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (count($triggers) > 0) {
                    echo "<table class='table table-condensed'>";
                    echo "<thead>";
                    echo "<tr>";
                    echo "<th>Trigger</th>";
                    echo "<th>Evento</th>";
                    echo "<th>Timing</th>";
                    echo "<th>Estado</th>";
                    echo "</tr>";
                    echo "</thead>";
                    echo "<tbody>";
                    
                    foreach ($triggers as $trigger) {
                        $estado = ($trigger['Trigger'] == 'pedidos_historial_insert') ? 'Problemático' : 'OK';
                        $clase = ($trigger['Trigger'] == 'pedidos_historial_insert') ? 'danger' : 'success';
                        
                        echo "<tr class='{$clase}'>";
                        echo "<td>{$trigger['Trigger']}</td>";
                        echo "<td>{$trigger['Event']}</td>";
                        echo "<td>{$trigger['Timing']}</td>";
                        echo "<td>{$estado}</td>";
                        echo "</tr>";
                    }
                    
                    echo "</tbody></table>";
                } else {
                    echo "<div class='alert alert-warning'>No se encontraron triggers</div>";
                }
                
                echo "<h5>📋 Últimos registros en pedidos_historial:</h5>";
                $stmt_historial = Conexion::conectar()->prepare("
                    SELECT id, pedido_id, estado_anterior, estado_nuevo, usuario_id, fecha_cambio 
                    FROM pedidos_historial 
                    ORDER BY fecha_cambio DESC 
                    LIMIT 5
                ");
                $stmt_historial->execute();
                $historial = $stmt_historial->fetchAll(PDO::FETCH_ASSOC);
                
                if (count($historial) > 0) {
                    echo "<table class='table table-condensed'>";
                    echo "<thead>";
                    echo "<tr>";
                    echo "<th>ID</th>";
                    echo "<th>Pedido</th>";
                    echo "<th>Estado Anterior</th>";
                    echo "<th>Estado Nuevo</th>";
                    echo "<th>Usuario ID</th>";
                    echo "<th>Fecha</th>";
                    echo "</tr>";
                    echo "</thead>";
                    echo "<tbody>";
                    
                    foreach ($historial as $registro) {
                        $usuario_class = $registro['usuario_id'] ? 'success' : 'danger';
                        echo "<tr>";
                        echo "<td>{$registro['id']}</td>";
                        echo "<td>{$registro['pedido_id']}</td>";
                        echo "<td>{$registro['estado_anterior']}</td>";
                        echo "<td>{$registro['estado_nuevo']}</td>";
                        echo "<td class='{$usuario_class}'>" . ($registro['usuario_id'] ?: 'NULL') . "</td>";
                        echo "<td><small>{$registro['fecha_cambio']}</small></td>";
                        echo "</tr>";
                    }
                    
                    echo "</tbody></table>";
                } else {
                    echo "<div class='alert alert-info'>No hay registros en pedidos_historial</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-success">
        <div class="panel-heading">
            <h3 class="panel-title">🛠️ Aplicar Corrección</h3>
        </div>
        <div class="panel-body">
            <h5>🔧 Acción Requerida:</h5>
            <p>Hacer clic en el botón para corregir el trigger automáticamente.</p>
            
            <form method="POST">
                <button type="submit" name="corregir_trigger" class="btn btn-warning btn-lg">
                    🔧 Corregir Trigger de Historial
                </button>
            </form>
            
            <div class="alert alert-info" style="margin-top: 15px;">
                <h6>💡 Qué hace esta corrección:</h6>
                <ol>
                    <li>Elimina el trigger problemático actual</li>
                    <li>Crea un nuevo trigger que maneja valores NULL correctamente</li>
                    <li>Usa COALESCE para proporcionar un valor por defecto</li>
                    <li>Evita el error "Column 'usuario_id' cannot be null"</li>
                </ol>
            </div>
        </div>
    </div>
    
    <hr>
    <div class="row">
        <div class="col-md-3">
            <a href="index.php?action=debug_problemas_pedidos" class="btn btn-primary btn-block">🔙 Debug Problemas</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=mesa" class="btn btn-info btn-block">🪑 Ver Mesas</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=registroPmesa&ida=10" class="btn btn-warning btn-block">🧪 Test Mesa 10</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=diagnostico" class="btn btn-success btn-block">📊 Diagnóstico</a>
        </div>
    </div>
</div>

</body>
</html>
