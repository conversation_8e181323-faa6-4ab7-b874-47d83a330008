<?php
session_start();
require_once "../../models/conexion.php";
require_once "../../models/crudFacturaAja.php";

// Verificar si se recibió mesa por GET
$mesaId = $_GET['mesa'] ?? 5;

echo "<!DOCTYPE html>";
echo "<html><head><title>Test Facturación Completa Final</title></head><body>";
echo "<h2>🎯 Test Facturación Completa Final</h2>";
echo "<p><strong>Mesa ID:</strong> $mesaId</p>";

// Verificar productos para facturar en esta mesa
try {
    $productos = DatosFacturaAja::obtenerProductosParaFacturar($mesaId);
    $totalProductos = count($productos);
    
    echo "<h3>📊 Estado Actual:</h3>";
    echo "<p><strong>Total productos:</strong> $totalProductos</p>";
    
    if ($totalProductos > 0) {
        $totalReal = 0;
        foreach ($productos as $producto) {
            $totalReal += $producto['valor_productos'];
        }
        
        echo "<p><strong>Total calculado:</strong> $" . number_format($totalReal) . "</p>";
        
        echo "<h3>🎯 Test Completo (Facturación + PDF)</h3>";
        echo "<p>Este test verifica:</p>";
        echo "<ul>";
        echo "<li>✅ Facturación correcta con mesa dinámica</li>";
        echo "<li>✅ Guardado en tabla ventas</li>";
        echo "<li>✅ Configuración de variables de sesión para PDF</li>";
        echo "<li>✅ Apertura automática de PDF para impresión</li>";
        echo "</ul>";
        
        echo "<button onclick='testCompleto()' style='background: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; margin: 5px;'>";
        echo "🎯 Test Completo Mesa $mesaId";
        echo "</button>";
        
        echo "<div id='resultado' style='margin-top: 20px; padding: 15px; border-radius: 5px;'></div>";
        
    } else {
        echo "<p style='color: orange;'>⚠️ No hay productos para facturar en esta mesa</p>";
        echo "<p>Ve a la mesa y agrega algunos productos primero.</p>";
        echo "<br><a href='index.php?action=registroPmesa&ida=$mesaId' style='background: #007bff; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>➕ Ir a Mesa $mesaId</a>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}

?>

<script>
function testCompleto() {
    const mesaId = <?=$mesaId?>;
    const totalReal = <?=$totalReal ?? 50000?>;
    
    // Usar exactamente los mismos datos que registroPmesa.php
    const datos = {
        efectivo: totalReal,
        bancolombia: 0,
        nequi: 0,
        daviplata: 0,
        tarjeta: 0,
        pago: 1,
        pcedula: 'TEST_COMPLETO_' + mesaId,
        totalDescuento: 0,
        total: totalReal,
        propina: 0,
        mesa: mesaId,
        tipoTarjeta: 'credito',
        optimizada: true
    };
    
    console.log('🎯 DATOS COMPLETOS para mesa ' + mesaId + ':', datos);
    
    document.getElementById('resultado').innerHTML = '<div style="background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; border: 1px solid #c3e6cb;">⏳ Procesando facturación completa para mesa ' + mesaId + '...<br>📄 Se abrirá automáticamente el PDF para impresión</div>';
    
    // Usar exactamente la misma llamada que registroPmesa.php
    $.ajax({
        url: "ajaxFactura.php",
        type: "POST",
        data: datos,
        timeout: 600000, // 10 minutos para mesas muy pesadas
        success: function(response) {
            console.log('✅ Respuesta facturación completa:', response);

            let html = '<h4>✅ Respuesta del Servidor:</h4>';
            html += '<div style="background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; max-height: 200px; overflow-y: auto; border: 1px solid #dee2e6;">';
            html += response;
            html += '</div>';

            if (response.includes('success_corregida') || response.includes('Facturación completada exitosamente')) {
                html += '<div style="background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin-top: 10px; border: 1px solid #c3e6cb;">';
                html += '<strong>🎉 ¡ÉXITO COMPLETO!</strong>';
                html += '<br>✅ Facturación completada para mesa ' + mesaId;
                html += '<br>✅ PDF abierto automáticamente para impresión';
                html += '<br>✅ Variables de sesión configuradas correctamente';
                html += '<br>✅ Mesa guardada en tabla ventas';
                html += '</div>';
                
                html += '<div style="background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin-top: 10px; border: 1px solid #ffeaa7;">';
                html += '<strong>📋 Verificaciones Adicionales:</strong>';
                html += '<br><a href="test_verificar_mesa_ventas.php?mesa=' + mesaId + '" target="_blank" style="color: #856404; text-decoration: underline;">🔍 Verificar en tabla ventas</a>';
                html += '<br><a href="pdf.php" target="_blank" style="color: #856404; text-decoration: underline;">📄 Abrir PDF manualmente</a>';
                html += '</div>';
                
            } else if (response.includes('error_corregida') || response.includes('Error')) {
                html += '<div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin-top: 10px; border: 1px solid #f5c6cb;">';
                html += '<strong>❌ Error en facturación para mesa ' + mesaId + '</strong>';
                html += '<br>Revisa los logs del servidor para más detalles';
                html += '</div>';
            } else {
                html += '<div style="background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin-top: 10px; border: 1px solid #bee5eb;">';
                html += '<strong>⚠️ Respuesta inesperada</strong>';
                html += '<br>Revisa la respuesta completa arriba';
                html += '</div>';
            }

            document.getElementById('resultado').innerHTML = html;
        },
        error: function(xhr, status, error) {
            console.error('❌ Error AJAX completo:', error, 'Status:', status, 'XHR:', xhr);

            let mensaje = '❌ Error en la facturación completa:\n\n';
            if (status === 'timeout') {
                mensaje += 'Timeout: La facturación está tomando más tiempo del esperado.\n\nVerifica manualmente si se completó.';
            } else if (xhr.status === 500) {
                mensaje += 'Error interno del servidor (500).\n\nRevisa los logs del servidor.';
            } else {
                mensaje += 'Error: ' + error + '\nStatus: ' + status;
            }

            document.getElementById('resultado').innerHTML = '<div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; border: 1px solid #f5c6cb;">' + mensaje.replace(/\n/g, '<br>') + '</div>';
        }
    });
}
</script>

<!-- Incluir jQuery para usar $.ajax como en registroPmesa.php -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<br><br>
<a href="test_verificar_mesa_ventas.php?mesa=<?=$mesaId?>" style="background: #17a2b8; color: white; padding: 10px; text-decoration: none; border-radius: 5px;">🔍 Verificar Ventas</a>
<a href="index.php?action=registroPmesa&ida=<?=$mesaId?>" style="background: #007bff; color: white; padding: 10px; text-decoration: none; border-radius: 5px;">🔙 Volver a Mesa <?=$mesaId?></a>

</body>
</html>
