<?php
session_start();

// Detectar la ruta correcta para conexion.php
if (file_exists("models/conexion.php")) {
    require_once "models/conexion.php";
} elseif (file_exists("../../models/conexion.php")) {
    require_once "../../models/conexion.php";
} else {
    die("Error: No se puede encontrar el archivo de conexión");
}

if (isset($_POST['termino'])) {
    $termino = $_POST['termino'];

    // Debug
    error_log("AJAX BUSCAR PRODUCTOS - Término: $termino");

    try {
        $db = Conexion::conectar();
        
        $sql = "SELECT id, nombre, precio FROM productos 
                WHERE nombre LIKE :termino OR codigo LIKE :termino 
                ORDER BY nombre LIMIT 10";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([':termino' => "%$termino%"]);
        $productos = $stmt->fetchAll(PDO::FETCH_ASSOC);

        error_log("AJAX BUSCAR PRODUCTOS - Encontrados: " . count($productos));

        if (count($productos) > 0) {
            foreach ($productos as $producto) {
                echo '<div class="producto-sugerencia" onclick="seleccionarProducto(' . 
                     $producto['id'] . ', \'' . 
                     addslashes($producto['nombre']) . '\', ' . 
                     $producto['precio'] . ')">';
                echo '<strong>' . htmlspecialchars($producto['nombre']) . '</strong><br>';
                echo '<small>$' . number_format($producto['precio']) . '</small>';
                echo '</div>';
            }
        } else {
            echo '<div class="producto-sugerencia" style="color: #666;">No se encontraron productos</div>';
        }
        
    } catch (Exception $e) {
        echo '<div class="producto-sugerencia" style="color: red;">Error: ' . $e->getMessage() . '</div>';
    }
}
?>
