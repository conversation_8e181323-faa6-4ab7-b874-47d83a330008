<?php
// Test que simula exactamente la interfaz de registroPmesa.php
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
if (!isset($_SESSION["usuario"])) {
    $_SESSION["usuario"] = 1;
    $_SESSION["tipo_usuario"] = 1;
    $_SESSION["perfil"] = "administrador";
}

require_once "../../models/conexion.php";
require_once "../../models/crudFacturaAja.php";

$mesaId = isset($_GET['mesa']) ? $_GET['mesa'] : 1;

echo "<h1>🖥️ Test Interfaz Facturación - Mesa $mesaId</h1>";
echo "<p>Este test simula exactamente lo que hace la interfaz de registroPmesa.php</p>";

try {
    // Obtener productos para facturar
    $productosFacturar = DatosFacturaAja::obtenerProductosParaFacturar($mesaId);
    
    if (!empty($productosFacturar)) {
        // Calcular total real
        $totalReal = 0;
        foreach ($productosFacturar as $producto) {
            $descuento = ($producto['preciopr'] * ($producto['descuentopvm'] / 100));
            $precioConDescuento = $producto['preciopr'] - $descuento;
            $subtotal = $precioConDescuento * $producto['cantidadpvm'];
            $totalReal += $subtotal;
        }
        
        echo "<div class='alert alert-info'>";
        echo "<h4>💰 Total a Facturar: $" . number_format($totalReal) . "</h4>";
        echo "<p>Productos encontrados: " . count($productosFacturar) . "</p>";
        echo "</div>";
        
        // Crear formulario idéntico al de registroPmesa.php
        echo "<h3>📝 Formulario de Facturación (Idéntico a registroPmesa.php)</h3>";
        echo "<form name='calculo' id='testForm'>";
        echo "<table border='1' class='table'>";
        
        echo "<tr>";
        echo "<td colspan='6'><h5><b>Total a Pagar</b></h5></td>";
        echo "<td colspan='3'><input type='text' name='pagar' id='pagar' value='$totalReal' readonly></td>";
        echo "</tr>";
        
        echo "<tr>";
        echo "<td colspan='6'><h5><b>Recibo en efectivo</b></h5></td>";
        echo "<td colspan='3'><input type='text' placeholder='$' name='efectivo' id='efectivo' onkeyup='funcion_calcular();' value='0'></td>";
        echo "</tr>";
        
        echo "<tr>";
        echo "<td colspan='4'><b>Bold</b> <input type='text' placeholder='$' name='tarjeta' id='tarjeta' onkeyup='funcion_calcular();' value='0'></td>";
        echo "<td colspan='2'><label><b>Bancolombia</b></label></td>";
        echo "<td colspan='3'><input type='text' placeholder='$' name='bancolombia' id='bancolombia' onkeyup='funcion_calcular();' value='0'></td>";
        echo "</tr>";
        
        echo "<tr>";
        echo "<td colspan='4'><b>Nequi</b> <input type='text' placeholder='$' name='nequi' id='nequi' onkeyup='funcion_calcular();' value='0'></td>";
        echo "<td colspan='2'><label>Daviplata</label></td>";
        echo "<td colspan='3'><input type='text' placeholder='$' name='daviplata' id='daviplata' onkeyup='funcion_calcular();' value='0'></td>";
        echo "</tr>";
        
        echo "<tr>";
        echo "<td colspan='6'><h5><b>Cambio</b></h5></td>";
        echo "<td colspan='2'><input type='text' placeholder='$' name='cambio' id='cambio' readonly></td>";
        echo "</tr>";
        
        echo "</table>";
        
        // Campos ocultos necesarios
        echo "<input type='hidden' name='total' id='total' value='$totalReal'>";
        echo "<input type='hidden' name='propina' id='propina' value='0'>";
        echo "<input type='hidden' name='totalDescuento' id='totalDescuento' value='0'>";
        echo "<input type='hidden' name='pcedula' id='pcedula' value='12345678'>";
        echo "<input type='hidden' name='mesa' id='mesa' value='$mesaId'>";
        echo "<input type='hidden' name='pago' id='pago' value='1'>";
        
        echo "</form>";
        
        echo "<h3>🧪 Acciones de Prueba</h3>";
        echo "<button onclick='llenarCamposAutomatico()' style='background: #17a2b8; color: white; padding: 10px; margin: 5px;'>📝 Llenar Campos Automáticamente</button>";
        echo "<button onclick='testFacturarInterfaz()' style='background: #28a745; color: white; padding: 10px; margin: 5px;'>💰 Facturar (Simular Interfaz)</button>";
        echo "<button onclick='mostrarValores()' style='background: #6c757d; color: white; padding: 10px; margin: 5px;'>👁️ Mostrar Valores</button>";
        
        echo "<div id='resultado' style='margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px;'></div>";
        
    } else {
        echo "<div class='alert alert-warning'>⚠️ No hay productos disponibles para facturar</div>";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}

echo "<br><a href='index.php?action=registroPmesa&ida=$mesaId' style='background: #007bff; color: white; padding: 10px; text-decoration: none;'>🔙 Volver a Mesa $mesaId</a>";
?>

<script>
// Función formatCurrency idéntica a registroPmesa.php
function formatCurrency(input) {
  if (!input || !input.value) {
    return 0;
  }
  
  let cleanValue = input.value.replace(/\D/g, '');
  let value = parseInt(cleanValue);
  
  if (isNaN(value)) {
    value = 0;
  }
  
  let options = { style: 'currency', currency: 'COP', minimumFractionDigits: 0, maximumFractionDigits: 0 };
  let formatter = new Intl.NumberFormat('es-CO', options);
  input.value = formatter.format(value);

  return value;
}

// Función funcion_calcular idéntica a registroPmesa.php
function funcion_calcular() {
    let nequi = document.getElementById('nequi') ? formatCurrency(document.getElementById('nequi')) : 0;
    let daviplata = document.getElementById('daviplata') ? formatCurrency(document.getElementById('daviplata')) : 0;
    let bancolombia = document.getElementById('bancolombia') ? formatCurrency(document.getElementById('bancolombia')) : 0;
    let efectivo = document.getElementById('efectivo') ? formatCurrency(document.getElementById('efectivo')) : 0;
    let propina = document.getElementById('propina') ? formatCurrency(document.getElementById('propina')) : 0;
    let tarjeta = document.getElementById('tarjeta') ? formatCurrency(document.getElementById('tarjeta')) : 0;
    
    var totalDescuento = document.calculo && document.calculo.totalDescuento ? parseInt(document.calculo.totalDescuento.value) || 0 : 0;
    var total = document.calculo && document.calculo.total ? parseInt(document.calculo.total.value) || 0 : 0;
    
    nequi = isNaN(nequi) ? 0 : nequi;
    daviplata = isNaN(daviplata) ? 0 : daviplata;
    bancolombia = isNaN(bancolombia) ? 0 : bancolombia;
    tarjeta = isNaN(tarjeta) ? 0 : tarjeta;
    efectivo = isNaN(efectivo) ? 0 : efectivo;
    propina = isNaN(propina) ? 0 : propina;
    totalDescuento = isNaN(totalDescuento) ? 0 : totalDescuento;
    total = isNaN(total) ? 0 : total;
    
    var calculo = nequi + daviplata + bancolombia + tarjeta + efectivo + totalDescuento - total - propina;
    
    if (document.calculo && document.calculo.pagar) {
        var totalPagar = total + propina;
        document.calculo.pagar.value = totalPagar;
        if (document.getElementById('pagar')) {
            document.getElementById('pagar').value = totalPagar;
            formatCurrency(document.getElementById('pagar'));
        }
    }
    
    if (document.calculo && document.calculo.cambio) {
        document.calculo.cambio.value = calculo;
        if (document.getElementById('cambio')) {
            document.getElementById('cambio').value = calculo;
            formatCurrency(document.getElementById('cambio'));
        }
    }
}

function llenarCamposAutomatico() {
    const total = <?=$totalReal ?? 0?>;
    const porcion = Math.floor(total / 5);
    
    document.getElementById('efectivo').value = porcion;
    document.getElementById('tarjeta').value = porcion;
    document.getElementById('nequi').value = porcion;
    document.getElementById('daviplata').value = porcion;
    document.getElementById('bancolombia').value = total - (porcion * 4);
    
    funcion_calcular();
    
    document.getElementById('resultado').innerHTML = '<div class="alert alert-success">✅ Campos llenados automáticamente</div>';
}

function mostrarValores() {
    const valores = {
        efectivo: document.getElementById('efectivo').value,
        tarjeta: document.getElementById('tarjeta').value,
        nequi: document.getElementById('nequi').value,
        daviplata: document.getElementById('daviplata').value,
        bancolombia: document.getElementById('bancolombia').value,
        total: document.getElementById('total').value,
        propina: document.getElementById('propina').value,
        mesa: document.getElementById('mesa').value,
        pago: document.getElementById('pago').value,
        pcedula: document.getElementById('pcedula').value
    };
    
    let html = '<h4>📊 Valores Actuales:</h4><ul>';
    Object.keys(valores).forEach(key => {
        html += '<li><strong>' + key + ':</strong> ' + valores[key] + '</li>';
    });
    html += '</ul>';
    
    document.getElementById('resultado').innerHTML = html;
}

function testFacturarInterfaz() {
    // Función idéntica a la de registroPmesa.php
    let nequi = formatCurrency(document.getElementById('nequi'));
    let daviplata = formatCurrency(document.getElementById('daviplata'));
    let bancolombia = formatCurrency(document.getElementById('bancolombia'));
    let pago = parseInt(document.getElementById('pago').value);
    let tarjeta = formatCurrency(document.getElementById('tarjeta'));
    let propina = formatCurrency(document.getElementById('propina'));
    let total = parseInt(document.getElementById('total').value);
    var totalDescuento = parseInt(document.getElementById('totalDescuento').value) || 0;
    var pcedula = document.getElementById('pcedula').value;
    let mesa = document.getElementById('mesa').value;
    
    if (pago == 1) {
        let efectivo = formatCurrency(document.getElementById('efectivo'));
        
        if (confirm('¿Confirme Facturar Pagada?')) {
            const datos = {
                efectivo: efectivo,
                bancolombia: bancolombia,
                nequi: nequi,
                daviplata: daviplata,
                tarjeta: tarjeta,
                pago: pago,
                pcedula: pcedula,
                totalDescuento: totalDescuento,
                total: total,
                propina: propina,
                mesa: mesa
            };
            
            console.log('📋 Datos enviados (idénticos a registroPmesa.php):', datos);
            
            fetch('ajaxFactura.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams(datos)
            })
            .then(response => response.text())
            .then(text => {
                console.log('📄 Respuesta:', text);
                
                if (text.includes('Pago realizado exitosamente') || text.includes('success')) {
                    document.getElementById('resultado').innerHTML = '<div class="alert alert-success">✅ ÉXITO: Facturación completada</div>';
                } else {
                    document.getElementById('resultado').innerHTML = '<div class="alert alert-danger">❌ Error: ' + text + '</div>';
                }
            })
            .catch(error => {
                console.error('❌ Error:', error);
                document.getElementById('resultado').innerHTML = '<div class="alert alert-danger">❌ Error de conexión: ' + error.message + '</div>';
            });
        }
    }
}
</script>

<style>
.alert {
    padding: 15px;
    margin: 15px 0;
    border-radius: 5px;
    border: 1px solid transparent;
}
.alert-success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
.alert-warning { background-color: #fff3cd; border-color: #ffeaa7; color: #856404; }
.alert-info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
.alert-danger { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
</style>
