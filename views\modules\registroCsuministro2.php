<?php 
	$registroCsuministro = new controllerCompraSuministro();
	$proveedores = $registroCsuministro -> listaProveedoresController();
	$registroCsuministro -> registroCompraSuministroController();
 ?>	
<link rel="stylesheet" type="text/css" href="../css/bootstrap.css">
<link rel="stylesheet" type="text/css" href="../css/bootstrap-theme.css">
<link rel="stylesheet" type="text/css" href="../estilo.css">
<link rel="stylesheet" type="text/css" href="../font-awesome/css/font-awesome.css">
<script src="../js/jquery-1.11.3.min.js" type="text/javascript"></script>
<script src="http://code.jquery.com/jquery-latest.js"></script>
<!--<script src="../js/jquery-latest.js" type="text/javascript"></script>-->
<script type="text/javascript">
	//------------------------- Nombre Buscar------------------------
		function buscarPlaca1(inputString) 
		 {
			if(inputString.length == 0) 
			 {	// Hide the suggestion box.
				$('#suggestions').hide();
			 } 
			else
			  {	//alert("Entro en el else de placa");
				$("#doss").load("views/modules/ajaxBuscarSuministroCompra.php", {placa: $("#dedo").val()}, function(){
	         	 
	     		 });
			 }
		 } 
	//------------------------- codigo Buscar-----------------------
		function buscarC(inputString) 
		 {
			if(inputString.length == 0) 
			 {	// Hide the suggestion box.
				$('#suggestions').hide();
			 } 
			else
			  {	//alert("Entro en el else de placa");
				$("#dos").load("views/modules/ajaxBuscarSuministroCod.php", {placa: $("#codigo").val()}, function(){
	         	 
	     		 });
			 }
		 } 
	//------------------ Nombre cerrar--------------------
	 /////////////Crear filas
	 function genera_tabla() 
	  {  // Obtener la referencia del elemento body
			  var body = document.getElementsByTagName("body")[0];				 
			  // Crea un elemento <table> y un elemento <tbody>
			  var tabla   = document.createElement("table");
			  var tblBody = document.createElement("tbody");				 
			  // Crea las celdas
			  for (var i = 0; i <11; i++) 
			   {
				    // Crea las hileras de la tabla
				    var hilera = document.createElement("tr");				 
				    for (var j = 0; j < 11; j++) 
				     {
					      // Crea un elemento <td> y un nodo de texto, haz que el nodo de
					      // texto sea el contenido de <td>, ubica el elemento <td> al final
					      // de la hilera de la tabla
					      var celda = document.createElement("td");
					      var textoCelda = document.createTextNode("celda en la hilera "+i+", columna "+j);
					      celda.appendChild(textoCelda);
					      hilera.appendChild(celda);
			     	 }			 
				    // agrega la hilera al final de la tabla (al final del elemento tblbody)
				    tblBody.appendChild(hilera);
		  		}			 
			  // posiciona el <tbody> debajo del elemento <table>
			  tabla.appendChild(tblBody);
			  // appends <table> into <body>
			  body.appendChild(tabla);
			  // modifica el atributo "border" de la tabla y lo fija a "2";
			  tabla.setAttribute("border", "1");
			 }
		///////////fin crear filas
		$(document).ready(function()
		{    /**  * Funcion para añadir una nueva columna en la tabla*/	     
		    $("#add").click(function()
		    	{
		            // Obtenemos el numero de filas (td) que tiene la primera columna
		            // (tr) del id "tabla"
		            var tds=$("#tabla tr:first td").length;
		            //variable de los imput creo
		            //var  nombre=$("#nombre");
		            var nombre = document.cal.nombre.value;
		            var codigo = document.cal.codigo.value;
		            var cantidad = document.cal.cantidad.value;
		            var precioC = document.cal.precioC.value;   
		            var precioV = document.cal.precioV.value;   
		            var descuento = document.cal.descuento.value;   
		            var sid = document.cal.sid.value;   
		            var totalCompra = document.cal.total.value;   
		            var diferenciaF = 0;   
		            var valorFactura = document.cal.valor_compraCsuministroRegistro.value;   
		            // Obtenemos el total de columnas (tr) del id "tabla"
		            var trs=$("#tabla tr").length;
		            var nuevaFila="<tr>";	           
		            for(var i=0;i<tds;i++)
		             {	// añadimos las columnas	
		                    var des =(precioC*descuento/100)/cantidad;         
		               switch (i) 
			            {
						case 0: nuevaFila+="<td><input class='form-control' type='text' name='codigo"+trs+"' value='"+codigo+ "' required /> </td>";
							break;  
						case 1: nuevaFila+="<td><input class='form-control' type='text' name='nombre"+trs+"' value='"+nombre+ "' required /> </td>";
						       break; 
						case 2:  nuevaFila+="<td><input class='form-control' type='text' name='cantidad"+trs+"' value='"+cantidad+ "' required /> </td>";
							break;	
						case 3: nuevaFila+="<td><input class='form-control' type='text' name='precioC"+trs+"' value='"+precioC-des+ "' required /> </td>";
							break;	
						case 4: nuevaFila+="<td><input class='form-control' type='text' name='precioCo"+trs+"' value='"+precioC+ "' required /> </td>";
							break;	
						case 5: nuevaFila+="<td><input class='form-control' type='text' name='precioV"+trs+"' value='"+precioV+ "' required /> </td>";
							break;	
						case 6: nuevaFila+="<td><input class='form-control' type='text' name='descuento"+trs+"' value='"+descuento+ "' required /> </td>";
							break;
						default:  nuevaFila+="<td><input class='form-control' type='hidden' name='sid"+trs+"' value='"+sid+ "' readonly='readonly'  required /> </td>"; 
					   }
		             }
		             if (trs==1) 
		              {
		              	totalCompra = parseInt(precioC) ;
		              }
		             else
		              {	totalCompra = parseInt(precioC) +parseInt(totalCompra);   }	             
		             diferencia=parseInt(valorFactura)-parseInt(totalCompra);
		             //alert( "precioT" +totalCompra+" cantidad:"+cantidad);
		            // Añadimos una columna con el numero total de filas.
		            // Añadimos uno al total, ya que cuando cargamos los valores para la
		            // columna, todavia no esta añadida	            
		            nuevaFila+="<td>"+(trs)+" ";
		            nuevaFila+="</tr>";
		            $("#tabla").append(nuevaFila);
		            document.cal.nombre.value="";
		            document.cal.codigo.value="";
		            document.cal.precioV.value="";
		            document.cal.precioC.value="";
		            document.cal.descuento.value="";
		            document.cal.sid.value="";
		            document.cal.fila.value=trs;
		            document.cal.total.value=totalCompra  ; 
		            document.cal.diferencia.value=diferencia  ; 
		            //alert( totalCompra);
		        });	 
		        /**
		         * Funcion para eliminar la ultima columna de la tabla.
		         * Si unicamente queda una columna, esta no sera eliminada
		         */
		     $("#del").click(function()
		        {// Obtenemos el total de columnas (tr) del id "tabla"
		            var trs=$("#tabla tr").length;
		            if(trs>1)
		            {// Eliminamos la ultima columna
		                $("#tabla tr:last").remove();
		              /*  var precioC = document.cal.precioC+trs+.value;
		                var cantidad = document.cal.cantidad+trs+.value;
		                var total = document.cal.total.value;
		                var valorFactura = document.cal.valor_compraCsuministroRegistro.value;
		                document.cal.total.value=total-(precioC*cantidad)  ;
		                document.cal.diferencia.value=diferencia  ;*/
		            }
		        });
		       // $("#destino").load("views/modules/ajaxFactura.php", {matriz: matriz,trs: trs, }, function(){
				        						 //alert("recibidos los datos por ajax efectivo"); 		 
				     						///});	
		});
	

</script>
<style>
td, input {padding:5px;}
</style>
<?php
 if (!isset($_POST['datos'])) 
	{ 
		//echo "<script>alert(' cantidad".$cantidad['1']."')</script>";
	}
?>
<h1>REGISTRO DE COMPRA SUMINISTRO</h1>
<br><br><br>

	

<form action="" method="post" name="cal">
	<div>			
	<div  class="col-md-4" style="background-color:#00f; border-radius: 15px 15px 15px 15px; padding:12px">	
		<h4 >Datos de la Factura</h4>
		<table>		 	
	<tr>
		<td align="right"><label> PROVEEDOR </label></td>
		<td><?php  
			# # %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  proveedores  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
				if($proveedores=="error")
					{	echo "debe registrar el proveedores"; }
				else{
						echo "<label></label>";
						$result='<select name="proveedores"  id="proveedores" autofocus="autofocus" required>';
						$result.=' <option value="-1">proveedores</option>';
						foreach ($proveedores as $row => $item)
						 	{	$result.=' <option value="'.$item["id"].'">'.$item["nombre"].'</option>';	}	
						 $result.='</select>';
						 echo $result;
					}  
			# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  End proveedores  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  max="<?=$_SESSION["fecha"];
			 ?>&laquo;<a class="enlace" href="registroProveedor" target="_blank"> Registrar proveedor</a>&raquo;<br></td>
	</tr>
	<tr>
		<td align="right"><label>  VALOR FACTURA  </label></td>
		<td><input type="text" placeholder="valor_compra" name="valor_compraCsuministroRegistro" required></td>
	</tr>
	<tr>
		<td align="right"><label> FECHA </label></td>
		<td><input type="date" placeholder="AAAA-MM-DD" name="fecha_horaCsuministroRegistro"  required></td>
	</tr>
	<tr>
		<td align="right"><label> No FACTURA  </label></td>
		<td><input type="text" placeholder="numero factura compra" name="numero_factura_compra" ></td>
	</tr>
	<tr>
		<td align="right"><label> ESTADO  </label></td>
		<td><div >
	  		<select name="pago" id="pago" >
		  <option value="1">Cancelada</option>
		  <option value="2">Credito</option>				  
		</select>
	  	</div></td>
	</tr>
	<tr>
		<td align="right"><label  > ABONO  </label></td>
		<td><input type="text" placeholder="$" name="abono" value="0" id="abono" ></td>
	</tr>		
	</table>				
</div>		
<div  class="col-md-8" style="background-color:#FFF; border-radius: 15px 15px 15px 15px; padding:12px">	

	<H3>Buscar Codigo</H3>
	<br>
	<label for=""><b>Nombre:</b></label><input type="text" placeholder="Nombre Suministros" class="mayuscula"  name="dedo" id="dedo" size="38" onkeyup="buscarPlaca1(this.value);"  >&nbsp;&nbsp;&nbsp;&nbsp;
	<br>
<div id="doss"></div>
	<br>
	<H3>Detalle de la Factura</H3>

	<label> Codigo  : <input type="text" placeholder="codigo Barra" name="codigo" id="codigo" onkeyup="buscarC(this.value);" autofocus ></label>
	<label>Cantidad<input type="text" name="cantidad" id="cantidad" style="width: 60px;" value="1"></label>
	
		</label><input type="hidden"  value="2" name="tipo" >
		</label><input type="hidden"  value="1" name="tiposuministro" >		
	<div id="dos"></div>
	<input type="hidden" name="fila" id="fila"> <br>
	<input type="button" id="add" value="Agregar" >
	<input type="button" id="del" value="eliminar la ultima fila " >
	<!-- class="btn btn-sm btn-success"  class="btn btn-sm btn-danger"
		class="btn btn-sm btn-primary"class="btn btn-sm btn-warning"
	-->			
	<p>	
	</div align="center">		
    <table id="tabla" class="table" border=1>
     <tr><td>Codigo</td>
        <td>Nombre</td>
        <td >Cantidad</td>
		<td >Precio Unidad des</td>
		<td >Precio Compra</td>
		
      <td>Precio Venta</td>  
      <td >Descuento</td>
      <td ></td>
         <!--  podemos añadir tantas columnas como deseemos -->
        <!--<td>tercera columna</td><input type="submit" value="Enviar">-->
     </tr>
    </table>
    <table>	 	
		<tr>			
			<td colspan="5">Total</td>
			<td><input type="text" name="total" id="total" value="0" ></td>
	       <td><input type="text" name="diferencia" id="diferencia" ></td>
	    </tr>
	</table>
	<button type="submit" name="datos"  >Guardar </button>
    <!--<button type="reset" name="borrar"  >Limpiar </button>-->
    <div id="destino">	</div>
</form>
