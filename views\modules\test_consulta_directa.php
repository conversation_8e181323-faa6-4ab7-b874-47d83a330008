<?php

session_start();

if(!$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "../../models/crudPedidosCategorias.php";

echo "<h2>🧪 Test Consulta Directa - Pedidos Pendientes</h2>";
echo "<hr>";

// Test directo del modelo
echo "<h3>1. Test Modelo crudPedidosCategorias</h3>";

echo "<h4>BAR:</h4>";
$pedidos_bar = DatosPedidosCategorias::obtenerPedidosPendientesModel('bar');
echo "<p><strong>Resultado:</strong> " . count($pedidos_bar) . " pedidos encontrados</p>";
if (count($pedidos_bar) > 0) {
    echo "<ul>";
    foreach ($pedidos_bar as $pedido) {
        echo "<li>Pedido {$pedido['numero_pedido']} - Mesa {$pedido['mesa_numero']} - {$pedido['productos_detalle']}</li>";
    }
    echo "</ul>";
} else {
    echo "<p style='color: red;'>❌ No se encontraron pedidos para BAR</p>";
}

echo "<h4>COCINA:</h4>";
$pedidos_cocina = DatosPedidosCategorias::obtenerPedidosPendientesModel('cocina');
echo "<p><strong>Resultado:</strong> " . count($pedidos_cocina) . " pedidos encontrados</p>";
if (count($pedidos_cocina) > 0) {
    echo "<ul>";
    foreach ($pedidos_cocina as $pedido) {
        echo "<li>Pedido {$pedido['numero_pedido']} - Mesa {$pedido['mesa_numero']} - {$pedido['productos_detalle']}</li>";
    }
    echo "</ul>";
} else {
    echo "<p style='color: red;'>❌ No se encontraron pedidos para COCINA</p>";
}

echo "<h4>ASADOS:</h4>";
$pedidos_asados = DatosPedidosCategorias::obtenerPedidosPendientesModel('asados');
echo "<p><strong>Resultado:</strong> " . count($pedidos_asados) . " pedidos encontrados</p>";
if (count($pedidos_asados) > 0) {
    echo "<ul>";
    foreach ($pedidos_asados as $pedido) {
        echo "<li>Pedido {$pedido['numero_pedido']} - Mesa {$pedido['mesa_numero']} - {$pedido['productos_detalle']}</li>";
    }
    echo "</ul>";
} else {
    echo "<p style='color: red;'>❌ No se encontraron pedidos para ASADOS</p>";
}

echo "<hr>";
echo "<h3>2. Test Controlador</h3>";

require_once "../../controllers/controllerPedidosCategorias.php";
$controller = new ControllerPedidosCategorias();

echo "<h4>BAR (Controlador):</h4>";
$pedidos_bar_ctrl = $controller->mostrarPedidosPendientesController('bar');
echo "<p><strong>Resultado:</strong> " . count($pedidos_bar_ctrl) . " pedidos encontrados</p>";

echo "<h4>COCINA (Controlador):</h4>";
$pedidos_cocina_ctrl = $controller->mostrarPedidosPendientesController('cocina');
echo "<p><strong>Resultado:</strong> " . count($pedidos_cocina_ctrl) . " pedidos encontrados</p>";

echo "<h4>ASADOS (Controlador):</h4>";
$pedidos_asados_ctrl = $controller->mostrarPedidosPendientesController('asados');
echo "<p><strong>Resultado:</strong> " . count($pedidos_asados_ctrl) . " pedidos encontrados</p>";

echo "<hr>";
echo "<h3>3. Test Estadísticas</h3>";

echo "<h4>BAR (Estadísticas):</h4>";
$stats_bar = DatosPedidosCategorias::obtenerEstadisticasDiaModel('bar');
echo "<p><strong>Pendientes:</strong> {$stats_bar['pendientes']}</p>";
echo "<p><strong>Entregados hoy:</strong> {$stats_bar['entregados_hoy']}</p>";
echo "<p><strong>Ventas hoy:</strong> $" . number_format($stats_bar['total_ventas_hoy'], 0) . "</p>";

echo "<h4>COCINA (Estadísticas):</h4>";
$stats_cocina = DatosPedidosCategorias::obtenerEstadisticasDiaModel('cocina');
echo "<p><strong>Pendientes:</strong> {$stats_cocina['pendientes']}</p>";
echo "<p><strong>Entregados hoy:</strong> {$stats_cocina['entregados_hoy']}</p>";
echo "<p><strong>Ventas hoy:</strong> $" . number_format($stats_cocina['total_ventas_hoy'], 0) . "</p>";

echo "<h4>ASADOS (Estadísticas):</h4>";
$stats_asados = DatosPedidosCategorias::obtenerEstadisticasDiaModel('asados');
echo "<p><strong>Pendientes:</strong> {$stats_asados['pendientes']}</p>";
echo "<p><strong>Entregados hoy:</strong> {$stats_asados['entregados_hoy']}</p>";
echo "<p><strong>Ventas hoy:</strong> $" . number_format($stats_asados['total_ventas_hoy'], 0) . "</p>";

echo "<hr>";
echo "<p><a href='pedidosCocinaPendientes'>← Volver a Pedidos Cocina</a></p>";

?>
