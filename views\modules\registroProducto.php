<?php 
 $registro = new controllerProductos();
 $codigo=$registro -> listaProductos1Controller();
?>
<h1>REGISTRO DE PRODUCTO</h1>
<form method="post" name="hola">
	<table>
		<thead>
			<tr > <td colspan="6" ></td> </tr>
		</thead>
		<tr>
			<td><label> Codigo : </label></td>
			<td><input type="text" placeholder="codigo" name="codigo" value="<?=$codigo;?>"  required></td>
		</tr>
		<tr>
			<td><label> Nombre : </label></td>
			<td><input type="text" placeholder="nombre" name="nombreProductoRegistro" autofocus="autofocus" style="width:400px;" required></td>
		</tr>
		<tr>
			<td><label> Cocina : </label></td>
			<td> <select name="cocina">
				<option value="si">si</option>
				<option value="no">no</option>
			</select>
			</td>
		</tr>
		<tr>
			<td><label> Precio : </label></td>
			<td><input type="text" placeholder="precio" name="precioProductoRegistro" required></td>
		</tr>
		<thead>
			<tr > <td colspan="6" ></td> </tr>
		</thead>
	</table>
	<br>
<div>
	<a href="registroProductoSuministro" target="_blank"> Producto sin Suministro &raquo;</a>
</div>
	<input type="submit" value="Enviar">
<?php
	
	$registro -> registroProductoController();

	if(isset($_GET["action"]))
		{	if($_GET["action"] == "okp")
				{	echo "Registro Exitoso";	}
		}

?>


</form>

