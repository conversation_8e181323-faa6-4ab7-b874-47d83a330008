<style>
	.mayuscula
	{text-transform: uppercase;  }

	#estiloMula{color:#000;background:#fff;/*height:40px*/;	}
	.mula{color:#000;background:#fff;font-size: 12px;}
	.er{background:#ffff26;} /*ff7a4d - 00d9a3*/
	.programado{background:#00d9a3;}
	.listap{background:#e5e5e5 }
	.estees{background:#F7CBD9; }
</style>
<?php 
	ob_start();
	class controllerDepartamentoCiudad extends MvcController
		{
			#DEPARTAMENTO
			#---------------------------------
				#REGISTRO DE DEPARTAMENTO
				#------------------------------------
					public function registroDepartamentoController()
						{
							if(isset($_POST["nombreDepartamentoRegistro"]))
								{	
									$datosController =array('nombre'=>$_POST["nombreDepartamentoRegistro"]);
									echo "<script>alert('Entro Controller ".$datosController["nombre"]." no')</script>";	
									$respuesta = DatosDepartamentoCiudad::registroDepartamentoModel($datosController, "departamentos");
									if($respuesta == "success")
										{	header("location:index.php?action=okD");	}
									else
										{	header("location:index.php");	}
								}
						}

				#VISTA DE DEPARTAMENTO
				#------------------------------------
					public function vistaDepartamentoController()
						{
							$respuesta = DatosDepartamentoCiudad::vistaDepartamentoModel("departamentos");
							foreach($respuesta as $row => $item)
								{
								echo'<tr>						
										<td>'.$item["nombre"].'</td>																	
										<td><a href="index.php?action=editarDepartamento&id='.$item["id"].'">Editar</a></td>
										<td><a href="index.php?action=departamento&idBorrar='.$item["id"].'">Borrar</a></td>
									</tr>';
								}
						}

				#EDITAR DEPARTAMENTO
				#------------------------------------
					public function editarDepartamentoController()
						{
							$datosController = $_GET["id"];
							$respuesta = DatosDepartamentoCiudad::editarDepartamentoModel($datosController, "departamentos");
							echo' <input type="text" value="'.$respuesta["nombre"].'" name="nombreDepartamentoEditar" required>									 
							 <input type="hidden" value="'.$respuesta["id"].'" name="idDepartamentoEditar" required> 				
								 <input type="submit" value="Actualizar">';
						}

				#ACTUALIZAR DEPARTAMENTO
				#------------------------------------
					public function actualizarDepartamentoController()
						{echo "<script>alert('Entro Controller Actualizar Departamento')</script>";
							if(isset($_POST["nombreDepartamentoEditar"]))
								{
									$datosController = array(  "nombre"=>$_POST["nombreDepartamentoEditar"],																
																"id"=>$_POST["idDepartamentoEditar"]);				
									$respuesta = DatosDepartamentoCiudad::actualizarDepartamentoModel($datosController, "departamentos");
									if($respuesta == "success")
										{	header("location:index.php?action=cambioD");	}
									else
										{	echo "error";	}
								}				
						}

				#BORRAR DEPARTAMENTO
				#------------------------------------
					public function borrarDepartamentoController()
						{
							if(isset($_GET["idBorrar"]))
								{
									$datosController = $_GET["idBorrar"];				
									$respuesta = DatosDepartamentoCiudad::borrarDepartamentoModel($datosController, "departamentos");
									if($respuesta == "success")
										{	header("location:index.php?action=departamento");	}
								}
						}			
			#--------------------------------- 	

			#CIUDAD
			#---------------------------------
				#REGISTRO DE CIUDAD
				#------------------------------------
					public function registroCiudadController()
						{	//echo "<script>alert('Guardo')</script>";
							if(isset($_POST["nombreCiudadRegistro"]) && isset($_POST["departamento"]))
								{//echo "<script>alert('Entro Controller ')</script>";		
									$datosController =array('departamento_id'=>$_POST["departamento"],
															'nombre'=>$_POST["nombreCiudadRegistro"]);
									//echo "<script>alert('Entro Controller ".$datosController["nombre"]." no')</script>";	
									$respuesta = DatosDepartamentoCiudad::registroCiudadModel($datosController, "ciudad");
									if($respuesta == "success")
										{	header("location:index.php?action=okC");	}
									else
										{	header("location:index.php");	}
								}
						}

				#VISTA DE CIUDAD
				#------------------------------------
					public function vistaCiudadController()
						{
							$respuesta = DatosDepartamentoCiudad::vistaCiudadModel("ciudad");
							foreach($respuesta as $row => $item)
								{
								echo'<tr>						
										<td>'.$item["departamento_id"].'</td>						
										<td>'.$item["nombre"].'</td>						
										<td><a href="index.php?action=editarCiudad&id='.$item["id"].'"><button>Editar</button></a></td>
										<td><a href="index.php?action=ciudad&idBorrar='.$item["id"].'"><button>Borrar</button></a></td>
									</tr>';
								}
						}

				#EDITAR CIUDAD
				#------------------------------------
					public function editarCiudadController()
						{
							$datosController = $_GET["id"];
							$respuesta = DatosDepartamentoCiudad::editarCiudadModel($datosController, "ciudad");
							echo' <input type="text" value="'.$respuesta["departamento_id"].'" name="idDEditar" required>	
							 <input type="text" value="'.$respuesta["nombre"].'" name="nombreEditar" required> 
							 <input type="hidden" value="'.$respuesta["id"].'" name="idEditar" required> 				
								 <input type="submit" value="Actualizar">';
						}

				#ACTUALIZAR CIUDAD
				#------------------------------------
					public function actualizarCiudadController()
						{echo "<script>alert('Entro Controller Actualizar Producto')</script>";

							if(isset($_POST["nombreEditar"]))
								{
									$datosController = array(  "departamento_id"=>$_POST["idDEditar"],			
																"nombre"=>$_POST["nombreEditar"],				
																"id"=>$_POST["idEditar"]);				
									$respuesta = DatosDepartamentoCiudad::actualizarCiudadModel($datosController, "ciudad");
									if($respuesta == "success")
										{	header("location:index.php?action=cambioC");	}
									else
										{	echo "error";	}
								}				
						}

				#BORRAR CIUDAD
				#------------------------------------
					public function borrarCiudadController()
						{
							if(isset($_GET["idBorrar"]))
								{
									$datosController = $_GET["idBorrar"];				
									$respuesta = DatosDepartamentoCiudad::borrarCiudadModel($datosController, "ciudad");
									if($respuesta == "success")
										{	header("location:index.php?action=ciudad");	}
								}
						}
	}			
			#---------------------------------