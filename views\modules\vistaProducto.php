<script type="text/javascript">
	function buscarVenta(inputString)
	  {
		alert("Entro a Reporte Venta y Compra de productos");
				//alert("Entro en el else de placa");
			$("#destino").load("views/modules/ajaxReportProduct.php", {fecha1: $("#fecha1").val(),fecha2: $("#fecha2").val(),code: $("#code1").val()}, function(){
	          //alert("recibidos los datos por ajax");		 
	 		 });		
	  } // placa
	function aMayusculas(obj,id)
	 {
	    obj = obj.toUpperCase();
	    document.getElementById(id).value = obj;
	 }
	 //------------------------- Nombre Buscar------------------------
		function buscarPlaca1(inputString) 
		 {
			if(inputString.length == 0) 
			 {	// Hide the suggestion box.
				$('#suggestions').hide();
			 } 
			else
			  {	//alert("Entro en el else de placa");
				$("#destino").load("views/modules/ajaxBuscarSuministro.php", {placa: $("#dedo").val()}, function(){
	         	 
	     		 });
			 }
		 } 
	//------------------ Nombre cerrar--------------------
	//------------------------- codigo Buscar------------------------
		function buscarPlaca2(inputString) 
		 {
			if(inputString.length == 0) 
			 {	// Hide the suggestion box.
				$('#suggestions').hide();
			 } 
			else
			  {	//alert("Entro en el else de placa");
				$("#destino").load("views/modules/ajaxBuscarSuministroCo.php", {placa: $("#codigo").val()}, function(){
	         	 
	     		 });
			 }
		 } 
	//------------------ Nombre cerrar--------------------
</script>

<h1>Movimiento Producto por fecha</h1>
<form method="post">.
	<label for=""><b>Fecha Inicio</b></label>
	<input type="date" placeholder="aa-mm-dd"  name="fecha1" id="fecha1" style="width:180px;" autofocus="autofocus" required>
	<label for=""><b>Fecha Final</b></label>
	<input type="date" placeholder="aa-mm-dd"  name="fecha2" id="fecha2" style="width:180px;"  required><label for=""><b>Código Producto</b></label>
	<input type="text" placeholder="xxx111"  name="code1" id="code1" style="width:180px;"  required><br>	
	<input type="button" name="enviar" id="enviar" value="Enviar" onclick="buscarVenta(this.value);">
	<!--<input type="submit" name="enviar" id="enviar" value="Generar Excel" ">--><br>	
</form>
<form method="post">
	
	<label for=""><b>Nombre:</b></label><input type="text" placeholder="Nombre Suministros" class="mayuscula"  name="dedo" id="dedo" size="38" onkeyup="buscarPlaca1(this.value);"  >&nbsp;&nbsp;&nbsp;&nbsp;
	
	<label for=""><b>Codigo Barra : </b></label><input type="text" placeholder="0345" class="mayuscula"  name="codigo" id="codigo" size="38" onkeyup="buscarPlaca2(this.value);"  autofocus="autofocus"><br>
	
</form>
<br>

<span id="destino" > </span>
<div id="dos"></div>
<?php
$reporteProductos = new controllerVistaVentas();
$reporteProductos -> reporteExcelController();

?>
	</table>

<?php

if(isset($_GET["action"]))
	{	if($_GET["action"] == "cambio")
			{	echo "Cambio Exitoso";	}
	}

?>