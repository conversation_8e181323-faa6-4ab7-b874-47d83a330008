<?php
session_start();

// Simular sesión de administrador para pruebas
if (!isset($_SESSION["usuario"])) {
    $_SESSION["usuario"] = 1;
    $_SESSION["tipo_usuario"] = 1;
    $_SESSION["perfil"] = "administrador";
}

echo "<h2>🧪 Test Facturación - Suma de Productos Corregida</h2>";

// Incluir archivos necesarios
require_once "../../models/conexion.php";
require_once "../../models/crudFacturaAja.php";

$mesa_test = isset($_GET['mesa']) ? $_GET['mesa'] : 47;

echo "<p><strong>Mesa de prueba:</strong> $mesa_test</p>";

try {
    echo "<h3>1. 📋 Análisis del Problema</h3>";
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🔍 Problema Identificado:</h4>";
    echo "<ul>";
    echo "<li><strong>Síntoma:</strong> Factura impresa muestra $439,500 pero copia muestra $424,500</li>";
    echo "<li><strong>Causa:</strong> Durante facturación, productos duplicados se SOBREESCRIBEN en lugar de SUMARSE</li>";
    echo "<li><strong>Ubicación:</strong> <code>models/crudFacturaAja.php</code> línea 242</li>";
    echo "<li><strong>Error:</strong> <code>cantidad = VALUES(cantidad)</code> (sobreescribe)</li>";
    echo "<li><strong>Corrección:</strong> <code>cantidad = cantidad + VALUES(cantidad)</code> (suma)</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>2. 🔧 Corrección Implementada</h3>";
    echo "<div style='background: #c8e6c9; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>✅ Cambio Realizado:</h4>";
    echo "<p><strong>Antes:</strong></p>";
    echo "<pre style='background: #ffcdd2; padding: 10px;'>ON DUPLICATE KEY UPDATE
cantidad = VALUES(cantidad),  -- ❌ SOBREESCRIBE
precio = VALUES(precio)</pre>";
    echo "<p><strong>Después:</strong></p>";
    echo "<pre style='background: #c8e6c9; padding: 10px;'>ON DUPLICATE KEY UPDATE
cantidad = cantidad + VALUES(cantidad),  -- ✅ SUMA
precio = VALUES(precio)</pre>";
    echo "</div>";
    
    echo "<h3>3. 📊 Productos para Facturar (Método Corregido)</h3>";
    
    $productos = DatosFacturaAja::obtenerProductosParaFacturar($mesa_test);
    
    if (!empty($productos)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>Producto</th><th>Cantidad Agrupada</th><th>Precio Unit.</th><th>Descuento</th><th>Total</th><th>Pedidos Origen</th>";
        echo "</tr>";
        
        $total_calculado = 0;
        $total_productos = 0;
        
        foreach ($productos as $prod) {
            $descuento = ($prod["preciopr"] * ($prod["descuentopvm"] / 100));
            $precio_con_descuento = $prod["preciopr"] - $descuento;
            $subtotal = $precio_con_descuento * $prod["cantidadpvm"];
            $total_calculado += $subtotal;
            $total_productos += $prod["cantidadpvm"];
            
            echo "<tr>";
            echo "<td>{$prod['nombrepr']}</td>";
            echo "<td style='text-align: center; background: #e8f5e8;'><strong>{$prod['cantidadpvm']}</strong></td>";
            echo "<td style='text-align: right;'>$" . number_format($prod['preciopr']) . "</td>";
            echo "<td style='text-align: right;'>$" . number_format($descuento) . "</td>";
            echo "<td style='text-align: right; background: #fff3cd;'>$" . number_format($subtotal) . "</td>";
            echo "<td style='font-size: 11px;'>{$prod['numero_pedido']}</td>";
            echo "</tr>";
        }
        
        echo "<tr style='background: #4caf50; color: white; font-weight: bold;'>";
        echo "<td>TOTAL</td>";
        echo "<td style='text-align: center;'>$total_productos</td>";
        echo "<td colspan='2'></td>";
        echo "<td style='text-align: right;'>$" . number_format($total_calculado) . "</td>";
        echo "<td></td>";
        echo "</tr>";
        echo "</table>";
        
        echo "<div style='background: #c8e6c9; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<strong>✅ RESUMEN:</strong><br>";
        echo "• <strong>Productos únicos:</strong> " . count($productos) . "<br>";
        echo "• <strong>Cantidad total:</strong> $total_productos unidades<br>";
        echo "• <strong>Total a facturar:</strong> $" . number_format($total_calculado) . "<br>";
        echo "</div>";
        
        // Simular facturación con corrección
        echo "<h3>4. 🧾 Simular Facturación Corregida</h3>";
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>Datos de Facturación Simulada:</h4>";
        echo "<ul>";
        echo "<li><strong>Mesa:</strong> $mesa_test</li>";
        echo "<li><strong>Cliente:</strong> 12345678 (Cliente de prueba)</li>";
        echo "<li><strong>Total:</strong> $" . number_format($total_calculado) . "</li>";
        echo "<li><strong>Efectivo:</strong> $" . number_format($total_calculado + 5000) . "</li>";
        echo "<li><strong>Cambio:</strong> $5,000</li>";
        echo "</ul>";
        
        echo "<button onclick='simularFacturacionCorregida()' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;'>💰 Simular Facturación Corregida</button>";
        echo "</div>";
        
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<strong>❌ No hay productos para facturar en la mesa $mesa_test</strong><br>";
        echo "Asegúrate de que la mesa tenga pedidos en estado 'enviado' o 'entregado'.";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>❌ Error:</strong> " . $e->getMessage();
    echo "</div>";
}

?>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
function simularFacturacionCorregida() {
    if (confirm('🧪 ¿Simular facturación CORREGIDA de la mesa <?=$mesa_test?>?\n\nEsta versión SUMA productos duplicados en lugar de sobreescribirlos.')) {
        
        // Datos de facturación simulada
        const datosFactura = {
            efectivo: <?=$total_calculado + 5000?>,
            nequi: 0,
            daviplata: 0,
            bancolombia: 0,
            tarjeta: 0,
            pago: 1,
            pcedula: '12345678',
            propina: 0,
            mesa: <?=$mesa_test?>,
            total: <?=$total_calculado?>,
            totalDescuento: 0,
            tipoTarjeta: 'credito'
        };
        
        // Mostrar indicador de carga
        document.body.innerHTML += '<div id="loading" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); color: white; display: flex; align-items: center; justify-content: center; font-size: 20px; z-index: 9999;">⏳ Procesando facturación corregida...</div>';
        
        $.ajax({
            url: "ajaxFactura.php",
            type: "POST",
            data: datosFactura,
            timeout: 60000,
            success: function(response) {
                document.getElementById('loading').remove();
                console.log('Respuesta facturación:', response);
                
                if (response.includes('success') || response.includes('Facturación completada')) {
                    alert('✅ Facturación CORREGIDA completada exitosamente!\n\n📄 Ahora la factura y su copia deben mostrar el mismo total.\n\n🔍 Verifica que los productos duplicados se hayan SUMADO correctamente.');
                    setTimeout(() => location.reload(), 2000);
                } else {
                    alert('❌ Error en la facturación:\n\n' + response);
                }
            },
            error: function(xhr, status, error) {
                document.getElementById('loading').remove();
                console.error('Error AJAX:', error);
                alert('❌ Error de conexión: ' + error);
            }
        });
    }
}
</script>

<div style="margin: 20px 0; padding: 15px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px;">
    <h4>🔧 Corrección Crítica Implementada</h4>
    <p><strong>Problema raíz identificado:</strong> Durante la facturación, cuando había productos duplicados (mismo producto en diferentes pedidos), el sistema los SOBREESCRIBÍA en lugar de SUMARLOS.</p>
    <p><strong>Solución aplicada:</strong> Modificado <code>ON DUPLICATE KEY UPDATE</code> para usar <code>cantidad = cantidad + VALUES(cantidad)</code> en lugar de <code>cantidad = VALUES(cantidad)</code>.</p>
    <p><strong>Resultado esperado:</strong> Ahora las facturas deben mostrar el total correcto tanto en la impresión original como en las copias posteriores.</p>
</div>

<div style="margin: 20px 0; padding: 15px; background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 5px;">
    <h4>📝 Verificación de la Corrección</h4>
    <ol>
        <li><strong>Facturar una mesa nueva</strong> con productos duplicados</li>
        <li><strong>Verificar la factura impresa</strong> (total A)</li>
        <li><strong>Hacer copia de factura</strong> (total B)</li>
        <li><strong>Confirmar que A = B</strong> (totales coinciden)</li>
        <li><strong>Revisar base de datos</strong> que productos estén sumados correctamente</li>
    </ol>
</div>

<div style="margin: 20px 0; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;">
    <h4>⚠️ Importante</h4>
    <p><strong>Facturas anteriores:</strong> Las facturas ya generadas (como 376 y 380) NO se pueden corregir automáticamente porque los datos ya están en la base de datos.</p>
    <p><strong>Facturas futuras:</strong> Todas las nuevas facturas generadas después de esta corrección funcionarán correctamente.</p>
</div>
