<!DOCTYPE html>
<html>
<head>
    <title>Test Debug2 Controlador - Mesa 10</title>
</head>
<body>

<h1>🔍 Test Debug2 Controlador - Mesa 10</h1>

<p>Este test usa el controlador debug para identificar exactamente dónde falla en el modelo.</p>

<div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;">
    <h4>⚠️ Información del Debug:</h4>
    <p>El diagnóstico anterior mostró que el error ocurre en la llamada a <code>facturaajaxController</code>.</p>
    <p>Este test usará un controlador debug que captura errores específicos del modelo.</p>
</div>

<button onclick="testDebug2Controlador()" style="background: #dc3545; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;">
🔍 Test Debug2 Controlador
</button>

<div id="resultado" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border: 1px solid #ddd; border-radius: 5px;"></div>

<script>
function testDebug2Controlador() {
    document.getElementById('resultado').innerHTML = '<div style="color: blue;">⏳ Ejecutando test debug2 del controlador...</div>';
    
    fetch('ajaxFactura_debug2.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            efectivo: 50000,
            bancolombia: 0,
            nequi: 0,
            daviplata: 0,
            tarjeta: 0,
            pago: 1,
            pcedula: 'MARCO',
            totalDescuento: 0,
            total: 50000,
            propina: 0,
            mesa: 10,
            tipoTarjeta: 'credito',
            optimizada: true
        })
    })
    .then(response => {
        console.log('Status:', response.status);
        console.log('Status Text:', response.statusText);
        return response.text();
    })
    .then(text => {
        console.log('Respuesta completa:', text);
        
        let html = '<h4>📄 Resultado del Test Debug2:</h4>';
        html += '<p><strong>Status:</strong> ' + (text ? 'Respuesta recibida' : 'Sin respuesta') + '</p>';
        
        // Extraer comentarios de debug2
        const debug2Matches = text.match(/<!-- DEBUG2: [^>]+ -->/g);
        if (debug2Matches) {
            html += '<h5>🔍 Pasos de Debug2:</h5>';
            html += '<ul>';
            debug2Matches.forEach(match => {
                const mensaje = match.replace(/<!-- DEBUG2: /, '').replace(/ -->/, '');
                html += '<li>' + mensaje + '</li>';
            });
            html += '</ul>';
        }
        
        // Buscar errores específicos
        if (text.includes('ERROR FATAL') || text.includes('EXCEPCIÓN')) {
            html += '<div style="background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;">';
            html += '<h5 style="color: #721c24;">❌ Error Detectado:</h5>';
            
            // Extraer mensajes de error
            const errorMatches = text.match(/Error [^:]+: [^<]+/g);
            if (errorMatches) {
                errorMatches.forEach(error => {
                    html += '<p style="color: #721c24;"><strong>' + error + '</strong></p>';
                });
            }
            html += '</div>';
        }
        
        html += '<h5>📄 Respuesta Completa:</h5>';
        html += '<pre style="background: #fff; padding: 10px; border: 1px solid #ddd; border-radius: 3px; white-space: pre-wrap; max-height: 400px; overflow-y: auto;">' + text + '</pre>';
        
        if (text.includes('success_debug2')) {
            html += '<div style="color: green; font-weight: bold; font-size: 18px;">✅ ÉXITO: Test debug2 completado</div>';
        } else if (text.includes('Error') || text.includes('error')) {
            html += '<div style="color: red; font-weight: bold; font-size: 18px;">❌ ERROR detectado en test debug2</div>';
        } else {
            html += '<div style="color: orange; font-weight: bold; font-size: 18px;">⚠️ Respuesta inesperada</div>';
        }
        
        document.getElementById('resultado').innerHTML = html;
    })
    .catch(error => {
        console.error('Error completo:', error);
        
        let html = '<h4>❌ Error en Test Debug2:</h4>';
        html += '<p><strong>Error:</strong> ' + error.message + '</p>';
        html += '<p>Este error indica el punto exacto donde falla en el controlador/modelo</p>';
        
        document.getElementById('resultado').innerHTML = html;
    });
}
</script>

<br><a href="index.php?action=registroPmesa&ida=10" style="background: #007bff; color: white; padding: 10px; text-decoration: none;">🔙 Volver a Mesa 10</a>
<br><a href="test_debug_rapido.php" style="background: #6c757d; color: white; padding: 10px; text-decoration: none; margin: 5px;">🔙 Volver a Debug Rápido</a>

</body>
</html>
