<?php

session_start();

if(!isset($_SESSION["validar"]) || !$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "models/conexion.php";

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Test ENUM Problema</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
</head>
<body>

<div class="container">
    <h2>🔍 Test: Problema con ENUM y COUNT</h2>
    <hr>
    
    <div class="panel panel-danger">
        <div class="panel-heading">
            <h3 class="panel-title">🚨 Problema Identificado: COUNT(pvm.id) vs COUNT(*)</h3>
        </div>
        <div class="panel-body">
            <p><strong>La tabla productos tiene ENUM('bar','cocina','asados')</strong></p>
            <p><strong>El problema puede estar en COUNT(pvm.id) en lugar de COUNT(*)</strong></p>
        </div>
    </div>
    
    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">🧪 Test 1: Consulta SIN COUNT - Solo JOIN</h3>
        </div>
        <div class="panel-body">
            <?php
            echo "<h4>Consulta básica para BAR sin GROUP BY:</h4>";
            $stmt = Conexion::conectar()->prepare("
                SELECT 
                    p.id as pedido_id,
                    p.numero_pedido,
                    p.fecha_envio,
                    p.mesa_id,
                    m.nombre as mesa_numero,
                    pr.nombre as producto_nombre,
                    pr.categoria,
                    pvm.cantidad,
                    pr.precio
                FROM pedidos p
                INNER JOIN mesas m ON p.mesa_id = m.id
                INNER JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
                INNER JOIN productos pr ON pvm.productos_id = pr.id
                WHERE p.estado = 'enviado'
                AND pr.categoria = 'bar'
                ORDER BY p.numero_pedido, pr.nombre
            ");
            $stmt->execute();
            $resultados_sin_group = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<p><strong>Resultados sin GROUP BY:</strong> " . count($resultados_sin_group) . " registros</p>";
            
            if (count($resultados_sin_group) > 0) {
                echo "<table class='table table-condensed'>";
                echo "<thead><tr><th>Pedido</th><th>Mesa</th><th>Producto</th><th>Categoría</th><th>Cantidad</th><th>Precio</th></tr></thead>";
                echo "<tbody>";
                foreach ($resultados_sin_group as $reg) {
                    echo "<tr>";
                    echo "<td>{$reg['numero_pedido']}</td>";
                    echo "<td>{$reg['mesa_numero']}</td>";
                    echo "<td>{$reg['producto_nombre']}</td>";
                    echo "<td>{$reg['categoria']}</td>";
                    echo "<td>{$reg['cantidad']}</td>";
                    echo "<td>$" . number_format($reg['precio'], 0) . "</td>";
                    echo "</tr>";
                }
                echo "</tbody></table>";
            } else {
                echo "<div class='alert alert-danger'>❌ No hay registros sin GROUP BY</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-warning">
        <div class="panel-heading">
            <h3 class="panel-title">🧪 Test 2: Consulta con COUNT(*) en lugar de COUNT(pvm.id)</h3>
        </div>
        <div class="panel-body">
            <?php
            echo "<h4>Consulta con COUNT(*) para BAR:</h4>";
            $stmt = Conexion::conectar()->prepare("
                SELECT DISTINCT
                    p.id as pedido_id,
                    p.numero_pedido,
                    p.fecha_envio,
                    p.mesa_id,
                    m.nombre as mesa_numero,
                    COUNT(*) as total_productos,
                    GROUP_CONCAT(
                        CONCAT(pr.nombre, ' x', pvm.cantidad)
                        ORDER BY pr.nombre
                        SEPARATOR ', '
                    ) as productos_detalle,
                    SUM(pvm.cantidad * pr.precio) as total_precio
                FROM pedidos p
                INNER JOIN mesas m ON p.mesa_id = m.id
                INNER JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
                INNER JOIN productos pr ON pvm.productos_id = pr.id
                WHERE p.estado = 'enviado'
                AND pr.categoria = 'bar'
                GROUP BY p.id, p.numero_pedido, p.fecha_envio, p.mesa_id, m.nombre
                ORDER BY p.fecha_envio ASC
            ");
            $stmt->execute();
            $resultados_count_asterisco = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<p><strong>Resultados con COUNT(*):</strong> " . count($resultados_count_asterisco) . " pedidos</p>";
            
            if (count($resultados_count_asterisco) > 0) {
                echo "<table class='table table-striped'>";
                echo "<thead><tr><th>Pedido</th><th>Mesa</th><th>Productos</th><th>Total</th></tr></thead>";
                echo "<tbody>";
                foreach ($resultados_count_asterisco as $pedido) {
                    echo "<tr>";
                    echo "<td>{$pedido['numero_pedido']}</td>";
                    echo "<td>{$pedido['mesa_numero']}</td>";
                    echo "<td>{$pedido['productos_detalle']}</td>";
                    echo "<td>$" . number_format($pedido['total_precio'], 0) . "</td>";
                    echo "</tr>";
                }
                echo "</tbody></table>";
            } else {
                echo "<div class='alert alert-danger'>❌ No hay resultados con COUNT(*)</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-success">
        <div class="panel-heading">
            <h3 class="panel-title">🧪 Test 3: Consulta con COUNT(pvm.productos_id)</h3>
        </div>
        <div class="panel-body">
            <?php
            echo "<h4>Consulta con COUNT(pvm.productos_id) para BAR:</h4>";
            $stmt = Conexion::conectar()->prepare("
                SELECT DISTINCT
                    p.id as pedido_id,
                    p.numero_pedido,
                    p.fecha_envio,
                    p.mesa_id,
                    m.nombre as mesa_numero,
                    COUNT(pvm.productos_id) as total_productos,
                    GROUP_CONCAT(
                        CONCAT(pr.nombre, ' x', pvm.cantidad)
                        ORDER BY pr.nombre
                        SEPARATOR ', '
                    ) as productos_detalle,
                    SUM(pvm.cantidad * pr.precio) as total_precio
                FROM pedidos p
                INNER JOIN mesas m ON p.mesa_id = m.id
                INNER JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
                INNER JOIN productos pr ON pvm.productos_id = pr.id
                WHERE p.estado = 'enviado'
                AND pr.categoria = 'bar'
                GROUP BY p.id, p.numero_pedido, p.fecha_envio, p.mesa_id, m.nombre
                ORDER BY p.fecha_envio ASC
            ");
            $stmt->execute();
            $resultados_count_productos = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<p><strong>Resultados con COUNT(pvm.productos_id):</strong> " . count($resultados_count_productos) . " pedidos</p>";
            
            if (count($resultados_count_productos) > 0) {
                echo "<table class='table table-striped'>";
                echo "<thead><tr><th>Pedido</th><th>Mesa</th><th>Productos</th><th>Total</th></tr></thead>";
                echo "<tbody>";
                foreach ($resultados_count_productos as $pedido) {
                    echo "<tr>";
                    echo "<td>{$pedido['numero_pedido']}</td>";
                    echo "<td>{$pedido['mesa_numero']}</td>";
                    echo "<td>{$pedido['productos_detalle']}</td>";
                    echo "<td>$" . number_format($pedido['total_precio'], 0) . "</td>";
                    echo "</tr>";
                }
                echo "</tbody></table>";
            } else {
                echo "<div class='alert alert-danger'>❌ No hay resultados con COUNT(pvm.productos_id)</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-primary">
        <div class="panel-heading">
            <h3 class="panel-title">🔍 Test 4: Verificar estructura de producto_vendido_mesa</h3>
        </div>
        <div class="panel-body">
            <?php
            echo "<h4>Verificar si pvm.id existe:</h4>";
            $stmt = Conexion::conectar()->prepare("DESCRIBE producto_vendido_mesa");
            $stmt->execute();
            $estructura = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $tiene_id = false;
            echo "<table class='table table-condensed'>";
            echo "<thead><tr><th>Campo</th><th>Tipo</th><th>Key</th></tr></thead>";
            echo "<tbody>";
            foreach ($estructura as $campo) {
                if ($campo['Field'] == 'id') {
                    $tiene_id = true;
                }
                echo "<tr>";
                echo "<td><strong>{$campo['Field']}</strong></td>";
                echo "<td>{$campo['Type']}</td>";
                echo "<td>{$campo['Key']}</td>";
                echo "</tr>";
            }
            echo "</tbody></table>";
            
            if (!$tiene_id) {
                echo "<div class='alert alert-danger'>";
                echo "<strong>❌ PROBLEMA ENCONTRADO:</strong> La tabla producto_vendido_mesa NO tiene campo 'id'.<br>";
                echo "Por eso COUNT(pvm.id) devuelve 0 resultados.<br>";
                echo "<strong>Solución:</strong> Usar COUNT(*) o COUNT(pvm.productos_id) en lugar de COUNT(pvm.id)";
                echo "</div>";
            } else {
                echo "<div class='alert alert-success'>✅ La tabla SÍ tiene campo 'id'</div>";
            }
            ?>
        </div>
    </div>
    
    <hr>
    <div class="row">
        <div class="col-md-4">
            <a href="index.php?action=test_modelo_corregido" class="btn btn-primary btn-block">🔙 Test Anterior</a>
        </div>
        <div class="col-md-4">
            <a href="index.php?action=pedidosBarPendientes" class="btn btn-info btn-block">🍺 Ir a Bar</a>
        </div>
        <div class="col-md-4">
            <a href="index.php?action=pedidosCocinaPendientes" class="btn btn-warning btn-block">🍳 Ir a Cocina</a>
        </div>
    </div>
</div>

</body>
</html>
