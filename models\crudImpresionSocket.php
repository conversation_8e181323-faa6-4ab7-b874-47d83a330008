<?php
/**
 * Clase de impresión usando Sockets TCP/IP puros
 * Máxima eficiencia y velocidad para impresoras de red
 */
class ImpresionSocket {
    
    // Configuración fija de impresoras
    private static $IMPRESORAS = [
        // CONFIGURACIÓN TEMPORAL PARA PRUEBAS (Red 192.168.18.x)
        'test' => ['ip' => '*************', 'puerto' => 9100],

        // CONFIGURACIÓN PRODUCCIÓN (Red 192.168.68.x)
        'bar' => ['ip' => '**************', 'puerto' => 9100],
        'cocina' => ['ip' => '**************', 'puerto' => 9100],
        'asados' => ['ip' => '**************', 'puerto' => 9100]
    ];
    
    // Configuración de socket
    private static $TIMEOUT_CONEXION = 3; // segundos
    private static $TIMEOUT_ESCRITURA = 2; // segundos
    
    /**
     * Imprimir usando socket TCP directo
     * @param string $categoria - bar, cocina, asados
     * @param string $contenido - Texto a imprimir
     * @return array - Resultado de la impresión
     */
    public static function imprimir($categoria, $contenido) {
        $categoria = strtolower(trim($categoria));
        
        if (!isset(self::$IMPRESORAS[$categoria])) {
            return [
                'success' => false,
                'error' => "Categoría '$categoria' no válida",
                'metodo' => 'socket'
            ];
        }
        
        $impresora = self::$IMPRESORAS[$categoria];
        return self::enviarPorSocket($impresora['ip'], $impresora['puerto'], $contenido, $categoria);
    }
    
    /**
     * Enviar datos por socket TCP puro
     * @param string $ip
     * @param int $puerto
     * @param string $contenido
     * @param string $categoria
     * @return array
     */
    private static function enviarPorSocket($ip, $puerto, $contenido, $categoria) {
        $inicio = microtime(true);
        
        try {
            // Crear socket TCP
            $socket = socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
            
            if ($socket === false) {
                return [
                    'success' => false,
                    'error' => 'No se pudo crear socket: ' . socket_strerror(socket_last_error()),
                    'metodo' => 'socket',
                    'categoria' => $categoria
                ];
            }
            
            // Configurar timeouts
            socket_set_option($socket, SOL_SOCKET, SO_RCVTIMEO, [
                'sec' => self::$TIMEOUT_CONEXION, 
                'usec' => 0
            ]);
            socket_set_option($socket, SOL_SOCKET, SO_SNDTIMEO, [
                'sec' => self::$TIMEOUT_ESCRITURA, 
                'usec' => 0
            ]);
            
            // Conectar
            $resultado_conexion = socket_connect($socket, $ip, $puerto);
            
            if ($resultado_conexion === false) {
                $error = socket_strerror(socket_last_error($socket));
                socket_close($socket);
                
                return [
                    'success' => false,
                    'error' => "No se pudo conectar a $ip:$puerto - $error",
                    'metodo' => 'socket',
                    'categoria' => $categoria,
                    'tiempo_ms' => round((microtime(true) - $inicio) * 1000, 2)
                ];
            }
            
            // Preparar datos ESC/POS
            $datos = self::generarComandosESCPOS($contenido, $categoria);
            
            // Enviar datos
            $bytes_enviados = socket_write($socket, $datos, strlen($datos));
            
            if ($bytes_enviados === false) {
                $error = socket_strerror(socket_last_error($socket));
                socket_close($socket);
                
                return [
                    'success' => false,
                    'error' => "Error enviando datos: $error",
                    'metodo' => 'socket',
                    'categoria' => $categoria,
                    'tiempo_ms' => round((microtime(true) - $inicio) * 1000, 2)
                ];
            }
            
            // Cerrar socket
            socket_close($socket);
            
            $tiempo_total = round((microtime(true) - $inicio) * 1000, 2);
            
            return [
                'success' => true,
                'categoria' => $categoria,
                'ip' => $ip,
                'puerto' => $puerto,
                'bytes_enviados' => $bytes_enviados,
                'tiempo_ms' => $tiempo_total,
                'metodo' => 'socket',
                'timestamp' => date('Y-m-d H:i:s')
            ];
            
        } catch (Exception $e) {
            if (isset($socket) && $socket) {
                socket_close($socket);
            }
            
            return [
                'success' => false,
                'error' => 'Excepción: ' . $e->getMessage(),
                'metodo' => 'socket',
                'categoria' => $categoria,
                'tiempo_ms' => round((microtime(true) - $inicio) * 1000, 2)
            ];
        }
    }
    
    /**
     * Generar comandos ESC/POS optimizados
     * @param string $contenido
     * @param string $categoria
     * @return string
     */
    private static function generarComandosESCPOS($contenido, $categoria) {
        $datos = "";
        
        // Reset de impresora
        $datos .= "\x1B\x40";
        
        // Configurar codificación
        $datos .= "\x1B\x74\x00"; // Tabla de caracteres PC437
        
        // Encabezado centrado
        $datos .= "\x1B\x61\x01"; // Centrar
        $datos .= "\x1B\x21\x08"; // Texto doble altura
        $datos .= "MACARENA\n";
        $datos .= "\x1B\x21\x00"; // Texto normal
        $datos .= "===================\n";
        
        // Información de la impresora
        $datos .= "\x1B\x61\x00"; // Alinear izquierda
        $datos .= "\x1B\x21\x08"; // Texto doble altura
        $datos .= strtoupper($categoria) . "\n";
        $datos .= "\x1B\x21\x00"; // Texto normal
        
        // Fecha y hora
        $datos .= date('d/m/Y H:i:s') . "\n";
        $datos .= "-------------------\n";
        
        // Contenido principal
        $datos .= $contenido . "\n";
        
        // Pie
        $datos .= "-------------------\n";
        $datos .= "\x1B\x61\x01"; // Centrar
        $datos .= "Gracias por su visita\n";
        
        // Espacios y corte
        $datos .= "\n\n\n";
        $datos .= "\x1D\x56\x42\x00"; // Cortar papel (si la impresora lo soporta)
        
        return $datos;
    }
    
    /**
     * Test de conectividad usando sockets
     * @return array
     */
    public static function testConectividad() {
        $resultados = [];
        
        foreach (self::$IMPRESORAS as $categoria => $config) {
            $inicio = microtime(true);
            
            $socket = socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
            
            if ($socket === false) {
                $resultados[$categoria] = [
                    'status' => 'error',
                    'error' => 'No se pudo crear socket',
                    'ip' => $config['ip'],
                    'puerto' => $config['puerto']
                ];
                continue;
            }
            
            // Timeout corto para test
            socket_set_option($socket, SOL_SOCKET, SO_RCVTIMEO, ['sec' => 2, 'usec' => 0]);
            socket_set_option($socket, SOL_SOCKET, SO_SNDTIMEO, ['sec' => 2, 'usec' => 0]);
            
            $resultado_conexion = socket_connect($socket, $config['ip'], $config['puerto']);
            $tiempo = round((microtime(true) - $inicio) * 1000, 2);
            
            if ($resultado_conexion === false) {
                $error = socket_strerror(socket_last_error($socket));
                $resultados[$categoria] = [
                    'status' => 'offline',
                    'error' => $error,
                    'ip' => $config['ip'],
                    'puerto' => $config['puerto'],
                    'tiempo_ms' => $tiempo
                ];
            } else {
                $resultados[$categoria] = [
                    'status' => 'online',
                    'ip' => $config['ip'],
                    'puerto' => $config['puerto'],
                    'tiempo_ms' => $tiempo
                ];
            }
            
            socket_close($socket);
        }
        
        return $resultados;
    }
    
    /**
     * Imprimir pedido completo usando sockets
     * @param array $productos
     * @param string $mesa
     * @param string $numero_pedido
     * @return array
     */
    public static function imprimirPedido($productos, $mesa, $numero_pedido = null) {
        if (!$numero_pedido) {
            $numero_pedido = 'P' . str_pad(rand(1000, 9999), 6, '0', STR_PAD_LEFT);
        }
        
        // Agrupar productos por categoría de impresora
        $productos_por_impresora = self::agruparPorImpresora($productos);
        
        $resultados = [];
        $inicio_total = microtime(true);
        
        foreach ($productos_por_impresora as $categoria_impresora => $items) {
            $contenido = self::generarTicketPedido($mesa, $numero_pedido, $items);
            $resultado = self::imprimir($categoria_impresora, $contenido);
            $resultados[$categoria_impresora] = $resultado;
        }
        
        $tiempo_total = round((microtime(true) - $inicio_total) * 1000, 2);
        
        return [
            'resultados' => $resultados,
            'tiempo_total_ms' => $tiempo_total,
            'numero_pedido' => $numero_pedido,
            'mesa' => $mesa
        ];
    }
    
    /**
     * Agrupar productos por categoría de impresora
     */
    private static function agruparPorImpresora($productos) {
        $agrupados = [];
        
        foreach ($productos as $producto) {
            $categoria_impresora = self::obtenerCategoriaImpresora($producto['categoria'] ?? '');
            
            if (!isset($agrupados[$categoria_impresora])) {
                $agrupados[$categoria_impresora] = [];
            }
            
            $agrupados[$categoria_impresora][] = $producto;
        }
        
        return $agrupados;
    }
    
    /**
     * Mapear categoría de producto a impresora
     */
    private static function obtenerCategoriaImpresora($categoria_producto) {
        $mapeo = [
            'bebidas' => 'bar', 'cervezas' => 'bar', 'licores' => 'bar',
            'vinos' => 'bar', 'cocteles' => 'bar', 'refrescos' => 'bar',
            'carnes' => 'asados', 'parrilla' => 'asados', 'asados' => 'asados',
            'pescados' => 'asados', 'mariscos' => 'asados'
        ];
        
        return $mapeo[strtolower($categoria_producto)] ?? 'cocina';
    }
    
    /**
     * Generar contenido del ticket para pedido
     */
    private static function generarTicketPedido($mesa, $numero_pedido, $productos) {
        $contenido = "MESA: $mesa\n";
        $contenido .= "PEDIDO: $numero_pedido\n\n";
        
        $total = 0;
        foreach ($productos as $producto) {
            $cantidad = $producto['cantidad'] ?? 1;
            $precio = $producto['precio'] ?? 0;
            $subtotal = $precio * $cantidad;
            $total += $subtotal;
            
            $contenido .= sprintf("%-20s %2d\n", substr($producto['nombre'], 0, 20), $cantidad);
            if ($precio > 0) {
                $contenido .= sprintf("  $%s\n", number_format($subtotal, 0));
            }
        }
        
        if ($total > 0) {
            $contenido .= "\nTOTAL: $" . number_format($total, 0);
        }
        
        return $contenido;
    }
    
    /**
     * Obtener información de configuración
     */
    public static function obtenerConfiguracion() {
        return [
            'impresoras' => self::$IMPRESORAS,
            'timeout_conexion' => self::$TIMEOUT_CONEXION,
            'timeout_escritura' => self::$TIMEOUT_ESCRITURA,
            'metodo' => 'socket_tcp'
        ];
    }
}
?>
