<?php 
	ob_start();
	class controllerdevolucion extends Mvc<PERSON><PERSON>roller
		{			
			####### DEVOLUCION 
			#---------------------------------	
			#BUSCAR FACTURA 
			#----------------------------------------------
				 public function buscarFacturaController()
					{	
						session_start();
						if ($_POST["buscarfactura"] > 0) 
							{
								$_SESSION["idfactura"] = $_POST["buscarfactura"];
							}
					 	$factura=$_SESSION["idfactura"];
					 	//$factura=57;
					 	//echo "<script>alert('Entro Controller y efectivo=".$factura." forma ');</script>";
					 	//	
					 	$respuesta=Datosdevolucion::buscarFacturadevolucionModel($factura);
					if ($respuesta>0) 
						{	
							echo '<table border="1" class="table table-hover">				
									<thead>					
										<tr>						
											<th>PRODUCTOS COD.</th>				
											<th>PRODUCTOS</th>				
											<th>PRECIO</th>				
											<th>CANTIDA FACTURA</th>											
											<th>TOTAL</th>											
											<th colspan="2">DEVOLVER</th>
										</tr>
									</thead>
									<tbody>	';
									$Total=0;
									$cantidadProducto=0;
						 	$i=0;
						 	foreach ($respuesta as $row => $item) 
							 	{  
							 		if ($_SESSION["devoluciones"][$i]['dproductoid'] == $item ['productoid'] && $_SESSION["devoluciones"][$i]['fid'] == $item ['fid'])
								 		{
								 			$cantidarestante = $item ['cantidad']-$_SESSION["devoluciones"][$i]['dcantidad'];
								 		}
								 	else {	$cantidarestante = $item ['cantidad'];	}
								 	//echo "<script>alert('Entro Controller y efectivo=".$cantidarestante." forma ');</script>";
							 		$facturaNo=$item["fid"];								
														$pedidoFactura=$item["pedido"];								
														$facturaValor=$item["total"];								
														$totalProducto=$item["cantidad"]*$item["precio"];
							 		$_SESSION["list"][$i] = array('pedido ' => $item ['pedido'],
									 						   'productoid' =>$item ['productoid'],
									 						   'cantidad' =>$cantidarestante,
									 						   'total' =>$item ['total'],
									 						   'prnombre' =>$item ['prnombre'],
									 						   'fid' =>$item ['fid'],
									 						   'precio' =>$item ['precio']);
							 		//echo "<script>alert('Entro Controller producto=".$item['productoid']." Nombre ".$_SESSION["list"][$i]['prnombre']." ');</script>";						 		
							 		# --------------------------------------------------------------
														echo'<tr>						
																<td>'.$item["productoid"].'</td>	
																<td>'.$item["prnombre"].'</td>										
																<td>'.$item["precio"].'</td>						
																<td>'.$cantidarestante.' </td>									
																<td>'.$totalProducto.'</td>	
																<td>
																	<!--<a href="index.php?action=Devolucion&idPedidon='.$item["pedido"].'&idBorrar='.$item["productoid"].'">Total</a>-->
																	<a href="index.php?action=parcialDevolucion&idPedidon='.$item["pedido"].'&idEditar='.$item["productoid"].'">Parcial</a>

																</td>
																
															</tr>';
															$Total=$Total+$totalProducto;
															$cantidadProducto=$cantidadProducto+$item["cantidad"];
															$i++;
															//$_SESSION[contador]= $_SESSION[contador]+1; <td><a href="index.php?action=parcialDevolucion&idPedidon='.$item["pedido"].'&idEditar='.$item["productoid"].'">Parcial</a></td>
									}				
										$_SESSION["cont"]=$i;		
												echo ' 	<tr> 
																<td colspan="3"><h4>Total </h4></td>
																<td colspan="1"><h4>'.$cantidadProducto. '</h4></td>
																<td colspan="4"> <input type="text" readonly name="total" id="total" Value="'.$Total.'"></td>
													   	</tr>
													   		Codigo factura<input type="text" readonly value="'.$facturaNo.'" name="idEditar" required>
													   		Codigo pedido<input type="text" readonly value="'.$pedidoFactura.'" name="idEditar" required> 
															Valor de factura<input type="text" readonly value="'.$facturaValor.'" name="idEditar" required>
													   	</tbody>
											</table>';
							 		#---------------------------------------------------------
						}	
					else
						{	/*echo' <h3>No se encontraron factura con ese número, verifique</h3><br>
						 		<h3>No se encontraron factura con ese número, verifique</h3><br>
						 		<h3>No se encontraron factura con ese número, verifique</h3><br>';*/
							echo "<script>alert('No se encontraron factura con ese número, verifique')</script>";
							header("location:registroDevolucion");
						}
						return $respuesta; 	
					}			
				#-----------------------------------------------		
				#BUSCAR FACTURA 1 
				#----------------------------------------------
				 public function buscarFactura1Controller()
					{	
						session_start();
						
					 	$factura=$_SESSION["idfactura"];
					 	//$factura=57;
					 	//echo "<script>alert('Entro Controller y efectivo=".$factura." forma ');</script>";
					 	//	
					 	$respuesta=Datosdevolucion::buscarFacturadevolucionModel($factura);
					if ($respuesta>0) 
						{	
							echo '<table border="1" class="table table-hover">				
									<thead>					
										<tr>						
											<th>PRODUCTOS COD.</th>				
											<th>PRODUCTOS</th>				
											<th>PRECIO</th>				
											<th>CANTIDA FACTURA</th>											
											<th>TOTAL</th>											
											<th colspan="2">DEVOLVER</th>
										</tr>
									</thead>
									<tbody>	';
									$Total=0;
									$cantidadProducto=0;
						 	$i=0;
						 	foreach ($respuesta as $row => $item) 
							 	{  
							 		if ($_SESSION["devoluciones"][$i]['dproductoid'] == $item ['productoid'] && $_SESSION["devoluciones"][$i]['fid'] == $item ['fid'])
								 		{
								 			$cantidarestante = $item ['cantidad']-$_SESSION["devoluciones"][$i]['dcantidad'];
								 		}
								 	else {	$cantidarestante = $item ['cantidad'];	}
								 	//echo "<script>alert('Entro Controller y efectivo=".$cantidarestante." forma ');</script>";
							 		$facturaNo=$item["fid"];								
														$pedidoFactura=$item["pedido"];								
														$facturaValor=$item["total"];								
														$totalProducto=$item["cantidad"]*$item["precio"];
							 		$_SESSION["list"][$i] = array('pedido ' => $item ['pedido'],
									 						   'productoid' =>$item ['productoid'],
									 						   'cantidad' =>$cantidarestante,
									 						   'total' =>$item ['total'],
									 						   'prnombre' =>$item ['prnombre'],
									 						   'fid' =>$item ['fid'],
									 						   'precio' =>$item ['precio']);
							 		//echo "<script>alert('Entro Controller producto=".$item['productoid']." Nombre ".$_SESSION["list"][$i]['prnombre']." ');</script>";						 		
							 		# --------------------------------------------------------------
														echo'<tr>						
																<td>'.$item["productoid"].'</td>	
																<td>'.$item["prnombre"].'</td>										
																<td>'.$item["precio"].'</td>						
																<td>'.$cantidarestante.' </td>									
																<td>'.$totalProducto.'</td>	
																<td><a href="index.php?action=Devolucion&idPedidon='.$item["pedido"].'&idBorrar='.$item["productoid"].'">Total</a></td>
																<td><a href="index.php?action=parcialDevolucion&idPedidon='.$item["pedido"].'&idEditar='.$item["productoid"].'">Parcial</a></td>
															</tr>';
															$Total=$Total+$totalProducto;
															$cantidadProducto=$cantidadProducto+$item["cantidad"];
															$i++;
															//$_SESSION[contador]= $_SESSION[contador]+1;
									}				
										$_SESSION["cont"]=$i;		
												echo ' 	<tr> 
																<td colspan="3"><h4>Total </h4></td>
																<td colspan="1"><h4>'.$cantidadProducto. '</h4></td>
																<td colspan="4"> <input type="text" readonly name="total" id="total" Value="'.$Total.'"></td>
													   	</tr>
													   		Codigo factura<input type="text" readonly value="'.$facturaNo.'" name="idEditar" required>
													   		Codigo pedido<input type="text" readonly value="'.$pedidoFactura.'" name="idEditar" required> 
															Valor de factura<input type="text" readonly value="'.$facturaValor.'" name="idEditar" required>
													   	</tbody>
											</table>';
							 		#---------------------------------------------------------
						}	
					else
						{	echo' <h3>No se encontraron factura con ese número, verifique</h3><br>
						 		<h3>No se encontraron factura con ese número, verifique</h3><br>
						 		<h3>No se encontraron factura con ese número, verifique</h3><br>';
							echo "<script>alert('No se encontraron factura con ese número, verifique')</script>";	
						}
						return $respuesta; 	
					}			
				#-----------------------------------------------
				//idFacturadevolucionModel
				#DEVOLUCION PARCIAL Registrar
				#------------------------------------
					public function parcialDevolucionController()
						{	
							session_start();
							$datosController = array('idproducto' => $_GET["idEditar"], 
													 'idpedido' => $_GET["idPedidon"]); 
							$respuesta = Datosdevolucion::parcialDevolucionModel($datosController, "pedido_productos_mesa");
							$_SESSION ["cantidadF"] = $respuesta["cantidad"];
							echo' Cantidad <input type="text" value="'.$respuesta["cantidad"].'" name="cantidad" required>			
								<input type="hidden" value="'.$datosController["idpedido"].'" name="pedido" required>
								 <input type="hidden" value="'.$datosController["idproducto"].'" name="producto" required>
								 <input type="submit" value="Actualizar">';
						}
				#---------------------------------------
				#ACTUALIZAR DEVOLUCION PARCIAL
				#------------------------------------
					public function actualizarDevolucionController()
						{	
							//session_start();
							//echo "<script>alert('Entro Controller Actualizar Pedido')</script>";
							if(isset($_POST["cantidad"]))
								{
								if ($_POST["cantidad"]<=$_SESSION ["cantidadF"]) 
									{	
									   ##---------------Se busca el valor de la fila-----------------------
										for ($i=0; $i <$_SESSION["cont"] ; $i++) 
											{ 
												//echo "<script>alert('entro controle for pro".$_SESSION["list"][$i]['productoid']." valor de i: ".$i." produc ".$_GET["idBorrar"]."');</script>";
												if ($_SESSION["list"][$i]['productoid'] == $_POST["producto"]) 
													{
														//echo "<script>alert('entro controle for If')</script>";
													 	$c = $i;
													 	break;
													} 
												//echo "<script>alert('entro controle for despues del if ".$i." ');</script>";
											}
									   ##-------------------------------------------
										//echo "<script>alert('Entro Controller Actualizar IF ".$_SESSION ["cantidadF"]." ')</script>";
										$datosController = array( "cantidad"=>$_POST["cantidad"],	
																	"idpedido"=>$_POST["pedido"],
																	"fid" => $_SESSION["list"][$c]['fid'],
																	"idproducto"=>$_POST["producto"],
																	'precio' => $_SESSION["list"][$c]['precio'],
																	"usuario" => $_SESSION["usuario"]);
											$respuesta = Datosdevolucion::registroDevolucionModel($datosController);
											if($respuesta == "success")
												{	header("location:Devolucion");	}
														else
												{		echo "error";	}
									}
								else {	echo "<script>alert('La cantidad digitada es mayor, la de la factura, verifique')</script>";	}
							/**/	}
						}
				#---------------------------------------------
				#BUSCAR DEVOLUCIONES DE UNA FACTURA
				#------------------------------------
					public function buscarDevolucionController()
						{	
							//session_start();
							$factura=$_SESSION["idfactura"];
							if($factura>0)
								{
									$datosController = $factura;
									$respuesta = Datosdevolucion::buscarDevolucionModel($datosController, "desvoluciones");
									# Devolucion  did, dturno dfacturaid dmotivo dproductoid dcantidad dfecha dprecio  dpedido pronombre 
									if ($respuesta>0) 
										{
											echo '<h2> DEVOLUCIONES DE LAS FACTURA</h2> ';
											echo '<table border="1" class="table table-hover">				
													<thead>					
														<tr>
															<th>No</th>						
															<th>PRODUCTOS COD.</th>				
															<th>PRODUCTOS</th>				
															<th>PRECIO</th>				
															<th>CANTIDA FACTURA</th>											
															<th>TOTAL</th>											
														</tr>
													</thead>
													<tbody>	';
													$Total=0;
													$cantidadProducto=0;
										 	$i=0;
										 	foreach ($respuesta as $row => $item) 
											 	{
											 		$facturaNo=$item["dfacturaid"];								
													$pedidoFactura=$item["dpedido"];	
													$totalProducto=$item["dcantidad"]*$item["dprecio"];
											 		$_SESSION["devoluciones"][$i] = array('dpedido ' => $item ['dpedido'],
													 						   'dproductoid' =>$item ['dproductoid'],
													 						   'dcantidad' =>$item ['dcantidad'],
													 						   'dfecha' =>$item ['dfecha'],
													 						   'pronombre' =>$item ['pronombre'],
													 						   'fid' =>$item ['dfacturaid'],
													 						   'did' =>$item ['did'],
													 						   'dprecio' =>$item ['dprecio']);
											 		//echo "<script>alert('Entro Controller producto=".$item['dproductoid']." Nombre ".$_SESSION["devoluciones"][$i]['dcantidad']." ');</script>";						 		
											 		# --------------------------------------------------------------
											 								$n=$i+1;				
																		echo'<tr>						
																				<td>'.$n.'</td>	
																				<td>'.$item["dproductoid"].'</td>	
																				<td>'.$item["pronombre"].'</td>										
																				<td>'.$item["dprecio"].'</td>						
																				<td>'.$item["dcantidad"].' </td>									
																				<td>'.$totalProducto.'</td>	
																				<td><!--<a href="index.php?action=Devolucion&idEliminar='.$item["did"].'">Eliminar</a></td>-->
																				<!--<td><a href="index.php?action=editarDevolucion&idEditarD='.$item["did"].'">Editar</a></td-->
																			</tr>';
																			$Total=$Total+$totalProducto;
																			$cantidadProducto=$cantidadProducto+$item["dcantidad"];
																			$i++;
																			//$_SESSION[contador]= $_SESSION[contador]+1;
													}				
														$_SESSION["contD"]=$i;		
																echo ' 	<tr> 
																				<td colspan="3"><h4>Total </h4></td>
																				<td colspan="1"><h4>'.$cantidadProducto. '</h4></td>
																				<td colspan="4"> <input type="text" readonly name="total" id="total" Value="'.$Total.'"></td>
																	   	</tr>
																	   		Codigo factura<input type="text" readonly value="'.$facturaNo.'" name="idEditar" required>
																	   		Codigo pedido<input type="text" readonly value="'.$pedidoFactura.'" name="idEditar" required> 
																	   	</tbody>
															</table>';
											 		#---------------------------------------------------------
										}
									/*$_SESSION ["cantidadF"] = $respuesta["dcantidad"];  "sinregistro"
									echo' Cantidad <input type="text" value="'.$respuesta["dcantidad"].'" name="dcantidad" required>			
										<input type="hidden" value="'.$datosController["dpedido"].'" name="dpedido" required>
										 <input type="hidden" value="'.$datosController["dproducto"].'" name="dproducto" required>
										 <input type="submit" value="Actualizar">';*/
								}
						}
				#---------------------------------------
				#TOTAL DEVOLUCION  registrar 
				#------------------------------------
					public function totalDevolucionController()
						{	//echo "<script>alert('entro controle total Devolucion');</script>";
							if(isset($_GET["idBorrar"]))
								{	
									//echo "<script>alert('entro controle IF Borra total');</script>";
									session_start();
									for ($i=0; $i <$_SESSION["cont"] ; $i++) 
										{ 
											//echo "<script>alert('entro controle for pro".$_SESSION["list"][$i]['productoid']." valor de i: ".$i." produc ".$_GET["idBorrar"]."');</script>";
											if ($_SESSION["list"][$i]['productoid'] == $_GET["idBorrar"]) 
												{
													//echo "<script>alert('entro controle for If')</script>";
												 	$c = $i;
												 	break;
												} 
											//echo "<script>alert('entro controle for despues del if ".$i." ');</script>";
										}
									//echo "<script>alert('cargar variable ".$i." valor de c; ".$c."');</script>";									
									$datosController =array('idproducto' => $_GET["idBorrar"], 
														'idpedido' => $_GET["idPedidon"],
														'fid' => $_SESSION["list"][$c]['fid'],
														'cantidad' => $_SESSION["list"][$c]['cantidad'],
														'precio' => $_SESSION["list"][$c]['precio'],
														'usuario' => $_SESSION["usuario"]);				
								/*	*/$respuesta = Datosdevolucion::registroDevolucionModel($datosController);
									if($respuesta == "success")
										{	
											header("location:index.php?action=Devolucion");	
										}
								}
						}			
				#--------------------------------- 
				#EDITAR DEVOLUCION 
				#------------------------------------
					public function editarDevolucionController()
						{	
							session_start();
									//
									$datosController =  $_GET["idEditarD"] ;
									$respuesta = Datosdevolucion::editarDevolucionModel($datosController, "devoluciones");
									$_SESSION ["cantidadF"] = $respuesta["cantidad"];
									echo' Cantidad <input type="text" value="'.$respuesta["cantidad"].'" name="cantidadD" required>			
										<input type="hidden" value="'.$datosController.'" name="idDevolucion" required>
										 <input type="submit" value="Actualizar">';
						}
				#---------------------------------------
				#ACTUALIZAR DEVOLUCION
				#------------------------------------
					public function actualizar1DevolucionController()
						{	
							//session_start();
							//echo "<script>alert('Entro Controller Actualizar Pedido')</script>";
							if(isset($_POST["cantidadD"]))
								{
										$datosController = array( "cantidad"=>$_POST["cantidad"],	
																	"id"=>$_POST["idDevolucion"]);
											$respuesta = Datosdevolucion::actualizarDevolucionModel($datosController, "devoluciones");
											if($respuesta == "success")
												{	header("location:Devolucion");	}
														else
												{		echo "error";	}
									}
						}
				#---------------------------------------------
				#ELIMINAR DEVOLUCION
				#------------------------------------
					public function eliminarDevolucionController()
						{
							if(isset($_GET["idEliminar"]))
								{
									$datosController = $_GET["idEliminar"];				
									$respuesta = Datosdevolucion::eliminarDevolucionModel($datosController, "devoluciones");
									if($respuesta == "success")
										{	header("location:Devolucion");	}
								}
						}			
				#---------------------------------
				#ASIGNAR VARIABLE
				#----------------------------------------
					public function asignarvaribleController()
					 {	//echo "<script>alert('entro controle')</script>";
					 	//session_start();
					 	if(isset($_POST["buscarfactura"]))
							{
							 	$_SESSION["idfactura"]= $_POST['buscarfactura'];
							 	header("location:Devolucion");
							 }
					 }
				#----------------------------------------
				#CONSULTA DEVOLUCIONES  
				#----------------------------------------
					public function devolucionTotalController()
					 {	//echo "<script>alert('entro controle')</script>";
					 	$respuesta = Datosdevolucion::devolucionTotalModel();
					 	foreach($respuesta as $row => $item)
						 {
						 	echo'<tr>
												
												<td>'.$item["id"].'</td>											
												<td>'.$item["turnos_cajeros_id"].'</td>											
												<td>'.$item["ventas_id"].'</td>											
												<td>'.$item["motivo"].'</td>											
												<td>'.$item["producto_id"].'</td>											
												<td>'.$item["cantidad"].'</td>											
												<td>'.$item["fecha_devolucion"].'</td>											
												<td>'.$item["precio"].'</td>											
												<td>'.$item["pedido_id"].'</td>											
																														
												
												<td><a href="index.php?action=devolucionT&devolucion='.$item["ventas_id"].'" ><button>Ver</button></a> </td>						
											</tr>';
						 }
					 }
				#----------------------------------------
				
				##ASIGNAR CLIENTE
				#------------------------------------
					public function devolicionController()
						{	//echo "<script>alert('entro controle')</script>";
							if(isset($_GET["devolucion"]))
								{
									$_SESSION["idfactura"] = $_GET["devolucion"];
										header("location:index.php?action=Devolucion");	
								}
						}			
				#--------------------------------- 
	#---------------------------------
	#CONSULTA Ultimas facturas
	#----------------------------------------
	 public function ultimasFacturaController()
		{	//echo "<script>alert('entro controle')</script>";
		   session_start();
		  //$respuesta = Datosdevolucion::ultimasFacturaModel($_SESSION["punto_id"]);

		  $respuesta = Datosdevolucion::ultimasFacturaModel(1);
		  $formateador2 = new NumberFormatter( 'es_CO', NumberFormatter::CURRENCY );
		  $formateador = new NumberFormatter( 'en_US', NumberFormatter::DECIMAL );
		  echo '<h3> Ultimas 100 Facturas</h3>
		  <table border="1" class="table table-hover">
			<thead>
				<tr>
					<th>No</th>
					<th>Factura</th>
					<th>Valor Pagado</th>
					<th>Efectivo</th>
					<th>Fecha</th>								
				</tr>
			</thead>
			<tbody>	';
			$c=0;
		  foreach($respuesta as $row => $item)
			{
			 $c++;
			  echo'
			  	<tr>
					<td>'.$c.'</td>
					<td>'.$item["id"].'</td>
					<td>'.$formateador->format($item["total"]).'</td>
					<td>'.$formateador->format($item["efectivo"]).'</td>
					<td>'.$item["fecha_venta"].'</td>					
				</tr>';
			}
		 echo'
		 	</tbody><thead>
				<tr>
					<td colspan="6"> </td>							
				</tr>
			</thead>
		 </table>';
		}
	#---------------------------------
	#CONSULTA Ultimas facturas a Credito
	#----------------------------------------
	 public function ultimasFacturaCreditoController()
		{	//echo "<script>alert('entro controle')</script>";
		   session_start();
		  $respuesta = Datosdevolucion::ultimasFacturaCreditoModel($_SESSION["punto_id"]);
		  $formateador2 = new NumberFormatter( 'es_CO', NumberFormatter::CURRENCY );
		  $formateador = new NumberFormatter( 'en_US', NumberFormatter::DECIMAL );
		  echo '<h3> Ultimas 100 Facturas a Crédito</h3>
		  <table border="1" class="table table-hover">
			<thead>
				<tr>
					<th>No</th>
					<th>Factura</th>
					<th>Valor Pagado</th>
					<th>Descuento</th>
					<th>Fecha</th>								
				</tr>
			</thead>
			<tbody>	';
			$c=0;
		  foreach($respuesta as $row => $item)
			{
			 $c++;
			  echo'
			  	<tr>
					<td>'.$c.'</td>
					<td>'.$item["id"].'</td>
					<td>'.$formateador->format($item["total"]).'</td>
					<td>'.$formateador->format($item["descuento"]).'</td>
					<td>'.$item["fecha_credito"].'</td>					
				</tr>';
			}
		 echo'
		 	</tbody><thead>
				<tr>
					<td colspan="6"> </td>							
				</tr>
			</thead>
		 </table>';
		}
	#---------------------------------	
			#----------------------------------------------
		}			
			