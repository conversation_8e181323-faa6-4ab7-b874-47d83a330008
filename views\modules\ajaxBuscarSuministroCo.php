<?php session_start();
require_once "../../models/crud.php";
require_once "../../models/crudSuministro.php";
require_once "../../controllers/controller.php";
require_once "../../controllers/controllerSuministro.php";
	ini_set("session.cookie_lifetime","28800");
	ini_set("session.gc_maxlifetime","28800");
		//echo'<br> <script>alert("Entro al ajax Buscar Suministros '.$_POST['placa'].'");</script> <br>';
	 if(isset($_POST['placa']))
		 {
			$queryString = $_POST['placa'];
			//echo'<br> <script>alert("Entro al ajax");</script> <br>';
			$ajax=new controllerSuministro();
			$r=$ajax->ajaxBuscarCodigo1Controller($queryString);
			if ($r>0)
			 {	//echo' <script>alert("Se encontraron resultados");</script>';
				echo'<form method="post"  >
					<table  class="table table-hover">
				 	 <thead align="center">
			 			<tr >
			 			 <th>Nombre</th>  <th>Codigo Barra</th><th>Precio</th> <th>Bodega</th> <th>Sucursal</th> <th>Venta</th>
			 			 <th> -- </th>
			 			</tr>
				 	 </thead>
				 	 <tbody >';
					$v=$ajax->ventaController($r['pid']);
				 	if ($v>0)
				 	 { $c=$v['cantidad'];	}
				 	else { $c=0;}
 					echo'<tr >
 					 		<td>'.$r['snombre'].'</td>
 					 		<td>'.$r['scodigo'].'</td>
 					 		<td>'.$r['pprecio'].'</td>
 					 		<td>'.$r['scantidad'].'</td>
 					 		<td>'.$r['sucantidad'].'</td>
 					 		<td>'.$c.'</td>';
					 	if ($_SESSION["tipo_usuario"]==1)
				 		{
				 		 echo'<!--td align="center">$'.$costo['Valor_Compra_Producto'].'</td-->
					 		<td>
					 			<a href="index.php?action=editarProducto&id='.$r["pid"].'">Editar </a>
					 			<a href="index.php?action=editarSucursal&sid='.$r["sid"].'">Editar Tienda</a>
					 		</td>';
				 		}
			 		echo'
 					 	</tr>';

				echo'	</tbody>
 					 	</table></form>';
			 }
			else
			 {	echo' <h3>No se encontraron Resultados</h3>';	}
		 }




?>