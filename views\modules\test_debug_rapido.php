<!DOCTYPE html>
<html>
<head>
    <title>Test Debug Rápido - Mesa 10</title>
</head>
<body>

<h1>🔍 Test Debug Rápido - Mesa 10</h1>

<p>Este test usa la versión debug de ajaxFactura.php para identificar exactamente dónde ocurre el error 500.</p>

<button onclick="testDebugRapido()" style="background: #dc3545; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;">
🔍 Test Debug Facturación
</button>

<div id="resultado" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border: 1px solid #ddd; border-radius: 5px;"></div>

<script>
function testDebugRapido() {
    document.getElementById('resultado').innerHTML = '<div style="color: blue;">⏳ Ejecutando test debug...</div>';
    
    fetch('ajaxFactura_debug.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            efectivo: 50000,
            bancolombia: 0,
            nequi: 0,
            daviplata: 0,
            tarjeta: 0,
            pago: 1,
            pcedula: 'MARCO',
            totalDescuento: 0,
            total: 50000,
            propina: 0,
            mesa: 10,
            tipoTarjeta: 'credito',
            optimizada: true
        })
    })
    .then(response => {
        console.log('Status:', response.status);
        console.log('Status Text:', response.statusText);
        return response.text();
    })
    .then(text => {
        console.log('Respuesta completa:', text);
        
        let html = '<h4>📄 Resultado del Test Debug:</h4>';
        html += '<p><strong>Status:</strong> ' + (text ? 'Respuesta recibida' : 'Sin respuesta') + '</p>';
        
        // Extraer comentarios de debug
        const debugMatches = text.match(/<!-- DEBUG: [^>]+ -->/g);
        if (debugMatches) {
            html += '<h5>🔍 Pasos de Debug:</h5>';
            html += '<ul>';
            debugMatches.forEach(match => {
                const mensaje = match.replace(/<!-- DEBUG: /, '').replace(/ -->/, '');
                html += '<li>' + mensaje + '</li>';
            });
            html += '</ul>';
        }
        
        html += '<h5>📄 Respuesta Completa:</h5>';
        html += '<pre style="background: #fff; padding: 10px; border: 1px solid #ddd; border-radius: 3px; white-space: pre-wrap; max-height: 300px; overflow-y: auto;">' + text + '</pre>';
        
        if (text.includes('success_debug')) {
            html += '<div style="color: green; font-weight: bold; font-size: 18px;">✅ ÉXITO: Test debug completado</div>';
        } else if (text.includes('Error') || text.includes('error')) {
            html += '<div style="color: red; font-weight: bold; font-size: 18px;">❌ ERROR detectado en test debug</div>';
        } else {
            html += '<div style="color: orange; font-weight: bold; font-size: 18px;">⚠️ Respuesta inesperada</div>';
        }
        
        document.getElementById('resultado').innerHTML = html;
    })
    .catch(error => {
        console.error('Error completo:', error);
        
        let html = '<h4>❌ Error en Test Debug:</h4>';
        html += '<p><strong>Error:</strong> ' + error.message + '</p>';
        html += '<p>Este error indica el punto exacto donde falla ajaxFactura.php</p>';
        
        document.getElementById('resultado').innerHTML = html;
    });
}
</script>

<br><a href="index.php?action=registroPmesa&ida=10" style="background: #007bff; color: white; padding: 10px; text-decoration: none;">🔙 Volver a Mesa 10</a>

</body>
</html>
