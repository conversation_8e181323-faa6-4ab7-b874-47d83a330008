
<?php
ob_start();
class controller<PERSON><PERSON><PERSON> extends Mvc<PERSON>ontroller
{
	#REGISTRO COCINA
	#------------------------------------
	 public function registroCocinaController($mesa)
		{ 	 //echo "<script>alert('Mesa No Session: ".$mesa." ')</script>";
		 session_start();
		 //echo "<script>alert('Mesero session: ".$_SESSION["usuario"]." ')</script>";
			 $datosController =array('mesero'=>$_SESSION["usuario"],
									'mesa'=>$mesa);
				//echo "<script>alert('entro controle array ".$datosController['cantidad']." ')</script>";
			 $respuesta = DatosCocina::registroCocinaModel($datosController, "producto_vendido_mesa");
			 if($respuesta == "success")
				{	//$_SESSION["mesero"] = $_SESSION["usuario"];
					header("location:mesa");
				}
			 else
				{
					echo "<script>alert('Verifique el codigo es el correcto ')</script>";
					header("location:registroPmesa");
				}
			/**/
		}
	#---------------------------------
	#RESETEAR
	#------------------------------------
	 public function resetearController()
		{
		 $respuesta = DatosCocina::resetearModel("cocina");
		 if($respuesta == "success")
			{	//$_SESSION["mesero"] = $_SESSION["usuario"];
				header("location:cocina");
			}
		 else
			{
				echo "<script>alert('Verifique el codigo es el correcto ')</script>";
				header("location:cocina");
			}
		}
	#---------------------------------
	#VISTA COCINA
	#------------------------------------
	 public function vistaCocinaController()
		{
			$fecha = date('Y-m-j H:i:s'); //inicializo la fecha con la hora
			$nuevafecha = strtotime ( '+1 hour' , strtotime ( $fecha ) ) ;
			$nuevafecha = strtotime ( '+10 minute' , $nuevafecha ) ; // utilizo "nuevafecha"
			$nuevafecha = strtotime ( '+10 second' , $nuevafecha ) ; // utilizo "nuevafecha"
			$nuevafecha = date ( 'Y-m-j H:i:s' , $nuevafecha );
			//echo "$fecha (Fecha Inicial)";
			//echo "
			//";
			//echo "$nuevafecha (Fecha con Suma)";
		 $respuesta = DatosCocina::vistaCocinaModel("cocina");
		 foreach($respuesta as $row => $item)
			{
				if ($item["cestado"] == 'Terminado')	{  $clase = 'dos'; }
				else{  $clase = 'uno';
					 $fecha = date('Y-m-j H:i:s');
					 $nuevafecha = strtotime ( '+30 minute' , strtotime ( $item["cfecha"] ) ) ; // utilizo "nuevafecha"
					 $nuevafecha = date ( 'Y-m-j H:i:s' , $nuevafecha );
					 if ($fecha > $nuevafecha)	{  $clase = 'tres'; }
					}
					//echo "<br> $nuevafecha (Fecha con Suma)";
			 echo'
			 	<tr class="'.$clase.'">
					<td>'.$item["cpedido_cocina"].'</td>
					<td>'.$item["mnombre"].'</td>
					<td>'.$item["pnombre"].'</td>
					<td>'.$item["ccantidad"].'</td>
					<td>'.$item["cnota"].'</td>
					<td>'.$item["cfecha"].'</td>
					<td>';
					if ($item["cestado"]=='inicio')
					{

						echo $item["cestado"]. ' <a href="index.php?action=cocina&fecha_horaBorrar='.$item["cfecha"].'&idBorrar='.$item["cproducto_id"].'">Ternimado</a>';
					}
					else
						{echo $item["cestado"]; }
					echo ' </td>

				</tr>';
			}
		}
	#---------------------------------<td><a href="index.php?action=editarPmesa&fecha_hora='.$item["fecha"].'&idBorrar='.$item["pedido_cocina"].'">Editar</a></td>
/*	#EDITAR COCINA
	#------------------------------------
	 public function editarPmesaController()
		{	//echo "<script>alert('entro cruz Suministro')</script>";
			$datosController = $_GET["fecha_hora"];

			$respuesta = DatosCocina::editarPmesaModel($datosController, "producto_vendido_mesa");
			echo'Producto <input type="text" value="'.$respuesta["productos_id"].'" name="productos_idPmesaEditar" required>
			 <input type="hidden" value="'.$respuesta["mesas_id"].'" name="mesas_idPmesaEditar" required>
			 cantidad<input type="text" value="'.$respuesta["cantidad"].'" name="cantidadPmesaEditar" required>
			 <input type="hidden" value="'.$respuesta["fecha_hora"].'" name="fecha_horaPmesaEditar" required>

				 <input type="submit" value="Actualizar">';
		}
	#---------------------------------
	#ACTUALIZAR COCINA
	#------------------------------------
	 public function actualizarPmesaController()
		{	echo "<script>alert('Entro Controller Actualizar Pedido')</script>";
			if(isset($_POST["productos_idPmesaEditar"], $_POST["mesas_idPmesaEditar"], $_POST["cantidadPmesaEditar"],$_POST["fecha_horaPmesaEditar"]))
				{	echo "<script>alert('Entro Controller Actualizar IF')</script>";
					$datosController = array(  "productos_id"=>$_POST["productos_idPmesaEditar"],
															"mesas_id"=>$_POST["mesas_idPmesaEditar"],
												"cantidad"=>$_POST["cantidadPmesaEditar"],
												"fecha_hora"=>$_POST["fecha_horaPmesaEditar"]	);
					$respuesta = DatosCocina::actualizarPmesaModel($datosController, "producto_vendido_mesa");
					if($respuesta == "success")
						{	header("location:index.php?action=cambioPm");	}
								else
						{	echo "error";	}
				}
		}
	*/#---------------------------------
	#BORRAR COCINA
	#------------------------------------
	 public function terminadoController()
		{	//echo "<script>alert('entro controle')</script>";
		 if(isset($_GET["idBorrar"]))
			{	//echo "<script>alert('entro controle IF')</script>";
			 $datosController =array('fecha_hora' => $_GET["fecha_horaBorrar"],
			 					'productos_id' => $_GET["idBorrar"]);
			 $respuesta = DatosCocina::terminadoModel($datosController, "cocina");
			 //if($respuesta == "success")
			//	{	header("location:index.php?action=cocina");	}
			}
		}
	#---------------------------------
}
