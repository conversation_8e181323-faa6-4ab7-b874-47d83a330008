<?php
// Test de liberación de mesa después del envío
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
if (!isset($_SESSION["usuario"])) {
    $_SESSION["usuario"] = 1;
    $_SESSION["tipo_usuario"] = 1;
    $_SESSION["perfil"] = "administrador";
}

require_once "../../models/conexion.php";
require_once "../../controllers/controllerEstadoPedidos.php";
require_once "../../models/crudEstadoPedidos.php";

$mesaId = isset($_GET['mesa']) ? $_GET['mesa'] : 1;

echo "<h1>🧪 Test Liberación de Mesa - Mesa $mesaId</h1>";

try {
    $controller = new ControllerEstadoPedidos();
    
    echo "<h3>1. 📋 Estado Actual</h3>";
    $pedidos = $controller->obtenerPedidosMesaController($mesaId);
    echo "Pedidos activos: " . count($pedidos) . "<br>";
    
    if (!empty($pedidos)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Número</th><th>Estado</th><th>Productos</th><th>Acción</th></tr>";
        foreach ($pedidos as $p) {
            echo "<tr>";
            echo "<td>{$p['id']}</td>";
            echo "<td>{$p['numero_pedido']}</td>";
            echo "<td>{$p['estado']}</td>";
            echo "<td>{$p['total_productos']}</td>";
            echo "<td>";
            if ($p['estado'] == 'borrador' && $p['total_productos'] > 0) {
                echo "<button onclick='enviarPedido({$p['id']})' style='background: #28a745; color: white; padding: 5px;'>📤 Enviar</button>";
            }
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>2. 🔍 Pedido Borrador</h3>";
    $borrador = $controller->obtenerPedidoBorradorController($mesaId);
    
    if ($borrador) {
        echo "✅ Borrador: {$borrador['numero_pedido']} (ID: {$borrador['id']})<br>";
        $productos = DatosEstadoPedidos::obtenerProductosPedidoModel($borrador['id']);
        echo "Productos: " . count($productos) . "<br>";
        
        if (count($productos) == 0) {
            echo "⚠️ <strong>El pedido borrador está vacío. Agregue productos desde la mesa y vuelva a probar.</strong><br>";
        }
    } else {
        echo "❌ No hay pedido borrador<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}

echo "<br><a href='index.php?action=registroPmesa&ida=$mesaId' style='background: #007bff; color: white; padding: 10px; text-decoration: none;'>🔙 Volver a Mesa $mesaId</a>";
echo "<br><a href='test_mesa_liberacion.php?mesa=$mesaId' style='background: #6c757d; color: white; padding: 10px; text-decoration: none; margin: 5px;'>🔄 Actualizar</a>";
?>

<script>
function enviarPedido(pedidoId) {
    if (confirm('¿Enviar pedido ' + pedidoId + ' a cocina?')) {
        console.log('📤 Enviando pedido:', pedidoId);
        
        fetch('ajaxEstadoPedidos.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'enviar_pedido=true&pedido_id=' + pedidoId
        })
        .then(response => response.text())
        .then(text => {
            console.log('📡 Respuesta del servidor:', text);
            try {
                const data = JSON.parse(text);
                if (data.status === 'success') {
                    alert('✅ Pedido enviado: ' + data.message);
                    console.log('🔄 Creando nuevo pedido borrador...');
                    crearNuevoBorrador();
                } else {
                    alert('❌ Error: ' + data.message);
                }
            } catch (e) {
                console.error('❌ Error parseando JSON:', e);
                console.log('Respuesta recibida:', text);
                alert('Error en la respuesta del servidor');
            }
        })
        .catch(error => {
            console.error('❌ Error de conexión:', error);
            alert('Error de conexión');
        });
    }
}

function crearNuevoBorrador() {
    const mesaId = <?=$mesaId?>;
    console.log('🆕 Solicitando nuevo pedido borrador para mesa:', mesaId);
    
    fetch('ajaxEstadoPedidos.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'obtener_pedido_borrador=true&mesa_id=' + mesaId
    })
    .then(response => response.text())
    .then(text => {
        console.log('📋 Respuesta nuevo borrador:', text);
        try {
            const borrador = JSON.parse(text);
            if (borrador && borrador.id) {
                console.log('✅ Nuevo borrador creado:', borrador.numero_pedido);
            }
        } catch (e) {
            console.log('ℹ️ Respuesta no es JSON válido, pero continuando...');
        }
        
        // Recargar página para ver cambios
        console.log('🔄 Recargando página...');
        setTimeout(() => location.reload(), 1500);
    })
    .catch(error => {
        console.error('❌ Error creando borrador:', error);
        setTimeout(() => location.reload(), 1500);
    });
}
</script>
