<?php
require_once '../../models/conexion.php';

$mesa = $_GET['mesa'] ?? 2;

echo "<h2>🧪 Test: Prefacturación - Mesa $mesa</h2>";

try {
    $db = Conexion::conectar();

    // 1. Obtener todos los pedidos de la mesa usando SQL directo
    $stmt = $db->prepare("SELECT * FROM pedidos WHERE mesa_id = ? ORDER BY fecha_pedido DESC");
    $stmt->execute([$mesa]);
    $pedidos = $stmt->fetchAll();

    if (empty($pedidos)) {
        echo "<p>❌ No hay pedidos en la mesa $mesa</p>";
        echo "<a href='test_agregar_producto.php?mesa=$mesa' class='btn btn-primary'>Agregar Producto Primero</a>";
        exit;
    }

    echo "<h3>📋 Pedidos de la Mesa:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>Número</th><th>Estado</th><th>Fecha</th><th>¿Se Factura?</th></tr>";

    foreach ($pedidos as $pedido) {
        // Verificar si el pedido tiene productos
        $stmtProductos = $db->prepare("SELECT COUNT(*) as total FROM producto_vendido_mesa WHERE pedidos_id = ?");
        $stmtProductos->execute([$pedido['id']]);
        $tieneProductos = $stmtProductos->fetch()['total'] > 0;

        // Solo se factura si no está cancelado Y tiene productos
        $seFactura = ($pedido['estado'] != 'cancelado' && $tieneProductos) ? '✅ SÍ' : '❌ NO';

        $color = ($pedido['estado'] == 'borrador') ? '#e7f3ff' :
                 (($pedido['estado'] == 'enviado') ? '#fff3cd' :
                 (($pedido['estado'] == 'entregado') ? '#d4edda' : '#f8d7da'));

        // Si es borrador sin productos, usar color gris
        if ($pedido['estado'] == 'borrador' && !$tieneProductos) {
            $color = '#f8f9fa';
        }

        echo "<tr style='background-color: $color;'>";
        echo "<td>{$pedido['id']}</td>";
        echo "<td>{$pedido['numero_pedido']}</td>";
        echo "<td>{$pedido['estado']}</td>";
        echo "<td>{$pedido['fecha_pedido']}</td>";
        echo "<td>$seFactura</td>";
        echo "</tr>";
    }
    echo "</table>";

    // 2. Calcular totales para facturación usando SQL directo
    echo "<h3>💰 Cálculo de Facturación:</h3>";

    $stmt = $db->prepare("
        SELECT pr.nombre, pvm.cantidad, pr.precio,
               (pvm.cantidad * pr.precio) as subtotal,
               p.numero_pedido, p.estado
        FROM productos pr
        JOIN producto_vendido_mesa pvm ON pr.id = pvm.productos_id
        JOIN pedidos p ON pvm.pedidos_id = p.id
        WHERE pvm.mesas_id = ? AND p.estado != 'cancelado'
        ORDER BY p.fecha_pedido DESC
    ");
    $stmt->execute([$mesa]);
    $productosFacturables = $stmt->fetchAll();

    $totalFacturable = 0;
    foreach ($productosFacturables as $producto) {
        $totalFacturable += $producto['subtotal'];
    }

    echo "<h4>🛒 Productos a Facturar:</h4>";
    if (!empty($productosFacturables)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr><th>Producto</th><th>Cantidad</th><th>Precio</th><th>Subtotal</th><th>Pedido</th><th>Estado</th></tr>";

        foreach ($productosFacturables as $item) {
            echo "<tr>";
            echo "<td>{$item['nombre']}</td>";
            echo "<td>{$item['cantidad']}</td>";
            echo "<td>\${$item['precio']}</td>";
            echo "<td>\${$item['subtotal']}</td>";
            echo "<td>{$item['numero_pedido']}</td>";
            echo "<td>{$item['estado']}</td>";
            echo "</tr>";
        }
        echo "</table>";

        echo "<h4>💰 Total a Facturar: \${$totalFacturable}</h4>";
    } else {
        echo "<p>❌ No hay productos para facturar</p>";
    }

    // 3. Simular prefacturación
    echo "<h3>📄 Simulando Prefacturación...</h3>";

    if ($totalFacturable > 0) {
        echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #ddd; margin: 10px 0;'>";
        echo "<h4>🧾 PREFACTURA - MESA $mesa</h4>";
        echo "<p><strong>Fecha:</strong> " . date('Y-m-d H:i:s') . "</p>";
        echo "<p><strong>Mesa:</strong> $mesa</p>";
        echo "<hr>";

        foreach ($productosFacturables as $item) {
            echo "<p>{$item['nombre']} x{$item['cantidad']} - \${$item['precio']} = \${$item['subtotal']}</p>";
        }

        echo "<hr>";
        echo "<p><strong>TOTAL: \${$totalFacturable}</strong></p>";
        echo "</div>";

        echo "<p>✅ Prefacturación generada correctamente</p>";

        // Botón para facturación completa
        echo "<h3>🔄 Siguiente Paso:</h3>";
        echo "<a href='test_facturacion_completa.php?mesa=$mesa&total=$totalFacturable' class='btn btn-success'>Proceder a Facturación Completa</a>";

    } else {
        echo "<p>❌ No se puede generar prefacturación sin productos</p>";
    }

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage();
}

echo "<hr>";
echo "<h3>🔄 Navegación</h3>";
echo "<a href='test_facturacion.php?mesa=$mesa' class='btn btn-primary'>Ver Diagnóstico Facturación</a>";
echo " | ";
echo "<a href='test_agregar_producto.php?mesa=$mesa' class='btn btn-info'>Agregar Producto</a>";
echo " | ";
echo "<a href='test_enviar_pedido.php?mesa=$mesa' class='btn btn-warning'>Enviar Pedido</a>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}

table {
    border-collapse: collapse;
    width: 100%;
    margin: 10px 0;
}

th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #f0f0f0;
    font-weight: bold;
}

.btn {
    display: inline-block;
    padding: 8px 16px;
    margin: 5px;
    text-decoration: none;
    border-radius: 4px;
    color: white;
}

.btn-primary { background-color: #007bff; }
.btn-success { background-color: #28a745; }
.btn-warning { background-color: #ffc107; color: #212529; }
.btn-info { background-color: #17a2b8; }

h2, h3, h4 { color: #333; }
p { margin: 8px 0; }
hr { margin: 20px 0; }
</style>
