<?php
// Prueba simple para verificar datos en la mesa

echo "<h1>🔍 Prueba Simple Mesa " . $_SESSION["mesa"] . "</h1>";
echo "<hr>";

$mesaId = $_SESSION["mesa"];
$db = Conexion::conectar();

echo "<h2>1. 📊 Verificar Datos Básicos</h2>";

// 1. Verificar si existe la mesa
$stmt = $db->prepare("SELECT * FROM mesas WHERE id = ?");
$stmt->execute([$mesaId]);
$mesa = $stmt->fetch();

if ($mesa) {
    echo "✅ Mesa encontrada: " . $mesa['numero'] . " - Estado: " . $mesa['estado'] . "<br>";
} else {
    echo "❌ Mesa no encontrada<br>";
}

// 2. Verificar pedidos en la mesa
echo "<h3>Pedidos en la mesa:</h3>";
$stmt = $db->prepare("SELECT * FROM pedidos WHERE mesa_id = ? ORDER BY fecha_pedido DESC");
$stmt->execute([$mesaId]);
$pedidos = $stmt->fetchAll();

echo "Total pedidos: " . count($pedidos) . "<br>";

if (!empty($pedidos)) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Número</th><th>Estado</th><th>Facturado</th><th>Fecha</th></tr>";
    foreach ($pedidos as $pedido) {
        echo "<tr>";
        echo "<td>" . $pedido['id'] . "</td>";
        echo "<td>" . $pedido['numero_pedido'] . "</td>";
        echo "<td>" . $pedido['estado'] . "</td>";
        echo "<td>" . $pedido['facturado'] . "</td>";
        echo "<td>" . $pedido['fecha_pedido'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// 3. Verificar productos en la mesa
echo "<h3>Productos en la mesa:</h3>";
$stmt = $db->prepare("
    SELECT pvm.*, pr.nombre as producto_nombre, p.numero_pedido, p.estado as pedido_estado
    FROM producto_vendido_mesa pvm
    LEFT JOIN productos pr ON pvm.productos_id = pr.id
    LEFT JOIN pedidos p ON pvm.pedidos_id = p.id
    WHERE pvm.mesas_id = ?
    ORDER BY pvm.fecha_hora DESC
");
$stmt->execute([$mesaId]);
$productos = $stmt->fetchAll();

echo "Total productos: " . count($productos) . "<br>";

if (!empty($productos)) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Producto</th><th>Cantidad</th><th>Pedido ID</th><th>Número Pedido</th><th>Estado Pedido</th></tr>";
    foreach ($productos as $producto) {
        echo "<tr>";
        echo "<td>" . $producto['producto_nombre'] . "</td>";
        echo "<td>" . $producto['cantidad'] . "</td>";
        echo "<td>" . $producto['pedidos_id'] . "</td>";
        echo "<td>" . $producto['numero_pedido'] . "</td>";
        echo "<td>" . $producto['pedido_estado'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

echo "<h2>2. 🧪 Crear Datos de Prueba</h2>";

// Botón para crear pedido de prueba
echo "<form method='post'>";
echo "<button type='submit' name='crear_pedido_prueba' style='background: #28a745; color: white; padding: 10px; border: none; margin: 5px;'>Crear Pedido de Prueba</button>";
echo "</form>";

if (isset($_POST['crear_pedido_prueba'])) {
    try {
        // Crear pedido de prueba
        $stmt = $db->prepare("
            INSERT INTO pedidos (mesa_id, mesero_id, cedula_cliente, estado, fecha_pedido, numero_pedido, facturado)
            VALUES (?, ?, '0', 'borrador', NOW(), CONCAT('P', LPAD(FLOOR(RAND() * 10000), 4, '0')), 'n')
        ");
        $stmt->execute([$mesaId, $_SESSION["usuario"]]);
        $pedidoId = $db->lastInsertId();
        
        // Buscar un producto para agregar
        $stmt = $db->prepare("SELECT id FROM productos LIMIT 1");
        $stmt->execute();
        $producto = $stmt->fetch();
        
        if ($producto) {
            // Agregar producto al pedido
            $stmt = $db->prepare("
                INSERT INTO producto_vendido_mesa (productos_id, mesas_id, pedidos_id, cantidad, fecha_hora, mesero, descuento, codigo_descuento, nota, cocina)
                VALUES (?, ?, ?, 1, NOW(), ?, 0, '', 'Producto de prueba', 'cocina')
            ");
            $stmt->execute([$producto['id'], $mesaId, $pedidoId, $_SESSION["usuario"]]);
            
            echo "<div style='background: #d4edda; color: #155724; padding: 10px; margin: 10px 0;'>";
            echo "✅ Pedido de prueba creado exitosamente (ID: $pedidoId)";
            echo "</div>";
        }
        
        // Recargar página para mostrar los nuevos datos
        echo "<script>setTimeout(function(){ location.reload(); }, 2000);</script>";
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px 0;'>";
        echo "❌ Error creando pedido: " . $e->getMessage();
        echo "</div>";
    }
}

echo "<h2>3. 🔗 Enlaces Útiles</h2>";
echo "<a href='index.php?action=registroPmesa&ida=" . $mesaId . "' style='background: #007bff; color: white; padding: 10px; text-decoration: none; margin: 5px; display: inline-block;'>🔗 Ir a Mesa " . $mesaId . "</a><br>";
echo "<a href='index.php?action=debug_mesa' style='background: #6c757d; color: white; padding: 10px; text-decoration: none; margin: 5px; display: inline-block;'>🔍 Debug Completo</a><br>";
echo "<a href='index.php?action=diagnostico' style='background: #ffc107; color: black; padding: 10px; text-decoration: none; margin: 5px; display: inline-block;'>🔧 Diagnóstico</a><br>";

?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}

table {
    background-color: white;
    width: 100%;
    max-width: 800px;
    margin: 10px 0;
}

th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #007bff;
    color: white;
}
</style>
