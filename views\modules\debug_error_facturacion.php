<?php

session_start();

if(!isset($_SESSION["validar"]) || !$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "models/conexion.php";

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Debug: Error de Facturación</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
</head>
<body>

<div class="container">
    <h2>🔧 Debug: Error de Facturación - Clave Duplicada</h2>
    <hr>
    
    <div class="alert alert-danger">
        <h4>❌ Error Detectado:</h4>
        <p><strong>SQLSTATE[23000]:</strong> Integrity constraint violation: 1062</p>
        <p><strong>Duplicate entry:</strong> '56-5-31' for key 'PRIMARY'</p>
        <p><strong>Tabla afectada:</strong> producto_vendido_mesa</p>
    </div>
    
    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">🔍 Análisis del Error</h3>
        </div>
        <div class="panel-body">
            <p><strong>Clave primaria compuesta:</strong> (productos_id, mesas_id, pedidos_id)</p>
            <p><strong>Valores del error:</strong></p>
            <ul>
                <li><strong>productos_id:</strong> 56</li>
                <li><strong>mesas_id:</strong> 5</li>
                <li><strong>pedidos_id:</strong> 31</li>
            </ul>
            <p><strong>Causa probable:</strong> Se está intentando insertar el mismo producto en el mismo pedido de la misma mesa dos veces.</p>
        </div>
    </div>
    
    <div class="panel panel-warning">
        <div class="panel-heading">
            <h3 class="panel-title">📊 Verificar Registro Duplicado</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                // Verificar si existe el registro problemático
                $stmt = Conexion::conectar()->prepare("
                    SELECT * FROM producto_vendido_mesa 
                    WHERE productos_id = 56 AND mesas_id = 5 AND pedidos_id = 31
                ");
                $stmt->execute();
                $registros = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (count($registros) > 0) {
                    echo "<div class='alert alert-warning'>";
                    echo "<h4>⚠️ Registro encontrado:</h4>";
                    echo "<p>Existe " . count($registros) . " registro(s) con esta combinación.</p>";
                    echo "</div>";
                    
                    echo "<table class='table table-bordered'>";
                    echo "<thead><tr><th>Producto ID</th><th>Mesa ID</th><th>Pedido ID</th><th>Cantidad</th><th>Precio</th></tr></thead>";
                    echo "<tbody>";
                    foreach ($registros as $reg) {
                        echo "<tr>";
                        echo "<td>{$reg['productos_id']}</td>";
                        echo "<td>{$reg['mesas_id']}</td>";
                        echo "<td>{$reg['pedidos_id']}</td>";
                        echo "<td>{$reg['cantidad']}</td>";
                        echo "<td>$" . number_format($reg['precio'], 0) . "</td>";
                        echo "</tr>";
                    }
                    echo "</tbody></table>";
                } else {
                    echo "<div class='alert alert-success'>";
                    echo "<h4>✅ No se encontró el registro problemático</h4>";
                    echo "<p>El registro con productos_id=56, mesas_id=5, pedidos_id=31 no existe actualmente.</p>";
                    echo "</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-primary">
        <div class="panel-heading">
            <h3 class="panel-title">🔍 Información Detallada</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                // Información del producto
                echo "<h5>📦 Información del Producto ID 56:</h5>";
                $stmt_producto = Conexion::conectar()->prepare("SELECT * FROM productos WHERE id = 56");
                $stmt_producto->execute();
                $producto = $stmt_producto->fetch(PDO::FETCH_ASSOC);
                
                if ($producto) {
                    echo "<p><strong>Nombre:</strong> {$producto['nombre']}</p>";
                    echo "<p><strong>Categoría:</strong> {$producto['categoria']}</p>";
                    echo "<p><strong>Precio:</strong> $" . number_format($producto['precio'], 0) . "</p>";
                } else {
                    echo "<p class='text-danger'>❌ Producto ID 56 no encontrado</p>";
                }
                
                // Información de la mesa
                echo "<h5>🪑 Información de la Mesa ID 5:</h5>";
                $stmt_mesa = Conexion::conectar()->prepare("SELECT * FROM mesas WHERE id = 5");
                $stmt_mesa->execute();
                $mesa = $stmt_mesa->fetch(PDO::FETCH_ASSOC);
                
                if ($mesa) {
                    echo "<p><strong>Nombre:</strong> {$mesa['nombre']}</p>";
                    echo "<p><strong>Estado:</strong> {$mesa['estado']}</p>";
                    echo "<p><strong>Descripción:</strong> {$mesa['descripcion']}</p>";
                } else {
                    echo "<p class='text-danger'>❌ Mesa ID 5 no encontrada</p>";
                }
                
                // Información del pedido
                echo "<h5>📋 Información del Pedido ID 31:</h5>";
                $stmt_pedido = Conexion::conectar()->prepare("SELECT * FROM pedidos WHERE id = 31");
                $stmt_pedido->execute();
                $pedido = $stmt_pedido->fetch(PDO::FETCH_ASSOC);
                
                if ($pedido) {
                    echo "<p><strong>Número:</strong> {$pedido['numero_pedido']}</p>";
                    echo "<p><strong>Estado:</strong> {$pedido['estado']}</p>";
                    echo "<p><strong>Mesa ID:</strong> {$pedido['mesa_id']}</p>";
                    echo "<p><strong>Fecha:</strong> {$pedido['fecha_pedido']}</p>";
                    echo "<p><strong>Facturado:</strong> {$pedido['facturado']}</p>";
                } else {
                    echo "<p class='text-danger'>❌ Pedido ID 31 no encontrado</p>";
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-success">
        <div class="panel-heading">
            <h3 class="panel-title">🔧 Soluciones Posibles</h3>
        </div>
        <div class="panel-body">
            <h5>💡 Opciones para resolver el error:</h5>
            
            <div class="row">
                <div class="col-md-6">
                    <h6>1. 🔄 Actualizar en lugar de insertar</h6>
                    <p>Modificar el código para usar <code>INSERT ... ON DUPLICATE KEY UPDATE</code> o verificar existencia antes de insertar.</p>
                    
                    <h6>2. 🧹 Limpiar registros duplicados</h6>
                    <p>Eliminar registros duplicados manteniendo solo uno.</p>
                </div>
                <div class="col-md-6">
                    <h6>3. 📊 Sumar cantidades</h6>
                    <p>Si es el mismo producto, sumar las cantidades en lugar de crear registros separados.</p>
                    
                    <h6>4. 🔍 Revisar lógica de facturación</h6>
                    <p>Verificar por qué se está intentando insertar el mismo registro dos veces.</p>
                </div>
            </div>
            
            <hr>
            
            <h5>🛠️ Acción Recomendada:</h5>
            <div class="alert alert-info">
                <p><strong>Paso 1:</strong> Modificar la lógica de inserción para usar <code>INSERT ... ON DUPLICATE KEY UPDATE cantidad = cantidad + VALUES(cantidad)</code></p>
                <p><strong>Paso 2:</strong> Revisar el proceso de facturación para evitar duplicados</p>
                <p><strong>Paso 3:</strong> Implementar validación antes de insertar</p>
            </div>
        </div>
    </div>
    
    <div class="panel panel-danger">
        <div class="panel-heading">
            <h3 class="panel-title">⚠️ Solución Temporal</h3>
        </div>
        <div class="panel-body">
            <p>Si necesitas facturar inmediatamente, puedes:</p>
            <ol>
                <li>Eliminar el registro duplicado manualmente</li>
                <li>Intentar la facturación nuevamente</li>
                <li>Implementar la solución permanente después</li>
            </ol>
            
            <div class="alert alert-warning">
                <strong>⚠️ Advertencia:</strong> Esta es una solución temporal. Es importante corregir la causa raíz del problema.
            </div>
        </div>
    </div>
    
    <hr>
    <div class="row">
        <div class="col-md-4">
            <a href="index.php?action=diagnostico" class="btn btn-primary btn-block">🔙 Diagnóstico</a>
        </div>
        <div class="col-md-4">
            <a href="index.php?action=mesa" class="btn btn-info btn-block">🏠 Volver a Mesas</a>
        </div>
        <div class="col-md-4">
            <a href="index.php?action=registroPmesa&ida=5" class="btn btn-warning btn-block">🪑 Ver Mesa 5</a>
        </div>
    </div>
</div>

</body>
</html>
