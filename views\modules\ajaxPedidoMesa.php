<?php
require_once "../../models/crud.php";
require_once "../../controllers/controller.php";
	ini_set("session.cookie_lifetime","28800");
	ini_set("session.gc_maxlifetime","28800");
//echo "<script>alert('entro al ajax ".$_POST['mesas_idPmesaRegistro']."')</script>";
if(isset($_POST['mesas_idPmesaRegistro']))
	{//echo "<script>alert('Si entro al ajax factura')</script>";
		$queryString = $_POST['mesas_idPmesaRegistro'];
		echo $queryString;
		$ajax=new MvcController();
		//$ajax->facturaajaxController($queryString);
		$ajax -> vistaPmesaAController();
		$ajax -> borrarPmesaController();
		echo "<script>alert('entro ajax ultimo pro')</script>";
	}