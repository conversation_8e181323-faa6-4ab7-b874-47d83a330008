<?php

	$registroPmesa = new MvcController();
	$productos_idPmesaRegistro = $registroPmesa -> listaProductosController();
	$mesas_idPmesaRegistro = $registroPmesa -> listaMesasController();
	$pedido_idPmesaRegistro = $registroPmesa -> listaPedidosController();
	$producto = $registroPmesa -> buscarProductoXController();
	$registroPmesa -> registroPmesaProductoController();

	if(isset($_GET["action"]))
		{	if($_GET["action"] == "okPm")
			{echo "Registro Exitoso";	}
		}

?>





<h1>REGISTRO DE PEDIDO MESA</h1>



<form method="post">
		<!--<?php/*  
			# 	# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  productos_idPmesaRegistro  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%% producto_id, id, cantidades
				if($productos_idPmesaRegistro=="error")
					{	echo "debe registrar el productos_idPmesaRegistro"; }
				else{
						echo "<label>Id productos_idPmesaRegistro </label>";
						$result='<select name="productos_idPmesaRegistro"  id="productos_idPmesaRegistro">';
						$result.=' <option value="-1">Seleccione una productos_idPmesaRegistro</option>';
						foreach ($productos_idPmesaRegistro as $row => $item)
						 	{	$result.=' <option value="'.$item["id"].'">'.$item["nombre"].'</option>';	}	
						 $result.='</select>';
						 echo $result." <br>";
					}
			# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  End productos_idPmesaRegistro  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%			 
			 
			# 	# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  mesas_idPmesaRegistro  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
				if($mesas_idPmesaRegistro=="error")
					{	echo "debe registrar el mesas"; }
				else{
						echo "<label>Id mesas </label>";
						$result='<select name="mesas_idPmesaRegistro"  id="mesas_idPmesaRegistro">';
						$result.=' <option value="-1">Seleccione una mesas_idPmesaRegistro</option>';
						foreach ($mesas_idPmesaRegistro as $row => $item)
						 	{	$result.=' <option value="'.$item["id"].'">'.$item["nombre"].'</option>';	}	
						 $result.='</select>';
						 echo $result." <br>";
					}
			# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  End suministros  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%			 
			 
			# 	# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  Pedido %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
				if($pedido_idPmesaRegistro=="error")
					{	echo "debe registrar el pedido"; }
				else{
						echo "<label>Id pedido </label>";
						$result='<select name="pedido_idPmesaRegistro"  id="pedido_idPmesaRegistro">';
						$result.=' <option value="-1">Seleccione una pedido_idPmesaRegistro</option>';
						foreach ($pedido_idPmesaRegistro as $row => $item)
						 	{	$result.=' <option value="'.$item["id"].'">'.$item["id"].'</option>';	}	
						 $result.='</select>';
						 echo $result." <br>";
					}
			# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  End pedido  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
	 	*/?>


	<label> Nombre PRODUCTO : </label>
	<input type="text" placeholder="PRODUCTO" name="productos_idPmesaRegistro" required><br>	
	<label> MESA : </label>
	<input type="text" placeholder="MESA" name="mesas_idPmesaRegistro" required><br-->
	<label> nombre: </label>
	<input type="text" placeholder="producto" name="producto" required><br>
	<label> CANTIDAD: </label>
	<input type="text" placeholder="CANTIDAD" name="cantidadPmesaRegistro" ><br>

	<label> listaProducto: </label>
	<input type="text" placeholder="listaProducto" name="listaProducto" ><br>
		
	<input type="submit" value="producto ">
	<input type="submit" value="Enviar">

</form>


