<?php

session_start();

if(!isset($_SESSION["validar"]) || !$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "models/conexion.php";

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Verificar Pedido 8</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
</head>
<body>

<div class="container">
    <h2>🔍 Verificar: Estado del Pedido ID 8</h2>
    <hr>
    
    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">📊 Información del Pedido ID 8</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                $stmt = Conexion::conectar()->prepare("
                    SELECT p.*, m.nombre as mesa_nombre
                    FROM pedidos p
                    LEFT JOIN mesas m ON p.mesa_id = m.id
                    WHERE p.id = 8
                ");
                $stmt->execute();
                $pedido = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($pedido) {
                    echo "<table class='table table-bordered'>";
                    echo "<tr><th>Campo</th><th>Valor</th></tr>";
                    echo "<tr><td><strong>ID</strong></td><td>{$pedido['id']}</td></tr>";
                    echo "<tr><td><strong>Número Pedido</strong></td><td>{$pedido['numero_pedido']}</td></tr>";
                    echo "<tr><td><strong>Estado</strong></td><td><span class='label label-" . 
                         ($pedido['estado'] == 'enviado' ? 'info' : 
                          ($pedido['estado'] == 'entregado' ? 'success' : 'warning')) . 
                         "'>{$pedido['estado']}</span></td></tr>";
                    echo "<tr><td><strong>Mesa</strong></td><td>{$pedido['mesa_nombre']} (ID: {$pedido['mesa_id']})</td></tr>";
                    echo "<tr><td><strong>Fecha Pedido</strong></td><td>{$pedido['fecha_pedido']}</td></tr>";
                    echo "<tr><td><strong>Fecha Envío</strong></td><td>{$pedido['fecha_envio']}</td></tr>";
                    echo "<tr><td><strong>Fecha Entrega</strong></td><td>{$pedido['fecha_entrega']}</td></tr>";
                    echo "<tr><td><strong>Usuario Envío</strong></td><td>{$pedido['usuario_envio']}</td></tr>";
                    echo "<tr><td><strong>Usuario Entrega</strong></td><td>{$pedido['usuario_entrega']}</td></tr>";
                    echo "<tr><td><strong>Mesero ID</strong></td><td>{$pedido['mesero_id']}</td></tr>";
                    echo "<tr><td><strong>Facturado</strong></td><td>{$pedido['facturado']}</td></tr>";
                    echo "</table>";
                    
                    if ($pedido['estado'] == 'entregado') {
                        echo "<div class='alert alert-success'>";
                        echo "<h4>✅ Pedido ya entregado</h4>";
                        echo "<p>Este pedido ya fue marcado como entregado el <strong>{$pedido['fecha_entrega']}</strong> por el usuario <strong>{$pedido['usuario_entrega']}</strong>.</p>";
                        echo "<p>Por eso el UPDATE no afecta ninguna fila - el pedido ya no está en estado 'enviado'.</p>";
                        echo "</div>";
                    } elseif ($pedido['estado'] == 'enviado') {
                        echo "<div class='alert alert-warning'>";
                        echo "<h4>⚠️ Pedido en estado enviado</h4>";
                        echo "<p>El pedido está en estado 'enviado' pero el UPDATE falla. Puede haber un problema con la consulta.</p>";
                        echo "</div>";
                    } else {
                        echo "<div class='alert alert-info'>";
                        echo "<h4>ℹ️ Pedido en estado: {$pedido['estado']}</h4>";
                        echo "<p>El pedido no está en estado 'enviado', por eso no se puede marcar como entregado.</p>";
                        echo "</div>";
                    }
                    
                } else {
                    echo "<div class='alert alert-danger'>";
                    echo "<h4>❌ Pedido no encontrado</h4>";
                    echo "<p>No existe un pedido con ID 8 en la base de datos.</p>";
                    echo "</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-warning">
        <div class="panel-heading">
            <h3 class="panel-title">📋 Productos del Pedido ID 8</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                $stmt = Conexion::conectar()->prepare("
                    SELECT pvm.*, pr.nombre as producto_nombre, pr.categoria, pr.precio
                    FROM producto_vendido_mesa pvm
                    INNER JOIN productos pr ON pvm.productos_id = pr.id
                    WHERE pvm.pedidos_id = 8
                    ORDER BY pr.categoria, pr.nombre
                ");
                $stmt->execute();
                $productos = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (count($productos) > 0) {
                    echo "<table class='table table-striped'>";
                    echo "<thead><tr><th>Producto</th><th>Categoría</th><th>Cantidad</th><th>Precio</th><th>Subtotal</th></tr></thead>";
                    echo "<tbody>";
                    $total = 0;
                    foreach ($productos as $prod) {
                        $subtotal = $prod['cantidad'] * $prod['precio'];
                        $total += $subtotal;
                        echo "<tr>";
                        echo "<td>{$prod['producto_nombre']}</td>";
                        echo "<td><span class='label label-" . 
                             ($prod['categoria'] == 'bar' ? 'info' : 
                              ($prod['categoria'] == 'cocina' ? 'warning' : 'danger')) . 
                             "'>{$prod['categoria']}</span></td>";
                        echo "<td>{$prod['cantidad']}</td>";
                        echo "<td>$" . number_format($prod['precio'], 0) . "</td>";
                        echo "<td>$" . number_format($subtotal, 0) . "</td>";
                        echo "</tr>";
                    }
                    echo "</tbody>";
                    echo "<tfoot><tr><th colspan='4'>Total</th><th>$" . number_format($total, 0) . "</th></tr></tfoot>";
                    echo "</table>";
                } else {
                    echo "<div class='alert alert-warning'>No hay productos asociados al pedido ID 8</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-success">
        <div class="panel-heading">
            <h3 class="panel-title">📜 Historial del Pedido ID 8</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                $stmt = Conexion::conectar()->prepare("
                    SELECT * FROM pedidos_historial 
                    WHERE pedido_id = 8 
                    ORDER BY fecha_cambio ASC
                ");
                $stmt->execute();
                $historial = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (count($historial) > 0) {
                    echo "<table class='table table-condensed'>";
                    echo "<thead><tr><th>Fecha</th><th>Estado Anterior</th><th>Estado Nuevo</th><th>Usuario</th><th>Observaciones</th></tr></thead>";
                    echo "<tbody>";
                    foreach ($historial as $h) {
                        echo "<tr>";
                        echo "<td>" . date('d/m/Y H:i:s', strtotime($h['fecha_cambio'])) . "</td>";
                        echo "<td><span class='label label-warning'>{$h['estado_anterior']}</span></td>";
                        echo "<td><span class='label label-success'>{$h['estado_nuevo']}</span></td>";
                        echo "<td>{$h['usuario_id']}</td>";
                        echo "<td>{$h['observaciones']}</td>";
                        echo "</tr>";
                    }
                    echo "</tbody></table>";
                } else {
                    echo "<div class='alert alert-info'>No hay historial de cambios para el pedido ID 8</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-primary">
        <div class="panel-heading">
            <h3 class="panel-title">🔧 Acciones</h3>
        </div>
        <div class="panel-body">
            <?php
            // Verificar estado actual
            $stmt = Conexion::conectar()->prepare("SELECT estado FROM pedidos WHERE id = 8");
            $stmt->execute();
            $estado_actual = $stmt->fetchColumn();
            
            if ($estado_actual == 'entregado') {
                echo "<div class='alert alert-info'>";
                echo "<h4>✅ Pedido ya procesado correctamente</h4>";
                echo "<p>El pedido ID 8 ya fue marcado como entregado. No necesita más acciones.</p>";
                echo "<p><strong>Recomendación:</strong> Crear un nuevo pedido de prueba para testing.</p>";
                echo "</div>";
                
                echo "<a href='index.php?action=crear_pedido_prueba' class='btn btn-success'>🧪 Crear Nuevo Pedido de Prueba</a>";
                
            } elseif ($estado_actual == 'enviado') {
                echo "<div class='alert alert-warning'>";
                echo "<h4>⚠️ Pedido en estado enviado pero UPDATE falla</h4>";
                echo "<p>Hay un problema con la consulta UPDATE. Revisar logs del servidor.</p>";
                echo "</div>";
            } else {
                echo "<div class='alert alert-info'>";
                echo "<h4>ℹ️ Pedido en estado: {$estado_actual}</h4>";
                echo "<p>Para poder marcarlo como entregado, primero debe estar en estado 'enviado'.</p>";
                echo "</div>";
            }
            ?>
        </div>
    </div>
    
    <hr>
    <div class="row">
        <div class="col-md-4">
            <a href="index.php?action=debug_estado_pedidos" class="btn btn-primary btn-block">🔙 Debug Estado</a>
        </div>
        <div class="col-md-4">
            <a href="index.php?action=pedidosBarPendientes" class="btn btn-info btn-block">🍺 Volver a Bar</a>
        </div>
        <div class="col-md-4">
            <a href="index.php?action=crear_pedido_prueba" class="btn btn-success btn-block">🧪 Crear Pedido</a>
        </div>
    </div>
</div>

</body>
</html>
