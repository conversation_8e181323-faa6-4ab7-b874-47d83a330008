<?php

session_start();

if(!$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "../../models/conexion.php";

echo "<h2>🔍 Test - Categorías de Productos</h2>";
echo "<hr>";

// 1. Verificar todas las categorías únicas
echo "<h3>1. Categorías únicas en productos:</h3>";
$stmt = Conexion::conectar()->prepare("
    SELECT DISTINCT categoria, COUNT(*) as total_productos
    FROM productos 
    GROUP BY categoria 
    ORDER BY categoria
");
$stmt->execute();
$categorias = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Categoría</th><th>Total Productos</th></tr>";
foreach($categorias as $categoria) {
    echo "<tr>";
    echo "<td>" . ($categoria['categoria'] ?: '<em>NULL/Vacío</em>') . "</td>";
    echo "<td>{$categoria['total_productos']}</td>";
    echo "</tr>";
}
echo "</table>";

// 2. Productos en pedidos enviados por categoría
echo "<h3>2. Productos en pedidos enviados por categoría:</h3>";
$stmt = Conexion::conectar()->prepare("
    SELECT pr.categoria, COUNT(DISTINCT p.id) as pedidos_con_categoria, COUNT(*) as productos_total
    FROM pedidos p
    JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
    JOIN productos pr ON pvm.productos_id = pr.id
    WHERE p.estado = 'enviado'
    GROUP BY pr.categoria
    ORDER BY pedidos_con_categoria DESC
");
$stmt->execute();
$productos_por_categoria = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Categoría</th><th>Pedidos con esta categoría</th><th>Total productos</th></tr>";
foreach($productos_por_categoria as $cat) {
    echo "<tr>";
    echo "<td>" . ($cat['categoria'] ?: '<em>NULL/Vacío</em>') . "</td>";
    echo "<td>{$cat['pedidos_con_categoria']}</td>";
    echo "<td>{$cat['productos_total']}</td>";
    echo "</tr>";
}
echo "</table>";

// 3. Test de filtros específicos
echo "<h3>3. Test de filtros por categoría:</h3>";

$categorias_test = ['bar', 'asados', 'cocina'];

foreach($categorias_test as $cat) {
    echo "<h4>Categoría: {$cat}</h4>";
    
    $stmt = Conexion::conectar()->prepare("
        SELECT COUNT(DISTINCT p.id) as total_pedidos
        FROM pedidos p
        INNER JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
        INNER JOIN productos pr ON pvm.productos_id = pr.id
        WHERE p.estado = 'enviado'
        AND CASE
            WHEN :categoria = 'bar' THEN pr.categoria IN ('bebidas', 'cervezas', 'licores', 'vinos', 'cocteles', 'refrescos', 'jugos', 'agua', 'bar')
            WHEN :categoria = 'asados' THEN pr.categoria IN ('carnes', 'parrilla', 'asados', 'pescados', 'mariscos')
            ELSE pr.categoria NOT IN ('bebidas', 'cervezas', 'licores', 'vinos', 'cocteles', 'refrescos', 'jugos', 'agua', 'bar', 'carnes', 'parrilla', 'asados', 'pescados', 'mariscos')
               OR pr.categoria IS NULL
               OR pr.categoria = ''
               OR pr.categoria = 'cocina'
               OR pr.categoria = 'comida'
               OR pr.categoria = 'platos'
               OR pr.categoria = 'entradas'
               OR pr.categoria = 'principales'
               OR pr.categoria = 'postres'
        END
    ");
    
    $stmt->bindParam(":categoria", $cat, PDO::PARAM_STR);
    $stmt->execute();
    $resultado = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<p><strong>Pedidos encontrados para {$cat}:</strong> {$resultado['total_pedidos']}</p>";
}

// 4. Productos específicos que deberían ir a cocina
echo "<h3>4. Productos que NO son bar ni asados (deberían ir a cocina):</h3>";
$stmt = Conexion::conectar()->prepare("
    SELECT DISTINCT pr.id, pr.nombre, pr.categoria
    FROM pedidos p
    JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
    JOIN productos pr ON pvm.productos_id = pr.id
    WHERE p.estado = 'enviado'
    AND pr.categoria NOT IN ('bebidas', 'cervezas', 'licores', 'vinos', 'cocteles', 'refrescos', 'jugos', 'agua', 'bar', 'carnes', 'parrilla', 'asados', 'pescados', 'mariscos')
    ORDER BY pr.categoria, pr.nombre
    LIMIT 20
");
$stmt->execute();
$productos_cocina = $stmt->fetchAll(PDO::FETCH_ASSOC);

if(count($productos_cocina) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Nombre</th><th>Categoría</th></tr>";
    foreach($productos_cocina as $producto) {
        echo "<tr>";
        echo "<td>{$producto['id']}</td>";
        echo "<td>{$producto['nombre']}</td>";
        echo "<td>" . ($producto['categoria'] ?: '<em>NULL/Vacío</em>') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>❌ No se encontraron productos para cocina</p>";
}

// 5. Productos con categorías NULL o vacías
echo "<h3>5. Productos con categorías NULL o vacías en pedidos enviados:</h3>";
$stmt = Conexion::conectar()->prepare("
    SELECT DISTINCT pr.id, pr.nombre, pr.categoria
    FROM pedidos p
    JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
    JOIN productos pr ON pvm.productos_id = pr.id
    WHERE p.estado = 'enviado'
    AND (pr.categoria IS NULL OR pr.categoria = '')
    ORDER BY pr.nombre
    LIMIT 10
");
$stmt->execute();
$productos_sin_categoria = $stmt->fetchAll(PDO::FETCH_ASSOC);

if(count($productos_sin_categoria) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Nombre</th><th>Categoría</th></tr>";
    foreach($productos_sin_categoria as $producto) {
        echo "<tr>";
        echo "<td>{$producto['id']}</td>";
        echo "<td>{$producto['nombre']}</td>";
        echo "<td><em>NULL/Vacío</em></td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: green;'>✅ No hay productos sin categoría en pedidos enviados</p>";
}

echo "<hr>";
echo "<p><a href='pedidosCocinaPendientes'>← Volver a Pedidos Cocina</a></p>";

?>
