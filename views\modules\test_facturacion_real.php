<?php
// Test de facturación con datos reales de la mesa
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
if (!isset($_SESSION["usuario"])) {
    $_SESSION["usuario"] = 1;
    $_SESSION["tipo_usuario"] = 1;
    $_SESSION["perfil"] = "administrador";
}

require_once "../../models/conexion.php";
require_once "../../models/crudFacturaAja.php";

$mesaId = isset($_GET['mesa']) ? $_GET['mesa'] : 1;

echo "<h1>💰 Test Facturación Real - Mesa $mesaId</h1>";

try {
    echo "<h3>1. 📋 Productos para Facturar</h3>";
    
    // Obtener productos reales para facturar
    $productosFacturar = DatosFacturaAja::obtenerProductosParaFacturar($mesaId);
    
    if (!empty($productosFacturar)) {
        echo "<div class='alert alert-success'>✅ Encontrados " . count($productosFacturar) . " producto(s) para facturar</div>";
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr><th>Producto</th><th>Precio</th><th>Cantidad</th><th>Subtotal</th><th>Estado Pedido</th></tr>";
        
        $totalReal = 0;
        foreach ($productosFacturar as $producto) {
            $descuento = ($producto['preciopr'] * ($producto['descuentopvm'] / 100));
            $precioConDescuento = $producto['preciopr'] - $descuento;
            $subtotal = $precioConDescuento * $producto['cantidadpvm'];
            $totalReal += $subtotal;
            
            echo "<tr>";
            echo "<td>{$producto['nombrepr']}</td>";
            echo "<td>$" . number_format($producto['preciopr']) . "</td>";
            echo "<td>{$producto['cantidadpvm']}</td>";
            echo "<td>$" . number_format($subtotal) . "</td>";
            echo "<td>{$producto['estado']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<div class='alert alert-info'>";
        echo "<h4>💰 Total Real a Facturar: $" . number_format($totalReal) . "</h4>";
        echo "</div>";
        
        // Calcular distribución de pago
        $efectivo = round($totalReal * 0.2);
        $tarjeta = round($totalReal * 0.2);
        $nequi = round($totalReal * 0.2);
        $daviplata = round($totalReal * 0.2);
        $bancolombia = $totalReal - ($efectivo + $tarjeta + $nequi + $daviplata);
        $propina = round($totalReal * 0.1);
        
        echo "<h3>2. 💳 Distribución de Pago Calculada</h3>";
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
        echo "<h4>Para Total: $" . number_format($totalReal) . "</h4>";
        echo "<ul>";
        echo "<li>💵 <strong>Efectivo:</strong> $" . number_format($efectivo) . " (20%)</li>";
        echo "<li>💳 <strong>Tarjeta:</strong> $" . number_format($tarjeta) . " (20%)</li>";
        echo "<li>📱 <strong>Nequi:</strong> $" . number_format($nequi) . " (20%)</li>";
        echo "<li>💰 <strong>Daviplata:</strong> $" . number_format($daviplata) . " (20%)</li>";
        echo "<li>🏦 <strong>Bancolombia:</strong> $" . number_format($bancolombia) . " (resto)</li>";
        echo "</ul>";
        echo "<p><strong>Total Pagado:</strong> $" . number_format($efectivo + $tarjeta + $nequi + $daviplata + $bancolombia) . "</p>";
        echo "<p><strong>Propina:</strong> $" . number_format($propina) . " (10% adicional)</p>";
        echo "</div>";
        
        echo "<h3>3. 🧪 Test de Facturación</h3>";
        echo "<button onclick='testFacturarReal($totalReal, $efectivo, $tarjeta, $nequi, $daviplata, $bancolombia, $propina)' ";
        echo "style='background: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;'>";
        echo "💰 Facturar con Datos Reales";
        echo "</button>";
        
    } else {
        echo "<div class='alert alert-warning'>⚠️ No hay productos disponibles para facturar</div>";
        echo "<p>Para crear productos de prueba:</p>";
        echo "<ol>";
        echo "<li>Ve a la mesa y agrega algunos productos</li>";
        echo "<li>Envía el pedido (para que cambie a estado 'enviado')</li>";
        echo "<li>Vuelve aquí para facturar</li>";
        echo "</ol>";
        
        // Mostrar estado de pedidos para debug
        echo "<h4>Debug: Estado de pedidos en la mesa</h4>";
        $db = Conexion::conectar();
        $sql = "SELECT p.*, COUNT(pvm.productos_id) as productos FROM pedidos p 
                LEFT JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id 
                WHERE p.mesa_id = ? GROUP BY p.id ORDER BY p.fecha_pedido DESC";
        $stmt = $db->prepare($sql);
        $stmt->bindParam(1, $mesaId, PDO::PARAM_INT);
        $stmt->execute();
        $pedidos = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($pedidos)) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>ID</th><th>Número</th><th>Estado</th><th>Productos</th><th>Fecha</th></tr>";
            foreach ($pedidos as $p) {
                echo "<tr>";
                echo "<td>{$p['id']}</td>";
                echo "<td>{$p['numero_pedido']}</td>";
                echo "<td>{$p['estado']}</td>";
                echo "<td>{$p['productos']}</td>";
                echo "<td>{$p['fecha_pedido']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
    echo "Stack trace: " . $e->getTraceAsString() . "<br>";
}

echo "<br><a href='index.php?action=registroPmesa&ida=$mesaId' style='background: #007bff; color: white; padding: 10px; text-decoration: none;'>🔙 Volver a Mesa $mesaId</a>";
echo "<br><a href='test_facturacion_real.php?mesa=$mesaId' style='background: #6c757d; color: white; padding: 10px; text-decoration: none; margin: 5px;'>🔄 Actualizar</a>";
?>

<script>
function testFacturarReal(totalReal, efectivo, tarjeta, nequi, daviplata, bancolombia, propina) {
    console.log('💰 Iniciando facturación con datos reales');
    console.log('Total:', totalReal, 'Efectivo:', efectivo, 'Tarjeta:', tarjeta, 'Nequi:', nequi, 'Daviplata:', daviplata, 'Bancolombia:', bancolombia, 'Propina:', propina);
    
    if (confirm('¿Facturar con datos reales?\n\nTotal: $' + totalReal.toLocaleString() + '\nEfectivo: $' + efectivo.toLocaleString() + '\nTarjeta: $' + tarjeta.toLocaleString() + '\nNequi: $' + nequi.toLocaleString() + '\nDaviplata: $' + daviplata.toLocaleString() + '\nBancolombia: $' + bancolombia.toLocaleString() + '\nPropina: $' + propina.toLocaleString())) {
        
        // Datos de facturación con valores reales
        const datosFactura = {
            efectivo: efectivo,
            tarjeta: tarjeta,
            nequi: nequi,
            daviplata: daviplata,
            bancolombia: bancolombia,
            pago: 1, // contado
            pcedula: '12345678',
            totalDescuento: 0,
            total: totalReal,
            propina: propina,
            mesa: <?=$mesaId?>,
            tipoTarjeta: 'credito'
        };
        
        console.log('📋 Datos enviados:', datosFactura);
        
        // Mostrar loading
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '⏳ Procesando facturación...';
        button.disabled = true;
        
        // Enviar facturación
        fetch('ajaxFactura.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams(datosFactura)
        })
        .then(response => response.text())
        .then(text => {
            console.log('📄 Respuesta completa:', text);
            
            // Restaurar botón
            button.innerHTML = originalText;
            button.disabled = false;
            
            // Analizar respuesta
            if (text.includes('Pago realizado exitosamente') || text.includes('success')) {
                alert('✅ ÉXITO: Facturación completada correctamente\n\nLa mesa debería estar libre ahora.');
                setTimeout(() => location.reload(), 2000);
            } else if (text.includes('alert')) {
                // Extraer mensaje del alert
                const alertMatch = text.match(/alert\('([^']+)'\)/);
                const mensaje = alertMatch ? alertMatch[1] : text;
                alert('ℹ️ Mensaje del sistema:\n\n' + mensaje);
            } else if (text.includes('error') || text.includes('Error')) {
                alert('❌ ERROR:\n\n' + text);
            } else {
                alert('📄 Respuesta del servidor:\n\n' + text);
            }
        })
        .catch(error => {
            console.error('❌ Error:', error);
            
            // Restaurar botón
            button.innerHTML = originalText;
            button.disabled = false;
            
            alert('❌ Error de conexión: ' + error.message);
        });
    }
}
</script>

<style>
.alert {
    padding: 15px;
    margin: 15px 0;
    border-radius: 5px;
    border: 1px solid transparent;
}
.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}
.alert-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}
.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}
</style>
