<?php
// Vista de pantalla de cocina para el sistema de pedidos
// Acceso: https://macarena.anconclub.com/pantallaCocina?categoria=cocina

// Verificar permisos (las sesiones ya están iniciadas por el sistema principal)
if (!isset($_SESSION["tipo_usuario"]) || ($_SESSION["tipo_usuario"] != 4 && $_SESSION["tipo_usuario"] != 1)) {
    echo "<script>alert('No tiene permisos para acceder a esta sección')</script>";
    header("location:inicio");
    exit;
}

// Crear instancia del controlador (las clases ya están cargadas por index.php)
$controllerEstado = new ControllerEstadoPedidos();

// Obtener categoría del usuario (por defecto cocina)
$categoria = isset($_GET['categoria']) ? $_GET['categoria'] : 'cocina';

// Validar categoría
$categoriasValidas = ['bar', 'cocina', 'asados'];
if (!in_array($categoria, $categoriasValidas)) {
    $categoria = 'cocina';
}

// Obtener pedidos pendientes para esta categoría
$pedidosPendientes = $controllerEstado->obtenerPedidosPendientesCategoriaController($categoria);
?>

<style>
    .pedido-card {
        border: 2px solid #007bff;
        margin-bottom: 15px;
        border-radius: 10px;
    }
    .pedido-enviado {
        border-color: #ffc107;
        background-color: #fff3cd;
    }
    .pedido-header {
        background-color: #007bff;
        color: white;
        padding: 10px;
        border-radius: 8px 8px 0 0;
    }
    .producto-item {
        padding: 5px 0;
        border-bottom: 1px solid #eee;
    }
    .btn-entregar {
        background-color: #28a745;
        border-color: #28a745;
        color: white;
        font-weight: bold;
    }
    .tiempo-pedido {
        font-size: 0.9em;
        color: #666;
    }
    .refresh-btn {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1000;
    }
</style>
<script>
        // Cargar productos de cada pedido
        $(document).ready(function() {
            <?php foreach ($pedidosPendientes as $pedido): ?>
                cargarProductosPedido(<?=$pedido['id']?>);
            <?php endforeach; ?>
            
            // Auto-refresh cada 30 segundos
            setInterval(function() {
                location.reload();
            }, 30000);
        });
        
        function cargarProductosPedido(pedidoId) {
            $.post("views/modules/ajaxEstadoPedidos.php", {
                obtener_productos_pedido: true,
                pedido_id: pedidoId
            }, function(data) {
                var productos = JSON.parse(data);
                var html = '';
                
                productos.forEach(function(producto) {
                    if (producto.categoria === '<?=$categoria?>') {
                        html += '<div class="producto-item">';
                        html += '<strong>' + producto.cantidad + 'x</strong> ' + producto.nombre;
                        if (producto.nota && producto.nota !== '-') {
                            html += '<br><small class="text-muted">Nota: ' + producto.nota + '</small>';
                        }
                        html += '</div>';
                    }
                });
                
                if (html === '') {
                    html = '<div class="text-muted">No hay productos de esta categoría</div>';
                }
                
                $('#productos-' + pedidoId).html(html);
            });
        }
        
        function marcarEntregado(pedidoId) {
            if (confirm('¿Confirma que el pedido ha sido entregado?')) {
                $.post("views/modules/ajaxEstadoPedidos.php", {
                    entregar_pedido: true,
                    pedido_id: pedidoId
                }, function(data) {
                    var resultado = JSON.parse(data);
                    if (resultado.status === 'success') {
                        $('#pedido-' + pedidoId).fadeOut(500, function() {
                            $(this).remove();
                            // Actualizar contador
                            var contador = $('.text-muted').text();
                            var numero = parseInt(contador.match(/\d+/)[0]) - 1;
                            $('.text-muted').text('(' + numero + ' pedidos pendientes)');
                            
                            if (numero === 0) {
                                location.reload();
                            }
                        });
                        
                        // Mostrar notificación de éxito
                        mostrarNotificacion('Pedido marcado como entregado', 'success');
                    } else {
                        alert('Error: ' + resultado.message);
                    }
                });
            }
        }
        
        function reimprimirPedido(pedidoId, categoria) {
            var motivo = prompt('Motivo de la reimpresión:', 'Reimpresión solicitada desde cocina');
            if (motivo) {
                $.post("views/modules/ajaxEstadoPedidos.php", {
                    reimprimir_pedido: true,
                    pedido_id: pedidoId,
                    categoria: categoria,
                    motivo: motivo
                }, function(data) {
                    var resultado = JSON.parse(data);
                    mostrarNotificacion(resultado.message, resultado.status === 'success' ? 'success' : 'danger');
                });
            }
        }
        
        function mostrarNotificacion(mensaje, tipo) {
            var alertClass = 'alert-' + tipo;
            var html = '<div class="alert ' + alertClass + ' alert-dismissible fade show" style="position: fixed; top: 80px; right: 20px; z-index: 1050; min-width: 300px;">';
            html += '<button type="button" class="close" data-dismiss="alert">&times;</button>';
            html += mensaje;
            html += '</div>';
            
            $('body').append(html);
            
            // Auto-cerrar después de 3 segundos
            setTimeout(function() {
                $('.alert').alert('close');
            }, 3000);
        }
    </script>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    Pantalla de <?=ucfirst($categoria)?>
                    <small class="text-muted">(<?=count($pedidosPendientes)?> pedidos pendientes)</small>
                </h1>
                
                <!-- Botones de categoría -->
                <div class="text-center mb-4">
                    <div class="btn-group" role="group">
                        <a href="?categoria=bar" class="btn <?=$categoria=='bar'?'btn-primary':'btn-outline-primary'?>">Bar</a>
                        <a href="?categoria=cocina" class="btn <?=$categoria=='cocina'?'btn-primary':'btn-outline-primary'?>">Cocina</a>
                        <a href="?categoria=asados" class="btn <?=$categoria=='asados'?'btn-primary':'btn-outline-primary'?>">Asados</a>
                    </div>
                </div>
                
                <!-- Botón de actualizar -->
                <button class="btn btn-info refresh-btn" onclick="location.reload()">
                    🔄 Actualizar
                </button>
                
                <!-- Lista de pedidos -->
                <div class="row" id="pedidos-container">
                    <?php if (empty($pedidosPendientes)): ?>
                        <div class="col-12">
                            <div class="alert alert-info text-center">
                                <h4>No hay pedidos pendientes para <?=ucfirst($categoria)?></h4>
                                <p>Los nuevos pedidos aparecerán aquí automáticamente.</p>
                            </div>
                        </div>
                    <?php else: ?>
                        <?php foreach ($pedidosPendientes as $pedido): ?>
                            <div class="col-md-6 col-lg-4">
                                <div class="card pedido-card pedido-enviado" id="pedido-<?=$pedido['id']?>">
                                    <div class="pedido-header">
                                        <h5 class="mb-1">
                                            <?=$pedido['numero_pedido']?> - Mesa <?=$pedido['mesa_numero']?>
                                        </h5>
                                        <small class="tiempo-pedido">
                                            Enviado: <?=date('H:i', strtotime($pedido['fecha_envio']))?>
                                            (<?=date_diff(date_create($pedido['fecha_envio']), date_create())->format('%i min')?>)
                                        </small>
                                    </div>
                                    <div class="card-body">
                                        <div class="productos-lista" id="productos-<?=$pedido['id']?>">
                                            <div class="text-center">
                                                <div class="spinner-border spinner-border-sm" role="status">
                                                    <span class="sr-only">Cargando...</span>
                                                </div>
                                                Cargando productos...
                                            </div>
                                        </div>
                                        <div class="text-center mt-3">
                                            <button class="btn btn-entregar btn-lg" onclick="marcarEntregado(<?=$pedido['id']?>)">
                                                ✓ Marcar como Entregado
                                            </button>
                                            <button class="btn btn-outline-info btn-sm ml-2" onclick="reimprimirPedido(<?=$pedido['id']?>, '<?=$categoria?>')">
                                                🖨️ Reimprimir
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>