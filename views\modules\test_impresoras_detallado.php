<?php
// Test detallado de conectividad con impresoras
require_once "../../models/conexion.php";

echo "<h1>🖨️ Test Detallado de Conectividad con Impresoras</h1>";
echo "<p><strong>Fecha/Hora:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<p><strong>IP del servidor:</strong> " . $_SERVER['SERVER_ADDR'] . "</p>";

// Obtener impresoras de la base de datos
try {
    $db = Conexion::conectar();
    $stmt = $db->prepare("SELECT * FROM impresoras ORDER BY categoria");
    $stmt->execute();
    $impresoras = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($impresoras)) {
        echo "<p>❌ No hay impresoras configuradas en la base de datos</p>";
        exit;
    }
    
    echo "<h2>📋 Impresoras Configuradas:</h2>";
    echo "<table border='1' style='border-collapse: collapse; margin-bottom: 20px;'>";
    echo "<tr><th>Categoría</th><th>IP</th><th>Puerto</th><th>Estado</th></tr>";
    
    foreach ($impresoras as $imp) {
        echo "<tr>";
        echo "<td>" . $imp['categoria'] . "</td>";
        echo "<td>" . $imp['ip'] . "</td>";
        echo "<td>" . $imp['puerto'] . "</td>";
        echo "<td>" . (isset($imp['activa']) && $imp['activa'] ? 'Activa' : 'Inactiva') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>🔍 Test de Conectividad Detallado:</h2>";
    
    foreach ($impresoras as $imp) {
        echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
        echo "<h3>🖨️ Impresora: " . strtoupper($imp['categoria']) . "</h3>";
        echo "<p><strong>IP:</strong> " . $imp['ip'] . "</p>";
        echo "<p><strong>Puerto:</strong> " . $imp['puerto'] . "</p>";
        
        // Test 1: Ping básico (simulado con fsockopen)
        echo "<h4>Test 1: Conectividad TCP</h4>";
        $start_time = microtime(true);
        $fp = @fsockopen($imp['ip'], $imp['puerto'], $errno, $errstr, 10);
        $end_time = microtime(true);
        $tiempo = round(($end_time - $start_time) * 1000, 2);
        
        if ($fp) {
            echo "<p>✅ <strong>CONECTADA</strong> - Tiempo de respuesta: {$tiempo}ms</p>";
            fclose($fp);
            
            // Test 2: Envío de comando básico
            echo "<h4>Test 2: Envío de Comando de Prueba</h4>";
            $fp2 = @fsockopen($imp['ip'], $imp['puerto'], $errno2, $errstr2, 5);
            if ($fp2) {
                $init = chr(27) . chr(64); // ESC @ - Inicializar impresora
                $test_text = "TEST MACARENA\n" . date('Y-m-d H:i:s') . "\n\n\n";
                $cut = chr(29) . chr(86) . chr(65) . chr(3); // Cortar papel
                
                $enviado = fwrite($fp2, $init . $test_text . $cut);
                fclose($fp2);
                
                if ($enviado) {
                    echo "<p>✅ <strong>COMANDO ENVIADO</strong> - Bytes enviados: $enviado</p>";
                    echo "<p>📄 Se envió un ticket de prueba a la impresora</p>";
                } else {
                    echo "<p>❌ <strong>ERROR ENVIANDO COMANDO</strong></p>";
                }
            } else {
                echo "<p>❌ <strong>ERROR EN SEGUNDA CONEXIÓN</strong> - $errstr2 ($errno2)</p>";
            }
            
        } else {
            echo "<p>❌ <strong>NO CONECTADA</strong> - Error: $errstr ($errno)</p>";
            echo "<p>⏱️ Tiempo de timeout: {$tiempo}ms</p>";
            
            // Diagnósticos adicionales
            echo "<h4>🔧 Diagnósticos Adicionales:</h4>";
            echo "<ul>";
            echo "<li><strong>Error Code:</strong> $errno</li>";
            echo "<li><strong>Error Message:</strong> $errstr</li>";
            
            // Verificar si es problema de red o puerto
            if ($errno == 111) {
                echo "<li>🔍 <strong>Connection Refused (111):</strong> La impresora rechaza la conexión</li>";
                echo "<li>💡 <strong>Posibles causas:</strong></li>";
                echo "<ul>";
                echo "<li>La impresora está apagada</li>";
                echo "<li>El puerto 9100 está bloqueado</li>";
                echo "<li>La IP es incorrecta</li>";
                echo "<li>Problema de red/firewall</li>";
                echo "</ul>";
            } elseif ($errno == 110) {
                echo "<li>⏰ <strong>Connection Timeout (110):</strong> No hay respuesta</li>";
                echo "<li>💡 <strong>Posibles causas:</strong></li>";
                echo "<ul>";
                echo "<li>La impresora no está en la red</li>";
                echo "<li>IP incorrecta</li>";
                echo "<li>Problemas de red</li>";
                echo "</ul>";
            }
            echo "</ul>";
        }
        
        // Test 3: Verificar si la IP responde en otros puertos comunes
        echo "<h4>Test 3: Verificación de Puertos Alternativos</h4>";
        $puertos_comunes = [80, 443, 23, 515, 631];
        $respuestas = [];
        
        foreach ($puertos_comunes as $puerto) {
            $fp_alt = @fsockopen($imp['ip'], $puerto, $errno_alt, $errstr_alt, 2);
            if ($fp_alt) {
                $respuestas[] = $puerto;
                fclose($fp_alt);
            }
        }
        
        if (!empty($respuestas)) {
            echo "<p>🔍 <strong>Puertos abiertos encontrados:</strong> " . implode(', ', $respuestas) . "</p>";
            echo "<p>💡 Esto indica que la IP está activa pero el puerto 9100 puede estar cerrado</p>";
        } else {
            echo "<p>❌ <strong>No se encontraron puertos abiertos</strong> - La IP puede estar inactiva</p>";
        }
        
        echo "</div>";
    }
    
    // Información adicional del sistema
    echo "<h2>🖥️ Información del Sistema:</h2>";
    echo "<ul>";
    echo "<li><strong>PHP Version:</strong> " . phpversion() . "</li>";
    echo "<li><strong>Servidor:</strong> " . $_SERVER['SERVER_SOFTWARE'] . "</li>";
    echo "<li><strong>IP del Servidor:</strong> " . $_SERVER['SERVER_ADDR'] . "</li>";
    echo "<li><strong>User Agent:</strong> " . $_SERVER['HTTP_USER_AGENT'] . "</li>";
    echo "</ul>";
    
    // Recomendaciones
    echo "<h2>💡 Recomendaciones:</h2>";
    echo "<div style='background-color: #f0f8ff; padding: 15px; border-left: 4px solid #007bff;'>";
    echo "<h4>Para solucionar problemas de conectividad:</h4>";
    echo "<ol>";
    echo "<li><strong>Verificar que las impresoras estén encendidas</strong></li>";
    echo "<li><strong>Confirmar que están en la misma red (192.168.68.x)</strong></li>";
    echo "<li><strong>Verificar las IPs desde tu computadora:</strong>";
    echo "<ul>";
    echo "<li>Abrir CMD y ejecutar: <code>ping **************</code></li>";
    echo "<li>Abrir CMD y ejecutar: <code>ping **************</code></li>";
    echo "<li>Abrir CMD y ejecutar: <code>ping **************</code></li>";
    echo "</ul></li>";
    echo "<li><strong>Verificar puerto 9100:</strong>";
    echo "<ul>";
    echo "<li>Ejecutar: <code>telnet ************** 9100</code></li>";
    echo "<li>Si se conecta, la impresora está lista</li>";
    echo "</ul></li>";
    echo "<li><strong>Revisar configuración de red de las impresoras</strong></li>";
    echo "<li><strong>Verificar que no haya firewall bloqueando el puerto 9100</strong></li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p>❌ <strong>Error:</strong> " . $e->getMessage() . "</p>";
}
?>
