<?php
// Configurador de impresoras - Cambiar entre prueba y producción
echo "<h1>⚙️ Configurador de Impresoras</h1>";
echo "<p><strong>Cambiar entre configuración de prueba y producción</strong></p>";

// Configuraciones disponibles
$configuraciones = [
    'prueba' => [
        'nombre' => 'Configuración de Prueba (Red 192.168.18.x)',
        'descripcion' => 'Para pruebas con una sola impresora',
        'impresoras' => [
            'test' => ['ip' => '*************', 'puerto' => 9100, 'nombre' => '🧪 TEST']
        ]
    ],
    'produccion' => [
        'nombre' => 'Configuración de Producción (Red 192.168.68.x)',
        'descripcion' => 'Para el restaurante Macarena',
        'impresoras' => [
            'bar' => ['ip' => '**************', 'puerto' => 9100, 'nombre' => '🍺 BAR'],
            'cocina' => ['ip' => '**************', 'puerto' => 9100, 'nombre' => '🍳 COCINA'],
            'asados' => ['ip' => '**************', 'puerto' => 9100, 'nombre' => '🥩 ASADOS']
        ]
    ],
    'mixta' => [
        'nombre' => 'Configuración Mixta (Ambas redes)',
        'descripcion' => 'Incluye impresora de prueba + producción',
        'impresoras' => [
            'test' => ['ip' => '*************', 'puerto' => 9100, 'nombre' => '🧪 TEST'],
            'bar' => ['ip' => '**************', 'puerto' => 9100, 'nombre' => '🍺 BAR'],
            'cocina' => ['ip' => '**************', 'puerto' => 9100, 'nombre' => '🍳 COCINA'],
            'asados' => ['ip' => '**************', 'puerto' => 9100, 'nombre' => '🥩 ASADOS']
        ]
    ]
];

// Detectar configuración actual
function detectarConfiguracionActual() {
    $archivo_socket = '../../models/crudImpresionSocket.php';
    if (file_exists($archivo_socket)) {
        $contenido = file_get_contents($archivo_socket);
        
        if (strpos($contenido, "'test' => ['ip' => '*************'") !== false) {
            if (strpos($contenido, "'bar' => ['ip' => '**************'") !== false) {
                return 'mixta';
            } else {
                return 'prueba';
            }
        } else {
            return 'produccion';
        }
    }
    return 'desconocida';
}

$config_actual = detectarConfiguracionActual();

echo "<div style='background-color: #d1ecf1; padding: 15px; border-left: 4px solid #17a2b8; margin-bottom: 20px;'>";
echo "<h4>📊 Estado Actual</h4>";
echo "<p><strong>Configuración activa:</strong> " . ($configuraciones[$config_actual]['nombre'] ?? 'Desconocida') . "</p>";
echo "<p><strong>Tu IP actual:</strong> " . $_SERVER['REMOTE_ADDR'] . "</p>";

// Detectar red actual
$ip_actual = $_SERVER['REMOTE_ADDR'];
if (strpos($ip_actual, '192.168.18.') === 0) {
    echo "<p>🟢 <strong>Red detectada:</strong> 192.168.18.x (Red de prueba)</p>";
} elseif (strpos($ip_actual, '192.168.68.') === 0) {
    echo "<p>🟢 <strong>Red detectada:</strong> 192.168.68.x (Red de producción)</p>";
} else {
    echo "<p>🟡 <strong>Red detectada:</strong> Otra red (usar proxy si es necesario)</p>";
}
echo "</div>";

// Procesar cambio de configuración
if (isset($_POST['cambiar_config'])) {
    $nueva_config = $_POST['configuracion'];
    
    if (isset($configuraciones[$nueva_config])) {
        echo "<div style='border: 1px solid #007bff; padding: 20px; margin: 15px 0; border-radius: 8px;'>";
        echo "<h4>🔄 Cambiando Configuración...</h4>";
        
        $config = $configuraciones[$nueva_config];
        
        // Generar código PHP para las impresoras
        $codigo_impresoras = "    // Configuración: " . $config['nombre'] . "\n";
        $codigo_impresoras .= "    private static \$IMPRESORAS = [\n";
        
        foreach ($config['impresoras'] as $categoria => $impresora) {
            $codigo_impresoras .= "        '$categoria' => ['ip' => '{$impresora['ip']}', 'puerto' => {$impresora['puerto']}],\n";
        }
        
        $codigo_impresoras .= "    ];";
        
        // Archivos a actualizar
        $archivos = [
            '../../models/crudImpresionSocket.php',
            '../../models/crudImpresionDirecta.php'
        ];
        
        $actualizados = 0;
        
        foreach ($archivos as $archivo) {
            if (file_exists($archivo)) {
                $contenido = file_get_contents($archivo);
                
                // Buscar y reemplazar la configuración de impresoras
                $patron = '/private static \$IMPRESORAS = \[.*?\];/s';
                $nuevo_contenido = preg_replace($patron, $codigo_impresoras, $contenido);
                
                if ($nuevo_contenido && $nuevo_contenido !== $contenido) {
                    if (file_put_contents($archivo, $nuevo_contenido)) {
                        echo "<p>✅ Actualizado: " . basename($archivo) . "</p>";
                        $actualizados++;
                    } else {
                        echo "<p>❌ Error actualizando: " . basename($archivo) . "</p>";
                    }
                } else {
                    echo "<p>⚠️ Sin cambios en: " . basename($archivo) . "</p>";
                }
            } else {
                echo "<p>❌ Archivo no encontrado: " . basename($archivo) . "</p>";
            }
        }
        
        // Actualizar API
        $archivo_api = '../../api/imprimir_directo.php';
        if (file_exists($archivo_api)) {
            $contenido_api = file_get_contents($archivo_api);
            
            $codigo_api = "// Configuración: " . $config['nombre'] . "\n";
            $codigo_api .= "\$IMPRESORAS = [\n";
            
            foreach ($config['impresoras'] as $categoria => $impresora) {
                $codigo_api .= "    '$categoria' => ['ip' => '{$impresora['ip']}', 'puerto' => {$impresora['puerto']}],\n";
            }
            
            $codigo_api .= "];";
            
            $patron_api = '/\/\/ Configuración.*?\$IMPRESORAS = \[.*?\];/s';
            $nuevo_contenido_api = preg_replace($patron_api, $codigo_api, $contenido_api);
            
            if ($nuevo_contenido_api && $nuevo_contenido_api !== $contenido_api) {
                if (file_put_contents($archivo_api, $nuevo_contenido_api)) {
                    echo "<p>✅ Actualizado: API de impresión</p>";
                    $actualizados++;
                }
            }
        }
        
        if ($actualizados > 0) {
            echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0;'>";
            echo "<h5>✅ Configuración Cambiada Exitosamente</h5>";
            echo "<p><strong>Nueva configuración:</strong> {$config['nombre']}</p>";
            echo "<p><strong>Archivos actualizados:</strong> $actualizados</p>";
            echo "<p><strong>Impresoras configuradas:</strong> " . count($config['impresoras']) . "</p>";
            echo "</div>";
        } else {
            echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
            echo "<h5>❌ Error en el Cambio</h5>";
            echo "<p>No se pudieron actualizar los archivos</p>";
            echo "</div>";
        }
        
        echo "</div>";
        
        // Actualizar configuración actual
        $config_actual = detectarConfiguracionActual();
    }
}

// Mostrar configuraciones disponibles
echo "<h2>⚙️ Configuraciones Disponibles</h2>";

foreach ($configuraciones as $key => $config) {
    $es_actual = ($key === $config_actual);
    $color_fondo = $es_actual ? '#d4edda' : '#f8f9fa';
    $color_borde = $es_actual ? '#28a745' : '#dee2e6';
    
    echo "<div style='background-color: $color_fondo; padding: 20px; margin: 15px 0; border-radius: 8px; border: 1px solid $color_borde;'>";
    
    if ($es_actual) {
        echo "<h4>✅ {$config['nombre']} (ACTIVA)</h4>";
    } else {
        echo "<h4>⚪ {$config['nombre']}</h4>";
    }
    
    echo "<p>{$config['descripcion']}</p>";
    
    echo "<h5>🖨️ Impresoras incluidas:</h5>";
    echo "<ul>";
    foreach ($config['impresoras'] as $categoria => $impresora) {
        echo "<li><strong>{$impresora['nombre']}:</strong> {$impresora['ip']}:{$impresora['puerto']}</li>";
    }
    echo "</ul>";
    
    if (!$es_actual) {
        echo "<form method='POST' style='margin-top: 15px;'>";
        echo "<input type='hidden' name='configuracion' value='$key'>";
        echo "<button type='submit' name='cambiar_config' style='background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold;'>🔄 Activar Esta Configuración</button>";
        echo "</form>";
    }
    
    echo "</div>";
}

// Test rápido de la configuración actual
echo "<h2>🧪 Test Rápido de Configuración Actual</h2>";

if (isset($_POST['test_rapido'])) {
    echo "<div style='border: 1px solid #007bff; padding: 20px; margin: 15px 0; border-radius: 8px;'>";
    echo "<h4>🔍 Probando configuración actual...</h4>";
    
    require_once '../../models/crudImpresionSocket.php';
    
    if (extension_loaded('sockets')) {
        $estado = ImpresionSocket::testConectividad();
        
        foreach ($estado as $categoria => $info) {
            if ($info['status'] === 'online') {
                echo "<div style='background-color: #d4edda; padding: 10px; border-left: 4px solid #28a745; margin: 10px 0;'>";
                echo "<p>✅ <strong>" . strtoupper($categoria) . ":</strong> Online ({$info['ip']}:{$info['puerto']}) - {$info['tiempo_ms']}ms</p>";
                echo "</div>";
            } else {
                echo "<div style='background-color: #f8d7da; padding: 10px; border-left: 4px solid #dc3545; margin: 10px 0;'>";
                echo "<p>❌ <strong>" . strtoupper($categoria) . ":</strong> Offline - {$info['error']}</p>";
                echo "</div>";
            }
        }
    } else {
        echo "<p>⚠️ Extensión de sockets no disponible. Usando método alternativo...</p>";
        
        require_once '../../models/crudImpresionDirecta.php';
        $estado = ImpresionDirecta::testConectividad();
        
        foreach ($estado as $categoria => $info) {
            if ($info['status'] === 'online') {
                echo "<p>✅ <strong>" . strtoupper($categoria) . ":</strong> Online</p>";
            } else {
                echo "<p>❌ <strong>" . strtoupper($categoria) . ":</strong> {$info['error']}</p>";
            }
        }
    }
    
    echo "</div>";
}

echo "<form method='POST' style='background-color: #fff3cd; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>🔍 Test de Conectividad</h4>";
echo "<p>Probar todas las impresoras de la configuración actual</p>";
echo "<button type='submit' name='test_rapido' style='background-color: #ffc107; color: #212529; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold; width: 100%;'>🧪 Probar Configuración Actual</button>";
echo "</form>";

// Información adicional
echo "<h2>💡 Información Importante</h2>";
echo "<div style='background-color: #e7f3ff; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>📋 Uso Recomendado:</h4>";
echo "<ul>";
echo "<li><strong>Para pruebas hoy:</strong> Usar configuración 'Mixta' o 'Prueba'</li>";
echo "<li><strong>Para producción:</strong> Cambiar a configuración 'Producción'</li>";
echo "<li><strong>Cambios automáticos:</strong> Los archivos se actualizan automáticamente</li>";
echo "<li><strong>Sin reinicio:</strong> Los cambios son inmediatos</li>";
echo "</ul>";

echo "<h4>⚠️ Notas:</h4>";
echo "<ul>";
echo "<li>Los cambios afectan a todos los tests de impresión</li>";
echo "<li>La configuración se mantiene hasta el próximo cambio</li>";
echo "<li>Siempre hacer test de conectividad después de cambiar</li>";
echo "</ul>";
echo "</div>";

// Botones de navegación
echo "<p style='margin-top: 30px;'>";
echo "<a href='test_impresion_socket.php' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🔌 Test Sockets</a>";
echo "<a href='test_impresion_simple.php' style='background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>⚡ Test Simple</a>";
echo "<a href='diagnostico' style='background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>← Diagnóstico</a>";
echo "</p>";
?>
