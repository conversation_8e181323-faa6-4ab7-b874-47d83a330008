<?php
require_once "conexion.php";
require_once "crudTurno.php";
class DatosAbonosProveedor extends Conexion
{
	#BUSCAR DEUDA CLIENTE TODO
	#-------------------------------------
	 public static function deudaProveedorModel()
		{//echo "<script>alert('entro crud Deuda proveedor ');</script>";
			$consulta = "SELECT cpr.id as cprid, cpr.compras_suministro_id as cprcompras_suministro_id, cpr.abonos_proveedores_id as cprabonos_proveedores_id, cpr.cuenta as cprcuenta,
					cs.id as csid, cs.proveedor_id as csproveedor_id, cs.valor_compra as csvalor_compra, cs.fecha_hora as csfecha_hora, cs.numero_factura_compra as csnumero_factura_compra, cs.fecha_modificacion as csfecha_modificacion,
					pr.id as prid, pr.nombre as prnombre, pr.nit as prnit,  pr.representante as prrepresentante, pr.direccion as prdireccion, pr.telefonos as prtelefonos
					FROM creditos_proveedores cpr,  compras_suministros cs, proveedores pr
					WHERE cpr.compras_suministro_id=cs.id  AND pr.id=cs.proveedor_id GROUP BY cs.id ORDER BY  pr.id ASC";
			$stmt = Conexion::conectar()->prepare($consulta);
			//echo "<script>alert('consulta  1');</script>";//echo "<script>alert('consulta  ".$consulta."  ');</script>";
			$stmt->execute();
			$r= $stmt->fetchAll();
			//echo "<script>alert('consulta  2 ');</script>";			
			return $r;
			$stmt->close();
		}
	#----------------------------------------
	#BUSCAR DEUDA CLIENTE
	#-------------------------------------
	 public static function buscardeudaProveedorModel($datosModel)
		{		//echo "<script>alert('entro crud Deuda del cliente ".$datosModel."')</script>";
			$consulta = "SELECT cpr.id as cprid, cpr.compras_suministro_id as cprcompras_suministro_id, cpr.abonos_proveedores_id as cprabonos_proveedores_id, cpr.cuenta as cprcuenta,
					cs.id as csid, cs.proveedor_id as csproveedor_id, cs.valor_compra as csvalor_compra, cs.fecha_hora as csfecha_hora, cs.numero_factura_compra as csnumero_factura_compra, cs.fecha_modificacion as csfecha_modificacion,
					pr.id as prid, pr.nombre as prnombre, pr.nit as prnit,  pr.representante as prrepresentante, pr.direccion as prdireccion, pr.telefonos as prtelefonos
					FROM creditos_proveedores cpr,  compras_suministros cs, proveedores pr
					WHERE cpr.compras_suministro_id=cs.id  AND pr.id=cs.proveedor_id AND pr.nit = :nit     GROUP BY cpr.compras_suministro_id ";
			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->bindParam(":nit", $datosModel, PDO::PARAM_STR);	
			$stmt->execute();
			$r= $stmt->fetchAll();
		 foreach ($r as $rows => $item)
			{	$vertor=0;	 }

			$c2=$stmt->rowCount();
			//echo '<br> C2='.$c2.'<br>';
			if ($c2>0) 
			 { 	//echo "<script>alert(' if entro consulta ".$r['dcuenta'] ."')</script>";
			 	return $r;	
			 }
			else
			 {echo "<script>alert(' Error BD Buscar deuda')</script>";				
				return 0;
		     }
			$stmt->close();
		}
	#----------------------------------------			
	#BUSCAR DEUDA factura
	#-------------------------------------
	 public static function buscardeudaFacturaPModel($datosModel)
		{	//echo "<script>alert('busqueda de factura Proveedor ".$datosModel."')</script>";
			$consulta = "SELECT cs.id as csid, cpr.cuenta as cprcuenta FROM creditos_proveedores cpr, compras_suministros cs WHERE cpr.compras_suministro_id=cs.id AND cs.id=:csid  order by cpr.id DESC limit 1";
			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->bindParam(":csid", $datosModel, PDO::PARAM_STR);	
			$stmt->execute();			
			$r= $stmt->fetch();
			$c2=$stmt->rowCount();
			//echo '<br> C2='.$c2.'<br>';
			if ($c2>0) 
			 { 	//echo "<script>alert(' if entro consulta ".$r['dcuenta'] ."')</script>";
			 	return $r;	
			 }
			else
			 {//echo "<script>alert(' Error BD Buscar deuda')</script>";				
				return 0;
		     }
			$stmt->close();
		}
	#----------------------------------------			
	# BUSACRA CEDULA
	# -----------------------------------------
	 public static function cedulaModel($cedula)
		{
			$consulta = "SELECT id, cedula, nombre, apellidos FROM personas WHERE cedula = :cedula";
			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->bindParam(":cedula", $datosModel, PDO::PARAM_STR);	
			$stmt->execute();
			return $stmt->fetch();
			$stmt->close();
		}
	# -----------------------------------------			
	# BUSACRA ABONO
	# -----------------------------------------
	 public static function abonoPModel($deuda_id)
		{ //echo "<script>alert('bucar a bono Proveedor rrr ".$deuda_id."')</script>";
		 $consulta = "SELECT cpr.id as cprid, cpr.compras_suministro_id as cprcompras_suministro_id, cpr.abonos_proveedores_id as cprabonos_proveedores_id, cpr.cuenta as cprcuenta,	a.id AS aid, a.turnos_cajeros_id AS aturnos_cajeros_id, a.fecha_abono AS afecha_abono, a.descripcion as adescripcion, a.valor AS avalor
					FROM creditos_proveedores cpr, abonos_proveedores a
					WHERE a.id=cpr.abonos_proveedores_id AND cpr.compras_suministro_id = :deuda_id  group by cpr.id ORDER  by a.id ASC";
		 $stmt = Conexion::conectar()->prepare($consulta);
		 $stmt->bindParam(":deuda_id", $deuda_id, PDO::PARAM_INT);	
		 $stmt->execute();
		 $r=$stmt->fetchAll();
		 $c2=$stmt->rowCount();
		 //echo '<br> C2='.$c2.'<br>';
		 if ($c2>0) 
		  {	return $r;	}
		 else
		  {	return 0;	}
		 $stmt->close();
		}
	# -----------------------------------------
	#REGISTRAR ABONO  csid cprcuenta
	#-------------------------------------
	 public static function abonoProveedorModel($datosModel, $tabla)
		{   $cuentay=$datosModel["deuda"];
			$abonoy=$datosModel["valorAbono"];	
			echo "<script>alert('Vuelto  ".$valorDeuda1." ' );</script>";		
			$cliente = DatosAbonosProveedor::buscardeudaFacturaPModel($datosModel["deuda"]);
			$deuday=$cliente["cprcuenta"];
			//echo "<script>alert('Entro crud  Abonos=".$abonoy."->Cuenta=".$cuentay."=>Deuda=".$deuday."');</script>";							
			$valorDeuda1 = $cliente["cprcuenta"] - $datosModel["valorAbono"];						
			$turnos = $datosModel["turno"];
			$stmt = Conexion::conectar(); 												
			date_default_timezone_set("America/Bogota");
			$fecha_abono=strftime("%Y-%m-%d %H:%M:%S");
			//echo "<script>alert(' - valordeuda: ".$valorDeuda1." valorAbono: ".$datosModel["deuda"]."  - valor: ".$cliente["cprcuenta"]." ');</script>";
		 try
			{//echo "<script>alert('Entro  try');</script>";								
			 $stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);  
			 $stmt->beginTransaction();
			 //if ($valorDeuda1<=0) 
			 if ($cliente["cprcuenta"]<=$datosModel["valorAbono"]) 
				{ 	
				 $valorDeuda =0;
				 //// Inserta Abono//
				 $consulta="INSERT INTO $tabla ( turnos_cajeros_id, fecha_abono, descripcion, valor) VALUES ( ".$turnos.", '".$fecha_abono."', '".$datosModel["descripcion"]."', ".$cliente["cprcuenta"].")";						  
				 echo " --".$consulta;					
				 $stmt->exec($consulta);	
				 $ultimo_id=$stmt->lastInsertId();	//ultimo pedido		
                 //echo "<script>alert('ultimo pedido ".$ultimo_id." ' );</script>";	
				 //	 insertar deuda nueva	
				 $consulta1 = "INSERT INTO creditos_proveedores(compras_suministro_id, abonos_proveedores_id, cuenta) VALUES (".$datosModel["deuda"].", $ultimo_id,  $valorDeuda)";
				 $stmt->exec($consulta1);
				 echo "<script>alert('Vuelto  ".$valorDeuda1." ' );</script>";
				}
			 else
				{
				 $valorDeuda =$valorDeuda1;
			 	 //// Inserta Abono//
				 //echo "<script>alert('los datos de abono tabla - turnos: ".$turnos."  -fecha_abono: ".$fecha_abono."  - datosModel : ".$datosModel["descripsion"]."  $valorDeuda: ".$valorDeuda."');</script>";						
				 $consulta="INSERT INTO $tabla ( turnos_cajeros_id, fecha_abono, descripcion, valor) VALUES ( ".$turnos.", '".$fecha_abono."', '".$datosModel["descripcion"]."', ".$datosModel["valorAbono"].")";						  
				 //echo " <BR>--".$consulta;					
				 //echo "<script>alert('los datos de deudas tabla - deuda: ".$datosModel["deuda"]."  -ultimo_id: ".$ultimo_id."  - cliente : ".$cliente["pid"]."  valorDeuda: ".$valorDeuda."');</script>";
				 $stmt->exec($consulta);	
				 //echo "<script>alert('ultimo Abono ".$ultimo_id." ' );</script>";
				 $ultimo_id=$stmt->lastInsertId();	//ultimo pedido	                      								
				 //echo "<script>alert('ultimo Abono ".$ultimo_id." ' );</script>";	
				 //	 insertar deuda nueva	
				 $consulta1 = "INSERT INTO creditos_proveedores(compras_suministro_id, abonos_proveedores_id, cuenta) VALUES (".$datosModel["deuda"].", ".$ultimo_id.", ".$valorDeuda.")";
				 //echo " <br>--".$consulta1;
				 $stmt->exec($consulta1);
				 //echo "<script>alert('Queda debindo  ".$valorDeuda1." ' );</script>";
				}	
			 //echo "<script>alert('fin de cruz try')</script>";
			 $stmt->commit();
			 //echo "<script>alert('fin de cruD try que pasa');</script>";
			 return "success"; 
			 $stmt->close();						
			} 
		 catch (PDOException $e)
			{	echo "<script>alert('catch entro')</script>";
				 $stmt->rollBack(); 
				print "Error!: ".$e->getMessage()."</br>";
				return "Error!: ".$e->getMessage()."</br>";   
			}					
			//	return "success";		
		}
	#------------------------------------------	
	#BORRAR PEDIDO MESA  DELETE FROM `producto_vendido_mesa` WHERE `productos_id`=13 AND `fecha_hora`='0000-00-00 00:00:00'
	#------------------------------------
	 public static function borrarAbonosClienteModel($datosModel, $tabla)
		{ 	//echo "<script>alert('Entro CRUD ".$datosModel["productos_id"]." es')</script>";
		 	$consulta = "DELETE FROM $tabla WHERE productos_id=:id AND fecha_hora = :fecha_hora";
		 	//echo "<script>alert('Entro CRUD ".$consulta." es')</script>";	
			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->bindParam(":id", $datosModel["productos_id"], PDO::PARAM_INT);
			$stmt->bindParam(":fecha_hora", $datosModel["fecha_hora"], PDO::PARAM_STR);
			if($stmt->execute())
				{	return "success";	}
			else{	return "error";		}
			$stmt->close();
		}
	#----------------------------------------------
}