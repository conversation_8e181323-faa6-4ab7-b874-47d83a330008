
<?php 
ob_start();
class controller<PERSON><PERSON>ento extends Mvc<PERSON>ontroller
{
	#REGISTRO DE DESCUENTO
	#------------------------------------
	 public function registroDescuentoController()
		{//echo "<script>alert('Entro Controller ".$_POST["nombreDescuentoRegistro"]." no')</script>";
		 if(isset($_POST["nombreDescuentoRegistro"]))
			{	
				$datosController =array('nombre'=>$_POST["nombreDescuentoRegistro"],
										'codigo'=>$_POST["codigo"],
										'activo'=>$_POST["activo"],
										'usuario'=>$_SESSION["usuario"],
										);
				/*echo "<script>alert('Entro Controller ".$datosController['nombre']." if')</script>";*/	
				$respuesta = DatosDescuento::registroDescuentoModel($datosController, "descuento");
				if($respuesta == "success")
					{	header("location:index.php?action=okDes");	}
				else
					{	header("location:index.php");	}
			}
		}
	#------------------------------------
	#VISTA DE DESCUENTOS
	#------------------------------------
	 public function vistaDescuentoController()
		{
		 $respuesta = DatosDescuento::vistaDescuentoModel("descuento");
		 foreach($respuesta as $row => $item)
			{
				echo'<tr>						
					<td>'.$item["porcentaje"].'</td>															
					<td>'.$item["codigo"].'</td>																
					<td>'.$item["Activo"].'</td>																
					<td><a href="index.php?action=editarDescuento&id='.$item["id"].'">Editar</a> <a href="index.php?action=descuento&idBorrar='.$item["id"].'">Borrar</a></td>
				</tr>';
			}
		}
	#------------------------------------
	#EDITAR DESCUENTO
	#------------------------------------
	 public function editarDescuentoController()
		{
			$datosController = $_GET["id"];
			$respuesta = DatosDescuento::editarDescuentoModel($datosController, "descuento");
			$_SESSION["edescuento"]=$respuesta;
			echo' <table>
				<thead> 	
		     		 <tr > <td colspan="2" ></td> </tr>
	    		</thead>
				<tbody>
					<tr>
						<td>Descuento :</td>
						<td><input type="text" value="'.$respuesta["porcentaje"].'" name="nombreDescuentoEditar" required></td>
					</tr>
					<tr>
						<td>Codigo :</td>
						<td><input type="text" value="'.$respuesta["codigo"].'" name="codigo" required></td>
					</tr>
					<tr>
						<td>Activo :</td>
						<td><input type="text" value="'.$respuesta["Activo"].'" name="activo" required></td>
					</tr>
				</tbody>
				<thead> 	
		     		 <tr > <td colspan="2" ></td> </tr>
	    		</thead>
			</table>
												 
			 <input type="hidden" value="'.$respuesta["id"].'" name="idDescuentoEditar" required> 				
				 <input type="submit" value="Actualizar">';
		}
	#------------------------------------
	#ACTUALIZAR DESCUENTO
	#------------------------------------
	 public function actualizarDescuentoController()
		{//echo "<script>alert('Entro Controller Actualizar Descuento')</script>";

			if(isset($_POST["nombreDescuentoEditar"]))
				{
					$datosController = array(  "nombre"=>$_POST["nombreDescuentoEditar"],														
												"codigo"=>$_POST["codigo"],														
												"activo"=>$_POST["activo"],														
												"id"=>$_POST["idDescuentoEditar"]
											);				
					$respuesta = DatosDescuento::actualizarDescuentoModel($datosController, "descuento");
					if($respuesta == "success")
						{	header("location:index.php?action=cambioDes");	}
					else
						{	echo "error";	}
				}				
		}
	#------------------------------------
	#BORRAR DESCUENTO
	#------------------------------------
	 public function borrarDescuentoController()
		{
			if(isset($_GET["idBorrar"]))
				{
					$datosController = $_GET["idBorrar"];				
					$respuesta = DatosDescuento::borrarDescuentoModel($datosController, "descuento");
					if($respuesta == "success")
						{	header("location:index.php?action=descuento");	}
				}
		}			
	#---------------------------------

}			
	