﻿punto a tener en cuenta en la seguridad y transparencia para todo.

* El administrador no vea la contraseña del otro usuario solo pueda (crearlo, modificar dato no clave, eliminarlo)
* Que cada usuario cambie su clave
* crear campo donde se registre de que punto se toma el suministro. para poder buscarlo pq el admin no tiene sucursal definida.
* activar la propina, al vendedor le paga comisión por venta o meta.
* crear tabla de activo    (id, nombre, fecha inicial, fecha de vencimiento, id usuario q lo creo, fecha ingreso al sistema). CRUD.
* agregar la modificaciones de los ingreso y egreso en el reporte por fecha, de la sede o usuarioo??
* crear reporte de traslado de la bodega a sucursal y biseversa.
* retringir la venta del producto si no esta hecho el traslado.
* en compra suministro q actualice el suministro
* Crear los reportes de excel.
* se modifica de ingreso o egreso, añadiendo le otor campo de sucursal o punto.
* esta es opcional que agregar campos diferente a cliente y empleado como notificaciones de producto y el medio q quieren q se lo envie y q producto le interesa.
* Ordenar todo lo q sea lista debe estar en controller.  

TABLA TRASLADO NOTA (UN TRASLADO SE PUEDE MODIFICAR Y BORRAR )
 - ID
 - SUMINISTRO_ID
 - PERSONA_ID
 - FECHA TRASLADO
 - CANTIDA PASADA
 - CANTIDAD_PUNTO_INICIAL(INICIAL)
 - CANTIDAD_PUNTO_FINAL(INICIAL)
 - FECHA MODIFICACION
 
 
 
TABLA DE AUDITORIA 
 -ID
 -TABLA MODIFICADA
 -CAMPO MODIFICADO
 -FECHA
 -USUARIO

889705444858

46863823110


