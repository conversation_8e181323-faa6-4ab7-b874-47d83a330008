<?php

session_start();

header('Content-Type: application/json');

try {
    // Verificar sesión
    if (!isset($_SESSION["validar"]) || !$_SESSION["validar"]) {
        echo json_encode(['error' => 'No autorizado', 'session' => false]);
        exit();
    }
    
    // Obtener datos
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    // Respuesta simple
    echo json_encode([
        'success' => true,
        'message' => 'AJAX mínimo funciona',
        'timestamp' => date('Y-m-d H:i:s'),
        'session_user' => $_SESSION['persona'] ?? 'No definido',
        'session_id' => $_SESSION['usuario'] ?? 'No definido',
        'data_received' => $data
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}

?>
