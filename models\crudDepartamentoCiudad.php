<?php

	#EXTENSIÓN DE CLASES: Los objetos pueden ser extendidos, y pueden heredar propiedades y métodos. Para definir una clase como extensión, debo definir una clase padre, y se utiliza dentro de una clase hija.

	require_once "conexion.php";

	class DatosDepartamentoCiudad extends Conexion
		{


			#DEPARTAMENTO
			#----------------------------------------------
				#REGISTRO DE DEPARTAMENTOS
				#-------------------------------------
					public static function registroDepartamentoModel($datosModel, $tabla){
								echo "<script>alert('Entro CRUD ".$datosModel["nombre"]." si')</script>";	

						$consulta="INSERT INTO $tabla (nombre) VALUES (:nombre)";
						//echo "<script>alert('Entro CRUD ".$consulta." no')</script>";	

						$stmt = Conexion::conectar()->prepare($consulta);
						$stmt->execute();
						
						$stmt->bindParam(":nombre", $datosModel["nombre"], PDO::PARAM_STR);
						echo "<script>alert('Guardo')</script>";			

						if($stmt->execute()){
							return "success";
						}
						else{ return "error";			}
						$stmt->close();
					}

				#VISTA DEPARTAMENTOS
				#-------------------------------------
					public static function vistaDepartamentoModel($tabla)
						{
							$stmt = Conexion::conectar()->prepare("SELECT id, nombre FROM $tabla");	
							$stmt->execute();
							#fetchAll(): Obtiene todas las filas de un conjunto de resultados asociado al objeto PDOStatement. 
							return $stmt->fetchAll();
							$stmt->close();
						}

				#EDITAR DEPARTAMENTOS
				#-------------------------------------

					public static function editarDepartamentoModel($datosModel, $tabla){
						$stmt = Conexion::conectar()->prepare("SELECT id, nombre FROM $tabla WHERE id = :id");
						$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);	
						$stmt->execute();
						return $stmt->fetch();
						$stmt->close();
					}

				#ACTUALIZAR DEPARTAMENTOS
				#-------------------------------------
					public static function actualizarDepartamentoModel($datosModel, $tabla)
						{
							$stmt = Conexion::conectar()->prepare("UPDATE $tabla SET nombre = :nombre WHERE id = :id");			
							$stmt->bindParam(":nombre", $datosModel["nombre"], PDO::PARAM_STR);			
							$stmt->bindParam(":id", $datosModel["id"], PDO::PARAM_INT);			

							if($stmt->execute())
								{	return "success";	}

							else{	return "error";		}
							$stmt->close();
						}

				#BORRAR DEPARTAMENTOS
				#------------------------------------
					public static function borrarDepartamentoModel($datosModel, $tabla)
						{
							$stmt = Conexion::conectar()->prepare("DELETE FROM $tabla WHERE id = :id");
							$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);
							if($stmt->execute())
								{	return "success";	}
							else{			return "error";		}
							$stmt->close();
						}
			#----------------------------------------------	
			
			##CIUDAD
			#----------------------------------------------
				#REGISTRO DE CIUDAD
				#-------------------------------------
					public static function registroCiudadModel($datosModel, $tabla)
						{
							echo "<script>alert('Entro CRUD  no')</script>";	
							$consulta="INSERT INTO $tabla (departamento_id, nombre) VALUES (:IdDpartamento,:nombre)";
							//echo "<script>alert('Entro CRUD ".$consulta." no')</script>";	".$datosModel['nombre']."
							$stmt = Conexion::conectar()->prepare($consulta);
							//$stmt->execute();			
							$stmt->bindParam(":IdDpartamento", $datosModel['departamento_id'], PDO::PARAM_INT);
							$stmt->bindParam(":nombre", $datosModel['nombre'], PDO::PARAM_STR);
							echo "<script>alert('Guardo')</script>";			

							if($stmt->execute())
								{	return "success";	}
							else{ return "error";	}
							$stmt->close();
						}

				#VISTA CIUDAD
				#-------------------------------------

					public static function vistaCiudadModel($tabla)
						{
							$stmt = Conexion::conectar()->prepare("SELECT id, departamento_id, nombre FROM $tabla");	
							$stmt->execute();
							
							return $stmt->fetchAll();
							$stmt->close();
						}

				#EDITAR CIUDAD
				#-------------------------------------

					public static function editarCiudadModel($datosModel, $tabla)
						{
							$stmt = Conexion::conectar()->prepare("SELECT id, departamento_id, nombre FROM $tabla WHERE id = :id");
							$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);	
							$stmt->execute();
							return $stmt->fetch();
							$stmt->close();
						}

				#ACTUALIZAR CIUDAD
				#-------------------------------------

					public static function actualizarCiudadModel($datosModel, $tabla)
						{ echo "<script>alert('Entro Actualizar Producto')</script>";	
							$stmt = Conexion::conectar()->prepare("UPDATE $tabla SET departamento_id =:departamento_id, nombre = :nombre WHERE id = :id");			
							$stmt->bindParam(":departamento_id", $datosModel["departamento_id"], PDO::PARAM_INT);
							$stmt->bindParam(":nombre", $datosModel["nombre"], PDO::PARAM_STR);
							$stmt->bindParam(":id", $datosModel["id"], PDO::PARAM_INT);
							if($stmt->execute())
								{echo "<script>alert('Guardo Actualizar Producto')</script>";return "success";	}
							else{	return "error";			}
							$stmt->close();
						}

				#BORRAR CIUDAD
				#------------------------------------
					public static function borrarCiudadModel($datosModel, $tabla)
					{
						$stmt = Conexion::conectar()->prepare("DELETE FROM $tabla WHERE id = :id");
						$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);
						if($stmt->execute())
							{	return "success";	}
						else{	return "error";		}
						$stmt->close();
					}
			#----------------------------------------------
			#
		}