<script type="text/javascript">
	function buscarVenta(inputString)
	  {
		alert("Entro a buscarVenta");
				//alert("Entro en el else de placa");
			$("#destino").load("views/modules/ajaxVentas.php", {fecha1: $("#fecha1").val(),fecha2: $("#fecha2").val()}, function(){
	          //alert("recibidos los datos por ajax");
	 		 });
	  } // placa
	function aMayusculas(obj,id)
	 {
	    obj = obj.toUpperCase();
	    document.getElementById(id).value = obj;
	 }
</script>

<h1>Buscar Ventas por fecha</h1>
<form method="post">.
	<label for=""><b>Fecha Inicio</b></label>
	<input type="datetime-local" placeholder="aa-mm-dd 00:00 a.m." name="fecha1" id="fecha1" autocomplete="on" style="width:200px;" autofocus="autofocus" required>
	<!--input type="date" placeholder="aa-mm-dd"  name="fecha1" id="fecha1" style="width:140px;" autofocus="autofocus" required-->
	<label for=""><b>Fecha Final</b></label>
	<input type="datetime-local" placeholder="aa-mm-dd 00:00 a.m."  name="fecha2" id="fecha2" style="width:240px;"  required><br>
	<input type="button" name="enviar" id="enviar" value="Enviar" onclick="buscarVenta(this.value);">
	<input type="submit" name="enviar" id="enviar" value="Generar Excel" "><br>
</form>
<span id="destino" > </span>
<?php
$reporteExcel = new controllerVistaVentas();
$reporteExcel -> reporteExcelController();

?>
	</table>

<?php

if(isset($_GET["action"]))
	{	if($_GET["action"] == "cambio")
			{	echo "Cambio Exitoso";	}
	}

?>




