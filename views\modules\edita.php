t<?php ob_start();
#session_start();


if(!$_SESSION["validar"]){

	header("location:ingresar");

	exit();

}

?>

<h1>EDITAR USUARIO</h1>


<form method="post">
	
	<?php

	$editarUsuario = new ControllerUsuario();
	$respuesta=$editarUsuario -> editarUsuarioController();
	$editarUsuario -> actualizarUsuarioController();
	$buscarPersona=$editarUsuario->buscarTipoPersonaController($respuesta["tipo_usuario"],"tipo_persona");

	        $result='<select name="tipo_persona"  id="tipo_persona">';
		 	$result.=' <option value="-1">Seleccione un tipo de usuario</option>';

		 	foreach ($buscarPersona as $row => $item) {

		 		if ($item[1]==$respuesta["tipo_usuario"]) {
		 			$result.=' <option value="'.$item["id"].'" selected>'.$item[1].'</option>';
		 		}else{
		 			$result.=' <option value="'.$item["id"].'">'.$item[1].'</option>';
		 		}

		 	}
		 $result.='</select>';

	echo $result;
	echo'<input type="hidden" value="'.$respuesta["id"].'" name="idEditar">
						 <input type="text" value="'.$respuesta["usuario"].'" name="usuarioEditar" required>
						 <input type="text" value="'.$respuesta["nombres"].'" name="nombredEditar" required>
						 <input type="text" value="'.$respuesta["apellidos"].'" name="apellidoEditar" required>
						 <input type="text" value="'.$respuesta["cedula"].'" name="cedulaEditar" required>
						 <input type="text" value="'.$respuesta["celular"].'" name="celularEditar" required>
						 <input type="text" value="'.$respuesta["pass"].'" name="passwordEditar" required>
						 <input type="submit" value="Actualizar">';

	ob_flush();
	?>


</form>

</form>



