const express = require('express');
const net = require('net');
const cors = require('cors');

const app = express();
const PORT = 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.text());

// Configuración de impresoras
const IMPRESORAS = {
    'bar': { ip: '**************', puerto: 9100 },
    'cocina': { ip: '**************', puerto: 9100 },
    'asados': { ip: '**************', puerto: 9100 }
};

// Función para imprimir
function imprimirEnImpresora(impresora, contenido) {
    return new Promise((resolve, reject) => {
        const config = IMPRESORAS[impresora.toLowerCase()];
        
        if (!config) {
            reject(new Error(`Impresora '${impresora}' no configurada`));
            return;
        }

        console.log(`📡 Conectando a ${impresora}: ${config.ip}:${config.puerto}`);
        
        const socket = new net.Socket();
        
        socket.setTimeout(10000); // 10 segundos timeout
        
        socket.connect(config.puerto, config.ip, () => {
            console.log(`✅ Conectado a ${impresora}`);
            
            // Comandos ESC/POS para impresora térmica
            let datos = '\x1B\x40'; // Reset
            datos += '\x1B\x61\x01'; // Centrar
            datos += '=== MACARENA RESTAURANT ===\n';
            datos += '\x1B\x61\x00'; // Alinear izquierda
            datos += `Impresora: ${impresora.toUpperCase()}\n`;
            datos += `Fecha: ${new Date().toLocaleString()}\n`;
            datos += '--------------------------------\n';
            datos += contenido;
            datos += '\n--------------------------------\n\n\n';
            datos += '\x1D\x56\x42\x00'; // Cortar papel
            
            socket.write(datos);
            
            setTimeout(() => {
                socket.end();
                console.log(`✅ Impresión enviada a ${impresora}`);
                resolve({ success: true, mensaje: `Impreso en ${impresora}` });
            }, 1000);
        });
        
        socket.on('error', (error) => {
            console.log(`❌ Error conectando a ${impresora}: ${error.message}`);
            reject(error);
        });
        
        socket.on('timeout', () => {
            console.log(`⏰ Timeout conectando a ${impresora}`);
            socket.destroy();
            reject(new Error('Timeout de conexión'));
        });
    });
}

// Ruta principal de impresión
app.post('/imprimir', async (req, res) => {
    try {
        const { impresora, contenido, categoria } = req.body;
        
        console.log(`\n🖨️ Solicitud de impresión recibida:`);
        console.log(`   Impresora: ${impresora}`);
        console.log(`   Categoría: ${categoria || 'N/A'}`);
        console.log(`   Contenido: ${contenido ? contenido.substring(0, 50) + '...' : 'Vacío'}`);
        
        if (!impresora || !contenido) {
            return res.status(400).json({
                success: false,
                error: 'Faltan parámetros: impresora y contenido son requeridos'
            });
        }
        
        const resultado = await imprimirEnImpresora(impresora, contenido);
        
        res.json({
            success: true,
            mensaje: resultado.mensaje,
            timestamp: new Date().toISOString(),
            impresora: impresora,
            categoria: categoria
        });
        
    } catch (error) {
        console.log(`❌ Error en impresión: ${error.message}`);
        
        res.status(500).json({
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// Ruta de test
app.get('/test', (req, res) => {
    res.json({
        mensaje: 'Proxy de impresión funcionando',
        timestamp: new Date().toISOString(),
        impresoras_configuradas: Object.keys(IMPRESORAS),
        puerto: PORT
    });
});

// Ruta para test de conectividad
app.get('/test-conectividad', async (req, res) => {
    const resultados = {};
    
    for (const [nombre, config] of Object.entries(IMPRESORAS)) {
        try {
            await new Promise((resolve, reject) => {
                const socket = new net.Socket();
                socket.setTimeout(5000);
                
                socket.connect(config.puerto, config.ip, () => {
                    socket.end();
                    resolve();
                });
                
                socket.on('error', reject);
                socket.on('timeout', () => reject(new Error('Timeout')));
            });
            
            resultados[nombre] = { 
                status: 'online', 
                ip: config.ip, 
                puerto: config.puerto 
            };
            
        } catch (error) {
            resultados[nombre] = { 
                status: 'offline', 
                ip: config.ip, 
                puerto: config.puerto, 
                error: error.message 
            };
        }
    }
    
    res.json({
        timestamp: new Date().toISOString(),
        resultados: resultados
    });
});

// Iniciar servidor
app.listen(PORT, '0.0.0.0', () => {
    console.log(`\n🚀 Proxy de Impresión Macarena iniciado`);
    console.log(`📡 Escuchando en puerto ${PORT}`);
    console.log(`🖨️ Impresoras configuradas:`);
    
    Object.entries(IMPRESORAS).forEach(([nombre, config]) => {
        console.log(`   ${nombre}: ${config.ip}:${config.puerto}`);
    });
    
    console.log(`\n🔗 URLs disponibles:`);
    console.log(`   Test: http://localhost:${PORT}/test`);
    console.log(`   Conectividad: http://localhost:${PORT}/test-conectividad`);
    console.log(`   Imprimir: POST http://localhost:${PORT}/imprimir`);
    console.log(`\n✅ Proxy listo para recibir solicitudes de impresión\n`);
});

// Manejo de errores
process.on('uncaughtException', (error) => {
    console.log('❌ Error no capturado:', error.message);
});

process.on('unhandledRejection', (reason, promise) => {
    console.log('❌ Promesa rechazada:', reason);
});
