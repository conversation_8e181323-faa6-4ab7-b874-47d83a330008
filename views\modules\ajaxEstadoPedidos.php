<?php
// Archivo AJAX para operaciones de estado de pedidos
// Este archivo maneja las peticiones AJAX desde la pantalla de cocina

// Activar reporte de errores para debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Iniciar sesión si no está iniciada
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Verificar que las clases necesarias estén disponibles
if (!class_exists('ControllerEstadoPedidos')) {
    // Intentar cargar desde diferentes rutas posibles
    $possible_paths = [
        "../../",  // Desde views/modules/
        "../../../", // Desde views/modules/ con un nivel más
        "", // Desde raíz
        "./" // Directorio actual
    ];

    $loaded = false;
    foreach ($possible_paths as $path) {
        if (file_exists($path . "models/conexion.php")) {
            error_log("AJAX: Cargando archivos desde ruta: " . $path);
            require_once $path . "models/conexion.php";
            require_once $path . "models/crudEstadoPedidos.php";
            require_once $path . "controllers/controllerEstadoPedidos.php";
            $loaded = true;
            break;
        }
    }

    if (!$loaded) {
        error_log("AJAX: No se pudieron cargar las clases necesarias");
        echo json_encode(array('status' => 'error', 'message' => 'No se pudieron cargar las clases necesarias'));
        exit;
    }
} else {
    error_log("AJAX: Clase ControllerEstadoPedidos ya disponible");
}

// Verificar que el usuario tenga permisos (más flexible para testing)
// Solo verificar permisos para acciones que modifican datos
$accionesQueRequierenPermisos = ['enviar_pedido', 'entregar_pedido', 'reimprimir_pedido', 'cancelar_pedido'];
$accionActual = '';

foreach ($accionesQueRequierenPermisos as $accion) {
    if (isset($_POST[$accion])) {
        $accionActual = $accion;
        break;
    }
}

// Solo verificar permisos para acciones que modifican datos
if ($accionActual && !isset($_SESSION["tipo_usuario"]) && !isset($_SESSION["usuario"])) {
    echo json_encode(array('status' => 'error', 'message' => 'No tiene permisos para realizar esta acción: ' . $accionActual));
    exit;
}

/*=============================================
ENVIAR PEDIDO A COCINA
=============================================*/
if(isset($_POST['enviar_pedido'])) {

    try {
        // Log para debugging
        error_log("AJAX: Iniciando envío de pedido");

        $pedidoId = $_POST['pedido_id'];

        if (empty($pedidoId)) {
            error_log("AJAX: Error - ID de pedido vacío");
            echo json_encode(array('status' => 'error', 'message' => 'ID de pedido requerido'));
            exit;
        }

        error_log("AJAX: Creando controller para pedido ID: " . $pedidoId);
        $controller = new ControllerEstadoPedidos();

        error_log("AJAX: Llamando enviarPedidoController");
        $resultado = $controller->enviarPedidoController($pedidoId);

        error_log("AJAX: Resultado del controller: " . print_r($resultado, true));

        if ($resultado) {
            echo json_encode($resultado);
        } else {
            echo json_encode(array('status' => 'error', 'message' => 'Error al enviar pedido'));
        }

    } catch (Exception $e) {
        error_log("AJAX: Excepción capturada: " . $e->getMessage());
        echo json_encode(array('status' => 'error', 'message' => 'Error: ' . $e->getMessage()));
    }
    exit;
}

/*=============================================
MARCAR PEDIDO COMO ENTREGADO
=============================================*/
if(isset($_POST['entregar_pedido'])) {

    try {
        $pedidoId = $_POST['pedido_id'];

        if (empty($pedidoId)) {
            echo json_encode(array('status' => 'error', 'message' => 'ID de pedido requerido'));
            exit;
        }

        $controller = new ControllerEstadoPedidos();
        $resultado = $controller->entregarPedidoController($pedidoId);

        if ($resultado) {
            echo json_encode($resultado);
        } else {
            echo json_encode(array('status' => 'error', 'message' => 'Error al entregar pedido'));
        }

    } catch (Exception $e) {
        echo json_encode(array('status' => 'error', 'message' => 'Error: ' . $e->getMessage()));
    }
    exit;
}

/*=============================================
OBTENER PEDIDOS DE UNA MESA
=============================================*/
if(isset($_POST['obtener_pedidos_mesa'])) {

    try {
        $mesaId = $_POST['mesa_id'];
        $estado = isset($_POST['estado']) ? $_POST['estado'] : null;

        error_log("AJAX: Obteniendo pedidos para mesa $mesaId, estado: " . ($estado ?? 'todos'));

        // Usar la misma consulta exacta que funciona en debug
        $db = Conexion::conectar();

        error_log("AJAX: Ejecutando consulta para mesa_id: " . $mesaId);

        // RESTAURADO: Solo mostrar pedidos borrador para que la mesa se "desocupe" después del envío
        $sql = "SELECT * FROM pedidos WHERE mesa_id = ? AND estado = 'borrador' ORDER BY fecha_pedido DESC";
        $stmt = $db->prepare($sql);
        $stmt->execute([$mesaId]);
        $pedidosBasicos = $stmt->fetchAll(PDO::FETCH_ASSOC);

        error_log("AJAX: Pedidos básicos encontrados: " . count($pedidosBasicos));

        // Transformar a formato esperado por el frontend
        $pedidos = [];
        foreach ($pedidosBasicos as $pedido) {
            // Obtener conteo de productos
            $stmtProductos = $db->prepare("SELECT COUNT(*) as total FROM producto_vendido_mesa WHERE pedidos_id = ?");
            $stmtProductos->execute([$pedido['id']]);
            $totalProductos = $stmtProductos->fetch(PDO::FETCH_ASSOC)['total'];

            $pedidos[] = [
                'id' => $pedido['id'],
                'numero_pedido' => $pedido['numero_pedido'],
                'estado' => $pedido['estado'],
                'fecha_pedido' => $pedido['fecha_pedido'],
                'fecha_envio' => $pedido['fecha_envio'],
                'fecha_entrega' => $pedido['fecha_entrega'],
                'mesa_numero' => $mesaId, // Usar el ID de mesa directamente
                'mesero_nombre' => 'Sin asignar',
                'total_productos' => $totalProductos
            ];
        }

        error_log("AJAX: Pedidos encontrados: " . count($pedidos));
        error_log("AJAX: Datos: " . print_r($pedidos, true));

        echo json_encode($pedidos);

    } catch (Exception $e) {
        error_log("AJAX: Error obteniendo pedidos: " . $e->getMessage());
        echo json_encode(array('error' => $e->getMessage()));
    }
    exit;
}

/*=============================================
OBTENER PEDIDO BORRADOR ACTUAL
=============================================*/
if(isset($_POST['obtener_pedido_borrador'])) {
    
    $mesaId = $_POST['mesa_id'];
    
    $controller = new ControllerEstadoPedidos();
    $pedido = $controller->obtenerPedidoBorradorController($mesaId);
    
    echo json_encode($pedido);
    exit;
}

/*=============================================
OBTENER PRODUCTOS DEL PEDIDO
=============================================*/
if(isset($_POST['obtener_productos_pedido'])) {
    
    $pedidoId = $_POST['pedido_id'];
    
    $controller = new ControllerEstadoPedidos();
    $productos = $controller->obtenerProductosPedidoController($pedidoId);
    
    echo json_encode($productos);
    exit;
}

/*=============================================
VERIFICAR PERMISOS DE MODIFICACION
=============================================*/
if(isset($_POST['verificar_permisos'])) {
    
    $pedidoId = $_POST['pedido_id'];
    
    $controller = new ControllerEstadoPedidos();
    $puedeModificar = $controller->puedeModificarPedidoController($pedidoId);
    
    echo json_encode(array('puede_modificar' => $puedeModificar));
    exit;
}

/*=============================================
REIMPRIMIR PEDIDO
=============================================*/
if(isset($_POST['reimprimir_pedido'])) {
    
    $pedidoId = $_POST['pedido_id'];
    $categoria = isset($_POST['categoria']) ? $_POST['categoria'] : null;
    $motivo = isset($_POST['motivo']) ? $_POST['motivo'] : 'Reimpresión solicitada';
    
    $controller = new ControllerEstadoPedidos();
    $resultado = $controller->reimprimirPedidoController($pedidoId, $categoria, $motivo);
    
    echo json_encode($resultado);
    exit;
}

/*=============================================
CANCELAR PEDIDO
=============================================*/
if(isset($_POST['cancelar_pedido'])) {
    
    $pedidoId = $_POST['pedido_id'];
    $motivo = isset($_POST['motivo']) ? $_POST['motivo'] : '';
    
    $controller = new ControllerEstadoPedidos();
    $resultado = $controller->cancelarPedidoController($pedidoId, $motivo);
    
    echo json_encode($resultado);
    exit;
}

/*=============================================
OBTENER HISTORIAL DEL PEDIDO
=============================================*/
if(isset($_POST['obtener_historial'])) {
    
    $pedidoId = $_POST['pedido_id'];
    
    $controller = new ControllerEstadoPedidos();
    $historial = $controller->obtenerHistorialPedidoController($pedidoId);
    
    echo json_encode($historial);
    exit;
}

/*=============================================
OBTENER PEDIDOS PENDIENTES POR CATEGORIA
=============================================*/
if(isset($_POST['obtener_pendientes_categoria'])) {
    
    $categoria = $_POST['categoria'];
    
    $controller = new ControllerEstadoPedidos();
    $pedidos = $controller->obtenerPedidosPendientesCategoriaController($categoria);
    
    echo json_encode($pedidos);
    exit;
}

/*=============================================
OBTENER PEDIDOS PARA FACTURAR (ENVIADOS/ENTREGADOS)
=============================================*/
if(isset($_POST['obtener_pedidos_facturar'])) {

    try {
        $mesaId = $_POST['mesa_id'];

        error_log("AJAX: Obteniendo pedidos para facturar en mesa $mesaId");

        $db = Conexion::conectar();

        // Obtener pedidos enviados y entregados para facturar
        $sql = "SELECT * FROM pedidos WHERE mesa_id = ? AND estado IN ('enviado', 'entregado') ORDER BY fecha_pedido DESC";
        $stmt = $db->prepare($sql);
        $stmt->bindParam(1, $mesaId, PDO::PARAM_INT);
        $stmt->execute();
        $pedidos = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Agregar conteo de productos a cada pedido
        foreach ($pedidos as &$pedido) {
            $sqlProductos = "SELECT COUNT(*) as total FROM producto_vendido_mesa WHERE pedidos_id = ?";
            $stmtProductos = $db->prepare($sqlProductos);
            $stmtProductos->bindParam(1, $pedido['id'], PDO::PARAM_INT);
            $stmtProductos->execute();
            $resultado = $stmtProductos->fetch(PDO::FETCH_ASSOC);
            $pedido['total_productos'] = $resultado['total'];
        }

        error_log("AJAX: Encontrados " . count($pedidos) . " pedidos para facturar");

        echo json_encode($pedidos);

    } catch (Exception $e) {
        error_log("AJAX ERROR: " . $e->getMessage());
        echo json_encode(array(
            'status' => 'error',
            'message' => 'Error obteniendo pedidos para facturar: ' . $e->getMessage()
        ));
    }
    exit;
}

// Si no se especifica ninguna acción válida
echo json_encode(array(
    'status' => 'error',
    'message' => 'Acción no válida',
    'available_actions' => [
        'enviar_pedido',
        'entregar_pedido',
        'obtener_pedidos_mesa',
        'obtener_pedidos_facturar',
        'obtener_pedido_borrador',
        'obtener_productos_pedido',
        'verificar_permisos',
        'reimprimir_pedido',
        'cancelar_pedido',
        'obtener_historial',
        'obtener_pendientes_categoria'
    ]
));
?>
