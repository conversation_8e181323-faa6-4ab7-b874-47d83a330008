<?php
// Configuración de Proxy para Impresoras
echo "<h1>🔧 Configurar Proxy para Impresoras</h1>";
echo "<p><strong>Problema identificado:</strong> El servidor web no está en la misma red que las impresoras</p>";

echo "<div style='background-color: #d1ecf1; padding: 15px; border-left: 4px solid #bee5eb; margin: 20px 0;'>";
echo "<h3>📋 Situación Actual:</h3>";
echo "<ul>";
echo "<li>✅ <strong>Portátil/Celular:</strong> Red local 192.168.68.x (pueden acceder a impresoras)</li>";
echo "<li>❌ <strong>Servidor web:</strong> Red externa (no puede acceder a impresoras)</li>";
echo "<li>🖨️ <strong>Impresoras:</strong> Red local **************, 121, 122</li>";
echo "</ul>";
echo "</div>";

echo "<h2>💡 Soluciones Disponibles:</h2>";

// Solución 1: Proxy HTTP
echo "<div style='border: 1px solid #007bff; padding: 20px; margin: 15px 0; border-radius: 8px;'>";
echo "<h3>🌐 Solución 1: Proxy HTTP en tu Portátil</h3>";
echo "<p><strong>Concepto:</strong> Tu portátil actúa como intermediario entre el servidor y las impresoras</p>";

echo "<h4>📝 Pasos a seguir:</h4>";
echo "<ol>";
echo "<li><strong>Instalar proxy en tu portátil</strong> (Node.js, Python, o PHP)</li>";
echo "<li><strong>Configurar rutas:</strong></li>";
echo "<ul>";
echo "<li><code>http://tu-ip-publica:3000/cocina</code> → **************:9100</li>";
echo "<li><code>http://tu-ip-publica:3000/bar</code> → **************:9100</li>";
echo "<li><code>http://tu-ip-publica:3000/asados</code> → **************:9100</li>";
echo "</ul>";
echo "<li><strong>Modificar Macarena</strong> para usar estas URLs</li>";
echo "</ol>";

echo "<h4>🔧 Código del Proxy (Node.js):</h4>";
echo "<pre style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto;'>";
echo htmlspecialchars('
const express = require("express");
const net = require("net");
const app = express();

// Middleware para recibir datos raw
app.use(express.raw({ type: "*/*", limit: "10mb" }));

// Proxy para COCINA
app.all("/cocina", (req, res) => {
    proxyToPrinter("**************", 9100, req, res);
});

// Proxy para BAR  
app.all("/bar", (req, res) => {
    proxyToPrinter("**************", 9100, req, res);
});

// Proxy para ASADOS
app.all("/asados", (req, res) => {
    proxyToPrinter("**************", 9100, req, res);
});

function proxyToPrinter(ip, port, req, res) {
    const client = new net.Socket();
    
    client.connect(port, ip, () => {
        console.log(`Conectado a ${ip}:${port}`);
        if (req.body && req.body.length > 0) {
            client.write(req.body);
        }
    });
    
    client.on("data", (data) => {
        res.write(data);
    });
    
    client.on("close", () => {
        res.end();
    });
    
    client.on("error", (err) => {
        res.status(500).send(`Error: ${err.message}`);
    });
}

app.listen(3000, "0.0.0.0", () => {
    console.log("Proxy de impresoras ejecutándose en puerto 3000");
});
');
echo "</pre>";
echo "</div>";

// Solución 2: Modificar configuración
echo "<div style='border: 1px solid #28a745; padding: 20px; margin: 15px 0; border-radius: 8px;'>";
echo "<h3>⚙️ Solución 2: Modificar Configuración de Macarena</h3>";
echo "<p>Cambiar las IPs de las impresoras para usar tu portátil como proxy</p>";

echo "<h4>📋 Configuración actual en la base de datos:</h4>";
try {
    require_once '../../models/conexion.php';
    $conexion = new Conexion();
    $pdo = $conexion->conectar();
    
    $stmt = $pdo->query("SELECT * FROM impresoras ORDER BY nombre");
    $impresoras = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($impresoras) {
        echo "<table style='width: 100%; border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr style='background-color: #f8f9fa;'>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Nombre</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>IP Actual</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Puerto</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Estado</th>";
        echo "</tr>";
        
        foreach ($impresoras as $imp) {
            echo "<tr>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$imp['nombre']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$imp['ip']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$imp['puerto']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . ($imp['activa'] ? '✅ Activa' : '❌ Inactiva') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p>⚠️ Error al consultar impresoras: " . $e->getMessage() . "</p>";
}

echo "<h4>🔄 Nueva configuración necesaria:</h4>";
echo "<p>Necesitamos tu <strong>IP pública</strong> o <strong>dominio</strong> para configurar el proxy:</p>";

echo "<form method='POST' style='background-color: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<h5>📡 Configurar Proxy:</h5>";
echo "<label>IP Pública o Dominio de tu portátil:</label><br>";
echo "<input type='text' name='proxy_host' placeholder='ej: ************* o midominio.com' style='width: 300px; padding: 5px; margin: 5px 0;'><br>";
echo "<label>Puerto del proxy:</label><br>";
echo "<input type='number' name='proxy_port' value='3000' style='width: 100px; padding: 5px; margin: 5px 0;'><br>";
echo "<button type='submit' name='configurar_proxy' style='background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin-top: 10px;'>🔧 Configurar Proxy</button>";
echo "</form>";

// Procesar configuración del proxy
if (isset($_POST['configurar_proxy'])) {
    $proxy_host = $_POST['proxy_host'];
    $proxy_port = $_POST['proxy_port'];
    
    if (!empty($proxy_host)) {
        echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0;'>";
        echo "<h4>✅ Configuración del Proxy:</h4>";
        echo "<p>Actualizar las IPs en la base de datos:</p>";
        echo "<pre style='background-color: #f8f9fa; padding: 10px; border-radius: 3px;'>";
        echo "UPDATE impresoras SET \n";
        echo "  ip = '$proxy_host', \n";
        echo "  puerto = '$proxy_port' \n";
        echo "WHERE nombre = 'COCINA';\n\n";
        echo "-- O usar rutas específicas:\n";
        echo "UPDATE impresoras SET ip = '$proxy_host/cocina' WHERE nombre = 'COCINA';\n";
        echo "UPDATE impresoras SET ip = '$proxy_host/bar' WHERE nombre = 'BAR';\n";
        echo "UPDATE impresoras SET ip = '$proxy_host/asados' WHERE nombre = 'ASADOS';";
        echo "</pre>";
        echo "</div>";
    }
}

echo "</div>";

// Solución 3: VPN
echo "<div style='border: 1px solid #ffc107; padding: 20px; margin: 15px 0; border-radius: 8px;'>";
echo "<h3>🔐 Solución 3: VPN/Túnel</h3>";
echo "<p>Conectar el servidor a la red local mediante VPN</p>";
echo "<ul>";
echo "<li><strong>OpenVPN:</strong> Crear servidor VPN en tu red local</li>";
echo "<li><strong>Ngrok:</strong> Túnel reverso (más complejo para impresoras)</li>";
echo "<li><strong>TeamViewer VPN:</strong> Solución comercial</li>";
echo "</ul>";
echo "</div>";

// Recomendación
echo "<div style='background-color: #fff3cd; padding: 20px; border-left: 4px solid #ffc107; margin: 20px 0;'>";
echo "<h3>💡 Recomendación:</h3>";
echo "<p><strong>Solución más rápida:</strong> Proxy HTTP (Solución 1)</p>";
echo "<ol>";
echo "<li>Instalar Node.js en tu portátil</li>";
echo "<li>Ejecutar el código del proxy</li>";
echo "<li>Obtener tu IP pública</li>";
echo "<li>Actualizar la configuración de impresoras en Macarena</li>";
echo "</ol>";
echo "<p><strong>⚠️ Importante:</strong> Tu portátil debe estar encendido y conectado para que funcione</p>";
echo "</div>";

// Botones de navegación
echo "<p>";
echo "<a href='test_red_servidor.php' style='background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>← Test Red Servidor</a>";
echo "<a href='actualizar_impresoras.php' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔧 Actualizar Impresoras</a>";
echo "</p>";
?>
