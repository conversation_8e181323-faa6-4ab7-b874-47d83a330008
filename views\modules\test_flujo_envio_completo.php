<?php
// Test completo del flujo de envío de pedidos
session_start();
if (!isset($_SESSION["usuario"])) {
    $_SESSION["usuario"] = 1;
    $_SESSION["tipo_usuario"] = 1;
    $_SESSION["perfil"] = "administrador";
}

require_once "../../models/conexion.php";
require_once "../../controllers/controllerEstadoPedidos.php";
require_once "../../models/crudEstadoPedidos.php";

$mesaId = isset($_GET['mesa']) ? $_GET['mesa'] : 1;

echo "<h1>🧪 Test Flujo Completo de Envío - Mesa $mesaId</h1>";

try {
    $controller = new ControllerEstadoPedidos();
    
    echo "<h3>1. 📋 Estado Inicial de la Mesa</h3>";
    $pedidosIniciales = $controller->obtenerPedidosMesaController($mesaId);
    echo "Pedidos encontrados: " . count($pedidosIniciales) . "<br>";
    
    if (!empty($pedidosIniciales)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Número</th><th>Estado</th><th>Productos</th></tr>";
        foreach ($pedidosIniciales as $p) {
            echo "<tr>";
            echo "<td>{$p['id']}</td>";
            echo "<td>{$p['numero_pedido']}</td>";
            echo "<td>{$p['estado']}</td>";
            echo "<td>{$p['total_productos']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>2. 🔍 Buscar Pedido Borrador</h3>";
    $pedidoBorrador = $controller->obtenerPedidoBorradorController($mesaId);
    
    if ($pedidoBorrador) {
        echo "✅ Pedido borrador encontrado:<br>";
        echo "- ID: {$pedidoBorrador['id']}<br>";
        echo "- Número: {$pedidoBorrador['numero_pedido']}<br>";
        echo "- Estado: {$pedidoBorrador['estado']}<br>";
        
        // Verificar si tiene productos
        $productos = DatosEstadoPedidos::obtenerProductosPedidoModel($pedidoBorrador['id']);
        echo "- Productos: " . count($productos) . "<br>";
        
        if (count($productos) > 0) {
            echo "<h3>3. 📤 Enviar Pedido a Cocina</h3>";
            $resultado = $controller->enviarPedidoController($pedidoBorrador['id']);
            
            if ($resultado['status'] == 'success') {
                echo "✅ Pedido enviado exitosamente: {$resultado['message']}<br>";
                
                echo "<h3>4. 🔄 Verificar Estado Después del Envío</h3>";
                
                // Verificar que el pedido cambió de estado
                $pedidoEnviado = DatosEstadoPedidos::obtenerPedidoPorIdModel($pedidoBorrador['id']);
                echo "Estado del pedido enviado: {$pedidoEnviado['estado']}<br>";
                
                // Verificar que se creó un nuevo pedido borrador
                $nuevoBorrador = $controller->obtenerPedidoBorradorController($mesaId);
                if ($nuevoBorrador && $nuevoBorrador['id'] != $pedidoBorrador['id']) {
                    echo "✅ Nuevo pedido borrador creado automáticamente:<br>";
                    echo "- ID: {$nuevoBorrador['id']}<br>";
                    echo "- Número: {$nuevoBorrador['numero_pedido']}<br>";
                    echo "- Estado: {$nuevoBorrador['estado']}<br>";
                } else {
                    echo "❌ No se creó un nuevo pedido borrador automáticamente<br>";
                }
                
                echo "<h3>5. 📊 Estado Final de la Mesa</h3>";
                $pedidosFinales = $controller->obtenerPedidosMesaController($mesaId);
                echo "Pedidos encontrados: " . count($pedidosFinales) . "<br>";
                
                if (!empty($pedidosFinales)) {
                    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
                    echo "<tr><th>ID</th><th>Número</th><th>Estado</th><th>Productos</th></tr>";
                    foreach ($pedidosFinales as $p) {
                        echo "<tr>";
                        echo "<td>{$p['id']}</td>";
                        echo "<td>{$p['numero_pedido']}</td>";
                        echo "<td>{$p['estado']}</td>";
                        echo "<td>{$p['total_productos']}</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                }
                
            } else {
                echo "❌ Error al enviar pedido: {$resultado['message']}<br>";
            }
        } else {
            echo "⚠️ El pedido borrador no tiene productos para enviar<br>";
        }
    } else {
        echo "❌ No se encontró pedido borrador<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error en el test: " . $e->getMessage() . "<br>";
}

echo "<br><a href='index.php?action=registroPmesa&ida=$mesaId' style='background: #007bff; color: white; padding: 10px; text-decoration: none;'>🔙 Volver a Mesa $mesaId</a>";
echo "<br><a href='test_flujo_envio_completo.php?mesa=$mesaId' style='background: #28a745; color: white; padding: 10px; text-decoration: none; margin: 5px;'>🔄 Repetir Test</a>";
?>
