
<?php
ob_start();
class controllerFacturaAja extends MvcController
 {
	#REGISTRO DE PRE FACTURA
	##------------------------------------
	 public function preFacturaajaxController($cedula, $propina, $mesa)
	  {	//echo "<script>alert('Entro Controller y efectivo=".$efectivo." forma de pago =".$pago." cedula ".$cedula." tipoTarjeta ".$tipoTarjeta." tarjeta ".$tarjeta."');</script>";
		session_start();
		$datosController=array('usuario' =>$_SESSION["usuario"],
							   'clienteCedula' =>$cedula,
							   'propina' =>$propina);
		//$mesa =1;
		//$mesa =$_SESSION["mesa"];

		//echo "<script>alert('ontroller  ".$datosController['propina']."')</script>";
		//echo "<script>alert('Controle Usuario ".$_SESSION["usuario"]." ');</script>";
		//$cliente=DatosFacturaAja::buscarCedula($datosController['clienteCedula']);
		//$datosController['clienteCedula']=$cliente['id'];
		//echo "<script>alert('el ID Usuario ".$cliente['id']." ');</script>";
		$respuesta = DatosFacturaAja::preFacturaajaxModel($mesa,$datosController);
		if($respuesta == "success")
		 {	//$vaciarMesa = DatosFacturaAja::cancelarController();
			//header("location:registroPmesa");
			$_SESSION["mesaPrefactura"] = $mesa;
			echo'<script>alert("Listo");window.open("pdf1","_blank");
			location.href ="mesa";</script>';
			unset($_SESSION["clientep"]);
		 }
		else
		 {	echo' <script>alert("No se guardo, intente nuevamente 1");</script>';
			//header("location:index.php?action=okPm");
		 }
	  }
	#---------------------------------------
	#REGISTRO DE FACTURA
	##------------------------------------
	 public function facturaajaxController($efectivo, $nequi, $daviplata, $bancolombia, $tarjeta, $pago, $cedula, $propina, $mesa)
	  {	//echo "<script>alert('Entro Controller y efectivo=".$efectivo." forma de pago =".$pago." cedula ".$cedula." tipoTarjeta ".$tipoTarjeta." tarjeta ".$tarjeta."');</script>";
		session_start();
		$datosController=array('usuario' =>$_SESSION["usuario"],
							   'efectivo' =>$efectivo,
							   'tarjeta' =>$tarjeta,
							   'nequi' =>$nequi,
							   'daviplata' =>$daviplata,
							   'bancolombia' =>$bancolombia,
							   'clienteCedula' =>$cedula,
							   'propina' =>$propina,
							   'mesa' =>$mesa,
							   'pago' =>$pago );
		//$mesa =1;
		//$mesa =$_SESSION["mesa"];
		$mesa =$mesa;

		//echo "<script>alert('ontroller  ".$datosController['propina']."')</script>";
		//echo "<script>alert('Controle Usuario ".$_SESSION["usuario"]." ');</script>";
		//$cliente=DatosFacturaAja::buscarCedula($datosController['clienteCedula']);
		//$datosController['clienteCedula']=$cliente['id'];
		//echo "<script>alert('el ID Usuario ".$cliente['id']." ');</script>";
		$respuesta = DatosFacturaAja::facturaajaxModel($mesa,$datosController);
		if($respuesta == "success")
		 {	//$vaciarMesa = DatosFacturaAja::cancelarController();
			//header("location:registroPmesa");
			echo'<script>alert("Pago realizado exitosamente");window.open("pdf","_blank");
			location.href ="mesa";</script>';
			unset($_SESSION["clientep"]);
		 }
		else
		 {	echo' <script>alert("No se guardo, intente nuevamente 1");</script>';
			//header("location:index.php?action=okPm");
		 }
	  }
	#---------------------------------------
	#CALCULO FACTURA
	#-----------------------------
		public function facturaTotalController($iva,$subtotal)
		 {
			$total = $iva*$subtotal + $subtotal;
			return $total;
		 }
		public function facturaCambioController($efectivo, $total)
		 {
			$cambio = $efectivo -$total;
			return $cambio;
		 }
	#------------------------------
	#Autocompletar Productos y buscar su codigo
	#------------------------------------
		public function buscarProductoController($nombrep)
			{
				$datosController = $nombrep;
				$respuesta = DatosFacturaAja::buscarNombrepModel($datosController, "productos");
				return $respuesta;
			}
	#------------------------------------
	#----------------------------
	#BUSCAR PERSONA CEDULA
	#------------------------------------
	 public function buscarCedulaController($cedula)
		{	//echo "<script>alert('Entro Controller cedula cliente=".$cedula." ');</script>";
			$datosController = $cedula;
			$respuesta = DatosFacturaAja::buscarCedula($datosController);
			return $respuesta;
		}
	#-----------------------------------
 }
