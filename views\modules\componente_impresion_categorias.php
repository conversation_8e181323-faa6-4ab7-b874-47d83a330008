<?php
/**
 * Componente de impresión por categorías para integrar en registroPmesa.php
 * Muestra botones dinámicos para imprimir pedidos por categoría
 */

// Función para obtener categorías de un pedido
function obtenerCategoriasDelPedidoComponente($pedido_id) {
    try {
        // Intentar diferentes rutas para la conexión dependiendo del contexto
        if (file_exists('../../models/conexion.php')) {
            require_once '../../models/conexion.php';
        } elseif (file_exists('models/conexion.php')) {
            require_once 'models/conexion.php';
        } else {
            throw new Exception("No se pudo encontrar el archivo de conexión");
        }

        $conexion = new Conexion();
        $pdo = $conexion->conectar();
        
        // Obtener productos del pedido con sus categorías
        $stmt = $pdo->prepare("
            SELECT
                p.id as producto_id,
                p.nombre as producto_nombre,
                p.precio,
                p.categoria as categoria_producto,
                pvm.cantidad,
                p.precio as precio_unitario,
                (pvm.cantidad * p.precio) as subtotal,
                CASE
                    WHEN p.categoria IN ('bebidas', 'cervezas', 'licores', 'vinos', 'cocteles', 'refrescos', 'jugos', 'agua', 'bar') THEN 'bar'
                    WHEN p.categoria IN ('carnes', 'parrilla', 'asados', 'pescados', 'mariscos') THEN 'asados'
                    ELSE 'cocina'
                END as categoria_impresora
            FROM producto_vendido_mesa pvm
            INNER JOIN productos p ON pvm.productos_id = p.id
            WHERE pvm.pedidos_id = :pedido_id
            ORDER BY categoria_impresora, p.nombre
        ");
        
        $stmt->bindParam(":pedido_id", $pedido_id, PDO::PARAM_INT);
        $stmt->execute();
        $productos = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Debug: Log para ver qué productos se encontraron
        error_log("obtenerCategoriasDelPedidoComponente - Pedido ID: $pedido_id");
        error_log("obtenerCategoriasDelPedidoComponente - Productos encontrados: " . count($productos));
        if (!empty($productos)) {
            foreach ($productos as $i => $prod) {
                error_log("Producto $i: {$prod['producto_nombre']} - Categoría: {$prod['categoria_impresora']}");
            }
        }

        if (empty($productos)) {
            error_log("obtenerCategoriasDelPedidoComponente - Retornando array vacío");
            return ['categorias' => [], 'total_productos' => 0];
        }
        
        // Agrupar productos por categoría de impresora
        $categorias = [];
        
        foreach ($productos as $producto) {
            $cat = $producto['categoria_impresora'];
            
            if (!isset($categorias[$cat])) {
                $categorias[$cat] = [
                    'nombre' => obtenerNombreCategoriaComponente($cat),
                    'productos' => [],
                    'total_productos' => 0,
                    'total_precio' => 0
                ];
            }
            
            $categorias[$cat]['productos'][] = $producto;
            $categorias[$cat]['total_productos'] += $producto['cantidad'];
            $categorias[$cat]['total_precio'] += $producto['subtotal'];
        }
        
        // Debug: Log del resultado final
        error_log("obtenerCategoriasDelPedidoComponente - Categorías agrupadas: " . count($categorias));
        foreach ($categorias as $cat => $info) {
            error_log("Categoría $cat: {$info['total_productos']} productos, {$info['total_precio']} precio");
        }

        $resultado = [
            'categorias' => $categorias,
            'total_productos' => count($productos),
            'pedido_id' => $pedido_id
        ];

        error_log("obtenerCategoriasDelPedidoComponente - Resultado final: " . json_encode(array_keys($resultado['categorias'])));

        return $resultado;
        
    } catch (Exception $e) {
        error_log("Error en obtenerCategoriasDelPedidoComponente: " . $e->getMessage());
        return ['categorias' => [], 'total_productos' => 0, 'error' => $e->getMessage()];
    }
}

function obtenerNombreCategoriaComponente($categoria) {
    $nombres = [
        'bar' => '🍺 BAR',
        'cocina' => '🍳 COCINA',
        'asados' => '🥩 ASADOS'
    ];
    return $nombres[$categoria] ?? strtoupper($categoria);
}

function generarTicketComponente($pedido_info, $productos, $categoria) {
    $contenido = "";
    
    // Encabezado
    $contenido .= "MESA: " . $pedido_info['mesa_numero'] . "\n";
    $contenido .= "PEDIDO: " . $pedido_info['numero_pedido'] . "\n";
    $contenido .= "CATEGORIA: " . strtoupper($categoria) . "\n";
    $contenido .= "FECHA: " . date('d/m/Y H:i') . "\n";
    $contenido .= "ESTADO: ENVIADO\n";
    $contenido .= "--------------------------------\n";
    
    // Productos
    $total_categoria = 0;
    foreach ($productos as $producto) {
        $cantidad = $producto['cantidad'];
        $nombre = substr($producto['producto_nombre'], 0, 25);
        $subtotal = $producto['subtotal'];
        $total_categoria += $subtotal;
        
        $contenido .= sprintf("%-25s %2d\n", $nombre, $cantidad);
        $contenido .= sprintf("  $%s\n", number_format($subtotal, 0));
    }
    
    // Total de la categoría
    $contenido .= "--------------------------------\n";
    $contenido .= sprintf("TOTAL %s: $%s\n", strtoupper($categoria), number_format($total_categoria, 0));
    $contenido .= "--------------------------------\n";
    
    // Pie
    $contenido .= "Hora impresion: " . date('H:i:s') . "\n";
    
    return $contenido;
}

// Función principal para mostrar botones de impresión
function mostrarBotonesImpresionCategorias($pedido_id, $numero_pedido, $mesa_numero, $mesero) {
    $datos_pedido = obtenerCategoriasDelPedidoComponente($pedido_id);
    
    if (empty($datos_pedido['categorias'])) {
        return; // No mostrar nada si no hay categorías
    }
    
    echo "<div id='impresion_categorias_$pedido_id' style='background-color: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 8px; border: 1px solid #dee2e6;'>";
    echo "<h6 style='color: #495057; margin-bottom: 15px;'>🖨️ Impresión por Categorías - $numero_pedido</h6>";
    
    echo "<div style='display: flex; flex-wrap: wrap; gap: 10px;'>";
    
    foreach ($datos_pedido['categorias'] as $categoria => $info) {
        $color_categoria = [
            'bar' => '#17a2b8',
            'cocina' => '#28a745', 
            'asados' => '#dc3545'
        ];
        
        $color = $color_categoria[$categoria] ?? '#6c757d';
        
        echo "<button onclick='imprimirCategoria(\"$pedido_id\", \"$categoria\", \"$numero_pedido\", \"$mesa_numero\", \"$mesero\")' ";
        echo "style='background-color: $color; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer; font-size: 12px; font-weight: bold;'>";
        echo "🖨️ {$info['nombre']} ({$info['total_productos']})";
        echo "</button>";
    }
    
    echo "</div>";
    echo "</div>";
    
    // JavaScript para manejar la impresión
    echo "<script>
    function imprimirCategoria(pedidoId, categoria, numeroPedido, mesaNumero, mesero) {
        // Mostrar loading
        event.target.innerHTML = '⏳ Preparando...';
        event.target.disabled = true;
        
        // Hacer petición AJAX para obtener contenido
        fetch('views/modules/ajax_impresion_categoria.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                pedido_id: pedidoId,
                categoria: categoria,
                numero_pedido: numeroPedido,
                mesa_numero: mesaNumero,
                mesero: mesero
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                abrirVentanaImpresion(data.contenido, categoria);
            } else {
                alert('Error: ' + data.error);
            }
            
            // Restaurar botón
            event.target.innerHTML = '🖨️ ' + categoria.toUpperCase();
            event.target.disabled = false;
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error de conexión');
            
            // Restaurar botón
            event.target.innerHTML = '🖨️ ' + categoria.toUpperCase();
            event.target.disabled = false;
        });
    }
    
    function abrirVentanaImpresion(contenido, categoria) {
        const ventana = window.open('', '_blank', 'width=400,height=600,scrollbars=yes');
        
        ventana.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>Impresión - \${categoria}</title>
                <style>
                    body { 
                        font-family: 'Courier New', monospace; 
                        font-size: 12px; 
                        margin: 20px; 
                        white-space: pre-wrap;
                    }
                    @media print {
                        body { margin: 0; }
                    }
                </style>
            </head>
            <body>
                \${contenido}
                <script>
                    window.onload = function() {
                        window.print();
                    };
                </scr\ipt>
            </body>
            </html>
        `);
        
        ventana.document.close();
    }
    </script>";
}
?>
