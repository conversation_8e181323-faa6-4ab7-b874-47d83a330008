<?php
// Test directo del AJAX sin interfaz
require_once "../../models/conexion.php";
require_once "../../models/crudEstadoPedidos.php";
require_once "../../controllers/controllerEstadoPedidos.php";

// Iniciar sesión
session_start();
if (!isset($_SESSION["usuario"])) {
    $_SESSION["usuario"] = 1;
    $_SESSION["tipo_usuario"] = 1;
    $_SESSION["perfil"] = "administrador";
}

echo "<h1>🔧 Test Directo del AJAX</h1>";

// Obtener un pedido borrador para probar
try {
    $stmt = Conexion::conectar()->prepare("
        SELECT id FROM pedidos 
        WHERE estado = 'borrador' 
        ORDER BY id DESC 
        LIMIT 1
    ");
    $stmt->execute();
    $pedido = $stmt->fetch();
    
    if ($pedido) {
        $pedidoId = $pedido['id'];
        echo "<h3>📋 Probando con pedido ID: $pedidoId</h3>";
        
        // Simular la llamada AJAX directamente
        echo "<h4>1. Simulando llamada AJAX...</h4>";
        
        // Simular $_POST
        $_POST['enviar_pedido'] = true;
        $_POST['pedido_id'] = $pedidoId;
        
        echo "<div style='background: #f8f9fa; padding: 10px; border: 1px solid #ddd; margin: 10px 0;'>";
        echo "<strong>Datos POST simulados:</strong><br>";
        echo "enviar_pedido = true<br>";
        echo "pedido_id = $pedidoId<br>";
        echo "</div>";
        
        // Capturar la salida del AJAX
        ob_start();
        
        try {
            // Incluir el archivo AJAX
            include 'ajaxEstadoPedidos.php';
        } catch (Exception $e) {
            echo "❌ Error incluyendo AJAX: " . $e->getMessage();
        }
        
        $output = ob_get_clean();
        
        echo "<h4>2. Respuesta del AJAX:</h4>";
        echo "<div style='background: #f8f9fa; padding: 10px; border: 1px solid #ddd; margin: 10px 0; font-family: monospace;'>";
        echo htmlspecialchars($output);
        echo "</div>";
        
        // Intentar parsear como JSON
        echo "<h4>3. Análisis de la respuesta:</h4>";
        $json = json_decode($output, true);
        if ($json) {
            echo "✅ JSON válido:<br>";
            echo "<pre>" . print_r($json, true) . "</pre>";
        } else {
            echo "❌ No es JSON válido. Error: " . json_last_error_msg() . "<br>";
            echo "Respuesta cruda: " . htmlspecialchars($output);
        }
        
        // Limpiar $_POST
        unset($_POST['enviar_pedido']);
        unset($_POST['pedido_id']);
        
    } else {
        echo "❌ No hay pedidos borrador disponibles para probar.<br>";
        echo "<a href='../../index.php?action=crear_datos_prueba'>Crear datos de prueba</a>";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage();
}

echo "<br><br>";
echo "<a href='test_envio_simple.php' style='background: #28a745; color: white; padding: 10px; text-decoration: none; margin: 5px;'>🔙 Test Simple</a>";
echo "<a href='test_ajax.php' style='background: #007bff; color: white; padding: 10px; text-decoration: none; margin: 5px;'>🔧 Test Completo</a>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}

pre {
    background: #f8f9fa;
    padding: 10px;
    border: 1px solid #ddd;
    overflow-x: auto;
}
</style>
