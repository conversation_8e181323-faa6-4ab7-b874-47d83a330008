<!DOCTYPE html>
<html>
<head>
    <title>⚡ Test Inventario Facturación Rápida</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1000px; margin: 0 auto; }
        .card { background: #f8f9fa; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #007bff; }
        .success { border-left-color: #28a745; background: #d4edda; }
        .warning { border-left-color: #ffc107; background: #fff3cd; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .btn { padding: 15px 30px; margin: 10px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold; font-size: 16px; cursor: pointer; border: none; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        h1 { color: #007bff; text-align: center; }
        h2 { color: #495057; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .debug-info { background: #e9ecef; padding: 10px; border-radius: 3px; font-family: monospace; margin: 10px 0; }
        .comparison-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        .comparison-table th, .comparison-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .comparison-table th { background-color: #f2f2f2; }
    </style>
</head>
<body>

<div class="container">
    <h1>⚡ Inventario en Facturación Rápida - Funcionalidad Agregada</h1>
    <p style="text-align: center; font-size: 18px; color: #6c757d;">
        Descuento automático de inventario en el sistema de facturación rápida
    </p>

    <div class="card success">
        <h2>✅ Funcionalidad Agregada a Facturación Rápida</h2>
        <p>He agregado el descuento automático de inventario al sistema de facturación rápida:</p>
        
        <ul>
            <li>✅ <strong>Incluido modelo crud.php:</strong> Para acceder a `productoListaSuministroModel()`</li>
            <li>✅ <strong>Descuento por producto:</strong> Procesa cada producto de la facturación rápida</li>
            <li>✅ <strong>Cálculo de cantidades:</strong> `cantidadTotalS = spcantidad * cantidad`</li>
            <li>✅ <strong>Consolidación de suministros:</strong> Suma descuentos del mismo suministro</li>
            <li>✅ <strong>Actualización de inventario:</strong> `UPDATE sucursal SET cantidad = ?`</li>
            <li>✅ <strong>Protección contra negativos:</strong> `max(0, cantidad)` evita inventario negativo</li>
            <li>✅ <strong>Logs específicos:</strong> "INVENTARIO RÁPIDA" para identificar origen</li>
        </ul>
    </div>

    <div class="card">
        <h2>🔧 Diferencias entre Facturación Normal y Rápida</h2>
        
        <table class="comparison-table">
            <thead>
                <tr>
                    <th>Aspecto</th>
                    <th>Facturación Normal (Mesa)</th>
                    <th>Facturación Rápida</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>Origen de productos</strong></td>
                    <td>Tabla `producto_vendido_mesa`</td>
                    <td>Array JSON desde frontend</td>
                </tr>
                <tr>
                    <td><strong>Estructura de datos</strong></td>
                    <td>`$producto["idpr"]`, `$producto["cantidadpvm"]`</td>
                    <td>`$producto['id']`, `$producto['cantidad']`</td>
                </tr>
                <tr>
                    <td><strong>Mesa asociada</strong></td>
                    <td>Mesa específica (1-N)</td>
                    <td>Mesa 0 (sin mesa física)</td>
                </tr>
                <tr>
                    <td><strong>Estado del pedido</strong></td>
                    <td>Borrador → Enviado → Facturado</td>
                    <td>Directamente 'facturado'</td>
                </tr>
                <tr>
                    <td><strong>Logs de inventario</strong></td>
                    <td>"INVENTARIO - Producto..."</td>
                    <td>"INVENTARIO RÁPIDA - Producto..."</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="card">
        <h2>🎯 Flujo de Descuento en Facturación Rápida</h2>
        
        <ol>
            <li><strong>Usuario selecciona productos:</strong> Frontend envía array JSON</li>
            <li><strong>Validación de pago:</strong> Verifica que el pago sea suficiente</li>
            <li><strong>Creación de pedido:</strong> INSERT en tabla `pedidos` con estado 'facturado'</li>
            <li><strong>Inserción de productos:</strong> INSERT en `pedido_productos_mesa` con mesa_id = 0</li>
            <li><strong>🆕 DESCUENTO DE INVENTARIO:</strong>
                <ul>
                    <li>Para cada producto: `Datos::productoListaSuministroModel($producto['id'])`</li>
                    <li>Calcular descuento: `$suministro["spcantidad"] * $producto['cantidad']`</li>
                    <li>Consolidar por suministro</li>
                    <li>Actualizar tabla `sucursal`</li>
                </ul>
            </li>
            <li><strong>Creación de venta:</strong> INSERT en tabla `ventas`</li>
            <li><strong>Configuración de PDF:</strong> Variables de sesión para impresión</li>
        </ol>
    </div>

    <div class="card warning">
        <h2>🧪 Instrucciones de Prueba</h2>
        <p>Para verificar que el descuento de inventario funcione en facturación rápida:</p>
        
        <ol>
            <li><strong>Verificar inventario inicial:</strong>
                <ul>
                    <li>Ve a "Suministros" → "Vista Sucursal"</li>
                    <li>Anota las cantidades de algunos suministros</li>
                    <li>Toma captura de pantalla para comparar después</li>
                </ul>
            </li>
            <li><strong>Realizar facturación rápida:</strong>
                <ul>
                    <li>Ve a "Facturación Rápida"</li>
                    <li>Busca y agrega productos que usen suministros conocidos</li>
                    <li>Configura formas de pago</li>
                    <li>Haz clic en "Facturar Rápido"</li>
                </ul>
            </li>
            <li><strong>Verificar descuento:</strong>
                <ul>
                    <li>Regresa a "Suministros" → "Vista Sucursal"</li>
                    <li>Compara con las cantidades iniciales</li>
                    <li>Las cantidades deben haber disminuido</li>
                </ul>
            </li>
            <li><strong>Revisar logs:</strong>
                <ul>
                    <li>Revisa los logs del servidor</li>
                    <li>Busca mensajes "INVENTARIO RÁPIDA"</li>
                    <li>Deben mostrar los suministros procesados</li>
                </ul>
            </li>
        </ol>
    </div>

    <div class="card">
        <h2>📊 Ejemplo de Funcionamiento</h2>
        <p>Supongamos que en facturación rápida vendes:</p>
        
        <div class="debug-info">
Productos seleccionados:
- 3 Hamburguesas (ID: 15)
- 2 Gaseosas (ID: 8)

Cada hamburguesa usa:
- Pan: 1 unidad
- Carne: 150g
- Queso: 50g

Cada gaseosa usa:
- Jarabe: 30ml
- Agua: 300ml
        </div>
        
        <h4>Cálculo de descuentos:</h4>
        <div class="debug-info">
Hamburguesas (3 unidades):
- Pan: 1 × 3 = 3 unidades
- Carne: 150g × 3 = 450g
- Queso: 50g × 3 = 150g

Gaseosas (2 unidades):
- Jarabe: 30ml × 2 = 60ml
- Agua: 300ml × 2 = 600ml

Total a descontar:
- Pan: 3 unidades
- Carne: 450g
- Queso: 150g
- Jarabe: 60ml
- Agua: 600ml
        </div>
    </div>

    <div class="card">
        <h2>📋 Logs Esperados</h2>
        <p>En los logs del servidor deberías ver:</p>
        
        <div class="debug-info">
FACTURACIÓN RÁPIDA - Iniciando descuento de inventario
INVENTARIO RÁPIDA - Producto Hamburguesa (ID: 15) tiene 3 suministros
INVENTARIO RÁPIDA - Suministro ID: 1, Cantidad actual: 100, A descontar: 3
INVENTARIO RÁPIDA - Suministro ID: 2, Cantidad actual: 5000, A descontar: 450
INVENTARIO RÁPIDA - Suministro ID: 3, Cantidad actual: 2000, A descontar: 150
INVENTARIO RÁPIDA - Producto Gaseosa (ID: 8) tiene 2 suministros
INVENTARIO RÁPIDA - Suministro ID: 4, Cantidad actual: 1000, A descontar: 60
INVENTARIO RÁPIDA - Suministro ID: 5, Cantidad actual: 10000, A descontar: 600
INVENTARIO RÁPIDA ACTUALIZADO - Suministro ID: 1, Nueva cantidad: 97
INVENTARIO RÁPIDA ACTUALIZADO - Suministro ID: 2, Nueva cantidad: 4550
INVENTARIO RÁPIDA ACTUALIZADO - Suministro ID: 3, Nueva cantidad: 1850
INVENTARIO RÁPIDA ACTUALIZADO - Suministro ID: 4, Nueva cantidad: 940
INVENTARIO RÁPIDA ACTUALIZADO - Suministro ID: 5, Nueva cantidad: 9400
FACTURACIÓN RÁPIDA - Inventario actualizado, 5 suministros procesados
        </div>
    </div>

    <div class="card">
        <h2>🔗 Enlaces de Prueba</h2>
        <p>Usa estos enlaces para probar la funcionalidad:</p>
        
        <a href="../../index.php?action=vistaSucursal" class="btn btn-primary" target="_blank">
            📦 Ver Inventario Actual
        </a>
        
        <a href="../../index.php?action=facturacionRapida" class="btn btn-success" target="_blank">
            ⚡ Facturación Rápida
        </a>
        
        <a href="../../index.php?action=mesa" class="btn btn-danger" target="_blank">
            📋 Lista de Mesas
        </a>
    </div>

    <div class="card success">
        <h2>🎉 Ambos Sistemas Completos</h2>
        <p style="font-size: 18px;">
            <strong>Ahora ambos sistemas de facturación incluyen descuento automático de inventario:</strong>
        </p>
        
        <div style="display: flex; gap: 20px;">
            <div style="flex: 1;">
                <h4>✅ Facturación Normal (Mesa):</h4>
                <ul>
                    <li>Validación de pagos</li>
                    <li>Facturación completa</li>
                    <li>Descuento de inventario</li>
                    <li>Impresión automática</li>
                    <li>Limpieza de mesa</li>
                </ul>
            </div>
            <div style="flex: 1;">
                <h4>✅ Facturación Rápida:</h4>
                <ul>
                    <li>Validación de pagos</li>
                    <li>Facturación directa</li>
                    <li>🆕 Descuento de inventario</li>
                    <li>Impresión automática</li>
                    <li>Sin mesa física</li>
                </ul>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 20px;">
            <button class="btn btn-success" onclick="mostrarResumenCompleto()">
                📋 Ver Resumen Completo
            </button>
        </div>
    </div>

</div>

<script>
function mostrarResumenCompleto() {
    alert('📋 SISTEMAS DE FACTURACIÓN COMPLETOS:\n\n' +
          '✅ FACTURACIÓN NORMAL (MESA):\n' +
          '   • Validación de 5 formas de pago\n' +
          '   • Facturación completa con pedidos\n' +
          '   • Descuento automático de inventario\n' +
          '   • Impresión automática de PDF\n' +
          '   • Limpieza y liberación de mesa\n\n' +
          '✅ FACTURACIÓN RÁPIDA:\n' +
          '   • Validación de 5 formas de pago\n' +
          '   • Facturación directa sin mesa\n' +
          '   • 🆕 Descuento automático de inventario\n' +
          '   • Impresión automática de PDF\n' +
          '   • Productos con estado "facturado"\n\n' +
          '🎯 INVENTARIO:\n' +
          '   • Descuento automático en ambos sistemas\n' +
          '   • Consolidación por suministro\n' +
          '   • Protección contra inventarios negativos\n' +
          '   • Logs detallados para debugging\n\n' +
          '¡Ambos sistemas completamente funcionales!');
}

// Test automático al cargar
document.addEventListener('DOMContentLoaded', function() {
    console.log('⚡ Test de inventario en facturación rápida cargado');
    console.log('✅ Funcionalidad agregada al sistema de facturación rápida');
    console.log('🧪 Prueba facturación rápida para verificar descuentos');
});
</script>

</body>
</html>
