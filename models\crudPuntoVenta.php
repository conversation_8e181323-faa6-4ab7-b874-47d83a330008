<?php

#EXTENSIÓN DE CLASES: Los objetos pueden ser extendidos, y pueden heredar propiedades y métodos. Para definir una clase como extensión, debo definir una clase padre, y se utiliza dentro de una clase hija.
require_once "conexion.php";
class DatosPuntoVenta extends Conexion
 {
	#REGISTRO DE PUNTO VENTA O SUCURSAL
	#-------------------------------------
	 public static function registroPuntoVentaModel($datosModel, $tabla)
		{	echo "<script>alert('Entro CRUD ".$datosModel["nombre"]." es')</script>";	
			$consulta="INSERT INTO $tabla (nombre, telefono, direccion) VALUES (:nombre, :telefono, :direccion)";
			//echo "<script>alert('Entro CRUD ".$consulta." no')</script>";	
			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->execute();			
			$stmt->bindParam(":nombre", $datosModel["nombre"], PDO::PARAM_STR);
			$stmt->bindParam(":direccion", $datosModel["direccion"], PDO::PARAM_STR);			
			$stmt->bindParam(":telefono", $datosModel["telefono"], PDO::PARAM_STR);
			echo "<script>alert('Guardo')</script>";
			if($stmt->execute())
				{	return "success";	}
			else{ return "error";	}
			$stmt->close();
		}
	#-------------------------------------
	#VISTA PUNTO VENTA O SUCURSAL
	#-------------------------------------
	 public static function vistaPuntoVentaModel($tabla)
		{
			$stmt = Conexion::conectar()->prepare("SELECT id, nombre, telefono, direccion FROM $tabla");	
			$stmt->execute();				
			return $stmt->fetchAll();
			$stmt->close();
		}
	#-------------------------------------

	#EDITAR PUNTO VENTA O SUCURSAL
	#-------------------------------------
	 public static function editarPuntoVentaModel($datosModel, $tabla)
		{
			$stmt = Conexion::conectar()->prepare("SELECT id, nombre, telefono, direccion FROM $tabla WHERE id = :id");
			$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);	
			$stmt->execute();
			return $stmt->fetch();
			$stmt->close();
		}
	#-------------------------------------

	#ACTUALIZAR PUNTO VENTA O SUCURSAL
	#-------------------------------------
	 public static function actualizarPuntoVentaModel($datosModel, $tabla)
		{//	echo "<script>alert('entro cruz Suministro')</script>";
			$stmt = Conexion::conectar()->prepare("UPDATE $tabla SET nombre = :nombre, telefono=:telefono,  direccion = :direccion WHERE id = :id");			
			$stmt->bindParam(":nombre", $datosModel["nombre"], PDO::PARAM_STR);
			$stmt->bindParam(":direccion", $datosModel["direccion"], PDO::PARAM_STR);				
			$stmt->bindParam(":telefono", $datosModel["telefono"], PDO::PARAM_STR);
			$stmt->bindParam(":id", $datosModel["id"], PDO::PARAM_INT);
			//echo "<script>alert('centro proceso')</script>";
			if($stmt->execute())
				{echo "<script>alert('Guardo Actualizar PuntoVenta')</script>";return "success";	}
			else{echo "<script>alert('Error base de dato serve')</script>";	return "error";			}
			$stmt->close();
		}
	#-------------------------------------

	#BORRAR PUNTO VENTA O SUCURSAL
	#------------------------------------
	 public static function borrarPuntoVentaModel($datosModel, $tabla)
		{
			$stmt = Conexion::conectar()->prepare("DELETE FROM $tabla WHERE id = :id");
			$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);
			if($stmt->execute())
				{	return "success";	}
			else{	return "error";		}
			$stmt->close();
		}
	#----------------------------------------------	
 }