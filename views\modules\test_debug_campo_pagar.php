<!DOCTYPE html>
<html>
<head>
    <title>🔍 Debug Campo Pagar</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1000px; margin: 0 auto; }
        .card { background: #f8f9fa; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #007bff; }
        .success { border-left-color: #28a745; background: #d4edda; }
        .warning { border-left-color: #ffc107; background: #fff3cd; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .btn { padding: 15px 30px; margin: 10px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold; font-size: 16px; cursor: pointer; border: none; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        h1 { color: #007bff; text-align: center; }
        h2 { color: #495057; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .debug-info { background: #e9ecef; padding: 10px; border-radius: 3px; font-family: monospace; margin: 10px 0; }
    </style>
</head>
<body>

<div class="container">
    <h1>🔍 Debug Campo Pagar - Diagnóstico</h1>
    <p style="text-align: center; font-size: 18px; color: #6c757d;">
        Diagnóstico de por qué el campo "Total a Pagar" sigue en 0
    </p>

    <div class="card warning">
        <h2>🔍 Script de Debug Agregado</h2>
        <p>He agregado un script de debug detallado al controlador que mostrará:</p>
        
        <ul>
            <li>🔧 <strong>Si el script se ejecuta:</strong> Mensaje en consola</li>
            <li>📊 <strong>Valor a asignar:</strong> El total calculado</li>
            <li>🎯 <strong>Si encuentra el campo:</strong> Confirmación de existencia</li>
            <li>📝 <strong>Valor anterior y nuevo:</strong> Antes y después de la actualización</li>
            <li>🎨 <strong>Aplicación de formato:</strong> Si formatCurrency funciona</li>
        </ul>
    </div>

    <div class="card">
        <h2>🧪 Instrucciones de Debug</h2>
        <p>Para diagnosticar el problema:</p>
        
        <ol>
            <li><strong>Ve a Mesa 5:</strong> Haz clic en el botón abajo</li>
            <li><strong>Abre la consola ANTES:</strong> Presiona F12 → pestaña "Console"</li>
            <li><strong>Recarga la página:</strong> Presiona F5 o Ctrl+R</li>
            <li><strong>Observa los mensajes:</strong> Busca mensajes que empiecen con 🔧, ✅, ❌, ⚠️</li>
            <li><strong>Reporta qué ves:</strong> Los mensajes te dirán exactamente qué está pasando</li>
        </ol>
    </div>

    <div class="card">
        <h2>📊 Mensajes Esperados en Consola</h2>
        <p>Deberías ver algo como esto:</p>
        
        <div class="debug-info">
🔧 Ejecutando script para actualizar campo pagar
Total a asignar: 50000
📄 DOM ready, intentando actualizar campo pagar
✅ Campo pagar encontrado, valor anterior: 0
✅ Campo pagar actualizado, valor nuevo: 50000
✅ Formato aplicado, valor final: $50,000
⏰ Timeout ejecutado, intentando actualizar campo pagar
✅ Campo pagar encontrado, valor anterior: $50,000
✅ Campo pagar actualizado, valor nuevo: 50000
✅ Formato aplicado, valor final: $50,000
        </div>
        
        <p><strong>Si ves estos mensajes, el script funciona correctamente.</strong></p>
    </div>

    <div class="card error">
        <h2>❌ Posibles Problemas y Soluciones</h2>
        
        <h4>Problema 1: No aparecen mensajes de debug</h4>
        <div class="debug-info">
Causa: El script no se está ejecutando
Solución: Verificar que el controlador se esté llamando correctamente
        </div>
        
        <h4>Problema 2: "Campo pagar no encontrado"</h4>
        <div class="debug-info">
Causa: El script se ejecuta antes de que el campo exista
Solución: El timeout debería solucionarlo
        </div>
        
        <h4>Problema 3: "Total a asignar: 0"</h4>
        <div class="debug-info">
Causa: El cálculo del total está fallando
Solución: Verificar obtenerTotalFacturacionController()
        </div>
        
        <h4>Problema 4: Campo se actualiza pero vuelve a 0</h4>
        <div class="debug-info">
Causa: Otro script está sobrescribiendo el valor
Solución: Buscar otros scripts que modifiquen el campo
        </div>
    </div>

    <div class="card">
        <h2>🔧 Test Manual en Consola</h2>
        <p>También puedes probar manualmente en la consola:</p>
        
        <div class="debug-info">
// 1. Verificar si el campo existe
console.log('Campo pagar:', document.getElementById('pagar'));

// 2. Ver el valor actual
console.log('Valor actual:', document.getElementById('pagar').value);

// 3. Actualizar manualmente
document.getElementById('pagar').value = '50000';
console.log('Valor después de actualizar:', document.getElementById('pagar').value);

// 4. Aplicar formato
if (typeof formatCurrency === 'function') {
    formatCurrency(document.getElementById('pagar'));
    console.log('Valor después de formato:', document.getElementById('pagar').value);
}
        </div>
    </div>

    <div class="card">
        <h2>🔗 Enlaces de Prueba</h2>
        <p>Usa estos enlaces para el debug:</p>
        
        <a href="../../index.php?action=registroPmesa&ida=5" class="btn btn-primary" target="_blank">
            🏠 Mesa 5 (Debug Principal)
        </a>
        
        <a href="../../index.php?action=registroPmesa&ida=3" class="btn btn-success" target="_blank">
            🏠 Mesa 3 (Alternativa)
        </a>
        
        <a href="../../index.php?action=mesa" class="btn btn-danger" target="_blank">
            📋 Lista de Mesas
        </a>
    </div>

    <div class="card success">
        <h2>📋 Checklist de Debug</h2>
        <p>Verifica estos puntos en orden:</p>
        
        <ol>
            <li>☐ <strong>Script se ejecuta:</strong> Aparecen mensajes 🔧 en consola</li>
            <li>☐ <strong>Total se calcula:</strong> "Total a asignar" no es 0</li>
            <li>☐ <strong>Campo se encuentra:</strong> Aparece mensaje ✅ "Campo pagar encontrado"</li>
            <li>☐ <strong>Valor se actualiza:</strong> "valor nuevo" muestra el total correcto</li>
            <li>☐ <strong>Formato se aplica:</strong> "valor final" muestra formato de moneda</li>
            <li>☐ <strong>Campo visible:</strong> El input muestra el valor correcto en pantalla</li>
        </ol>
        
        <p><strong>Si algún punto falla, ese es el problema que necesitamos resolver.</strong></p>
    </div>

</div>

<script>
// Test automático al cargar
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔍 Test de debug campo pagar cargado');
    console.log('📋 Abre Mesa 5 y revisa la consola para ver los mensajes de debug');
});
</script>

</body>
</html>
