<?php

require_once "../../models/crud.php";
require_once "../../controllers/controller.php";

session_start();

/*=============================================
DESCONTAR INVENTARIO MANUALMENTE
=============================================*/
if(isset($_POST['descontar_inventario'])) {
    
    $pedidoId = $_POST['pedido_id'];
    
    // Incluir controladores necesarios
    require_once "../../controllers/controllerEstadoPedidos.php";
    require_once "../../models/crudEstadoPedidos.php";
    
    $controller = new ControllerEstadoPedidos();
    
    // Verificar que el pedido esté entregado
    if ($controller->validarEstadoPedidoController($pedidoId, 'entregado')) {
        
        // Descontar inventario
        $resultado = $controller->descontarInventarioManualController($pedidoId);
        
        if ($resultado) {
            echo json_encode(array('status' => 'success', 'message' => 'Inventario descontado correctamente'));
        } else {
            echo json_encode(array('status' => 'error', 'message' => 'Error al descontar inventario'));
        }
        
    } else {
        echo json_encode(array('status' => 'error', 'message' => 'El pedido debe estar entregado para descontar inventario'));
    }
    
    exit;
}

/*=============================================
VERIFICAR STOCK DISPONIBLE
=============================================*/
if(isset($_POST['verificar_stock'])) {
    
    $productoId = $_POST['producto_id'];
    $cantidad = $_POST['cantidad'];
    
    // Verificar si hay suficiente stock
    $stmt = Conexion::conectar()->prepare("
        SELECT s.cantidad, su.nombre as suministro_nombre
        FROM sucursal s
        JOIN suministros_productos sp ON s.suministro_id = sp.suministro_id
        JOIN suministros su ON s.suministro_id = su.id
        WHERE sp.producto_id = ? AND s.punto_id = 1
    ");
    $stmt->execute([$productoId]);
    $suministros = $stmt->fetchAll();
    
    $stockSuficiente = true;
    $detalles = array();
    
    foreach ($suministros as $suministro) {
        $cantidadNecesaria = $cantidad * $suministro['cantidades'];
        $disponible = $suministro['cantidad'];
        
        $detalles[] = array(
            'suministro' => $suministro['suministro_nombre'],
            'necesario' => $cantidadNecesaria,
            'disponible' => $disponible,
            'suficiente' => $disponible >= $cantidadNecesaria
        );
        
        if ($disponible < $cantidadNecesaria) {
            $stockSuficiente = false;
        }
    }
    
    echo json_encode(array(
        'stock_suficiente' => $stockSuficiente,
        'detalles' => $detalles
    ));
    
    exit;
}

/*=============================================
OBTENER ESTADO DEL INVENTARIO
=============================================*/
if(isset($_POST['estado_inventario'])) {
    
    $stmt = Conexion::conectar()->prepare("
        SELECT s.id, su.nombre, s.cantidad, su.cantidad_minima,
               CASE 
                   WHEN s.cantidad <= su.cantidad_minima THEN 'critico'
                   WHEN s.cantidad <= (su.cantidad_minima * 2) THEN 'bajo'
                   ELSE 'normal'
               END as estado
        FROM sucursal s
        JOIN suministros su ON s.suministro_id = su.id
        WHERE s.punto_id = 1
        ORDER BY s.cantidad ASC
    ");
    $stmt->execute();
    $inventario = $stmt->fetchAll();
    
    echo json_encode($inventario);
    exit;
}

// Si no se especifica ninguna acción válida
echo json_encode(array('status' => 'error', 'message' => 'Acción no válida'));
?>
