
<?php 
	ob_start();
	class controllerPedidoMesaProducto extends MvcController
		{
			
			#PEDIDO MESA PRODUCTO productos_id, mesas_id, pedidos_id, cantidad
			#---------------------------------

				#REGISTRO DE PEDIDO MESA PRODUCTO
				#------------------------------------
					public function registroPmesaProductoController()
						{
							if(isset($_POST["productos_idPmesaRegistro"]))
								{	
									$datosController =array('productos_id'=>$_POST["productos_idPmesaRegistro"],
															'mesas_id'=>$_POST["mesas_idPmesaRegistro"],
															'pedidos_id'=>$_POST["pedido_idPmesaRegistro"],
															'cantidad'=>$_POST["cantidadPmesaRegistro"]);
															
									//echo "<script>alert('Entro Controller ".$datosController["cantidad"]." no')</script>";	
									$respuesta = DatosPedidoMesaProducto::registroPmesaProductoModel($datosController, "pedido_productos_mesa");
									if($respuesta == "success")
										{	header("location:index.php?action=okPmP");	}
									else
										{	header("location:index.php");	}
								}
						}

				#VISTA DE PEDIDO MESA PRODUCTO
				#------------------------------------
				 public function vistaPmesaProductoController()
				  {
					$respuesta = DatosPedidoMesaProducto::vistaPmesaProductoModel("pedido_productos_mesa");
					foreach($respuesta as $row => $item)
					 {
						echo'<tr>						
								<td>'.$item["productos_id"].'</td>						
								<td>'.$item["mesas_id"].'</td>										
								<td>'.$item["pedidos_id"].'</td>						
								<td>'.$item["cantidad"].'</td>						
								<td><a href="index.php?action=editarPmesaProducto&mesas_id='.$item["mesas_id"].'">Editar</a></td>
								<td><a href="index.php?action=PmesaProducto&idBorrar='.$item["mesas_id"].'">Borrar</a></td>
							</tr>';
					 }
				  }
				#------------------------------------

				#EDITAR PEDIDO MESA PRODUCTO
				#------------------------------------
					public function editarPmesaProductoController()
						{
							$datosController = $_GET["mesas_id"];
							$respuesta = DatosPedidoMesaProducto::editarPmesaProductoModel($datosController, "pedido_productos_mesa");
							echo' <input type="text" value="'.$respuesta["productos_id"].'" name="productos_idPmesaEditar" required>	
							 <input type="text" value="'.$respuesta["mesas_id"].'" name="mesas_idPmesaEditar" required> 
							 <input type="text" value="'.$respuesta["pedidos_id"].'" name="cantidadPmesaEditar" required> 
							 <input type="text" value="'.$respuesta["cantidad"].'" name="fecha_horaPmesaEditar" required> 

							 
										
								 <input type="submit" value="Actualizar">';
						}

				#ACTUALIZAR PEDIDO MESA PRODUCTO
				#------------------------------------
					public function actualizarPmesaProductoController()
						{	echo "<script>alert('Entro Controller Actualizar Pedido')</script>";

							if(isset($_POST["productos_idPmesaEditar"], $_POST["mesas_idPmesaEditar"], $_POST["cantidadPmesaEditar"],$_POST["fecha_horaPmesaEditar"]))
								{	echo "<script>alert('Entro Controller Actualizar IF')</script>";
									$datosController = array(  "productos_id"=>$_POST["productos_idPmesaEditar"],			
																			"mesas_id"=>$_POST["mesas_idPmesaEditar"],				
																"pedidos_id"=>$_POST["cantidadPmesaEditar"],				
																"cantidad"=>$_POST["fecha_horaPmesaEditar"]																	
																);				
									$respuesta = DatosPedidoMesaProducto::actualizarPrmesaProductoModel($datosController, "pedido_productos_mesa");
									if($respuesta == "success")
										{	header("location:index.php?action=cambioPm");	}
												else
										{	echo "error";	}
								}				
						}

				#BORRAR PEDIDO MESA PRODUCTO
				#------------------------------------
								public function borrarPmesaProductoController()
									{
										if(isset($_GET["idBorrar"]))
											{
												$datosController = $_GET["idBorrar"];				
												$respuesta = DatosPedidoMesaProducto::borrarPmesaProductoModel($datosController, "pedido_productos_mesa");
												if($respuesta == "success")
													{	header("location:index.php?action=PmesaProducto");	}
											}
									}			
			#--------------------------------- 

		}			
			