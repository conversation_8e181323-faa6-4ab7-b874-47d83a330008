
<?php 
	$registroCsuministro = new controllerCompraSuministro();
	$proveedores = $registroCsuministro -> listaProveedoresController();
	$registroCsuministro -> registroCompraSuministroController();
	$registroCsuministro -> diferenciaController();
	$registroCsuministro -> registroCompraSuministroController();
 ?>
	
	<link rel="stylesheet" type="text/css" href="../css/bootstrap.css">
	<link rel="stylesheet" type="text/css" href="../css/bootstrap-theme.css">
	<link rel="stylesheet" type="text/css" href="../estilo.css">
	<link rel="stylesheet" type="text/css" href="../font-awesome/css/font-awesome.css">
	<script src="../js/jquery-1.11.3.min.js" type="text/javascript"></script>
	<script src="http://code.jquery.com/jquery-latest.js"></script>
	
	<script type="text/javascript">
	     /////////////Crear filas
	    	 function genera_tabla() 
	    	  {
				  // Obtener la referencia del elemento body
				  var body = document.getElementsByTagName("body")[0];				 
				  // Crea un elemento <table> y un elemento <tbody>
				  var tabla   = document.createElement("table");
				  var tblBody = document.createElement("tbody");				 
				  // Crea las celdas
				  for (var i = 0; i < 3; i++) 
				   {
					    // Crea las hileras de la tabla
					    var hilera = document.createElement("tr");				 
					    for (var j = 0; j < 3; j++) 
					     {
						      // Crea un elemento <td> y un nodo de texto, haz que el nodo de
						      // texto sea el contenido de <td>, ubica el elemento <td> al final
						      // de la hilera de la tabla
						      var celda = document.createElement("td");
						      var textoCelda = document.createTextNode("celda en la hilera "+i+", columna "+j);
						      celda.appendChild(textoCelda);
						      hilera.appendChild(celda);
				     	 }			 
					    // agrega la hilera al final de la tabla (al final del elemento tblbody)
					    tblBody.appendChild(hilera);
			  		}			 
				  // posiciona el <tbody> debajo del elemento <table>
				  tabla.appendChild(tblBody);
				  // appends <table> into <body>
				  body.appendChild(tabla);
				  // modifica el atributo "border" de la tabla y lo fija a "2";
				  tabla.setAttribute("border", "1");
				 }
	    	///////////fin crear filas

	    $(document).ready(function()
	    {
	        /**  * Funcion para añadir una nueva columna en la tabla*/
	         
	        $("#add").click(function()
	        	{
		            // Obtenemos el numero de filas (td) que tiene la primera columna
		            // (tr) del id "tabla"
		            var tds=$("#tabla tr:first td").length;
		            //variable de los imput creo
		            //var  nombre=$("#nombre");
		            var nombre = document.cal.nombre.value;
		            var cantidad = document.cal.cantidad.value;   
		            var precio = document.cal.precio.value;   
		            // Obtenemos el total de columnas (tr) del id "tabla"
		            var trs=$("#tabla tr").length;
		            var nuevaFila="<tr>";
		            ////////////////////////matriz ///////////
		            matriz= new Array(trs);
		            for(var j=0;j<trs;j++)
		             {
		             	matriz[j]=new Array(3);
		             }
		            for(var j=0;j<trs;j++)
		             {
		             	matriz[j]['suministro']=nombre;
		             	matriz[j]['cantidad']=cantidad;
		             	matriz[j]['precio']=cantidad;
		             	//alert(matriz[j]['suministro']+" cantidad:"+matriz[j]['cantidad']);
		             }
		          //////////////////////////////////// 
		            for(var i=0;i<tds;i++)
		             {
		                // añadimos las columnas
		                if (i==0)
		                 {
		                 	nuevaFila+="<td><input class='form-control' type='text' name='nombre"+trs+"' value='"+nombre+ "' required /> </td>";
		                 	
		                 }
		                else
		                 {	if (i==1) 
		                 	 {
		                 		nuevaFila+="<td><input class='form-control' type='text' name='cantidad"+trs+"' value='"+cantidad+"' required /></td>";
		                 	 }
		                 	else
		                 	 {
		                 		nuevaFila+="<td><input class='form-control' type='text' name='precio"+trs+"' value='"+precio+"' required /></td>";
		                 	 }
		                 	//matriz[trs][i]=cantidad;
		                 }
		             }
		            // Añadimos una columna con el numero total de filas.
		            // Añadimos uno al total, ya que cuando cargamos los valores para la
		            // columna, todavia no esta añadida	            
		            nuevaFila+="<td>"+(trs)+" ";
		            nuevaFila+="</tr>";
		            $("#tabla").append(nuevaFila);
		            document.cal.nombre.value="";
		            document.cal.cantidad.value="";
		            document.cal.precio.value="";
		            document.cal.fila.value=trs;
		            //alert( matriz[trs-1]['nombre']);
		        });
		 
		        /**
		         * Funcion para eliminar la ultima columna de la tabla.
		         * Si unicamente queda una columna, esta no sera eliminada
		         */
		        $("#del").click(function(){
	            // Obtenemos el total de columnas (tr) del id "tabla"
		            var trs=$("#tabla tr").length;
		            if(trs>1)
		            {
		                // Eliminamos la ultima columna
		                $("#tabla tr:last").remove();
		            }
		        });
		       // $("#destino").load("views/modules/ajaxFactura.php", {matriz: matriz,trs: trs, }, function(){
				        						 //alert("recibidos los datos por ajax efectivo"); 		 
				     						///});	
	    });

	    </script>

	    <style>
	    td, input {padding:5px;}
	    </style>
	
		<?php
			if (!isset($_POST['datos'])) 
			 { 
				//echo "<script>alert(' cantidad".$cantidad['1']."')</script>";
			 }
		?>
		



