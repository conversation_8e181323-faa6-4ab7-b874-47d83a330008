<?php
require_once "../../models/crud.php";
require_once "../../models/crudPedidoMesaVendido.php";

require_once "../../controllers/controller.php";
require_once "../../controllers/controllerPedidoMesaVendido.php";

if(isset($_POST['producto_id']))
{	//session_start();

	$datos=array( "producto_id"=>$_POST["producto_id"],
				  "mesa_id"=>$_POST["mesa_id"],
				  "fecha_hora"=>$_POST["fecha_hora"]);
	$ajax= new controllerPedidoMesaVendido();
	//$bancos=$ajax->addPmesaController($datos);
	$bancos=$ajax->addCantidadPmesaController($datos);

	if ($bancos=='success') {
		echo "success";
	 }else{
	 	echo "error";
	 }

}

if(isset($_POST['reducirProducto']))
{	//session_start();

	$datos=array( "producto_id"=>$_POST["reducirProducto"],
				  "mesa_id"=>$_POST["mesa_id"],
				  "fecha_hora"=>$_POST["fecha_hora"]);
	$ajax= new controllerPedidoMesaVendido();
	$bancos=$ajax->disminuirPmesaController($datos);

	if ($bancos=='success') {
		echo "success";
	 }else{
	 	echo "error";
	 }

}

/*=============================================
CANCELAR PRODUCTO INDIVIDUAL
=============================================*/
if(isset($_POST['cancelarProducto'])) {
	session_start();

	$productoId = $_POST["cancelarProducto"];
	$pedidoId = isset($_POST["pedido_id"]) ? $_POST["pedido_id"] : null;
	$mesa = $_SESSION["mesa"];

	try {
		// Verificar permisos del usuario
		$tipoUsuario = isset($_SESSION["tipo_usuario"]) ? $_SESSION["tipo_usuario"] : 0;

		// Solo administradores (tipo 1 y 3) pueden cancelar productos de pedidos enviados
		if ($tipoUsuario != 1 && $tipoUsuario != 3) {
			// Verificar si el pedido está en estado borrador
			if ($pedidoId) {
				require_once "../../models/conexion.php";
				$stmt = Conexion::conectar()->prepare("SELECT estado FROM pedidos WHERE id = ?");
				$stmt->bindParam(1, $pedidoId, PDO::PARAM_INT);
				$stmt->execute();
				$pedido = $stmt->fetch();

				if ($pedido && $pedido['estado'] != 'borrador') {
					echo "error_permisos";
					exit;
				}
			}
		}

		// Eliminar el producto usando el método existente
		$datos = array(
			'fecha_hora' => date('Y-m-d H:i:s'), // Usar fecha actual como fallback
			'mesa' => $mesa,
			'productos_id' => $productoId
		);

		$ajax = new controllerPedidoMesaVendido();
		$resultado = DatosPedidoMesaVendido::borrarPmesaModel($datos, "producto_vendido_mesa");

		if ($resultado == "success") {
			echo "success";
		} else {
			echo "error_eliminacion";
		}

	} catch (Exception $e) {
		error_log("Error cancelando producto: " . $e->getMessage());
		echo "error_excepcion";
	}
}