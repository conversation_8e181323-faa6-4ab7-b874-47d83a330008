<?php
require_once "../../models/crud.php";
require_once "../../models/crudPedidoMesaVendido.php";

require_once "../../controllers/controller.php";
require_once "../../controllers/controllerPedidoMesaVendido.php";

if(isset($_POST['producto_id'])) 
{	//session_start();

	$datos=array( "producto_id"=>$_POST["producto_id"],
				  "mesa_id"=>$_POST["mesa_id"],
				  "fecha_hora"=>$_POST["fecha_hora"]);
	$ajax= new controllerPedidoMesaVendido();
	//$bancos=$ajax->addPmesaController($datos);
	$bancos=$ajax->addCantidadPmesaController($datos);

	if ($bancos=='success') {
		echo "success";
	 }else{
	 	echo "error";
	 }
		
} 

if(isset($_POST['reducirProducto'])) 
{	//session_start();

	$datos=array( "producto_id"=>$_POST["reducirProducto"],
				  "mesa_id"=>$_POST["mesa_id"],
				  "fecha_hora"=>$_POST["fecha_hora"]);
	$ajax= new controllerPedidoMesaVendido();
	$bancos=$ajax->disminuirPmesaController($datos);

	if ($bancos=='success') {
		echo "success";
	 }else{
	 	echo "error";
	 }
		
} 