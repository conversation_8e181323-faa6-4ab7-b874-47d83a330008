<?php
//ini_set('display_errors', 1);
//ini_set('display_startup_errors', 1);
//error_reporting(E_ALL);
	$vistaUsuario = new controllerSuministro();
	$puntos = $vistaUsuario -> listaPuntosController();
	date_default_timezone_set("America/Bogota");
	//$fecha_actual=strftime("%Y-%m-%d %H:%M:%S");
	$cadena = array("January", "February","March","April", "May", "June","July","August","September", "October","November","December");
	$reemplazo = array("Enero", "Febrero","Marzo","Abril", "Mayo", "Junio","Julio","Agosto","Septiembre", "Octubre","Noviembre","Diciembre");
	$fecha_actual=strftime("%Y-%B-%d %H:%M:%S"); //%B Nombre completo del mes en Ingles <?=$fecha_final;
	$fecha_final=str_replace($cadena, $reemplazo, $fecha_actual);
?>
<script language="JavaScript">
	//////////////Marcar y desmarcar todo los checkbox//////////
	 function marcar(source)
		{
		 checkboxes=document.getElementsByTagName('input'); //obtenemos todos los controles del tipo Input
		 for(i=0;i<checkboxes.length;i++) //recoremos todos los controles
			{
			 if(checkboxes[i].type == "checkbox") //solo si es un checkbox entramos
				{
				 checkboxes[i].checked=source.checked; //si es un checkbox le damos el valor del checkbox que lo llamó (Marcar/Desmarcar Todos)
				}
			}
		}
	//////////////Marcar y desmarcar todo los checkbox//////////
	 function validar()
		{
		    var a=document.some_form['graduate[]'];
			alert("Tamaño del array"+a.length);
			var p=0;
			for(i=0;i<a.length;i++){
				if(a[i].checked){
					alert(a[i].value);
					p=1;
				}
			}
			if (p==0){
				alert('Seleccione por lo menos 1 casilla');
				return false;
			}

			document.some_form.submitted.value='yes';
			return true;
		}
</script>

<h1>Suministro </h1>
<div>
	<a href="registroProductoSuministro" target="_blank"> Producto sin Suministro &raquo;</a>
</div>
<form  method="post" >

<div class="table-responsive">
	<table>
		<tr>
			<td><label> Sucursal  : </label></td>
			<td><?php
			# # %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  puntos  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
				if($puntos=="error")
					{	echo "debe registrar el Sucursal"; }
				else{
						echo "<label></label>";
						$result='<select name="puntos"  id="puntos" autofocus="autofocus" required>';
						$result.=' <option value="-1">Sucursal</option>';
						foreach ($puntos as $row => $item)
						 {
						  if ($_SESSION["tipo_usuario"]==1)
						   {  $result.=' <option value="'.$item["id"].'">'.$item["nombre"].'</option>';   }
						 elseif ($_SESSION["punto_id"]==$item["id"])
						  {	$result.=' <option value="'.$item["id"].'">'.$item["nombre"].'</option>'; }
						  }
						 $result.='</select>';
						 echo $result;
					}

			# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  End puntos  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  max="<?=$_SESSION["fecha"];
			 ?><!--&laquo;<a class="enlace" href="registroPuntoVenta" target="_blank"> Registrar Sucursal</a>&raquo;<br>--></td>
		</tr>
	</table>
	<br>
	<input type="submit" value="Cargar"><br>
	<table border="1" class="table table-hover">
		<thead align="center">
			<tr align="center">
				<th>No</th>
				<th>CODIGO</th>
				<th>NOMBRES</th>
				<?php
				 if ($_SESSION["tipo_usuario"]==1)
					{
						echo '
						<th>PRECIO COMPRA</th>';
					}
				 ?>
				<!--th>IVA %</th-->
				<th>UNIDADES</th>
				<th>CANTIDAD</th>
				<?php
				 if ($_SESSION["tipo_usuario"]==1)
					{
						echo '
						<td align="center" >Selecione
						<input type="checkbox" onclick="marcar(this);" /></td align="center">
						';
					}
				 ?>
				<th></th>

			</tr>
		</thead>
		<tbody>
		 <?php
			$vistaUsuario -> vistaSuministroController();
			$vistaUsuario -> borrarSuministroController();
		 ?>
</div>
</form>
<?php

	$vistaUsuario -> cargarSucursal1Controller();
 if(isset($_GET["action"]))
	{	if($_GET["action"] == "cambio")		{	echo "Cambio Exitoso";	}	}
?>