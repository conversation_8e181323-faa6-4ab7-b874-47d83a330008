<?php
/*session_start();*/
if(!$_SESSION["validar"])
	{	header("location:index.php?action=ingresar");	exit();	}
?>
<script type="text/javascript">
	///// cargar zonas de mesa
	 function mesaZona(mesa)
	    {
	     if (dato == "norte")
	     	{
	     	 alert("Selecion");
	     	}

		 //$("#destino").load("views/modules/ajaxmesaZona.php", {mesa: mesa}, function(){ });
		}
	///////final Zona
</script>
<style type="text/css">
.cero{ background-color: yellow;border: 1px solid #fff; }
	.uno{  background-color: red;border: 1px solid #fff;}
	.dos{  background-color: green !important;border: 1px solid #fff; color: #fff !important;}
</style>
<h1>MESAS</h1>
<div style="margin-bottom: 15px;">
    <a href="registroMesa" class="btn btn-primary">Agregar Mesa</a>
    <a href="facturacionRapida" class="btn btn-success" style="margin-left: 10px;">
        🚀 FACTURACIÓN RÁPIDA
    </a>
</div>
<!--<div id="destino" > </div>
	<table border="1" class="table"  style="border-radius: 15px">
		<thead>
			<tr>
				<th>MESA | LUGAR | ACCIÓN</th>
				<th>MESA | LUGAR | ACCIÓN</th>
				<th>MESA | LUGAR | ACCIÓN</th>
				<th>MESA | LUGAR | ACCIÓN</th>
			</tr>
		</thead>
		<tbody>
			<div>-->
			<?php
			$vistaUsuario = new controllerMesa();
			$vistaUsuario -> vistaMesaController();
			$vistaUsuario -> borrarMesaController();
			?>
	<!--		</div><br>
			<div>
				<a href="registroMesa" >Agregar mas</a>
			</div>

		</tbody>
	</table>-->

<?php
if(isset($_GET["action"]))
	{
		if($_GET["action"] == "cambioM")
			{	echo "Cambio Exitoso";	}
	}
?>
<div class="col-xs-6 col-sm-4 col-lg-2"></div>