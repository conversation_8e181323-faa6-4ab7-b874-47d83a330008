<?php
require_once "conexion.php";
class DatosRoll extends Conexion
{
	#REGISTRO DE ROLL
	#-------------------------------------
	 public static function registroRolModel($datosModel, $tabla)
		{echo "<script>alert('Entro CRUD ".$datosModel['nombre']." no')</script>";
		 date_default_timezone_set("America/Bogota");
		 $fecha_creado=strftime("%Y-%m-%d %H:%M:%S");
		 $stmt = Conexion::conectar();
		 try
			{
			 $stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
			 $stmt->beginTransaction();
			 $consultar="INSERT INTO $tabla (nombre) VALUES ('".$datosModel["nombre"]."')";
			echo "<br>".$consultar."<br>";
				$stmt->exec($consultar);
				$idTablatura = $stmt->lastInsertId();	//ultimo id
				$cambio=$idTablatura.'-'.$datosModel["nombre"];
				$consultarA="INSERT INTO auditoria(tabla, accion, persona_id, fecha_accion, cambio)
				VALUES ('".$tabla."', 'INSERT', ".$_SESSION["usuario"].", '$fecha_creado', '$cambio')";
				echo "<br>".$consultarA."<br>";
				$stmt->exec($consultarA);
				/**/$stmt->commit();
				//echo "<script>alert('fin de cruD try que pasa');</script>";
				return "success";
				$stmt->close();
			 }
			catch (Exception $e)
			 {
			 	echo "<script>alert('catch error');</script>";
			 	$stmt->rollBack();
				print "Error!: ".$e->getMessage()."</br>";
				return "Error!: ".$e->getMessage()."</br>";
			 }

		}
	#-------------------------------------
	#VISTA ROLL
	#-------------------------------------
	 public static function vistaRolModel($tabla)
		{
			$consulta = "SELECT id, nombre FROM $tabla";
			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->execute();
			return $stmt->fetchAll();
			$stmt->close();
		}
	#------------------------------------
	#EDITAR ROLL
	#-------------------------------------
	 public static function editarRolModel($datosModel, $tabla)
		{
			$stmt = Conexion::conectar()->prepare("SELECT id, nombre FROM $tabla WHERE id = :id");
			$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);
			$stmt->execute();
			$r=$stmt->fetch();
			return $r;
			$stmt->close();

		}
	#-------------------------------------
	#ACTUALIZAR ROLL $_SESSION["erol"]
	#-------------------------------------
	 public static function actualizarRolModel($datosModel, $tabla)
		{ 	//echo "<script>alert('Entro Actualizar Producto')</script>";
			date_default_timezone_set("America/Bogota");
			$fecha_creado=strftime("%Y-%m-%d %H:%M:%S");
			$stmt = Conexion::conectar();
		 try {	//echo '<script>alert("entro CRUZ");</script>';
				$stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
				$stmt->beginTransaction();
				$consultar = "UPDATE $tabla SET nombre = '".$datosModel["nombre"]."' WHERE id = ".$datosModel["id"];
			echo "<br".$consultar."<br";
				if ($_SESSION["erol"]["nombre"]!=$datosModel["nombre"])
					{	$camb='perfil:'.$_SESSION["erol"]["nombre"].'='.$datosModel["nombre"];	} else{$camb=0;}
			$stmt->exec($consultar);
				$idTablatura = $stmt->lastInsertId();	//ultimo id
				$cambio=$datosModel["idEditar"].'-'.$camb;
				$consultarA="INSERT INTO aditoria(tabla, accion, persona_id, fecha_accion, cambio)
				VALUES ('".$tabla."', 'UPDATE', ".$_SESSION["usuario"].", '$fecha_creado', '$cambio')";
				echo "<br".$consultarA."<br";
				$stmt->exec($consultarA);
				$stmt->commit();
				//echo "<script>alert('fin de cruD try que pasa');</script>";
				return "success";
				$stmt->close();
			 }
			catch (Exception $e)
			 {
			 	echo "<script>alert('catch error');</script>";
			 	$stmt->rollBack();
				echo "Error!: ".$e->getMessage()."</br>";
				return "Error!: ".$e->getMessage()."</br>";
			 }

		}
	#-------------------------------------
	#BORRAR ROLL
	#------------------------------------
	 public static function borrarRolModel($datosModel, $tabla)
		{
			date_default_timezone_set("America/Bogota");
			$fecha_creado=strftime("%Y-%m-%d %H:%M:%S");
			$stmt = Conexion::conectar();
		 try
			{//echo '<script>alert("entro CRUD");</script>';
			 	$stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
				$stmt->beginTransaction();
			$consultar = "DELETE FROM $tabla WHERE id =".$datosModel;
			    echo "<br".$consultar."<br";
				$stmt->exec($consultar);
				$cambio=$datosModel;
				$consultarA="INSERT INTO aditoria(tabla, accion, persona_id, fecha_accion, cambio)
				VALUES ('".$tabla."', 'DELETE', ".$_SESSION["usuario"].", '$fecha_creado', '$cambio')";
				//echo "<br".$consultarA."<br";
				$stmt->exec($consultarA);
				$stmt->commit();
				//echo "<script>alert('fin de cruD try que pasa');</script>";
				return "success";
				$stmt->close();
			 }
			catch (Exception $e)
			 {
			 	echo "<script>alert('catch error');</script>";
			 	$stmt->rollBack();
				echo "Error!: ".$e->getMessage()."</br>";
				return "Error!: ".$e->getMessage()."</br>";
			 }
		}
	#----------------------------------------------
}