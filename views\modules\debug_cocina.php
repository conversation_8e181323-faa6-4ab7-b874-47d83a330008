<?php
// Debug específico para pantalla de cocina

echo "<h1>🔍 Debug Pantalla Cocina</h1>";
echo "<hr>";

$categoria = isset($_GET['categoria']) ? $_GET['categoria'] : 'cocina';

echo "<h2>1. 📊 Información General</h2>";
echo "Categoría solicitada: " . $categoria . "<br>";
echo "Usuario: " . $_SESSION["usuario"] . "<br>";
echo "Tipo Usuario: " . $_SESSION["tipo_usuario"] . "<br>";

echo "<h2>2. 🔍 Verificar Pedidos Enviados</h2>";

$db = Conexion::conectar();

// Consulta directa para ver pedidos enviados
$stmt = $db->prepare("
    SELECT p.*, m.numero as mesa_numero, COUNT(pvm.productos_id) as total_productos
    FROM pedidos p
    LEFT JOIN mesas m ON p.mesa_id = m.id
    LEFT JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
    WHERE p.estado = 'enviado'
    GROUP BY p.id
    ORDER BY p.fecha_envio ASC
");
$stmt->execute();
$pedidosEnviados = $stmt->fetchAll();

echo "<strong>Total pedidos enviados:</strong> " . count($pedidosEnviados) . "<br>";

if (!empty($pedidosEnviados)) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>Número</th><th>Mesa</th><th>Fecha Envío</th><th>Total Productos</th></tr>";
    foreach ($pedidosEnviados as $pedido) {
        echo "<tr>";
        echo "<td>" . $pedido['id'] . "</td>";
        echo "<td>" . $pedido['numero_pedido'] . "</td>";
        echo "<td>" . $pedido['mesa_numero'] . "</td>";
        echo "<td>" . $pedido['fecha_envio'] . "</td>";
        echo "<td>" . $pedido['total_productos'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "❌ No hay pedidos enviados<br>";
}

echo "<h2>3. 🛒 Verificar Productos por Categoría</h2>";

foreach (['bar', 'cocina', 'asados'] as $cat) {
    echo "<h3>Categoría: " . ucfirst($cat) . "</h3>";
    
    $stmt = $db->prepare("
        SELECT DISTINCT p.id, p.numero_pedido, p.fecha_envio, m.numero as mesa_numero,
               COUNT(pvm.productos_id) as total_productos
        FROM pedidos p
        JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
        JOIN productos prod ON pvm.productos_id = prod.id
        JOIN mesas m ON p.mesa_id = m.id
        WHERE p.estado = 'enviado' AND prod.categoria = ?
        GROUP BY p.id
        ORDER BY p.fecha_envio ASC
    ");
    $stmt->execute([$cat]);
    $pedidosCategoria = $stmt->fetchAll();
    
    echo "Pedidos con productos de " . $cat . ": " . count($pedidosCategoria) . "<br>";
    
    if (!empty($pedidosCategoria)) {
        echo "<ul>";
        foreach ($pedidosCategoria as $pedido) {
            echo "<li>Pedido " . $pedido['numero_pedido'] . " - Mesa " . $pedido['mesa_numero'] . " - " . $pedido['total_productos'] . " productos</li>";
        }
        echo "</ul>";
    }
}

echo "<h2>4. 🔧 Probar Controlador</h2>";

try {
    require_once "controllers/controllerEstadoPedidos.php";
    require_once "models/crudEstadoPedidos.php";
    
    $controllerEstado = new ControllerEstadoPedidos();
    
    foreach (['bar', 'cocina', 'asados'] as $cat) {
        echo "<h3>Controlador - Categoría: " . ucfirst($cat) . "</h3>";
        $pedidosPendientes = $controllerEstado->obtenerPedidosPendientesCategoriaController($cat);
        echo "Pedidos encontrados: " . count($pedidosPendientes) . "<br>";
        
        if (!empty($pedidosPendientes)) {
            echo "<ul>";
            foreach ($pedidosPendientes as $pedido) {
                echo "<li>Pedido " . $pedido['numero_pedido'] . " - Mesa " . $pedido['mesa_numero'] . "</li>";
            }
            echo "</ul>";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}

echo "<h2>5. 🧪 Crear Pedido de Prueba</h2>";

echo "<form method='post'>";
echo "<button type='submit' name='crear_pedido_enviado' style='background: #28a745; color: white; padding: 10px; border: none; margin: 5px;'>Crear Pedido Enviado de Prueba</button>";
echo "</form>";

if (isset($_POST['crear_pedido_enviado'])) {
    try {
        // Crear pedido de prueba
        $stmt = $db->prepare("
            INSERT INTO pedidos (mesa_id, mesero_id, cedula_cliente, estado, fecha_pedido, fecha_envio, numero_pedido, facturado)
            VALUES (1, ?, '0', 'enviado', NOW(), NOW(), CONCAT('P', LPAD(FLOOR(RAND() * 10000), 4, '0')), 'n')
        ");
        $stmt->execute([$_SESSION["usuario"]]);
        $pedidoId = $db->lastInsertId();
        
        // Buscar productos de diferentes categorías
        $categorias = ['bar', 'cocina', 'asados'];
        foreach ($categorias as $cat) {
            $stmt = $db->prepare("SELECT id FROM productos WHERE categoria = ? LIMIT 1");
            $stmt->execute([$cat]);
            $producto = $stmt->fetch();
            
            if ($producto) {
                // Agregar producto al pedido
                $stmt = $db->prepare("
                    INSERT INTO producto_vendido_mesa (productos_id, mesas_id, pedidos_id, cantidad, fecha_hora, mesero, descuento, codigo_descuento, nota, cocina)
                    VALUES (?, 1, ?, 1, NOW(), ?, 0, '', 'Producto de prueba " . $cat . "', ?)
                ");
                $stmt->execute([$producto['id'], $pedidoId, $_SESSION["usuario"], $cat]);
            }
        }
        
        echo "<div style='background: #d4edda; color: #155724; padding: 10px; margin: 10px 0;'>";
        echo "✅ Pedido enviado de prueba creado exitosamente (ID: $pedidoId)";
        echo "</div>";
        
        // Recargar página para mostrar los nuevos datos
        echo "<script>setTimeout(function(){ location.reload(); }, 2000);</script>";
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px 0;'>";
        echo "❌ Error creando pedido: " . $e->getMessage();
        echo "</div>";
    }
}

echo "<h2>6. 🔗 Enlaces Útiles</h2>";
echo "<a href='index.php?action=pantallaCocina&categoria=bar' style='background: #007bff; color: white; padding: 10px; text-decoration: none; margin: 5px; display: inline-block;'>🍺 Pantalla Bar</a><br>";
echo "<a href='index.php?action=pantallaCocina&categoria=cocina' style='background: #28a745; color: white; padding: 10px; text-decoration: none; margin: 5px; display: inline-block;'>🍳 Pantalla Cocina</a><br>";
echo "<a href='index.php?action=pantallaCocina&categoria=asados' style='background: #dc3545; color: white; padding: 10px; text-decoration: none; margin: 5px; display: inline-block;'>🥩 Pantalla Asados</a><br>";
echo "<a href='index.php?action=diagnostico' style='background: #ffc107; color: black; padding: 10px; text-decoration: none; margin: 5px; display: inline-block;'>🔧 Diagnóstico</a><br>";

?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}

table {
    background-color: white;
    width: 100%;
    max-width: 800px;
    margin: 10px 0;
}

th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #007bff;
    color: white;
}
</style>
