<!DOCTYPE html>
<html>
    <head>
        <meta charset="UTF-8">
        <title></title>
        <script type="text/javascript">
            function imprimir() {
                if (window.print) {
                    window.print();
                } else {
                    alert("La función de impresion no esta soportada por su navegador.");
                }
            }
        </script>
    </head>
    <body onload="imprimir();">
<?php
ob_start();
session_start();
    //1111111111111111111111111111111111111111111111111111111111
    $producto=$_SESSION["productos"];//array
    $pedido=$_SESSION["pedidos_id"];
	$cajero=$_SESSION["turno_cajero_id"];
	$subtotal=$_SESSION["subtotal"];
	$total=$_SESSION["total"];
	$efectivo=$_SESSION["efectivo"];
	$tarjeta=$_SESSION["tarjeta"];
	$bancolombia=$_SESSION["bancolombia"] ;
	$nequi=$_SESSION["nequi"];
	$daviplata=$_SESSION["daviplata"];
	$cambio=$_SESSION["cambio"];
	$factura=$_SESSION["idFactura"];
	$propina=$_SESSION["propina"];
	$totalCuenta=$total-$propina;
	$subTotal=$totalCuenta/1.08;
	$impoconsumo=$total-$subTotal;
	$mesa=$_SESSION['mesaFactura'];

	$bancolombia=$_SESSION["bancolombia"] ;
	$nequi=$_SESSION["nequi"];
	$daviplata=$_SESSION["daviplata"];

    //restaurante111111111111111111111111111111111111111111111111

	if ($factura>0 and $factura<=1000) {
		$dian1="POS No. 13028003284316";
		$dian2="Fecha 2017-03-02";
		$dian3="autorizada del 1 al 1000";
	}else if ($factura>1000) {
		$dian1="POS No. 18762002707283";
		$dian2="Fecha 2017-03-27";
		$dian3="autorizada del 1001 al 50000.";
	}

	setlocale(LC_MONETARY, 'en_US');
	///////////////////////////////////////////////
	/////////////////////////////////////////////////// <img name ="lagarto" src="http://i.imgur.com/afC0L.jpg" alt="Notepad++" title="Notepad++ editor de texto">

$content='<center><div style="font-family: Consolas,monaco,monospace; align:center; width:260;">Hostal La Macarena</b> </div>
	<div style="font-family: Consolas,monaco,monospace; align:center; width:220;">	N.I.T. # 50937594-7 <br></div>
	<div style="font-family: Consolas,monaco,monospace; align:center; width:220;">	 Regimen NO RESPONSABLE DE IVA</div><br>
	<div  style="font-family: Consolas,monaco,monospace;  align:Right; width:260;">
	Factura No: '.$factura.' <br> Fecha :'.date('d/m/Y').'<br> <br></div>
	<div style="font-family: Consolas,monaco,monospace;  align:center; width:220;" >Km 27 700M LA REVUELTA
  <br>tel : +57 301 7475781 <!--************--> </div>
	<div style="font-family: Consolas,monaco,monospace;  align:center; width:220;" ></div>
	<hr>
		<table cellspacing="0" cellpadding="0"  style="font-family: Consolas,monaco,monospace; width:220; border:1px solid black;" >
			<thead>
				<tr>
					<td colspan="2"> Cod cajero:</td> <td colspan="2">'.$cajero.'</td>
				</tr>
				<tr>
					<td colspan="2"> <b>Mesa:</b></td> <td colspan="2"><b>'.$mesa.'</b></td>
				</tr>
				<tr>
					<td style="border:1px dotted black; cellspacing:2" >Produc</td>
					<!--<td>Prec</td>-->
					<td   align="center">Cant</td>
					<td style="border:1px dotted black;"   align="right">Total</td>
				</tr>
			</thead>
			<tbody>';
				$lista =$_SESSION["productos"];
				//echo "<script>alert('Controle Usuario ".$lista[0]["codigo"]." ');</script>";&nbsp;&nbsp;&nbsp;
				for ($i=0; $i < count($lista) ; $i++)
					{	$des=$lista[$i]["precio"]*($lista[$i]["descuento"]/100);
						//$desT=($lista[$i]["precio"]-$des)*$lista[$i]["cantidad"];
						$desT=$des*$lista[$i]["cantidad"];
						$content.='<tr>
							<td style="border:1px dotted black;" >'.$lista[$i]["nombre"].'</td>
							<!--<td>$'.number_format( $lista[$i]["precio"]).'</td>-->
							<td style="border:1px dotted black;"   align="center">'.$lista[$i]["cantidad"].'</td>
							<td style="border:1px dotted black;"  align="right">$'.number_format( $lista[$i]["total"]).'</td>
						</tr>
						<!--tr>
							<td>Descuento</td>
							<td>$'.number_format($lista[$i]["descuento"]).'%</td>
							<td>$'.number_format($des).'</td>
							<td>$'.number_format($desT).'</td>

						</tr-->'
						;
					}
					$content.='

							<tr>
								<td colspan="1" >Sub Total </td>
								<td colspan="2" align="right">$'.number_format( $subTotal).'</td>
							</tr>
							<tr>
								<td colspan="1" >Ipoconsumo </td>
								<td colspan="2" align="right">$'.number_format($impoconsumo ).'</td>
							</tr>
							<tr><td colspan="3">____________________________</td></tr>
							<tr>
								<td colspan="1" >Total </td>
								<td colspan="2" align="right">$'.number_format( $totalCuenta).'</td>
							</tr>
							<tr>
								<td colspan="1" >Propina</td>
								<td colspan="2" align="right">$'.number_format( $propina).'</td>
							</tr>
							<tr><td colspan="3">____________________________</td></tr>
							<tr>
								<td colspan1="1" >Total a pagar</td>
								<td colspan="2" align="right"><b>$'.number_format( $total).'</b></td>
							</tr>';
				if ($efectivo>0 && $tarjeta>0)
				 {
					$content.='
						  <tr>
								<td colspan="1" >Efectivo</td>
								<td colspan="2" align="right">$'.number_format( $efectivo).'</td>
						  </tr>
						  <tr>
								<td colspan="1" >Bold</td>
								<td colspan="2" align="right" >$'.number_format( $tarjeta).'</td>
						  </tr>
						  <tr>
								<td colspan="1" >Nequi</td>
								<td colspan="2" align="right" >$'.number_format( $nequi).'</td>
						  </tr>
						  <tr>
								<td colspan="1" >Daviplata</td>
								<td colspan="2" align="right" >$'.number_format( $daviplata).'</td>
						  </tr>';

				 }
				else
				 {
				 	if ($efectivo>0)
					 {
						$content.='<tr>
									<td colspan="1" >Efectivo</td>
									<td colspan="2" align="right">$'.number_format( $efectivo).'</td>
							  </tr>';
					 }
					else
					 {
					 	if ($tarjeta>0)
						 {
							$content.='<tr>
										<td colspan="1" >Tarjeta '.$tipoTarjeta.' </td>
										<td colspan="2" align="right">$'.number_format( $tarjeta).'</td>
								  </tr>';
						 }
					 }
				 }

$content.='<tr>
			<td colspan="1" >Cambio </td>
			<td colspan="2" align="right">'.number_format( $cambio).'</td>
		  </tr>
		  <tr>
			<td colspan="1" ><h1>...</h1> </td>
			<td colspan="2" align="right"><h1>&nbsp;</h1></td>
		  </tr>
</table>
<hr>
	<br>

	<div style="font-family: Consolas,monaco,monospace; align:center; width:260;" >
				Numeración de facturación <!--br>

				'.$dian1.'   <br>

				'.$dian2.' <br>

				'.$dian3.' <br>
				<br>
				No se admiten reclamaciones Posteriores <br><br>
				Esta factura cambiaria de compraventa se asimila en sus efectos legales a una letra de cambio segun el
				art 747 del codigo de comercio.
	</div></center-->
	<table > <tr><td></td></tr></table>';
		echo($content);
?>
    </body>
</html>
