<?php
require_once "../../models/crud.php";
require_once "../../models/crudFacturaAja.php";
require_once "../../models/cruddevolucion.php";
require_once "../../models/crudPedidoMesaProducto.php";

require_once "../../controllers/controller.php";
require_once "../../controllers/controllerFacturaAja.php";
require_once "../../controllers/controllerdevolucion.php";
	ini_set("session.cookie_lifetime","28800");
	ini_set("session.gc_maxlifetime","28800");
					//$devolucion -> totalDevolucionController();
if(isset($_POST['factura']))
	{	//session_start();
		$queryString = $_POST['factura'];
		//$_SESSION["idfactura"]= $_POST['factura'];
		echo $queryString;
		$d = new controllerdevolucion();
		$d -> asignarvaribleController();
	 //echo'<br> <script>alert("Entro al ajax");</script> <br>';
		header("location:Devolucion");
	}
else
	{	echo "<script>alert('Error')</script>";	}