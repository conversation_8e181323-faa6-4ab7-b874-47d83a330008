<?php
	if(isset($_GET["action"]))
		{	if($_GET["action"] == "okPm")
			{echo "Registro Exitoso";	}
		}
?>
<script type="text/javascript">
	// ------CANCELA TODO LOS PRODUCTOS PEDIDO EN LA MESA
	function cancelarPedido() 
			{	
				if (confirm('confirme si elimina el pedido?')) 
					{		setTimeout('location.href="views/modules/ajaxCancelarPedido.php"',500);		}			
			//alert("Entro en el else de placa");
				/*$("#destino").load("views/modules/ajaxPermiso.php", function()
					{  	alert("recibidos los datos por ajax Monte");    		 });*/		
			} 
	// ------CANCELA TODO LOS PRODUCTOS PEDIDO EN LA MESA fin 	
		function funcion_calcular()
			{
				var efectivo = document.calculo.efectivo.value;
				var total = document.calculo.total.value;
				/*					
					var precio = document.calculo.precio.value;
					var cantid = document.calculo.cantid.value;
					var importe = document.calculo.importe.value;
				*/
				var calculo = efectivo-total;
				//alert ('El cambio: ' + calculo);
				document.calculo.cambio.value = calculo;
			}
	
	// ------CAMBIO O VUELTOS 
		function cambio() 
		 {	//alert("Vamos bien");
					var total=document.getElementById("total");
					var pago = document.getElementById("efectivo");
					var r= 0;
					r=pago.value-total.value;
					alert("El cambio es="+pago.value);
					document.getElementById("cambio").value=r;
		 }  
	// ------CANCELA TODO LOS PRODUCTOS PEDIDO EN LA MESA fin
	
	// ------FACTURAR--------------
		function facturar() 		
			{	
				var pago = document.calculo.pago.value;
				//var pcedula = document.calculo.pcedula.value;
				var total = document.calculo.total.value;
				if (pago == 1) 
					{	
						var efectivo = document.calculo.efectivo.value;
						/*if (total <= efectivo) 
							{*/
								if (confirm('confirme Facturar Pagada?')) 
									{		//setTimeout('location.href="views/modules/ajaxFactura.php"',500);	
												$("#destino").load("views/modules/ajaxFactura.php", {efectivo: efectivo,pago: pago, }, function(){
				        						 //alert("recibidos los datos por ajax efectivo"); 		 
				     						});	
									}	
							/*}
						else { alert("Verifique el valor digitado, es menor que el total de la cuenta "); }
							*/
					}
				else
					{
						if (confirm('confirme Facturar Acredito?')) 
							{		//setTimeout('location.href="views/modules/ajaxFactura.php"',500);	
										$("#destino").load("views/modules/ajaxFactura.php", {pago: pago, pcedula:pcedula}, function(){
		        						 //alert("recibidos los datos por ajax efectivo"); 		 
		     						});	
							}
					}			
			} 
	// ------FACTURAR FIN//		".$_SESSION["mesa"]."
	
	// ------buscarCliente--------------
		function buscarCliente() 		
			{	
				//alert("Buscar factura funtion"); 
				var pcedula = document.calculo.pcedula.value;
				if (confirm('confirme Facturar Buscar?')) 
									{		//setTimeout('location.href="views/modules/ajaxFactura.php"',500);	
												$("#cliente").load("views/modules/ajaxBuscarCedula.php", {pcedula: pcedula}, function(){
				        						 //alert("recibidos los datos por ajax efectivo"); 		 
				     						});	
									}
			} 
	// ------buscarCliente FIN//							onkeypress="buscarCliente();"					
</script>	
	 <div class="row">
	      <div class="col-md-8">	      	
	      	<form name="calculo" method="post" >	
			  	<div ><h2>PEDIDO </h2></div><br>
				<!-- Cliente No Cedula:  <input type="text" name="pcedula" value="1" id="pcedula" >&nbsp;
				<a  role="button" onclick="buscarCliente();" name="factura" value="factura">&laquo; verificar </a> &nbsp;&nbsp; 
				<a href="registro" target="_blank"> Registrar Cliente &raquo;</a>	<div id="cliente"></div><br>-->
			  	<input type="hidden"  name="mesa" value="1"> 
			  	<input type="hidden"  name="pago" id="pago" value="1">
			<!-- 	<select name="pago" id="pago">
				  <option value="1">Pago</option>
				  <option value="2">Credito</option>
				</select> --> 
				<table border="1" class="table table-hover">				
					<thead>					
						<tr>						
							<th>PRODUCTOS COD.</th>				
							<th>PRODUCTOS</th>	
							<th>FECHA</th>				
							<th>PRECIO</th>				
							<th>CANTIDAD</th>				
							<th>TOTAL</th>												
							
						</tr>
					</thead>
					<tbody>					
						<?php
							$vistaPmesa = new controllerPedidoMesaVendido();
							$vistaPmesa -> vistaPmesaController();
							$vistaPmesa -> borrarPmesaController();			
						?>
						<tr>
							<td colspan="5"><h4>Recibo en efectivo  </h4></td> 
							<td colspan="3"><h4><input type="text" placeholder="$" name="efectivo" id="efectivo" onkeyup="funcion_calcular();" required>  </h4></td> 			
						</tr>
						<tr> 							
							<td colspan="5"><h4>Cambio  </h4></td> 
							<td colspan="3"><h4><input type="text" placeholder="$" name="cambio" id="cambio" required>  </h4></td>
						</tr>
					</tbody>
				</table>
				<span id="destino" > </span>
				<p><a class="btn btn-primary btn-lg"  role="button"  onclick="cancelarPedido();" name="cancelar" value="cancelar">Cancelar &raquo;</a>
				<a class="btn btn-primary btn-lg"  role="button" onclick="facturar();" name="factura" value="factura">Facturar &raquo;</a></p>
				<!--<input type="submit"  value="Facturar">-->
			 </form>
	      </div>
      <div class="col-md-4" style="background-color:#FFC; padding:12px"></p>			
		<form method="post"> 
		 	<div ><h4>Producto </h4></div><br>
			 	<?php 
			 		$registroPmesa = new controllerPedidoMesaVendido();					
					$registroPmesa -> registroPmesaController();
			 	?>				
					<label>  CANTIDAD: </label><input type="text" placeholder="1" name="cantidadPmesaRegistro" value="1" required><br>
					<label>  PRODUCTO: </label><input type="text" placeholder="PRODUCTO" name="codigo" required><br>				
					<input type="hidden" placeholder="MESA" name="mesas_idPmesaRegistro" value="1" >														
				<input type="submit" value="Enviar">
				<br><br><br><br><br><br>
				<br><br><br><br><br><br>
				<br><br><br><br><br><br>
				<br><br><br><br><br><br>
			</div>	
		</form>
     </div>
  