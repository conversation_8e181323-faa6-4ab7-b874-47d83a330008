<?php
session_start();

// Simular sesión de administrador para pruebas
if (!isset($_SESSION["usuario"])) {
    $_SESSION["usuario"] = 1;
    $_SESSION["tipo_usuario"] = 1;
    $_SESSION["perfil"] = "administrador";
}

echo "<h2>🧪 Test Agrupación de Productos - Facturación Real</h2>";

// Incluir archivos necesarios
require_once "../../models/conexion.php";
require_once "../../models/crudFacturaAja.php";

$mesa_test = isset($_GET['mesa']) ? $_GET['mesa'] : 47;

echo "<p><strong>Mesa de prueba:</strong> $mesa_test</p>";

try {
    echo "<h3>1. 📋 Productos Antes de Facturar (Método Corregido)</h3>";
    
    $productos = DatosFacturaAja::obtenerProductosParaFacturar($mesa_test);
    
    if (!empty($productos)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>Producto</th><th>Cantidad Total</th><th>Precio Unit.</th><th>Descuento</th><th>Total</th><th>Pedidos Origen</th>";
        echo "</tr>";
        
        $total_calculado = 0;
        $total_productos = 0;
        
        foreach ($productos as $prod) {
            $descuento = ($prod["preciopr"] * ($prod["descuentopvm"] / 100));
            $precio_con_descuento = $prod["preciopr"] - $descuento;
            $subtotal = $precio_con_descuento * $prod["cantidadpvm"];
            $total_calculado += $subtotal;
            $total_productos += $prod["cantidadpvm"];
            
            echo "<tr>";
            echo "<td>{$prod['nombrepr']}</td>";
            echo "<td style='text-align: center; background: #e8f5e8;'><strong>{$prod['cantidadpvm']}</strong></td>";
            echo "<td style='text-align: right;'>$" . number_format($prod['preciopr']) . "</td>";
            echo "<td style='text-align: right;'>$" . number_format($descuento) . "</td>";
            echo "<td style='text-align: right; background: #fff3cd;'>$" . number_format($subtotal) . "</td>";
            echo "<td style='font-size: 11px;'>{$prod['numero_pedido']}</td>";
            echo "</tr>";
        }
        
        echo "<tr style='background: #4caf50; color: white; font-weight: bold;'>";
        echo "<td>TOTAL</td>";
        echo "<td style='text-align: center;'>$total_productos</td>";
        echo "<td colspan='2'></td>";
        echo "<td style='text-align: right;'>$" . number_format($total_calculado) . "</td>";
        echo "<td></td>";
        echo "</tr>";
        echo "</table>";
        
        echo "<div style='background: #c8e6c9; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<strong>✅ RESUMEN:</strong><br>";
        echo "• <strong>Productos únicos:</strong> " . count($productos) . "<br>";
        echo "• <strong>Cantidad total:</strong> $total_productos unidades<br>";
        echo "• <strong>Total a facturar:</strong> $" . number_format($total_calculado) . "<br>";
        echo "</div>";
        
        // Simular facturación
        echo "<h3>2. 🧾 Simular Facturación</h3>";
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>Datos de Facturación Simulada:</h4>";
        echo "<ul>";
        echo "<li><strong>Mesa:</strong> $mesa_test</li>";
        echo "<li><strong>Cliente:</strong> 12345678 (Cliente de prueba)</li>";
        echo "<li><strong>Total:</strong> $" . number_format($total_calculado) . "</li>";
        echo "<li><strong>Efectivo:</strong> $" . number_format($total_calculado + 5000) . "</li>";
        echo "<li><strong>Cambio:</strong> $5,000</li>";
        echo "</ul>";
        
        echo "<button onclick='simularFacturacion()' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;'>💰 Simular Facturación</button>";
        echo "</div>";
        
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<strong>❌ No hay productos para facturar en la mesa $mesa_test</strong><br>";
        echo "Asegúrate de que la mesa tenga pedidos en estado 'enviado' o 'entregado'.";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>❌ Error:</strong> " . $e->getMessage();
    echo "</div>";
}

?>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
function simularFacturacion() {
    if (confirm('🧪 ¿Simular facturación de la mesa <?=$mesa_test?>?\n\nEsto creará una factura real con los productos agrupados correctamente.')) {
        
        // Datos de facturación simulada
        const datosFactura = {
            efectivo: <?=$total_calculado + 5000?>,
            nequi: 0,
            daviplata: 0,
            bancolombia: 0,
            tarjeta: 0,
            pago: 1,
            pcedula: '12345678',
            propina: 0,
            mesa: <?=$mesa_test?>,
            total: <?=$total_calculado?>,
            totalDescuento: 0,
            tipoTarjeta: 'credito',
            optimizada: true
        };
        
        // Mostrar indicador de carga
        document.body.innerHTML += '<div id="loading" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); color: white; display: flex; align-items: center; justify-content: center; font-size: 20px; z-index: 9999;">⏳ Procesando facturación...</div>';
        
        $.ajax({
            url: "ajaxFactura.php",
            type: "POST",
            data: datosFactura,
            timeout: 60000,
            success: function(response) {
                document.getElementById('loading').remove();
                console.log('Respuesta facturación:', response);
                
                if (response.includes('success') || response.includes('Facturación completada')) {
                    alert('✅ Facturación simulada completada exitosamente!\n\n📄 Revisa la nueva factura para verificar que los productos estén agrupados correctamente.');
                    setTimeout(() => location.reload(), 2000);
                } else {
                    alert('❌ Error en la facturación:\n\n' + response);
                }
            },
            error: function(xhr, status, error) {
                document.getElementById('loading').remove();
                console.error('Error AJAX:', error);
                alert('❌ Error de conexión: ' + error);
            }
        });
    }
}
</script>

<div style="margin: 20px 0; padding: 15px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px;">
    <h4>🔧 Corrección Implementada</h4>
    <p><strong>Problema identificado:</strong> Los productos iguales no se agrupaban durante la facturación, creando múltiples registros separados en lugar de un solo registro con la cantidad total.</p>
    <p><strong>Solución aplicada:</strong> Modificado el método <code>obtenerProductosParaFacturar()</code> para usar <code>GROUP BY</code> y <code>SUM(cantidad)</code>, agrupando productos iguales automáticamente.</p>
    <p><strong>Resultado esperado:</strong> Las facturas ahora deben mostrar productos agrupados correctamente, con totales que coinciden entre la tabla de productos y el total de la factura.</p>
</div>
