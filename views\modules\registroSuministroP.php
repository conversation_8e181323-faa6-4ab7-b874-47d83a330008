<?php

	$registroSuministroP = new controllerSuministroProducto();
	$productos = $registroSuministroP -> listaProductosController();
	$suministros = $registroSuministroP -> listaSuministrosController();
?>

<h1>PRODUCTOS Y SUMINISTROS</h1>
<form method="post">
	<label> Nombre Producto</label>
	<!--input type="text" placeholder="proveedor_id" name="proveedor_idCsuministroRegistro" required><br-->
	<?php  
# 	# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  productos  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%% producto_id, id, cantidades
	if($productos=="error")
		{	echo "debe registrar el productos"; }
	else{
			echo "<label>Id productos </label>";
			$result='<select name="productos"  id="productos">';
			$result.=' <option value="-1">Seleccione una productos</option>';
			foreach ($productos as $row => $item)
			 	{	$result.=' <option value="'.$item["id"].'">'.$item["nombre"].'</option>';	}	
			 $result.='</select>';
			 echo $result." <br>";
		}
# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  End productos  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%% 
# 	# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  suministros  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
	if($suministros=="error")
		{	echo "debe registrar el suministros"; }
	else{
			echo "<label>Id suministros </label>";
			$result='<select name="suministros"  id="suministros">';
			$result.=' <option value="-1">Seleccione una suministros</option>';
			foreach ($suministros as $row => $item)
			 	{	$result.=' <option value="'.$item["id"].'">'.$item["nombre"].'</option>';	}	
			 $result.='</select>';
			 echo $result." <br>";
		}
# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  End suministros  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
 ?>	
	<label> cantidades: </label>
	<input type="text" placeholder="cantidades" name="cantidades" required><br>	
	<input type="submit" value="Enviar">

</form>
<?php 
	$registroSuministroP -> registroSuministroPController();

	if(isset($_GET["action"]))
		{	
			if($_GET["action"] == "okSP")
			{echo "Registro Exitoso";	}

		}
?>