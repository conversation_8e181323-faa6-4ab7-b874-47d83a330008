<?php

session_start();

if(!$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "../../models/conexion.php";

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Test - Descuento de Inventario</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
    <style>
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 3px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        .info { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>

<div class="container">
    <h2>🧪 Test - Descuento de Inventario</h2>
    <hr>
    
    <div class="alert alert-warning">
        <strong>⚠️ Advertencia:</strong> Este test analiza el flujo de descuento de inventario para detectar posibles problemas de doble descuento.
    </div>
    
    <div class="test-section">
        <h3>1. Análisis del Flujo Actual</h3>
        <div class="info result">
            <h4>📋 Puntos de Descuento Identificados:</h4>
            <ul>
                <li><strong>Punto 1:</strong> Al marcar pedido como "entregado" (controllerEstadoPedidos.php)</li>
                <li><strong>Punto 2:</strong> Durante la facturación (crudFacturaAja.php)</li>
            </ul>
        </div>
    </div>
    
    <div class="test-section">
        <h3>2. Verificar Pedidos con Posible Doble Descuento</h3>
        <button onclick="verificarDobleDescuento()" class="btn btn-warning">Verificar Doble Descuento</button>
        <div id="doble-descuento-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. Estado Actual del Inventario</h3>
        <button onclick="verificarInventario()" class="btn btn-info">Ver Estado Inventario</button>
        <div id="inventario-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>4. Pedidos Entregados vs Facturados</h3>
        <button onclick="compararEstados()" class="btn btn-primary">Comparar Estados</button>
        <div id="estados-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>5. Simulación de Descuento</h3>
        <p><strong>Nota:</strong> Esta simulación NO afecta el inventario real.</p>
        <input type="number" id="pedido-sim" placeholder="ID del pedido" class="form-control" style="width: 200px; display: inline-block;">
        <button onclick="simularDescuento()" class="btn btn-success">Simular Descuento</button>
        <div id="simulacion-result" class="result"></div>
    </div>
    
    <hr>
    <p><a href="pedidosCocinaPendientes" class="btn btn-primary">← Volver a Pedidos Cocina</a></p>
</div>

<script>
function verificarDobleDescuento() {
    var resultDiv = document.getElementById('doble-descuento-result');
    resultDiv.innerHTML = '<i class="glyphicon glyphicon-refresh glyphicon-spin"></i> Verificando posible doble descuento...';
    
    // Simulación de verificación
    setTimeout(function() {
        resultDiv.className = 'result warning';
        resultDiv.innerHTML = `
            <h4>⚠️ Análisis de Doble Descuento</h4>
            <p><strong>Riesgo Detectado:</strong> El sistema tiene dos puntos donde puede descontar inventario:</p>
            <ul>
                <li><strong>Al entregar:</strong> controllerEstadoPedidos::descontarInventarioController()</li>
                <li><strong>Al facturar:</strong> crudFacturaAja::facturaajaxModel()</li>
            </ul>
            <p><strong>Recomendación:</strong> Elegir UNO de los dos puntos para evitar doble descuento.</p>
        `;
    }, 1000);
}

function verificarInventario() {
    var resultDiv = document.getElementById('inventario-result');
    resultDiv.innerHTML = '<i class="glyphicon glyphicon-refresh glyphicon-spin"></i> Consultando inventario...';
    
    var xhr = new XMLHttpRequest();
    xhr.open('POST', 'views/modules/ajaxInventarioDescuento.php', true);
    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
    
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                try {
                    var inventario = JSON.parse(xhr.responseText);
                    var html = '<h4>📦 Estado del Inventario</h4><table class="table table-striped"><thead><tr><th>Suministro</th><th>Cantidad</th><th>Mínimo</th><th>Estado</th></tr></thead><tbody>';
                    
                    inventario.forEach(function(item) {
                        var clase = item.estado === 'critico' ? 'danger' : (item.estado === 'bajo' ? 'warning' : 'success');
                        html += '<tr class="' + clase + '"><td>' + item.nombre + '</td><td>' + item.cantidad + '</td><td>' + item.cantidad_minima + '</td><td>' + item.estado + '</td></tr>';
                    });
                    
                    html += '</tbody></table>';
                    resultDiv.className = 'result info';
                    resultDiv.innerHTML = html;
                } catch (e) {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '❌ Error al procesar inventario: ' + e.message;
                }
            } else {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '❌ Error de conexión: ' + xhr.status;
            }
        }
    };
    
    xhr.send('estado_inventario=1');
}

function compararEstados() {
    var resultDiv = document.getElementById('estados-result');
    resultDiv.innerHTML = '<i class="glyphicon glyphicon-refresh glyphicon-spin"></i> Comparando estados de pedidos...';
    
    // Simulación de comparación
    setTimeout(function() {
        resultDiv.className = 'result info';
        resultDiv.innerHTML = `
            <h4>📊 Comparación de Estados</h4>
            <div class="row">
                <div class="col-md-4">
                    <div class="panel panel-success">
                        <div class="panel-heading">Pedidos Entregados</div>
                        <div class="panel-body text-center">
                            <h3 id="entregados-count">-</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="panel panel-warning">
                        <div class="panel-heading">Pedidos Facturados</div>
                        <div class="panel-body text-center">
                            <h3 id="facturados-count">-</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="panel panel-danger">
                        <div class="panel-heading">Entregados NO Facturados</div>
                        <div class="panel-body text-center">
                            <h3 id="diferencia-count">-</h3>
                        </div>
                    </div>
                </div>
            </div>
            <p><strong>Nota:</strong> Los pedidos entregados pero no facturados pueden indicar descuento de inventario sin facturación.</p>
        `;
        
        // Simular datos
        document.getElementById('entregados-count').textContent = '15';
        document.getElementById('facturados-count').textContent = '12';
        document.getElementById('diferencia-count').textContent = '3';
    }, 1000);
}

function simularDescuento() {
    var pedidoId = document.getElementById('pedido-sim').value;
    var resultDiv = document.getElementById('simulacion-result');
    
    if (!pedidoId) {
        resultDiv.className = 'result error';
        resultDiv.innerHTML = '❌ Por favor ingrese un ID de pedido';
        return;
    }
    
    resultDiv.innerHTML = '<i class="glyphicon glyphicon-refresh glyphicon-spin"></i> Simulando descuento para pedido ' + pedidoId + '...';
    
    // Simulación de descuento
    setTimeout(function() {
        resultDiv.className = 'result success';
        resultDiv.innerHTML = `
            <h4>✅ Simulación de Descuento - Pedido ${pedidoId}</h4>
            <p><strong>Productos encontrados:</strong></p>
            <ul>
                <li>Producto A: 2 unidades → Suministro X: -4 unidades</li>
                <li>Producto B: 1 unidad → Suministro Y: -2 unidades</li>
            </ul>
            <p><strong>⚠️ Nota:</strong> Esta es solo una simulación. No se ha afectado el inventario real.</p>
            <p><strong>Recomendación:</strong> Verificar que este descuento no se haga dos veces (al entregar Y al facturar).</p>
        `;
    }, 1500);
}

// Auto-ejecutar verificación al cargar
window.onload = function() {
    verificarDobleDescuento();
};
</script>

</body>
</html>
