<?php

#EXTENSIÓN DE CLASES: Los objetos pueden ser extendidos, y pueden heredar propiedades y métodos. Para definir una clase como extensión, debo definir una clase padre, y se utiliza dentro de una clase hija.

require_once "conexion.php";
require_once "crudSuministro.php";

class DatosFacturaCompraSuministro extends Conexion
{
	#REGISTRO DE FACTURA COMPRA SUMINISTRO tabla pendiente
	#-------------------------------------------------------
	 public static function registroFacturaSumistroPModel($datosModel, $tabla)
		{//echo "<script>alert('Entro CRUD ".$datosModel['fecha_hora']." no')</script>";
			//(id, proveedor_id, valor_factura, fecha_factura, numero_factura, pago, abono, suministro_id, cantidad, precio, precioV, descuento)
		 $consulta="INSERT INTO $tabla (proveedor_id, valor_factura, fecha_factura, numero_factura, pago, abono, suministro_id, cantidad, precio, precioV, descuento, precioU, iva)
		 VALUES (".$datosModel['proveedor_id'].", ".$datosModel['valor_factura'].", '".$datosModel['fecha_factura']."', '".$datosModel['numero_factura']."', ".$datosModel['pago'].", ".$datosModel['abono'].", ".$datosModel['suministro_id'].", ".$datosModel['cantidad'].", ".$datosModel['precio'].", 0, ".$datosModel['descuento'].", ".$datosModel['precioU'].", ".$datosModel['iva'].")";
		// echo "<br> --".$consulta."--<br> ";
		 $stmt = Conexion::conectar();
		 if($stmt->exec($consulta))
			{	//echo "<script>alert('entro al if de CRUD')</script>";
				return "success";
			}
		 else{ /*echo "<script>alert('entro al no entro al  if pq ??')</script>";*/ return "error";	}
		 $stmt->close();
		}
	#-------------------------------------------------------
	#REGISTRO DE FACTURA COMPRA SUMINISTRO
	#-------------------------------------------------------
	 public static function registroFacturaSumistroModel($datosModel, $tabla)
		{//echo "<script>alert('Entro CRUD ".$datosModel['fecha_hora']." no')</script>";
			//$consulta="INSERT INTO $tabla (suministro_id, compra_suministro_id, cantidad, precio) VALUES (:suministro_id, :compra_suministro_id, :cantidad, :precio)";
			$consulta="INSERT INTO $tabla (suministro_id, compra_suministro_id, cantidad, precio, descuento) VALUES (".$datosModel['suministro_id'].", ".$datosModel['compra_id'].", ".$datosModel['cantidad'].", ".$datosModel['precio'].", ".$datosModel['descuento'].")";
			//echo "<script>alert('Entro CRUD ".$consulta." no')</script>";
			//$stmt = Conexion::conectar()->prepare($consulta);
			$stmt = Conexion::conectar();
			/*$stmt->execute();
			$stmt->bindParam(":suministro_id", $datosModel['suministro_id'], PDO::PARAM_INT);
			$stmt->bindParam(":compra_suministro_id", $datosModel['compra_id'], PDO::PARAM_INT);
			$stmt->bindParam(":cantidad", $datosModel['cantidad'], PDO::PARAM_INT);
			$stmt->bindParam(":precio", $datosModel['precio'], PDO::PARAM_INT);
			//echo "<script>alert('Guardo')</script>";
			//$ultimo_id=$stmt->lastInsertId();
			*/
		 if($stmt->exec($consulta))
			{	//echo "<script>alert('entro al if de CRUD')</script>";
				return "success";
			}
		 else{ /*echo "<script>alert('entro al no entro al  if pq ??')</script>";*/ return "error";	}
		 $stmt->close();
		}
	#-------------------------------------------------------
	#Pendiente //(id, proveedor_id, valor_factura, fecha_factura, numero_factura, pago, abono, suministro_id, cantidad, precio, descuento, facturado)
	#------------------------------------------------------
	 public static function pendiente_factura($id)
		{//echo "<script>alert('Entro crud ".$id." ')</script>";
			$consulta="SELECT pf.id AS pfid, pf.proveedor_id AS pfproveedor_id, pf.valor_factura AS pfvalor_factura, pf.fecha_factura AS pffecha_factura, pf.numero_factura AS pfnumero_factura, pf.pago AS pfpago, pf.abono AS pfabono, pf.suministro_id AS pfsuministro_id, pf.cantidad AS pfcantidad, pf.precio AS pfprecio, pf.descuento AS pfdescuento, pf.facturado AS pffacturado, pf.iva AS pfiva,
				s.id As sid, s.codigo AS scodigo, s.nombre AS snombre
			from pendiente_factura pf, suministros s
			where s.id=pf.suministro_id AND pf.cantidad>0 AND pf.proveedor_id=:id";
			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->bindParam(":id", $id, PDO::PARAM_INT);
			//echo "<br>".$consulta."<br>";
			$stmt->execute();
			$r=$stmt->fetchAll();
			return $r;
			$stmt->close();
		}
	#-------------------------------------------------------
	#BUSCAR ID SUMINISTRO tabla pendiente factura no repetir suministro
	#--------------------------------------------------------------------------------
	 public static function bucarIdSuministroPModel($codigo)
		{
			$consulta="SELECT suministro_id from pendiente_factura where suministro_id=:codigo ";
			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->bindParam(":codigo", $codigo, PDO::PARAM_INT);
			$stmt->execute();
			$r= $stmt->fetch();
		 $c2=$stmt->rowCount();
		 //echo '<br> C2='.$c2.'<br>';
		 if ($c2>0)
		  	{ 	//echo "<script>alert(' if entro consulta ".$r['dcuenta'] ."')</script>";
		  	 return $r;
		  	}
		 else
		 	{//echo "<script>alert(' Error BD Buscar deuda')</script>";
		 	 return 0;
		    }
		 $stmt->close();
		}
	#-------------------------------------------------------
	#BUSCAR cantidad ID SUMINISTRO tabla Sucursal para promedio en compras
	#--------------------------------------------------------------------------------
	 public static function bucarCantidadSuministroSucursalModel($codigo)
		{
			$consulta="SELECT sum(cantidad) as sucantidad FROM sucursal WHERE suministro_id=:codigo ";
			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->bindParam(":codigo", $codigo, PDO::PARAM_INT);
			$stmt->execute();
			$r= $stmt->fetch();
		 $c2=$stmt->rowCount();
		 //echo '<br> C2='.$c2.'<br>';
		 if ($c2>0)
		  	{ 	//echo "<script>alert(' if entro consulta ".$r['dcuenta'] ."')</script>";
		  	 return $r;
		  	}
		 else
		 	{//echo "<script>alert(' Error BD Buscar deuda')</script>";
		 	 return 0;
		    }
		 $stmt->close();
		}
	#-------------------------------------------------------
	#BUSCAR ID SUMINISTRO
	#--------------------------------------------------------------------------------
		public static function bucarIdSuministroModel($codigo)
			{
				$consulta="SELECT id from suministros where codigo=:codigo ";
				$stmt = Conexion::conectar()->prepare($consulta);
				$stmt->bindParam(":codigo", $codigo, PDO::PARAM_INT);
				$stmt->execute();
				$rows=$stmt->fetch();
				return $rows['id'];
				$stmt->close();
			}
	#-------------------------------------------------------
	#VISTA FACTURA COMPRA SUMINISTRO
	#------------------------------------------------------------------
	 public static function vistaFacturaSumistroModel($compraId)
		{	//echo "<script>alert('entro cruz  factura No".$compraId." guardar');</script>";
			$consulta="SELECT s.id as sid, s.codigo AS scodigo, s.nombre as snombre, fs.cantidad as fscantidad, fs.precio as fsprecio, fs.descuento AS fsdescuento, fs.facturado AS fsfacturado,
			cs.id as csid, cs.numero_factura_compra as csnf, cs.valor_compra as csvalor
			FROM suministros s, detalle_factura_suministro fs, compras_suministros cs WHERE cs.id=fs.compra_suministro_id AND s.id=fs.suministro_id AND fs.compra_suministro_id=:id";
			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->bindParam(":id", $compraId, PDO::PARAM_INT);
			$stmt->execute();

			return $stmt->fetchAll();
			$stmt->close();
		}
	#---------------------------------------------

	#CONSULTAR DETALLE COMPRA SUMINISTRO SEGUN EL ID COMPRA SUMINISTRO
	#------------------------------------------
		public static function actualizarSuministroFModen($dato)
			{//echo "<script>alert('entro cruz 2 factura".$dato." guardar');</script>";
				$consulta="SELECT suministro_id, compra_suministro_id, cantidad, precio, descuento, facturado FROM detalle_factura_suministro WHERE compra_suministro_id = :id ";
				$stmt = Conexion::conectar()->prepare($consulta);
				$stmt->bindParam(":id", $dato, PDO::PARAM_INT);
				if($stmt->execute())
				{	return $stmt->fetchAll();	}
			else{	return "error";		}
			$stmt->close();
			}
	#------------------------------------------
	#ACTUALIZAR FACTURA SUMINISTRO  boveda y vaciar pendiente
	#------------------------------------------
	 public static function actualizarFacturaSuministroPfModen($dato)
		{//echo "<script>alert('entro cruz guardar');</script>";
		 $c=0;
		 $suministroFactura = DatosFacturaCompraSuministro::pendiente_factura($dato);
		 //echo "<script>alert('primera consulta ".$suministroFactura['precio'] ." registros');</script>";
		 foreach ($suministroFactura as $row => $item)
			{	//bien echo "<script>alert('entro al foeach ".$item["suministro_id"]." ');</script>";
				$suministro=DatosSuministro::editarSuministroModel($item["suministro_id"], "suministros");
				$listaS[$c] = array('id' => $suministro['sid'],
									'nombre' => $suministro['snombre'],
									'cantidad' => $suministro['scantidad'],
									'precio' => $suministro['sprecio']
								);
				//echo "<script>alert('suministro Array ID = ".$listaS[$c]['id']." - PRECIO = ".$listas[$c]['precio']."- CANTIDAD = ".$listaS[$c]['cantidad']."Array ".$c."');</script>";
				$c++;
			}
			$stmt = Conexion::conectar();
		 try
			{	//echo "<script>alert('Entro  try'  );</script>";
			 $stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
			 $stmt->beginTransaction();
			 $i=0;
			 echo "<script>alert('Entro  try '   );</script>";
			 foreach ($suministroFactura as $row => $item)
				{		//echo "<script>alert('Entro  try for - ".$item['id']." vez ".$i." y precio=".$item['precio']." cantidad =".$item['cantidad']."'  );</script>";
				 if ($item['suministro_id']==$listaS[$i]['id'] && $item['facturado']=='no' )
					{	//echo "<script>alert(' If- ".$item['id']." vez ".$i." y precio=".$item['precio']." cantidad =".$item['cantidad']."'  );</script>";
						$cantidad = 0;
						$precio = 0;
						$IVA=16/100;
						$desc=$item['precio']* $item['descuento']/100;
						$desTotal=$item['precio']-$desc;
						$IVAtotal=$desTotal+$desTotal*$IVA;
						$unidad=$IVAtotal/$item['cantidad'];
						$cantidad = $listaS[$i]['cantidad'] + $item['cantidad'];
						$precio = $unidad;
						$factura = $listaS[$i]['id'];
						$consulta="UPDATE suministros SET  cantidad= ".$cantidad.", precio = ".$precio."  WHERE id = ".$factura;
						//echo "<script>alert('Consulta ".$consulta." ' );</script>";
						echo "<br>consulta1".$consulta."<br>";
						$stmt->exec($consulta);
						$consulta1="UPDATE detalle_factura_suministro SET  facturado= 'si'  WHERE suministro_id = ".$factura;
						//echo "<script>alert('Consulta ".$consulta." ' );</script>";
						echo "<br>consulta1".$consulta1."<br>";
						$stmt->exec($consulta1);
						$i++;
						//sleep(5);
					}
				 else {echo "<script>alert('Ya esta guardado ')</script>";}
				}
			 //echo "<script>alert('fin de cruz try ')</script>";
			 $stmt->commit();
			 return "success";
			 $stmt->close();
			}
		 catch (PDOException $e)
			{	echo "<script>alert('catch entro')</script>";
			 $stmt->rollBack();
			 print "Error!: ".$e->getMessage()."</br>";
			 return "Error!: ".$e->getMessage()."</br>";
			}


			//return "success";
		}

	#------------------------------------------
	#ACTUALIZAR FACTURA SUMINISTRO   id, nombre, cantidad, precio, cantidad_minima
	#------------------------------------------
	 public static function actualizarFacturaSuministroModen($dato)
		{//echo "<script>alert('entro cruz guardar');</script>";
		 $c=0;
		 $suministroFactura = DatosFacturaCompraSuministro::actualizarSuministroFModen($dato);
		 //echo "<script>alert('primera consulta ".$suministroFactura['precio'] ." registros');</script>";
		 foreach ($suministroFactura as $row => $item)
			{	//bien echo "<script>alert('entro al foeach ".$item["suministro_id"]." ');</script>";
				$suministro=DatosSuministro::editarSuministroModel($item["suministro_id"], "suministros");
				$listaS[$c] = array('id' => $suministro['sid'],
									'nombre' => $suministro['snombre'],
									'cantidad' => $suministro['scantidad'],
									'precio' => $suministro['sprecio']
								);
				//echo "<script>alert('suministro Array ID = ".$listaS[$c]['id']." - PRECIO = ".$listas[$c]['precio']."- CANTIDAD = ".$listaS[$c]['cantidad']."Array ".$c."');</script>";
				$c++;
			}
			$stmt = Conexion::conectar();
		 try
			{	//echo "<script>alert('Entro  try'  );</script>";
			 $stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
			 $stmt->beginTransaction();
			 $i=0;
			 echo "<script>alert('Entro  try '   );</script>";
			 foreach ($suministroFactura as $row => $item)
				{		//echo "<script>alert('Entro  try for - ".$item['id']." vez ".$i." y precio=".$item['precio']." cantidad =".$item['cantidad']."'  );</script>";
				 if ($item['suministro_id']==$listaS[$i]['id'] && $item['facturado']=='no' )
					{	//echo "<script>alert(' If- ".$item['id']." vez ".$i." y precio=".$item['precio']." cantidad =".$item['cantidad']."'  );</script>";
						$cantidad = 0;
						$precio = 0;
						$IVA=16/100;
						$desc=$item['precio']* $item['descuento']/100;
						$desTotal=$item['precio']-$desc;
						$IVAtotal=$desTotal+$desTotal*$IVA;
						$unidad=$IVAtotal/$item['cantidad'];
						$cantidad = $listaS[$i]['cantidad'] + $item['cantidad'];
						$precio = $unidad;
						$factura = $listaS[$i]['id'];
						$consulta="UPDATE suministros SET  cantidad= ".$cantidad.", precio = ".$precio."  WHERE id = ".$factura;
						//echo "<script>alert('Consulta ".$consulta." ' );</script>";
						echo "<br>consulta1".$consulta."<br>";
						$stmt->exec($consulta);
						$consulta1="UPDATE detalle_factura_suministro SET  facturado= 'si'  WHERE suministro_id = ".$factura;
						//echo "<script>alert('Consulta ".$consulta." ' );</script>";
						echo "<br>consulta1".$consulta1."<br>";
						$stmt->exec($consulta1);
						$i++;
						//sleep(5);
					}
				 else {echo "<script>alert('Ya esta guardado ')</script>";}
				}
			 //echo "<script>alert('fin de cruz try ')</script>";
			 $stmt->commit();
			 return "success";
			 $stmt->close();
			}
		 catch (PDOException $e)
			{	echo "<script>alert('catch entro')</script>";
			 $stmt->rollBack();
			 print "Error!: ".$e->getMessage()."</br>";
			 return "Error!: ".$e->getMessage()."</br>";
			}


			//return "success";
		}

	#------------------------------------------
	#EDITAR FACTURA COMPRA SUMINISTRO
	#-------------------------------------
	 public static function editarFacturaSumistroModel($datosModel, $tabla)
		{
			$stmt = Conexion::conectar()->prepare("SELECT id, proveedor_id, valor_compra, fecha_hora, numero_factura_compra FROM $tabla WHERE id = :id");
			$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);
			$stmt->execute();
			return $stmt->fetch();
			$stmt->close();
		}
	#--------------------------------------
	#ACTUALIZAR FACTURA COMPRA SUMINISTRO
	#-------------------------------------
	 public static function actualizarFacturaSumistroModel($datosModel, $tabla)
		{ echo "<script>alert('Entro Actualizar Producto')</script>";
			$stmt = Conexion::conectar()->prepare("UPDATE $tabla SET proveedor_id = :proveedor_id, valor_compra =:valor_compra, fecha_hora =:fecha_hora, numero_factura_compra =:numero_factura_compra WHERE id = :id");
			$stmt->bindParam(":proveedor_id", $datosModel["proveedor_id"], PDO::PARAM_STR);
			$stmt->bindParam(":valor_compra", $datosModel["valor_compra"], PDO::PARAM_INT);
			$stmt->bindParam(":fecha_hora", $datosModel["fecha_hora"], PDO::PARAM_INT);
			$stmt->bindParam(":numero_factura_compra", $datosModel["numero_factura_compra"], PDO::PARAM_INT);
			$stmt->bindParam(":id", $datosModel["id"], PDO::PARAM_INT);
			if($stmt->execute())
				{echo "<script>alert('Guardo Actualizar Producto')</script>";return "success";	}
			else{	return "error";			}
			$stmt->close();
		}
	#------------------------------------
	#BORRAR FACTURA COMPRA SUMINISTRO tabla pendiente
	#------------------------------------
		public static function borrarFacturaSumistroPModel($datosModel, $tabla)
		{
			$stmt = Conexion::conectar()->prepare("DELETE FROM $tabla WHERE id = :id");
			$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);
			if($stmt->execute())
				{	return "success";	}
			else{	return "error";		}
			$stmt->close();
		}
	#--------------------------------------------------
	#BORRAR FACTURA COMPRA SUMINISTRO
	#------------------------------------
		public static function borrarFacturaSumistroModel($datosModel, $tabla)
		{
			$stmt = Conexion::conectar()->prepare("DELETE FROM $tabla WHERE id = :id");
			$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);
			if($stmt->execute())
				{	return "success";	}
			else{	return "error";		}
			$stmt->close();
		}
	#--------------------------------------------------
	#EDITAR  SUMINISTRO pendiente
	#------------------------------------
	 public static function editarFacturaSumistroPModel($datosModel, $tabla)
		{	//echo "<script>alert('Editar ".$datosModel["suministro_id"]."   este es ".$datosModel["compra_suministro_id"]."');</script>";
			$consulta ="SELECT suministro_id, cantidad, precio, descuento FROM pendiente_factura WHERE suministro_id=:suministro_id";
			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->bindParam(":suministro_id", $datosModel["suministro_id"], PDO::PARAM_INT);
			$stmt->execute();
			return $stmt->fetch();
			$stmt->close();
		}
	#--------------------------------------
	#EDITAR  SUMINISTRO Cantidad factura
	#------------------------------------
	 public static function editarFacturaSumistroCModel($datosModel, $tabla)
		{	//echo "<script>alert('Editar ".$datosModel["suministro_id"]."   este es ".$datosModel["compra_suministro_id"]."');</script>";
			$consulta ="SELECT suministro_id, compra_suministro_id, cantidad, precio FROM detalle_factura_suministro WHERE suministro_id=:suministro_id AND compra_suministro_id=:compra_suministro_id";
			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->bindParam(":suministro_id", $datosModel["suministro_id"], PDO::PARAM_INT);
			$stmt->bindParam(":compra_suministro_id", $datosModel["compra_suministro_id"], PDO::PARAM_INT);
			$stmt->execute();
			return $stmt->fetch();
			$stmt->close();
		}
	#--------------------------------------
	#ACTUALIZAR CANTIDAD tabla pendiente
	#----------------------------------------------
	 public static function actualizarFacturaSumistroPModel($datosModel, $tabla)
		{
			$consulta="UPDATE pendiente_factura SET cantidad=:cantidad, precio=:precio WHERE suministro_id=:suministro_id ";
			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->bindParam(":suministro_id", $datosModel["suministro_id"], PDO::PARAM_INT);
			$stmt->bindParam(":cantidad", $datosModel["cantidad"], PDO::PARAM_INT);
			$stmt->bindParam(":precio", $datosModel["precio"], PDO::PARAM_INT);

			if($stmt->execute())
				{	return "success";	}
			else{ return "error";		}
			$stmt->close();
		}
	#-------------------------------------------------
	#ACTUALIZAR CANTIDAD y precio de factura
	#----------------------------------------------
	 public static function actualizarFacturaSumistroCModel($datosModel, $tabla)
		{
			$consulta="UPDATE detalle_factura_suministro SET cantidad=:cantidad, precio=:precio WHERE suministro_id=:suministro_id AND compra_suministro_id=:compra_suministro_id";
			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->bindParam(":suministro_id", $datosModel["suministro_id"], PDO::PARAM_STR);
			$stmt->bindParam(":compra_suministro_id", $datosModel["compra_suministro_id"], PDO::PARAM_STR);
			$stmt->bindParam(":cantidad", $datosModel["cantidad"], PDO::PARAM_STR);
			$stmt->bindParam(":precio", $datosModel["precio"], PDO::PARAM_STR);

			if($stmt->execute())
				{	return "success";	}
			else{ return "error";		}
			$stmt->close();
		}
	#-------------------------------------------------
	#ACTUALIZAR CANTIDAD SUMINISTRO
	#----------------------------------------------
	 public static function SuministroCantidadModel($idproducto,$actualizar)
		{
			$consulta="UPDATE suministros SET cantidad=:cantidad WHERE id=$idproducto";
			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->bindParam(":cantidad", $actualizar, PDO::PARAM_STR);

			if($stmt->execute())
				{	return "success";	}
			else{ return "error";		}
			$stmt->close();
		}

	#-------------------------------------------------
}