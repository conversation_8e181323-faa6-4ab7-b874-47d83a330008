
<?php
ob_start();
class controller<PERSON><PERSON><PERSON> extends Mvc<PERSON>ontroller
{
	#REGISTRO DE MESAS
	#------------------------------------
	 public function registroMesaController()
		{
		 if(isset($_POST["nombreMesaRegistro"]))
			{
			 $datosController =array('nombre'=>$_POST["nombreMesaRegistro"]);
			//echo "<script>alert('Entro Controller ".$datosController." no')</script>";
			 $respuesta = DatosMesa::registroMesaModel($datosController, "mesas");
			 if($respuesta == "success")
				{	header("location:index.php?action=okM");	}
			 else
				{	header("location:index.php");	}
			}
		}
	#------------------------------------
	#VISTA DE MESAS
	#------------------------------------
	 public function vistaMesaController()
		{
			 unset($_SESSION["mesa"]);

			 if($_SESSION["tipo_usuario"]==1){	
			 	//echo 'Es uno &nbsp; &nbsp; <br>';
			 	$respuesta = DatosMesa::vistaMesaModel("mesas");
			 }else if($_SESSION["tipo_usuario"]==3){
			 	$respuesta = DatosMesa::vistaMesasMeseroModel("mesas",$_SESSION['usuario']);
			 	//echo 'Es tres &nbsp; &nbsp; <br>';
			 }

			 //$respuesta = DatosMesa::vistaMesaModel("mesas");

			 //echo '<h4>'.$_SESSION['usuario'].'<-->'.$_SESSION["tipo_usuario"].'</h4>';

			if ($respuesta==0) {
			 	// code...
			 	echo "<h3>No hay mesas registradas para este mesero</h3>";
			}else{

				 foreach($respuesta as $row => $item)
					{
						if ($item["estado"] == 'OCUPADA')	{  $clase = 'cero'; }
						elseif ($item["estado"] == 'DISPONIBLE')	{  $clase = 'uno'; }
						else {  $clase = 'dos'; }


						 echo '<div class="col-xs-6 col-sm-4 col-lg-3 '.$clase.'" style="flex: 1;"> '.$item["nombre"].' | '.$item["descripcion"].' | 
						 	<a href="index.php?action=editarMesa&id='.$item["id"].'"><img src="img/editar-min.png" width="20" height="20" alt=""></a></a> |
									<a href="index.php?action=registroPmesa&ida='.$item["id"].'&mesero='.$item["descripcion"].'"><img src="img/open-min.png" width="30" height="30" alt=""></a> </div>';
					}
			} 	
		}
	#------------------------------------
	#EDITAR MESAS
	#------------------------------------
	 public function editarMesaController()
		{
		 $datosController = $_GET["id"];
		 $respuesta = DatosMesa::editarMesaModel($datosController, "mesas");
		 echo' Nombre Mesa:<input type="text" value="'.$respuesta["nombre"].'" name="nombreMesaEditar" required> Cupo Mesa:<input type="text" value="'.$respuesta["cupo"].'" name="cupo" required> 	Ubicación Mesa:<input type="text" value="'.$respuesta["descripcion"].'" name="descripcion" >
			 <input type="hidden" value="'.$respuesta["id"].'" name="idMesaEditar" required>
				 <input type="submit" value="Actualizar">';
		}
	#------------------------------------
	#ACTUALIZAR MESAS
	#------------------------------------
	 public function actualizarMesaController()
		{//echo "<script>alert('Entro Controller Actualizar Mesa')</script>";
		 if(isset($_POST["nombreMesaEditar"]))
			{
			 $datosController = array("nombre"=>$_POST["nombreMesaEditar"],
									"cupo"=>$_POST["cupo"],
									"descripcion"=>$_POST["descripcion"],
									"id"=>$_POST["idMesaEditar"]);
			 $respuesta = DatosMesa::actualizarMesaModel($datosController, "mesas");
			 if($respuesta == "success")
				{	header("location:index.php?action=mesa");	}
			 else
				{	echo "error";	}
			}
		}
	#------------------------------------
	#BORRAR MESAS
	#------------------------------------
	 public function borrarMesaController()
		{
		 if(isset($_GET["idBorrar"]))
			{	echo"<script>alert('Entro Controller Actualizar Mesa')</script>";
				$datosController = $_GET["idBorrar"];
				$respuesta = DatosMesa::borrarMesaModel($datosController, "mesas");
			 if($respuesta == "success")
				{	header("location:index.php?action=mesa");	}
			}
		}
	#---------------------------------

}
