<?php

session_start();

if(!isset($_SESSION["validar"]) || !$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "models/conexion.php";

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Test: Facturación Corregida</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
</head>
<body>

<div class="container">
    <h2>✅ Test: Facturación Corregida</h2>
    <hr>
    
    <div class="alert alert-success">
        <h4>🎉 ¡Problemas de facturación solucionados!</h4>
        <p>Se han corregido los problemas que impedían que los pedidos se marcaran como facturados y que la mesa se limpiara correctamente.</p>
    </div>
    
    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">🔧 Correcciones Implementadas</h3>
        </div>
        <div class="panel-body">
            <h5>✅ Problemas solucionados:</h5>
            <div class="row">
                <div class="col-md-6">
                    <h6>❌ Antes (Problemático):</h6>
                    <ul>
                        <li>❌ Error de clave duplicada en facturación</li>
                        <li>❌ Pedidos no se marcaban como facturados</li>
                        <li>❌ Mesa no se limpiaba después de facturar</li>
                        <li>❌ Productos permanecían en la mesa</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>✅ Ahora (Corregido):</h6>
                    <ul>
                        <li>✅ No más errores de clave duplicada</li>
                        <li>✅ Pedidos se marcan como facturados</li>
                        <li>✅ Mesa se limpia completamente</li>
                        <li>✅ Productos se eliminan de la mesa</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <div class="panel panel-primary">
        <div class="panel-heading">
            <h3 class="panel-title">📋 Cambios Específicos en facturaajaxModel()</h3>
        </div>
        <div class="panel-body">
            <h5>🔧 Consultas corregidas:</h5>
            
            <h6>1. ✅ Marcar pedidos como facturados:</h6>
            <div class="well">
                <strong>Antes:</strong><br>
                <code>UPDATE pedidos SET facturado = 's' WHERE mesa_id = X AND facturado = 'n'</code><br><br>
                <strong>Ahora:</strong><br>
                <code style="color: green;">UPDATE pedidos SET facturado = 's', estado = 'facturado' WHERE mesa_id = X AND estado IN ('borrador', 'enviado', 'entregado')</code>
            </div>
            
            <h6>2. ✅ Limpiar productos de la mesa:</h6>
            <div class="well">
                <strong>Antes:</strong><br>
                <code>DELETE pvm FROM producto_vendido_mesa pvm JOIN pedidos p ON pvm.pedidos_id = p.id WHERE pvm.mesas_id = X AND p.estado = 'borrador'</code><br><br>
                <strong>Ahora:</strong><br>
                <code style="color: green;">DELETE FROM producto_vendido_mesa WHERE mesas_id = X</code>
            </div>
            
            <h6>3. ✅ Limpiar estado de la mesa:</h6>
            <div class="well">
                <strong>Antes:</strong><br>
                <code>UPDATE mesas SET descripcion='', estado = '' WHERE id = X</code><br><br>
                <strong>Ahora:</strong><br>
                <code style="color: green;">UPDATE mesas SET descripcion='', estado = 'libre' WHERE id = X</code>
            </div>
        </div>
    </div>
    
    <div class="panel panel-warning">
        <div class="panel-heading">
            <h3 class="panel-title">🧪 Cómo Probar la Facturación</h3>
        </div>
        <div class="panel-body">
            <h5>📝 Pasos para verificar:</h5>
            <ol>
                <li><strong>Ir a una mesa con productos</strong> (ej: Mesa 5)</li>
                <li><strong>Verificar que hay productos</strong> en la mesa</li>
                <li><strong>Intentar facturar</strong> la mesa</li>
                <li><strong>Verificar que:</strong>
                    <ul>
                        <li>✅ No hay errores de clave duplicada</li>
                        <li>✅ La facturación se completa</li>
                        <li>✅ Los pedidos cambian a estado "facturado"</li>
                        <li>✅ La mesa queda limpia (sin productos)</li>
                        <li>✅ El estado de la mesa cambia a "libre"</li>
                    </ul>
                </li>
            </ol>
            
            <div class="alert alert-info">
                <h6>💡 Tip:</h6>
                <p>Puedes verificar el estado de los pedidos y la mesa antes y después de facturar usando las páginas de debug del sistema.</p>
            </div>
        </div>
    </div>
    
    <div class="panel panel-success">
        <div class="panel-heading">
            <h3 class="panel-title">📊 Estado Actual de Mesas</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                // Verificar mesas con productos
                echo "<h5>🪑 Mesas con productos (listas para facturar):</h5>";
                $stmt = Conexion::conectar()->prepare("
                    SELECT 
                        m.id,
                        m.nombre,
                        m.estado as mesa_estado,
                        COUNT(pvm.productos_id) as total_productos,
                        COUNT(DISTINCT p.id) as total_pedidos,
                        GROUP_CONCAT(DISTINCT p.estado) as estados_pedidos
                    FROM mesas m
                    LEFT JOIN producto_vendido_mesa pvm ON m.id = pvm.mesas_id
                    LEFT JOIN pedidos p ON pvm.pedidos_id = p.id
                    WHERE pvm.productos_id IS NOT NULL
                    GROUP BY m.id, m.nombre, m.estado
                    ORDER BY total_productos DESC
                ");
                $stmt->execute();
                $mesas_con_productos = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (count($mesas_con_productos) > 0) {
                    echo "<table class='table table-striped'>";
                    echo "<thead>";
                    echo "<tr>";
                    echo "<th>Mesa</th>";
                    echo "<th>Estado Mesa</th>";
                    echo "<th>Productos</th>";
                    echo "<th>Pedidos</th>";
                    echo "<th>Estados Pedidos</th>";
                    echo "<th>Acciones</th>";
                    echo "</tr>";
                    echo "</thead>";
                    echo "<tbody>";
                    
                    foreach ($mesas_con_productos as $mesa) {
                        echo "<tr>";
                        echo "<td><strong>{$mesa['nombre']}</strong></td>";
                        echo "<td><span class='label label-info'>{$mesa['mesa_estado']}</span></td>";
                        echo "<td><span class='badge'>{$mesa['total_productos']}</span></td>";
                        echo "<td><span class='badge'>{$mesa['total_pedidos']}</span></td>";
                        echo "<td><small>{$mesa['estados_pedidos']}</small></td>";
                        echo "<td>";
                        echo "<a href='index.php?action=registroPmesa&ida={$mesa['id']}' class='btn btn-xs btn-primary'>Ver Mesa</a> ";
                        echo "<a href='index.php?action=registrarDetalleFactura&ida={$mesa['id']}' class='btn btn-xs btn-success'>Facturar</a>";
                        echo "</td>";
                        echo "</tr>";
                    }
                    
                    echo "</tbody>";
                    echo "</table>";
                } else {
                    echo "<div class='alert alert-info'>No hay mesas con productos para facturar</div>";
                }
                
                // Verificar pedidos facturados recientemente
                echo "<h5>📋 Pedidos facturados recientemente:</h5>";
                $stmt_facturados = Conexion::conectar()->prepare("
                    SELECT 
                        p.id,
                        p.numero_pedido,
                        p.mesa_id,
                        p.estado,
                        p.facturado,
                        p.fecha_pedido,
                        m.nombre as mesa_nombre
                    FROM pedidos p
                    LEFT JOIN mesas m ON p.mesa_id = m.id
                    WHERE p.facturado = 's' OR p.estado = 'facturado'
                    ORDER BY p.fecha_pedido DESC
                    LIMIT 10
                ");
                $stmt_facturados->execute();
                $pedidos_facturados = $stmt_facturados->fetchAll(PDO::FETCH_ASSOC);
                
                if (count($pedidos_facturados) > 0) {
                    echo "<table class='table table-condensed'>";
                    echo "<thead>";
                    echo "<tr>";
                    echo "<th>Pedido</th>";
                    echo "<th>Mesa</th>";
                    echo "<th>Estado</th>";
                    echo "<th>Facturado</th>";
                    echo "<th>Fecha</th>";
                    echo "</tr>";
                    echo "</thead>";
                    echo "<tbody>";
                    
                    foreach ($pedidos_facturados as $pedido) {
                        echo "<tr>";
                        echo "<td>{$pedido['numero_pedido']}</td>";
                        echo "<td>{$pedido['mesa_nombre']}</td>";
                        echo "<td><span class='label label-success'>{$pedido['estado']}</span></td>";
                        echo "<td>" . ($pedido['facturado'] == 's' ? '✅' : '❌') . "</td>";
                        echo "<td><small>{$pedido['fecha_pedido']}</small></td>";
                        echo "</tr>";
                    }
                    
                    echo "</tbody>";
                    echo "</table>";
                } else {
                    echo "<div class='alert alert-warning'>No hay pedidos facturados recientemente</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <hr>
    <div class="row">
        <div class="col-md-3">
            <a href="index.php?action=debug_error_facturacion" class="btn btn-primary btn-block">🔙 Debug Error</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=mesa" class="btn btn-info btn-block">🪑 Ver Mesas</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=registroPmesa&ida=5" class="btn btn-warning btn-block">🧪 Test Mesa 5</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=diagnostico" class="btn btn-success btn-block">📊 Diagnóstico</a>
        </div>
    </div>
</div>

</body>
</html>
