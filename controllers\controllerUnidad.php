
<?php 
ob_start();
class controllerUnidad extends Mvc<PERSON>ontroller
{
	#REGISTRO DE UNIDADES
	#------------------------------------
	 public function registroUnidadController()
		{//echo "<script>alert('Entro Controller ".$_POST["nombreUnidadRegistro"]." no')</script>";
		 if(isset($_POST["nombreUnidadRegistro"]))
			{	
				$datosController =array('nombre'=>$_POST["nombreUnidadRegistro"]);
				//echo "<script>alert('if ".$datosController['nombre']." ')</script>";	
			/**/	$respuesta = DatosUnidad::registroUnidadModel($datosController, "unidades");

				if($respuesta == "success")
					{	header("location:index.php?action=okUni");	}
				else
					{	header("location:index.php");	}
			}
		}
	#------------------------------------
	#VISTA DE UNIDADES
	#------------------------------------
	 public function vistaUnidadController()
		{
		 $respuesta = DatosUnidad::vistaUnidadModel("unidades");
		 foreach($respuesta as $row => $item)
			{
				echo'<tr>						
					<td>'.$item["nombre"].'</td>																	
					<td><a href="index.php?action=editarUnidad&id='.$item["id"].'">Editar</a> <a href="index.php?action=unidades&idBorrar='.$item["id"].'">Borrar</a></td>
				</tr>';
			}
		}
	#------------------------------------
	#EDITAR UNIDADES
	#------------------------------------
	 public function editarUnidadController()
		{
			$datosController = $_GET["id"];
			$respuesta = DatosUnidad::editarUnidadModel($datosController, "unidades");
			$_SESSION["erol"]=$respuesta;
			echo' <table>
				<thead> 	
		     		 <tr > <td colspan="2" ></td> </tr>
	    		</thead>
				<tbody>
					<tr>
						<td>Nombre :</td>
						<td><input type="text" value="'.$respuesta["nombre"].'" name="nombreUnidadEditar" required></td>
					</tr>
				</tbody>
				<thead> 	
		     		 <tr > <td colspan="2" ></td> </tr>
	    		</thead>
			</table>
												 
			 <input type="hidden" value="'.$respuesta["id"].'" name="idUnidadEditar" required> 				
				 <input type="submit" value="Actualizar">';
		}
	#------------------------------------
	#ACTUALIZAR UNIDADES
	#------------------------------------
	 public function actualizarUnidadController()
		{echo "<script>alert('Entro Controller Actualizar Unidad')</script>";

			if(isset($_POST["nombreUnidadEditar"]))
				{
					$datosController = array(  "nombre"=>$_POST["nombreUnidadEditar"],														
												"id"=>$_POST["idUnidadEditar"]);				
					$respuesta = DatosUnidad::actualizarUnidadModel($datosController, "unidades");
					if($respuesta == "success")
						{	header("location:index.php?action=cambioUni");	}
					else
						{	echo "error";	}
				}				
		}
	#------------------------------------
	#BORRAR UNIDADES
	#------------------------------------
	 public function borrarUnidadController()
		{
			if(isset($_GET["idBorrar"]))
				{
					$datosController = $_GET["idBorrar"];				
					$respuesta = DatosUnidad::borrarUnidadModel($datosController, "unidades");
					if($respuesta == "success")
						{	header("location:index.php?action=unidades");	}
				}
		}			
	#---------------------------------

}			
	