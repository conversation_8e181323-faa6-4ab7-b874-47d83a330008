<!DOCTYPE html>
<html>
<head>
    <title>Test Optimizado Final - Mesa 10</title>
</head>
<body>

<h1>🚀 Test Optimizado Final - Mesa 10</h1>

<p>Este test usa la versión optimizada específicamente diseñada para mesas pesadas con muchos pedidos.</p>

<div style="background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;">
    <h4>🚀 Optimizaciones Implementadas:</h4>
    <ul>
        <li><strong>Timeout extendido:</strong> 10 minutos para procesar 21 pedidos</li>
        <li><strong>Memoria aumentada:</strong> 1GB para manejar datos masivos</li>
        <li><strong>Procesamiento directo:</strong> Sin usar el modelo complejo</li>
        <li><strong>Transacciones optimizadas:</strong> Configuración específica para mesas pesadas</li>
        <li><strong>Inserción por lotes:</strong> Procesamiento eficiente de productos</li>
    </ul>
</div>

<div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;">
    <h4>📊 Estado Actual Mesa 10:</h4>
    <p><strong>Pedidos para facturar:</strong> 21 pedidos</p>
    <p><strong>Productos estimados:</strong> ~40-50 productos</p>
    <p><strong>Tiempo estimado:</strong> 2-5 minutos</p>
</div>

<button onclick="testOptimizadoFinal()" style="background: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;">
🚀 Test Facturación Optimizada
</button>

<div id="resultado" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border: 1px solid #ddd; border-radius: 5px;"></div>

<script>
function testOptimizadoFinal() {
    if (!confirm('🚀 FACTURACIÓN OPTIMIZADA PARA MESA 10\n\n¿Proceder con la facturación de 21 pedidos?\n\nEsto puede tomar varios minutos pero debería funcionar correctamente.')) {
        return;
    }
    
    document.getElementById('resultado').innerHTML = '<div style="color: blue; font-weight: bold;">⏳ Ejecutando facturación optimizada para mesa pesada...</div><div style="margin-top: 10px; color: #666;">Procesando 21 pedidos con sistema optimizado. Esto puede tomar 2-5 minutos.</div>';
    
    const startTime = Date.now();
    
    fetch('ajaxFactura_optimizado.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            efectivo: 50000,
            bancolombia: 0,
            nequi: 0,
            daviplata: 0,
            tarjeta: 0,
            pago: 1,
            pcedula: 'MARCO',
            totalDescuento: 0,
            total: 50000,
            propina: 0,
            mesa: 10,
            tipoTarjeta: 'credito',
            optimizada: true
        }),
        signal: AbortSignal.timeout(600000) // 10 minutos
    })
    .then(response => {
        const endTime = Date.now();
        const duration = Math.round((endTime - startTime) / 1000);
        
        console.log('Status:', response.status);
        console.log('Status Text:', response.statusText);
        console.log('Duración:', duration, 'segundos');
        
        return response.text();
    })
    .then(text => {
        const endTime = Date.now();
        const duration = Math.round((endTime - startTime) / 1000);
        
        console.log('Respuesta completa:', text);
        
        let html = '<h4>🚀 Resultado Facturación Optimizada:</h4>';
        html += '<p><strong>Duración:</strong> ' + duration + ' segundos</p>';
        html += '<p><strong>Status:</strong> ' + (text ? 'Respuesta recibida' : 'Sin respuesta') + '</p>';
        
        // Buscar alertas JavaScript
        if (text.includes('alert(')) {
            html += '<div style="background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;">';
            html += '<h5 style="color: #155724;">✅ Mensajes del Sistema:</h5>';
            
            const alertMatches = text.match(/alert\('([^']+)'\)/g);
            if (alertMatches) {
                alertMatches.forEach(alert => {
                    const mensaje = alert.replace(/alert\('/, '').replace(/'\)/, '');
                    html += '<p style="color: #155724;"><strong>' + mensaje.replace(/\\n/g, '<br>') + '</strong></p>';
                });
            }
            html += '</div>';
        }
        
        // Buscar errores específicos
        if (text.includes('error_optimizada') || text.includes('error_general')) {
            html += '<div style="background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;">';
            html += '<h5 style="color: #721c24;">❌ Error Detectado:</h5>';
            
            // Extraer mensajes de error
            const errorMatches = text.match(/error_[^:]*: [^<\n]+/g);
            if (errorMatches) {
                errorMatches.forEach(error => {
                    html += '<p style="color: #721c24;"><strong>' + error + '</strong></p>';
                });
            }
            html += '</div>';
        }
        
        html += '<h5>📄 Respuesta Completa:</h5>';
        html += '<pre style="background: #fff; padding: 10px; border: 1px solid #ddd; border-radius: 3px; white-space: pre-wrap; max-height: 300px; overflow-y: auto;">' + text + '</pre>';
        
        if (text.includes('success_optimizada')) {
            html += '<div style="color: green; font-weight: bold; font-size: 18px;">✅ ÉXITO: Facturación optimizada completada</div>';
            html += '<p style="color: green;">¡La mesa 10 ha sido facturada exitosamente con el sistema optimizado!</p>';
            html += '<p><a href="index.php?action=registroPmesa&ida=10" style="background: #28a745; color: white; padding: 10px; text-decoration: none; border-radius: 3px;">🔄 Verificar Mesa 10</a></p>';
        } else if (text.includes('Facturación completada exitosamente')) {
            html += '<div style="color: green; font-weight: bold; font-size: 18px;">✅ ÉXITO: Facturación completada</div>';
            html += '<p style="color: green;">¡La mesa 10 ha sido facturada exitosamente!</p>';
        } else if (text.includes('error') || text.includes('Error')) {
            html += '<div style="color: red; font-weight: bold; font-size: 18px;">❌ ERROR en facturación optimizada</div>';
            html += '<p>Revisa los detalles del error arriba.</p>';
        } else {
            html += '<div style="color: orange; font-weight: bold; font-size: 18px;">⚠️ Respuesta inesperada</div>';
        }
        
        document.getElementById('resultado').innerHTML = html;
    })
    .catch(error => {
        const endTime = Date.now();
        const duration = Math.round((endTime - startTime) / 1000);
        
        console.error('Error completo:', error);
        
        let html = '<h4>❌ Error en Facturación Optimizada:</h4>';
        html += '<p><strong>Duración antes del error:</strong> ' + duration + ' segundos</p>';
        html += '<p><strong>Error:</strong> ' + error.message + '</p>';
        
        if (error.name === 'TimeoutError') {
            html += '<div style="background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;">';
            html += '<h5 style="color: #856404;">⏰ Timeout después de 10 minutos</h5>';
            html += '<p style="color: #856404;">La facturación puede haberse completado en el servidor.</p>';
            html += '<p style="color: #856404;">Verifica manualmente el estado de la mesa 10.</p>';
            html += '</div>';
        }
        
        document.getElementById('resultado').innerHTML = html;
    });
}
</script>

<h3>📋 Información Técnica:</h3>
<div style="background: #e9ecef; padding: 15px; border-radius: 5px;">
    <h4>🔧 Configuración Optimizada:</h4>
    <ul>
        <li><strong>max_execution_time:</strong> 600 segundos (10 minutos)</li>
        <li><strong>memory_limit:</strong> 1024M (1GB)</li>
        <li><strong>innodb_lock_wait_timeout:</strong> 600 segundos</li>
        <li><strong>Procesamiento:</strong> Directo sin modelo complejo</li>
        <li><strong>Transacciones:</strong> Optimizadas para volumen alto</li>
    </ul>
</div>

<br><a href="index.php?action=registroPmesa&ida=10" style="background: #007bff; color: white; padding: 10px; text-decoration: none;">🔙 Volver a Mesa 10</a>

</body>
</html>
