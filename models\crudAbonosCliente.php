<?php
require_once "conexion.php";
require_once "crudTurno.php";
class DatosAbonosCliente extends Conexion
 {	
	#BUSCAR DEUDA CLIENTE TODO
	#-------------------------------------
	 public static function deudaTodoModel()
		{		//echo "<script>alert('entro crud Deuda del cliente ".$datosModel."')</script>";
			$consulta = "SELECT d.id as did, d.credito_id as dcredito_id, d.abonos_id as dabonos_id, d.cliente_id as dcliente_id, d.cuenta as dcuenta, p.id as pid, p.nombre as pnombre, p.apellidos as pellidos, p.cedula as pcedula  
				FROM deudas d, personas p WHERE d.cliente_id = p.id  GROUP BY dcredito_id ORDER BY  d.cliente_id ASC";
			$stmt = Conexion::conectar()->prepare($consulta);			
			$stmt->execute();
			$r= $stmt->fetchAll();			
			return $r;
			$stmt->close();
		}
	#----------------------------------------
	#BUSCAR DEUDA CLIENTE
	#-------------------------------------
	 public static function buscardeudaClienteModel($datosModel)
		{		//echo "<script>alert('entro crud Deuda del cliente ".$datosModel."')</script>";
			$consulta = "SELECT p.id AS pid, p.cedula AS pcedula, p.nombre AS pnombre, p.apellidos AS papellidos, d.cliente_id AS dclienteid, d.cuenta AS dcuenta, d.abonos_id AS dabonos, d.credito_id AS dcreditoid, c.fecha_credito As cfecha, c.valor AS ctotal 
			FROM personas p, deudas d, creditos c 
			WHERE c.id=d.credito_id AND d.cliente_id=p.id AND p.cedula= :cedula group by c.id";
			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->bindParam(":cedula", $datosModel, PDO::PARAM_STR);	
			$stmt->execute();
			$r= $stmt->fetchAll();
			foreach ($r as $rows => $item)
			 {
				$vertor=0;
			 }
			$c2=$stmt->rowCount();
			//echo '<br> C2='.$c2.'<br>';
			if ($c2>0) 
			 { 	//echo "<script>alert(' if entro consulta ".$r['dcuenta'] ."')</script>";
			 	return $r;	
			 }
			else
			 {//echo "<script>alert(' Error BD Buscar deuda')</script>";				
				return 0;
		     }
			$stmt->close();
		}
	#----------------------------------------	
	#BUSCAR DEUDA factura
	#-------------------------------------
	 public static function buscardeudaFacturaModel($datosModel)
		{//echo "<script>alert('buscar Proveedor 1 ".$datosModel."')</script>";
			$consulta = "SELECT p.id AS pid, p.cedula AS pcedula, p.nombre AS pnombre, p.apellidos AS papellidos, d.cliente_id AS dclienteid, d.cuenta AS dcuenta, d.abonos_id AS dabonos, d.credito_id AS dcreditoid, c.fecha_credito As cfecha, c.valor AS ctotal 
			FROM personas p, deudas d, creditos c 
			WHERE c.id=d.credito_id AND d.cliente_id=p.id AND  d.credito_id=:credito_id order by d.id DESC limit 1";
			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->bindParam(":credito_id", $datosModel, PDO::PARAM_STR);	
			$stmt->execute();			
			$r= $stmt->fetch();
			$c2=$stmt->rowCount();
			//echo '<br> C2='.$c2.'<br>';
			if ($c2>0) 
			 {	//echo "<script>alert(' if entro consulta ".$r['dcuenta'] ."')</script>";
			 	return $r;	
			 }
			else
			 {echo "<script>alert(' Error BD Buscar deuda')</script>";				
				return 0;
		     }
			$stmt->close();
		}
	#----------------------------------------
	# BUSACRA CEDULA
	# -----------------------------------------
		public static function cedulaModel($cedula)
			{
				$consulta = "SELECT id, cedula, nombre, apellidos FROM personas WHERE cedula = :cedula";
				$stmt = Conexion::conectar()->prepare($consulta);
				$stmt->bindParam(":cedula", $datosModel, PDO::PARAM_STR);	
				$stmt->execute();
				return $stmt->fetch();
				$stmt->close();
			}
	# -----------------------------------------				
	# BUSACRA ABONO
	# -----------------------------------------
		public static function abonoModel($deuda_id)
			{
				$consulta = "SELECT d.credito_id AS dcredito_id, d.abonos_id AS dabonos_id, d.cliente_id AS dcliente_id, d.cuenta AS dcuenta, a.id AS aid, a.turnos_cajeros_id AS aturnos_cajeros_id, a.fecha_bono AS afecha_bono, a.descripsion AS adescripsion, a.valor AS avalor 
					FROM deudas d, abonos a 
					WHERE d.abonos_id=a.id AND d.credito_id=:deuda_id group by d.cuenta ORDER  by a.id";
				$stmt = Conexion::conectar()->prepare($consulta);
				$stmt->bindParam(":deuda_id", $deuda_id, PDO::PARAM_INT);	
				$stmt->execute();
				$r=$stmt->fetchAll();

				$c2=$stmt->rowCount();
				//echo '<br> C2='.$c2.'<br>';
				if ($c2>0) 
				 {	return $r;	}
				else
				 {	return 0;	}
				$stmt->close();
			}
	# -----------------------------------------
	#REGISTRAR ABONO   id, id_personas, turnos_cajeros_id, fecha_bono, descripsion, valor
	#-------------------------------------
		public static function abonoClienteModel($datosModel, $tabla)
			{   $cuentay=$datosModel["deuda"];
				$abonoy=$datosModel["valorAbono"];							 		
			
				$cliente = DatosAbonosCliente::buscardeudaFacturaModel($datosModel["deuda"]);
				$deuday=$cliente["dcuenta"];
				//echo "<script>alert('Entro crud  Abonos=".$abonoy."->Cuenta=".$cuentay."=>Deuda=".$deuday."');</script>";
												
				$valorDeuda1 = $cliente["dcuenta"] - $datosModel["valorAbono"];			
				
				$turnos = $datosModel["turno"];
				$stmt = Conexion::conectar(); 
													
				date_default_timezone_set("America/Bogota");
				$fecha_abono=strftime("%Y-%m-%d %H:%M:%S");
				//echo "<script>alert(' - valordeuda: ".$valorDeuda1." valorAbono: ".$datosModel["deuda"]."  - valor: ".$cliente["dcuenta"]." ');</script>";
				try{	
							//echo "<script>alert('Entro  try');</script>";								
							$stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);  
							$stmt->beginTransaction();
						//if ($valorDeuda1<=0) 
						if ($cliente["dcuenta"]<=$datosModel["valorAbono"]) 
						 { 	
						 	$valorDeuda =0;
						 	//// Inserta Abono//
							$consulta="INSERT INTO $tabla ( turnos_cajeros_id, fecha_bono, descripsion, valor) VALUES ( ".$turnos.", '".$fecha_abono."', '".$datosModel["descripcion"]."', ".$cliente["dcuenta"].")";
							  
							echo " --".$consulta;					
							
							$stmt->exec($consulta);	
							$ultimo_id=$stmt->lastInsertId();	//ultimo pedido		
                          								
							//echo "<script>alert('ultimo pedido ".$ultimo_id." ' );</script>";	

							//	 insertar deuda nueva	
							$consulta1 = "INSERT INTO deudas(credito_id, abonos_id, cliente_id, cuenta) VALUES (".$datosModel["deuda"].", $ultimo_id, ".$cliente["pid"].", $valorDeuda)";
							$stmt->exec($consulta1);

							//echo "<script>alert('Vuelto  ".$valorDeuda1." ' );</script>";

						 }
						else
						 {
						 	$valorDeuda =$valorDeuda1;

						 	//// Inserta Abono//
						 	//echo "<script>alert('los datos de abono tabla - turnos: ".$turnos."  -fecha_abono: ".$fecha_abono."  - datosModel : ".$datosModel["descripsion"]."  $valorDeuda: ".$valorDeuda."');</script>";
							
							$consulta="INSERT INTO $tabla ( turnos_cajeros_id, fecha_bono, descripsion, valor) VALUES ( ".$turnos.", '".$fecha_abono."', '".$datosModel["descripcion"]."', ".$datosModel["valorAbono"].")";
							  
							echo " --".$consulta;					
							//echo "<script>alert('los datos de deudas tabla - deuda: ".$datosModel["deuda"]."  -ultimo_id: ".$ultimo_id."  - cliente : ".$cliente["pid"]."  valorDeuda: ".$valorDeuda."');</script>";
							$stmt->exec($consulta);	
							//echo "<script>alert('ultimo Abono ".$ultimo_id." ' );</script>";
							$ultimo_id=$stmt->lastInsertId();	//ultimo pedido					
                          								
							//echo "<script>alert('ultimo Abono ".$ultimo_id." ' );</script>";	

							//	 insertar deuda nueva	
							$consulta1 = "INSERT INTO deudas(credito_id, abonos_id, cliente_id, cuenta) VALUES (".$datosModel["deuda"].", ".$ultimo_id.", ".$cliente["pid"].", ".$valorDeuda.")";
							echo " --".$consulta1;
							$stmt->exec($consulta1);
							//echo "<script>alert('Queda debindo  ".$valorDeuda1." ' );</script>";

						 }	
							//echo "<script>alert('fin de cruz try')</script>";
							$stmt->commit();
							//echo "<script>alert('fin de cruD try que pasa');</script>";
							return "success"; 
							$stmt->close();						
						} 
					catch (PDOException $e)
						{	echo "<script>alert('catch entro')</script>";
							  $stmt->rollBack(); 
							print "Error!: ".$e->getMessage()."</br>";
							return "Error!: ".$e->getMessage()."</br>";   
						}							
							
					
				//	return "success";		
			}
	#------------------------------------------	
	#BORRAR PEDIDO MESA  DELETE FROM `producto_vendido_mesa` WHERE `productos_id`=13 AND `fecha_hora`='0000-00-00 00:00:00'
	#------------------------------------
		public static function borrarAbonosClienteModel($datosModel, $tabla)
		{ 	/*echo "<script>alert('Entro CRUD ".$datosModel["productos_id"]." es')</script>";	
		 	echo "<script>alert('Entro CRUD ".$datosModel["fecha_hora"]." es')</script>";
		 	*/$consulta = "DELETE FROM $tabla WHERE productos_id=:id AND fecha_hora = :fecha_hora";
		 	echo "<script>alert('Entro CRUD ".$consulta." es')</script>";	
			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->bindParam(":id", $datosModel["productos_id"], PDO::PARAM_INT);
			$stmt->bindParam(":fecha_hora", $datosModel["fecha_hora"], PDO::PARAM_STR);
			if($stmt->execute())
				{	return "success";	}
			else{	return "error";		}
			$stmt->close();
		}
	#----------------------------------------------
 }