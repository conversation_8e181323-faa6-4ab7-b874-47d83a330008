{"name": "proxy-impresion-macarena", "version": "1.0.0", "description": "Proxy para impresión híbrida del sistema Macarena", "main": "proxy-server.js", "scripts": {"start": "node proxy-server.js", "dev": "nodemon proxy-server.js", "test": "node test-proxy.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["proxy", "impresion", "restaurant", "pos", "thermal-printer"], "author": "Sistema <PERSON>", "license": "MIT"}