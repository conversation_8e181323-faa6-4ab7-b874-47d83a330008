<?php
session_start();

echo "<h2>🔍 Debug Facturas 376 y 380 - Productos vs Total</h2>";

// Incluir archivos necesarios
require_once "../../models/conexion.php";

$facturas_debug = [376, 380];

foreach ($facturas_debug as $factura_id) {
    echo "<div style='border: 2px solid #007bff; margin: 20px 0; padding: 15px; border-radius: 8px;'>";
    echo "<h3>📄 Factura ID: $factura_id</h3>";
    
    try {
        // 1. Datos de la venta principal
        echo "<h4>1. 💰 Datos de la Venta</h4>";
        $stmt = Conexion::conectar()->prepare("
            SELECT v.*, p.nombre as cliente_nombre, p.cedula as cliente_cedula
            FROM ventas v
            LEFT JOIN personas p ON v.cliente = p.id
            WHERE v.id = ?
        ");
        $stmt->bindParam(1, $factura_id, PDO::PARAM_INT);
        $stmt->execute();
        $venta = $stmt->fetch();
        
        if ($venta) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>Campo</th><th>Valor</th></tr>";
            echo "<tr><td><strong>ID Venta</strong></td><td>{$venta['id']}</td></tr>";
            echo "<tr><td><strong>Cliente</strong></td><td>{$venta['cliente_nombre']} ({$venta['cliente_cedula']})</td></tr>";
            echo "<tr><td><strong>Total Venta</strong></td><td style='background: #ffeb3b; font-weight: bold;'>$" . number_format($venta['total']) . "</td></tr>";
            echo "<tr><td><strong>Descuento</strong></td><td>$" . number_format($venta['descuento']) . "</td></tr>";
            echo "<tr><td><strong>Efectivo</strong></td><td>$" . number_format($venta['efectivo']) . "</td></tr>";
            echo "<tr><td><strong>Tarjeta</strong></td><td>$" . number_format($venta['tarjeta']) . "</td></tr>";
            echo "<tr><td><strong>Nequi</strong></td><td>$" . number_format($venta['nequi']) . "</td></tr>";
            echo "<tr><td><strong>Daviplata</strong></td><td>$" . number_format($venta['daviplata']) . "</td></tr>";
            echo "<tr><td><strong>Bancolombia</strong></td><td>$" . number_format($venta['bancolombia']) . "</td></tr>";
            echo "<tr><td><strong>Propina</strong></td><td>$" . number_format($venta['propina']) . "</td></tr>";
            echo "<tr><td><strong>Fecha</strong></td><td>{$venta['fecha']}</td></tr>";
            echo "<tr><td><strong>Usuario</strong></td><td>{$venta['usuario']}</td></tr>";
            echo "</table>";
        } else {
            echo "❌ No se encontró la venta con ID $factura_id<br>";
            continue;
        }
        
        // 2. Productos facturados
        echo "<h4>2. 🛒 Productos Facturados</h4>";
        $stmt = Conexion::conectar()->prepare("
            SELECT 
                pv.id,
                pv.producto,
                pv.cantidad,
                pv.precio_unitario,
                pv.precio_total,
                pv.descuento,
                p.nombre as producto_nombre,
                p.codigo as producto_codigo,
                p.precio as precio_actual_producto
            FROM producto_vendido pv
            LEFT JOIN productos p ON pv.producto = p.id
            WHERE pv.venta = ?
            ORDER BY pv.id
        ");
        $stmt->bindParam(1, $factura_id, PDO::PARAM_INT);
        $stmt->execute();
        $productos = $stmt->fetchAll();
        
        if (!empty($productos)) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th>ID</th><th>Código</th><th>Producto</th><th>Cantidad</th><th>Precio Unit.</th><th>Descuento</th><th>Total</th><th>Precio Actual</th>";
            echo "</tr>";
            
            $total_calculado = 0;
            $productos_agrupados = [];
            
            foreach ($productos as $prod) {
                echo "<tr>";
                echo "<td>{$prod['id']}</td>";
                echo "<td>{$prod['producto_codigo']}</td>";
                echo "<td>{$prod['producto_nombre']}</td>";
                echo "<td style='text-align: center;'>{$prod['cantidad']}</td>";
                echo "<td style='text-align: right;'>$" . number_format($prod['precio_unitario']) . "</td>";
                echo "<td style='text-align: right;'>$" . number_format($prod['descuento']) . "</td>";
                echo "<td style='text-align: right; background: #e8f5e8;'>$" . number_format($prod['precio_total']) . "</td>";
                echo "<td style='text-align: right;'>$" . number_format($prod['precio_actual_producto']) . "</td>";
                echo "</tr>";
                
                $total_calculado += $prod['precio_total'];
                
                // Agrupar productos iguales
                $key = $prod['producto'] . '_' . $prod['precio_unitario'];
                if (!isset($productos_agrupados[$key])) {
                    $productos_agrupados[$key] = [
                        'nombre' => $prod['producto_nombre'],
                        'codigo' => $prod['producto_codigo'],
                        'precio_unitario' => $prod['precio_unitario'],
                        'cantidad_total' => 0,
                        'total' => 0,
                        'registros' => 0
                    ];
                }
                $productos_agrupados[$key]['cantidad_total'] += $prod['cantidad'];
                $productos_agrupados[$key]['total'] += $prod['precio_total'];
                $productos_agrupados[$key]['registros']++;
            }
            
            echo "<tr style='background: #ffeb3b; font-weight: bold;'>";
            echo "<td colspan='6'>TOTAL CALCULADO</td>";
            echo "<td style='text-align: right;'>$" . number_format($total_calculado) . "</td>";
            echo "<td></td>";
            echo "</tr>";
            echo "</table>";
            
            // 3. Análisis de productos agrupados
            echo "<h4>3. 📊 Productos Agrupados (Detectar Duplicados)</h4>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th>Código</th><th>Producto</th><th>Precio Unit.</th><th>Cantidad Total</th><th>Total</th><th>Registros</th>";
            echo "</tr>";
            
            foreach ($productos_agrupados as $grupo) {
                $color = $grupo['registros'] > 1 ? 'background: #ffcdd2;' : '';
                echo "<tr style='$color'>";
                echo "<td>{$grupo['codigo']}</td>";
                echo "<td>{$grupo['nombre']}</td>";
                echo "<td style='text-align: right;'>$" . number_format($grupo['precio_unitario']) . "</td>";
                echo "<td style='text-align: center;'>{$grupo['cantidad_total']}</td>";
                echo "<td style='text-align: right;'>$" . number_format($grupo['total']) . "</td>";
                echo "<td style='text-align: center;'>" . ($grupo['registros'] > 1 ? "⚠️ {$grupo['registros']}" : "✅ 1") . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // 4. Comparación final
            echo "<h4>4. ⚖️ Comparación Final</h4>";
            $diferencia = $venta['total'] - $total_calculado;
            $color_diferencia = $diferencia == 0 ? '#4caf50' : '#f44336';
            
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><td><strong>Total en tabla 'ventas'</strong></td><td style='background: #ffeb3b;'>$" . number_format($venta['total']) . "</td></tr>";
            echo "<tr><td><strong>Total calculado productos</strong></td><td style='background: #e8f5e8;'>$" . number_format($total_calculado) . "</td></tr>";
            echo "<tr><td><strong>Diferencia</strong></td><td style='background: $color_diferencia; color: white; font-weight: bold;'>$" . number_format($diferencia) . "</td></tr>";
            echo "</table>";
            
            if ($diferencia != 0) {
                echo "<div style='background: #ffcdd2; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
                echo "<strong>❌ PROBLEMA DETECTADO:</strong> Los totales no coinciden.<br>";
                echo "Posibles causas:<br>";
                echo "• Productos duplicados no sumados correctamente<br>";
                echo "• Error en el cálculo durante la facturación<br>";
                echo "• Descuentos aplicados incorrectamente<br>";
                echo "• Propina incluida en el total de productos<br>";
                echo "</div>";
            } else {
                echo "<div style='background: #c8e6c9; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
                echo "<strong>✅ CORRECTO:</strong> Los totales coinciden perfectamente.";
                echo "</div>";
            }
            
        } else {
            echo "❌ No se encontraron productos para la factura $factura_id<br>";
        }
        
    } catch (Exception $e) {
        echo "❌ Error procesando factura $factura_id: " . $e->getMessage() . "<br>";
    }
    
    echo "</div>";
}

// 5. Verificar el proceso de facturación
echo "<div style='border: 2px solid #ff9800; margin: 20px 0; padding: 15px; border-radius: 8px;'>";
echo "<h3>🔧 Verificar Proceso de Facturación</h3>";

echo "<h4>5.1 Revisar ajaxFactura.php</h4>";
echo "<p>El problema puede estar en cómo se procesan los productos durante la facturación.</p>";

echo "<h4>5.2 Posibles causas del problema:</h4>";
echo "<ul>";
echo "<li><strong>Productos duplicados:</strong> Si hay productos iguales en diferentes pedidos, pueden no sumarse correctamente</li>";
echo "<li><strong>Error en vistaPmesaController:</strong> La función que obtiene productos para facturar puede tener problemas</li>";
echo "<li><strong>Cálculo incorrecto:</strong> El total puede incluir propinas o descuentos mal calculados</li>";
echo "<li><strong>Concurrencia:</strong> Si se factura muy rápido, pueden quedar registros inconsistentes</li>";
echo "</ul>";

echo "</div>";
?>

<style>
table {
    font-size: 12px;
}
th {
    background-color: #f5f5f5;
    padding: 8px;
    text-align: left;
}
td {
    padding: 6px;
    border: 1px solid #ddd;
}
</style>

<div style="margin: 20px 0; padding: 15px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px;">
    <h4>🔧 Próximos Pasos</h4>
    <ol>
        <li>Revisar el código de facturación en <code>ajaxFactura.php</code></li>
        <li>Verificar cómo se agrupan productos iguales</li>
        <li>Comprobar el cálculo de totales en <code>facturaajaxModel</code></li>
        <li>Revisar si hay problemas con descuentos o propinas</li>
    </ol>
</div>
