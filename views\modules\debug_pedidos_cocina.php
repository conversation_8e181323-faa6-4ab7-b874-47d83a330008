<?php

session_start();

if(!$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "models/conexion.php";

echo "<h2>🔍 Debug - Pedidos Cocina</h2>";
echo "<hr>";

// 1. Verificar pedidos enviados
echo "<h3>1. Todos los pedidos con estado 'enviado':</h3>";
$stmt = Conexion::conectar()->prepare("
    SELECT p.id, p.numero_pedido, p.estado, p.fecha_envio, m.numero as mesa
    FROM pedidos p 
    JOIN mesas m ON p.mesa_id = m.id 
    WHERE p.estado = 'enviado' 
    ORDER BY p.fecha_envio DESC 
    LIMIT 10
");
$stmt->execute();
$pedidos_enviados = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>ID</th><th>Número</th><th>Estado</th><th>Mesa</th><th>Fecha Envío</th></tr>";
foreach($pedidos_enviados as $pedido) {
    echo "<tr>";
    echo "<td>{$pedido['id']}</td>";
    echo "<td>{$pedido['numero_pedido']}</td>";
    echo "<td>{$pedido['estado']}</td>";
    echo "<td>{$pedido['mesa']}</td>";
    echo "<td>{$pedido['fecha_envio']}</td>";
    echo "</tr>";
}
echo "</table>";
echo "<p><strong>Total pedidos enviados:</strong> " . count($pedidos_enviados) . "</p>";

// 2. Verificar productos en pedidos enviados
echo "<h3>2. Productos en pedidos enviados:</h3>";
$stmt = Conexion::conectar()->prepare("
    SELECT pr.id, pr.nombre, pr.categoria, COUNT(*) as cantidad_pedidos
    FROM pedidos p
    JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
    JOIN productos pr ON pvm.productos_id = pr.id
    WHERE p.estado = 'enviado'
    GROUP BY pr.id, pr.nombre, pr.categoria
    ORDER BY cantidad_pedidos DESC
    LIMIT 20
");
$stmt->execute();
$productos = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>ID Producto</th><th>Nombre</th><th>Categoría</th><th>En Pedidos</th></tr>";
foreach($productos as $producto) {
    echo "<tr>";
    echo "<td>{$producto['id']}</td>";
    echo "<td>{$producto['nombre']}</td>";
    echo "<td>{$producto['categoria']}</td>";
    echo "<td>{$producto['cantidad_pedidos']}</td>";
    echo "</tr>";
}
echo "</table>";

// 3. Verificar categorías únicas
echo "<h3>3. Categorías únicas en productos:</h3>";
$stmt = Conexion::conectar()->prepare("
    SELECT DISTINCT categoria, COUNT(*) as total_productos
    FROM productos 
    GROUP BY categoria 
    ORDER BY categoria
");
$stmt->execute();
$categorias = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Categoría</th><th>Total Productos</th></tr>";
foreach($categorias as $categoria) {
    echo "<tr>";
    echo "<td>{$categoria['categoria']}</td>";
    echo "<td>{$categoria['total_productos']}</td>";
    echo "</tr>";
}
echo "</table>";

// 4. Probar consulta específica de cocina
echo "<h3>4. Consulta específica para COCINA:</h3>";
$categoria = 'cocina';
$stmt = Conexion::conectar()->prepare("
    SELECT DISTINCT
        p.id as pedido_id,
        p.numero_pedido,
        p.fecha_envio,
        p.mesa_id,
        m.numero as mesa_numero,
        COUNT(pvm.id) as total_productos,
        GROUP_CONCAT(
            CONCAT(pr.nombre, ' x', pvm.cantidad, ' (', pr.categoria, ')') 
            ORDER BY pr.nombre 
            SEPARATOR ', '
        ) as productos_detalle,
        SUM(pvm.cantidad * pr.precio) as total_precio
    FROM pedidos p
    INNER JOIN mesas m ON p.mesa_id = m.id
    INNER JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
    INNER JOIN productos pr ON pvm.productos_id = pr.id
    WHERE p.estado = 'enviado'
    AND pr.categoria NOT IN ('bebidas', 'cervezas', 'licores', 'vinos', 'cocteles', 'refrescos', 'jugos', 'agua', 'bar', 'carnes', 'parrilla', 'asados', 'pescados', 'mariscos')
    GROUP BY p.id, p.numero_pedido, p.fecha_envio, p.mesa_id, m.numero
    ORDER BY p.fecha_envio ASC
    LIMIT 10
");
$stmt->execute();
$pedidos_cocina = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<p><strong>Pedidos encontrados para COCINA:</strong> " . count($pedidos_cocina) . "</p>";

if(count($pedidos_cocina) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Número</th><th>Mesa</th><th>Productos</th><th>Total</th></tr>";
    foreach($pedidos_cocina as $pedido) {
        echo "<tr>";
        echo "<td>{$pedido['pedido_id']}</td>";
        echo "<td>{$pedido['numero_pedido']}</td>";
        echo "<td>{$pedido['mesa_numero']}</td>";
        echo "<td>{$pedido['productos_detalle']}</td>";
        echo "<td>$" . number_format($pedido['total_precio'], 0) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>❌ No se encontraron pedidos para cocina</p>";
}

// 5. Probar consulta SIN filtro de categoría
echo "<h3>5. Todos los pedidos enviados SIN filtro de categoría:</h3>";
$stmt = Conexion::conectar()->prepare("
    SELECT DISTINCT
        p.id as pedido_id,
        p.numero_pedido,
        p.fecha_envio,
        p.mesa_id,
        m.numero as mesa_numero,
        COUNT(pvm.id) as total_productos,
        GROUP_CONCAT(
            CONCAT(pr.nombre, ' x', pvm.cantidad, ' (', pr.categoria, ')') 
            ORDER BY pr.nombre 
            SEPARATOR ', '
        ) as productos_detalle,
        SUM(pvm.cantidad * pr.precio) as total_precio
    FROM pedidos p
    INNER JOIN mesas m ON p.mesa_id = m.id
    INNER JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
    INNER JOIN productos pr ON pvm.productos_id = pr.id
    WHERE p.estado = 'enviado'
    GROUP BY p.id, p.numero_pedido, p.fecha_envio, p.mesa_id, m.numero
    ORDER BY p.fecha_envio ASC
    LIMIT 5
");
$stmt->execute();
$todos_pedidos = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<p><strong>Total pedidos enviados (sin filtro):</strong> " . count($todos_pedidos) . "</p>";

if(count($todos_pedidos) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Número</th><th>Mesa</th><th>Productos (con categorías)</th><th>Total</th></tr>";
    foreach($todos_pedidos as $pedido) {
        echo "<tr>";
        echo "<td>{$pedido['pedido_id']}</td>";
        echo "<td>{$pedido['numero_pedido']}</td>";
        echo "<td>{$pedido['mesa_numero']}</td>";
        echo "<td style='font-size: 12px;'>{$pedido['productos_detalle']}</td>";
        echo "<td>$" . number_format($pedido['total_precio'], 0) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

echo "<hr>";
echo "<p><a href='pedidosCocinaPendientes'>← Volver a Pedidos Cocina</a></p>";

?>
