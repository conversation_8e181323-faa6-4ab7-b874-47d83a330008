<?php

session_start();

if(!isset($_SESSION["validar"]) || !$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Test AJAX Pedido 8</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
</head>
<body>

<div class="container">
    <h2>🧪 Test: AJAX Pedido 8 Específico</h2>
    <hr>
    
    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">📊 Información de sesión</h3>
        </div>
        <div class="panel-body">
            <p><strong>Usuario:</strong> <?php echo $_SESSION['persona'] ?? 'No definido'; ?></p>
            <p><strong>ID Usuario:</strong> <?php echo $_SESSION['usuario'] ?? 'No definido'; ?></p>
            <p><strong>Tipo Usuario:</strong> <?php echo $_SESSION['tipo_usuario'] ?? 'No definido'; ?></p>
        </div>
    </div>
    
    <div class="panel panel-danger">
        <div class="panel-heading">
            <h3 class="panel-title">🎯 Test Específico: Pedido ID 8</h3>
        </div>
        <div class="panel-body">
            <button onclick="testPedido8()" class="btn btn-danger btn-lg">🧪 Test Marcar Pedido 8</button>
            <div id="result-8" style="margin-top: 15px;"></div>
        </div>
    </div>
    
    <div class="panel panel-warning">
        <div class="panel-heading">
            <h3 class="panel-title">🔍 Test con diferentes métodos</h3>
        </div>
        <div class="panel-body">
            <div class="row">
                <div class="col-md-4">
                    <button onclick="testConFetch()" class="btn btn-warning">🚀 Test con Fetch</button>
                    <div id="fetch-result" style="margin-top: 10px;"></div>
                </div>
                <div class="col-md-4">
                    <button onclick="testConXHR()" class="btn btn-info">📡 Test con XHR</button>
                    <div id="xhr-result" style="margin-top: 10px;"></div>
                </div>
                <div class="col-md-4">
                    <button onclick="testConJQuery()" class="btn btn-success">💎 Test con jQuery</button>
                    <div id="jquery-result" style="margin-top: 10px;"></div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="panel panel-primary">
        <div class="panel-heading">
            <h3 class="panel-title">📋 Log Detallado</h3>
        </div>
        <div class="panel-body">
            <div id="detailed-log" style="background: #f5f5f5; padding: 15px; min-height: 200px; font-family: monospace; font-size: 12px; white-space: pre-wrap;"></div>
            <button onclick="clearLog()" class="btn btn-sm btn-default">🗑️ Limpiar Log</button>
        </div>
    </div>
    
    <hr>
    <div class="row">
        <div class="col-md-4">
            <a href="index.php?action=debug_estado_pedidos" class="btn btn-primary btn-block">🔙 Debug Estado</a>
        </div>
        <div class="col-md-4">
            <a href="index.php?action=pedidosBarPendientes" class="btn btn-info btn-block">🍺 Volver a Bar</a>
        </div>
        <div class="col-md-4">
            <a href="index.php?action=test_ajax_marcar_entregado" class="btn btn-warning btn-block">🧪 Test General</a>
        </div>
    </div>
</div>

<script>
function log(message) {
    const timestamp = new Date().toLocaleTimeString();
    const logDiv = document.getElementById('detailed-log');
    logDiv.textContent += `[${timestamp}] ${message}\n`;
    logDiv.scrollTop = logDiv.scrollHeight;
}

function clearLog() {
    document.getElementById('detailed-log').textContent = '';
}

function testPedido8() {
    log('🎯 Iniciando test específico para pedido ID 8...');
    
    const data = {
        accion: 'marcar_entregado',
        pedido_id: 8
    };
    
    log('📤 Datos a enviar: ' + JSON.stringify(data));
    
    $.ajax({
        url: 'views/modules/ajaxPedidosCategorias.php',
        method: 'POST',
        dataType: 'json',
        data: JSON.stringify(data),
        contentType: 'application/json',
        timeout: 10000, // 10 segundos de timeout
        beforeSend: function() {
            log('📡 Enviando petición AJAX...');
            $('#result-8').html('<div class="alert alert-info">🔄 Procesando...</div>');
        },
        success: function(response) {
            log('✅ Respuesta exitosa: ' + JSON.stringify(response));
            $('#result-8').html('<div class="alert alert-success">✅ Éxito: ' + JSON.stringify(response) + '</div>');
        },
        error: function(xhr, status, error) {
            log('❌ Error AJAX:');
            log('   - Status: ' + status);
            log('   - Error: ' + error);
            log('   - HTTP Status: ' + xhr.status);
            log('   - Response Text: ' + xhr.responseText);
            log('   - Ready State: ' + xhr.readyState);
            
            $('#result-8').html(`
                <div class="alert alert-danger">
                    <strong>❌ Error:</strong><br>
                    <strong>Status:</strong> ${status}<br>
                    <strong>Error:</strong> ${error}<br>
                    <strong>HTTP Status:</strong> ${xhr.status}<br>
                    <strong>Response:</strong> ${xhr.responseText}<br>
                    <strong>Ready State:</strong> ${xhr.readyState}
                </div>
            `);
        },
        complete: function() {
            log('🏁 Petición AJAX completada');
        }
    });
}

function testConFetch() {
    log('🚀 Test con Fetch API...');
    
    fetch('views/modules/ajaxPedidosCategorias.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            accion: 'marcar_entregado',
            pedido_id: 8
        })
    })
    .then(response => {
        log('📡 Fetch response status: ' + response.status);
        log('📡 Fetch response ok: ' + response.ok);
        return response.text();
    })
    .then(text => {
        log('📄 Fetch response text: ' + text);
        document.getElementById('fetch-result').innerHTML = 
            '<div class="alert alert-success">✅ Fetch: <pre>' + text + '</pre></div>';
    })
    .catch(error => {
        log('❌ Fetch error: ' + error.message);
        document.getElementById('fetch-result').innerHTML = 
            '<div class="alert alert-danger">❌ Fetch Error: ' + error.message + '</div>';
    });
}

function testConXHR() {
    log('📡 Test con XMLHttpRequest...');
    
    var xhr = new XMLHttpRequest();
    xhr.open('POST', 'views/modules/ajaxPedidosCategorias.php', true);
    xhr.setRequestHeader('Content-Type', 'application/json');
    
    xhr.onreadystatechange = function() {
        log('📊 XHR readyState: ' + xhr.readyState + ', status: ' + xhr.status);
        
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                log('✅ XHR success: ' + xhr.responseText);
                document.getElementById('xhr-result').innerHTML = 
                    '<div class="alert alert-success">✅ XHR: <pre>' + xhr.responseText + '</pre></div>';
            } else {
                log('❌ XHR error: ' + xhr.status + ' - ' + xhr.responseText);
                document.getElementById('xhr-result').innerHTML = 
                    '<div class="alert alert-danger">❌ XHR Error: ' + xhr.status + ' - ' + xhr.responseText + '</div>';
            }
        }
    };
    
    xhr.onerror = function() {
        log('❌ XHR network error');
        document.getElementById('xhr-result').innerHTML = 
            '<div class="alert alert-danger">❌ XHR Network Error</div>';
    };
    
    xhr.send(JSON.stringify({
        accion: 'marcar_entregado',
        pedido_id: 8
    }));
}

function testConJQuery() {
    log('💎 Test con jQuery AJAX...');
    
    $.post({
        url: 'views/modules/ajaxPedidosCategorias.php',
        data: JSON.stringify({
            accion: 'marcar_entregado',
            pedido_id: 8
        }),
        contentType: 'application/json',
        dataType: 'json'
    })
    .done(function(response) {
        log('✅ jQuery success: ' + JSON.stringify(response));
        document.getElementById('jquery-result').innerHTML = 
            '<div class="alert alert-success">✅ jQuery: ' + JSON.stringify(response) + '</div>';
    })
    .fail(function(xhr, status, error) {
        log('❌ jQuery error: ' + status + ' - ' + error + ' - ' + xhr.responseText);
        document.getElementById('jquery-result').innerHTML = 
            '<div class="alert alert-danger">❌ jQuery Error: ' + status + ' - ' + error + '<br>Response: ' + xhr.responseText + '</div>';
    });
}

// Auto-ejecutar test al cargar
$(document).ready(function() {
    log('🚀 Página cargada, listo para tests...');
});
</script>

</body>
</html>
