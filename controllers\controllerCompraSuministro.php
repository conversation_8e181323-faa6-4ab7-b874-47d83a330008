<?php
ob_start();
class controllerCompraSuministro extends <PERSON><PERSON><PERSON><PERSON><PERSON>roller
 {
 	#REGISTRO DE COMPRA SUMINISTRO tabla temporal pendiente
	#------------------------------------
	 public function registroCsuministroPController()
		{//echo '<script>alert("entro controle");</script>';
		  $pendiente = DatosComprasuministro::pendienteFacturaModel();
		 if ($pendiente==0)
			{ //echo '<script>alert("entro Registrar");</script>';


			 if (-1==$_POST["proveedores"])
				{
					echo '<script>alert("seleciones un proveedor");</script>';
					header("location:index.php?action=falloCs");
				}
			 else
				{
				 if(isset($_POST["proveedores"]))
					{	//echo '<script>alert("entro controle");</script>';
						session_start();
						$datosController =array('proveedor_id'=>$_POST["proveedores"],
												'valor_compra'=>$_POST["valor_compraCsuministroRegistro"],
												'fecha_hora'=>$_POST["fecha_horaCsuministroRegistro"],
												'numero_factura_compra'=>$_POST["numero_factura_compra"],
												'pago'=>$_POST["pago"],
												'abono'=>$_POST["abono"]
											);
					 $facturaPro = DatosComprasuministro::poveedorFacturaModel($datosController["proveedor_id"], $datosController["numero_factura_compra"]);
					 if ($facturaPro==0)
					  	{
					 	 //echo '<script>alert("entro controle IF");</script>';
					 	 $respuesta = DatosComprasuministro::registroCsuministroPModel($datosController, "pendiente_factura");
					 	 //echo "<script>alert(' Controller id mayor ".$respuesta['idcompra']." regrso')</script>";
						 //echo "<br>proveedor_id".$datosController['proveedor_id']."<br> valor_compra".$datosController['valor_compra']."<br>  fecha_hora".$datosController['fecha_hora']."<br> numero_factura_compra".$datosController['numero_factura_compra']."<br> proveedor_id".$datosController['proveedor_id'];
						 if($respuesta['success'] == "success")
						 //if($respuesta == "success")
							{
				 			 $_SESSION["fecha_factura"]=$datosController["fecha_hora"];
				 			 $_SESSION["pago"]= $datosController["pago"];
				 			 $_SESSION["abono"]=$datosController["abono"];
							 $_SESSION["ver"]=$respuesta['idcompra'];
							 $_SESSION["facturaN"]=$datosController["numero_factura_compra"];
							 $_SESSION["valorT"]=$datosController["valor_compra"];
							 $_SESSION["proveedor_id"]=$datosController["proveedor_id"];
							 echo '<script>alert("Final Exitoso");</script>';
							 //echo "<script>alert(' Variables de session ".$_session['proveedor']." regrso')</script>";
							 header("location:index.php?action=registroFacturaSuministro&id=".$_SESSION["ver"]);
							}
						 else
							{	header("location:index.php?action=falloCs");	}
						}
					 else
					 	echo '<script>alert("Ese No de factura con ese proveedor ya existe, verifique");</script>';
					}
				}
			}
		 else
		 	{ echo '<script>alert("entro Esle factura pendiente");</script>';
		 	 //(id, proveedor_id, valor_factura, fecha_factura, numero_factura, pago, abono, suministro_id, cantidad, precio, precioV, descuento, facturado)
		 		$_SESSION["ver"]=$pendiente['id'];
				$_SESSION["facturaN"]=$pendiente["numero_factura"];
				$_SESSION["valorT"]=$pendiente["valor_factura"];
				$_SESSION["proveedor_id"]=$pendiente["proveedor_id"];
				$_SESSION["fecha_factura"]=$pendiente["fecha_factura"];
				$_SESSION["pago"]= $pendiente["pago"];
				$_SESSION["abono"]=$pendiente["abono"];
				echo '<script>alert("Final Exitoso");</script>';
				//echo "<script>alert(' Variables de session ".$_session['proveedor']." regrso')</script>";
				header("location:index.php?action=registroFacturaSuministro&id=".$_SESSION["ver"]);
			}
		}
	#-----------------------------------
 	#REGISTRO DE COMPRA SUMINISTRO
	#------------------------------------
	 public function registroCsuministroController()
		{//echo '<script>alert("entro controle");</script>';
		 if (-1==$_POST["proveedores"])
			{
				echo '<script>alert("seleciones un proveedor");</script>';
				header("location:index.php?action=falloCs");
			}
		 else
			{
				if(isset($_POST["proveedores"]))
				{
					$datosController =array('proveedor_id'=>$_POST["proveedores"],
											'valor_compra'=>$_POST["valor_compraCsuministroRegistro"],
											'fecha_hora'=>$_POST["fecha_horaCsuministroRegistro"],
											'numero_factura_compra'=>$_POST["numero_factura_compraCsuministroRegistro"]);
					//echo '<script>alert("entro controle IF");</script>';
					$respuesta = DatosComprasuministro::registroCsuministroModel($datosController, "compras_suministros");
					//echo "<script>alert(' Controller id mayor ".$respuesta['idcompra']." regrso')</script>";
					//echo "<br>proveedor_id".$datosController['proveedor_id']."<br> valor_compra".$datosController['valor_compra']."<br> fecha_hora".$datosController['fecha_hora']."<br> numero_factura_compra".$datosController['numero_factura_compra']."<br> proveedor_id".$datosController['proveedor_id'];
					if($respuesta['success'] == "success")
					//if($respuesta == "success")
						{	session_start();
							$_SESSION["ver"]=$respuesta['idcompra'];
							$_SESSION["facturaN"]=$datosController["numero_factura_compra"];
							$_SESSION["valorT"]=$datosController["valor_compra"];
							$_SESSION["proveedor_id"]=$datosController["proveedor_id"];
							echo '<script>alert("Final Exitoso");</script>';
							//echo "<script>alert(' Variables de session ".$_session['proveedor']." regrso')</script>";
							header("location:index.php?action=registroFacturaSuministro&id=".$_SESSION["ver"]);
						}
					else
						{	header("location:index.php?action=falloCs");	}
				/**/}
			}
		}
	#-----------------------------------
 	#REGISTRO DE COMPRA SUMINISTRO Aja Unificado
	#------------------------------------
	 public function registroCompraSuministroAjaTController($datos)
		{	//echo "<script>alert(' Variables de session ".$datos["numero_factura_compra"]." nombre ".$datos["nombre"]." cantidad ".$datos["codigo"]." precio ".$datos["sid"]." PROVEEDOR ".$datos["precioV"]."')</script>";
		 if (-1==$datos["proveedor_id"])
			{	echo '<script>alert("seleciones un proveedor");</script>';
				header("location:index.php?action=falloCs");
			}
		 else
			{	//$i=1;	echo"<script>alert('if era dos: "."')</script>";
			 	//echo"<script>alert('if ".$datos['cantidad']."')</script>";
			 $datosController=array('proveedor_id'=>$datos["proveedor_id"],
									'valor_compra'=>$datos["valor_compra"],
									'fecha_hora'=>$datos["fecha_hora"],
									'pago'=>$datos["pago"],
									'abono'=>$datos["abono"],
									'numero_factura_compra'=>$datos["numero_factura_compra"],
									'nombre' => $datos['nombre'],
									'codigo' => $datos['codigo'],
									'id' => $datos['sid'],
									'precioC' => $datos['precioC'],
									'cantidad' => $datos['cantidad'],
									'precioV' => $datos['precioV']
									);
			 $respuesta = DatosComprasuministro::registroCsuministroAjaTModel($datosController, "compra_suministro");
			/* echo "<script>alert(' Controller id mayor ".$respuesta['idcompra']." regrso')</script>";*/
			 if($respuesta['success'] == 'success')
			 //if($respuesta == "success")
			  	{	//session_start();
			  	 $respuesta1 = DatosComprasuministro::temporalFacturaModen($datosController['proveedor_id'], $datosController['numero_factura_compra']);
			 	 $_SESSION["ver"]=$respuesta['idcompra'];
			 	 echo '<script>alert("Final Exitoso");</script>';
			 	 return $respuesta1;
			 	 //echo "<script>alert(' Variables de session ".$_session['proveedor']." regrso')</script>";
			 	 //header("location:Csuministro");	echo '<script>alert("Final Exitoso");</script>';
			 	}
			 else
			  	{	echo '<script>alert("ERRORRRRRRRRRR");</script>';
			 	 //header("location:index.php?action=falloCs");
				}
			  }
		}
	#---------------------------------------
 	#REGISTRO DE COMPRA SUMINISTRO Aja Unificado tabla pendiente
	#------------------------------------
	 public function registroCompraSuministroAjaController()
		{	//echo "<script>alert(' entro Controller bien')</script>";
		 $respuesta = DatosComprasuministro::registroCsuministroAja1Model("compras_suministros");
		/* echo "<script>alert(' Controller id mayor ".$respuesta['idcompra']." regrso')</script>";*/
		// if($respuesta['success'] == 'success')
		 if($respuesta == "success")
		  	{	//session_start();
		 	 // echo '<script>alert("Final Exitoso");</script>';
		 	 // return $respuesta1;
		 	 echo'<script>alert(" Registro hecho");	location.href ="Csuministro";</script>';
		 	 unset($_SESSION["ver"]);
			 unset($_SESSION["proveedor"]);
			 unset($_SESSION["valorT"]);
			 unset($_SESSION["facturaN"]);
			 unset($_SESSION["abono"]);
			 unset($_SESSION["fecha_factura"]);
			 unset($_SESSION["pago"]);
		 	 //echo "<script>alert(' Variables de session ".$_session['proveedor']." regrso')</script>";
		 	 //header("location:Csuministro");echo'<script>alert("Exitos");</script>';header("location:index.php?action=Csuministro");


		 	}
		 else
		  	{	echo '<script>alert("ERRORRRRRRRRRR");</script>';
		 	 //header("location:index.php?action=falloCs");
			}/*	*/
		}
	#---------------------------------------
	#REGISTRO DE COMPRA SUMINISTRO Unificado
	#------------------------------------
	 public function registroCompraSuministroController()
		{	//echo "<script>alert(' Variables de session ".$_POST["fecha_horaCsuministroRegistro"]." nombre ".$_POST["nombre1"]." cantidad ".$_POST["cantidad1"]." precio ".$_POST["precio1"]." PROVEEDOR ".$_POST["proveedores"]."')</script>";
			//echo "<script>alert(' fila ".$_POST["fila"]." nombre ".$_POST["nombre1"]." ')</script>";
			if (-1==$_POST["proveedores"])
			 {	echo '<script>alert("seleciones un proveedor");</script>';
				header("location:index.php?action=falloCs");
			 }
			else
			 {	//$i=1;	echo"<script>alert('if era dos: ".$i."')</script>";
				if(isset($_POST["proveedores"]))
				 {	//echo"<script>alert('if ".$_POST['cantidad'.$i]."')</script>";
					$fila=$_POST["fila"];
					$datosController =array('proveedor_id'=>$_POST["proveedores"],
											'valor_compra'=>$_POST["valor_compraCsuministroRegistro"],
											'fecha_hora'=>$_POST["fecha_horaCsuministroRegistro"],
											'pago'=>$_POST["pago"],
											'abono'=>$_POST["abono"],
											'contadorFilas'=>$fila,
											'numero_factura_compra'=>$_POST["numero_factura_compra"]);
					//echo"<script>alert('proveedor ".$datosController['proveedor_id']." -valor_compra".$datosController['valor_compra']." -fecha_hora".$datosController['fecha_hora']." -pago".$datosController['pago']." -abono".$datosController['abono']." - contadorFilas".$datosController['contadorFilas']."  -numero_factura_compra".$datosController['numero_factura_compra']."   ')</script>";
					$totalSumi =0;
					for ($i=1; $i <=$fila ; $i++)
					 {
					 	echo"<script>alert(' entra al for suministro o productos compra ".$_POST['cantidad'.$i]."')</script>";
					 	$compra[$i]  = array('nombre' => $_POST['nombre'.$i],
											 'codigo' => $_POST['codigo'.$i],
											 'descuento' => $_POST['descuento'.$i],
											 'id' => $_POST['sid'.$i],
											 'precioC' => $_POST['precioC'.$i],
											 'cantidad' => $_POST['cantidad'.$i],
											 'precioV' => $_POST['precioV'.$i]);
					 	$totalSumi = $totalSumi + $_POST['precioC'.$i] *$_POST['cantidad'.$i];
						//echo "<script>alert('Cantida for ".$compra[$i]['cantidad']." es ".$i." ')</script>";
					 }//nombre codigo cantidad referencia genero talla marca color diseno precioC precioV
					$diferencia = $datosController['valor_compra'] - $totalSumi;
					if ($diferencia==0)
					 {
						$respuesta = DatosComprasuministro::registroCsuministroModel($datosController, "compras_suministros", $compra);
						//echo "<script>alert(' Controller id mayor ".$respuesta['idcompra']." regrso')</script>";
						//echo '<script>alert("Volvio controle");</script>';
						if($respuesta['success'] == 'success')
						//if($respuesta == "success")
						 {	//session_start();
							$_SESSION[$i]["ver"]=$respuesta['idcompra'];
							echo '<script>alert("Final Exitoso");</script>';
							//echo "<script>alert(' Variables de session ".$_session['proveedor']." regrso')</script>";
							//header("location:Csuministro");
						 }
						else
						 {	echo '<script>alert("ERRORRRRRRRRRR");</script>';
							//header("location:index.php?action=falloCs");
						 }
					 }
					else
					 {
					 	$_SESSION["facturaCompra"]=$datosController;
					 	$_SESSION["facturaDetalle"]=$compra;
					 	echo "<script>alert('el total de los suministros no cuadra con el valor de la factura, tiene una diferencia de : ".$diferencia." ');</script>";
					 	//header("location:registroCsuministro1");
					 }
				 }
			 }

		}
	#---------------------------------------
	#DIFERENCIA
	#---------------------------------------
		public function diferenciaController()
			{
				echo'<h1>REGISTRO DE COMPRA SUMINISTRO</h1>
				<div>
				<form action="" method="post" name="cal">
					<div>
					<div  class="col-md-4" style="background-color:#00f; border-radius: 15px 15px 15px 15px; padding:12px">
						<h4 >Datos de la Factura</h4>
						<table>
					<tr>
						<td align="right"><label> PROVEEDOR </label></td>
						<td>
							 <input type="text" name="proveedor" value="'.$_SESSION["facturaCompra"]["proveedor_id"].'" required></td>
					</tr>
					<tr>
						<td align="right"><label>  VALOR FACTURA  </label></td>
						<td><input type="text" value="'.$_SESSION["facturaCompra"]["valor_compra"].'" name="valor_compraCsuministroRegistro" required></td>
					</tr>
					<tr>
						<td align="right"><label> FECHA </label></td>
						<td><input type="text" value="'.$_SESSION["facturaCompra"]["fecha_hora"].'" name="fecha_horaCsuministroRegistro" required>
						</td>
					</tr>
					<tr>
						<td align="right"><label> No FACTURA  </label></td>
						<td><input type="text" value="'.$_SESSION["facturaCompra"]["numero_factura_compranumero"].'" name="numero_factura_compra" ></td>
					</tr>
					<tr>
						<td align="right"><label> ESTADO  </label></td>
						<td>
						<div >
					  		<select name="pago" id="pago" onchange="habilitar(this.value);">
						  <option value="1">Cancelada</option>
						  <option value="2">Credito</option>

						</select>
					  	</div></td>
					</tr>
					<tr>
						<td align="right"><label  > ABONO  </label></td>
						<td><input type="text"  name="abono" value="'.$_SESSION["facturaCompra"]["abono"].'" id="abono" ></td>
					</tr>
			</table>

					</div>


				<div  class="col-md-8" style="background-color:#FFF; border-radius: 15px 15px 15px 15px; padding:12px">

					<H3>Detalle de la Factura</H3>
					<table>

						<tr>
							<td align="right">Suministro:</td>
							<td><input type="text" name="nombre" id="nombre"></td>

							<td align="right">Cantidad</td>
							<td><input type="text" name="cantidad" id="cantidad"></td>

							<td align="right">Precio:</td>
							<td><input type="text" name="precio" id="precio" ></td>
						</tr>
					</table>

					<input type="hidden" name="fila" id="fila"> <br>
					<input type="button" id="add" value="Agregar" class="btn btn-sm btn-success">
					<input type="button" id="del" value="eliminar la ultima fila " class="btn btn-sm btn-danger">
					<button type="submit" name="datos" class="btn btn-sm btn-primary" >Guardar </button>
					<button type="reset" name="borrar" class="btn btn-sm btn-warning" >Limpiar </button>
					<p>
				    <table id="tabla" class="table" border=1>
				        <tr>
				            <td>Suministro</td>
				            <td>Cantidad</td>
				            <td>Precio</td>
				            <!-- podemos añadir tantas columnas como deseemos -->
				            <!--<td>tercera columna</td><input type="submit" value="Enviar">-->
				        </tr>
				    </table>

				</div>
				<div id="destino">	</div>
				</div>
				</form><br>
				<br>
				<br><br>
				<br>
				<br><br>
				<br>
				<br>
				</div>';
			}
	#--------------------------------------------
	#REGISTRO DE COMPRA SUMINISTRO SELECT id, proveedor_id, valor_compra, fecha_hora, numero_factura_compra FROM compras_suministros WHERE
	#------------------------------------
	 public function registroCsuministro2Controller()
		{//echo '<script>alert("entro controle");</script>';
			/*$datosController =array('proveedor_id'=>$_POST["proveedores"],
									'valor_compra'=>$_POST["valor_compra"],
									'fecha_horaC'=>$_POST["fecha_horaC"],
									'pago'=>$_POST["pago"],
									'abono'=>$_POST["abono"],
									'numero_factura_compra'=>$_POST["numero_factura_compra"]);
			*/
			//echo "<script>alert('Antes de la funcion CRUD".$datosController['fecha_horaC']." tipo de pago2: ".$datosController['pago']."  3".$datosController['proveedor_id']." 4 ".$datosController['valor_compra']." 5".$datosController['abono']."  6".$datosController['numero_factura_compra']."')</script>";
			$respuesta = DatosComprasuministro::registroCsuministro1Model($datosController, "compras_suministros");
			//echo "<script>alert(' Controller id mayor ".$respuesta['idcompra']." regrso')</script>";
			//echo '<script>alert("Volvio controle");</script>';


			return 	$respuesta;
		}
	#-------------------------------------
	#VISTA DE COMPRA SUMINISTRO <!--<td>'.$item["proveedor_id"].'</td>-->
	#------------------------------------
	 public function vistaCsuministroController()
		{
		 $respuesta = DatosComprasuministro::vistaCsuministroModel("compras_suministros");
		 foreach($respuesta as $row => $item)
			{
			 echo'
			 	<tr>

					<td>'.$item["proveedor"].'</td>
					<td>'.$item["valor_compra"].'</td>
					<td>'.$item["fecha_hora"].'</td>
					<td>'.$item["numero_factura_compra"].'</td>
					<td>'.$item["fecha_modificacion"].'</td>
					<td><a href="index.php?action=registroFacturaSuministro1&id='.$item["id"].'">Ver</a></td>

				</tr>';
			}
		}//<td><a href="index.php?action=Csuministro&idBorrar='.$item["id"].'">Borrar</a></td>
	#---------------------------------
	#EDITAR COMPRA SUMINISTRO
	#------------------------------------
		public function editarCsuministroController()
			{
				$datosController = $_GET["id"];
				$respuesta = DatosComprasuministro::editarCsuministroModel($datosController, "compras_suministros");
				echo'<input type="text" value="'.$respuesta["proveedor_id"].'" name="proveedor_idEditar" required>
				 <input type="text" value="'.$respuesta["valor_compra"].'" name="valor_compraEditar" required>
				 <input type="text" value="'.$respuesta["fecha_hora"].'" name="fecha_horaEditar" required>
				 <input type="text" value="'.$respuesta["numero_factura_compra"].'" name="numero_factura_compraEditar" required>
				 <input type="hidden" value="'.$respuesta["id"].'" name="idEditar" required>
					 <input type="submit" value="Actualizar">';

			}
	#---------------------------------
	#ACTUALIZAR COMPRA SUMINISTRO
	#------------------------------------
	 public function actualizarCsuministroController()
		{//echo "<script>alert('Entro Controller Actualizar Producto')</script>";

		 if(isset($_POST["proveedor_idEditar"]))
			{
				$datosController = array(  "proveedor_id"=>$_POST["proveedor_idEditar"],
											"valor_compra"=>$_POST["valor_compraEditar"],
											"fecha_hora"=>$_POST["fecha_horaEditar"],
											"numero_factura_compra"=>$_POST["numero_factura_compraEditar"],
											"id"=>$_POST["idEditar"]);
				$respuesta = DatosComprasuministro::actualizarCsuministroModel($datosController, "compras_suministros");
				if($respuesta == "success")
					{	header("location:index.php?action=cambioCs");	}
				else
					{	echo "error";	}
			}
		}
	#---------------------------------------
	#---------------------------------
	#BORRAR COMPRA SUMINISTRO
	#------------------------------------
	 public function borrarCsuministroController()
		{
			if(isset($_GET["idBorrar"]))
				{
					$datosController = $_GET["idBorrar"];
					$respuesta = DatosComprasuministro::borrarCsuministroModel($datosController, "compras_suministros");
					if($respuesta == "success")
						{	header("location:index.php?action=Csuministro");	}
				}
		}
    #---------------------------------

	##LISTA Proveedores
	#---------------------------------------
	 public function listaProveedoresController()
		{
			$respuesta = DatosComprasuministro::listaProveedoresModel("proveedores");
			return 	$respuesta;
		}/**/
	#---------------------------------------

}
