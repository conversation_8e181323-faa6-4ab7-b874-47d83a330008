<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Impresión Directa - Macarena</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; }
        .impresora { background: white; padding: 20px; margin: 15px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .btn { background-color: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold; width: 100%; margin-top: 10px; }
        .btn:hover { background-color: #0056b3; }
        .btn:disabled { background-color: #6c757d; cursor: not-allowed; }
        .resultado { margin-top: 15px; padding: 10px; border-radius: 5px; }
        .exito { background-color: #d4edda; border-left: 4px solid #28a745; }
        .error { background-color: #f8d7da; border-left: 4px solid #dc3545; }
        textarea { width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 3px; font-family: monospace; resize: vertical; }
        .info { background-color: #d1ecf1; padding: 15px; border-left: 4px solid #17a2b8; margin-bottom: 20px; }
        .impresora-titulo { color: #007bff; margin-bottom: 15px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖨️ Test Impresión Directa</h1>
        
        <div class="info">
            <h4>📡 Configuración de Red Actual</h4>
            <p><strong>Tu IP:</strong> <?php echo $_SERVER['REMOTE_ADDR']; ?></p>
            <p><strong>Impresoras configuradas:</strong></p>
            <ul>
                <li><strong>Bar:</strong> **************:9100</li>
                <li><strong>Cocina:</strong> **************:9100</li>
                <li><strong>Asados:</strong> **************:9100</li>
            </ul>
            <p>✅ <strong>Sin verificaciones</strong> - Impresión directa optimizada</p>
        </div>

        <!-- Impresora BAR -->
        <div class="impresora">
            <h3 class="impresora-titulo">🍺 BAR (**************)</h3>
            <textarea id="contenido_bar" rows="6">Mesa 5
Cerveza Corona x2
Whisky Etiqueta Negra x1
Agua con Gas x1
--------------------------------
TOTAL: $45.000</textarea>
            <button class="btn" onclick="imprimir('bar')" id="btn_bar">🖨️ Imprimir en BAR</button>
            <div id="resultado_bar" class="resultado" style="display: none;"></div>
        </div>

        <!-- Impresora COCINA -->
        <div class="impresora">
            <h3 class="impresora-titulo">🍳 COCINA (**************)</h3>
            <textarea id="contenido_cocina" rows="6">Mesa 3
Bandeja Paisa x1
Sancocho de Gallina x2
Arroz con Pollo x1
--------------------------------
TOTAL: $65.000</textarea>
            <button class="btn" onclick="imprimir('cocina')" id="btn_cocina">🖨️ Imprimir en COCINA</button>
            <div id="resultado_cocina" class="resultado" style="display: none;"></div>
        </div>

        <!-- Impresora ASADOS -->
        <div class="impresora">
            <h3 class="impresora-titulo">🥩 ASADOS (**************)</h3>
            <textarea id="contenido_asados" rows="6">Mesa 7
Churrasco x1
Costillas BBQ x1
Chorizo Argentino x2
--------------------------------
TOTAL: $85.000</textarea>
            <button class="btn" onclick="imprimir('asados')" id="btn_asados">🖨️ Imprimir en ASADOS</button>
            <div id="resultado_asados" class="resultado" style="display: none;"></div>
        </div>

        <!-- Test masivo -->
        <div class="impresora" style="background-color: #fff3cd;">
            <h3 style="color: #856404;">🚀 Test Masivo - Todas las Impresoras</h3>
            <p>Envía un pedido de prueba a las 3 impresoras simultáneamente</p>
            <button class="btn" onclick="testMasivo()" id="btn_masivo" style="background-color: #ffc107; color: #212529;">🚀 Imprimir en TODAS</button>
            <div id="resultado_masivo" class="resultado" style="display: none;"></div>
        </div>
    </div>

    <script>
        // Función principal de impresión
        async function imprimir(categoria) {
            const contenido = document.getElementById('contenido_' + categoria).value;
            const boton = document.getElementById('btn_' + categoria);
            const resultado = document.getElementById('resultado_' + categoria);
            
            // UI feedback
            boton.disabled = true;
            boton.innerHTML = '🔄 Imprimiendo...';
            resultado.style.display = 'block';
            resultado.className = 'resultado';
            resultado.innerHTML = '<p>⏳ Enviando a impresora...</p>';
            
            try {
                const response = await fetch('../../api/imprimir_directo.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        categoria: categoria,
                        contenido: contenido
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultado.className = 'resultado exito';
                    resultado.innerHTML = `
                        <p>✅ <strong>Impresión Exitosa</strong></p>
                        <p>Impresora: ${data.categoria.toUpperCase()}</p>
                        <p>IP: ${data.ip}</p>
                        <p>Bytes enviados: ${data.bytes_enviados}</p>
                        <p>Hora: ${data.timestamp}</p>
                    `;
                } else {
                    resultado.className = 'resultado error';
                    resultado.innerHTML = `
                        <p>❌ <strong>Error de Impresión</strong></p>
                        <p>Impresora: ${categoria.toUpperCase()}</p>
                        <p>Error: ${data.error}</p>
                    `;
                }
                
            } catch (error) {
                resultado.className = 'resultado error';
                resultado.innerHTML = `
                    <p>❌ <strong>Error de Conexión</strong></p>
                    <p>Error: ${error.message}</p>
                `;
            }
            
            // Restaurar botón
            boton.disabled = false;
            boton.innerHTML = `🖨️ Imprimir en ${categoria.toUpperCase()}`;
        }
        
        // Test masivo
        async function testMasivo() {
            const boton = document.getElementById('btn_masivo');
            const resultado = document.getElementById('resultado_masivo');
            
            boton.disabled = true;
            boton.innerHTML = '🔄 Imprimiendo en todas...';
            resultado.style.display = 'block';
            resultado.className = 'resultado';
            resultado.innerHTML = '<p>⏳ Enviando a las 3 impresoras...</p>';
            
            const categorias = ['bar', 'cocina', 'asados'];
            const contenidos = {
                'bar': 'TEST MASIVO - BAR\nCerveza x1\nHora: ' + new Date().toLocaleTimeString(),
                'cocina': 'TEST MASIVO - COCINA\nBandeja Paisa x1\nHora: ' + new Date().toLocaleTimeString(),
                'asados': 'TEST MASIVO - ASADOS\nChurrasco x1\nHora: ' + new Date().toLocaleTimeString()
            };
            
            const promesas = categorias.map(categoria =>
                fetch('../../api/imprimir_directo.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        categoria: categoria,
                        contenido: contenidos[categoria]
                    })
                }).then(r => r.json()).then(data => ({categoria, data}))
            );
            
            try {
                const resultados = await Promise.all(promesas);
                
                let html = '<h4>📊 Resultados del Test Masivo:</h4>';
                let exitosos = 0;
                
                resultados.forEach(({categoria, data}) => {
                    if (data.success) {
                        html += `<p>✅ <strong>${categoria.toUpperCase()}:</strong> Exitoso (${data.bytes_enviados} bytes)</p>`;
                        exitosos++;
                    } else {
                        html += `<p>❌ <strong>${categoria.toUpperCase()}:</strong> Error - ${data.error}</p>`;
                    }
                });
                
                html += `<p><strong>Resumen:</strong> ${exitosos}/3 impresoras exitosas</p>`;
                
                resultado.className = exitosos === 3 ? 'resultado exito' : 'resultado error';
                resultado.innerHTML = html;
                
            } catch (error) {
                resultado.className = 'resultado error';
                resultado.innerHTML = `<p>❌ Error en test masivo: ${error.message}</p>`;
            }
            
            boton.disabled = false;
            boton.innerHTML = '🚀 Imprimir en TODAS';
        }
        
        // Auto-focus en el primer textarea
        document.getElementById('contenido_bar').focus();
    </script>
</body>
</html>
