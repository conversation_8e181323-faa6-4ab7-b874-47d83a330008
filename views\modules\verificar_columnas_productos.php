<?php
// Verificar estructura de la tabla productos
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once "../../models/conexion.php";

echo "<h1>🔍 Verificar Estructura Tabla Productos</h1>";

try {
    $db = Conexion::conectar();
    
    // Mostrar estructura de la tabla productos
    $sql = "DESCRIBE productos";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $columnas = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>📋 Columnas de la tabla 'productos':</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    foreach ($columnas as $columna) {
        echo "<tr>";
        echo "<td><strong>" . $columna['Field'] . "</strong></td>";
        echo "<td>" . $columna['Type'] . "</td>";
        echo "<td>" . $columna['Null'] . "</td>";
        echo "<td>" . $columna['Key'] . "</td>";
        echo "<td>" . $columna['Default'] . "</td>";
        echo "<td>" . $columna['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Buscar columnas relacionadas con costo/compra/valor
    echo "<h3>🔍 Columnas relacionadas con costo/compra/valor:</h3>";
    $columnasCosto = [];
    foreach ($columnas as $columna) {
        $campo = strtolower($columna['Field']);
        if (strpos($campo, 'costo') !== false || 
            strpos($campo, 'compra') !== false || 
            strpos($campo, 'valor') !== false ||
            strpos($campo, 'precio') !== false) {
            $columnasCosto[] = $columna['Field'];
        }
    }
    
    if (!empty($columnasCosto)) {
        echo "<ul>";
        foreach ($columnasCosto as $columna) {
            echo "<li><strong>$columna</strong></li>";
        }
        echo "</ul>";
    } else {
        echo "<p>No se encontraron columnas relacionadas con costo/compra/valor.</p>";
    }
    
    // Mostrar algunos registros de ejemplo
    echo "<h3>📊 Ejemplo de datos (primeros 3 productos):</h3>";
    $sql = "SELECT * FROM productos LIMIT 3";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $productos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($productos)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
        
        // Encabezados
        echo "<tr>";
        foreach (array_keys($productos[0]) as $columna) {
            echo "<th>$columna</th>";
        }
        echo "</tr>";
        
        // Datos
        foreach ($productos as $producto) {
            echo "<tr>";
            foreach ($producto as $valor) {
                echo "<td>" . htmlspecialchars($valor) . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ Error: " . $e->getMessage() . "</div>";
}

echo "<br><a href='test_optimizado_final.php' style='background: #007bff; color: white; padding: 10px; text-decoration: none;'>🔙 Volver al Test</a>";
?>

<style>
table { margin: 15px 0; }
th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
th { background-color: #f2f2f2; }
</style>
