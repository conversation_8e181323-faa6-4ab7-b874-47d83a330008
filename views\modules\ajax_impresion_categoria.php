<?php
// AJAX para generar contenido de impresión por categoría
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'error' => 'Método no permitido']);
    exit;
}

$input = file_get_contents('php://input');
$data = json_decode($input, true);

if (!$data || !isset($data['pedido_id']) || !isset($data['categoria'])) {
    echo json_encode(['success' => false, 'error' => 'Datos incompletos']);
    exit;
}

try {
    require_once '../../models/conexion.php';
    
    $pedido_id = intval($data['pedido_id']);
    $categoria = $data['categoria'];
    $numero_pedido = $data['numero_pedido'] ?? '';
    $mesa_numero = $data['mesa_numero'] ?? '';
    $mesero = $data['mesero'] ?? '';
    
    $conexion = new Conexion();
    $pdo = $conexion->conectar();
    
    // Obtener productos de la categoría específica
    $stmt = $pdo->prepare("
        SELECT
            p.nombre as producto_nombre,
            p.precio,
            pvm.cantidad, pvm.nota as nota,
            p.precio as precio_unitario,
            (pvm.cantidad * p.precio) as subtotal
        FROM producto_vendido_mesa pvm
        INNER JOIN productos p ON pvm.productos_id = p.id
        WHERE pvm.pedidos_id = :pedido_id
        AND CASE
            WHEN p.categoria IN ('bebidas', 'cervezas', 'licores', 'vinos', 'cocteles', 'refrescos', 'jugos', 'agua', 'bar') THEN 'bar'
            WHEN p.categoria IN ('carnes', 'parrilla', 'asados', 'pescados', 'mariscos') THEN 'asados'
            ELSE 'cocina'
        END = :categoria
        ORDER BY p.nombre
    ");
    
    $stmt->bindParam(":pedido_id", $pedido_id, PDO::PARAM_INT);
    $stmt->bindParam(":categoria", $categoria, PDO::PARAM_STR);
    $stmt->execute();
    $productos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($productos)) {
        echo json_encode(['success' => false, 'error' => 'No hay productos para esta categoría']);
        exit;
    }
    
    // Generar contenido del ticket
    $contenido = "";
    date_default_timezone_set('America/Bogota');//Zona Horaria Colombia
    
    // Encabezado
    //$contenido .= "<b>MESA: " . $mesa_numero ." -> ".$mesero."</b> \n";
    $contenido .= '<div style="text-align: left; white-space: pre-wrap;"><b>MESA: ' . $mesa_numero . ' -> ' . $mesero . '</b></div>' . "\n";
    $contenido .= "PEDIDO: " . $numero_pedido . "\n";
    $contenido .= "CATEGORIA: " . strtoupper($categoria) . "\n";
    //$contenido .= "FECHA: " . date('d/m/Y H:i') . "\n";
    $contenido .= "FECHA: " . date('d/m/Y h:i A') . "\n";
    $contenido .= "ESTADO: ENVIADO\n";
    $contenido .= "--------------------------------\n";
    
    // Productos
    $total_categoria = 0;
    foreach ($productos as $producto) {
        $cantidad = $producto['cantidad'];
        $nota = $producto['nota'];
        $nombre = substr($producto['producto_nombre'], 0, 25);
        $subtotal = $producto['subtotal'];
        $total_categoria += $subtotal;

        $product=$nombre."  ".$nota;
        
        $contenido .= sprintf("%-25s %2d\n", $product, $cantidad);
        $contenido .= sprintf("  $%s\n", number_format($subtotal, 0));
    }
    
    // Total de la categoría
    $contenido .= "--------------------------------\n";
    $contenido .= sprintf("TOTAL %s: $%s\n", strtoupper($categoria), number_format($total_categoria, 0));
    $contenido .= "--------------------------------\n";
    
    // Pie
    $contenido .= "Hora impresion: " . date('H:i:s') . "\n";
    
    // Registrar log de impresión
    try {
        $ip_cliente = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $stmt = $pdo->prepare("
            INSERT INTO logs_impresion_pedidos 
            (pedido_id, categoria, metodo, success, ip_cliente, fecha_hora) 
            VALUES 
            (:pedido_id, :categoria, 'ventana_impresion', 1, :ip_cliente, NOW())
        ");
        $stmt->bindParam(":pedido_id", $pedido_id, PDO::PARAM_INT);
        $stmt->bindParam(":categoria", $categoria, PDO::PARAM_STR);
        $stmt->bindParam(":ip_cliente", $ip_cliente, PDO::PARAM_STR);
        $stmt->execute();
    } catch (Exception $e) {
        // Log error but don't fail the request
        error_log("Error registrando log de impresión: " . $e->getMessage());
    }
    
    echo json_encode([
        'success' => true,
        'contenido' => $contenido,
        'categoria' => $categoria,
        'total_productos' => count($productos),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    error_log("Error en ajax_impresion_categoria: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => 'Error interno: ' . $e->getMessage()
    ]);
}
?>
