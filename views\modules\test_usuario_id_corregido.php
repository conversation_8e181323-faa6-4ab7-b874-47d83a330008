<?php

session_start();

if(!isset($_SESSION["validar"]) || !$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "models/conexion.php";

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Test: Error usuario_id Corregido</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
</head>
<body>

<div class="container">
    <h2>✅ Test: Error usuario_id Corregido</h2>
    <hr>
    
    <div class="alert alert-success">
        <h4>🎉 ¡Error de usuario_id solucionado!</h4>
        <p>Se ha corregido el problema que causaba el error "Column 'usuario_id' cannot be null" durante la facturación.</p>
    </div>
    
    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">🔧 Corrección Implementada</h3>
        </div>
        <div class="panel-body">
            <h5>❌ Problema anterior:</h5>
            <div class="well">
                <code style="color: red;">
                INSERT INTO pedidos (mesero_id, facturado, cedula_cliente)<br>
                VALUES ($mesero, 'n', '$clienteCedula')
                </code>
                <p><small>❌ No incluía usuario_id, causando error NULL</small></p>
            </div>
            
            <h5>✅ Solución implementada:</h5>
            <div class="well">
                <code style="color: green;">
                // Obtener usuario_id de la sesión o usar mesero como fallback<br>
                $usuario_id = isset($_SESSION['usuario']) ? $_SESSION['usuario'] : $mesero;<br><br>
                INSERT INTO pedidos (mesero_id, usuario_id, facturado, cedula_cliente)<br>
                VALUES ($mesero, $usuario_id, 'n', '$clienteCedula')
                </code>
                <p><small>✅ Incluye usuario_id, evitando error NULL</small></p>
            </div>
        </div>
    </div>
    
    <div class="panel panel-primary">
        <div class="panel-heading">
            <h3 class="panel-title">👤 Estado Actual de la Sesión</h3>
        </div>
        <div class="panel-body">
            <?php
            echo "<h5>📋 Variables de sesión:</h5>";
            echo "<table class='table table-striped'>";
            echo "<thead>";
            echo "<tr>";
            echo "<th>Variable</th>";
            echo "<th>Valor</th>";
            echo "<th>Estado</th>";
            echo "</tr>";
            echo "</thead>";
            echo "<tbody>";
            
            $session_vars = [
                'usuario' => 'ID del usuario (para usuario_id)',
                'validar' => 'Estado de validación',
                'mesa' => 'Mesa actual',
                'clientep' => 'Cliente actual',
                'nombre' => 'Nombre del usuario',
                'apellidos' => 'Apellidos del usuario'
            ];
            
            foreach ($session_vars as $var => $descripcion) {
                $valor = isset($_SESSION[$var]) ? $_SESSION[$var] : 'No definida';
                $estado = isset($_SESSION[$var]) ? 'success' : 'warning';
                $icono = isset($_SESSION[$var]) ? '✅' : '⚠️';
                
                echo "<tr class='{$estado}'>";
                echo "<td><code>\$_SESSION['{$var}']</code><br><small>{$descripcion}</small></td>";
                echo "<td><strong>{$valor}</strong></td>";
                echo "<td>{$icono}</td>";
                echo "</tr>";
            }
            echo "</tbody></table>";
            
            // Mostrar qué valor se usaría para usuario_id
            $usuario_id_a_usar = isset($_SESSION['usuario']) ? $_SESSION['usuario'] : 'mesero (fallback)';
            echo "<div class='alert alert-info'>";
            echo "<h6>💡 Valor que se usará para usuario_id:</h6>";
            echo "<p><strong>{$usuario_id_a_usar}</strong></p>";
            echo "</div>";
            ?>
        </div>
    </div>
    
    <div class="panel panel-warning">
        <div class="panel-heading">
            <h3 class="panel-title">🔍 Verificación de la Tabla pedidos</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                echo "<h5>📊 Estructura del campo usuario_id:</h5>";
                $stmt = Conexion::conectar()->prepare("
                    SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT 
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_NAME = 'pedidos' 
                    AND COLUMN_NAME IN ('usuario_id', 'mesero_id')
                ");
                $stmt->execute();
                $campos = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (count($campos) > 0) {
                    echo "<table class='table table-condensed'>";
                    echo "<thead>";
                    echo "<tr>";
                    echo "<th>Campo</th>";
                    echo "<th>Tipo</th>";
                    echo "<th>Permite NULL</th>";
                    echo "<th>Valor por Defecto</th>";
                    echo "</tr>";
                    echo "</thead>";
                    echo "<tbody>";
                    
                    foreach ($campos as $campo) {
                        $row_class = $campo['IS_NULLABLE'] == 'NO' ? 'warning' : 'success';
                        echo "<tr class='{$row_class}'>";
                        echo "<td><strong>{$campo['COLUMN_NAME']}</strong></td>";
                        echo "<td>{$campo['DATA_TYPE']}</td>";
                        echo "<td>{$campo['IS_NULLABLE']}</td>";
                        echo "<td>" . ($campo['COLUMN_DEFAULT'] ?: 'Ninguno') . "</td>";
                        echo "</tr>";
                    }
                    
                    echo "</tbody></table>";
                } else {
                    echo "<div class='alert alert-warning'>No se encontraron los campos usuario_id o mesero_id</div>";
                }
                
                // Verificar últimos pedidos
                echo "<h5>📋 Últimos pedidos creados:</h5>";
                $stmt_pedidos = Conexion::conectar()->prepare("
                    SELECT id, numero_pedido, mesero_id, usuario_id, facturado, fecha_pedido 
                    FROM pedidos 
                    ORDER BY fecha_pedido DESC 
                    LIMIT 5
                ");
                $stmt_pedidos->execute();
                $ultimos_pedidos = $stmt_pedidos->fetchAll(PDO::FETCH_ASSOC);
                
                if (count($ultimos_pedidos) > 0) {
                    echo "<table class='table table-condensed'>";
                    echo "<thead>";
                    echo "<tr>";
                    echo "<th>ID</th>";
                    echo "<th>Número</th>";
                    echo "<th>Mesero ID</th>";
                    echo "<th>Usuario ID</th>";
                    echo "<th>Facturado</th>";
                    echo "<th>Fecha</th>";
                    echo "</tr>";
                    echo "</thead>";
                    echo "<tbody>";
                    
                    foreach ($ultimos_pedidos as $pedido) {
                        $usuario_class = $pedido['usuario_id'] ? 'success' : 'danger';
                        echo "<tr>";
                        echo "<td>{$pedido['id']}</td>";
                        echo "<td>{$pedido['numero_pedido']}</td>";
                        echo "<td>{$pedido['mesero_id']}</td>";
                        echo "<td class='{$usuario_class}'>";
                        echo $pedido['usuario_id'] ? $pedido['usuario_id'] : '<span class="text-danger">NULL</span>';
                        echo "</td>";
                        echo "<td>{$pedido['facturado']}</td>";
                        echo "<td><small>{$pedido['fecha_pedido']}</small></td>";
                        echo "</tr>";
                    }
                    
                    echo "</tbody></table>";
                } else {
                    echo "<div class='alert alert-info'>No hay pedidos en la tabla</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-success">
        <div class="panel-heading">
            <h3 class="panel-title">🧪 Prueba de Facturación</h3>
        </div>
        <div class="panel-body">
            <h5>✅ Ahora puedes probar la facturación sin errores:</h5>
            
            <div class="row">
                <div class="col-md-6">
                    <h6>🎯 Pasos para probar:</h6>
                    <ol>
                        <li>Ir a una mesa con productos</li>
                        <li>Intentar facturar</li>
                        <li>Verificar que no aparece el error de usuario_id</li>
                        <li>Confirmar que la facturación se completa</li>
                    </ol>
                </div>
                <div class="col-md-6">
                    <h6>✅ Errores solucionados:</h6>
                    <ul>
                        <li>✅ Error de clave duplicada</li>
                        <li>✅ Error de usuario_id NULL</li>
                        <li>✅ Pedidos no se marcaban como facturados</li>
                        <li>✅ Mesa no se limpiaba</li>
                    </ul>
                </div>
            </div>
            
            <div class="alert alert-success">
                <h6>🎉 ¡Sistema de facturación completamente funcional!</h6>
                <p>Todos los errores conocidos han sido solucionados.</p>
            </div>
        </div>
    </div>
    
    <hr>
    <div class="row">
        <div class="col-md-3">
            <a href="index.php?action=debug_error_usuario_id" class="btn btn-primary btn-block">🔙 Debug Usuario ID</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=mesa" class="btn btn-info btn-block">🪑 Ver Mesas</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=registroPmesa&ida=5" class="btn btn-warning btn-block">🧪 Test Mesa 5</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=registrarDetalleFactura&ida=5" class="btn btn-success btn-block">💰 Facturar Mesa 5</a>
        </div>
    </div>
</div>

</body>
</html>
