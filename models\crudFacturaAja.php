<?php
#EXTENSIÓN DE CLASES: Los objetos pueden ser extendidos, y pueden heredar propiedades y métodos. Para definir una clase como extensión, debo definir una clase padre, y se utiliza dentro de una clase hija.
require_once "conexion.php";
require_once "crudPedidoMesaVendido.php";
require_once "crudTurno.php";
require_once "crudVistaVentas.php";
class DatosFacturaAja extends Conexion
{
	# Pre Factura Ajax
	#------------------------------------
	 public static function preFacturaajaxModel($mesa, $datosModel)
		{
		 $Total=0;
		 $cantidaPoducto=0;
		 $totalProducto=0;
		 $i=0;
		 $cliente= DatosFacturaAja::buscarCedula($datosModel["clienteCedula"]);
		 // CORREGIDO: Obtener productos de pedidos enviados/entregados para facturar
		 $respuesta = DatosFacturaAja::obtenerProductosParaFacturar($mesa);
		 //echo "<script>alert('este es ID DEL CLIENTE ".$cliente["id"]."');</script>";
		 $j=0;
		 foreach($respuesta as $row => $item)
			{	//echo "<script>alert('contador ".$i." ');</script>";
		 	 $desc=	$item["preciopr"]*($item["descuentopvm"]/100)	;
			 $totalProducto=($item["preciopr"] - $desc)*$item["cantidadpvm"];
			 //$Total=$Total+$totalProducto;
			 $cantidaPoducto=$cantidaPoducto+$item["cantidadpvm"];
			 $lista[$i]= array('codigo' =>$item["idpr"],
						  'nombre' =>$item["nombrepr"],
						  'precio' =>$item["preciopr"],
						  'cantidad' =>$item["cantidadpvm"],
						  'idmesa' =>$item["idmesa"],
						  'mesero' =>$item["mesero"],
						  'descuento' =>$item["descuentopvm"],
						  'codigoD' =>$item["pvmcodigo_descuento"],
						  'total' => $totalProducto );
			 $Total=$Total+$totalProducto;
			 //$cantidaPoducto=$cantidaPoducto+$item["cantidadpvm"];
			 //echo "<script>alert('Matriz codigo-.Id ".$lista[$i]["codigo"]." nombre ".$lista[$i]["nombre"]."  precio".$lista[$i]["precio"]."--cantidad=".$lista[$i]["cantidad"]." valor i= ".$i."');</script>";
			 $i++;
			 /////////////////// cargas la lista de todo los Suministro///////////////////
			 $consultaSuministro  = Datos::productoListaSuministroModel($item["idpr"]);
			 //echo "<script>alert('consulta 2 id del producto".$item["idpr"]."');</script>";
			 //$suministroProducto=array();
			 foreach($consultaSuministro as $rows => $ite)
				{//echo "<script>alert('foreach 2 id suministro=".$ite["sid"]."--spcantidad=".$ite["spcantidad"]."--cantidadpmv=".$item["cantidadpvm"]." ');</script>";
				 //echo "<script>alert('consulta suministro".$item["idpr"]."');</script>";
				 $cantidadTotalS = $ite["spcantidad"] * $item["cantidadpvm"];//cantidad total para descontar los suministro spcantidad
				 if ($j == 0)
					{	//echo "<script>alert('entro a suministro J=".$j."');</script>";
					 //  agrega a lista nuevo suministro  //
					 $listaActualizar[$j]["sid"] = $ite["sid"];
					 $listaActualizar[$j]["cantidadTotalS"] = $ite["sucantidad"] - $cantidadTotalS;
					 $j++;
					}
				 else
					{	$limite = $j;
					 for ($c=0; $c < $limite  ; $c++)
						{
						 if ($ite["sid"] == $listaActualizar[$c]["sid"] )
							{
							 $listaActualizar[$c]["cantidadTotalS"]= $listaActualizar[$c]["cantidadTotalS"] - $cantidadTotalS;
							}
						 else
							{	///  agrega a lista nuevo suministro   //
								$listaActualizar[$j]["sid"] = $ite["sid"];
								$listaActualizar[$j]["cantidadTotalS"] = $ite["sucantidad"]- $cantidadTotalS;
								$j++;	/*	*/
							}
						}
					}
				}
			/////////////////// cargas la lista ///////////////////
			}
		 $_SESSION['productos']=$lista;
		 $turnos = DatosTurno::editarTurnoModel($datosModel["usuario"],"turnos_cajeros");
		 if ($turnos["id"]==0)
			{	echo'<script>alert("Inicie Seccion");	location.href ="index";</script>';	 }
		 $stmt = Conexion::conectar();
		 date_default_timezone_set("America/Bogota");
		 $fecha_factura=strftime("%Y-%m-%d %H:%M:%S");
		 //echo "<script>alert('Entro crud Usuario ".$fecha_factura."  Jfinal=".$turnos["id"]."');</script>";
			 $c=0;
			 $turno = $turnos["id"];
			 $iva = 8;
			 $Totalf =$datosModel['propina']+ $Total;

			 //////////Variables de la factura
			 $_SESSION["turno_cajero_id"] = $turno;
			 $_SESSION["subtotal"] =$Total ;
			 $_SESSION["total"] = $Totalf;
			 $_SESSION['propina'] = $datosModel['propina'];
				///  Actualizar cantidades de Suministro //
				//echo "<script>alert('atualizar sucursal ".$cantidad= $listaActualizar[$j]['cantidadTotalS']." valor ".$j." ' );</script>";
			 return "success";
			 $stmt->close();

		}
	#------------------------------------
	#Factura Ajax  idpr nombrepr preciopr cantidadpvm fechapvm
	#------------------------------------
	 public static function facturaajaxModel($mesa, $datosModel)
		{
		 try {
			 // OPTIMIZACIÓN PARA MESAS PESADAS
			 $tiempoInicio = microtime(true);
			 error_log("facturaajaxModel INICIADO - Mesa: $mesa");
			 error_log("facturaajaxModel - Datos recibidos: " . print_r($datosModel, true));

			 $Total=0;
			 $cantidaPoducto=0;
			 $totalProducto=0;
			 $i=0;

			 // OPTIMIZACIÓN: Cache del cliente
			 error_log("facturaajaxModel - Buscando cliente: " . $datosModel["clienteCedula"]);
			 $cliente= DatosFacturaAja::buscarCedula($datosModel["clienteCedula"]);
			 error_log("facturaajaxModel - Cliente encontrado: " . print_r($cliente, true));

			 // OPTIMIZACIÓN: Obtener productos con límite de memoria
			 error_log("facturaajaxModel - Obteniendo productos para facturar mesa: $mesa");
			 $respuesta = DatosFacturaAja::obtenerProductosParaFacturar($mesa);
			 $totalProductos = count($respuesta);
			 error_log("facturaajaxModel - Mesa: $mesa, Productos a procesar: $totalProductos");

			 if ($totalProductos == 0) {
			 	error_log("facturaajaxModel - ERROR: No hay productos para facturar en mesa $mesa");
			 	return "Error: No hay productos para facturar";
			 }

			 $j=0;
		 error_log("facturaajaxModel - Iniciando procesamiento de productos");
		 foreach($respuesta as $row => $item)
			{
			 error_log("facturaajaxModel - Procesando producto $i: " . $item["nombrepr"] . " (ID: " . $item["idpr"] . ")");

		 	 $desc=	$item["preciopr"]*($item["descuentopvm"]/100)	;
			 $totalProducto=($item["preciopr"] - $desc)*$item["cantidadpvm"];
			 //$Total=$Total+$totalProducto;
			 $cantidaPoducto=$cantidaPoducto+$item["cantidadpvm"];
			 $lista[$i]= array('codigo' =>$item["idpr"],
						  'nombre' =>$item["nombrepr"],
						  'precio' =>$item["preciopr"],
						  'cantidad' =>$item["cantidadpvm"],
						  'idmesa' =>$item["idmesa"],
						  'mesero' =>$item["mesero"],
						  'descuento' =>$item["descuentopvm"],
						  'codigoD' =>$item["pvmcodigo_descuento"],
						  'total' => $totalProducto );
			 $Total=$Total+$totalProducto;

			 $i++;
			 /////////////////// cargas la lista de todo los Suministro///////////////////
			 $consultaSuministro  = Datos::productoListaSuministroModel($item["idpr"]);
			 //echo "<script>alert('consulta 2 id del producto".$item["idpr"]."');</script>";
			 //$suministroProducto=array();
			 foreach($consultaSuministro as $rows => $ite)
				{//echo "<script>alert('foreach 2 id suministro=".$ite["sid"]."--spcantidad=".$ite["spcantidad"]."--cantidadpmv=".$item["cantidadpvm"]." ');</script>";
				 //echo "<script>alert('consulta suministro".$item["idpr"]."');</script>";
				 $cantidadTotalS = $ite["spcantidad"] * $item["cantidadpvm"];//cantidad total para descontar los suministro spcantidad
				 if ($j == 0)
					{	//echo "<script>alert('entro a suministro J=".$j."');</script>";
					 //  agrega a lista nuevo suministro  //
					 $listaActualizar[$j]["sid"] = $ite["sid"];
					 $listaActualizar[$j]["cantidadTotalS"] = $ite["sucantidad"] - $cantidadTotalS;
					 $j++;
					}
				 else
					{	$limite = $j;
					 for ($c=0; $c < $limite  ; $c++)
						{
						 if ($ite["sid"] == $listaActualizar[$c]["sid"] )
							{
							 $listaActualizar[$c]["cantidadTotalS"]= $listaActualizar[$c]["cantidadTotalS"] - $cantidadTotalS;
							}
						 else
							{	///  agrega a lista nuevo suministro   //
								$listaActualizar[$j]["sid"] = $ite["sid"];
								$listaActualizar[$j]["cantidadTotalS"] = $ite["sucantidad"]- $cantidadTotalS;
								$j++;	/*	*/
							}
						}
					}
				}
			/////////////////// cargas la lista ///////////////////
			}
		 $_SESSION['productos']=$lista;
		 $turnos = DatosTurno::editarTurnoModel($datosModel["usuario"],"turnos_cajeros");
		 if ($turnos["id"]==0)
			{	echo'<script>alert("Inicie Seccion");	location.href ="index";</script>';	 }
		 error_log("facturaajaxModel - Conectando a base de datos");
		 $stmt = Conexion::conectar();
		 date_default_timezone_set("America/Bogota");
		 $fecha_factura=strftime("%Y-%m-%d %H:%M:%S");
		 error_log("facturaajaxModel - Fecha factura: $fecha_factura");

		 try
			{
			 error_log("facturaajaxModel - Iniciando transacción de base de datos");
			 $stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
			 // OPTIMIZACIÓN: Transacción con configuración para mesas pesadas
			 $stmt->beginTransaction();

			 // Configurar para mesas pesadas
			 error_log("facturaajaxModel - Configurando timeouts para mesa pesada");
			 $stmt->exec("SET SESSION innodb_lock_wait_timeout = 300");
			 $stmt->exec("SET SESSION lock_wait_timeout = 300");

			 $mesero=$lista[0]["mesero"];
			 error_log("facturaajaxModel - Mesero obtenido: $mesero");

			 // VALIDACIÓN: Verificar que mesero no esté vacío
			 if (empty($mesero) || !is_numeric($mesero)) {
			 	 error_log("facturaajaxModel: ERROR - mesero vacío o inválido. Valor: " . var_export($mesero, true));
			 	 error_log("facturaajaxModel: Lista[0]: " . var_export($lista[0], true));

			 	 // Usar usuario de sesión como fallback
			 	 $mesero = isset($_SESSION['usuario']) ? $_SESSION['usuario'] : 1;
			 	 error_log("facturaajaxModel: Usando mesero fallback: {$mesero}");
			 }

			 error_log("facturaajaxModel - Mesa: $mesa, Procesando $totalProductos productos");

			 //echo "<script>alert('el mesero id=".$mesero." ' );</script>";
			 //// Inserta el pedido//
			 error_log("facturaajaxModel - Insertando nuevo pedido");
			 $consulta="INSERT INTO pedidos (mesero_id, facturado, cedula_cliente) VALUES (".$mesero.", 'n', '".$datosModel["clienteCedula"]."')";
			 error_log("facturaajaxModel: Consulta INSERT pedidos: " . $consulta);
			 //echo " <br>--".$consulta." <br>--";
			 $stmt->exec($consulta);
			 $ultimo_id=$stmt->lastInsertId();	//ultimo pedido
			 error_log("facturaajaxModel - Pedido insertado con ID: $ultimo_id");
			 //echo "<script>alert('ultimo pedido ".$ultimo_id." ' );</script>";
			 $c=0;
			 foreach ($respuesta as $row => $item)
				{	//echo "<script>alert('foreach SESSION recorrido ' );</script>";
				 $costosp=VistaVentas::CostoProductoModel($item['idpr']);
				 $costosproducto=round($costosp["Valor_Compra_Producto"],1);
				 // SOLUCIÓN PERMANENTE: Usar INSERT ... ON DUPLICATE KEY UPDATE para evitar errores de clave duplicada
				 $consulta1 = "INSERT INTO pedido_productos_mesa(productos_id, mesas_id, pedidos_id, cantidad, precio, valor_productos, descuento, codigo_descuento)
				              VALUES (".$item['idpr'].",".$item['idmesa'].",".$ultimo_id.",".$item['cantidadpvm'].",".$item['preciopr'].",".$costosproducto.", ".$item['descuentopvm'].", '".$item['pvmcodigo_descuento']."')
				              ON DUPLICATE KEY UPDATE
				              cantidad = VALUES(cantidad),
				              precio = VALUES(precio),
				              valor_productos = VALUES(valor_productos),
				              descuento = VALUES(descuento),
				              codigo_descuento = VALUES(codigo_descuento)";
					//echo " <br>--".$consulta1." <br>--";
					//echo "<script>alert('insertar producto detalle factura ".$item['descuentopvm']." ' );</script>";
				 $c++;
				 $stmt->exec($consulta1);	//.$datosModel["efectivo"].
				}
			 $turno = $turnos["id"];
			 $iva = 8;
			 $Totalf =$datosModel['propina']+ $Total;
			 if($datosModel['pago'] == 1)
				{
					$efectivo = $datosModel['efectivo'];
					$nequi = $datosModel['nequi'];
					$daviplata = $datosModel['daviplata'];
					$bancolombia = $datosModel['bancolombia'];//cambiamos a tipo
					$tarjeta = $datosModel['tarjeta'];//valor pago en tarjeta
					$cambio = $tarjeta + $datosModel['efectivo']+$bancolombia+$nequi+$daviplata - $Total-$datosModel['propina'];
					$propina = $datosModel['propina'];
					//  Insertando en la tabla venta o la factura/		tipotarjeta`, `valortarjeta
					$consulta2 = "INSERT INTO ventas(pedidos_id, turno_cajero_id, valor, fecha_venta, subtotal, iva, total, efectivo, bancolombia,nequi, daviplata, valortarjeta, cambio, propina, mesa) VALUES ($ultimo_id, $turno, $Total, '$fecha_factura', $Total, $iva, $Totalf, $efectivo, $bancolombia, $nequi, $daviplata, $tarjeta, $cambio, $propina, '$mesa')";
					//echo " <br>--".$consulta2." <br>--";
					$stmt->exec($consulta2);
					$idFactura = $stmt->lastInsertId();	//ultimo id de factura
					//////////Variables de la factura
					$_SESSION["efectivo"] = $efectivo;
					$_SESSION["tarjeta"] = $tarjeta;
					$_SESSION["bancolombia"] = $bancolombia;
					$_SESSION["nequi"] = $nequi;
					$_SESSION["daviplata"] = $daviplata;
					$_SESSION["cambio"] = $cambio;
				}
			 else
				{// echo "<script>alert('entro a credito CRUD  -ultimo_id:".$ultimo_id."   -turno:".$turno."    -Total:".$Total." -fecha_factura:".$fecha_factura."  -iva:".$iva."     -Total:".$Totalf."  ' );</script>";
					//  Insertando en la tabla venta o la factura/
				 $consulta2 = "INSERT INTO creditos(pedidos_id, turno_cajero_id, valor, fecha_credito, subtotal, iva, total, propina, mesa) VALUES (".$ultimo_id.", $turno, ".$Total.", '$fecha_factura', ".$Total.", ".$iva.", ".$Totalf.", ".$datosModel['propina'].", '$mesa')";
					//echo "<script>alert('insertar tabla venta o factura ".$consulta2." ' );</script>";
				 $stmt->exec($consulta2);
				 $idDeuda = $stmt->lastInsertId();	//ultimo id de factura
				 //echo "<script>alert('ultimo id credito ".$idDeuda." ' );</script>";
				 $consultaD = "INSERT INTO deudas(credito_id, cliente_id, cuenta) VALUES (".$idDeuda.", ".$datosModel["clienteCedula"].", ".$Totalf.")";
				 //echo " <br>--".$consultaD." <br>--";
				 $stmt->exec($consultaD);
				}
			 //////////Variables de la factura
			 $_SESSION["pedidos_id"] = $ultimo_id;
			 $_SESSION["turno_cajero_id"] = $turno;
			 $_SESSION["subtotal"] =$Total ;
			 $_SESSION["total"] = $Totalf;
			 $_SESSION['idFactura'] = $idFactura;
			 $_SESSION['propina'] = $datosModel['propina'];
			 $_SESSION['mesaFactura'] = $mesa;
				///  Actualizar cantidades de Suministro //
				//echo "<script>alert('atualizar sucursal ".$cantidad= $listaActualizar[$j]['cantidadTotalS']." valor ".$j." ' );</script>";
			 for ($c=0; $c <$j ; $c++)
				{ //echo "<script>alert('ultimo for J=".$j." ' );</script>";
					$idSuministro = $listaActualizar[$c]['sid'];
					$cantidad= $listaActualizar[$c]['cantidadTotalS'];
					$consulta3="UPDATE sucursal SET  cantidad=".$cantidad."  WHERE suministro_id = ".$idSuministro;
					//echo "<script>alert('Consulta3 ".$cantidad." ' );</script>";
					$stmt->exec($consulta3);
					//echo "<script>alert('Consulta ".$c." ' );</script>";
				}
			 // REVERTIDO: Usar lógica original de limpieza que funciona
			 // NUEVO: Actualizar estado de pedidos a 'facturado' antes de limpiar
			 $consulta_pedidos = "UPDATE pedidos SET estado = 'facturado', fecha_entrega = NOW() WHERE mesa_id = " . $mesa . " AND estado IN ('borrador', 'enviado', 'entregado')";
			 $stmt->exec($consulta_pedidos);
			 error_log("facturaajaxModel: Pedidos marcados como facturados para mesa {$mesa}");

			 //echo "<script>alert('mesa id ".$mesa." ')</script>";
			 $consulta4 = "DELETE FROM producto_vendido_mesa where mesas_id =" .$mesa;
			 $stmt->exec($consulta4);
			 //echo "<script>alert('Consulta4 ".$consulta4." ' );</script>";
			 $consultaC = "DELETE FROM cocina where mesa_id =" .$mesa;
			 $stmt->exec($consultaC);
			 //echo "<script>alert('Consulta4 ".$consulta4." ' );</script>";
			 $consulta5 = " UPDATE mesas SET descripcion='', estado = '' WHERE id = ".$mesa;
			 $stmt->exec($consulta5);
			 //echo "<script>alert('Consulta5 ".$consulta5." ' );</script>";
			 //echo "<script>alert('Consulta5 ".$consulta5." ' );</script>";
			 $stmt->commit();

			 // OPTIMIZACIÓN: Log de tiempo total
			 $tiempoFin = microtime(true);
			 $tiempoTotal = round(($tiempoFin - $tiempoInicio) * 1000, 2);
			 error_log("facturaajaxModel COMPLETADO - Mesa: $mesa, Tiempo total: {$tiempoTotal}ms, Productos: $totalProductos");

			 //echo "<script>alert('fin de cruD try que pasa');</script>";
			 return "success";
			 $stmt->close();
			}
		 catch (PDOException $e)
			{	echo "<script>alert('catch entro')</script>";
				$stmt->rollBack();
				error_log("facturaajaxModel - ERROR PDO: " . $e->getMessage());
				error_log("facturaajaxModel - Stack trace: " . $e->getTraceAsString());
				print "Error!: ".$e->getMessage()."</br>";
				return "Error!: ".$e->getMessage()."</br>";
			} /*return "success";	*/

		 } catch (Exception $e) {
		 	error_log("facturaajaxModel - ERROR GENERAL: " . $e->getMessage());
		 	error_log("facturaajaxModel - Stack trace: " . $e->getTraceAsString());
		 	return "Error general: " . $e->getMessage();
		 } catch (Error $e) {
		 	error_log("facturaajaxModel - ERROR FATAL: " . $e->getMessage());
		 	error_log("facturaajaxModel - Stack trace: " . $e->getTraceAsString());
		 	return "Error fatal: " . $e->getMessage();
		 }

		}
	#------------------------------------
	#OBTENER PRODUCTOS PARA FACTURAR (ENVIADOS/ENTREGADOS)
	#------------------------------------------
	 public static function obtenerProductosParaFacturar($mesa)
	 {
	 	try {
	 		$stmt = Conexion::conectar();

	 		// Obtener productos de pedidos enviados/entregados para facturar
	 		$consulta = "SELECT pr.id AS idpr, pr.nombre AS nombrepr, pr.precio AS preciopr, pr.codigo AS prcodigo,
	 					pvm.cantidad AS cantidadpvm, pvm.fecha_hora AS fechapvm, pvm.mesas_id AS idmesa,
	 					pvm.mesero AS mesero, pvm.descuento AS descuentopvm, pvm.codigo_descuento AS pvmcodigo_descuento,
	 					p.numero_pedido, p.estado
	 					FROM productos pr
	 					JOIN producto_vendido_mesa pvm ON pr.id = pvm.productos_id
	 					JOIN pedidos p ON pvm.pedidos_id = p.id
	 					WHERE pvm.mesas_id = :idmesa AND p.estado IN ('enviado', 'entregado')
	 					ORDER BY pvm.fecha_hora DESC";

	 		$stmt2 = $stmt->prepare($consulta);
	 		$stmt2->bindParam(":idmesa", $mesa, PDO::PARAM_INT);
	 		$stmt2->execute();

	 		$resultado = $stmt2->fetchAll(PDO::FETCH_ASSOC);

	 		// CORREGIR: Calcular valor_productos para cada producto
	 		foreach ($resultado as &$producto) {
	 			$descuento = ($producto["preciopr"] * ($producto["descuentopvm"] / 100));
	 			$precioConDescuento = $producto["preciopr"] - $descuento;
	 			$producto["valor_productos"] = $precioConDescuento * $producto["cantidadpvm"];
	 		}

	 		error_log("obtenerProductosParaFacturar: Mesa $mesa, productos encontrados: " . count($resultado));

	 		return $resultado;

	 	} catch (Exception $e) {
	 		error_log("obtenerProductosParaFacturar ERROR: " . $e->getMessage());
	 		return array();
	 	}
	 }

	#------------------------------------
	#BUSCAR DEUDA
	#------------------------------------------
	 public static function buscarDeuda ($cedula)
		{
			//echo "<script>alert('Entro CRUD mesa ".$idmesa." ')</script>";
			$consulta = "SELECT p.id AS pid, p.cedula AS pcedula, p.nombre AS pnombre, p.apellidos AS papellidos, d.cliente_id AS dclienteid, d.cuenta AS dcuenta, d.abonos_id AS dabonos, d.credito_id AS dcreditoid  FROM personas p, deudas d WHERE d.cliente_id=p.id AND p.cedula= :cedula";
			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->bindParam(":cedula", $cedula, PDO::PARAM_STR);
			$stmt->execute();
			$r = $stmt->fetch();
			$c=$stmt->rowCount();
			//echo '<br> C2='.$c2.'<br>';
			if ($c>0)
				{	return $r;	}
			else
				{	return 0;	}
			$stmt->close();
		}
	#------------------------------------
	#BUSCAR nombre producto autocompletar
	#------------------------------------------
	 public static function buscarNombrepModel($nombrep)
		{   $nombre='%'.$nombrep.'%';
			//echo "<script>alert('Entro CRUD autocompletar')</script>";
			$consulta = "SELECT p.codigo as codigo, p.nombre as nombre FROM productos p where  p.nombre like (:nombrep)  LIMIT 0,50";
			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->bindParam(":nombrep", $nombre, PDO::PARAM_STR);
			$stmt->execute();
			$r = $stmt->fetchAll();
			$c=$stmt->rowCount();
			//echo '<br> C2='.$c2.'<br>';
			if ($c>0)
				{	return $r;	}
			else
				{	return 0;	}
			$stmt->close();
		}
	#------------------------------------------
	#BUSCAR CEDULA CLIENTE
	#------------------------------------------
	 public static function buscarCedula ($datosModel)
		{	//echo "<script>alert('Entro CRUD cedula ".$datosModel." ')</script>";
			$consulta = "SELECT id, ciudad_id, roles_id, cedula, nombre, apellidos  FROM personas WHERE cedula=:cedula ";
			$stmt = Conexion::conectar()->prepare($consulta);
			//$stmt->bindParam(":cedula", $datosModel, PDO::PARAM_STR);
			$stmt->bindParam(":cedula", $datosModel, PDO::PARAM_STR);
			$stmt->execute();
			$r = $stmt->fetch();
			//echo $datosModel. " -- ".$consulta;
			$c=$stmt->rowCount();
			//echo "<script>alert('SEGUNDO CRUD cedula ".$datosModel." ')</script>";
			//echo '<br> C2='.$c2.'<br>';
		//	echo "<script>alert(' CRUD cedula Matrys ".$r["cedula"]." ')</script>";
			if ($c>0)
				{	//echo "<script>alert(' CRUD cedula if  R ".$r["nombre"]." ')</script>";
					return $r;
				}
			else
				{	//echo "<script>alert(' CRUD cedula else ".$r." ')</script>";
					return 0;
				}
			$stmt->close();
		}
	#------------------------------------------
}