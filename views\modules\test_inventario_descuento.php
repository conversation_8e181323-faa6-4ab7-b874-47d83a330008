<!DOCTYPE html>
<html>
<head>
    <title>📦 Test Descuento de Inventario</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1000px; margin: 0 auto; }
        .card { background: #f8f9fa; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #007bff; }
        .success { border-left-color: #28a745; background: #d4edda; }
        .warning { border-left-color: #ffc107; background: #fff3cd; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .btn { padding: 15px 30px; margin: 10px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold; font-size: 16px; cursor: pointer; border: none; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        h1 { color: #007bff; text-align: center; }
        h2 { color: #495057; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .debug-info { background: #e9ecef; padding: 10px; border-radius: 3px; font-family: monospace; margin: 10px 0; }
        .inventory-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        .inventory-table th, .inventory-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .inventory-table th { background-color: #f2f2f2; }
    </style>
</head>
<body>

<div class="container">
    <h1>📦 Descuento de Inventario - Funcionalidad Agregada</h1>
    <p style="text-align: center; font-size: 18px; color: #6c757d;">
        Verificación del descuento automático de inventario al facturar
    </p>

    <div class="card success">
        <h2>✅ Funcionalidad Agregada</h2>
        <p>He agregado el descuento automático de inventario al proceso de facturación:</p>
        
        <ul>
            <li>✅ <strong>Incluido modelo crud.php:</strong> Para acceder a `productoListaSuministroModel()`</li>
            <li>✅ <strong>Descuento por producto:</strong> Obtiene suministros de cada producto facturado</li>
            <li>✅ <strong>Cálculo de cantidades:</strong> `cantidadTotalS = spcantidad * cantidadpvm`</li>
            <li>✅ <strong>Consolidación de suministros:</strong> Suma descuentos de mismo suministro</li>
            <li>✅ <strong>Actualización de inventario:</strong> `UPDATE sucursal SET cantidad = ?`</li>
            <li>✅ <strong>Protección contra negativos:</strong> `max(0, cantidad)` evita inventario negativo</li>
            <li>✅ <strong>Logs detallados:</strong> Para monitorear el proceso</li>
        </ul>
    </div>

    <div class="card">
        <h2>🔧 Cómo Funciona</h2>
        
        <h4>1. Obtención de Suministros:</h4>
        <div class="debug-info">
// Para cada producto facturado:
$consultaSuministro = Datos::productoListaSuministroModel($producto["idpr"]);

// Retorna: sid, spcantidad, sucantidad
// sid = ID del suministro
// spcantidad = cantidad por producto
// sucantidad = cantidad actual en inventario
        </div>
        
        <h4>2. Cálculo de Descuento:</h4>
        <div class="debug-info">
// Cantidad a descontar = cantidad por producto × cantidad vendida
$cantidadTotalS = $suministro["spcantidad"] * $producto["cantidadpvm"];

// Ejemplo: Si un producto usa 2 unidades de harina y se vendieron 3 productos:
// cantidadTotalS = 2 × 3 = 6 unidades de harina a descontar
        </div>
        
        <h4>3. Consolidación:</h4>
        <div class="debug-info">
// Si varios productos usan el mismo suministro, se suman los descuentos:
if ($suministro["sid"] == $listaActualizar[$c]["sid"]) {
    $listaActualizar[$c]["cantidadTotalS"] -= $cantidadTotalS;
}

// Ejemplo: Producto A usa 2 harinas, Producto B usa 3 harinas
// Total a descontar = 2 + 3 = 5 harinas
        </div>
        
        <h4>4. Actualización de Inventario:</h4>
        <div class="debug-info">
// Actualizar tabla sucursal con nueva cantidad
$cantidad = max(0, $listaActualizar[$c]['cantidadTotalS']);
UPDATE sucursal SET cantidad = ? WHERE suministro_id = ?

// max(0, cantidad) evita inventarios negativos
        </div>
    </div>

    <div class="card warning">
        <h2>🧪 Instrucciones de Prueba</h2>
        <p>Para verificar que el descuento de inventario funcione:</p>
        
        <ol>
            <li><strong>Verificar inventario inicial:</strong>
                <ul>
                    <li>Ve a "Suministros" → "Vista Sucursal"</li>
                    <li>Anota las cantidades actuales de algunos suministros</li>
                    <li>Especialmente ingredientes como harina, aceite, etc.</li>
                </ul>
            </li>
            <li><strong>Realizar una venta:</strong>
                <ul>
                    <li>Ve a Mesa 5</li>
                    <li>Agrega productos que usen suministros conocidos</li>
                    <li>Envía el pedido</li>
                    <li>Factura la mesa</li>
                </ul>
            </li>
            <li><strong>Verificar descuento:</strong>
                <ul>
                    <li>Regresa a "Suministros" → "Vista Sucursal"</li>
                    <li>Verifica que las cantidades hayan disminuido</li>
                    <li>El descuento debe ser proporcional a los productos vendidos</li>
                </ul>
            </li>
            <li><strong>Revisar logs:</strong>
                <ul>
                    <li>Abre la consola del navegador (F12)</li>
                    <li>Busca mensajes que empiecen con "INVENTARIO"</li>
                    <li>Deben mostrar los suministros procesados</li>
                </ul>
            </li>
        </ol>
    </div>

    <div class="card">
        <h2>📊 Ejemplo de Funcionamiento</h2>
        <p>Supongamos que vendes 2 hamburguesas y cada hamburguesa usa:</p>
        
        <table class="inventory-table">
            <thead>
                <tr>
                    <th>Suministro</th>
                    <th>Cantidad por Producto</th>
                    <th>Productos Vendidos</th>
                    <th>Total a Descontar</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Pan (ID: 1)</td>
                    <td>1 unidad</td>
                    <td>2 hamburguesas</td>
                    <td>1 × 2 = 2 panes</td>
                </tr>
                <tr>
                    <td>Carne (ID: 2)</td>
                    <td>150g</td>
                    <td>2 hamburguesas</td>
                    <td>150 × 2 = 300g</td>
                </tr>
                <tr>
                    <td>Queso (ID: 3)</td>
                    <td>50g</td>
                    <td>2 hamburguesas</td>
                    <td>50 × 2 = 100g</td>
                </tr>
            </tbody>
        </table>
        
        <h4>Resultado en la tabla sucursal:</h4>
        <div class="debug-info">
ANTES:
- Pan (ID: 1): 100 unidades
- Carne (ID: 2): 5000g
- Queso (ID: 3): 2000g

DESPUÉS:
- Pan (ID: 1): 98 unidades (100 - 2)
- Carne (ID: 2): 4700g (5000 - 300)
- Queso (ID: 3): 1900g (2000 - 100)
        </div>
    </div>

    <div class="card">
        <h2>📋 Logs Esperados</h2>
        <p>En los logs del servidor deberías ver:</p>
        
        <div class="debug-info">
FACTURACIÓN CORREGIDA - Iniciando descuento de inventario
INVENTARIO - Producto Hamburguesa (ID: 15) tiene 3 suministros
INVENTARIO - Suministro ID: 1, Cantidad actual: 100, A descontar: 2
INVENTARIO - Suministro ID: 2, Cantidad actual: 5000, A descontar: 300
INVENTARIO - Suministro ID: 3, Cantidad actual: 2000, A descontar: 100
INVENTARIO ACTUALIZADO - Suministro ID: 1, Nueva cantidad: 98
INVENTARIO ACTUALIZADO - Suministro ID: 2, Nueva cantidad: 4700
INVENTARIO ACTUALIZADO - Suministro ID: 3, Nueva cantidad: 1900
FACTURACIÓN CORREGIDA - Inventario actualizado, 3 suministros procesados
        </div>
    </div>

    <div class="card">
        <h2>🔗 Enlaces de Prueba</h2>
        <p>Usa estos enlaces para probar la funcionalidad:</p>
        
        <a href="../../index.php?action=vistaSucursal" class="btn btn-primary" target="_blank">
            📦 Ver Inventario Actual
        </a>
        
        <a href="../../index.php?action=registroPmesa&ida=5" class="btn btn-success" target="_blank">
            🏠 Mesa 5 (Hacer Venta)
        </a>
        
        <a href="../../index.php?action=mesa" class="btn btn-danger" target="_blank">
            📋 Lista de Mesas
        </a>
    </div>

    <div class="card success">
        <h2>🎉 Funcionalidad Completa</h2>
        <p style="font-size: 18px;">
            <strong>El sistema ahora incluye descuento automático de inventario:</strong>
        </p>
        
        <ul>
            <li>✅ <strong>Validación de pagos:</strong> Funciona correctamente</li>
            <li>✅ <strong>Facturación completa:</strong> Crea pedidos, ventas y productos</li>
            <li>✅ <strong>Descuento de inventario:</strong> Actualiza automáticamente las cantidades</li>
            <li>✅ <strong>Impresión automática:</strong> Abre PDF después de facturar</li>
            <li>✅ <strong>Limpieza de mesa:</strong> Libera la mesa para nuevos pedidos</li>
        </ul>
        
        <div style="text-align: center; margin-top: 20px;">
            <button class="btn btn-success" onclick="mostrarResumenCompleto()">
                📋 Ver Resumen Completo
            </button>
        </div>
    </div>

</div>

<script>
function mostrarResumenCompleto() {
    alert('📋 SISTEMA DE FACTURACIÓN COMPLETO:\n\n' +
          '✅ VALIDACIÓN DE PAGOS:\n' +
          '   • Campo "Total a Pagar" muestra valor correcto\n' +
          '   • Validación de 5 formas de pago\n' +
          '   • Mensajes de error claros\n\n' +
          '✅ FACTURACIÓN:\n' +
          '   • Crea pedidos y ventas\n' +
          '   • Inserta productos facturados\n' +
          '   • Calcula totales y cambios\n\n' +
          '✅ INVENTARIO:\n' +
          '   • Descuenta suministros automáticamente\n' +
          '   • Consolida descuentos por suministro\n' +
          '   • Protege contra inventarios negativos\n\n' +
          '✅ FINALIZACIÓN:\n' +
          '   • Limpia la mesa\n' +
          '   • Abre PDF para impresión\n' +
          '   • Logs detallados para debugging\n\n' +
          '¡Sistema completamente funcional!');
}

// Test automático al cargar
document.addEventListener('DOMContentLoaded', function() {
    console.log('📦 Test de descuento de inventario cargado');
    console.log('✅ Funcionalidad agregada al proceso de facturación');
    console.log('🧪 Prueba facturando una mesa para verificar descuentos');
});
</script>

</body>
</html>
