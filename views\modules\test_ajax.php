<?php
// Archivo para probar las funciones AJAX

// Incluir archivos necesarios (ya están incluidos desde index.php)
// require_once "models/conexion.php";
// require_once "models/crudEstadoPedidos.php";
// require_once "controllers/controllerEstadoPedidos.php";

// Iniciar sesión si no está iniciada
session_start();
if (!isset($_SESSION["usuario"])) {
    $_SESSION["usuario"] = 1;
    $_SESSION["tipo_usuario"] = 1;
    $_SESSION["perfil"] = "administrador";
}

echo "<h1>🧪 Prueba de Funciones AJAX</h1>";
echo "<hr>";

// Verificar que tengamos datos para probar
try {
    $db = Conexion::conectar();
} catch (Exception $e) {
    echo "❌ Error de conexión: " . $e->getMessage();
    exit;
}

echo "<h2>1. 📊 Datos Disponibles para Prueba</h2>";

// Buscar pedidos en estado borrador
$stmt = $db->prepare("SELECT * FROM pedidos WHERE estado = 'borrador' LIMIT 5");
$stmt->execute();
$pedidosBorrador = $stmt->fetchAll();

echo "<h3>Pedidos Borrador:</h3>";
if (!empty($pedidosBorrador)) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Número</th><th>Mesa</th><th>Estado</th><th>Acción</th></tr>";
    foreach ($pedidosBorrador as $pedido) {
        echo "<tr>";
        echo "<td>" . $pedido['id'] . "</td>";
        echo "<td>" . $pedido['numero_pedido'] . "</td>";
        echo "<td>" . $pedido['mesa_id'] . "</td>";
        echo "<td>" . $pedido['estado'] . "</td>";
        echo "<td><button onclick='enviarPedidoTest(" . $pedido['id'] . ")' style='background: #28a745; color: white; padding: 5px; border: none;'>📤 Enviar</button></td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "❌ No hay pedidos borrador. <a href='index.php?action=crear_datos_prueba'>Crear datos de prueba</a><br>";
}

// Buscar pedidos enviados
$stmt = $db->prepare("SELECT * FROM pedidos WHERE estado = 'enviado' LIMIT 5");
$stmt->execute();
$pedidosEnviados = $stmt->fetchAll();

echo "<h3>Pedidos Enviados:</h3>";
if (!empty($pedidosEnviados)) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Número</th><th>Mesa</th><th>Estado</th><th>Acción</th></tr>";
    foreach ($pedidosEnviados as $pedido) {
        echo "<tr>";
        echo "<td>" . $pedido['id'] . "</td>";
        echo "<td>" . $pedido['numero_pedido'] . "</td>";
        echo "<td>" . $pedido['mesa_id'] . "</td>";
        echo "<td>" . $pedido['estado'] . "</td>";
        echo "<td><button onclick='entregarPedidoTest(" . $pedido['id'] . ")' style='background: #007bff; color: white; padding: 5px; border: none;'>✅ Entregar</button></td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "❌ No hay pedidos enviados<br>";
}

echo "<h2>2. 🔧 Pruebas Directas</h2>";

echo "<div style='margin: 20px 0;'>";
echo "<button onclick='probarConexionAjax()' style='background: #ffc107; color: black; padding: 10px; border: none; margin: 5px;'>🔗 Probar Conexión AJAX</button>";
echo "<button onclick='crearPedidoPrueba()' style='background: #6c757d; color: white; padding: 10px; border: none; margin: 5px;'>📝 Crear Pedido de Prueba</button>";
echo "</div>";

echo "<h2>3. 📋 Log de Resultados</h2>";
echo "<div id='log' style='background: #f8f9fa; padding: 10px; border: 1px solid #ddd; height: 300px; overflow-y: scroll;'>";
echo "Esperando resultados...<br>";
echo "</div>";

echo "<h2>4. 🔗 Enlaces Útiles</h2>";
echo "<a href='index.php?action=crear_datos_prueba' style='background: #007bff; color: white; padding: 10px; text-decoration: none; margin: 5px; display: inline-block;'>🧪 Crear Datos</a><br>";
echo "<a href='index.php?action=test_flujo_pedidos' style='background: #28a745; color: white; padding: 10px; text-decoration: none; margin: 5px; display: inline-block;'>🧪 Test Flujo</a><br>";
echo "<a href='index.php?action=diagnostico' style='background: #ffc107; color: black; padding: 10px; text-decoration: none; margin: 5px; display: inline-block;'>🔧 Diagnóstico</a><br>";
echo "<a href='views/modules/test_final.php' style='background: #6c757d; color: white; padding: 10px; text-decoration: none; margin: 5px; display: inline-block;'>🎯 Test Final</a><br>";

?>

<script>
function log(message) {
    const logDiv = document.getElementById('log');
    const timestamp = new Date().toLocaleTimeString();
    logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
    logDiv.scrollTop = logDiv.scrollHeight;
}

function probarConexionAjax() {
    log('🔗 Probando conexión AJAX...');

    fetch('views/modules/ajaxEstadoPedidos.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'test_connection=true'
    })
    .then(response => {
        log('📡 Respuesta recibida, status: ' + response.status);
        return response.text();
    })
    .then(text => {
        log('📄 Respuesta como texto: ' + text.substring(0, 200) + (text.length > 200 ? '...' : ''));
        try {
            const data = JSON.parse(text);
            log('✅ JSON válido: ' + JSON.stringify(data));
        } catch (e) {
            log('❌ Error parseando JSON: ' + e.message);
        }
    })
    .catch(error => {
        log('❌ Error de conexión: ' + error.message);
    });
}

function enviarPedidoTest(pedidoId) {
    log(`📤 Enviando pedido ${pedidoId}...`);

    fetch('views/modules/ajaxEstadoPedidos.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'enviar_pedido=true&pedido_id=' + pedidoId
    })
    .then(response => {
        log('📡 Respuesta recibida para envío, status: ' + response.status);
        return response.text();
    })
    .then(text => {
        log('📄 Respuesta como texto: ' + text);
        try {
            const data = JSON.parse(text);
            log('✅ Resultado: ' + JSON.stringify(data));
            if (data.status === 'success') {
                log('🎉 Pedido enviado exitosamente');
                setTimeout(() => location.reload(), 2000);
            } else {
                log('⚠️ Error en envío: ' + data.message);
            }
        } catch (e) {
            log('❌ Error parseando JSON: ' + e.message);
        }
    })
    .catch(error => {
        log('❌ Error: ' + error.message);
    });
}

function entregarPedidoTest(pedidoId) {
    log(`✅ Entregando pedido ${pedidoId}...`);

    fetch('views/modules/ajaxEstadoPedidos.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'entregar_pedido=true&pedido_id=' + pedidoId
    })
    .then(response => {
        log('📡 Respuesta recibida para entrega, status: ' + response.status);
        return response.text();
    })
    .then(text => {
        log('📄 Respuesta como texto: ' + text);
        try {
            const data = JSON.parse(text);
            log('✅ Resultado: ' + JSON.stringify(data));
            if (data.status === 'success') {
                log('🎉 Pedido entregado exitosamente');
                setTimeout(() => location.reload(), 2000);
            } else {
                log('⚠️ Error en entrega: ' + data.message);
            }
        } catch (e) {
            log('❌ Error parseando JSON: ' + e.message);
        }
    })
    .catch(error => {
        log('❌ Error: ' + error.message);
    });
}

function crearPedidoPrueba() {
    log('📝 Creando pedido de prueba...');

    fetch('index.php?action=crear_datos_prueba', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'crear_pedido_borrador=true'
    })
    .then(response => {
        log('📡 Pedido de prueba creado');
        setTimeout(() => location.reload(), 2000);
    })
    .catch(error => {
        log('❌ Error creando pedido: ' + error.message);
    });
}

// Probar conexión automáticamente al cargar
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(probarConexionAjax, 1000);
});
</script>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}

table {
    background-color: white;
    width: 100%;
    max-width: 600px;
    margin: 10px 0;
}

th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #007bff;
    color: white;
}

button {
    cursor: pointer;
    border-radius: 5px;
    font-weight: bold;
}

button:hover {
    opacity: 0.8;
}
</style>
