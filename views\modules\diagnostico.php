<?php
// Vista de diagnóstico para el sistema de pedidos
// Acceso: https://macarena.anconclub.com/diagnostico

// Crear instancia del controlador (las clases ya están cargadas por index.php)
$controllerDiagnostico = new ControllerDiagnostico();

// Verificar si se solicita formato HTML completo
if (isset($_GET['formato']) && $_GET['formato'] == 'html') {
    echo $controllerDiagnostico->generarReporteHtmlController();
    exit;
}

// Ejecutar diagnóstico completo
$diagnostico = $controllerDiagnostico->ejecutarDiagnosticoController();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="text-center mb-4">🔧 Diagnóstico del Sistema Macarena</h1>
            <p class="text-center"><strong>Generado:</strong> <?=$diagnostico['timestamp']?></p>
            <hr>

            <?php
            // Estado general
            $estadoGeneral = $diagnostico['estado_general'];
            $alertClass = $estadoGeneral['status'] == 'success' ? 'alert-success' : ($estadoGeneral['status'] == 'warning' ? 'alert-warning' : 'alert-danger');
            ?>

            <div class="alert <?=$alertClass?> text-center">
                <h4>Estado General: <?=$estadoGeneral['message']?></h4>
                <p class="mb-0">✅ Exitosos: <?=$estadoGeneral['exitosos']?> | ⚠️ Advertencias: <?=$estadoGeneral['advertencias']?> | ❌ Errores: <?=$estadoGeneral['errores']?></p>
            </div>

            <div class="row">
                <!-- 1. Conexión a Base de Datos -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5>1. 🔌 Conexión a Base de Datos</h5>
                        </div>
                        <div class="card-body">
                            <?php
                            $conexion = $diagnostico['detalles']['conexion'];
                            $iconoConexion = $conexion['status'] == 'success' ? '✅' : '❌';
                            ?>
                            <p><?=$iconoConexion?> <?=$conexion['message']?></p>
                            <?php if (isset($conexion['database'])): ?>
                                <p><strong>Base de datos:</strong> <?=$conexion['database']?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- 2. Estructura de tabla pedidos -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5>2. 📋 Estructura de Tabla Pedidos</h5>
                        </div>
                        <div class="card-body">
                            <?php
                            $estructura = $diagnostico['detalles']['estructura_pedidos'];
                            $iconoEstructura = $estructura['status'] == 'success' ? '✅' : '❌';
                            ?>
                            <p><?=$iconoEstructura?> <?=$estructura['message']?></p>

                            <?php if (isset($estructura['columnas'])): ?>
                                <p><strong>Columnas encontradas:</strong> <?=count($estructura['columnas'])?></p>
                            <?php endif; ?>

                            <?php if (isset($estructura['columnas_faltantes']) && !empty($estructura['columnas_faltantes'])): ?>
                                <p class="text-danger"><strong>Columnas faltantes:</strong> <?=implode(', ', $estructura['columnas_faltantes'])?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- 3. Tabla Impresoras -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5>3. 🖨️ Tabla Impresoras</h5>
                        </div>
                        <div class="card-body">
                            <?php
                            $impresoras = $diagnostico['detalles']['tabla_impresoras'];
                            $iconoImpresoras = $impresoras['status'] == 'success' ? '✅' : '❌';
                            ?>
                            <p><?=$iconoImpresoras?> <?=$impresoras['message']?></p>

                            <?php if (isset($impresoras['total_impresoras'])): ?>
                                <p><strong>Total impresoras configuradas:</strong> <?=$impresoras['total_impresoras']?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- 4. Configuración de Impresoras -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5>4. ⚙️ Configuración de Impresoras</h5>
                        </div>
                        <div class="card-body">
                            <?php
                            $configImpresoras = $diagnostico['detalles']['configuracion_impresoras'];
                            $iconoConfig = $configImpresoras['status'] == 'success' ? '✅' : ($configImpresoras['status'] == 'warning' ? '⚠️' : '❌');
                            ?>
                            <p><?=$iconoConfig?> <?=$configImpresoras['message']?></p>

                            <?php if (isset($configImpresoras['impresoras'])): ?>
                                <ul class="list-unstyled">
                                    <?php foreach ($configImpresoras['impresoras'] as $imp): ?>
                                        <li><strong><?=$imp['categoria']?>:</strong> <?=$imp['ip']?>:<?=$imp['puerto']?></li>
                                    <?php endforeach; ?>
                                </ul>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Secciones adicionales en formato compacto -->
            <div class="row">
                <div class="col-12">
                    <div class="accordion" id="diagnosticoAccordion">

                        <!-- 5. Tablas de Historial -->
                        <div class="card">
                            <div class="card-header" id="headingHistorial">
                                <h5 class="mb-0">
                                    <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapseHistorial">
                                        5. 📚 Tablas de Historial
                                    </button>
                                </h5>
                            </div>
                            <div id="collapseHistorial" class="collapse show" data-parent="#diagnosticoAccordion">
                                <div class="card-body">
                                    <?php
                                    $tablasHistorial = $diagnostico['detalles']['tablas_historial'];
                                    foreach ($tablasHistorial as $tabla => $resultado):
                                        $iconoTabla = $resultado['status'] == 'success' ? '✅' : '❌';
                                    ?>
                                        <p><?=$iconoTabla?> <strong><?=ucfirst($tabla)?>:</strong> <?=$resultado['message']?>
                                        <?php if (isset($resultado['registros'])): ?>
                                            (Registros: <?=$resultado['registros']?>)
                                        <?php endif; ?>
                                        </p>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>

                        <!-- 6. Triggers -->
                        <div class="card">
                            <div class="card-header" id="headingTriggers">
                                <h5 class="mb-0">
                                    <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapseTriggers">
                                        6. ⚡ Triggers
                                    </button>
                                </h5>
                            </div>
                            <div id="collapseTriggers" class="collapse" data-parent="#diagnosticoAccordion">
                                <div class="card-body">
                                    <?php
                                    $triggers = $diagnostico['detalles']['triggers'];
                                    $iconoTriggers = $triggers['status'] == 'success' ? '✅' : ($triggers['status'] == 'warning' ? '⚠️' : '❌');
                                    ?>
                                    <p><?=$iconoTriggers?> <?=$triggers['message']?></p>

                                    <?php if (isset($triggers['triggers'])): ?>
                                        <p><strong>Triggers encontrados:</strong> <?=implode(', ', $triggers['triggers'])?></p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- 7. Archivos del Sistema -->
                        <div class="card">
                            <div class="card-header" id="headingArchivos">
                                <h5 class="mb-0">
                                    <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapseArchivos">
                                        7. 📁 Archivos del Sistema
                                    </button>
                                </h5>
                            </div>
                            <div id="collapseArchivos" class="collapse" data-parent="#diagnosticoAccordion">
                                <div class="card-body">
                                    <?php
                                    $archivos = $diagnostico['detalles']['archivos'];
                                    foreach ($archivos as $archivo => $resultado):
                                        $iconoArchivo = $resultado['status'] == 'success' ? '✅' : '❌';
                                        $nombreArchivo = basename($archivo);
                                    ?>
                                        <p><?=$iconoArchivo?> <strong><?=$nombreArchivo?>:</strong> <?=$resultado['message']?>
                                        <?php if (isset($resultado['size'])): ?>
                                            (<?=number_format($resultado['size'] / 1024, 2)?> KB)
                                        <?php endif; ?>
                                        </p>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Información adicional en cards -->
            <div class="row mt-4">
                <!-- 8. Información de Sesión -->
                <div class="col-md-4 mb-3">
                    <div class="card">
                        <div class="card-header">
                            <h6>8. 👤 Información de Sesión</h6>
                        </div>
                        <div class="card-body">
                            <?php
                            $infoSesion = $controllerDiagnostico->obtenerInformacionSesionController();
                            if ($infoSesion['usuario_activo']):
                            ?>
                                <p class="mb-1">✅ <strong>Usuario:</strong> <?=$infoSesion['usuario']?></p>
                                <p class="mb-1">✅ <strong>Tipo:</strong> <?=$infoSesion['tipo_usuario']?></p>
                                <p class="mb-0">✅ <strong>Mesa:</strong> <?=$infoSesion['mesa']?></p>
                            <?php else: ?>
                                <p class="mb-0">⚠️ No hay usuario en sesión</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- 9. Información del Sistema -->
                <div class="col-md-4 mb-3">
                    <div class="card">
                        <div class="card-header">
                            <h6>9. 💻 Información del Sistema</h6>
                        </div>
                        <div class="card-body">
                            <?php $infoSistema = $controllerDiagnostico->obtenerInformacionSistemaController(); ?>
                            <p class="mb-1"><strong>PHP:</strong> <?=$infoSistema['php_version']?></p>
                            <p class="mb-1"><strong>Servidor:</strong> <?=substr($infoSistema['servidor'], 0, 20)?>...</p>
                            <p class="mb-0"><strong>Fecha:</strong> <?=$infoSistema['fecha_hora']?></p>
                        </div>
                    </div>
                </div>

                <!-- 10. Datos del Sistema -->
                <div class="col-md-4 mb-3">
                    <div class="card">
                        <div class="card-header">
                            <h6>10. 📊 Datos del Sistema</h6>
                        </div>
                        <div class="card-body">
                            <?php
                            $datosPrueba = $diagnostico['detalles']['datos_prueba'];
                            $iconoDatos = $datosPrueba['status'] == 'success' ? '✅' : '❌';
                            ?>
                            <p><?=$iconoDatos?> <?=$datosPrueba['message']?></p>

                            <?php if ($datosPrueba['status'] == 'success'): ?>
                                <p class="mb-1"><strong>Pedidos:</strong> <?=$datosPrueba['total_pedidos']?></p>
                                <p class="mb-1"><strong>Productos:</strong> <?=$datosPrueba['total_productos']?></p>
                                <p class="mb-0"><strong>Mesas:</strong> <?=$datosPrueba['total_mesas']?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 11. Test de Conectividad con Impresoras -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5>11. 🔗 Test de Conectividad con Impresoras</h5>
                        </div>
                        <div class="card-body">
                            <?php
                            $testConectividad = $controllerDiagnostico->probarConectividadImpresorasController();
                            if (!empty($testConectividad)):
                                foreach ($testConectividad as $resultado):
                                    $icono = $resultado['conectada'] ? '✅' : '❌';
                            ?>
                                    <p><?=$icono?> <strong><?=$resultado['categoria']?></strong> (<?=$resultado['ip']?>:<?=$resultado['puerto']?>): <?=$resultado['mensaje']?></p>
                            <?php
                                endforeach;
                            else:
                            ?>
                                <p>⚠️ No hay impresoras configuradas para probar</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Resumen y Recomendaciones -->
            <hr class="my-4">
            <div class="row">
                <div class="col-12">
                    <h2>📋 Resumen y Recomendaciones</h2>

                    <?php if ($estadoGeneral['errores'] > 0): ?>
                        <div class="alert alert-danger">
                            <strong>⚠️ Acción Requerida:</strong> Se encontraron <?=$estadoGeneral['errores']?> errores críticos que deben ser corregidos.

                            <h6 class="mt-3">Pasos recomendados:</h6>
                            <ol class="mb-0">
                                <li>Ejecutar el script de migración: <code>sql/migracion_segura.sql</code></li>
                                <li>Verificar que todos los archivos estén subidos al servidor</li>
                                <li>Revisar los logs de error del servidor web</li>
                                <li>Ejecutar nuevamente este diagnóstico</li>
                            </ol>
                        </div>
                    <?php elseif ($estadoGeneral['advertencias'] > 0): ?>
                        <div class="alert alert-warning">
                            <strong>ℹ️ Información:</strong> El sistema está funcional pero hay <?=$estadoGeneral['advertencias']?> advertencias que podrían mejorarse.
                        </div>
                    <?php else: ?>
                        <div class="alert alert-success">
                            <strong>✅ Excelente:</strong> El sistema está completamente funcional y configurado correctamente.
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Enlaces útiles -->
            <div class="row mt-4">
                <div class="col-12">
                    <h4>🔗 Enlaces Útiles</h4>
                    <div class="row">
                        <div class="col-md-2 mb-2">
                            <a href="?formato=html" target="_blank" class="btn btn-primary btn-block">📄 Reporte HTML</a>
                        </div>
                        <div class="col-md-2 mb-2">
                            <a href="pantallaCocina?categoria=bar" target="_blank" class="btn btn-success btn-block">🍺 Pantalla Bar</a>
                        </div>
                        <div class="col-md-2 mb-2">
                            <a href="pantallaCocina?categoria=cocina" target="_blank" class="btn btn-warning btn-block">🍳 Pantalla Cocina</a>
                        </div>
                        <div class="col-md-2 mb-2">
                            <a href="pantallaCocina?categoria=asados" target="_blank" class="btn btn-danger btn-block">🥩 Pantalla Asados</a>
                        </div>
                        <div class="col-md-2 mb-2">
                            <a href="index.php?action=registroPmesa&ida=1" target="_blank" class="btn btn-purple btn-block">🪑 Mesa 1</a>
                        </div>
                        <div class="col-md-2 mb-2">
                            <a href="inicio" class="btn btn-secondary btn-block">🏠 Inicio</a>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-3 mb-2">
                            <a href="debug_mesa" class="btn btn-dark btn-block">🔍 Debug Mesa</a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="debug_cocina" class="btn btn-warning btn-block">🍳 Debug Cocina</a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="test_flujo_pedidos" class="btn btn-success btn-block">🧪 Test Flujo</a>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-3 mb-2">
                            <a href="crear_datos_prueba" class="btn btn-primary btn-block">🧪 Crear Datos</a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="test_ajax" class="btn btn-danger btn-block">🔧 Test AJAX</a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-4">
                <small class="text-muted">Diagnóstico generado usando el patrón MVC - Sistema Macarena v2.0</small>
            </div>
        </div>
    </div>
</div>

<style>
.btn-purple {
    background-color: #6f42c1;
    border-color: #6f42c1;
    color: white;
}
.btn-purple:hover {
    background-color: #5a32a3;
    border-color: #5a32a3;
    color: white;
}
</style>
