<?php
// Debug específico para el pedido P000475
echo "<h1>🔍 Debug Pedido Específico</h1>";
echo "<p><strong>Investigar por qué no aparecen los botones de categorías</strong></p>";

$pedido_numero = $_GET['pedido'] ?? 'P000475';

try {
    require_once '../../models/conexion.php';
    $conexion = new Conexion();
    $pdo = $conexion->conectar();
    
    echo "<div style='background-color: #d1ecf1; padding: 15px; border-left: 4px solid #17a2b8; margin-bottom: 20px;'>";
    echo "<h4>🔍 Investigando Pedido: $pedido_numero</h4>";
    echo "</div>";
    
    // 1. Buscar el pedido
    echo "<h2>1. 📋 Información del Pedido</h2>";
    $stmt = $pdo->prepare("SELECT * FROM pedidos WHERE numero_pedido = :numero");
    $stmt->bindParam(":numero", $pedido_numero, PDO::PARAM_STR);
    $stmt->execute();
    $pedido = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($pedido) {
        echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0;'>";
        echo "<h4>✅ Pedido Encontrado</h4>";
        echo "<p><strong>ID:</strong> {$pedido['id']}</p>";
        echo "<p><strong>Número:</strong> {$pedido['numero_pedido']}</p>";
        echo "<p><strong>Estado:</strong> {$pedido['estado']}</p>";
        echo "<p><strong>Mesa ID:</strong> {$pedido['mesa_id']}</p>";
        echo "<p><strong>Fecha:</strong> {$pedido['fecha_pedido']}</p>";
        echo "</div>";
        
        $pedido_id = $pedido['id'];
        
    } else {
        echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
        echo "<h4>❌ Pedido No Encontrado</h4>";
        echo "<p>No se encontró el pedido con número: $pedido_numero</p>";
        echo "</div>";
        
        // Mostrar pedidos disponibles
        echo "<h3>📋 Pedidos Disponibles:</h3>";
        $stmt = $pdo->query("SELECT id, numero_pedido, estado, fecha_pedido FROM pedidos ORDER BY fecha_pedido DESC LIMIT 10");
        $pedidos_disponibles = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($pedidos_disponibles) {
            echo "<ul>";
            foreach ($pedidos_disponibles as $p) {
                echo "<li><a href='?pedido={$p['numero_pedido']}'>{$p['numero_pedido']}</a> - {$p['estado']} - {$p['fecha_pedido']}</li>";
            }
            echo "</ul>";
        }
        exit;
    }
    
    // 2. Buscar productos del pedido
    echo "<h2>2. 🛒 Productos del Pedido</h2>";
    $stmt = $pdo->prepare("
        SELECT 
            p.id as producto_id,
            p.nombre as producto_nombre,
            p.categoria as categoria_producto,
            p.precio,
            pvm.cantidad,
            pvm.fecha_hora,
            pvm.nota,
            (pvm.cantidad * p.precio) as subtotal,
            CASE
                WHEN p.categoria IN ('bebidas', 'cervezas', 'licores', 'vinos', 'cocteles', 'refrescos', 'jugos', 'agua', 'bar') THEN 'bar'
                WHEN p.categoria IN ('carnes', 'parrilla', 'asados', 'pescados', 'mariscos') THEN 'asados'
                ELSE 'cocina'
            END as categoria_impresora
        FROM producto_vendido_mesa pvm
        INNER JOIN productos p ON pvm.productos_id = p.id
        WHERE pvm.pedidos_id = :pedido_id
        ORDER BY categoria_impresora, p.nombre
    ");
    $stmt->bindParam(":pedido_id", $pedido_id, PDO::PARAM_INT);
    $stmt->execute();
    $productos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p><strong>Total productos encontrados:</strong> " . count($productos) . "</p>";
    
    if (!empty($productos)) {
        echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0;'>";
        echo "<h4>✅ Productos Encontrados</h4>";
        
        echo "<table style='width: 100%; border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr style='background-color: #e9ecef;'>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Producto</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Categoría Original</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Categoría Impresora</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Cantidad</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Precio</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Subtotal</th>";
        echo "</tr>";
        
        $total_pedido = 0;
        foreach ($productos as $prod) {
            echo "<tr>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$prod['producto_nombre']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$prod['categoria_producto']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px; font-weight: bold;'>" . strtoupper($prod['categoria_impresora']) . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px; text-align: center;'>{$prod['cantidad']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px; text-align: right;'>$" . number_format($prod['precio'], 0) . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px; text-align: right;'>$" . number_format($prod['subtotal'], 0) . "</td>";
            echo "</tr>";
            $total_pedido += $prod['subtotal'];
        }
        
        echo "<tr style='background-color: #f8f9fa; font-weight: bold;'>";
        echo "<td colspan='5' style='border: 1px solid #ddd; padding: 8px; text-align: right;'>TOTAL:</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px; text-align: right;'>$" . number_format($total_pedido, 0) . "</td>";
        echo "</tr>";
        echo "</table>";
        echo "</div>";
        
        // 3. Agrupar por categoría de impresora
        echo "<h2>3. 📂 Agrupación por Categoría de Impresora</h2>";
        $categorias_agrupadas = [];
        foreach ($productos as $prod) {
            $cat = $prod['categoria_impresora'];
            if (!isset($categorias_agrupadas[$cat])) {
                $categorias_agrupadas[$cat] = [
                    'productos' => [],
                    'total_cantidad' => 0,
                    'total_precio' => 0
                ];
            }
            $categorias_agrupadas[$cat]['productos'][] = $prod;
            $categorias_agrupadas[$cat]['total_cantidad'] += $prod['cantidad'];
            $categorias_agrupadas[$cat]['total_precio'] += $prod['subtotal'];
        }
        
        foreach ($categorias_agrupadas as $categoria => $info) {
            $color_categoria = [
                'bar' => '#17a2b8',
                'cocina' => '#28a745', 
                'asados' => '#dc3545'
            ];
            $color = $color_categoria[$categoria] ?? '#6c757d';
            
            echo "<div style='background-color: white; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 5px solid $color;'>";
            echo "<h4 style='color: $color;'>🖨️ " . strtoupper($categoria) . "</h4>";
            echo "<p><strong>Productos:</strong> {$info['total_cantidad']} items</p>";
            echo "<p><strong>Total:</strong> $" . number_format($info['total_precio'], 0) . "</p>";
            
            echo "<ul>";
            foreach ($info['productos'] as $prod) {
                echo "<li>{$prod['producto_nombre']} x{$prod['cantidad']} - $" . number_format($prod['subtotal'], 0) . "</li>";
            }
            echo "</ul>";
            echo "</div>";
        }
        
        // 4. Probar el componente
        echo "<h2>4. 🧪 Probar Componente de Impresión</h2>";
        
        require_once 'componente_impresion_categorias.php';
        
        echo "<div style='background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>🎯 Resultado del Componente:</h4>";
        
        mostrarBotonesImpresionCategorias($pedido_id, $pedido['numero_pedido'], $pedido['mesa_id']);
        
        echo "</div>";
        
    } else {
        echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
        echo "<h4>❌ No se encontraron productos</h4>";
        echo "<p>El pedido existe pero no tiene productos asociados.</p>";
        echo "</div>";
        
        // Verificar si hay productos en producto_vendido_mesa para este pedido
        $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM producto_vendido_mesa WHERE pedidos_id = :pedido_id");
        $stmt->bindParam(":pedido_id", $pedido_id, PDO::PARAM_INT);
        $stmt->execute();
        $total_pvm = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        echo "<p><strong>Productos en producto_vendido_mesa:</strong> $total_pvm</p>";
        
        if ($total_pvm > 0) {
            echo "<p>⚠️ Hay productos en la tabla pero no se están mostrando. Posible problema en el JOIN.</p>";
            
            // Mostrar productos sin JOIN
            $stmt = $pdo->prepare("SELECT * FROM producto_vendido_mesa WHERE pedidos_id = :pedido_id");
            $stmt->bindParam(":pedido_id", $pedido_id, PDO::PARAM_INT);
            $stmt->execute();
            $productos_raw = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h3>Productos raw en producto_vendido_mesa:</h3>";
            echo "<pre>" . print_r($productos_raw, true) . "</pre>";
        }
    }
    
} catch (Exception $e) {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
    echo "<h4>❌ Error</h4>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Archivo:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Línea:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

// Formulario para cambiar pedido
echo "<h2>🔄 Cambiar Pedido</h2>";
echo "<form method='GET' style='background-color: #fff3cd; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<label><strong>Número de Pedido:</strong></label><br>";
echo "<input type='text' name='pedido' value='$pedido_numero' style='padding: 8px; border: 1px solid #ccc; border-radius: 3px; width: 200px;'><br><br>";
echo "<button type='submit' style='background-color: #ffc107; color: #212529; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold;'>🔍 Investigar Pedido</button>";
echo "</form>";

// Botones de navegación
echo "<p style='margin-top: 30px;'>";
echo "<a href='test_registroPmesa_integrado.php' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>← Test Integración</a>";
echo "<a href='../../index.php?action=registroPmesa&ida=3' style='background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🪑 Mesa 3</a>";
echo "<a href='demo_impresion_categorias.php' style='background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🎯 Demo</a>";
echo "</p>";
?>
