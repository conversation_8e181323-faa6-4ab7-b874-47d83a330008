
<?php 
ob_start();
class controller<PERSON>oll extends MvcController
{
	#REGISTRO DE ROLES
	#------------------------------------
	 public function registroRolController()
		{//echo "<script>alert('Entro Controller ".$_POST["nombreRolRegistro"]." no')</script>";
		 if(isset($_POST["nombreRolRegistro"]))
			{	
				$datosController =array('nombre'=>$_POST["nombreRolRegistro"]);
				echo "<script>alert('Entro Controller ".$datosController['nombre']." if')</script>";	
			/**/	$respuesta = DatosRoll::registroRolModel($datosController, "roles");

				if($respuesta == "success")
					{	header("location:index.php?action=okR");	}
				else
					{	header("location:index.php");	}
			}
		}
	#------------------------------------
	#VISTA DE ROLES
	#------------------------------------
	 public function vistaRolController()
		{
		 $respuesta = DatosRoll::vistaRolModel("roles");
		 foreach($respuesta as $row => $item)
			{
				echo'<tr>						
					<td>'.$item["nombre"].'</td>																	
					<td><a href="index.php?action=editarRoll&id='.$item["id"].'">Editar</a> <a href="index.php?action=rol&idBorrar='.$item["id"].'">Borrar</a></td>
				</tr>';
			}
		}
	#------------------------------------
	#EDITAR ROLES
	#------------------------------------
	 public function editarRolController()
		{
			$datosController = $_GET["id"];
			$respuesta = DatosRoll::editarRolModel($datosController, "roles");
			$_SESSION["erol"]=$respuesta;
			echo' <table>
				<thead> 	
		     		 <tr > <td colspan="2" ></td> </tr>
	    		</thead>
				<tbody>
					<tr>
						<td>Nombre :</td>
						<td><input type="text" value="'.$respuesta["nombre"].'" name="nombreRolEditar" required></td>
					</tr>
				</tbody>
				<thead> 	
		     		 <tr > <td colspan="2" ></td> </tr>
	    		</thead>
			</table>
												 
			 <input type="hidden" value="'.$respuesta["id"].'" name="idRolEditar" required> 				
				 <input type="submit" value="Actualizar">';
		}
	#------------------------------------
	#ACTUALIZAR ROLES
	#------------------------------------
	 public function actualizarRolController()
		{echo "<script>alert('Entro Controller Actualizar Roll')</script>";

			if(isset($_POST["nombreRolEditar"]))
				{
					$datosController = array(  "nombre"=>$_POST["nombreRolEditar"],																		
												"id"=>$_POST["idRolEditar"]);				
					$respuesta = DatosRoll::actualizarRolModel($datosController, "roles");
					if($respuesta == "success")
						{	header("location:index.php?action=cambioR");	}
					else
						{	echo "error";	}
				}				
		}
	#------------------------------------
	#BORRAR ROLES
	#------------------------------------
	 public function borrarRolController()
		{
			if(isset($_GET["idBorrar"]))
				{
					$datosController = $_GET["idBorrar"];				
					$respuesta = DatosRoll::borrarRolModel($datosController, "roles");
					if($respuesta == "success")
						{	header("location:index.php?action=rol");	}
				}
		}			
	#---------------------------------

}			
	