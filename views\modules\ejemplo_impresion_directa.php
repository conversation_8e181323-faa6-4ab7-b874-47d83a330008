<?php
// Ejemplo de cómo integrar la impresión directa en el sistema existente
echo "<h1>📝 Ejemplo de Integración - Impresión Directa</h1>";
echo "<p><strong>Cómo usar la impresión directa en tu sistema</strong></p>";

// Incluir la clase
require_once '../../models/crudImpresionDirecta.php';

echo "<h2>💡 Ejemplos de Uso:</h2>";

echo "<div style='background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<h3>1. 🖨️ Impresión Simple</h3>";
echo "<pre><code>";
echo "// Incluir la clase\n";
echo "require_once 'models/crudImpresionDirecta.php';\n\n";
echo "// Imprimir en una categoría específica\n";
echo "\$contenido = \"Mesa 5\\nCerveza Corona x2\\nTotal: \$16.000\";\n";
echo "\$resultado = ImpresionDirecta::imprimir('bar', \$contenido);\n\n";
echo "if (\$resultado['success']) {\n";
echo "    echo \"✅ Impreso exitosamente\";\n";
echo "} else {\n";
echo "    echo \"❌ Error: \" . \$resultado['error'];\n";
echo "}";
echo "</code></pre>";
echo "</div>";

echo "<div style='background-color: #e7f3ff; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<h3>2. 🧾 Impresión de Pedido Completo</h3>";
echo "<pre><code>";
echo "// Productos del pedido\n";
echo "\$productos = [\n";
echo "    ['nombre' => 'Cerveza Corona', 'categoria' => 'bebidas', 'cantidad' => 2, 'precio' => 8000],\n";
echo "    ['nombre' => 'Bandeja Paisa', 'categoria' => 'comidas', 'cantidad' => 1, 'precio' => 25000],\n";
echo "    ['nombre' => 'Churrasco', 'categoria' => 'carnes', 'cantidad' => 1, 'precio' => 35000]\n";
echo "];\n\n";
echo "// Imprimir pedido distribuido\n";
echo "\$resultados = ImpresionDirecta::imprimirPedido(\$productos, '5', 'P000123');\n\n";
echo "// Verificar resultados\n";
echo "foreach (\$resultados as \$categoria => \$resultado) {\n";
echo "    if (\$resultado['success']) {\n";
echo "        echo \"✅ \$categoria: Impreso\";\n";
echo "    } else {\n";
echo "        echo \"❌ \$categoria: \" . \$resultado['error'];\n";
echo "    }\n";
echo "}";
echo "</code></pre>";
echo "</div>";

echo "<div style='background-color: #fff3cd; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<h3>3. 🔍 Test de Conectividad</h3>";
echo "<pre><code>";
echo "// Verificar estado de impresoras\n";
echo "\$estado = ImpresionDirecta::testConectividad();\n\n";
echo "foreach (\$estado as \$categoria => \$info) {\n";
echo "    if (\$info['status'] === 'online') {\n";
echo "        echo \"✅ \$categoria: Online\";\n";
echo "    } else {\n";
echo "        echo \"❌ \$categoria: Offline - \" . \$info['error'];\n";
echo "    }\n";
echo "}";
echo "</code></pre>";
echo "</div>";

// Test en vivo
echo "<h2>🧪 Test en Vivo:</h2>";

if (isset($_POST['test_simple'])) {
    $categoria = $_POST['categoria'];
    $contenido = $_POST['contenido'];
    
    echo "<div style='border: 1px solid #007bff; padding: 20px; margin: 15px 0; border-radius: 8px;'>";
    echo "<h4>🔄 Ejecutando test simple...</h4>";
    
    $resultado = ImpresionDirecta::imprimir($categoria, $contenido);
    
    if ($resultado['success']) {
        echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0;'>";
        echo "<h5>✅ Impresión Exitosa</h5>";
        echo "<p><strong>Categoría:</strong> {$resultado['categoria']}</p>";
        echo "<p><strong>IP:</strong> {$resultado['ip']}</p>";
        echo "<p><strong>Bytes enviados:</strong> {$resultado['bytes_enviados']}</p>";
        echo "<p><strong>Timestamp:</strong> {$resultado['timestamp']}</p>";
        echo "</div>";
    } else {
        echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
        echo "<h5>❌ Error de Impresión</h5>";
        echo "<p><strong>Categoría:</strong> {$resultado['categoria']}</p>";
        echo "<p><strong>Error:</strong> {$resultado['error']}</p>";
        echo "</div>";
    }
    
    echo "</div>";
}

if (isset($_POST['test_pedido'])) {
    echo "<div style='border: 1px solid #007bff; padding: 20px; margin: 15px 0; border-radius: 8px;'>";
    echo "<h4>🔄 Ejecutando test de pedido...</h4>";
    
    $productos = [
        ['nombre' => 'Cerveza Corona', 'categoria' => 'bebidas', 'cantidad' => 2, 'precio' => 8000],
        ['nombre' => 'Bandeja Paisa', 'categoria' => 'comidas', 'cantidad' => 1, 'precio' => 25000],
        ['nombre' => 'Churrasco', 'categoria' => 'carnes', 'cantidad' => 1, 'precio' => 35000]
    ];
    
    $resultados = ImpresionDirecta::imprimirPedido($productos, '5', 'P000' . rand(100, 999));
    
    echo "<h5>📊 Resultados por Impresora:</h5>";
    
    foreach ($resultados as $categoria => $resultado) {
        if ($resultado['success']) {
            echo "<div style='background-color: #d4edda; padding: 10px; border-left: 4px solid #28a745; margin: 10px 0;'>";
            echo "<p>✅ <strong>{$categoria}:</strong> Impreso exitosamente ({$resultado['bytes_enviados']} bytes)</p>";
            echo "</div>";
        } else {
            echo "<div style='background-color: #f8d7da; padding: 10px; border-left: 4px solid #dc3545; margin: 10px 0;'>";
            echo "<p>❌ <strong>{$categoria}:</strong> {$resultado['error']}</p>";
            echo "</div>";
        }
    }
    
    echo "</div>";
}

if (isset($_POST['test_conectividad'])) {
    echo "<div style='border: 1px solid #007bff; padding: 20px; margin: 15px 0; border-radius: 8px;'>";
    echo "<h4>🔄 Ejecutando test de conectividad...</h4>";
    
    $estado = ImpresionDirecta::testConectividad();
    
    echo "<h5>📡 Estado de Impresoras:</h5>";
    
    foreach ($estado as $categoria => $info) {
        if ($info['status'] === 'online') {
            echo "<div style='background-color: #d4edda; padding: 10px; border-left: 4px solid #28a745; margin: 10px 0;'>";
            echo "<p>✅ <strong>{$categoria}:</strong> Online ({$info['ip']}:{$info['puerto']})</p>";
            echo "</div>";
        } else {
            echo "<div style='background-color: #f8d7da; padding: 10px; border-left: 4px solid #dc3545; margin: 10px 0;'>";
            echo "<p>❌ <strong>{$categoria}:</strong> Offline - {$info['error']}</p>";
            echo "</div>";
        }
    }
    
    echo "</div>";
}

// Formularios de test
echo "<h2>🎯 Tests Interactivos:</h2>";

// Test simple
echo "<form method='POST' style='background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>🖨️ Test Simple</h4>";
echo "<div style='margin: 15px 0;'>";
echo "<label><strong>Categoría:</strong></label><br>";
echo "<select name='categoria' required style='padding: 8px; border: 1px solid #ccc; border-radius: 3px; width: 200px;'>";
echo "<option value='bar'>Bar</option>";
echo "<option value='cocina'>Cocina</option>";
echo "<option value='asados'>Asados</option>";
echo "</select>";
echo "</div>";
echo "<div style='margin: 15px 0;'>";
echo "<label><strong>Contenido:</strong></label><br>";
echo "<textarea name='contenido' rows='4' cols='50' required style='padding: 8px; border: 1px solid #ccc; border-radius: 3px;'>Mesa 1\nProducto de prueba\nCantidad: 1\nTotal: $10.000</textarea>";
echo "</div>";
echo "<button type='submit' name='test_simple' style='background-color: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold;'>🖨️ Probar Impresión Simple</button>";
echo "</form>";

// Test pedido
echo "<form method='POST' style='background-color: #e7f3ff; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>🧾 Test Pedido Completo</h4>";
echo "<p>Imprime un pedido de ejemplo en las 3 impresoras automáticamente</p>";
echo "<button type='submit' name='test_pedido' style='background-color: #28a745; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold;'>🧾 Probar Pedido Completo</button>";
echo "</form>";

// Test conectividad
echo "<form method='POST' style='background-color: #fff3cd; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>🔍 Test Conectividad</h4>";
echo "<p>Verifica el estado de todas las impresoras</p>";
echo "<button type='submit' name='test_conectividad' style='background-color: #ffc107; color: #212529; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold;'>🔍 Probar Conectividad</button>";
echo "</form>";

// Información de integración
echo "<h2>🔧 Integración en tu Sistema:</h2>";
echo "<div style='background-color: #e7f3ff; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>📋 Para integrar en tu sistema existente:</h4>";
echo "<ol>";
echo "<li><strong>Incluir la clase:</strong> <code>require_once 'models/crudImpresionDirecta.php';</code></li>";
echo "<li><strong>En el envío de pedidos:</strong> Usar <code>ImpresionDirecta::imprimirPedido()</code></li>";
echo "<li><strong>Para impresiones específicas:</strong> Usar <code>ImpresionDirecta::imprimir()</code></li>";
echo "<li><strong>Para verificar estado:</strong> Usar <code>ImpresionDirecta::testConectividad()</code></li>";
echo "</ol>";

echo "<h4>✅ Ventajas:</h4>";
echo "<ul>";
echo "<li>🚀 <strong>Rápido:</strong> Conexión directa sin proxy</li>";
echo "<li>🔒 <strong>Confiable:</strong> Sin dependencias externas</li>";
echo "<li>📱 <strong>Compatible:</strong> Funciona desde móviles y PC</li>";
echo "<li>⚡ <strong>Eficiente:</strong> Sin verificaciones innecesarias</li>";
echo "</ul>";
echo "</div>";

// Botones de navegación
echo "<p style='margin-top: 30px;'>";
echo "<a href='test_impresion_directo.php' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>⚡ Test Directo</a>";
echo "<a href='test_impresion_completo.php' style='background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>← Test Completo</a>";
echo "<a href='index.php?action=registroPmesa&ida=1' style='background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🪑 Mesa Real</a>";
echo "</p>";
?>
