<?php
require_once "../../models/crud.php";
require_once "../../models/crudSuministro.php";
require_once "../../controllers/controller.php";
require_once "../../controllers/controllerSuministro.php";
	ini_set("session.cookie_lifetime","28800");
	ini_set("session.gc_maxlifetime","28800");
//echo'<br> <script>alert("Entro al ajax Buscar Suministros '.$_POST['placa'].'");</script> <br>';
if(isset($_POST['zona']))
	{
	 $zona = $_POST['zona'];
	 $cont=0;
	 //echo'<br> <script>alert("Entro al ajax");</script> <br>';
	 $ajax=new MvcController();
	 $r=$ajax->mesasController();
		 echo'
		 	<form method="post"  >
				<table  class="table table-hover">
			 	 <thead >
		 			<tr >
		 			 <th>No</th><th>Ubicación</th><th>Nombre</th> <th>Estado</th>
		 			</tr>
			 	 </thead>
			 	 <tbody>';
		 foreach ($r as $row => $item)
			{
			 $cont++;
			 echo'<tr >
			 		<td>'.$cont.'</td>
			 		<td>'.$item['descripcion'].'</td>
			 		<td>'.$item['nombre'].'</td>
			 		<td>'.$item['estado'].'</td>
			 	</tr>';
			}
			 echo'</tbody>
			 		<thead>
			 			<tr><td colspan="4"></td></tr>
			 		</thead>

					 	</table></form>';

	 }
?>