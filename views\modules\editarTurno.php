<style>
        .tabla-ventas {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-family: Arial, sans-serif;
        }

        .tabla-ventas th,
        .tabla-ventas td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }

        .tabla-ventas th {
            background-color: #f2f2f2 !important;
            font-weight: bold;
        }

        .tabla-ventas tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        .tabla-ventas tr:hover {
            background-color: #f5f5f5;
        }

        .texto-derecha {
            text-align: right;
        }

        .texto-centro {
            text-align: center;
        }

        .categoria-bar {
            background-color: #e8f5e8;
        }

        .categoria-cocina {
            background-color: #fff3e0;
        }

        .categoria-asados {
            background-color: #fce4ec;
        }

        .total-row {
            background-color: #e3f2fd;
            font-weight: bold;
        }

        .titulo {
            text-align: center;
            color: #333;
            margin-bottom: 10px;
        }

        .sin-datos {
            text-align: center;
            color: #666;
            font-style: italic;
        }
    </style>
<?php
 //session_start();
 if(!$_SESSION["validar"])
	{	header("location:index.php?action=ingresar");
		exit();
	}
?>
<h1>CERRAR TURNO</h1>
<form method="post">
	<?php
		//codeium puedes explicarme el siguiente codigodigo
		$editarU = new controllerTurno();
		$editarU -> consultarTurnoController();
		$editarU -> cerrarTurnoController();
	?>
</form>



