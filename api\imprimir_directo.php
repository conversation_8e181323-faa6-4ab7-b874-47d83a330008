<?php
// API de impresión directa - Sin verificaciones, máxima eficiencia
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Método no permitido']);
    exit;
}

// Configuración fija de impresoras
$IMPRESORAS = [
    // CONFIGURACIÓN TEMPORAL PARA PRUEBAS (Red 192.168.18.x)
    'test' => ['ip' => '*************', 'puerto' => 9100],

    // CONFIGURACIÓN PRODUCCIÓN (Red 192.168.68.x)
    'bar' => ['ip' => '**************', 'puerto' => 9100],
    'cocina' => ['ip' => '**************', 'puerto' => 9100],
    'asados' => ['ip' => '**************', 'puerto' => 9100]
];

// Obtener datos
$input = file_get_contents('php://input');
$data = json_decode($input, true);

if (!$data || !isset($data['categoria']) || !isset($data['contenido'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Faltan parámetros']);
    exit;
}

$categoria = strtolower($data['categoria']);
$contenido = $data['contenido'];

// Verificar que la categoría existe
if (!isset($IMPRESORAS[$categoria])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => "Categoría '$categoria' no válida"]);
    exit;
}

$impresora = $IMPRESORAS[$categoria];
$ip = $impresora['ip'];
$puerto = $impresora['puerto'];

// Función de impresión directa optimizada
function imprimirDirecto($ip, $puerto, $contenido, $categoria) {
    try {
        // Conexión rápida sin timeout largo
        $socket = @fsockopen($ip, $puerto, $errno, $errstr, 3);
        
        if (!$socket) {
            return ['success' => false, 'error' => "No conecta a $ip:$puerto - $errstr"];
        }
        
        // Comandos ESC/POS optimizados
        $datos = "\x1B\x40"; // Reset impresora
        $datos .= "\x1B\x61\x01"; // Centrar texto
        $datos .= "=== MACARENA ===\n";
        $datos .= "\x1B\x61\x00"; // Alinear izquierda
        $datos .= strtoupper($categoria) . " - " . date('H:i:s') . "\n";
        $datos .= "--------------------------------\n";
        $datos .= $contenido;
        $datos .= "\n--------------------------------\n\n\n";
        $datos .= "\x1D\x56\x42\x00"; // Cortar papel
        
        // Enviar datos
        $bytes_enviados = fwrite($socket, $datos);
        fclose($socket);
        
        if ($bytes_enviados > 0) {
            return [
                'success' => true, 
                'mensaje' => "Impreso en $categoria",
                'bytes' => $bytes_enviados,
                'ip' => $ip
            ];
        } else {
            return ['success' => false, 'error' => 'No se pudieron enviar datos'];
        }
        
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// Ejecutar impresión
$resultado = imprimirDirecto($ip, $puerto, $contenido, $categoria);

// Respuesta
if ($resultado['success']) {
    echo json_encode([
        'success' => true,
        'categoria' => $categoria,
        'ip' => $resultado['ip'],
        'bytes_enviados' => $resultado['bytes'],
        'timestamp' => date('Y-m-d H:i:s')
    ]);
} else {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'categoria' => $categoria,
        'ip' => $ip,
        'error' => $resultado['error']
    ]);
}
?>
