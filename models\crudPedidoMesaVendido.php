<?php
	/*	date_default_timezone_set("America/Bogota");
		 $fecha_creado=strftime("%Y-%m-%d %H:%M:%S");
		 $stmt = Conexion::conectar();
		 try
			{
			 $stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
			 $stmt->beginTransaction();
			 $consultar="INSERT INTO $tabla (nombre) VALUES ('".$datosModel["nombre"]."')";
			echo "<br>".$consultar."<br>";
				$stmt->exec($consultar);
				$idTablatura = $stmt->lastInsertId();	//ultimo id
				$cambio=$idTablatura.'-'.$datosModel["nombre"];
				$consultarA="INSERT INTO aditoria(tabla, accion, persona_id, fecha_accion, cambio)
				VALUES ('".$tabla."', 'INSERT', ".$_SESSION["usuario"].", '$fecha_creado', '$cambio')";
				echo "<br>".$consultarA."<br>";
				$stmt->exec($consultarA);
				/*$stmt->commit();
				//echo "<script>alert('fin de cruD try que pasa');</script>";
				return "success";
				$stmt->close();
			 }
			catch (Exception $e)
			 {
			 	echo "<script>alert('catch error');</script>";
			 	$stmt->rollBack();
				print "Error!: ".$e->getMessage()."</br>";
				return "Error!: ".$e->getMessage()."</br>";
			 }
	 */
require_once "conexion.php";
//require_once "crudSuministro.php";
class DatosPedidoMesaVendido extends Conexion
{
	#BUSCAR PRODUCTO
	#------------------------------
	 public static function buscarClienteMesaModel($datosModel, $tabla)
		{
		 	$consulta = "SELECT id, nombre, cupo, descripcion, estado FROM $tabla WHERE id=:mesa";
		 	//echo "<script>alert('Busqueda del Producto ".$consulta." ')</script>";
		 	$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->bindParam(":mesa", $datosModel, PDO::PARAM_STR);
			$stmt->execute();
			$r = $stmt->fetch();
			//echo "<script>alert('Producto id  --".$datosModel["id"]."')</script>";
			$c=$stmt->rowCount();
			//echo '<br> C2='.$c2.'<br>';
		 if ($c>0)
			{	return $r;	}
		 else
			{	return 0;	}
			$stmt->close();
		}
	#----------------------------------
	#VISTA MESA por ID
	#-------------------------------------
	 public static function vistaMesaIdModel($id,$tabla)
		{
			$stmt = Conexion::conectar()->prepare("SELECT id, nombre, cupo, descripcion, estado  FROM $tabla WHERE id=$id ");
			$stmt->execute();
			return $stmt->fetch();
			$stmt->close();
		}
	#--------------------------------
	#BUSCAR DESCUENTO
	#------------------------------
		public static function buscarDescuento($datosModel, $tabla)
		 {
		 	$consulta = "SELECT id, persona_id, porcentaje, codigo, Activo, fechaM FROM $tabla WHERE Activo='si' AND codigo=:codigo";
		 	//echo "<script>alert('Busqueda del Producto ".$consulta." ')</script>";
		 	$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->bindParam(":codigo", $datosModel, PDO::PARAM_STR);
			$stmt->execute();
			$r = $stmt->fetch();
			//echo "<script>alert('Producto id  --".$datosModel["id"]."')</script>";
				$c=$stmt->rowCount();
				//echo '<br> C2='.$c2.'<br>';
				if ($c>0)
					{	return $r;	}
				else
					{	return 0;	}
				$stmt->close();
		 }
	#--------------------------------
	#BUSCAR PRODUCTO
	#------------------------------
		public static function buscarProducto($datosModel, $tabla)
		 {
		 	$consulta = "SELECT * FROM productos WHERE codigo=:codigo";
		 	//echo "<script>alert('Busqueda del Producto ".$consulta." ')</script>";
		 	$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->bindParam(":codigo", $datosModel, PDO::PARAM_INT);
			$stmt->execute();
			$r = $stmt->fetch();
			//echo "<script>alert('Producto id  --".$datosModel["id"]."')</script>";
				$c=$stmt->rowCount();
				//echo '<br> C2='.$c2.'<br>';
				if ($c>0)
					{	return $r;	}
				else
					{	return 0;	}
				$stmt->close();
		 }
	#--------------------------------
	#REGISTRO DE PEDIDO MESA
	#-------------------------------------
	 public static function registroPmesaModel($datosModel, $tabla)
		{		//echo "<script>alert('Entro CRUD  --".$datosModel["codigo"]."  --".$datosModel["mesas_id"]." --".$datosModel["mesero"]." --".$datosModel["cantidad"]."')</script>";
			date_default_timezone_set("America/Bogota");
			$fecha_in=strftime("%Y-%m-%d %H:%M:%S");
			$producto = DatosPedidoMesaVendido::buscarProducto($datosModel["codigo"], "productos");
			$descuento = DatosPedidoMesaVendido::buscarDescuento($datosModel["descuento"], "descuento");
		 if ($descuento==0)
			{
				$valorD=0;
				$codigoD=0;
				echo "<script>alert('No existe este Codigo : ".$datosModel["codigo"]."  o Esta desactivado')</script>";
			}
		 else
		 	{
				$valorD=$descuento["porcentaje"];
				$codigoD=$datosModel["descuento"];

			}
		 if ($producto ==0)
			{	//echo "<script>alert('No existe este producto : ".$datosModel["codigo"]." ')</script>";
				return "error";
			}
		 else
			{
			 $stmt = Conexion::conectar();
			 try
				{
				 $stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
				 $stmt->beginTransaction();

				 // Verificar que tenemos un pedido_id válido
				 if (!isset($datosModel["pedido_id"]) || empty($datosModel["pedido_id"])) {
				 	throw new Exception("No se puede agregar producto sin un pedido válido");
				 }

				 $pedidoId = $datosModel["pedido_id"];

				 // SOLUCIÓN PERMANENTE: Usar INSERT ... ON DUPLICATE KEY UPDATE
				 $consultar="INSERT INTO $tabla(productos_id, mesas_id, pedidos_id, cantidad, fecha_hora, mesero, descuento, codigo_descuento, nota, cocina)
				            VALUES (".$producto["id"].", ".$datosModel["mesas_id"].", ".$pedidoId.", ".$datosModel["cantidad"].", '$fecha_in', ".$datosModel["mesero"].", ".$valorD.", '".$codigoD."', '".$datosModel["nota"]."', '".$producto["cocina"]."')
				            ON DUPLICATE KEY UPDATE
				            cantidad = cantidad + VALUES(cantidad),
				            fecha_hora = VALUES(fecha_hora),
				            mesero = VALUES(mesero),
				            descuento = VALUES(descuento),
				            codigo_descuento = VALUES(codigo_descuento),
				            nota = VALUES(nota),
				            cocina = VALUES(cocina)";

			 error_log("registroPmesaModel: Ejecutando consulta con ON DUPLICATE KEY UPDATE");
			 //echo "<br>".$consultar."<br>";
			 //echo "<script>alert('Entro CRUD  --".$consultar." ')</script>";
			 $stmt->exec($consultar);
			 $consultar1 = "UPDATE mesas SET descripcion = '".$datosModel["cliente"]."' WHERE  id = ".$datosModel["mesas_id"];
			 //$ultimo_id=0;	 $ultimo_id=$stmt->lastInsertId();
			 //echo "<br>".$consultar1."<br>";
			 $stmt->exec($consultar1);
			 echo "<script>alert('Guardo')</script>";
			 $stmt->commit();
			return "success";
				$stmt->close();
			 }
			catch (Exception $e)
			 {
			 	echo "<script>alert('catch error');</script>";
			 	$stmt->rollBack();
				print "Error!: ".$e->getMessage()."</br>";
				return "Error!: ".$e->getMessage()."</br>";
			 }
			}

		}
	#-------------------------------------

	#VISTA PEDIDO MESA  pr.id, pr.nombre,pr.precio, pvm.cantidad, pvm.fecha_hora
	#-------------------------------------
		public static function vistaPmesaModel($idmesa,$tabla)
		 {
			//echo "<script>alert('Entro CRUD mesa ".$idmesa." ')</script>";
			// CORREGIDO: Mostrar solo productos de pedidos borrador para que la mesa se "desocupe"
			$consulta = "SELECT pr.id AS idpr, pr.nombre AS nombrepr, pr.precio AS preciopr, pr.codigo AS prcodigo,
						pvm.cantidad AS cantidadpvm, pvm.fecha_hora AS fechapvm, pvm.mesas_id AS idmesa,
						pvm.mesero AS mesero, pvm.descuento AS descuentopvm, pvm.codigo_descuento AS pvmcodigo_descuento,
						p.numero_pedido, p.estado
						FROM productos pr
						JOIN producto_vendido_mesa pvm ON pr.id = pvm.productos_id
						JOIN pedidos p ON pvm.pedidos_id = p.id
						WHERE pvm.mesas_id = :idmesa AND p.estado = 'borrador'
						ORDER BY pvm.fecha_hora DESC";
			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->bindParam(":idmesa", $idmesa, PDO::PARAM_INT);
			//echo "---".$consulta;
			$stmt->execute();
			return $stmt->fetchAll();
			$stmt->close();
		 }
	#-------------------------------------
	#EDITAR PEDIDO MESA
	#-------------------------------------
	 public static function editarPmesaModel($datosModel, $tabla)
		{	//echo "<script>alert('entro cruz Suministro ".$datosModel."')</script>";
			$consultar="SELECT productos_id, mesas_id, cantidad, fecha_hora, nota FROM $tabla WHERE fecha_hora = :fecha_hora";
			$stmt = Conexion::conectar()->prepare($consultar);
			$stmt->bindParam(":fecha_hora", $datosModel, PDO::PARAM_STR);
			$stmt->execute();
			//echo "<br>".$consultar."<br>";
			return $stmt->fetch();
			$stmt->close();
		}
	#-------------------------------------
	#ADD PEDIDO MESA VENDIDO
	#-------------------------------------
	 public static function addPmesaModel($datosModel, $tabla)
		{
			error_log("addPmesaModel: Datos recibidos: " . print_r($datosModel, true));

			$mesas_id = $datosModel['mesa_id'];
			$productos_id = $datosModel['producto_id'];
			$pedido_id = isset($datosModel['pedido_id']) ? $datosModel['pedido_id'] : null;
			$fecha_hora = $datosModel['fecha_hora'];

			if (!$pedido_id) {
				error_log("addPmesaModel: Error - pedido_id no proporcionado");
				return "error";
			}

			// SOLUCIÓN PERMANENTE: Usar INSERT ... ON DUPLICATE KEY UPDATE
			// Esto elimina la necesidad de verificar existencia y evita errores de clave duplicada
			$consultar = "INSERT INTO $tabla (productos_id, mesas_id, pedidos_id, cantidad, fecha_hora, mesero)
			             VALUES (?, ?, ?, 1, ?, ?)
			             ON DUPLICATE KEY UPDATE
			             cantidad = cantidad + 1,
			             fecha_hora = VALUES(fecha_hora),
			             mesero = VALUES(mesero)";

			$stmt = Conexion::conectar()->prepare($consultar);
			$mesero = isset($_SESSION["usuario"]) ? $_SESSION["usuario"] : 1;
			$resultado = $stmt->execute([$productos_id, $mesas_id, $pedido_id, $fecha_hora, $mesero]);
			error_log("addPmesaModel: Usando INSERT ... ON DUPLICATE KEY UPDATE");

			if($resultado) {
				error_log("addPmesaModel: Éxito");
				return "success";
			} else {
				error_log("addPmesaModel: Error en la consulta");
				return "error";
			}
		}
	#--------------------------------------------
	#ADD Cantidad Producto PEDIDO MESA VENDIDO
	#--------------------------------------------
	 public static function addCantidadPmesaModel($datosModel, $tabla)
		{	//echo "<script>alert('entro cruz Suministro ".$datosModel."')</script>";

			$mesas_id=$datosModel['mesa_id'];
			$productos_id=$datosModel['producto_id'];

			$consultar="UPDATE $tabla SET cantidad= cantidad+1  WHERE fecha_hora = :fecha_hora and mesas_id=$mesas_id and productos_id=$productos_id";

			$stmt = Conexion::conectar()->prepare($consultar);
			$stmt->bindParam(":fecha_hora", $datosModel['fecha_hora'], PDO::PARAM_STR);
			
			//echo "<br>".$consultar."<br>";
			if($stmt->execute())
			{
				return "success";

			}else{return "error";}

			$stmt->close();
		}
	#-------------------------------------
	#Disminuir PEDIDO MESA VENDIDO
	#-------------------------------------
	 public static function disminuirPmesaModel($datosModel, $tabla)
		{	//echo "<script>alert('entro cruz Suministro ".$datosModel."')</script>";

			$mesas_id=$datosModel['mesa_id'];
			$productos_id=$datosModel['producto_id'];

			$consultar="UPDATE $tabla SET cantidad= cantidad-1  WHERE fecha_hora = :fecha_hora and mesas_id=$mesas_id and productos_id=$productos_id";

			$stmt = Conexion::conectar()->prepare($consultar);
			$stmt->bindParam(":fecha_hora", $datosModel['fecha_hora'], PDO::PARAM_STR);
			
			//echo "<br>".$consultar."<br>";
			if($stmt->execute())
			{
				return "success";

			}else{return "error";}

			$stmt->close();
		}
	#-------------------------------------
	#ACTUALIZAR PEDIDO MESA productos_id, mesas_id, cantidad, fecha_hora
	#-------------------------------------
		public static function actualizaPmesaModel($estado, $datosModel, $tabla)
			{ //echo "<script>alert('entro cruz Suministro')</script>";
				$consulta = "UPDATE $tabla SET estado = :estado, mesero=:mesero WHERE id = :mesas_id";
				//echo "<script>alert('entro cruz Suministro ".$consulta."')</script>";
				$stmt = Conexion::conectar()->prepare($consulta);
				$stmt->bindParam(":mesas_id", $datosModel, PDO::PARAM_INT);
				$stmt->bindParam(":estado", $estado, PDO::PARAM_STR);
				$stmt->bindParam(":mesero", $_SESSION["usuario"], PDO::PARAM_INT);
				//echo "<script>alert('centro proceso')</script>";
				if($stmt->execute())
					{echo "<script>alert('Guardo Actualizar Proveedor')</script>";return "success";	}
				else{echo "<script>alert('Error base de dato serve')</script>";	return "error";			}
				$stmt->close();
			}


	#ACTUALIZAR PEDIDO MESA productos_id, mesas_id, cantidad, fecha_hora
	#-------------------------------------
		public static function actualizarPmesaModel($datosModel, $tabla)
			{
				//echo "<script>alert('entro cruz Suministro')</script>";
				$consulta = "UPDATE $tabla SET productos_id = :productos_id, mesas_id= :mesas_id, cantidad =:cantidad, fecha_hora = :fecha_hora, nota = :nota WHERE productos_id = :productos_id and mesas_id = :mesas_id";
				//echo "<script>alert('entro cruz Suministro ".$consulta."')</script>";
				$stmt = Conexion::conectar()->prepare($consulta);
				$stmt->bindParam(":productos_id", $datosModel["productos_id"], PDO::PARAM_INT);
				$stmt->bindParam(":mesas_id", $datosModel["mesas_id"], PDO::PARAM_INT);
				$stmt->bindParam(":cantidad", $datosModel["cantidad"], PDO::PARAM_INT);
				$stmt->bindParam(":fecha_hora", $datosModel["fecha_hora"], PDO::PARAM_STR);
				$stmt->bindParam(":nota", $datosModel["nota"], PDO::PARAM_STR);
				//echo "<script>alert('centro proceso')</script>";
				if($stmt->execute())
					{echo "<script>alert('Guardo Actualizar Proveedor')</script>";return "success";	}
				else{echo "<script>alert('Error base de dato serve')</script>";	return "error";			}
				$stmt->close();
			}


	#BORRAR PEDIDO MESA
	#------------------------------------
	 public static function borrarPmesaModel($datosModel, $tabla)
		{ 	//echo "<script>alert('Entro CRUD ".$datosModel["productos_id"]." es')</script>";
			$producto=$datosModel["productos_id"];
			$fecha=$datosModel["fecha_hora"];
			$mesa=$datosModel["mesa"];
			$stmt = Conexion::conectar();
	 	 try
 			{
 			 $stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
			 $stmt->beginTransaction();

			 // CORREGIDO: Bloquear eliminación de productos de pedidos enviados/entregados
			 // Primero obtener el pedido_id del producto que se va a eliminar
			 $obtenerPedidoId = "SELECT pedidos_id FROM producto_vendido_mesa
			                    WHERE productos_id = $producto AND fecha_hora = '$fecha'";
			 error_log("ELIMINACIÓN: Consulta pedido_id: $obtenerPedidoId");
			 $resultadoPedidoId = $stmt->query($obtenerPedidoId);
			 $pedidoInfo = $resultadoPedidoId->fetch();
			 error_log("ELIMINACIÓN: Pedido info: " . print_r($pedidoInfo, true));

			 if ($pedidoInfo) {
			     // Verificar el estado del pedido
			     $verificarEstado = "SELECT estado FROM pedidos WHERE id = " . $pedidoInfo['pedidos_id'];
			     error_log("ELIMINACIÓN: Consulta estado: $verificarEstado");
			     $resultadoEstado = $stmt->query($verificarEstado);
			     $estadoPedido = $resultadoEstado->fetch();
			     error_log("ELIMINACIÓN: Estado pedido: " . print_r($estadoPedido, true));

			     if ($estadoPedido && $estadoPedido['estado'] !== 'borrador') {
			         $mensaje = "❌ No se pueden eliminar productos de pedidos en estado: " . $estadoPedido['estado'] . ". Solo se pueden eliminar productos de pedidos en borrador.";
			         error_log("ELIMINACIÓN: BLOQUEADO - $mensaje");
			         throw new Exception($mensaje);
			     } else {
			         error_log("ELIMINACIÓN: ✅ Producto puede ser eliminado (estado: " . ($estadoPedido ? $estadoPedido['estado'] : 'no encontrado') . ")");
			     }
			 } else {
			     error_log("ELIMINACIÓN: ⚠️ No se encontró información del pedido para el producto");
			     // Si no se encuentra el pedido, permitir eliminación (puede ser un producto huérfano)
			 }

			 $consulta = "DELETE FROM $tabla WHERE productos_id=$producto AND fecha_hora = '$fecha'";
		 	 echo "<br>".$consulta."<br>";
		 	 echo "<br> mesa:".$mesa." producto: ".$producto."<br>";
		 	 $stmt ->exec($consulta);
		 	 $consultaC = "DELETE FROM cocina where producto_id=$producto AND mesa_id =$mesa";
		 	 echo "<br>".$consultaC."<br>";
			 $stmt->exec($consultaC);/**/
			 $stmt->commit();
			 //echo "<script>alert('fin de cruD try que pasa');</script>";
			 return "success";
			 $stmt->close();
 	    	}
	 	 catch (PDOException $e)
 	 		{
		 	 echo "<script>alert('catch entro')</script>";
			 $stmt->rollBack();
			 print "¡Error!: " . $e->getMessage() . "<br/>";
			 die();
			}
		}
	#----------------------------------------------
	/*=============================================
	CREAR NUEVO PEDIDO CON MESA
	=============================================*/
	static public function crearNuevoPedidoModel($meseroId, $mesaId, $cedulaCliente) {
		$stmt = Conexion::conectar()->prepare("
			INSERT INTO pedidos (mesero_id, mesa_id, facturado, cedula_cliente, fecha_pedido) 
			VALUES (?, ?, 'n', ?, NOW())
		");
		$stmt->bindParam(1, $meseroId, PDO::PARAM_INT);
		$stmt->bindParam(2, $mesaId, PDO::PARAM_INT);
		$stmt->bindParam(3, $cedulaCliente, PDO::PARAM_STR);
		
		if($stmt->execute()) {
			return Conexion::conectar()->lastInsertId();
		} else {
			return "error";
		}
		$stmt->close();
		$stmt = null;
	}

	/*=============================================
	OBTENER PEDIDOS ACTIVOS DE UNA MESA
	=============================================*/
	static public function obtenerPedidosActivosMesaModel($mesaId) {
		$stmt = Conexion::conectar()->prepare("
			SELECT id, mesero_id, fecha_pedido
			FROM pedidos
			WHERE mesa_id = ? AND facturado = 'n'
			ORDER BY fecha_pedido DESC
		");
		$stmt->bindParam(1, $mesaId, PDO::PARAM_INT);
		$stmt->execute();
		return $stmt->fetchAll();
		$stmt->close();
		$stmt = null;
	}

	/*=============================================
	OBTENER PRODUCTOS PARA FACTURACIÓN (TODOS LOS ESTADOS EXCEPTO CANCELADO)
	=============================================*/
	static public function obtenerProductosEntregadosMesaModel($mesaId) {
		// Incluir todos los estados excepto cancelado para facturación completa
		$consulta = "SELECT pr.id AS idpr, pr.nombre AS nombrepr, pr.precio AS preciopr, pr.codigo AS prcodigo,
					pvm.cantidad AS cantidadpvm, pvm.fecha_hora AS fechapvm, pvm.mesas_id AS idmesa,
					pvm.mesero AS mesero, pvm.descuento AS descuentopvm, pvm.codigo_descuento AS pvmcodigo_descuento,
					p.numero_pedido, p.estado, p.id as pedido_id
					FROM productos pr
					JOIN producto_vendido_mesa pvm ON pr.id = pvm.productos_id
					JOIN pedidos p ON pvm.pedidos_id = p.id
					WHERE pvm.mesas_id = :idmesa AND p.estado NOT IN ('cancelado')
					ORDER BY p.fecha_pedido DESC, pvm.fecha_hora DESC";
		$stmt = Conexion::conectar()->prepare($consulta);
		$stmt->bindParam(":idmesa", $mesaId, PDO::PARAM_INT);
		$stmt->execute();
		return $stmt->fetchAll();
		$stmt->close();
	}
}
