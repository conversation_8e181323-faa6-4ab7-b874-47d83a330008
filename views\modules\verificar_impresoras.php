<?php
// Verificar estado de la tabla impresoras
require_once "../../models/conexion.php";

echo "<h1>🖨️ Verificación de Tabla Impresoras</h1>";

try {
    $db = Conexion::conectar();
    
    // Verificar si la tabla existe
    echo "<h3>1. Verificando existencia de tabla 'impresoras':</h3>";
    $stmt = $db->prepare("SHOW TABLES LIKE 'impresoras'");
    $stmt->execute();
    $tablaExiste = $stmt->fetch();
    
    if ($tablaExiste) {
        echo "✅ La tabla 'impresoras' existe<br>";
        
        // Verificar estructura
        echo "<h3>2. Estructura de la tabla:</h3>";
        $stmt = $db->prepare("DESCRIBE impresoras");
        $stmt->execute();
        $columnas = $stmt->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Clave</th><th>Default</th></tr>";
        foreach ($columnas as $col) {
            echo "<tr>";
            echo "<td>" . $col['Field'] . "</td>";
            echo "<td>" . $col['Type'] . "</td>";
            echo "<td>" . $col['Null'] . "</td>";
            echo "<td>" . $col['Key'] . "</td>";
            echo "<td>" . $col['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Verificar datos
        echo "<h3>3. Datos en la tabla:</h3>";
        $stmt = $db->prepare("SELECT * FROM impresoras");
        $stmt->execute();
        $impresoras = $stmt->fetchAll();
        
        if (!empty($impresoras)) {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>Categoría</th><th>IP</th><th>Puerto</th><th>Activa</th></tr>";
            foreach ($impresoras as $imp) {
                echo "<tr>";
                echo "<td>" . (isset($imp['id']) ? $imp['id'] : 'N/A') . "</td>";
                echo "<td>" . (isset($imp['categoria']) ? $imp['categoria'] : 'N/A') . "</td>";
                echo "<td>" . (isset($imp['ip']) ? $imp['ip'] : 'N/A') . "</td>";
                echo "<td>" . (isset($imp['puerto']) ? $imp['puerto'] : 'N/A') . "</td>";
                echo "<td>" . (isset($imp['activa']) ? ($imp['activa'] ? 'Sí' : 'No') : 'N/A') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "⚠️ La tabla está vacía. Creando datos de ejemplo...<br>";
            
            // Crear datos de ejemplo
            $stmt = $db->prepare("
                INSERT INTO impresoras (categoria, ip, puerto, activa) VALUES 
                ('cocina', '*************', 9100, 1),
                ('bar', '*************', 9100, 1),
                ('postres', '*************', 9100, 1)
            ");
            
            if ($stmt->execute()) {
                echo "✅ Datos de ejemplo creados exitosamente<br>";
            } else {
                echo "❌ Error creando datos de ejemplo<br>";
            }
        }
        
    } else {
        echo "❌ La tabla 'impresoras' NO existe. Creándola...<br>";
        
        // Crear tabla
        $sql = "
            CREATE TABLE impresoras (
                id INT AUTO_INCREMENT PRIMARY KEY,
                categoria VARCHAR(50) NOT NULL,
                ip VARCHAR(15) NOT NULL,
                puerto INT DEFAULT 9100,
                activa BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ";
        
        if ($db->exec($sql)) {
            echo "✅ Tabla 'impresoras' creada exitosamente<br>";
            
            // Insertar datos de ejemplo
            $stmt = $db->prepare("
                INSERT INTO impresoras (categoria, ip, puerto, activa) VALUES 
                ('cocina', '*************', 9100, 1),
                ('bar', '*************', 9100, 1),
                ('postres', '*************', 9100, 1)
            ");
            
            if ($stmt->execute()) {
                echo "✅ Datos de ejemplo insertados<br>";
            }
        } else {
            echo "❌ Error creando la tabla<br>";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage();
}

echo "<br><a href='test_envio_simple.php' style='background: #28a745; color: white; padding: 10px; text-decoration: none;'>🔙 Volver al Test</a>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}

table {
    margin: 10px 0;
    width: 100%;
    max-width: 800px;
}

th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #f0f0f0;
}
</style>
