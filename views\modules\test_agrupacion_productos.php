<?php
session_start();

echo "<h2>🧪 Test Agrupación de Productos - Corrección Facturación</h2>";

// Incluir archivos necesarios
require_once "../../models/conexion.php";
require_once "../../models/crudFacturaAja.php";

$mesas_test = [47, 2, 10]; // Mesas para probar

foreach ($mesas_test as $mesa_id) {
    echo "<div style='border: 2px solid #28a745; margin: 20px 0; padding: 15px; border-radius: 8px;'>";
    echo "<h3>📋 Mesa $mesa_id - Comparación Antes vs Después</h3>";
    
    try {
        // 1. Método ANTERIOR (sin agrupar)
        echo "<h4>❌ Método ANTERIOR (Sin Agrupar)</h4>";
        $stmt = Conexion::conectar();
        $consulta_anterior = "SELECT pr.id AS idpr, pr.nombre AS nombrepr, pr.precio AS preciopr, pr.codigo AS prcodigo,
                    pvm.cantidad AS cantidadpvm, pvm.fecha_hora AS fechapvm, pvm.mesas_id AS idmesa,
                    pvm.mesero AS mesero, pvm.descuento AS descuentopvm, pvm.codigo_descuento AS pvmcodigo_descuento,
                    p.numero_pedido, p.estado
                    FROM productos pr
                    JOIN producto_vendido_mesa pvm ON pr.id = pvm.productos_id
                    JOIN pedidos p ON pvm.pedidos_id = p.id
                    WHERE pvm.mesas_id = :idmesa AND p.estado IN ('enviado', 'entregado')
                    ORDER BY pvm.fecha_hora DESC";
        
        $stmt2 = $stmt->prepare($consulta_anterior);
        $stmt2->bindParam(":idmesa", $mesa_id, PDO::PARAM_INT);
        $stmt2->execute();
        $productos_anterior = $stmt2->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($productos_anterior)) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; font-size: 12px;'>";
            echo "<tr style='background: #ffcdd2;'>";
            echo "<th>Producto</th><th>Cantidad</th><th>Precio</th><th>Total</th><th>Pedido</th>";
            echo "</tr>";
            
            $total_anterior = 0;
            $registros_anterior = 0;
            
            foreach ($productos_anterior as $prod) {
                $descuento = ($prod["preciopr"] * ($prod["descuentopvm"] / 100));
                $precio_con_descuento = $prod["preciopr"] - $descuento;
                $subtotal = $precio_con_descuento * $prod["cantidadpvm"];
                $total_anterior += $subtotal;
                $registros_anterior++;
                
                echo "<tr>";
                echo "<td>{$prod['nombrepr']}</td>";
                echo "<td style='text-align: center;'>{$prod['cantidadpvm']}</td>";
                echo "<td style='text-align: right;'>$" . number_format($prod['preciopr']) . "</td>";
                echo "<td style='text-align: right;'>$" . number_format($subtotal) . "</td>";
                echo "<td>{$prod['numero_pedido']}</td>";
                echo "</tr>";
            }
            
            echo "<tr style='background: #ffeb3b; font-weight: bold;'>";
            echo "<td colspan='3'>TOTAL ANTERIOR ($registros_anterior registros)</td>";
            echo "<td style='text-align: right;'>$" . number_format($total_anterior) . "</td>";
            echo "<td></td>";
            echo "</tr>";
            echo "</table>";
        } else {
            echo "<p>❌ No hay productos para facturar en mesa $mesa_id</p>";
        }
        
        // 2. Método NUEVO (agrupado)
        echo "<h4>✅ Método NUEVO (Agrupado)</h4>";
        $productos_nuevo = DatosFacturaAja::obtenerProductosParaFacturar($mesa_id);
        
        if (!empty($productos_nuevo)) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; font-size: 12px;'>";
            echo "<tr style='background: #c8e6c9;'>";
            echo "<th>Producto</th><th>Cantidad Total</th><th>Precio</th><th>Total</th><th>Pedidos</th>";
            echo "</tr>";
            
            $total_nuevo = 0;
            $registros_nuevo = 0;
            
            foreach ($productos_nuevo as $prod) {
                $descuento = ($prod["preciopr"] * ($prod["descuentopvm"] / 100));
                $precio_con_descuento = $prod["preciopr"] - $descuento;
                $subtotal = $precio_con_descuento * $prod["cantidadpvm"];
                $total_nuevo += $subtotal;
                $registros_nuevo++;
                
                echo "<tr>";
                echo "<td>{$prod['nombrepr']}</td>";
                echo "<td style='text-align: center; background: #e8f5e8;'><strong>{$prod['cantidadpvm']}</strong></td>";
                echo "<td style='text-align: right;'>$" . number_format($prod['preciopr']) . "</td>";
                echo "<td style='text-align: right;'>$" . number_format($subtotal) . "</td>";
                echo "<td style='font-size: 10px;'>{$prod['numero_pedido']}</td>";
                echo "</tr>";
            }
            
            echo "<tr style='background: #4caf50; color: white; font-weight: bold;'>";
            echo "<td colspan='3'>TOTAL NUEVO ($registros_nuevo registros)</td>";
            echo "<td style='text-align: right;'>$" . number_format($total_nuevo) . "</td>";
            echo "<td></td>";
            echo "</tr>";
            echo "</table>";
        } else {
            echo "<p>❌ No hay productos para facturar en mesa $mesa_id</p>";
        }
        
        // 3. Comparación
        if (!empty($productos_anterior) && !empty($productos_nuevo)) {
            $diferencia_registros = $registros_anterior - $registros_nuevo;
            $diferencia_total = $total_anterior - $total_nuevo;
            
            echo "<h4>⚖️ Comparación</h4>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><td><strong>Registros Anterior</strong></td><td style='background: #ffcdd2;'>$registros_anterior</td></tr>";
            echo "<tr><td><strong>Registros Nuevo</strong></td><td style='background: #c8e6c9;'>$registros_nuevo</td></tr>";
            echo "<tr><td><strong>Reducción de registros</strong></td><td style='background: #fff3cd;'><strong>$diferencia_registros</strong></td></tr>";
            echo "<tr><td><strong>Total Anterior</strong></td><td style='background: #ffcdd2;'>$" . number_format($total_anterior) . "</td></tr>";
            echo "<tr><td><strong>Total Nuevo</strong></td><td style='background: #c8e6c9;'>$" . number_format($total_nuevo) . "</td></tr>";
            
            if ($diferencia_total == 0) {
                echo "<tr><td><strong>Diferencia</strong></td><td style='background: #4caf50; color: white;'><strong>✅ $0 (CORRECTO)</strong></td></tr>";
            } else {
                echo "<tr><td><strong>Diferencia</strong></td><td style='background: #f44336; color: white;'><strong>❌ $" . number_format($diferencia_total) . "</strong></td></tr>";
            }
            echo "</table>";
            
            if ($diferencia_registros > 0) {
                echo "<div style='background: #c8e6c9; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
                echo "<strong>✅ MEJORA DETECTADA:</strong><br>";
                echo "• Se redujeron <strong>$diferencia_registros registros duplicados</strong><br>";
                echo "• Los productos iguales ahora se agrupan correctamente<br>";
                echo "• El total se mantiene igual (sin pérdida de datos)<br>";
                echo "</div>";
            }
        }
        
    } catch (Exception $e) {
        echo "❌ Error procesando mesa $mesa_id: " . $e->getMessage() . "<br>";
    }
    
    echo "</div>";
}

// 4. Verificar facturas problemáticas específicas
echo "<div style='border: 2px solid #ff9800; margin: 20px 0; padding: 15px; border-radius: 8px;'>";
echo "<h3>🔍 Verificar Facturas Problemáticas 376 y 380</h3>";

$facturas_problema = [376, 380];

foreach ($facturas_problema as $factura_id) {
    echo "<h4>📄 Factura $factura_id</h4>";
    
    try {
        // Obtener datos de la venta
        $stmt = Conexion::conectar()->prepare("SELECT total FROM ventas WHERE id = ?");
        $stmt->bindParam(1, $factura_id, PDO::PARAM_INT);
        $stmt->execute();
        $venta = $stmt->fetch();
        
        if ($venta) {
            // Obtener productos facturados
            $stmt = Conexion::conectar()->prepare("
                SELECT COUNT(*) as total_registros, SUM(precio_total) as total_productos
                FROM producto_vendido 
                WHERE venta = ?
            ");
            $stmt->bindParam(1, $factura_id, PDO::PARAM_INT);
            $stmt->execute();
            $productos_info = $stmt->fetch();
            
            $diferencia = $venta['total'] - $productos_info['total_productos'];
            
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><td><strong>Total en ventas</strong></td><td>$" . number_format($venta['total']) . "</td></tr>";
            echo "<tr><td><strong>Total productos</strong></td><td>$" . number_format($productos_info['total_productos']) . "</td></tr>";
            echo "<tr><td><strong>Registros productos</strong></td><td>{$productos_info['total_registros']}</td></tr>";
            
            if ($diferencia == 0) {
                echo "<tr><td><strong>Estado</strong></td><td style='background: #4caf50; color: white;'>✅ CORRECTO</td></tr>";
            } else {
                echo "<tr><td><strong>Diferencia</strong></td><td style='background: #f44336; color: white;'>❌ $" . number_format($diferencia) . "</td></tr>";
            }
            echo "</table>";
        }
        
    } catch (Exception $e) {
        echo "❌ Error: " . $e->getMessage() . "<br>";
    }
}

echo "</div>";
?>

<style>
table {
    font-size: 12px;
    width: 100%;
}
th {
    background-color: #f5f5f5;
    padding: 8px;
    text-align: left;
}
td {
    padding: 6px;
    border: 1px solid #ddd;
}
</style>

<div style="margin: 20px 0; padding: 15px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px;">
    <h4>🔧 Solución Implementada</h4>
    <p><strong>Problema:</strong> Los productos iguales no se agrupaban, creando múltiples registros en lugar de sumar cantidades.</p>
    <p><strong>Solución:</strong> Modificado <code>obtenerProductosParaFacturar()</code> para usar <code>GROUP BY</code> y <code>SUM(cantidad)</code>.</p>
    <p><strong>Resultado:</strong> Ahora los productos iguales se agrupan correctamente, manteniendo el total pero reduciendo registros duplicados.</p>
</div>
