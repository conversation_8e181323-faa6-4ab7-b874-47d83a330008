<?php
// Versión debug del controllerFacturaAja para diagnosticar error 500
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once "../../models/crudFacturaAja.php";

class controllerFacturaAja_debug
{
    #---------------------------------------
    #REGISTRO DE FACTURA DEBUG
    ##------------------------------------
    public function facturaajaxController($efectivo, $nequi, $daviplata, $bancolombia, $tarjeta, $pago, $cedula, $propina, $mesa)
    {
        try {
            error_log("DEBUG CONTROLLER: Iniciando facturaajaxController");
            error_log("DEBUG CONTROLLER: Parámetros - Mesa: $mesa, Efectivo: $efectivo, Cedula: $cedula");
            
            session_start();
            
            // Verificar sesión
            if (!isset($_SESSION["usuario"])) {
                error_log("DEBUG CONTROLLER: ERROR - No hay usuario en sesión");
                throw new Exception("No hay usuario en sesión");
            }
            
            error_log("DEBUG CONTROLLER: Usuario en sesión: " . $_SESSION["usuario"]);
            
            $datosController = array(
                'usuario' => $_SESSION["usuario"],
                'efectivo' => $efectivo,
                'tarjeta' => $tarjeta,
                'nequi' => $nequi,
                'daviplata' => $daviplata,
                'bancolombia' => $bancolombia,
                'clienteCedula' => $cedula,
                'propina' => $propina,
                'mesa' => $mesa,
                'pago' => $pago
            );
            
            error_log("DEBUG CONTROLLER: Datos del controlador preparados");
            error_log("DEBUG CONTROLLER: " . print_r($datosController, true));
            
            // Verificar que la clase del modelo existe
            if (!class_exists('DatosFacturaAja')) {
                error_log("DEBUG CONTROLLER: ERROR - Clase DatosFacturaAja no existe");
                throw new Exception("Clase DatosFacturaAja no encontrada");
            }
            
            error_log("DEBUG CONTROLLER: Clase DatosFacturaAja encontrada");
            
            // Verificar que el método existe
            if (!method_exists('DatosFacturaAja', 'facturaajaxModel')) {
                error_log("DEBUG CONTROLLER: ERROR - Método facturaajaxModel no existe");
                throw new Exception("Método facturaajaxModel no encontrado");
            }
            
            error_log("DEBUG CONTROLLER: Método facturaajaxModel encontrado");
            error_log("DEBUG CONTROLLER: Llamando a facturaajaxModel con mesa: $mesa");
            
            // Llamada al modelo con manejo de errores
            $respuesta = DatosFacturaAja::facturaajaxModel($mesa, $datosController);
            
            error_log("DEBUG CONTROLLER: facturaajaxModel completado, respuesta: " . $respuesta);
            
            if ($respuesta == "success") {
                error_log("DEBUG CONTROLLER: Facturación exitosa");
                echo '<script>alert("DEBUG: Pago realizado exitosamente");window.open("pdf","_blank");location.href ="mesa";</script>';
                unset($_SESSION["clientep"]);
                return "success";
            } else {
                error_log("DEBUG CONTROLLER: Facturación falló, respuesta: " . $respuesta);
                echo '<script>alert("DEBUG: No se guardó, intente nuevamente");</script>';
                return "error";
            }
            
        } catch (Exception $e) {
            error_log("DEBUG CONTROLLER: EXCEPCIÓN CAPTURADA: " . $e->getMessage());
            error_log("DEBUG CONTROLLER: Stack trace: " . $e->getTraceAsString());
            echo '<script>alert("DEBUG ERROR: ' . addslashes($e->getMessage()) . '");</script>';
            throw $e; // Re-lanzar para que se capture en ajaxFactura
        } catch (Error $e) {
            error_log("DEBUG CONTROLLER: ERROR FATAL CAPTURADO: " . $e->getMessage());
            error_log("DEBUG CONTROLLER: Stack trace: " . $e->getTraceAsString());
            echo '<script>alert("DEBUG FATAL ERROR: ' . addslashes($e->getMessage()) . '");</script>';
            throw $e;
        }
    }
}
?>
