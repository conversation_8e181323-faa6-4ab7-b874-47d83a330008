<?php
// Test de facturación con los 5 métodos de pago
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
if (!isset($_SESSION["usuario"])) {
    $_SESSION["usuario"] = 1;
    $_SESSION["tipo_usuario"] = 1;
    $_SESSION["perfil"] = "administrador";
}

require_once "../../models/conexion.php";
require_once "../../controllers/controllerEstadoPedidos.php";
require_once "../../models/crudEstadoPedidos.php";

$mesaId = isset($_GET['mesa']) ? $_GET['mesa'] : 1;

echo "<h1>💰 Test Facturación 5 Métodos de Pago - Mesa $mesaId</h1>";

try {
    $controller = new ControllerEstadoPedidos();
    $db = Conexion::conectar();
    
    echo "<h3>1. 📋 Pedidos Disponibles para Facturar</h3>";
    
    // Buscar pedidos enviados/entregados
    $sql = "SELECT * FROM pedidos WHERE mesa_id = ? AND estado IN ('enviado', 'entregado') ORDER BY fecha_pedido DESC";
    $stmt = $db->prepare($sql);
    $stmt->bindParam(1, $mesaId, PDO::PARAM_INT);
    $stmt->execute();
    $pedidosFacturar = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($pedidosFacturar)) {
        echo "<div class='alert alert-success'>✅ Hay " . count($pedidosFacturar) . " pedido(s) disponible(s) para facturar</div>";
        
        foreach ($pedidosFacturar as $pedido) {
            // Contar productos
            $sqlProductos = "SELECT COUNT(*) as total FROM producto_vendido_mesa WHERE pedidos_id = ?";
            $stmtProductos = $db->prepare($sqlProductos);
            $stmtProductos->bindParam(1, $pedido['id'], PDO::PARAM_INT);
            $stmtProductos->execute();
            $totalProductos = $stmtProductos->fetch(PDO::FETCH_ASSOC)['total'];
            
            echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
            echo "<h4>Pedido: {$pedido['numero_pedido']} - Estado: {$pedido['estado']}</h4>";
            echo "<p>Productos: $totalProductos | Fecha: {$pedido['fecha_pedido']}</p>";
            echo "<button onclick='testFacturar5Metodos({$pedido['id']}, \"{$pedido['numero_pedido']}\")' ";
            echo "style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;'>";
            echo "💰 Facturar con 5 Métodos</button>";
            echo "</div>";
        }
    } else {
        echo "<div class='alert alert-warning'>⚠️ No hay pedidos disponibles para facturar</div>";
        echo "<p>Para crear un pedido de prueba:</p>";
        echo "<ol>";
        echo "<li>Ve a la mesa y agrega algunos productos</li>";
        echo "<li>Envía el pedido</li>";
        echo "<li>Vuelve aquí para facturar</li>";
        echo "</ol>";
    }
    
    echo "<h3>2. 💳 Configuración de Prueba (Lógica Correcta)</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h4>Distribución del Pago para la Cuenta ($50,000)</h4>";
    echo "<ul>";
    echo "<li>💵 <strong>Efectivo:</strong> $10,000 (20%)</li>";
    echo "<li>💳 <strong>Tarjeta (Bold):</strong> $10,000 (20%)</li>";
    echo "<li>📱 <strong>Nequi:</strong> $10,000 (20%)</li>";
    echo "<li>💰 <strong>Daviplata:</strong> $10,000 (20%)</li>";
    echo "<li>🏦 <strong>Bancolombia:</strong> $10,000 (20%)</li>";
    echo "</ul>";
    echo "<p><strong>Total Cuenta:</strong> $50,000</p>";
    echo "<p><strong>Total Pagado para Cuenta:</strong> $50,000 ✅</p>";
    echo "<p><strong>Propina (Adicional):</strong> $5,000 (se registra pero no afecta validación)</p>";
    echo "<p><strong>Cambio:</strong> $0 (pago exacto)</p>";
    echo "<p><em>Nota: La propina es adicional y se registra para estadísticas, pero no se incluye en la validación del pago.</em></p>";
    echo "</div>";
    
    echo "<h3>3. 🧪 Datos de Prueba (Corregidos)</h3>";
    echo "<div style='background: #e9ecef; padding: 15px; border-radius: 5px;'>";
    echo "<pre>";
    echo "efectivo: 10000\n";
    echo "tarjeta: 10000\n";
    echo "nequi: 10000\n";
    echo "daviplata: 10000\n";
    echo "bancolombia: 10000\n";
    echo "propina: 5000 (adicional, no afecta validación)\n";
    echo "total: 50000 (solo cuenta)\n";
    echo "pago: 1 (contado)\n";
    echo "pcedula: '12345678'\n";
    echo "mesa: $mesaId";
    echo "</pre>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}

echo "<br><a href='index.php?action=registroPmesa&ida=$mesaId' style='background: #007bff; color: white; padding: 10px; text-decoration: none;'>🔙 Volver a Mesa $mesaId</a>";
echo "<br><a href='test_facturacion_5_metodos.php?mesa=$mesaId' style='background: #6c757d; color: white; padding: 10px; text-decoration: none; margin: 5px;'>🔄 Actualizar</a>";
?>

<script>
function testFacturar5Metodos(pedidoId, numeroPedido) {
    console.log('💰 Iniciando facturación con 5 métodos para pedido:', numeroPedido);
    
    if (confirm('¿Facturar pedido ' + numeroPedido + ' con los 5 métodos de pago?\n\nCuenta: $50,000\nEfectivo: $10,000\nTarjeta: $10,000\nNequi: $10,000\nDaviplata: $10,000\nBancolombia: $10,000\nPropina: $5,000 (adicional)')) {

        // Datos de facturación con los 5 métodos (CORREGIDOS)
        const datosFactura = {
            efectivo: 10000,
            tarjeta: 10000,
            nequi: 10000,
            daviplata: 10000,
            bancolombia: 10000,
            pago: 1, // contado
            pcedula: '12345678',
            totalDescuento: 0,
            total: 50000, // Solo el total de la cuenta
            propina: 5000, // Adicional, no afecta validación
            mesa: <?=$mesaId?>,
            tipoTarjeta: 'credito'
        };
        
        console.log('📋 Datos de facturación:', datosFactura);
        
        // Mostrar loading
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '⏳ Procesando...';
        button.disabled = true;
        
        // Enviar facturación
        fetch('ajaxFactura.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams(datosFactura)
        })
        .then(response => response.text())
        .then(text => {
            console.log('📄 Respuesta completa del servidor:', text);
            
            // Restaurar botón
            button.innerHTML = originalText;
            button.disabled = false;
            
            // Analizar respuesta
            if (text.trim() === '') {
                alert('✅ ÉXITO: Facturación completada sin errores\n\nPedido ' + numeroPedido + ' facturado correctamente con los 5 métodos de pago.');
                setTimeout(() => location.reload(), 2000);
            } else if (text.includes('success') || text.includes('exitoso') || text.includes('completado')) {
                alert('✅ ÉXITO: Facturación completada\n\nRespuesta: ' + text.substring(0, 200));
                setTimeout(() => location.reload(), 2000);
            } else if (text.includes('error') || text.includes('Error') || text.includes('ERROR')) {
                alert('❌ ERROR en facturación:\n\n' + text.substring(0, 500));
            } else {
                alert('ℹ️ Respuesta del servidor:\n\n' + text.substring(0, 500));
            }
        })
        .catch(error => {
            console.error('❌ Error de conexión:', error);
            
            // Restaurar botón
            button.innerHTML = originalText;
            button.disabled = false;
            
            alert('❌ Error de conexión: ' + error.message);
        });
    }
}

// Función para test manual con formulario
function testFacturarFormulario(pedidoId, numeroPedido) {
    if (confirm('¿Crear formulario de facturación para ' + numeroPedido + '?')) {
        
        // Crear formulario
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'ajaxFactura.php';
        form.target = '_blank';
        
        const datos = {
            efectivo: 10000,
            tarjeta: 10000,
            nequi: 10000,
            daviplata: 10000,
            bancolombia: 10000,
            pago: 1,
            pcedula: '12345678',
            totalDescuento: 0,
            total: 50000, // Solo cuenta
            propina: 5000, // Adicional
            mesa: <?=$mesaId?>,
            tipoTarjeta: 'credito'
        };
        
        // Agregar campos al formulario
        Object.keys(datos).forEach(key => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = key;
            input.value = datos[key];
            form.appendChild(input);
        });
        
        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);
    }
}
</script>

<style>
.alert {
    padding: 15px;
    margin: 15px 0;
    border-radius: 5px;
    border: 1px solid transparent;
}
.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}
.alert-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}
.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}
</style>
