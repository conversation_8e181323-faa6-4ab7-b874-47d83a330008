<?php
// Test de debug para facturación desde registroPmesa.php
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
if (!isset($_SESSION["usuario"])) {
    $_SESSION["usuario"] = 1;
    $_SESSION["tipo_usuario"] = 1;
    $_SESSION["perfil"] = "administrador";
}

require_once "../../models/conexion.php";
require_once "../../models/crudFacturaAja.php";

$mesaId = isset($_GET['mesa']) ? $_GET['mesa'] : 1;

echo "<h1>🔍 Debug Facturación desde registroPmesa.php - Mesa $mesaId</h1>";

try {
    // Obtener productos para facturar
    $productosFacturar = DatosFacturaAja::obtenerProductosParaFacturar($mesaId);
    
    if (!empty($productosFacturar)) {
        // Calcular total real
        $totalReal = 0;
        foreach ($productosFacturar as $producto) {
            $descuento = ($producto['preciopr'] * ($producto['descuentopvm'] / 100));
            $precioConDescuento = $producto['preciopr'] - $descuento;
            $subtotal = $precioConDescuento * $producto['cantidadpvm'];
            $totalReal += $subtotal;
        }
        
        echo "<div class='alert alert-success'>";
        echo "<h4>✅ Productos Disponibles para Facturar</h4>";
        echo "<p><strong>Total:</strong> $" . number_format($totalReal) . "</p>";
        echo "<p><strong>Productos:</strong> " . count($productosFacturar) . "</p>";
        echo "</div>";
        
        echo "<h3>🧪 Test de Facturación Paso a Paso</h3>";
        
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>📋 Datos que se enviarán:</h4>";
        echo "<ul>";
        echo "<li><strong>efectivo:</strong> 10000</li>";
        echo "<li><strong>tarjeta:</strong> 10000</li>";
        echo "<li><strong>nequi:</strong> 10000</li>";
        echo "<li><strong>daviplata:</strong> 10000</li>";
        echo "<li><strong>bancolombia:</strong> " . ($totalReal - 40000) . "</li>";
        echo "<li><strong>total:</strong> $totalReal</li>";
        echo "<li><strong>propina:</strong> 0</li>";
        echo "<li><strong>pago:</strong> 1 (contado)</li>";
        echo "<li><strong>mesa:</strong> $mesaId</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<button onclick='testFacturacionCompleta()' style='background: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;'>";
        echo "💰 Test Facturación Completa";
        echo "</button>";
        
        echo "<button onclick='testValidacionPago()' style='background: #17a2b8; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; margin-left: 10px;'>";
        echo "🔍 Test Solo Validación";
        echo "</button>";
        
        echo "<div id='resultado' style='margin-top: 20px; padding: 15px; background: #fff; border: 1px solid #ddd; border-radius: 5px;'></div>";
        
    } else {
        echo "<div class='alert alert-warning'>";
        echo "<h4>⚠️ No hay productos para facturar</h4>";
        echo "<p>Para crear productos de prueba:</p>";
        echo "<ol>";
        echo "<li>Ve a la mesa y agrega productos</li>";
        echo "<li>Envía el pedido</li>";
        echo "<li>Vuelve aquí para facturar</li>";
        echo "</ol>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ Error: " . $e->getMessage() . "</div>";
}

echo "<br><a href='index.php?action=registroPmesa&ida=$mesaId' style='background: #007bff; color: white; padding: 10px; text-decoration: none;'>🔙 Volver a Mesa $mesaId</a>";
?>

<script>
function testFacturacionCompleta() {
    const totalReal = <?=$totalReal ?? 50000?>;
    const bancolombia = totalReal - 40000;
    
    const datos = {
        efectivo: 10000,
        tarjeta: 10000,
        nequi: 10000,
        daviplata: 10000,
        bancolombia: bancolombia,
        pago: 1,
        pcedula: '12345678',
        totalDescuento: 0,
        total: totalReal,
        propina: 0,
        mesa: <?=$mesaId?>,
        tipoTarjeta: 'credito'
    };
    
    console.log('🔍 Datos enviados:', datos);
    
    document.getElementById('resultado').innerHTML = '<div style="color: blue;">⏳ Enviando facturación...</div>';
    
    fetch('ajaxFactura.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams(datos)
    })
    .then(response => response.text())
    .then(text => {
        console.log('📄 Respuesta completa:', text);
        
        let html = '<h4>📄 Respuesta del Servidor:</h4>';
        html += '<pre style="background: #f8f9fa; padding: 10px; border-radius: 5px; white-space: pre-wrap;">' + text + '</pre>';
        
        if (text.includes('success') || text.includes('Pago registrado')) {
            html += '<div style="color: green; font-weight: bold;">✅ ÉXITO: Facturación completada</div>';
        } else if (text.includes('error') || text.includes('Error')) {
            html += '<div style="color: red; font-weight: bold;">❌ ERROR en facturación</div>';
        } else if (text.includes('alert')) {
            const alertMatch = text.match(/alert\('([^']+)'\)/);
            const mensaje = alertMatch ? alertMatch[1] : 'Mensaje del sistema';
            html += '<div style="color: orange; font-weight: bold;">⚠️ ALERTA: ' + mensaje + '</div>';
        }
        
        document.getElementById('resultado').innerHTML = html;
    })
    .catch(error => {
        console.error('❌ Error:', error);
        document.getElementById('resultado').innerHTML = '<div style="color: red;">❌ Error de conexión: ' + error.message + '</div>';
    });
}

function testValidacionPago() {
    const totalReal = <?=$totalReal ?? 50000?>;
    
    // Test con pago insuficiente para ver la validación
    const datos = {
        efectivo: 10000,
        tarjeta: 0,
        nequi: 0,
        daviplata: 0,
        bancolombia: 0,
        pago: 1,
        pcedula: '12345678',
        totalDescuento: 0,
        total: totalReal,
        propina: 0,
        mesa: <?=$mesaId?>,
        tipoTarjeta: 'credito'
    };
    
    console.log('🔍 Test validación con pago insuficiente:', datos);
    
    document.getElementById('resultado').innerHTML = '<div style="color: blue;">⏳ Probando validación de pago...</div>';
    
    fetch('ajaxFactura.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams(datos)
    })
    .then(response => response.text())
    .then(text => {
        console.log('📄 Respuesta validación:', text);
        
        let html = '<h4>🔍 Test de Validación (Pago Insuficiente):</h4>';
        html += '<p><strong>Enviado:</strong> $10,000 efectivo</p>';
        html += '<p><strong>Total cuenta:</strong> $' + totalReal.toLocaleString() + '</p>';
        html += '<p><strong>Resultado esperado:</strong> Error de pago insuficiente</p>';
        html += '<hr>';
        html += '<h5>📄 Respuesta del servidor:</h5>';
        html += '<pre style="background: #f8f9fa; padding: 10px; border-radius: 5px; white-space: pre-wrap;">' + text + '</pre>';
        
        if (text.includes('menor al total') || text.includes('insuficiente')) {
            html += '<div style="color: green; font-weight: bold;">✅ CORRECTO: Validación funcionando</div>';
        } else {
            html += '<div style="color: red; font-weight: bold;">❌ ERROR: Validación no está funcionando</div>';
        }
        
        document.getElementById('resultado').innerHTML = html;
    })
    .catch(error => {
        console.error('❌ Error:', error);
        document.getElementById('resultado').innerHTML = '<div style="color: red;">❌ Error de conexión: ' + error.message + '</div>';
    });
}
</script>

<style>
.alert {
    padding: 15px;
    margin: 15px 0;
    border-radius: 5px;
    border: 1px solid transparent;
}
.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}
.alert-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}
.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}
</style>
