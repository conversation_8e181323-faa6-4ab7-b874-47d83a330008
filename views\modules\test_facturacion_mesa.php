<?php
// Test para verificar qué productos se incluyen en la facturación
session_start();
if (!isset($_SESSION["usuario"])) {
    $_SESSION["usuario"] = 1;
    $_SESSION["tipo_usuario"] = 1;
    $_SESSION["perfil"] = "administrador";
}

$mesaId = isset($_GET['mesa']) ? $_GET['mesa'] : 2;

echo "<h1>🧾 Test Facturación Mesa $mesaId</h1>";

require_once "../../models/conexion.php";
require_once "../../models/crudPedidoMesaVendido.php";

try {
    $db = Conexion::conectar();
    
    echo "<h3>1. 📊 Todos los Pedidos de la Mesa</h3>";
    $stmt = $db->prepare("SELECT * FROM pedidos WHERE mesa_id = ? ORDER BY fecha_pedido DESC");
    $stmt->execute([$mesaId]);
    $todosPedidos = $stmt->fetchAll();
    
    if (!empty($todosPedidos)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Número</th><th>Estado</th><th>Fecha</th><th>¿Se Factura?</th></tr>";
        foreach ($todosPedidos as $p) {
            // Verificar si el pedido tiene productos
            $stmtProductos = $db->prepare("SELECT COUNT(*) as total FROM producto_vendido_mesa WHERE pedidos_id = ?");
            $stmtProductos->execute([$p['id']]);
            $tieneProductos = $stmtProductos->fetch()['total'] > 0;

            // Solo se factura si está en estado facturable Y tiene productos
            $esFacturable = in_array($p['estado'], ['entregado', 'borrador']);
            $seFactura = ($esFacturable && $tieneProductos) ? '✅ SÍ' : '❌ NO';

            // Color según estado y si tiene productos
            if ($esFacturable && $tieneProductos) {
                $color = '#d4edda'; // Verde para facturables con productos
            } elseif ($p['estado'] == 'borrador' && !$tieneProductos) {
                $color = '#f8f9fa'; // Gris para borradores vacíos
            } else {
                $color = '#f8d7da'; // Rojo para no facturables
            }

            echo "<tr style='background-color: $color;'>";
            echo "<td>" . $p['id'] . "</td>";
            echo "<td>" . $p['numero_pedido'] . "</td>";
            echo "<td><strong>" . $p['estado'] . "</strong></td>";
            echo "<td>" . $p['fecha_pedido'] . "</td>";
            echo "<td><strong>$seFactura</strong></td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>2. 🛒 Productos que SÍ se Facturan (Método Actual)</h3>";
    $productosFacturacion = DatosPedidoMesaVendido::obtenerProductosEntregadosMesaModel($mesaId);
    
    echo "<strong>Productos incluidos en facturación:</strong> " . count($productosFacturacion) . "<br>";
    
    if (!empty($productosFacturacion)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Producto</th><th>Cantidad</th><th>Precio</th><th>Subtotal</th><th>Estado Pedido</th><th>Número Pedido</th></tr>";
        
        $totalFacturacion = 0;
        foreach ($productosFacturacion as $prod) {
            $subtotal = $prod['cantidadpvm'] * $prod['preciopr'];
            $totalFacturacion += $subtotal;
            echo "<tr>";
            echo "<td>" . $prod['nombrepr'] . "</td>";
            echo "<td>" . $prod['cantidadpvm'] . "</td>";
            echo "<td>$" . number_format($prod['preciopr']) . "</td>";
            echo "<td>$" . number_format($subtotal) . "</td>";
            echo "<td>" . $prod['estado'] . "</td>";
            echo "<td>" . $prod['numero_pedido'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "<strong>💰 Total a facturar (método actual): $" . number_format($totalFacturacion) . "</strong><br>";
    } else {
        echo "<span style='color: red;'>⚠️ No hay productos para facturar con el método actual</span><br>";
    }
    
    echo "<h3>3. 🔍 Productos que DEBERÍAN Facturarse (Todos los Estados)</h3>";
    
    // Consulta que incluye todos los estados excepto cancelado
    $sqlTodos = "SELECT pr.id AS idpr, pr.nombre AS nombrepr, pr.precio AS preciopr, pr.codigo AS prcodigo,
                pvm.cantidad AS cantidadpvm, pvm.fecha_hora AS fechapvm, pvm.mesas_id AS idmesa,
                pvm.mesero AS mesero, pvm.descuento AS descuentopvm, pvm.codigo_descuento AS pvmcodigo_descuento,
                p.numero_pedido, p.estado, p.id as pedido_id
                FROM productos pr
                JOIN producto_vendido_mesa pvm ON pr.id = pvm.productos_id
                JOIN pedidos p ON pvm.pedidos_id = p.id
                WHERE pvm.mesas_id = ? AND p.estado NOT IN ('cancelado')
                ORDER BY p.fecha_pedido DESC, pvm.fecha_hora DESC";
    
    $stmtTodos = $db->prepare($sqlTodos);
    $stmtTodos->execute([$mesaId]);
    $todosProductos = $stmtTodos->fetchAll();
    
    echo "<strong>Productos que deberían facturarse:</strong> " . count($todosProductos) . "<br>";
    
    if (!empty($todosProductos)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Producto</th><th>Cantidad</th><th>Precio</th><th>Subtotal</th><th>Estado Pedido</th><th>Número Pedido</th></tr>";
        
        $totalCompleto = 0;
        foreach ($todosProductos as $prod) {
            $subtotal = $prod['cantidadpvm'] * $prod['preciopr'];
            $totalCompleto += $subtotal;
            $color = in_array($prod['estado'], ['entregado', 'borrador']) ? '#d4edda' : '#fff3cd';
            echo "<tr style='background-color: $color;'>";
            echo "<td>" . $prod['nombrepr'] . "</td>";
            echo "<td>" . $prod['cantidadpvm'] . "</td>";
            echo "<td>$" . number_format($prod['preciopr']) . "</td>";
            echo "<td>$" . number_format($subtotal) . "</td>";
            echo "<td><strong>" . $prod['estado'] . "</strong></td>";
            echo "<td>" . $prod['numero_pedido'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "<strong>💰 Total completo que debería facturarse: $" . number_format($totalCompleto) . "</strong><br>";
        
        if ($totalCompleto != $totalFacturacion) {
            $diferencia = $totalCompleto - $totalFacturacion;
            echo "<div style='background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; margin: 10px 0;'>";
            echo "<strong>⚠️ PROBLEMA DETECTADO:</strong><br>";
            echo "Diferencia: $" . number_format($diferencia) . " no se está facturando<br>";
            echo "Esto corresponde a productos en pedidos con estado 'enviado'";
            echo "</div>";
        }
    }
    
    echo "<h3>4. 💡 Recomendación</h3>";
    echo "<div style='background: #d1ecf1; padding: 10px; border: 1px solid #bee5eb; margin: 10px 0;'>";
    echo "<strong>Estados que deberían incluirse en facturación:</strong><br>";
    echo "✅ <strong>borrador</strong> - Pedidos aún no enviados<br>";
    echo "✅ <strong>enviado</strong> - Pedidos en cocina<br>";
    echo "✅ <strong>entregado</strong> - Pedidos ya servidos<br>";
    echo "❌ <strong>cancelado</strong> - Pedidos cancelados (no facturar)<br>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage();
}

echo "<hr>";
echo "<h3>🔄 Navegación y Tests</h3>";
echo "<div style='margin: 10px 0;'>";
echo "<h4>📋 Tests del Flujo Completo:</h4>";
echo "<a href='test_flujo_simple.php?mesa=$mesaId' style='background: #6f42c1; color: white; padding: 8px 16px; text-decoration: none; margin: 5px; border-radius: 4px;'>🚀 Test Simple</a>";
echo "<a href='test_agregar_producto.php?mesa=$mesaId' style='background: #28a745; color: white; padding: 8px 16px; text-decoration: none; margin: 5px; border-radius: 4px;'>1. Agregar Producto</a>";
echo "<a href='test_enviar_pedido.php?mesa=$mesaId' style='background: #ffc107; color: #212529; padding: 8px 16px; text-decoration: none; margin: 5px; border-radius: 4px;'>2. Enviar Pedido</a>";
echo "<a href='test_prefacturacion.php?mesa=$mesaId' style='background: #17a2b8; color: white; padding: 8px 16px; text-decoration: none; margin: 5px; border-radius: 4px;'>3. Prefacturación</a>";
echo "<a href='test_facturacion_completa.php?mesa=$mesaId' style='background: #dc3545; color: white; padding: 8px 16px; text-decoration: none; margin: 5px; border-radius: 4px;'>4. Facturación Completa</a>";
echo "</div>";

echo "<div style='margin: 10px 0;'>";
echo "<h4>🔗 Enlaces del Sistema:</h4>";
echo "<a href='debug_mesa_pedidos.php?mesa=$mesaId' style='background: #6c757d; color: white; padding: 8px 16px; text-decoration: none; margin: 5px; border-radius: 4px;'>🔍 Debug Pedidos</a>";
echo "<a href='../../index.php?action=registroPmesa&ida=$mesaId' style='background: #007bff; color: white; padding: 8px 16px; text-decoration: none; margin: 5px; border-radius: 4px;'>🔙 Volver a Mesa</a>";
echo "<a href='diagnostico.php' style='background: #6f42c1; color: white; padding: 8px 16px; text-decoration: none; margin: 5px; border-radius: 4px;'>📊 Diagnóstico General</a>";
echo "</div>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}

table {
    width: 100%;
    max-width: 800px;
}

th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #f0f0f0;
}

h3 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 5px;
}
</style>
