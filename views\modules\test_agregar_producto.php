<?php
// Test para simular la adición de un producto
session_start();
if (!isset($_SESSION["usuario"])) {
    $_SESSION["usuario"] = 1;
    $_SESSION["tipo_usuario"] = 1;
    $_SESSION["perfil"] = "administrador";
}

$mesaId = isset($_GET['mesa']) ? $_GET['mesa'] : 2;
$_SESSION["mesa"] = $mesaId;

echo "<h1>🧪 Test Agregar Producto - Mesa $mesaId</h1>";

// Simular datos de producto
$datosProducto = array(
    "producto_id" => 1, // ID de un producto existente
    "mesa_id" => $mesaId,
    "fecha_hora" => date('Y-m-d H:i:s')
);

echo "<h3>1. 📋 Datos del Producto a Agregar</h3>";
echo "<pre>" . print_r($datosProducto, true) . "</pre>";

echo "<h3>2. 🔄 Simulando AJAX de Agregar Producto</h3>";

// Simular la petición AJAX
$postData = http_build_query($datosProducto);

$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => 'Content-Type: application/x-www-form-urlencoded',
        'content' => $postData
    ]
]);

$url = 'https://macarena.anconclub.com/views/modules/ajaxPedidoVentas.php';
echo "<strong>URL:</strong> $url<br>";
echo "<strong>Datos enviados:</strong> $postData<br><br>";

$response = file_get_contents($url, false, $context);

echo "<strong>Respuesta del AJAX:</strong><br>";
echo "<div style='background: #f8f9fa; padding: 10px; border: 1px solid #ddd; margin: 10px 0; font-family: monospace;'>";
echo htmlspecialchars($response);
echo "</div>";

echo "<h3>3. 🔍 Verificar Estado Después</h3>";

// Verificar pedidos de la mesa después de agregar
require_once "../../models/conexion.php";

try {
    $db = Conexion::conectar();
    
    // Verificar pedidos de la mesa
    $stmt = $db->prepare("SELECT * FROM pedidos WHERE mesa_id = ? ORDER BY fecha_pedido DESC LIMIT 3");
    $stmt->execute([$mesaId]);
    $pedidos = $stmt->fetchAll();
    
    echo "<strong>Pedidos en la mesa después de agregar:</strong><br>";
    if (!empty($pedidos)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Número</th><th>Estado</th><th>Fecha</th></tr>";
        foreach ($pedidos as $p) {
            echo "<tr>";
            echo "<td>" . $p['id'] . "</td>";
            echo "<td>" . $p['numero_pedido'] . "</td>";
            echo "<td>" . $p['estado'] . "</td>";
            echo "<td>" . $p['fecha_pedido'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Verificar productos del último pedido
        $ultimoPedido = $pedidos[0];
        echo "<h4>Productos del pedido {$ultimoPedido['numero_pedido']}:</h4>";
        
        $stmtProductos = $db->prepare("
            SELECT p.nombre, pvm.cantidad 
            FROM producto_vendido_mesa pvm 
            JOIN productos p ON pvm.productos_id = p.id 
            WHERE pvm.pedidos_id = ?
        ");
        $stmtProductos->execute([$ultimoPedido['id']]);
        $productos = $stmtProductos->fetchAll();
        
        if (!empty($productos)) {
            foreach ($productos as $prod) {
                echo "- {$prod['nombre']} (x{$prod['cantidad']})<br>";
            }
        } else {
            echo "<span style='color: red;'>⚠️ Este pedido no tiene productos asociados</span><br>";
        }
    } else {
        echo "No hay pedidos en esta mesa<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage();
}

echo "<br><br>";
echo "<a href='debug_mesa_pedidos.php?mesa=$mesaId' style='background: #28a745; color: white; padding: 10px; text-decoration: none;'>🔍 Debug Completo</a>";
echo "<a href='../../index.php?action=registroPmesa&ida=$mesaId' style='background: #007bff; color: white; padding: 10px; text-decoration: none; margin-left: 10px;'>🔙 Volver a Mesa</a>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}

table {
    width: 100%;
    max-width: 600px;
}

th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #f0f0f0;
}

pre {
    background: #f8f9fa;
    padding: 10px;
    border: 1px solid #ddd;
    overflow-x: auto;
}
</style>
