<?php
// Test de Suministros y Productos - Diagnóstico
echo "<h1>🧪 Test de Suministros y Productos</h1>";
echo "<p><strong>Diagnóstico del sistema de asignación de suministros a productos</strong></p>";

try {
    require_once '../../models/conexion.php';
    $conexion = new Conexion();
    $pdo = $conexion->conectar();
    
    echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0;'>";
    echo "<h4>✅ Conexión a base de datos exitosa</h4>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
    echo "<h4>❌ Error de conexión a base de datos</h4>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
    exit;
}

// Verificar estructura de tablas
echo "<h2>📋 Verificación de Tablas:</h2>";

$tablas_verificar = [
    'productos' => 'Productos del sistema',
    'suministros' => 'Suministros/ingredientes',
    'suministros_productos' => 'Relación suministros-productos (recetas)'
];

echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<table style='width: 100%; border-collapse: collapse;'>";
echo "<tr style='background-color: #e9ecef;'>";
echo "<th style='border: 1px solid #ddd; padding: 8px; text-align: left;'>Tabla</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px; text-align: left;'>Descripción</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px; text-align: center;'>Estado</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px; text-align: center;'>Registros</th>";
echo "</tr>";

foreach ($tablas_verificar as $tabla => $descripcion) {
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM $tabla");
        $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        $estado = "✅ Existe";
        $color = "#d4edda";
    } catch (Exception $e) {
        $total = "N/A";
        $estado = "❌ No existe";
        $color = "#f8d7da";
    }
    
    echo "<tr style='background-color: $color;'>";
    echo "<td style='border: 1px solid #ddd; padding: 8px; font-weight: bold;'>$tabla</td>";
    echo "<td style='border: 1px solid #ddd; padding: 8px;'>$descripcion</td>";
    echo "<td style='border: 1px solid #ddd; padding: 8px; text-align: center;'>$estado</td>";
    echo "<td style='border: 1px solid #ddd; padding: 8px; text-align: center;'>$total</td>";
    echo "</tr>";
}

echo "</table>";
echo "</div>";

// Verificar estructura de suministros
echo "<h2>🔍 Estructura de Tabla Suministros:</h2>";
try {
    $stmt = $pdo->query("DESCRIBE suministros");
    $columnas = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<div style='background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<table style='width: 100%; border-collapse: collapse;'>";
    echo "<tr style='background-color: #cce7ff;'>";
    echo "<th style='border: 1px solid #ddd; padding: 8px;'>Campo</th>";
    echo "<th style='border: 1px solid #ddd; padding: 8px;'>Tipo</th>";
    echo "<th style='border: 1px solid #ddd; padding: 8px;'>Nulo</th>";
    echo "<th style='border: 1px solid #ddd; padding: 8px;'>Clave</th>";
    echo "</tr>";
    
    foreach ($columnas as $columna) {
        echo "<tr>";
        echo "<td style='border: 1px solid #ddd; padding: 8px; font-weight: bold;'>{$columna['Field']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$columna['Type']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$columna['Null']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$columna['Key']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p>⚠️ Error al obtener estructura: " . $e->getMessage() . "</p>";
}

// Mostrar algunos suministros de ejemplo
echo "<h2>📦 Suministros Disponibles (Muestra):</h2>";
try {
    $stmt = $pdo->query("SELECT id, codigo, nombre, activo FROM suministros WHERE activo = 's' ORDER BY nombre LIMIT 10");
    $suministros = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($suministros) {
        echo "<div style='background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<table style='width: 100%; border-collapse: collapse;'>";
        echo "<tr style='background-color: #ffeaa7;'>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>ID</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Código</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Nombre</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Estado</th>";
        echo "</tr>";
        
        foreach ($suministros as $suministro) {
            echo "<tr>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$suministro['id']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px; font-weight: bold;'>{$suministro['codigo']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$suministro['nombre']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>✅ Activo</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
    } else {
        echo "<p>⚠️ No se encontraron suministros activos</p>";
    }
    
} catch (Exception $e) {
    echo "<p>⚠️ Error al obtener suministros: " . $e->getMessage() . "</p>";
}

// Test de búsqueda de suministro por código
echo "<h2>🔍 Test de Búsqueda por Código:</h2>";

if (isset($_POST['test_codigo'])) {
    $codigo_test = $_POST['codigo_test'];
    
    echo "<div style='border: 1px solid #007bff; padding: 20px; margin: 15px 0; border-radius: 8px;'>";
    echo "<h4>🧪 Probando código: $codigo_test</h4>";
    
    try {
        require_once '../../models/crudSuministroProducto.php';
        
        $resultado = DatosSuministroProducto::suministroCodigoModel($codigo_test);
        
        if ($resultado) {
            echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0;'>";
            echo "<h5>✅ Suministro Encontrado</h5>";
            echo "<p><strong>ID:</strong> {$resultado['id']}</p>";
            echo "<p><strong>Código:</strong> {$resultado['codigo']}</p>";
            echo "<p><strong>Nombre:</strong> {$resultado['nombre']}</p>";
            echo "<p><strong>Estado:</strong> {$resultado['activo']}</p>";
            echo "</div>";
        } else {
            echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
            echo "<h5>❌ Suministro No Encontrado</h5>";
            echo "<p>No se encontró ningún suministro con el código: <strong>$codigo_test</strong></p>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
        echo "<h5>❌ Error en la búsqueda</h5>";
        echo "<p>Error: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    
    echo "</div>";
}

// Formulario de test
echo "<form method='POST' style='background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>🎯 Probar Búsqueda de Suministro:</h4>";
echo "<div style='margin: 15px 0;'>";
echo "<label><strong>Código del Suministro:</strong></label><br>";
echo "<input type='text' name='codigo_test' placeholder='Ej: 123456' required style='padding: 8px; border: 1px solid #ccc; border-radius: 3px; width: 200px;'>";
echo "<button type='submit' name='test_codigo' style='background-color: #007bff; color: white; padding: 8px 16px; border: none; border-radius: 3px; margin-left: 10px; cursor: pointer;'>🔍 Buscar</button>";
echo "</div>";
echo "</form>";

// Mostrar relaciones existentes
echo "<h2>🔗 Relaciones Suministros-Productos Existentes:</h2>";
try {
    $stmt = $pdo->query("
        SELECT 
            sp.id,
            p.id as producto_id,
            p.nombre as producto_nombre,
            s.id as suministro_id,
            s.codigo as suministro_codigo,
            s.nombre as suministro_nombre,
            sp.cantidades
        FROM suministros_productos sp
        INNER JOIN productos p ON sp.producto_id = p.id
        INNER JOIN suministros s ON sp.suministro_id = s.id
        ORDER BY p.nombre, s.nombre
        LIMIT 20
    ");
    $relaciones = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($relaciones) {
        echo "<div style='background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<table style='width: 100%; border-collapse: collapse; font-size: 12px;'>";
        echo "<tr style='background-color: #cce7ff;'>";
        echo "<th style='border: 1px solid #ddd; padding: 6px;'>Producto</th>";
        echo "<th style='border: 1px solid #ddd; padding: 6px;'>Suministro</th>";
        echo "<th style='border: 1px solid #ddd; padding: 6px;'>Código</th>";
        echo "<th style='border: 1px solid #ddd; padding: 6px;'>Cantidad</th>";
        echo "</tr>";
        
        foreach ($relaciones as $relacion) {
            echo "<tr>";
            echo "<td style='border: 1px solid #ddd; padding: 6px; font-weight: bold;'>{$relacion['producto_nombre']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 6px;'>{$relacion['suministro_nombre']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 6px;'>{$relacion['suministro_codigo']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 6px;'>{$relacion['cantidades']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
    } else {
        echo "<p>⚠️ No se encontraron relaciones suministros-productos</p>";
    }
    
} catch (Exception $e) {
    echo "<p>⚠️ Error al obtener relaciones: " . $e->getMessage() . "</p>";
}

// Diagnóstico y recomendaciones
echo "<h2>💡 Diagnóstico y Recomendaciones:</h2>";
echo "<div style='background-color: #fff3cd; padding: 20px; border-left: 4px solid #ffc107; margin: 20px 0;'>";
echo "<h4>🔧 Problemas Solucionados:</h4>";
echo "<ul>";
echo "<li>✅ <strong>Función suministroCodigoModel:</strong> Mejorada con manejo de errores</li>";
echo "<li>✅ <strong>Función registroSuministroPModel:</strong> Validación de datos y manejo de duplicados</li>";
echo "<li>✅ <strong>Controlador:</strong> Mejor manejo de errores y mensajes informativos</li>";
echo "<li>✅ <strong>Búsqueda flexible:</strong> Busca por código exacto y parcial</li>";
echo "</ul>";

echo "<h4>📋 Para probar el sistema:</h4>";
echo "<ol>";
echo "<li>Ve a: <a href='index.php?action=registroReseta&id=78'>Registro de Receta</a></li>";
echo "<li>Ingresa un <strong>código de suministro válido</strong> (ver tabla arriba)</li>";
echo "<li>Especifica la <strong>cantidad</strong> necesaria</li>";
echo "<li>Haz clic en <strong>Enviar</strong></li>";
echo "</ol>";

echo "<h4>⚠️ Si sigue sin funcionar:</h4>";
echo "<ul>";
echo "<li>Verificar que el <strong>código del suministro</strong> exista en la base de datos</li>";
echo "<li>Verificar que el suministro esté <strong>activo</strong> (activo = 's')</li>";
echo "<li>Revisar los <strong>logs de error</strong> del servidor</li>";
echo "<li>Verificar que el <strong>producto ID</strong> sea válido</li>";
echo "</ul>";
echo "</div>";

// Botones de navegación
echo "<p style='margin-top: 30px;'>";
echo "<a href='index.php?action=registroReseta&id=78' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🧪 Probar Registro de Receta</a>";
echo "<a href='diagnostico' style='background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>← Volver al Diagnóstico</a>";
echo "</p>";
?>
