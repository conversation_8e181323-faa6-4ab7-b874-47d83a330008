<?php

session_start();

if(!isset($_SESSION["validar"]) || !$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "models/conexion.php";

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Verificar: Estructura de Tablas</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
</head>
<body>

<div class="container">
    <h2>🔍 Verificar: Estructura de Tablas de Productos</h2>
    <hr>
    
    <div class="alert alert-info">
        <h4>📋 Verificación de Claves Primarias</h4>
        <p>Verificando la estructura de las tablas donde se insertan productos para asegurar que la solución ON DUPLICATE KEY UPDATE funcione correctamente.</p>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="panel panel-primary">
                <div class="panel-heading">
                    <h3 class="panel-title">📊 Tabla: producto_vendido_mesa</h3>
                </div>
                <div class="panel-body">
                    <?php
                    try {
                        echo "<h5>Estructura:</h5>";
                        $stmt = Conexion::conectar()->prepare("DESCRIBE producto_vendido_mesa");
                        $stmt->execute();
                        $estructura = $stmt->fetchAll(PDO::FETCH_ASSOC);
                        
                        echo "<table class='table table-condensed'>";
                        echo "<thead><tr><th>Campo</th><th>Tipo</th><th>Key</th><th>Default</th></tr></thead>";
                        echo "<tbody>";
                        foreach ($estructura as $campo) {
                            $key_class = '';
                            if ($campo['Key'] == 'PRI') $key_class = 'danger';
                            elseif ($campo['Key'] == 'UNI') $key_class = 'warning';
                            elseif ($campo['Key'] == 'MUL') $key_class = 'info';
                            
                            echo "<tr class='{$key_class}'>";
                            echo "<td><strong>{$campo['Field']}</strong></td>";
                            echo "<td>{$campo['Type']}</td>";
                            echo "<td>{$campo['Key']}</td>";
                            echo "<td>{$campo['Default']}</td>";
                            echo "</tr>";
                        }
                        echo "</tbody></table>";
                        
                        echo "<h5>Índices:</h5>";
                        $stmt_indices = Conexion::conectar()->prepare("SHOW INDEX FROM producto_vendido_mesa");
                        $stmt_indices->execute();
                        $indices = $stmt_indices->fetchAll(PDO::FETCH_ASSOC);
                        
                        echo "<table class='table table-condensed'>";
                        echo "<thead><tr><th>Nombre</th><th>Columna</th><th>Único</th></tr></thead>";
                        echo "<tbody>";
                        foreach ($indices as $indice) {
                            echo "<tr>";
                            echo "<td>{$indice['Key_name']}</td>";
                            echo "<td>{$indice['Column_name']}</td>";
                            echo "<td>" . ($indice['Non_unique'] == 0 ? 'Sí' : 'No') . "</td>";
                            echo "</tr>";
                        }
                        echo "</tbody></table>";
                        
                    } catch (Exception $e) {
                        echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
                    }
                    ?>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="panel panel-success">
                <div class="panel-heading">
                    <h3 class="panel-title">📊 Tabla: pedido_productos_mesa</h3>
                </div>
                <div class="panel-body">
                    <?php
                    try {
                        echo "<h5>Estructura:</h5>";
                        $stmt = Conexion::conectar()->prepare("DESCRIBE pedido_productos_mesa");
                        $stmt->execute();
                        $estructura = $stmt->fetchAll(PDO::FETCH_ASSOC);
                        
                        echo "<table class='table table-condensed'>";
                        echo "<thead><tr><th>Campo</th><th>Tipo</th><th>Key</th><th>Default</th></tr></thead>";
                        echo "<tbody>";
                        foreach ($estructura as $campo) {
                            $key_class = '';
                            if ($campo['Key'] == 'PRI') $key_class = 'danger';
                            elseif ($campo['Key'] == 'UNI') $key_class = 'warning';
                            elseif ($campo['Key'] == 'MUL') $key_class = 'info';
                            
                            echo "<tr class='{$key_class}'>";
                            echo "<td><strong>{$campo['Field']}</strong></td>";
                            echo "<td>{$campo['Type']}</td>";
                            echo "<td>{$campo['Key']}</td>";
                            echo "<td>{$campo['Default']}</td>";
                            echo "</tr>";
                        }
                        echo "</tbody></table>";
                        
                        echo "<h5>Índices:</h5>";
                        $stmt_indices = Conexion::conectar()->prepare("SHOW INDEX FROM pedido_productos_mesa");
                        $stmt_indices->execute();
                        $indices = $stmt_indices->fetchAll(PDO::FETCH_ASSOC);
                        
                        echo "<table class='table table-condensed'>";
                        echo "<thead><tr><th>Nombre</th><th>Columna</th><th>Único</th></tr></thead>";
                        echo "<tbody>";
                        foreach ($indices as $indice) {
                            echo "<tr>";
                            echo "<td>{$indice['Key_name']}</td>";
                            echo "<td>{$indice['Column_name']}</td>";
                            echo "<td>" . ($indice['Non_unique'] == 0 ? 'Sí' : 'No') . "</td>";
                            echo "</tr>";
                        }
                        echo "</tbody></table>";
                        
                    } catch (Exception $e) {
                        echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>
    
    <div class="panel panel-warning">
        <div class="panel-heading">
            <h3 class="panel-title">🔍 Análisis de Claves Primarias</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                // Verificar clave primaria de producto_vendido_mesa
                $stmt1 = Conexion::conectar()->prepare("
                    SELECT COLUMN_NAME 
                    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                    WHERE TABLE_NAME = 'producto_vendido_mesa' 
                    AND CONSTRAINT_NAME = 'PRIMARY'
                    ORDER BY ORDINAL_POSITION
                ");
                $stmt1->execute();
                $pk_pvm = $stmt1->fetchAll(PDO::FETCH_COLUMN);
                
                // Verificar clave primaria de pedido_productos_mesa
                $stmt2 = Conexion::conectar()->prepare("
                    SELECT COLUMN_NAME 
                    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                    WHERE TABLE_NAME = 'pedido_productos_mesa' 
                    AND CONSTRAINT_NAME = 'PRIMARY'
                    ORDER BY ORDINAL_POSITION
                ");
                $stmt2->execute();
                $pk_ppm = $stmt2->fetchAll(PDO::FETCH_COLUMN);
                
                echo "<div class='row'>";
                echo "<div class='col-md-6'>";
                echo "<h5>🔑 Clave Primaria producto_vendido_mesa:</h5>";
                if (count($pk_pvm) > 0) {
                    echo "<ul>";
                    foreach ($pk_pvm as $campo) {
                        echo "<li><code>{$campo}</code></li>";
                    }
                    echo "</ul>";
                } else {
                    echo "<p class='text-warning'>⚠️ No se encontró clave primaria</p>";
                }
                echo "</div>";
                
                echo "<div class='col-md-6'>";
                echo "<h5>🔑 Clave Primaria pedido_productos_mesa:</h5>";
                if (count($pk_ppm) > 0) {
                    echo "<ul>";
                    foreach ($pk_ppm as $campo) {
                        echo "<li><code>{$campo}</code></li>";
                    }
                    echo "</ul>";
                } else {
                    echo "<p class='text-warning'>⚠️ No se encontró clave primaria</p>";
                }
                echo "</div>";
                echo "</div>";
                
                // Comparar claves primarias
                echo "<hr>";
                echo "<h5>📊 Comparación:</h5>";
                if (count($pk_pvm) > 0 && count($pk_ppm) > 0) {
                    $pk_pvm_str = implode(', ', $pk_pvm);
                    $pk_ppm_str = implode(', ', $pk_ppm);
                    
                    if ($pk_pvm_str === $pk_ppm_str) {
                        echo "<div class='alert alert-success'>";
                        echo "<h6>✅ Claves primarias idénticas</h6>";
                        echo "<p>Ambas tablas tienen la misma estructura de clave primaria: <code>{$pk_pvm_str}</code></p>";
                        echo "<p>La solución ON DUPLICATE KEY UPDATE funcionará correctamente en ambas tablas.</p>";
                        echo "</div>";
                    } else {
                        echo "<div class='alert alert-warning'>";
                        echo "<h6>⚠️ Claves primarias diferentes</h6>";
                        echo "<p><strong>producto_vendido_mesa:</strong> <code>{$pk_pvm_str}</code></p>";
                        echo "<p><strong>pedido_productos_mesa:</strong> <code>{$pk_ppm_str}</code></p>";
                        echo "<p>Puede ser necesario ajustar la solución para cada tabla.</p>";
                        echo "</div>";
                    }
                } else {
                    echo "<div class='alert alert-danger'>";
                    echo "<h6>❌ No se pueden comparar las claves primarias</h6>";
                    echo "<p>Una o ambas tablas no tienen clave primaria definida.</p>";
                    echo "</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">🛠️ Estado de la Solución</h3>
        </div>
        <div class="panel-body">
            <h5>✅ Funciones ya corregidas:</h5>
            <ul>
                <li>✅ <code>registroPmesaModel()</code> - producto_vendido_mesa</li>
                <li>✅ <code>addPmesaModel()</code> - producto_vendido_mesa</li>
                <li>✅ <code>registroPmesaProductoModel()</code> - producto_vendido_mesa</li>
                <li>✅ <code>facturaajaxModel()</code> - pedido_productos_mesa</li>
            </ul>
            
            <h5>🎯 Resultado esperado:</h5>
            <p>Con estas correcciones, ya no deberías ver el error:</p>
            <div class="well">
                <code>SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '56-5-31' for key 'PRIMARY'</code>
            </div>
            
            <div class="alert alert-success">
                <h6>🎉 ¡Solución implementada!</h6>
                <p>Ahora puedes intentar facturar sin errores de clave duplicada.</p>
            </div>
        </div>
    </div>
    
    <hr>
    <div class="row">
        <div class="col-md-3">
            <a href="index.php?action=debug_error_facturacion" class="btn btn-primary btn-block">🔙 Debug Error</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=mesa" class="btn btn-info btn-block">🪑 Ir a Mesas</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=registroPmesa&ida=5" class="btn btn-warning btn-block">🧪 Test Mesa 5</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=diagnostico" class="btn btn-success btn-block">📊 Diagnóstico</a>
        </div>
    </div>
</div>

</body>
</html>
