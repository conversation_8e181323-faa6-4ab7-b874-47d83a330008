<?php
 $vistaPmesa = new controllerCocina();

?>
<script type="text/javascript">
	// ------RESETEAR TODO LOS PRODUCTOS PEDIDO EN LA MESA
	 function resetear()
		{
			if (confirm('confirme si, elimina todo los pedido e inicia los pedidos en 1?'))
				{		setTimeout('location.href="views/modules/ajaxResetea.php"',500);		}
		//alert("Entro en el else de placa");
			/*$("#destino").load("views/modules/ajaxPermiso.php", function()
				{  	alert("recibidos los datos por ajax Monte");    		 });*/
		}
	// ------RESETEAR TODO LOS PRODUCTOS PEDIDO EN LA MESA fin
	 function funcion_calcular()
		{
			var efectivo = document.calculo.efectivo.value;
			var total = document.calculo.total.value;
			/*
				var precio = document.calculo.precio.value;
				var cantid = document.calculo.cantid.value;
				var importe = document.calculo.importe.value;
			*/
			var calculo = efectivo-total;
			//alert ('El cambio: ' + calculo);
			document.calculo.cambio.value = calculo;
		}
	 setTimeout("document.location=document.location", 5000);

</script>
<style type="text/css">
	.tres{ background-color: red;}
	.uno{ background-color: yellow;}
	.dos{  background-color: green !important;}
</style>
<div class="row">
	<div class="col-md-12">
      	<form name="calculo" method="post" >
		  	<div ><h2>COMANDA COCINA </h2></div><br>
		  	<input type="hidden"  name="mesa" value="1">
		  	<input type="hidden"  name="pago" id="pago" value="1">
			<table border="1" class="table table-hover">
				<thead>
					<tr>
						<th>ORDEN</th>
						<th>MESA</th>
						<th>PRODUCTO</th>
						<th>CANTIDAD</th>
						<th>NOTA</th>
						<th>FECHA</th>
						<th>ESTADO</th>
					</tr>
				</thead>
				<tbody>
					<?php

					$vistaPmesa-> vistaCocinaController();
					$vistaPmesa -> terminadoController();

					?>

				</tbody>
			</table>
			<a class="btn btn-primary btn-lg"  role="button" onclick="resetear();" name="resetear" value="resetear">resetear &raquo;</a></p>
			<span id="destino" > </span>
		</form>
	</div>
</div>

