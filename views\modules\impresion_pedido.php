<?php
// Interfaz de impresión por categorías para pedidos
if (!isset($_GET['pedido_id'])) {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
    echo "<h4>❌ Error</h4>";
    echo "<p>No se especificó el ID del pedido</p>";
    echo "</div>";
    exit;
}

$pedido_id = intval($_GET['pedido_id']);

require_once '../../models/conexion.php';
require_once '../../models/crudPedidoImpresion.php';

// Obtener categorías del pedido
$datos_pedido = PedidoImpresion::obtenerCategoriasDelPedido($pedido_id);

if (isset($datos_pedido['error'])) {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
    echo "<h4>❌ Error</h4>";
    echo "<p>{$datos_pedido['error']}</p>";
    echo "</div>";
    exit;
}

if (empty($datos_pedido['categorias'])) {
    echo "<div style='background-color: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 15px 0;'>";
    echo "<h4>⚠️ Pedido Vacío</h4>";
    echo "<p>No hay productos en este pedido</p>";
    echo "</div>";
    exit;
}

echo "<h1>🖨️ Impresión de Pedido</h1>";
echo "<p><strong>Seleccionar categoría para imprimir</strong></p>";

// Información del pedido
echo "<div style='background-color: #d1ecf1; padding: 15px; border-left: 4px solid #17a2b8; margin-bottom: 20px;'>";
echo "<h4>📋 Información del Pedido</h4>";
echo "<p><strong>Pedido ID:</strong> $pedido_id</p>";
echo "<p><strong>Total de productos:</strong> {$datos_pedido['total_productos']}</p>";
echo "<p><strong>Categorías disponibles:</strong> " . count($datos_pedido['categorias']) . "</p>";
echo "<p><strong>Tu IP:</strong> " . $_SERVER['REMOTE_ADDR'] . "</p>";
echo "</div>";

// Procesar impresión
if (isset($_POST['imprimir_categoria'])) {
    $categoria_seleccionada = $_POST['categoria'];
    
    echo "<div style='border: 1px solid #007bff; padding: 20px; margin: 15px 0; border-radius: 8px;'>";
    echo "<h4>🖨️ Preparando impresión para: " . PedidoImpresion::obtenerNombreCategoria($categoria_seleccionada) . "</h4>";
    
    $contenido_impresion = PedidoImpresion::generarContenidoImpresion($pedido_id, $categoria_seleccionada);
    
    if ($contenido_impresion['success']) {
        echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0;'>";
        echo "<h5>✅ Contenido Generado</h5>";
        echo "<p><strong>Productos incluidos:</strong> {$contenido_impresion['total_productos']}</p>";
        echo "</div>";
        
        // Mostrar contenido que se va a imprimir
        echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h5>📄 Contenido a Imprimir:</h5>";
        echo "<pre style='background-color: white; padding: 10px; border: 1px solid #ddd; font-family: monospace; white-space: pre-wrap;'>";
        echo htmlspecialchars($contenido_impresion['contenido']);
        echo "</pre>";
        echo "</div>";
        
        // JavaScript para abrir ventana de impresión
        echo "<script>
        function abrirVentanaImpresion() {
            const contenido = `" . addslashes($contenido_impresion['contenido']) . "`;
            const ventana = window.open('', '_blank', 'width=400,height=600,scrollbars=yes');
            
            ventana.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Impresión - {$categoria_seleccionada}</title>
                    <style>
                        body { 
                            font-family: 'Courier New', monospace; 
                            font-size: 12px; 
                            margin: 20px; 
                            white-space: pre-wrap;
                        }
                        @media print {
                            body { margin: 0; }
                        }
                    </style>
                </head>
                <body>
                    \${contenido}
                    <script>
                        window.onload = function() {
                            window.print();
                        };
                    </scr\ipt>
                </body>
                </html>
            `);
            
            ventana.document.close();
            
            // Registrar log de impresión
            fetch('../../api/registrar_impresion.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    pedido_id: $pedido_id,
                    categoria: '$categoria_seleccionada',
                    metodo: 'ventana_impresion'
                })
            });
        }
        </script>";
        
        echo "<div style='text-align: center; margin: 20px 0;'>";
        echo "<button onclick='abrirVentanaImpresion()' style='background-color: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold; font-size: 16px;'>🖨️ ABRIR VENTANA DE IMPRESIÓN</button>";
        echo "</div>";
        
        echo "<div style='background-color: #e7f3ff; padding: 15px; border-left: 4px solid #007bff; margin: 15px 0;'>";
        echo "<h5>💡 Instrucciones:</h5>";
        echo "<ol>";
        echo "<li>Se abrirá una nueva ventana con el contenido</li>";
        echo "<li>La ventana se enviará automáticamente a imprimir</li>";
        echo "<li>Selecciona tu impresora en el diálogo</li>";
        echo "<li>Confirma la impresión</li>";
        echo "</ol>";
        echo "</div>";
        
    } else {
        echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
        echo "<h5>❌ Error</h5>";
        echo "<p>{$contenido_impresion['error']}</p>";
        echo "</div>";
    }
    
    echo "</div>";
}

// Mostrar categorías disponibles con botones
echo "<h2>📂 Categorías Disponibles para Impresión</h2>";

foreach ($datos_pedido['categorias'] as $categoria => $info) {
    $color_categoria = [
        'bar' => '#17a2b8',
        'cocina' => '#28a745', 
        'asados' => '#dc3545',
        'test' => '#ffc107'
    ];
    
    $color = $color_categoria[$categoria] ?? '#6c757d';
    
    echo "<div style='background-color: white; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 5px solid $color; box-shadow: 0 2px 4px rgba(0,0,0,0.1);'>";
    
    echo "<h4 style='color: $color; margin-bottom: 15px;'>{$info['nombre']}</h4>";
    
    echo "<div style='margin-bottom: 15px;'>";
    echo "<p><strong>Productos:</strong> {$info['total_productos']} items</p>";
    echo "<p><strong>Total:</strong> $" . number_format($info['total_precio'], 0) . "</p>";
    echo "</div>";
    
    // Lista de productos
    echo "<div style='background-color: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 15px;'>";
    echo "<h6>📋 Productos incluidos:</h6>";
    echo "<ul style='margin: 5px 0; padding-left: 20px;'>";
    foreach ($info['productos'] as $producto) {
        echo "<li>{$producto['producto_nombre']} x{$producto['cantidad']} - $" . number_format($producto['subtotal'], 0) . "</li>";
    }
    echo "</ul>";
    echo "</div>";
    
    // Botón de impresión
    echo "<form method='POST' style='margin: 0;'>";
    echo "<input type='hidden' name='categoria' value='$categoria'>";
    echo "<button type='submit' name='imprimir_categoria' style='background-color: $color; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold; width: 100%;'>🖨️ IMPRIMIR {$info['nombre']}</button>";
    echo "</form>";
    
    echo "</div>";
}

// Información adicional
echo "<h2>💡 Información del Sistema</h2>";
echo "<div style='background-color: #e7f3ff; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>🔧 Cómo Funciona:</h4>";
echo "<ol>";
echo "<li><strong>Selección automática:</strong> Solo aparecen las categorías que tienen productos</li>";
echo "<li><strong>Impresión separada:</strong> Cada categoría se imprime independientemente</li>";
echo "<li><strong>Control manual:</strong> El operario decide cuándo y qué imprimir</li>";
echo "<li><strong>Ventana de impresión:</strong> Se abre una ventana optimizada para imprimir</li>";
echo "<li><strong>Compatible:</strong> Funciona desde cualquier dispositivo</li>";
echo "</ol>";

echo "<h4>✅ Ventajas:</h4>";
echo "<ul>";
echo "<li>🎯 <strong>Simple:</strong> Solo botones por categoría</li>";
echo "<li>🔒 <strong>Confiable:</strong> Usa el sistema de impresión del navegador</li>";
echo "<li>📱 <strong>Universal:</strong> Funciona en móviles y PC</li>";
echo "<li>⚡ <strong>Rápido:</strong> Sin configuraciones complejas</li>";
echo "</ul>";
echo "</div>";

// Botones de navegación
echo "<p style='margin-top: 30px;'>";
echo "<a href='javascript:history.back()' style='background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>← Volver</a>";
echo "<a href='index.php?action=registroPmesa&ida=1' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🪑 Nueva Mesa</a>";
echo "<a href='pantallaCocina?categoria=cocina' style='background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🍳 Pantalla Cocina</a>";
echo "</p>";
?>
