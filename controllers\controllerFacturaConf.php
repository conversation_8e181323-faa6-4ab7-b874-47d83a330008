
<?php
ob_start();
class controllerFacturaConf extends Mv<PERSON><PERSON><PERSON>roller
{
	#REGISTRO DE ROLES
	#------------------------------------
	 public function registroFacturaConfController()
		{//echo "<script>alert('Entro Controller ".$_POST["nombre"]." no')</script>";
		 if(isset($_POST["nombre"]))
			{
				$datosController =array('nombre'=>$_POST["nombre"],
										'nit'=>$_POST["nit"],
										'regimen'=>$_POST["regimen"],
										'ciudad'=>$_POST["ciudad"],
										'direccion_fisica'=>$_POST["direccion_fisica"],
										'web'=>$_POST["web"],
										'email'=>$_POST["email"],
										'telefono'=>$_POST["telefono"],
										'dian'=>$_POST["dian"],
										);
				//echo "<script>alert('Entro Controller ".$datosController['nombre']." if')</script>";
				$respuesta = DatosFacturaConf::registroFacturaConfModel($datosController, "factura_conf");
            /**/
				if($respuesta == "success")
					{	header("location:index.php?action=okFC");	}
				else
					{	header("location:registroFacturaConf");	}
			}
		}
	#------------------------------------
	#VISTA
	#------------------------------------
	 public function vistaFacturaConfController()
		{
		 $respuesta = DatosFacturaConf::vistaFacturaConfModel("factura_conf");
		 foreach($respuesta as $row => $item)
			{
				echo'<tr>
					<td>'.$item["nombre"].'</td>
					<td>'.$item["nit"].'</td>
					<td>'.$item["regimen"].'</td>
					<td>'.$item["ciudad"].'</td>
					<td>'.$item["direccion_fisica"].'</td>
					<td>'.$item["email"].'</td>
					<td>'.$item["telefono"].'</td>
					<td>'.$item["dian"].'</td>
					<td><a href="index.php?action=editarFacturaConf&id='.$item["id"].'">Editar</a></td>
					<td> <a href="index.php?action=facturaConf&idBorrar='.$item["id"].'">Borrar</a></td>
				</tr>';
			}
		}
	#------------------------------------
	#EDITAR
	#------------------------------------
	 public function editarFacturaConfController()
		{
			$datosController = $_GET["id"];
			$respuesta = DatosFacturaConf::editarFacturaConfModel($datosController, "factura_conf");
			$_SESSION["erol"]=$respuesta;
			echo' <table>
				<thead>
		     		 <tr > <td colspan="2" ></td> </tr>
	    		</thead>
				<tbody>
					<tr>
						<td>Nombre :</td>
						<td><input type="text" value="'.$respuesta["nombre"].'" name="nombre" required></td>
					</tr>
					<tr>
						<td></td>
						<td><input type="text" value="'.$respuesta["nit"].'" name="nit" required></td>
					</tr>
					<tr>
						<td></td>
						<td><input type="text" value="'.$respuesta["regimen"].'" name="regimen" required></td>
					</tr>
					<tr>
						<td></td>
						<td><input type="text" value="'.$respuesta["ciudad"].'" name="ciudad" required></td>
					</tr>
					<tr>
						<td></td>
						<td><input type="text" value="'.$respuesta["direccion_fisica"].'" name="direccion_fisica" required></td>
					</tr>
					<tr>
						<td></td>
						<td><input type="text" value="'.$respuesta["web"].'" name="web" required></td>
					</tr>
					<tr>
						<td></td>
						<td><input type="text" value="'.$respuesta["email"].'" name="email" required></td>
					</tr>
					<tr>
						<td></td>
						<td><input type="text" value="'.$respuesta["telefono"].'" name="telefono" required></td>
					</tr>
					<tr>
						<td></td>
						<td><input type="text" value="'.$respuesta["dian"].'" name="dian" required></td>
					</tr>
				</tbody>
				<thead>
		     		 <tr > <td colspan="2" ></td> </tr>
	    		</thead>
			</table>

			 <input type="hidden" value="'.$respuesta["id"].'" name="idFacturaConfEditar" required>
				 <input type="submit" value="Actualizar">';
		}
	#------------------------------------
	#ACTUALIZAR
	#------------------------------------
	 public function actualizarFacturaConfController()
		{//echo "<script>alert('Entro Controller Actualizar FacturaConf')</script>";

			if(isset($_POST["nombre"]))
				{
					$datosController = array(  "nombre"=>$_POST["nombre"],
											  "nit"=>$_POST["nit"],
											  "regimen"=>$_POST["regimen"],
											  "ciudad"=>$_POST["ciudad"],
											  "direccion_fisica"=>$_POST["direccion_fisica"],
											  "web"=>$_POST["web"],
											  "email"=>$_POST["email"],
											  "telefono"=>$_POST["telefono"],
											  "dian"=>$_POST["dian"],

												"id"=>$_POST["idFacturaConfEditar"]);
					$respuesta = DatosFacturaConf::actualizarFacturaConfModel($datosController, "factura_conf");
					if($respuesta == "success")
						{	header("location:facturaConf");	}
					else
						{	echo "error";	}
				}
		}
	#------------------------------------
	#BORRAR
	#------------------------------------
	 public function borrarFacturaConfController()
		{
			if(isset($_GET["idBorrar"]))
				{
					$datosController = $_GET["idBorrar"];
					$respuesta = DatosFacturaConf::borrarFacturaConfModel($datosController, "factura_conf");
					if($respuesta == "success")
						{	header("location:facturaConf");	}
				}
		}
	#---------------------------------

}
