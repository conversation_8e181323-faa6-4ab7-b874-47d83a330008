<!DOCTYPE html>
<html>
<head>
    <title>Test Modelo Debug - Mesa 10</title>
</head>
<body>

<h1>🔍 Test Modelo Debug - Mesa 10</h1>

<p>Este test ejecuta directamente el modelo <code>facturaajaxModel</code> con logging detallado para identificar exactamente dónde falla.</p>

<div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;">
    <h4>⚠️ Información del Test:</h4>
    <p>Este test ejecutará el modelo completo de facturación con 26 pedidos.</p>
    <p>El logging detallado mostrará cada paso del proceso.</p>
    <p><strong>CUIDADO:</strong> Este test puede facturar realmente la mesa si no hay errores.</p>
</div>

<button onclick="testModeloDebug()" style="background: #dc3545; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;">
🔍 Test Modelo Debug (CUIDADO)
</button>

<button onclick="verLogsServidor()" style="background: #6c757d; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; margin-left: 10px;">
📋 Ver Logs del Servidor
</button>

<div id="resultado" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border: 1px solid #ddd; border-radius: 5px;"></div>

<script>
function testModeloDebug() {
    if (!confirm('⚠️ ADVERTENCIA: Este test ejecutará el modelo completo de facturación.\n\n¿Estás seguro de continuar?\n\nEsto puede facturar realmente la mesa 10.')) {
        return;
    }
    
    document.getElementById('resultado').innerHTML = '<div style="color: blue;">⏳ Ejecutando test del modelo con logging detallado...</div>';
    
    fetch('ajaxFactura_debug2.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            efectivo: 50000,
            bancolombia: 0,
            nequi: 0,
            daviplata: 0,
            tarjeta: 0,
            pago: 1,
            pcedula: 'MARCO',
            totalDescuento: 0,
            total: 50000,
            propina: 0,
            mesa: 10,
            tipoTarjeta: 'credito',
            optimizada: true
        })
    })
    .then(response => {
        console.log('Status:', response.status);
        console.log('Status Text:', response.statusText);
        return response.text();
    })
    .then(text => {
        console.log('Respuesta completa:', text);
        
        let html = '<h4>📄 Resultado del Test Modelo Debug:</h4>';
        html += '<p><strong>Status:</strong> ' + (text ? 'Respuesta recibida' : 'Sin respuesta') + '</p>';
        
        // Extraer comentarios de debug2
        const debug2Matches = text.match(/<!-- DEBUG2: [^>]+ -->/g);
        if (debug2Matches) {
            html += '<h5>🔍 Pasos del Controlador:</h5>';
            html += '<ul>';
            debug2Matches.forEach(match => {
                const mensaje = match.replace(/<!-- DEBUG2: /, '').replace(/ -->/, '');
                html += '<li>' + mensaje + '</li>';
            });
            html += '</ul>';
        }
        
        // Buscar errores específicos
        if (text.includes('ERROR') || text.includes('Error')) {
            html += '<div style="background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;">';
            html += '<h5 style="color: #721c24;">❌ Error Detectado:</h5>';
            
            // Extraer mensajes de error
            const errorMatches = text.match(/Error [^:]*: [^<\n]+/g);
            if (errorMatches) {
                errorMatches.forEach(error => {
                    html += '<p style="color: #721c24;"><strong>' + error + '</strong></p>';
                });
            }
            html += '</div>';
        }
        
        // Buscar alertas JavaScript
        if (text.includes('alert(')) {
            html += '<div style="background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;">';
            html += '<h5 style="color: #856404;">⚠️ Alertas del Sistema:</h5>';
            
            const alertMatches = text.match(/alert\('([^']+)'\)/g);
            if (alertMatches) {
                alertMatches.forEach(alert => {
                    const mensaje = alert.replace(/alert\('/, '').replace(/'\)/, '');
                    html += '<p style="color: #856404;"><strong>' + mensaje + '</strong></p>';
                });
            }
            html += '</div>';
        }
        
        html += '<h5>📄 Respuesta Completa:</h5>';
        html += '<pre style="background: #fff; padding: 10px; border: 1px solid #ddd; border-radius: 3px; white-space: pre-wrap; max-height: 400px; overflow-y: auto;">' + text + '</pre>';
        
        if (text.includes('success_debug2')) {
            html += '<div style="color: green; font-weight: bold; font-size: 18px;">✅ ÉXITO: Modelo ejecutado correctamente</div>';
            html += '<p>La facturación se completó. Verifica el estado de la mesa.</p>';
        } else if (text.includes('Error') || text.includes('error')) {
            html += '<div style="color: red; font-weight: bold; font-size: 18px;">❌ ERROR detectado en el modelo</div>';
            html += '<p>Revisa los logs del servidor para más detalles.</p>';
        } else {
            html += '<div style="color: orange; font-weight: bold; font-size: 18px;">⚠️ Respuesta inesperada</div>';
        }
        
        document.getElementById('resultado').innerHTML = html;
    })
    .catch(error => {
        console.error('Error completo:', error);
        
        let html = '<h4>❌ Error en Test Modelo:</h4>';
        html += '<p><strong>Error:</strong> ' + error.message + '</p>';
        html += '<p>Este error indica un problema en el modelo facturaajaxModel</p>';
        
        document.getElementById('resultado').innerHTML = html;
    });
}

function verLogsServidor() {
    document.getElementById('resultado').innerHTML = '<div style="color: blue;">📋 Los logs detallados del servidor se encuentran en:</div>';
    document.getElementById('resultado').innerHTML += '<ul>';
    document.getElementById('resultado').innerHTML += '<li><strong>Error Log PHP:</strong> Revisa el error_log del servidor</li>';
    document.getElementById('resultado').innerHTML += '<li><strong>Logs específicos:</strong> Busca entradas que contengan "facturaajaxModel"</li>';
    document.getElementById('resultado').innerHTML += '<li><strong>Pasos del modelo:</strong> Cada paso está loggeado con detalles</li>';
    document.getElementById('resultado').innerHTML += '</ul>';
    document.getElementById('resultado').innerHTML += '<p>Los logs te dirán exactamente en qué línea del modelo falla el proceso.</p>';
}
</script>

<h3>📋 Información de Debug:</h3>
<div style="background: #d1ecf1; padding: 15px; border-radius: 5px;">
    <h4>🔍 Qué buscar en los logs:</h4>
    <ul>
        <li><strong>"facturaajaxModel INICIADO"</strong> - Inicio del proceso</li>
        <li><strong>"Productos a procesar: X"</strong> - Cantidad de productos</li>
        <li><strong>"Procesando producto X"</strong> - Cada producto individual</li>
        <li><strong>"Conectando a base de datos"</strong> - Conexión DB</li>
        <li><strong>"Iniciando transacción"</strong> - Inicio de transacción</li>
        <li><strong>"Pedido insertado con ID"</strong> - Inserción exitosa</li>
        <li><strong>"ERROR"</strong> - Cualquier error encontrado</li>
    </ul>
</div>

<br><a href="index.php?action=registroPmesa&ida=10" style="background: #007bff; color: white; padding: 10px; text-decoration: none;">🔙 Volver a Mesa 10</a>
<br><a href="test_debug2_controlador.php" style="background: #6c757d; color: white; padding: 10px; text-decoration: none; margin: 5px;">🔙 Volver a Debug2</a>

</body>
</html>
