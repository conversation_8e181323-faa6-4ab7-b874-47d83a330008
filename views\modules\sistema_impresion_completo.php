<?php
// Sistema de Impresión Completo - Celulares + Categorías
echo "<h1>📱🖨️ Sistema de Impresión Completo</h1>";
echo "<p><strong>Solución integral:</strong> Celulares + Impresión por categoría</p>";

echo "<div style='background-color: #d1ecf1; padding: 15px; border-left: 4px solid #bee5eb; margin: 20px 0;'>";
echo "<h3>🎯 Objetivos del Sistema:</h3>";
echo "<ul>";
echo "<li>✅ <strong>Funcionar en celulares</strong> (Android/iOS)</li>";
echo "<li>✅ <strong>Impresión automática por categoría</strong> de productos</li>";
echo "<li>✅ <strong>Proxy accesible</strong> desde cualquier dispositivo</li>";
echo "<li>✅ <strong>Configuración dinámica</strong> de IP</li>";
echo "</ul>";
echo "</div>";

// Verificar categorías actuales
echo "<h2>📋 Configuración Actual de Categorías:</h2>";
try {
    require_once '../../models/conexion.php';
    $conexion = new Conexion();
    $pdo = $conexion->conectar();
    
    // Obtener categorías
    $stmt = $pdo->query("SELECT DISTINCT categoria FROM productos WHERE categoria IS NOT NULL AND categoria != '' ORDER BY categoria");
    $categorias = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    // Obtener impresoras
    $stmt = $pdo->query("SELECT * FROM impresoras ORDER BY nombre");
    $impresoras = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<div style='display: flex; gap: 20px; margin: 15px 0;'>";
    
    // Mostrar categorías
    echo "<div style='flex: 1; border: 1px solid #ccc; padding: 15px; border-radius: 5px;'>";
    echo "<h4>🏷️ Categorías de Productos:</h4>";
    if ($categorias) {
        echo "<ul>";
        foreach ($categorias as $categoria) {
            echo "<li><strong>$categoria</strong></li>";
        }
        echo "</ul>";
    } else {
        echo "<p>⚠️ No se encontraron categorías</p>";
    }
    echo "</div>";
    
    // Mostrar impresoras
    echo "<div style='flex: 1; border: 1px solid #ccc; padding: 15px; border-radius: 5px;'>";
    echo "<h4>🖨️ Impresoras Disponibles:</h4>";
    if ($impresoras) {
        echo "<ul>";
        foreach ($impresoras as $imp) {
            $estado = $imp['activa'] ? '✅' : '❌';
            echo "<li>$estado <strong>{$imp['nombre']}</strong> ({$imp['ip']}:{$imp['puerto']})</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>⚠️ No se encontraron impresoras</p>";
    }
    echo "</div>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p>⚠️ Error al consultar datos: " . $e->getMessage() . "</p>";
}

// Configuración de mapeo categoría -> impresora
echo "<h2>⚙️ Configurar Mapeo Categoría → Impresora:</h2>";

if (isset($_POST['guardar_mapeo'])) {
    try {
        // Crear tabla de mapeo si no existe
        $pdo->exec("CREATE TABLE IF NOT EXISTS categoria_impresora (
            id INT AUTO_INCREMENT PRIMARY KEY,
            categoria VARCHAR(100) NOT NULL,
            impresora VARCHAR(50) NOT NULL,
            activo BOOLEAN DEFAULT TRUE,
            fecha_creacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_categoria (categoria)
        )");
        
        // Limpiar mapeos anteriores
        $pdo->exec("DELETE FROM categoria_impresora");
        
        // Insertar nuevos mapeos
        $stmt = $pdo->prepare("INSERT INTO categoria_impresora (categoria, impresora) VALUES (?, ?)");
        
        foreach ($_POST as $key => $value) {
            if (strpos($key, 'categoria_') === 0 && !empty($value)) {
                $categoria = str_replace('categoria_', '', $key);
                $categoria = str_replace('_', ' ', $categoria);
                $stmt->execute([$categoria, $value]);
            }
        }
        
        echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0;'>";
        echo "<h4>✅ Mapeo Guardado Exitosamente</h4>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
        echo "<h4>❌ Error al guardar mapeo</h4>";
        echo "<p>Error: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
}

// Obtener mapeo actual
$mapeo_actual = [];
try {
    $stmt = $pdo->query("SELECT * FROM categoria_impresora WHERE activo = TRUE");
    $mapeos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($mapeos as $mapeo) {
        $mapeo_actual[$mapeo['categoria']] = $mapeo['impresora'];
    }
} catch (Exception $e) {
    // Tabla no existe aún
}

echo "<form method='POST' style='background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>🔗 Asignar Categorías a Impresoras:</h4>";

if ($categorias && $impresoras) {
    foreach ($categorias as $categoria) {
        $categoria_key = str_replace(' ', '_', $categoria);
        $selected_impresora = $mapeo_actual[$categoria] ?? '';
        
        echo "<div style='margin: 10px 0; display: flex; align-items: center; gap: 10px;'>";
        echo "<label style='min-width: 150px; font-weight: bold;'>$categoria:</label>";
        echo "<select name='categoria_$categoria_key' style='padding: 5px; border: 1px solid #ccc; border-radius: 3px;'>";
        echo "<option value=''>-- Seleccionar Impresora --</option>";
        
        foreach ($impresoras as $imp) {
            $selected = ($selected_impresora === $imp['nombre']) ? 'selected' : '';
            echo "<option value='{$imp['nombre']}' $selected>{$imp['nombre']}</option>";
        }
        
        echo "</select>";
        echo "</div>";
    }
    
    echo "<button type='submit' name='guardar_mapeo' style='background-color: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin-top: 15px; cursor: pointer;'>💾 Guardar Mapeo</button>";
} else {
    echo "<p>⚠️ Necesitas tener categorías y impresoras configuradas primero</p>";
}

echo "</form>";

// Proxy mejorado para celulares
echo "<h2>📱 Proxy Mejorado para Celulares:</h2>";
echo "<div style='border: 1px solid #007bff; padding: 20px; margin: 15px 0; border-radius: 8px;'>";
echo "<h3>🌐 Código del Proxy Mejorado</h3>";
echo "<p>Este proxy incluye:</p>";
echo "<ul>";
echo "<li>✅ <strong>CORS completo</strong> para celulares</li>";
echo "<li>✅ <strong>Headers móviles</strong> optimizados</li>";
echo "<li>✅ <strong>Impresión por categoría</strong> automática</li>";
echo "<li>✅ <strong>API REST</strong> para aplicaciones móviles</li>";
echo "</ul>";

echo "<pre style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; font-size: 11px; max-height: 400px;'>";
echo htmlspecialchars('const express = require("express");
const net = require("net");
const cors = require("cors");
const mysql = require("mysql2/promise");

const app = express();
const PORT = 3000;

// Configuración de base de datos
const DB_CONFIG = {
    host: "localhost",
    user: "root", // Ajustar según tu configuración
    password: "", // Ajustar según tu configuración
    database: "ewogjwfm_macarena"
};

// Middleware completo para celulares
app.use(cors({
    origin: "*",
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization", "X-Requested-With"],
    credentials: true
}));

app.use(express.json({ limit: "10mb" }));
app.use(express.raw({ type: "*/*", limit: "10mb" }));
app.use(express.urlencoded({ extended: true }));

// Headers adicionales para móviles
app.use((req, res, next) => {
    res.header("Access-Control-Allow-Origin", "*");
    res.header("Access-Control-Allow-Methods", "GET,PUT,POST,DELETE,OPTIONS");
    res.header("Access-Control-Allow-Headers", "Content-Type, Authorization, Content-Length, X-Requested-With");
    
    // Responder a preflight requests
    if (req.method === "OPTIONS") {
        res.sendStatus(200);
    } else {
        next();
    }
});

// Configuración de impresoras
const IMPRESORAS = {
    "COCINA": { ip: "**************", puerto: 9100 },
    "BAR": { ip: "**************", puerto: 9100 },
    "ASADOS": { ip: "**************", puerto: 9100 }
};

// Función para obtener mapeo de categorías
async function obtenerMapeoCategoria() {
    try {
        const connection = await mysql.createConnection(DB_CONFIG);
        const [rows] = await connection.execute(
            "SELECT categoria, impresora FROM categoria_impresora WHERE activo = TRUE"
        );
        await connection.end();
        
        const mapeo = {};
        rows.forEach(row => {
            mapeo[row.categoria] = row.impresora;
        });
        return mapeo;
    } catch (error) {
        console.error("Error obteniendo mapeo:", error);
        return {};
    }
}

// Función para enviar a impresora
function enviarAImpresora(nombreImpresora, datos, res) {
    const config = IMPRESORAS[nombreImpresora.toUpperCase()];
    if (!config) {
        return res.status(404).json({ 
            error: `Impresora ${nombreImpresora} no encontrada`,
            impresoras_disponibles: Object.keys(IMPRESORAS)
        });
    }

    const client = new net.Socket();
    let respuesta = "";

    client.setTimeout(10000); // 10 segundos timeout

    client.connect(config.puerto, config.ip, () => {
        console.log(`✅ Conectado a ${nombreImpresora} (${config.ip}:${config.puerto})`);
        
        if (datos && datos.length > 0) {
            client.write(datos);
            console.log(`📄 Enviado ${datos.length} bytes a ${nombreImpresora}`);
        }
    });

    client.on("data", (data) => {
        respuesta += data.toString();
    });

    client.on("close", () => {
        console.log(`🔌 Conexión cerrada con ${nombreImpresora}`);
        res.json({ 
            success: true, 
            impresora: nombreImpresora,
            ip: config.ip,
            puerto: config.puerto,
            respuesta: respuesta || "Enviado correctamente",
            timestamp: new Date().toISOString()
        });
    });

    client.on("error", (err) => {
        console.error(`❌ Error con ${nombreImpresora}:`, err.message);
        res.status(500).json({ 
            error: `Error conectando a ${nombreImpresora}: ${err.message}`,
            impresora: nombreImpresora,
            ip: config.ip,
            puerto: config.puerto
        });
    });

    client.on("timeout", () => {
        console.error(`⏰ Timeout con ${nombreImpresora}`);
        client.destroy();
        res.status(408).json({ 
            error: `Timeout conectando a ${nombreImpresora}`,
            impresora: nombreImpresora,
            ip: config.ip
        });
    });
}

// Rutas directas para impresoras
app.all("/cocina", (req, res) => {
    enviarAImpresora("COCINA", req.body, res);
});

app.all("/bar", (req, res) => {
    enviarAImpresora("BAR", req.body, res);
});

app.all("/asados", (req, res) => {
    enviarAImpresora("ASADOS", req.body, res);
});

// Ruta para imprimir por categoría
app.post("/imprimir-categoria", async (req, res) => {
    try {
        const { categoria, datos } = req.body;
        
        if (!categoria) {
            return res.status(400).json({ error: "Categoría requerida" });
        }
        
        const mapeo = await obtenerMapeoCategoria();
        const impresora = mapeo[categoria];
        
        if (!impresora) {
            return res.status(404).json({ 
                error: `No hay impresora configurada para la categoría: ${categoria}`,
                mapeo_disponible: mapeo
            });
        }
        
        console.log(`📋 Imprimiendo categoría "${categoria}" en impresora "${impresora}"`);
        enviarAImpresora(impresora, datos, res);
        
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Ruta para obtener configuración
app.get("/config", async (req, res) => {
    try {
        const mapeo = await obtenerMapeoCategoria();
        res.json({
            impresoras: IMPRESORAS,
            mapeo_categorias: mapeo,
            proxy_info: {
                version: "2.0",
                mobile_optimized: true,
                timestamp: new Date().toISOString()
            }
        });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Ruta de estado
app.get("/status", (req, res) => {
    res.json({
        proxy: "Proxy de Impresoras Macarena Mobile",
        version: "2.0",
        mobile_support: true,
        impresoras: IMPRESORAS,
        endpoints: ["/cocina", "/bar", "/asados", "/imprimir-categoria", "/config"],
        timestamp: new Date().toISOString()
    });
});

// Ruta principal
app.get("/", (req, res) => {
    res.json({
        mensaje: "🖨️ Proxy de Impresoras Macarena - Versión Móvil",
        version: "2.0",
        mobile_optimized: true,
        rutas_directas: ["/cocina", "/bar", "/asados"],
        rutas_inteligentes: ["/imprimir-categoria"],
        configuracion: ["/config", "/status"],
        impresoras: Object.keys(IMPRESORAS)
    });
});

// Iniciar servidor
app.listen(PORT, "0.0.0.0", () => {
    console.log(`🚀 Proxy móvil de impresoras ejecutándose en puerto ${PORT}`);
    console.log(`📡 Accesible desde: http://***********:${PORT}`);
    console.log(`📱 Optimizado para celulares y tablets`);
    console.log(`🖨️ Impresoras configuradas:`, Object.keys(IMPRESORAS));
    console.log(`🏷️ Soporte para impresión por categoría activado`);
});');
echo "</pre>";

echo "<h4>📦 Dependencias adicionales:</h4>";
echo "<code>npm install express cors mysql2</code>";
echo "</div>";

// Botones de navegación
echo "<p style='margin-top: 30px;'>";
echo "<a href='proxy_dinamico.php' style='background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>← Proxy Dinámico</a>";
echo "<a href='test_impresion_categoria.php' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🧪 Test Impresión por Categoría</a>";
echo "</p>";
?>
