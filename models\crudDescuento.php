<?php
require_once "conexion.php";
class DatosDescuento extends Conexion
{//(id, persona_id, porcentaje, codigo, Activo, fechaM)
	#REGISTRO DE DESCUENTO
	#-------------------------------------
	 public static function registroDescuentoModel($datosModel, $tabla)
		{//echo "<script>alert('Entro CRUD ".$datosModel['nombre']." no')</script>";
		 date_default_timezone_set("America/Bogota");
		 $fecha_creado=strftime("%Y-%m-%d %H:%M:%S");
		 $stmt = Conexion::conectar(); 
		 try 
			{
			 $stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);  
			 $stmt->beginTransaction();	
			 $consultar="INSERT INTO $tabla (persona_id, porcentaje, codigo, Activo, fechaM) VALUES (".$datosModel["usuario"].", '".$datosModel["nombre"]."', '".$datosModel["codigo"]."', '".$datosModel["activo"]."', '".$fecha_creado."')";
			echo "<br>".$consultar."<br>";
				$stmt->exec($consultar);				
				$idTablatura = $stmt->lastInsertId();	//ultimo id 
				$cambio=$idTablatura.'-porcentaje'.$datosModel["nombre"].'-codigo'.$datosModel["codigo"].'-Activo'.$datosModel["activo"].'-Fecha'.$fecha_creado;	
				$consultarA="INSERT INTO auditoria(tabla, accion, persona_id, fecha_accion, cambio)  
				VALUES ('".$tabla."', 'INSERT', ".$_SESSION["usuario"].", '$fecha_creado', '$cambio')"; 
				echo "<br>".$consultarA."<br>";
				$stmt->exec($consultarA);					
				/**/$stmt->commit();
				//echo "<script>alert('fin de cruD try que pasa');</script>";
				return "success"; 
				$stmt->close();	
			 }
			catch (Exception $e) 
			 {	
			 	echo "<script>alert('catch error');</script>";
			 	$stmt->rollBack(); 
				print "Error!: ".$e->getMessage()."</br>";
				return "Error!: ".$e->getMessage()."</br>";
			 }						

		}
	#-------------------------------------
	#VISTA DESCUENTO
	#-------------------------------------
	 public static function vistaDescuentoModel($tabla)
		{
			$consulta = "SELECT id, persona_id, porcentaje, codigo, Activo, fechaM FROM $tabla";
			$stmt = Conexion::conectar()->prepare($consulta);	
			$stmt->execute();								
			return $stmt->fetchAll();
			$stmt->close();							
		}
	#------------------------------------
	#EDITAR DESCUENTO
	#-------------------------------------
	 public static function editarDescuentoModel($datosModel, $tabla)
		{
			$stmt = Conexion::conectar()->prepare("SELECT id, persona_id, porcentaje, codigo, Activo, fechaM FROM $tabla WHERE id = :id");
			$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);	
			$stmt->execute();
			$r=$stmt->fetch();
			return $r;
			$stmt->close();

		}
	#-------------------------------------
	#ACTUALIZAR DESCUENTO $_SESSION["erol"]
	#-------------------------------------
	 public static function actualizarDescuentoModel($datosModel, $tabla)
		{ 	//echo "<script>alert('Entro Actualizar Producto')</script>";
			date_default_timezone_set("America/Bogota");
			$fecha_creado=strftime("%Y-%m-%d %H:%M:%S");
			$stmt = Conexion::conectar();			
		 try {	//echo '<script>alert("entro CRUZ");</script>';
				$stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
				$stmt->beginTransaction();
				$consultar = "UPDATE $tabla SET porcentaje = '".$datosModel["nombre"]."', codigo = '".$datosModel["codigo"]."' , Activo = '".$datosModel["activo"]."' WHERE id = ".$datosModel["id"];
			//echo "<br".$consultar."<br";				
				if ($_SESSION["edescuento"]["porcentaje"]!=$datosModel["nombre"]) 
					{	$camb='perfil:'.$_SESSION["edescuento"]["porcentaje"].'='.$datosModel["nombre"];	}
				elseif ($_SESSION["edescuento"]["codigo"]!=$datosModel["codigo"]) 
					{ 	$camb='perfil:'.$_SESSION["edescuento"]["codigo"].'='.$datosModel["codigo"];	 } 
				elseif ($_SESSION["edescuento"]["Activo"]!=$datosModel["activo"]) 
					{ 	$camb='perfil:'.$_SESSION["edescuento"]["Activo"].'='.$datosModel["activo"];	 }
				else{$camb=0;}
			$stmt->exec($consultar);				
				$idTablatura = $stmt->lastInsertId();	//ultimo id 
				$cambio=$datosModel["idEditar"].'-'.$camb;	
				$consultarA="INSERT INTO auditoria(tabla, accion, persona_id, fecha_accion, cambio)  
				VALUES ('".$tabla."', 'UPDATE', ".$_SESSION["usuario"].", '$fecha_creado', '$cambio')"; 
				//echo "<br".$consultarA."<br";
				$stmt->exec($consultarA);								
				$stmt->commit();
				//echo "<script>alert('fin de cruD try que pasa');</script>";
				return "success"; 
				$stmt->close();	
			 }
			catch (Exception $e) 
			 {	
			 	echo "<script>alert('catch error');</script>";
			 	$stmt->rollBack(); 
				echo "Error!: ".$e->getMessage()."</br>";
				return "Error!: ".$e->getMessage()."</br>";
			 }

		}
	#-------------------------------------
	#BORRAR DESCUENTO
	#------------------------------------
	 public static function borrarDescuentoModel($datosModel, $tabla)
		{
			date_default_timezone_set("America/Bogota");
			$fecha_creado=strftime("%Y-%m-%d %H:%M:%S");
			$stmt = Conexion::conectar(); 
		 try 
			{//echo '<script>alert("entro CRUD");</script>';	
			 	$stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);  
				$stmt->beginTransaction();	
			$consultar = "DELETE FROM $tabla WHERE id =".$datosModel;				
			    echo "<br".$consultar."<br";
				$stmt->exec($consultar);
				$cambio=$datosModel;	
				$consultarA="INSERT INTO auditoria(tabla, accion, persona_id, fecha_accion, cambio)  
				VALUES ('".$tabla."', 'DELETE', ".$_SESSION["usuario"].", '$fecha_creado', '$cambio')"; 
				//echo "<br".$consultarA."<br";
				$stmt->exec($consultarA);								
				$stmt->commit();
				//echo "<script>alert('fin de cruD try que pasa');</script>";
				return "success"; 
				$stmt->close();	
			 }
			catch (Exception $e) 
			 {	
			 	echo "<script>alert('catch error');</script>";
			 	$stmt->rollBack(); 
				echo "Error!: ".$e->getMessage()."</br>";
				return "Error!: ".$e->getMessage()."</br>";
			 }						
		}
	#----------------------------------------------
}