<?php

session_start();

if(!isset($_SESSION["validar"]) || !$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "models/conexion.php";

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Test SQL Directo</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
</head>
<body>

<div class="container">
    <h2>🧪 Test: SQL Directo para Pedido ID 8</h2>
    <hr>
    
    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">🔍 Test 1: Verificar condiciones WHERE</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                echo "<h4>Verificando condiciones de la consulta UPDATE:</h4>";
                
                // Test 1: Verificar que el pedido cumple las condiciones
                $stmt = Conexion::conectar()->prepare("
                    SELECT id, estado 
                    FROM pedidos 
                    WHERE id = 8 AND estado = 'enviado'
                ");
                $stmt->execute();
                $resultado = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($resultado) {
                    echo "<div class='alert alert-success'>✅ El pedido ID 8 SÍ cumple las condiciones WHERE</div>";
                    echo "<p>ID: {$resultado['id']}, Estado: '{$resultado['estado']}'</p>";
                } else {
                    echo "<div class='alert alert-danger'>❌ El pedido ID 8 NO cumple las condiciones WHERE</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-warning">
        <div class="panel-heading">
            <h3 class="panel-title">🧪 Test 2: UPDATE sin parámetros</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                echo "<h4>Ejecutando UPDATE directo (sin parámetros):</h4>";
                
                // Test 2: UPDATE directo sin parámetros
                $stmt = Conexion::conectar()->prepare("
                    UPDATE pedidos 
                    SET estado = 'entregado', 
                        fecha_entrega = NOW(), 
                        usuario_entrega = 1 
                    WHERE id = 8 
                    AND estado = 'enviado'
                ");
                
                $execute_result = $stmt->execute();
                $affected_rows = $stmt->rowCount();
                
                echo "<p><strong>Execute result:</strong> " . ($execute_result ? 'TRUE' : 'FALSE') . "</p>";
                echo "<p><strong>Affected rows:</strong> {$affected_rows}</p>";
                
                if ($execute_result && $affected_rows > 0) {
                    echo "<div class='alert alert-success'>✅ UPDATE directo EXITOSO</div>";
                    
                    // Verificar el cambio
                    $stmt_check = Conexion::conectar()->prepare("SELECT estado, fecha_entrega, usuario_entrega FROM pedidos WHERE id = 8");
                    $stmt_check->execute();
                    $pedido_actualizado = $stmt_check->fetch(PDO::FETCH_ASSOC);
                    
                    echo "<p><strong>Estado actualizado:</strong> {$pedido_actualizado['estado']}</p>";
                    echo "<p><strong>Fecha entrega:</strong> {$pedido_actualizado['fecha_entrega']}</p>";
                    echo "<p><strong>Usuario entrega:</strong> {$pedido_actualizado['usuario_entrega']}</p>";
                    
                } else {
                    echo "<div class='alert alert-danger'>❌ UPDATE directo FALLÓ</div>";
                    
                    // Mostrar información de error
                    $errorInfo = $stmt->errorInfo();
                    echo "<p><strong>Error Info:</strong></p>";
                    echo "<pre>" . print_r($errorInfo, true) . "</pre>";
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-success">
        <div class="panel-heading">
            <h3 class="panel-title">🧪 Test 3: UPDATE con parámetros (como en el modelo)</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                echo "<h4>Ejecutando UPDATE con parámetros (como en el modelo):</h4>";
                
                // Primero, restaurar el estado a 'enviado' si fue cambiado en el test anterior
                $stmt_restore = Conexion::conectar()->prepare("UPDATE pedidos SET estado = 'enviado', fecha_entrega = NULL, usuario_entrega = NULL WHERE id = 8");
                $stmt_restore->execute();
                echo "<p><em>Estado restaurado a 'enviado' para el test</em></p>";
                
                // Test 3: UPDATE con parámetros como en el modelo
                $pedido_id = 8;
                $usuario_id = 1;
                
                $stmt = Conexion::conectar()->prepare("
                    UPDATE pedidos 
                    SET estado = 'entregado', 
                        fecha_entrega = NOW(), 
                        usuario_entrega = :usuario_id 
                    WHERE id = :pedido_id 
                    AND estado = 'enviado'
                ");
                
                echo "<p><strong>Parámetros:</strong></p>";
                echo "<p>- pedido_id: {$pedido_id} (tipo: " . gettype($pedido_id) . ")</p>";
                echo "<p>- usuario_id: {$usuario_id} (tipo: " . gettype($usuario_id) . ")</p>";
                
                $stmt->bindParam(":pedido_id", $pedido_id, PDO::PARAM_INT);
                $stmt->bindParam(":usuario_id", $usuario_id, PDO::PARAM_INT);
                
                $execute_result = $stmt->execute();
                $affected_rows = $stmt->rowCount();
                
                echo "<p><strong>Execute result:</strong> " . ($execute_result ? 'TRUE' : 'FALSE') . "</p>";
                echo "<p><strong>Affected rows:</strong> {$affected_rows}</p>";
                
                if ($execute_result && $affected_rows > 0) {
                    echo "<div class='alert alert-success'>✅ UPDATE con parámetros EXITOSO</div>";
                } else {
                    echo "<div class='alert alert-danger'>❌ UPDATE con parámetros FALLÓ</div>";
                    
                    // Mostrar información de error
                    $errorInfo = $stmt->errorInfo();
                    echo "<p><strong>Error Info:</strong></p>";
                    echo "<pre>" . print_r($errorInfo, true) . "</pre>";
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-primary">
        <div class="panel-heading">
            <h3 class="panel-title">🧪 Test 4: Verificar tipos de datos</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                echo "<h4>Verificando tipos de datos de los parámetros:</h4>";
                
                // Simular exactamente lo que hace el modelo
                $pedido_id = $_POST['test_pedido_id'] ?? 8;
                $usuario_id = $_SESSION['usuario'] ?? 1;
                
                echo "<p><strong>Valores recibidos:</strong></p>";
                echo "<p>- pedido_id: '{$pedido_id}' (tipo: " . gettype($pedido_id) . ")</p>";
                echo "<p>- usuario_id: '{$usuario_id}' (tipo: " . gettype($usuario_id) . ")</p>";
                
                // Convertir a enteros explícitamente
                $pedido_id_int = (int)$pedido_id;
                $usuario_id_int = (int)$usuario_id;
                
                echo "<p><strong>Valores convertidos:</strong></p>";
                echo "<p>- pedido_id_int: {$pedido_id_int} (tipo: " . gettype($pedido_id_int) . ")</p>";
                echo "<p>- usuario_id_int: {$usuario_id_int} (tipo: " . gettype($usuario_id_int) . ")</p>";
                
                // Test con conversión explícita
                $stmt_restore = Conexion::conectar()->prepare("UPDATE pedidos SET estado = 'enviado', fecha_entrega = NULL, usuario_entrega = NULL WHERE id = 8");
                $stmt_restore->execute();
                
                $stmt = Conexion::conectar()->prepare("
                    UPDATE pedidos 
                    SET estado = 'entregado', 
                        fecha_entrega = NOW(), 
                        usuario_entrega = :usuario_id 
                    WHERE id = :pedido_id 
                    AND estado = 'enviado'
                ");
                
                $stmt->bindParam(":pedido_id", $pedido_id_int, PDO::PARAM_INT);
                $stmt->bindParam(":usuario_id", $usuario_id_int, PDO::PARAM_INT);
                
                $execute_result = $stmt->execute();
                $affected_rows = $stmt->rowCount();
                
                echo "<p><strong>Execute result:</strong> " . ($execute_result ? 'TRUE' : 'FALSE') . "</p>";
                echo "<p><strong>Affected rows:</strong> {$affected_rows}</p>";
                
                if ($execute_result && $affected_rows > 0) {
                    echo "<div class='alert alert-success'>✅ UPDATE con conversión explícita EXITOSO</div>";
                } else {
                    echo "<div class='alert alert-danger'>❌ UPDATE con conversión explícita FALLÓ</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-default">
        <div class="panel-heading">
            <h3 class="panel-title">📊 Estado final del pedido</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                $stmt = Conexion::conectar()->prepare("SELECT id, estado, fecha_entrega, usuario_entrega FROM pedidos WHERE id = 8");
                $stmt->execute();
                $estado_final = $stmt->fetch(PDO::FETCH_ASSOC);
                
                echo "<table class='table table-bordered'>";
                echo "<tr><th>Campo</th><th>Valor</th></tr>";
                echo "<tr><td>ID</td><td>{$estado_final['id']}</td></tr>";
                echo "<tr><td>Estado</td><td><span class='label label-" . 
                     ($estado_final['estado'] == 'entregado' ? 'success' : 'info') . 
                     "'>{$estado_final['estado']}</span></td></tr>";
                echo "<tr><td>Fecha Entrega</td><td>{$estado_final['fecha_entrega']}</td></tr>";
                echo "<tr><td>Usuario Entrega</td><td>{$estado_final['usuario_entrega']}</td></tr>";
                echo "</table>";
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <hr>
    <div class="row">
        <div class="col-md-4">
            <a href="index.php?action=verificar_pedido_8" class="btn btn-primary btn-block">🔙 Verificar Pedido</a>
        </div>
        <div class="col-md-4">
            <a href="index.php?action=pedidosBarPendientes" class="btn btn-info btn-block">🍺 Volver a Bar</a>
        </div>
        <div class="col-md-4">
            <a href="index.php?action=debug_estado_pedidos" class="btn btn-warning btn-block">📊 Debug Estado</a>
        </div>
    </div>
</div>

</body>
</html>
