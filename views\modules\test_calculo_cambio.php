<?php
// Test del cálculo de cambio
$mesaId = isset($_GET['mesa']) ? $_GET['mesa'] : 1;
?>

<!DOCTYPE html>
<html>
<head>
    <title>Test Cálculo de Cambio</title>
    <style>
        .container { max-width: 800px; margin: 20px auto; padding: 20px; }
        .form-group { margin: 10px 0; }
        .form-group label { display: inline-block; width: 150px; font-weight: bold; }
        .form-group input { width: 200px; padding: 5px; }
        .result { background: #f8f9fa; padding: 15px; margin: 15px 0; border-radius: 5px; }
        .alert { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .alert-info { background: #d1ecf1; border: 1px solid #bee5eb; }
        .alert-success { background: #d4edda; border: 1px solid #c3e6cb; }
        .alert-warning { background: #fff3cd; border: 1px solid #ffeaa7; }
    </style>
</head>
<body>

<div class="container">
    <h1>🧮 Test Cálculo de Cambio - Mesa <?=$mesaId?></h1>
    
    <div class="alert alert-info">
        <h4>📝 Fórmula del Cambio:</h4>
        <p><strong>Cambio = Total Pagado - Total a Pagar</strong></p>
        <ul>
            <li><strong>Total Pagado:</strong> Efectivo + Tarjeta + Nequi + Daviplata + Bancolombia + Descuentos</li>
            <li><strong>Total a Pagar:</strong> Total Cuenta + Propina</li>
        </ul>
        <p><em>Si el cambio es negativo, falta dinero. Si es positivo, hay vuelto.</em></p>
    </div>

    <form name="calculo" id="testForm">
        <h3>💰 Datos de la Cuenta</h3>
        
        <div class="form-group">
            <label>Total Cuenta:</label>
            <input type="text" name="total" id="total" value="50000" onkeyup="funcion_calcular();">
        </div>
        
        <div class="form-group">
            <label>Propina:</label>
            <input type="text" name="propina" id="propina" value="5000" onkeyup="funcion_calcular();">
        </div>
        
        <div class="form-group">
            <label>Descuentos:</label>
            <input type="text" name="totalDescuento" id="totalDescuento" value="0" onkeyup="funcion_calcular();">
        </div>

        <h3>💳 Métodos de Pago</h3>
        
        <div class="form-group">
            <label>Efectivo:</label>
            <input type="text" name="efectivo" id="efectivo" value="0" onkeyup="funcion_calcular();">
        </div>
        
        <div class="form-group">
            <label>Tarjeta:</label>
            <input type="text" name="tarjeta" id="tarjeta" value="0" onkeyup="funcion_calcular();">
        </div>
        
        <div class="form-group">
            <label>Nequi:</label>
            <input type="text" name="nequi" id="nequi" value="0" onkeyup="funcion_calcular();">
        </div>
        
        <div class="form-group">
            <label>Daviplata:</label>
            <input type="text" name="daviplata" id="daviplata" value="0" onkeyup="funcion_calcular();">
        </div>
        
        <div class="form-group">
            <label>Bancolombia:</label>
            <input type="text" name="bancolombia" id="bancolombia" value="0" onkeyup="funcion_calcular();">
        </div>

        <h3>📊 Resultados</h3>
        
        <div class="form-group">
            <label>Total a Pagar:</label>
            <input type="text" name="pagar" id="pagar" readonly style="background: #e9ecef;">
        </div>
        
        <div class="form-group">
            <label>Cambio:</label>
            <input type="text" name="cambio" id="cambio" readonly style="background: #e9ecef;">
        </div>
        
        <input type="hidden" name="mesa" value="<?=$mesaId?>">
        <input type="hidden" name="pago" value="1">
        <input type="hidden" name="pcedula" value="12345678">
    </form>

    <h3>🧪 Tests Predefinidos</h3>
    <button onclick="testPagoExacto()" style="background: #28a745; color: white; padding: 10px; margin: 5px;">✅ Pago Exacto</button>
    <button onclick="testPagoConVuelto()" style="background: #17a2b8; color: white; padding: 10px; margin: 5px;">💰 Pago con Vuelto</button>
    <button onclick="testPagoInsuficiente()" style="background: #dc3545; color: white; padding: 10px; margin: 5px;">❌ Pago Insuficiente</button>
    <button onclick="testPago5Metodos()" style="background: #6f42c1; color: white; padding: 10px; margin: 5px;">🎯 5 Métodos</button>
    <button onclick="limpiarCampos()" style="background: #6c757d; color: white; padding: 10px; margin: 5px;">🧹 Limpiar</button>

    <div id="resultado" class="result"></div>
</div>

<script>
// Función formatCurrency idéntica a registroPmesa.php
function formatCurrency(input) {
  if (!input || !input.value) {
    return 0;
  }
  
  let cleanValue = input.value.replace(/\D/g, '');
  let value = parseInt(cleanValue);
  
  if (isNaN(value)) {
    value = 0;
  }
  
  let options = { style: 'currency', currency: 'COP', minimumFractionDigits: 0, maximumFractionDigits: 0 };
  let formatter = new Intl.NumberFormat('es-CO', options);
  input.value = formatter.format(value);

  return value;
}

// Función funcion_calcular corregida
function funcion_calcular() {
    // Obtener valores de los campos
    let efectivo = document.getElementById('efectivo') ? formatCurrency(document.getElementById('efectivo')) : 0;
    let propina = document.getElementById('propina') ? formatCurrency(document.getElementById('propina')) : 0;
    let tarjeta = document.getElementById('tarjeta') ? formatCurrency(document.getElementById('tarjeta')) : 0;
    let nequi = document.getElementById('nequi') ? formatCurrency(document.getElementById('nequi')) : 0;
    let daviplata = document.getElementById('daviplata') ? formatCurrency(document.getElementById('daviplata')) : 0;
    let bancolombia = document.getElementById('bancolombia') ? formatCurrency(document.getElementById('bancolombia')) : 0;
    
    var totalDescuento = document.calculo && document.calculo.totalDescuento ? parseInt(document.calculo.totalDescuento.value) || 0 : 0;
    var total = document.calculo && document.calculo.total ? parseInt(document.calculo.total.value) || 0 : 0;
    
    // Asegurar que todos los valores sean números válidos
    efectivo = isNaN(efectivo) ? 0 : efectivo;
    propina = isNaN(propina) ? 0 : propina;
    tarjeta = isNaN(tarjeta) ? 0 : tarjeta;
    nequi = isNaN(nequi) ? 0 : nequi;
    daviplata = isNaN(daviplata) ? 0 : daviplata;
    bancolombia = isNaN(bancolombia) ? 0 : bancolombia;
    totalDescuento = isNaN(totalDescuento) ? 0 : totalDescuento;
    total = isNaN(total) ? 0 : total;
    
    // Cálculo del cambio
    var totalPagado = efectivo + tarjeta + nequi + daviplata + bancolombia + totalDescuento;
    var totalAPagar = total + propina;
    var cambio = totalPagado - totalAPagar;
    
    // Actualizar campos
    if (document.calculo && document.calculo.pagar) {
        document.calculo.pagar.value = totalAPagar;
        if (document.getElementById('pagar')) {
            document.getElementById('pagar').value = totalAPagar;
            formatCurrency(document.getElementById('pagar'));
        }
    }
    
    if (document.calculo && document.calculo.cambio) {
        document.calculo.cambio.value = cambio;
        if (document.getElementById('cambio')) {
            document.getElementById('cambio').value = cambio;
            formatCurrency(document.getElementById('cambio'));
        }
    }
    
    // Mostrar resultado detallado
    mostrarResultado(totalPagado, totalAPagar, cambio);
}

function mostrarResultado(totalPagado, totalAPagar, cambio) {
    let html = '<h4>📊 Cálculo Detallado:</h4>';
    html += '<p><strong>Total Pagado:</strong> $' + totalPagado.toLocaleString() + '</p>';
    html += '<p><strong>Total a Pagar:</strong> $' + totalAPagar.toLocaleString() + '</p>';
    html += '<p><strong>Cambio:</strong> $' + cambio.toLocaleString();
    
    if (cambio > 0) {
        html += ' <span style="color: green;">✅ (Hay vuelto)</span>';
    } else if (cambio < 0) {
        html += ' <span style="color: red;">❌ (Falta dinero)</span>';
    } else {
        html += ' <span style="color: blue;">✅ (Pago exacto)</span>';
    }
    html += '</p>';
    
    document.getElementById('resultado').innerHTML = html;
}

function testPagoExacto() {
    document.getElementById('total').value = '50000';
    document.getElementById('propina').value = '5000';
    document.getElementById('efectivo').value = '55000';
    limpiarOtrosMetodos();
    funcion_calcular();
}

function testPagoConVuelto() {
    document.getElementById('total').value = '50000';
    document.getElementById('propina').value = '5000';
    document.getElementById('efectivo').value = '60000';
    limpiarOtrosMetodos();
    funcion_calcular();
}

function testPagoInsuficiente() {
    document.getElementById('total').value = '50000';
    document.getElementById('propina').value = '5000';
    document.getElementById('efectivo').value = '40000';
    limpiarOtrosMetodos();
    funcion_calcular();
}

function testPago5Metodos() {
    document.getElementById('total').value = '50000';
    document.getElementById('propina').value = '5000';
    document.getElementById('efectivo').value = '11000';
    document.getElementById('tarjeta').value = '11000';
    document.getElementById('nequi').value = '11000';
    document.getElementById('daviplata').value = '11000';
    document.getElementById('bancolombia').value = '11000';
    funcion_calcular();
}

function limpiarOtrosMetodos() {
    document.getElementById('tarjeta').value = '0';
    document.getElementById('nequi').value = '0';
    document.getElementById('daviplata').value = '0';
    document.getElementById('bancolombia').value = '0';
}

function limpiarCampos() {
    document.getElementById('total').value = '0';
    document.getElementById('propina').value = '0';
    document.getElementById('efectivo').value = '0';
    limpiarOtrosMetodos();
    funcion_calcular();
}

// Ejecutar cálculo inicial
document.addEventListener('DOMContentLoaded', function() {
    funcion_calcular();
});
</script>

</body>
</html>
