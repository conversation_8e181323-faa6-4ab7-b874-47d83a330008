<?php
session_start();
require_once "../../models/conexion.php";
require_once "../../models/crudFacturaAja.php";

// Verificar si se recibió mesa por GET
$mesaId = $_GET['mesa'] ?? 5;

echo "<!DOCTYPE html>";
echo "<html><head><title>Test Flujo Facturación Final</title></head><body>";
echo "<h2>🔧 Test Flujo Facturación Final</h2>";
echo "<p><strong>Mesa ID:</strong> $mesaId</p>";

// Verificar productos para facturar en esta mesa
try {
    $productos = DatosFacturaAja::obtenerProductosParaFacturar($mesaId);
    $totalProductos = count($productos);
    
    echo "<h3>📊 Estado Actual:</h3>";
    echo "<p><strong>Total productos:</strong> $totalProductos</p>";
    
    if ($totalProductos > 0) {
        $totalReal = 0;
        foreach ($productos as $producto) {
            $totalReal += $producto['valor_productos'];
        }
        
        echo "<p><strong>Total calculado:</strong> $" . number_format($totalReal) . "</p>";
        
        echo "<h3>🚀 Test Final de Facturación</h3>";
        echo "<p>Este test usa exactamente el mismo flujo que registroPmesa.php</p>";
        
        echo "<button onclick='testFinal()' style='background: #dc3545; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; margin: 5px;'>";
        echo "🔥 Test Final Mesa $mesaId";
        echo "</button>";
        
        echo "<div id='resultado' style='margin-top: 20px; padding: 15px; border-radius: 5px;'></div>";
        
    } else {
        echo "<p style='color: orange;'>⚠️ No hay productos para facturar en esta mesa</p>";
        echo "<p>Ve a la mesa y agrega algunos productos primero.</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}

?>

<script>
function testFinal() {
    const mesaId = <?=$mesaId?>;
    const totalReal = <?=$totalReal ?? 50000?>;
    
    // Usar exactamente los mismos datos que registroPmesa.php
    const datos = {
        efectivo: totalReal,
        bancolombia: 0,
        nequi: 0,
        daviplata: 0,
        tarjeta: 0,
        pago: 1,
        pcedula: 'TEST_FINAL_' + mesaId,
        totalDescuento: 0,
        total: totalReal,
        propina: 0,
        mesa: mesaId,
        tipoTarjeta: 'credito',
        optimizada: true
    };
    
    console.log('🔥 DATOS FINALES para mesa ' + mesaId + ':', datos);
    
    document.getElementById('resultado').innerHTML = '<div style="background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; border: 1px solid #ffeaa7;">⏳ Procesando facturación FINAL para mesa ' + mesaId + '...<br>Usando ajaxFactura.php corregido</div>';
    
    // Usar exactamente la misma llamada que registroPmesa.php
    $.ajax({
        url: "ajaxFactura.php",
        type: "POST",
        data: datos,
        timeout: 600000, // 10 minutos para mesas muy pesadas
        success: function(response) {
            console.log('✅ Respuesta facturación final:', response);

            let html = '<h4>✅ Respuesta del Servidor (ajaxFactura.php):</h4>';
            html += '<div style="background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto; border: 1px solid #dee2e6;">';
            html += response;
            html += '</div>';

            if (response.includes('success_corregida') || response.includes('Facturación completada exitosamente')) {
                html += '<div style="background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin-top: 10px; border: 1px solid #c3e6cb;">';
                html += '<strong>🎉 ¡ÉXITO! Facturación completada para mesa ' + mesaId + '</strong>';
                html += '<br>✅ El archivo ajaxFactura.php está funcionando correctamente';
                html += '<br>✅ La mesa se guardó correctamente en la tabla ventas';
                html += '<br>✅ El cambio se calculó correctamente';
                html += '</div>';
                
                // Recargar después de 3 segundos
                setTimeout(() => {
                    if (confirm('¿Verificar el resultado en la tabla ventas?')) {
                        window.open('test_verificar_mesa_ventas.php?mesa=' + mesaId, '_blank');
                    }
                }, 3000);
                
            } else if (response.includes('error_corregida') || response.includes('Error')) {
                html += '<div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin-top: 10px; border: 1px solid #f5c6cb;">';
                html += '<strong>❌ Error en facturación para mesa ' + mesaId + '</strong>';
                html += '<br>Revisa los logs del servidor para más detalles';
                html += '</div>';
            } else {
                html += '<div style="background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin-top: 10px; border: 1px solid #bee5eb;">';
                html += '<strong>⚠️ Respuesta inesperada</strong>';
                html += '<br>Revisa la respuesta completa arriba';
                html += '</div>';
            }

            document.getElementById('resultado').innerHTML = html;
        },
        error: function(xhr, status, error) {
            console.error('❌ Error AJAX final:', error, 'Status:', status, 'XHR:', xhr);

            let mensaje = '❌ Error en la facturación final:\n\n';
            if (status === 'timeout') {
                mensaje += 'Timeout: La facturación está tomando más tiempo del esperado.\n\nEsto es normal para mesas con muchos pedidos.\n\nPor favor, verifica manualmente si se completó.';
            } else if (xhr.status === 500) {
                mensaje += 'Error interno del servidor (500).\n\nRevisa los logs del servidor para más detalles.\n\nLa mesa puede haberse facturado parcialmente.';
            } else {
                mensaje += 'Error: ' + error + '\nStatus: ' + status + '\n\nVerifica el estado de la mesa manualmente.';
            }

            document.getElementById('resultado').innerHTML = '<div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; border: 1px solid #f5c6cb;">' + mensaje.replace(/\n/g, '<br>') + '</div>';
        }
    });
}
</script>

<!-- Incluir jQuery para usar $.ajax como en registroPmesa.php -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<br><br>
<a href="test_verificar_mesa_ventas.php?mesa=<?=$mesaId?>" style="background: #17a2b8; color: white; padding: 10px; text-decoration: none; border-radius: 5px;">🔍 Verificar Ventas</a>
<a href="index.php?action=registroPmesa&ida=<?=$mesaId?>" style="background: #007bff; color: white; padding: 10px; text-decoration: none; border-radius: 5px;">🔙 Volver a Mesa <?=$mesaId?></a>

</body>
</html>
