<?php
// Test de Impresión por Categoría
echo "<h1>🧪 Test de Impresión por Categoría</h1>";
echo "<p><strong>Prueba la impresión automática según la categoría del producto</strong></p>";

// Obtener datos necesarios
try {
    require_once '../../models/conexion.php';
    $conexion = new Conexion();
    $pdo = $conexion->conectar();
    
    // Obtener productos con categorías
    $stmt = $pdo->query("
        SELECT DISTINCT categoria, COUNT(*) as cantidad 
        FROM productos 
        WHERE categoria IS NOT NULL AND categoria != '' 
        GROUP BY categoria 
        ORDER BY categoria
    ");
    $categorias = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Obtener algunos productos de ejemplo
    $stmt = $pdo->query("
        SELECT id, nombre, categoria, precio 
        FROM productos 
        WHERE categoria IS NOT NULL AND categoria != '' 
        ORDER BY categoria, nombre 
        LIMIT 20
    ");
    $productos_ejemplo = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Obtener mapeo actual
    $mapeo_actual = [];
    try {
        $stmt = $pdo->query("SELECT categoria, impresora FROM categoria_impresora WHERE activo = TRUE");
        $mapeos = $stmt->fetchAll(PDO::FETCH_ASSOC);
        foreach ($mapeos as $mapeo) {
            $mapeo_actual[$mapeo['categoria']] = $mapeo['impresora'];
        }
    } catch (Exception $e) {
        // Tabla no existe
    }
    
} catch (Exception $e) {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
    echo "<h4>❌ Error de conexión</h4>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
    exit;
}

// Mostrar configuración actual
echo "<div style='background-color: #e7f3ff; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0;'>";
echo "<h3>📋 Configuración Actual:</h3>";

if ($categorias) {
    echo "<table style='width: 100%; border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr style='background-color: #f8f9fa;'>";
    echo "<th style='border: 1px solid #ddd; padding: 8px;'>Categoría</th>";
    echo "<th style='border: 1px solid #ddd; padding: 8px;'>Productos</th>";
    echo "<th style='border: 1px solid #ddd; padding: 8px;'>Impresora Asignada</th>";
    echo "<th style='border: 1px solid #ddd; padding: 8px;'>Estado</th>";
    echo "</tr>";
    
    foreach ($categorias as $cat) {
        $impresora = $mapeo_actual[$cat['categoria']] ?? 'No asignada';
        $estado_color = isset($mapeo_actual[$cat['categoria']]) ? '#28a745' : '#dc3545';
        $estado_texto = isset($mapeo_actual[$cat['categoria']]) ? '✅ Configurada' : '❌ Sin configurar';
        
        echo "<tr>";
        echo "<td style='border: 1px solid #ddd; padding: 8px; font-weight: bold;'>{$cat['categoria']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px; text-align: center;'>{$cat['cantidad']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>$impresora</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px; color: $estado_color;'>$estado_texto</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>⚠️ No se encontraron categorías de productos</p>";
}
echo "</div>";

// Test de impresión por categoría
echo "<h2>🖨️ Test de Impresión por Categoría:</h2>";

if (isset($_POST['test_categoria'])) {
    $categoria_test = $_POST['categoria_test'];
    $proxy_url = $_POST['proxy_url'];
    
    if (!empty($categoria_test) && !empty($proxy_url)) {
        echo "<div style='border: 1px solid #007bff; padding: 20px; margin: 15px 0; border-radius: 8px;'>";
        echo "<h4>🧪 Ejecutando Test para: $categoria_test</h4>";
        
        // Crear datos de prueba para imprimir
        $datos_impresion = "=== TEST DE IMPRESION ===\n";
        $datos_impresion .= "Categoria: $categoria_test\n";
        $datos_impresion .= "Fecha: " . date('Y-m-d H:i:s') . "\n";
        $datos_impresion .= "Proxy: $proxy_url\n";
        $datos_impresion .= "========================\n";
        $datos_impresion .= "Productos de prueba:\n";
        
        // Agregar productos de ejemplo de esta categoría
        foreach ($productos_ejemplo as $prod) {
            if ($prod['categoria'] === $categoria_test) {
                $datos_impresion .= "- {$prod['nombre']} - $" . number_format($prod['precio'], 2) . "\n";
            }
        }
        
        $datos_impresion .= "========================\n";
        $datos_impresion .= "Test completado\n\n\n";
        
        // Preparar datos para enviar al proxy
        $post_data = json_encode([
            'categoria' => $categoria_test,
            'datos' => $datos_impresion
        ]);
        
        // Configurar contexto para la petición
        $context = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => [
                    'Content-Type: application/json',
                    'Content-Length: ' . strlen($post_data)
                ],
                'content' => $post_data,
                'timeout' => 10
            ]
        ]);
        
        echo "<p><strong>📡 Enviando a:</strong> $proxy_url/imprimir-categoria</p>";
        echo "<p><strong>📄 Datos:</strong></p>";
        echo "<pre style='background-color: #f8f9fa; padding: 10px; border-radius: 3px; max-height: 200px; overflow-y: auto;'>";
        echo htmlspecialchars($datos_impresion);
        echo "</pre>";
        
        // Intentar enviar al proxy
        $resultado = @file_get_contents($proxy_url . '/imprimir-categoria', false, $context);
        
        if ($resultado !== false) {
            $respuesta = json_decode($resultado, true);
            if ($respuesta && isset($respuesta['success']) && $respuesta['success']) {
                echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0;'>";
                echo "<h5>✅ Impresión Exitosa</h5>";
                echo "<p><strong>Impresora:</strong> {$respuesta['impresora']}</p>";
                echo "<p><strong>IP:</strong> {$respuesta['ip']}:{$respuesta['puerto']}</p>";
                echo "<p><strong>Respuesta:</strong> {$respuesta['respuesta']}</p>";
                echo "</div>";
            } else {
                echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
                echo "<h5>❌ Error en la Impresión</h5>";
                echo "<p><strong>Error:</strong> " . ($respuesta['error'] ?? 'Error desconocido') . "</p>";
                echo "</div>";
            }
        } else {
            echo "<div style='background-color: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 15px 0;'>";
            echo "<h5>⚠️ No se pudo conectar al proxy</h5>";
            echo "<p>Verifica que el proxy esté ejecutándose en: $proxy_url</p>";
            echo "</div>";
        }
        
        echo "</div>";
    }
}

// Formulario de test
echo "<form method='POST' style='background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>🎯 Configurar Test:</h4>";

echo "<div style='margin: 15px 0;'>";
echo "<label><strong>Categoría a probar:</strong></label><br>";
echo "<select name='categoria_test' required style='padding: 8px; border: 1px solid #ccc; border-radius: 3px; width: 200px;'>";
echo "<option value=''>-- Seleccionar Categoría --</option>";
foreach ($categorias as $cat) {
    $impresora = $mapeo_actual[$cat['categoria']] ?? '';
    $disabled = empty($impresora) ? 'disabled' : '';
    $texto_extra = empty($impresora) ? ' (Sin impresora)' : " → $impresora";
    echo "<option value='{$cat['categoria']}' $disabled>{$cat['categoria']}$texto_extra</option>";
}
echo "</select>";
echo "</div>";

echo "<div style='margin: 15px 0;'>";
echo "<label><strong>URL del Proxy:</strong></label><br>";
echo "<input type='text' name='proxy_url' value='http://***********:3000' required style='padding: 8px; border: 1px solid #ccc; border-radius: 3px; width: 300px;'>";
echo "<small style='display: block; color: #666; margin-top: 5px;'>Asegúrate de que el proxy esté ejecutándose</small>";
echo "</div>";

echo "<button type='submit' name='test_categoria' style='background-color: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold;'>🧪 Ejecutar Test</button>";
echo "</form>";

// Test desde celular
echo "<h2>📱 Test desde Celular:</h2>";
echo "<div style='border: 1px solid #28a745; padding: 20px; margin: 15px 0; border-radius: 8px;'>";
echo "<h4>📲 Instrucciones para probar desde celular:</h4>";

echo "<ol>";
echo "<li><strong>Conectar el celular</strong> a la misma red WiFi</li>";
echo "<li><strong>Abrir navegador</strong> en el celular</li>";
echo "<li><strong>Ir a:</strong> <code>http://***********:3000</code></li>";
echo "<li><strong>Probar endpoint:</strong> <code>http://***********:3000/config</code></li>";
echo "</ol>";

echo "<h5>🔧 Código JavaScript para celular:</h5>";
echo "<pre style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; font-size: 12px;'>";
echo htmlspecialchars('// Función para imprimir desde celular
async function imprimirDesdeMovil(categoria, datos) {
    try {
        const response = await fetch("http://***********:3000/imprimir-categoria", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                categoria: categoria,
                datos: datos
            })
        });
        
        const resultado = await response.json();
        
        if (resultado.success) {
            alert(`✅ Impreso en ${resultado.impresora}`);
        } else {
            alert(`❌ Error: ${resultado.error}`);
        }
    } catch (error) {
        alert(`❌ Error de conexión: ${error.message}`);
    }
}

// Ejemplo de uso
imprimirDesdeMovil("Bebidas", "Coca Cola - $2.50\\nAgua - $1.00\\n");');
echo "</pre>";

echo "<h5>📋 QR Code para acceso rápido:</h5>";
echo "<p>Genera un QR con la URL: <strong>http://***********:3000</strong></p>";
echo "</div>";

// Productos de ejemplo por categoría
if ($productos_ejemplo) {
    echo "<h2>📦 Productos de Ejemplo por Categoría:</h2>";
    echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    
    $productos_por_categoria = [];
    foreach ($productos_ejemplo as $prod) {
        $productos_por_categoria[$prod['categoria']][] = $prod;
    }
    
    foreach ($productos_por_categoria as $categoria => $productos) {
        $impresora = $mapeo_actual[$categoria] ?? 'Sin asignar';
        echo "<h4>🏷️ $categoria → 🖨️ $impresora</h4>";
        echo "<ul>";
        foreach ($productos as $prod) {
            echo "<li><strong>{$prod['nombre']}</strong> - $" . number_format($prod['precio'], 2) . "</li>";
        }
        echo "</ul>";
    }
    echo "</div>";
}

// Botones de navegación
echo "<p style='margin-top: 30px;'>";
echo "<a href='sistema_impresion_completo.php' style='background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>← Sistema Completo</a>";
echo "<a href='verificar_impresoras.php' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🖨️ Verificar Impresoras</a>";
echo "</p>";
?>
