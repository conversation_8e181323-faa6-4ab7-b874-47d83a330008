<?php

session_start();

if(!$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

echo "<h2>🧪 Test Flujo Completo - Pedidos Pendientes</h2>";
echo "<hr>";

// Simular exactamente lo que hacen las páginas de pedidos pendientes

echo "<h3>1. Test BAR (Simulando pedidosBarPendientes.php)</h3>";
echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 10px 0;'>";

try {
    require_once "../../controllers/controllerPedidosCategorias.php";
    
    $controller = new ControllerPedidosCategorias();
    
    echo "<h4>Paso 1: Crear controlador ✅</h4>";
    
    // Obtener pedidos pendientes para bar
    $pedidos_bar = $controller->mostrarPedidosPendientesController('bar');
    echo "<h4>Paso 2: Obtener pedidos pendientes para BAR</h4>";
    echo "<p><strong>Resultado:</strong> " . count($pedidos_bar) . " pedidos encontrados</p>";
    
    if (count($pedidos_bar) > 0) {
        echo "<ul>";
        foreach ($pedidos_bar as $pedido) {
            echo "<li><strong>{$pedido['numero_pedido']}</strong> - Mesa {$pedido['mesa_numero']} - {$pedido['productos_detalle']}</li>";
        }
        echo "</ul>";
    }
    
    // Obtener estadísticas
    $estadisticas_bar = $controller->obtenerEstadisticasDiaController('bar');
    echo "<h4>Paso 3: Obtener estadísticas para BAR</h4>";
    echo "<p><strong>Pendientes:</strong> {$estadisticas_bar['pendientes']}</p>";
    echo "<p><strong>Entregados hoy:</strong> {$estadisticas_bar['entregados_hoy']}</p>";
    
    // Simular la vista
    echo "<h4>Paso 4: Simular vista de pedidos pendientes</h4>";
    ob_start();
    $controller->vistaPedidosPendientesController('bar');
    $vista_output = ob_get_clean();
    
    if (strlen($vista_output) > 100) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px;'>";
        echo "✅ Vista generada correctamente (" . strlen($vista_output) . " caracteres)";
        echo "</div>";
        
        // Mostrar una parte de la vista
        echo "<h5>Muestra de la vista generada:</h5>";
        echo "<div style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; max-height: 200px; overflow-y: auto;'>";
        echo htmlspecialchars(substr($vista_output, 0, 500)) . "...";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px;'>";
        echo "❌ Vista no generada o muy corta (" . strlen($vista_output) . " caracteres)";
        echo "</div>";
        
        if ($vista_output) {
            echo "<p><strong>Contenido:</strong> " . htmlspecialchars($vista_output) . "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px;'>";
    echo "❌ Error: " . $e->getMessage();
    echo "</div>";
}

echo "</div>";

echo "<h3>2. Test COCINA (Simulando pedidosCocinaPendientes.php)</h3>";
echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 10px 0;'>";

try {
    // Obtener pedidos pendientes para cocina
    $pedidos_cocina = $controller->mostrarPedidosPendientesController('cocina');
    echo "<h4>Paso 1: Obtener pedidos pendientes para COCINA</h4>";
    echo "<p><strong>Resultado:</strong> " . count($pedidos_cocina) . " pedidos encontrados</p>";
    
    if (count($pedidos_cocina) > 0) {
        echo "<ul>";
        foreach ($pedidos_cocina as $pedido) {
            echo "<li><strong>{$pedido['numero_pedido']}</strong> - Mesa {$pedido['mesa_numero']} - {$pedido['productos_detalle']}</li>";
        }
        echo "</ul>";
    }
    
    // Obtener estadísticas
    $estadisticas_cocina = $controller->obtenerEstadisticasDiaController('cocina');
    echo "<h4>Paso 2: Obtener estadísticas para COCINA</h4>";
    echo "<p><strong>Pendientes:</strong> {$estadisticas_cocina['pendientes']}</p>";
    echo "<p><strong>Entregados hoy:</strong> {$estadisticas_cocina['entregados_hoy']}</p>";
    
    // Simular la vista
    echo "<h4>Paso 3: Simular vista de pedidos pendientes</h4>";
    ob_start();
    $controller->vistaPedidosPendientesController('cocina');
    $vista_output_cocina = ob_get_clean();
    
    if (strlen($vista_output_cocina) > 100) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px;'>";
        echo "✅ Vista generada correctamente (" . strlen($vista_output_cocina) . " caracteres)";
        echo "</div>";
        
        // Mostrar una parte de la vista
        echo "<h5>Muestra de la vista generada:</h5>";
        echo "<div style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; max-height: 200px; overflow-y: auto;'>";
        echo htmlspecialchars(substr($vista_output_cocina, 0, 500)) . "...";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px;'>";
        echo "❌ Vista no generada o muy corta (" . strlen($vista_output_cocina) . " caracteres)";
        echo "</div>";
        
        if ($vista_output_cocina) {
            echo "<p><strong>Contenido:</strong> " . htmlspecialchars($vista_output_cocina) . "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px;'>";
    echo "❌ Error: " . $e->getMessage();
    echo "</div>";
}

echo "</div>";

echo "<h3>3. Verificación de Archivos y Rutas</h3>";
echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 10px 0;'>";

$archivos_verificar = [
    '../../controllers/controllerPedidosCategorias.php',
    '../../models/crudPedidosCategorias.php',
    '../../models/conexion.php'
];

foreach ($archivos_verificar as $archivo) {
    if (file_exists($archivo)) {
        echo "<p>✅ <strong>{$archivo}</strong> - Existe</p>";
    } else {
        echo "<p>❌ <strong>{$archivo}</strong> - NO existe</p>";
    }
}

echo "</div>";

echo "<h3>4. Test de Conexión a Base de Datos</h3>";
echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 10px 0;'>";

try {
    require_once "../../models/conexion.php";
    $conexion = Conexion::conectar();
    
    if ($conexion) {
        echo "<p>✅ Conexión a base de datos exitosa</p>";
        
        // Test simple
        $stmt = $conexion->prepare("SELECT COUNT(*) as total FROM pedidos WHERE estado = 'enviado'");
        $stmt->execute();
        $resultado = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<p>📊 Total de pedidos enviados en la base de datos: <strong>{$resultado['total']}</strong></p>";
        
    } else {
        echo "<p>❌ Error en conexión a base de datos</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error de conexión: " . $e->getMessage() . "</p>";
}

echo "</div>";

echo "<hr>";
echo "<h3>🎯 Resumen de Diagnóstico</h3>";

if (isset($pedidos_bar) && isset($pedidos_cocina)) {
    echo "<ul>";
    echo "<li><strong>Pedidos BAR encontrados:</strong> " . count($pedidos_bar) . "</li>";
    echo "<li><strong>Pedidos COCINA encontrados:</strong> " . count($pedidos_cocina) . "</li>";
    echo "<li><strong>Vista BAR generada:</strong> " . (isset($vista_output) && strlen($vista_output) > 100 ? "✅ Sí" : "❌ No") . "</li>";
    echo "<li><strong>Vista COCINA generada:</strong> " . (isset($vista_output_cocina) && strlen($vista_output_cocina) > 100 ? "✅ Sí" : "❌ No") . "</li>";
    echo "</ul>";
    
    if (count($pedidos_bar) == 0 && count($pedidos_cocina) == 0) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>⚠️ Posibles Causas:</h4>";
        echo "<ol>";
        echo "<li>No hay pedidos con estado 'enviado' en la base de datos</li>";
        echo "<li>Los productos no tienen las categorías esperadas</li>";
        echo "<li>Hay un problema en las consultas SQL del modelo</li>";
        echo "<li>El pedido P000004 no está en estado 'enviado'</li>";
        echo "</ol>";
        echo "</div>";
    }
}

echo "<p><a href='views/modules/debug_pedido_P000004.php' class='btn btn-primary'>🔍 Debug Específico P000004</a></p>";
echo "<p><a href='pedidosCocinaPendientes' class='btn btn-warning'>🍳 Ir a Cocina</a></p>";
echo "<p><a href='pedidosBarPendientes' class='btn btn-info'>🍺 Ir a Bar</a></p>";

?>
