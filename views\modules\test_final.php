<?php
// Test final que simula exactamente el comportamiento real
require_once "../../models/conexion.php";
require_once "../../models/crudEstadoPedidos.php";
require_once "../../controllers/controllerEstadoPedidos.php";

// Iniciar sesión
session_start();
if (!isset($_SESSION["usuario"])) {
    $_SESSION["usuario"] = 1;
    $_SESSION["tipo_usuario"] = 1;
    $_SESSION["perfil"] = "administrador";
}

echo "<h1>🎯 Test Final - Simulación Exacta</h1>";

// Obtener pedidos borrador
try {
    $stmt = Conexion::conectar()->prepare("
        SELECT p.id, p.numero_pedido, p.mesa_id, p.estado, m.numero as mesa_numero
        FROM pedidos p 
        LEFT JOIN mesas m ON p.mesa_id = m.id 
        WHERE p.estado = 'borrador' 
        ORDER BY p.id DESC 
        LIMIT 3
    ");
    $stmt->execute();
    $pedidosBorrador = $stmt->fetchAll();
    
    echo "<h3>📋 Pedidos Borrador Disponibles:</h3>";
    if (!empty($pedidosBorrador)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; max-width: 600px;'>";
        echo "<tr style='background: #f0f0f0;'><th>ID</th><th>Número</th><th>Mesa</th><th>Estado</th><th>Acción</th></tr>";
        foreach ($pedidosBorrador as $pedido) {
            echo "<tr>";
            echo "<td>" . $pedido['id'] . "</td>";
            echo "<td>" . $pedido['numero_pedido'] . "</td>";
            echo "<td>Mesa " . $pedido['mesa_numero'] . "</td>";
            echo "<td>" . $pedido['estado'] . "</td>";
            echo "<td><button onclick='enviarPedidoFinal(" . $pedido['id'] . ")' style='background: #28a745; color: white; padding: 8px; border: none; cursor: pointer; border-radius: 4px;'>📤 Enviar</button></td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "❌ No hay pedidos borrador disponibles.<br>";
        echo "<button onclick='crearPedidoPrueba()' style='background: #007bff; color: white; padding: 10px; border: none; cursor: pointer; border-radius: 4px;'>➕ Crear Pedido de Prueba</button>";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage();
}

echo "<h3>📊 Log de Actividad en Tiempo Real:</h3>";
echo "<div id='log' style='background: #f8f9fa; padding: 15px; border: 1px solid #ddd; height: 400px; overflow-y: scroll; font-family: monospace; font-size: 13px; border-radius: 4px;'></div>";

echo "<br>";
echo "<h4>🔗 Enlaces de Navegación:</h4>";
echo "<a href='../../index.php?action=test_ajax' style='background: #007bff; color: white; padding: 10px; text-decoration: none; margin: 5px; border-radius: 4px;'>🔧 Test AJAX Completo</a>";
echo "<a href='../../index.php?action=registroPmesa&ida=1' style='background: #28a745; color: white; padding: 10px; text-decoration: none; margin: 5px; border-radius: 4px;'>🍽️ Mesa Real</a>";
echo "<a href='../../index.php?action=crear_datos_prueba' style='background: #ffc107; color: black; padding: 10px; text-decoration: none; margin: 5px; border-radius: 4px;'>➕ Crear Datos</a>";
?>

<script>
function log(message) {
    const logDiv = document.getElementById('log');
    const timestamp = new Date().toLocaleTimeString();
    logDiv.innerHTML += `[${timestamp}] ${message}\n`;
    logDiv.scrollTop = logDiv.scrollHeight;
}

function enviarPedidoFinal(pedidoId) {
    log(`🚀 INICIANDO ENVÍO DE PEDIDO ${pedidoId}`);
    log(`📡 Preparando petición AJAX...`);
    
    // Simular exactamente lo que hace el JavaScript real
    fetch('ajaxEstadoPedidos.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'enviar_pedido=true&pedido_id=' + pedidoId
    })
    .then(response => {
        log(`📡 Respuesta HTTP recibida:`);
        log(`   - Status: ${response.status}`);
        log(`   - Status Text: ${response.statusText}`);
        log(`   - Headers: ${response.headers.get('content-type')}`);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        return response.text();
    })
    .then(text => {
        log(`📄 Respuesta cruda recibida:`);
        log(`   - Longitud: ${text.length} caracteres`);
        log(`   - Contenido: ${text.substring(0, 200)}${text.length > 200 ? '...' : ''}`);
        
        try {
            const data = JSON.parse(text);
            log(`✅ JSON parseado exitosamente:`);
            log(`   - Status: ${data.status}`);
            log(`   - Message: ${data.message}`);
            log(`   - Impresión: ${data.impresion}`);
            
            if (data.status === 'success') {
                log(`🎉 ¡ÉXITO! Pedido enviado correctamente`);
                
                // Mostrar notificación visual
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed; top: 20px; right: 20px; 
                    background: #28a745; color: white; 
                    padding: 15px; border-radius: 8px; 
                    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
                    z-index: 1000; font-weight: bold;
                `;
                notification.textContent = '✅ Pedido enviado exitosamente!';
                document.body.appendChild(notification);
                
                setTimeout(() => {
                    notification.remove();
                    log(`🔄 Recargando página en 2 segundos...`);
                    setTimeout(() => location.reload(), 2000);
                }, 3000);
                
            } else {
                log(`⚠️ ERROR EN RESPUESTA: ${data.message}`);
                alert('❌ Error: ' + data.message);
            }
            
        } catch (e) {
            log(`❌ ERROR PARSEANDO JSON:`);
            log(`   - Error: ${e.message}`);
            log(`   - Respuesta recibida: ${text}`);
            
            // Intentar mostrar más detalles del error
            if (text.includes('Fatal error') || text.includes('Warning')) {
                log(`🔍 Detectado error PHP en la respuesta`);
            }
            
            alert('❌ Error: Respuesta no válida del servidor');
        }
    })
    .catch(error => {
        log(`❌ ERROR DE RED O FETCH:`);
        log(`   - Error: ${error.message}`);
        log(`   - Stack: ${error.stack}`);
        alert('❌ Error de conexión: ' + error.message);
    });
}

function crearPedidoPrueba() {
    log('📝 Creando pedido de prueba...');
    
    fetch('../../index.php?action=crear_datos_prueba', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'crear_pedido_borrador=true'
    })
    .then(response => {
        log('✅ Pedido de prueba creado');
        setTimeout(() => location.reload(), 2000);
    })
    .catch(error => {
        log('❌ Error creando pedido: ' + error.message);
    });
}

// Log inicial
document.addEventListener('DOMContentLoaded', function() {
    log('🚀 Sistema de test final cargado');
    log('📋 Listo para enviar pedidos');
    log('🔧 Simulando comportamiento exacto del sistema real');
    log('');
});
</script>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}

table {
    margin: 10px 0;
}

th, td {
    padding: 10px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #f0f0f0;
    font-weight: bold;
}

button:hover, a:hover {
    opacity: 0.8;
    transform: translateY(-1px);
}

a {
    display: inline-block;
    text-decoration: none;
    transition: all 0.2s;
}

button {
    transition: all 0.2s;
}
</style>
