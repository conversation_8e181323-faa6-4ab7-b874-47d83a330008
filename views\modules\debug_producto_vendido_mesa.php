<?php

session_start();

if(!isset($_SESSION["validar"]) || !$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "models/conexion.php";

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Debug producto_vendido_mesa</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
</head>
<body>

<div class="container">
    <h2>🔍 Debug: Tabla producto_vendido_mesa</h2>
    <hr>
    
    <div class="panel panel-danger">
        <div class="panel-heading">
            <h3 class="panel-title">🚨 Problema Identificado</h3>
        </div>
        <div class="panel-body">
            <p><strong>El JOIN con producto_vendido_mesa devuelve 0 registros, pero sabemos que los productos existen.</strong></p>
            <p>Esto significa que los productos no están correctamente vinculados con los pedidos en la tabla producto_vendido_mesa.</p>
        </div>
    </div>
    
    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">📊 Contenido de producto_vendido_mesa</h3>
        </div>
        <div class="panel-body">
            <?php
            $stmt = Conexion::conectar()->prepare("SELECT * FROM producto_vendido_mesa ORDER BY fecha_hora DESC LIMIT 20");
            $stmt->execute();
            $registros = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "<p><strong>Total registros en producto_vendido_mesa:</strong> " . count($registros) . "</p>";
            
            if (count($registros) > 0) {
                echo "<table class='table table-striped table-condensed'>";
                echo "<thead><tr><th>productos_id</th><th>mesas_id</th><th>pedidos_id</th><th>cantidad</th><th>fecha_hora</th><th>mesero</th></tr></thead>";
                echo "<tbody>";
                foreach ($registros as $reg) {
                    echo "<tr>";
                    echo "<td>{$reg['productos_id']}</td>";
                    echo "<td>{$reg['mesas_id']}</td>";
                    echo "<td>{$reg['pedidos_id']}</td>";
                    echo "<td>{$reg['cantidad']}</td>";
                    echo "<td>{$reg['fecha_hora']}</td>";
                    echo "<td>{$reg['mesero']}</td>";
                    echo "</tr>";
                }
                echo "</tbody></table>";
            } else {
                echo "<div class='alert alert-warning'>❌ No hay registros en producto_vendido_mesa</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-warning">
        <div class="panel-heading">
            <h3 class="panel-title">🔍 Buscar registros específicos de pedidos P000004 y P006046</h3>
        </div>
        <div class="panel-body">
            <?php
            echo "<h4>Registros para pedido ID 4 (P000004):</h4>";
            $stmt = Conexion::conectar()->prepare("SELECT * FROM producto_vendido_mesa WHERE pedidos_id = 4");
            $stmt->execute();
            $registros_p4 = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "<p><strong>Registros encontrados:</strong> " . count($registros_p4) . "</p>";
            
            if (count($registros_p4) > 0) {
                foreach ($registros_p4 as $reg) {
                    echo "<p>- Producto ID: {$reg['productos_id']}, Mesa ID: {$reg['mesas_id']}, Cantidad: {$reg['cantidad']}</p>";
                }
            } else {
                echo "<p style='color: red;'>❌ No hay registros para pedido ID 4</p>";
            }
            
            echo "<h4>Registros para pedido ID 8 (P006046):</h4>";
            $stmt = Conexion::conectar()->prepare("SELECT * FROM producto_vendido_mesa WHERE pedidos_id = 8");
            $stmt->execute();
            $registros_p8 = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "<p><strong>Registros encontrados:</strong> " . count($registros_p8) . "</p>";
            
            if (count($registros_p8) > 0) {
                foreach ($registros_p8 as $reg) {
                    echo "<p>- Producto ID: {$reg['productos_id']}, Mesa ID: {$reg['mesas_id']}, Cantidad: {$reg['cantidad']}</p>";
                }
            } else {
                echo "<p style='color: red;'>❌ No hay registros para pedido ID 8</p>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-success">
        <div class="panel-heading">
            <h3 class="panel-title">🔍 Buscar por mesa_id en lugar de pedidos_id</h3>
        </div>
        <div class="panel-body">
            <?php
            echo "<h4>Registros para mesa ID 7 (donde está P000004):</h4>";
            $stmt = Conexion::conectar()->prepare("SELECT * FROM producto_vendido_mesa WHERE mesas_id = 7");
            $stmt->execute();
            $registros_m7 = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "<p><strong>Registros encontrados:</strong> " . count($registros_m7) . "</p>";
            
            if (count($registros_m7) > 0) {
                foreach ($registros_m7 as $reg) {
                    echo "<p>- Producto ID: {$reg['productos_id']}, Pedido ID: {$reg['pedidos_id']}, Cantidad: {$reg['cantidad']}, Fecha: {$reg['fecha_hora']}</p>";
                }
            } else {
                echo "<p style='color: red;'>❌ No hay registros para mesa ID 7</p>";
            }
            
            echo "<h4>Registros para mesa ID 10 (donde está P006046):</h4>";
            $stmt = Conexion::conectar()->prepare("SELECT * FROM producto_vendido_mesa WHERE mesas_id = 10");
            $stmt->execute();
            $registros_m10 = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "<p><strong>Registros encontrados:</strong> " . count($registros_m10) . "</p>";
            
            if (count($registros_m10) > 0) {
                foreach ($registros_m10 as $reg) {
                    echo "<p>- Producto ID: {$reg['productos_id']}, Pedido ID: {$reg['pedidos_id']}, Cantidad: {$reg['cantidad']}, Fecha: {$reg['fecha_hora']}</p>";
                }
            } else {
                echo "<p style='color: red;'>❌ No hay registros para mesa ID 10</p>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-danger">
        <div class="panel-heading">
            <h3 class="panel-title">🚨 PROBLEMA IDENTIFICADO: Dos tablas diferentes</h3>
        </div>
        <div class="panel-body">
            <p><strong>Hay DOS tablas para productos vendidos:</strong></p>
            <ul>
                <li><strong>producto_vendido_mesa</strong> - Tabla que usamos en consultas</li>
                <li><strong>pedido_productos_mesa</strong> - Tabla alternativa</li>
            </ul>
            <p><strong>Los productos pueden estar en la tabla incorrecta.</strong></p>
        </div>
    </div>

    <div class="panel panel-warning">
        <div class="panel-heading">
            <h3 class="panel-title">🔍 Verificar tabla pedido_productos_mesa</h3>
        </div>
        <div class="panel-body">
            <?php
            echo "<h4>Contenido de pedido_productos_mesa:</h4>";
            $stmt = Conexion::conectar()->prepare("SELECT * FROM pedido_productos_mesa ORDER BY pedidos_id DESC LIMIT 20");
            $stmt->execute();
            $registros_ppm = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "<p><strong>Total registros en pedido_productos_mesa:</strong> " . count($registros_ppm) . "</p>";

            if (count($registros_ppm) > 0) {
                echo "<table class='table table-striped table-condensed'>";
                echo "<thead><tr><th>productos_id</th><th>mesas_id</th><th>pedidos_id</th><th>cantidad</th><th>precio</th></tr></thead>";
                echo "<tbody>";
                foreach ($registros_ppm as $reg) {
                    echo "<tr>";
                    echo "<td>{$reg['productos_id']}</td>";
                    echo "<td>{$reg['mesas_id']}</td>";
                    echo "<td>{$reg['pedidos_id']}</td>";
                    echo "<td>{$reg['cantidad']}</td>";
                    echo "<td>{$reg['precio']}</td>";
                    echo "</tr>";
                }
                echo "</tbody></table>";
            } else {
                echo "<div class='alert alert-warning'>❌ No hay registros en pedido_productos_mesa</div>";
            }

            echo "<h4>Buscar pedidos específicos en pedido_productos_mesa:</h4>";
            $stmt = Conexion::conectar()->prepare("SELECT * FROM pedido_productos_mesa WHERE pedidos_id IN (4, 8)");
            $stmt->execute();
            $registros_especificos = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "<p><strong>Registros para pedidos 4 y 8:</strong> " . count($registros_especificos) . "</p>";

            if (count($registros_especificos) > 0) {
                foreach ($registros_especificos as $reg) {
                    echo "<p>- Pedido ID: {$reg['pedidos_id']}, Producto ID: {$reg['productos_id']}, Mesa ID: {$reg['mesas_id']}, Cantidad: {$reg['cantidad']}</p>";
                }
            } else {
                echo "<p style='color: red;'>❌ No hay registros para pedidos 4 y 8 en pedido_productos_mesa</p>";
            }
            ?>
        </div>
    </div>

    <div class="panel panel-primary">
        <div class="panel-heading">
            <h3 class="panel-title">🔧 Solución</h3>
        </div>
        <div class="panel-body">
            <h4>Si los productos están en pedido_productos_mesa:</h4>
            <p>1. <strong>Cambiar las consultas</strong> para usar pedido_productos_mesa en lugar de producto_vendido_mesa</p>
            <p>2. <strong>O migrar los datos</strong> de pedido_productos_mesa a producto_vendido_mesa</p>

            <h4>Si no están en ninguna tabla:</h4>
            <p>1. <strong>Corregir el proceso de inserción</strong> en crear_pedido_prueba.php</p>
            <p>2. <strong>Asegurar que se guarden</strong> en la tabla correcta</p>

            <h4>Próximos pasos:</h4>
            <p>1. <strong>Verificar cuál tabla tiene los datos</strong></p>
            <p>2. <strong>Ajustar las consultas</strong> para usar la tabla correcta</p>
            <p>3. <strong>Probar las páginas de pedidos pendientes</strong></p>
        </div>
    </div>
    
    <hr>
    <div class="row">
        <div class="col-md-4">
            <a href="index.php?action=debug_sql_paso_a_paso" class="btn btn-primary btn-block">🔙 Debug SQL</a>
        </div>
        <div class="col-md-4">
            <a href="index.php?action=crear_pedido_prueba" class="btn btn-success btn-block">🧪 Crear Pedido</a>
        </div>
        <div class="col-md-4">
            <a href="index.php?action=debug_pedidos_creados" class="btn btn-info btn-block">📋 Ver Pedidos</a>
        </div>
    </div>
</div>

</body>
</html>
