<?php
//session_start();
	if(!$_SESSION["validar"])
		{	header("location:index.php?action=ingresar");	exit();		}/**/
	if(isset($_GET["action"]))
		{	if($_GET["action"] == "okFs")	{	echo "Registro Exitoso";	}	}
		/*ini_set('display_errors', 1);
		ini_set('display_startup_errors', 1);
		error_reporting(E_ALL);*/
?>
<script type="text/javascript">
	//------------------------- Nombre Buscar------------------------
	 function buscarPlaca1(inputString) 
	 	{
		 if(inputString.length == 0) 
		 	{	// Hide the suggestion box.
			 $('#suggestions').hide();
		 	} 
		 else
		  	{	//alert("Entro en el else de placa");
			 $("#doss").load("views/modules/ajaxBuscarSuministroCompra.php", {placa: $("#dedo").val()}, function(){	});
		 	}
	 	} 
	//------------------------- co<PERSON>-----------------------
		function buscarC(inputString) 
		 {
			if(inputString.length == 0) 
			 {	// Hide the suggestion box.
				$('#suggestions').hide();
			 } 
			else
			  {	//alert("Entro en el else de placa");
				$("#dos").load("views/modules/ajaxBuscarSuministroCod.php", {placa: $("#suministros").val()}, function(){ });
			 }
		 } 
	//------------------ Nombre cerrar-------------------	
	// ------FACTURAR--------------
	 function guardarSuministro() 		
		{	var compra = document.calculo.compra.value;
		 if (confirm('confirme los detalles de la Facturar?'+compra)) 
			{		//setTimeout('location.href="views/modules/ajaxFactura.php"',500);	
				var compra = document.calculo.compra.value;
				//alert("enviando factura # "+compra); 
				$("#destino").load("views/modules/ajaxSuministro.php", {compra: compra}, function(){	});	
			}				
		} 
	// ------FACTURAR FIN//			
</script>
<!--$_session['idcompra']  	$_session['proveedor']	$_session['valorT']	$_session['facturaN']     registroCsuministroController  -->
<div class="row">
  <div class="col-md-8">
  	<form name="calculo" method="post" >	      		
  		<div ><h2> No factura :<?php echo $_SESSION["facturaN"];?>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Valor Factura: <?php echo $_SESSION["valorT"];?>
  		</h2></div><br>
		<input type="hidden" name="compra" id="compra" value="<?=$_SESSION["ver"];?>" required>
	  	<h2>DETALLE DE FACTURA</h2>
		<table border="1" class="table table-hover">				
			<thead>					
				<tr>						
					<th> CODIGO</th>				
					<th>NOMBRE</th>				
					<th>CANTIDAD</th>				
					<th>PRECIO</th>	
					<th>DESC%</th>			
					<th>SIN IVA</th>			
					<th>IVA%</th>			
					<th>VALOR Ud</th>			
					<th>TOTAL PAGAR</th>	
					<th>FACT</th>	
					<th></th>							
				</tr>
			</thead>
			<tbody>					
				<?php
					$vistaFsuministro = new controllerFacturaCompraSuministro();
					$vistaFsuministro -> vistaFacturaSumistroController();
					$vistaFsuministro -> borrarFacturaSumistroController();			
					//$vistaFsuministro -> actualizarFacturaSumistroController();			
				?>						
			</tbody>
		</table>				
		<span id="destino" > </span>		
		<a class="btn btn-primary btn-lg"  role="button" onclick="guardarSuministro();" name="guardar" value="Guardar">Guardar &raquo;</a>	
		<!--<input type="submit"  value="Facturar">-->
	</form>				
  </div>
      <div class="col-md-4" style="background-color:#FFC; padding:12px">
		<?php			
			$registroFacturaSuministro = new controllerFacturaCompraSuministro();
			//$registroFacturaSuministro ->registroCsuministroController();
			$suministros = $registroFacturaSuministro -> listaSuministros1Controller();		
			$registroFacturaSuministro -> registroFacturaSumistroController();
			//	echo "<script>alert('factura este es el numero".$_SESSION["facturaN"]." que pasa')</script>";
		?>		
		<h2>AGREGAR SUMINISTRO</h2><br>							
		<form method="post"> 
			<input type="hidden" name="nfactura" value="<?=$_SESSION["ver"];?>" required readonly> <br>
			<label>Codigo :&nbsp;&nbsp;&nbsp;&nbsp;</label><input type="text" name="suministros" id="suministros" autofocus="autofocus" onkeyup="buscarC(this.value);" required > <br><a href="registroProveedor" target="_blank"> Registrar Suministro</a><br>
			 <label>Cantidad :&nbsp;&nbsp;&nbsp;&nbsp;</label> <input type="text" name="cantidad" required> <br>
			  <div id="dos"></div><br>	
			  <input type="submit" value="Enviar"><br>					
				<H5>----Buscar Codigo-----</H5>			
			<label for=""><b>Nombre:</b></label>
			<input type="text" placeholder="Nombre Suministros" class="mayuscula"  name="dedo" id="dedo" size="38" onkeyup="buscarPlaca1(this.value);" >
			&nbsp;&nbsp;&nbsp;&nbsp;
			<br>
			<div id="doss"></div>
			<br>
							
							

			<br><br><br><br><br>
			<br><br><br><br><br><br>
			<br><br><br><br><br><br>
			<br><br><br><br><br><br>
		</form>
			
     </div>
