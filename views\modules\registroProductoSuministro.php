<?php
 $registro = new controllerSuministro();
 $unidades=$registro->listaUnidadesController();
 $codigo=$registro -> listaSuministros1Controller();

	if(isset($_GET["action"]))
	 {	if($_GET["action"] == "oks") {	echo "Registro Exitoso";	}	 }
?>
<h1>REGISTRO DE PRODUCTOS</h1>
<form method="post">
	<table >
		<tr>
			<td align="right"> <label> Codigo  : </label></td><td><input type="text"  value="<?=$codigo;?>" name="codigo" autofocus required>	 </td>
		</tr>
		<tr>
			<td align="right"><label> Nombre  : </label></td>
			<td><input type="text" placeholder="nombre" name="nombreSuministroRegistro" required></td>
		</tr>
		<tr>
			<td align="right"><label> Cantidad  : </label></td>
			<td><input type="text" placeholder="cantidad" name="cantidadSuministroRegistro" value="1" required></td>
		</tr>
		<tr>
			<td align="right"><label>Unidades :</label></td>
			<td>
				<?php
				 # %%%%%%%%%%%%%%%%%%% Roles  %%%%%%%%%%%%%%%%%%%%%%%%%
					if($unidades=="error")
						{	echo "debe registrar la unidad"; }
					else{
							echo "";
							$result='<select name="unidad_id"  id="unidad_id">';
							$result.=' <option value="-1">Unidad Medida</option>';
							foreach ($unidades as $row => $ite)
						 	 {
						 	 	$result.=' <option value="'.$ite["id"].'">'.$ite["nombre"].'</option>';
						 	 }
							 $result.='</select>';
							 echo $result." <br>";
						 	}
				 # %%%%%%%%%%%%%%%%%%%%%%%%%%%%  End Roles  %%%%%%%%%%%%%%%%%%%%%%%%%%%
			?>
			</td>
		</tr>
		<tr>
			<td align="right"><label> Precio Compra : </label></td>
			<td><input type="text" placeholder="precio" name="precioSuministroRegistro" value="1" required></td>
		</tr>
		<tr>
			<input type="hidden" placeholder="MINIMO" name="cantidadMSuministroRegistro" value="1" required>
		</tr>
		<tr>
			<td align="right"><label> precio Venta: </label></td>
			<td><input type="text" placeholder="precio" name="precioRegistro" required></td>
		</tr>
		<input type="hidden"  value="1" name="tiposuministro" required>
	</table><br>
	<input type="submit" value="Enviar">
	<input type="reset" value="Cancelar">
	<br>
<?php
	$registro -> registroSuministroController();
?>
</form>

