<?php

session_start();

if(!isset($_SESSION["validar"]) || !$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "models/conexion.php";

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Test: Cancelar Mesa</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
</head>
<body>

<div class="container">
    <h2>🧪 Test: Funcionalidad Cancelar Mesa</h2>
    <hr>
    
    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">📋 Nueva Funcionalidad de Cancelación</h3>
        </div>
        <div class="panel-body">
            <p><strong>Funcionalidad implementada:</strong></p>
            <ul>
                <li>✅ <strong>Cancela todos los pedidos</strong> de la mesa (estado → 'cancelado')</li>
                <li>✅ <strong>Cancela entregas por categoría</strong> pendientes</li>
                <li>✅ <strong>Elimina productos</strong> del pedido actual</li>
                <li>✅ <strong>Limpia la mesa</strong> para nuevos pedidos</li>
                <li>✅ <strong>Mantiene compatibilidad</strong> con el sistema actual</li>
            </ul>
            
            <div class="alert alert-warning">
                <h4>⚠️ Importante:</h4>
                <p>Esta funcionalidad <strong>NO afecta la facturación</strong> - solo cancela pedidos pendientes y borradores.</p>
            </div>
        </div>
    </div>
    
    <div class="panel panel-primary">
        <div class="panel-heading">
            <h3 class="panel-title">🔍 Estado Actual de Mesas con Pedidos</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                $stmt = Conexion::conectar()->prepare("
                    SELECT 
                        m.id as mesa_id,
                        m.nombre as mesa_nombre,
                        COUNT(DISTINCT p.id) as total_pedidos,
                        COUNT(DISTINCT CASE WHEN p.estado = 'borrador' THEN p.id END) as pedidos_borrador,
                        COUNT(DISTINCT CASE WHEN p.estado = 'enviado' THEN p.id END) as pedidos_enviado,
                        COUNT(DISTINCT CASE WHEN p.estado = 'entregado' THEN p.id END) as pedidos_entregado,
                        COUNT(DISTINCT CASE WHEN p.estado = 'cancelado' THEN p.id END) as pedidos_cancelado,
                        COUNT(pvm.productos_id) as total_productos
                    FROM mesas m
                    LEFT JOIN pedidos p ON m.id = p.mesa_id
                    LEFT JOIN producto_vendido_mesa pvm ON m.id = pvm.mesas_id
                    WHERE m.id <= 10
                    GROUP BY m.id, m.nombre
                    ORDER BY m.id
                ");
                $stmt->execute();
                $mesas = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (count($mesas) > 0) {
                    echo "<table class='table table-striped'>";
                    echo "<thead>";
                    echo "<tr>";
                    echo "<th>Mesa</th>";
                    echo "<th>Total Pedidos</th>";
                    echo "<th>Borrador</th>";
                    echo "<th>Enviado</th>";
                    echo "<th>Entregado</th>";
                    echo "<th>Cancelado</th>";
                    echo "<th>Productos</th>";
                    echo "<th>Acciones</th>";
                    echo "</tr>";
                    echo "</thead>";
                    echo "<tbody>";
                    
                    foreach ($mesas as $mesa) {
                        $tiene_activos = ($mesa['pedidos_borrador'] > 0 || $mesa['pedidos_enviado'] > 0);
                        $clase_fila = $tiene_activos ? 'warning' : '';
                        
                        echo "<tr class='{$clase_fila}'>";
                        echo "<td><strong>{$mesa['mesa_nombre']}</strong></td>";
                        echo "<td>{$mesa['total_pedidos']}</td>";
                        echo "<td><span class='label label-info'>{$mesa['pedidos_borrador']}</span></td>";
                        echo "<td><span class='label label-warning'>{$mesa['pedidos_enviado']}</span></td>";
                        echo "<td><span class='label label-success'>{$mesa['pedidos_entregado']}</span></td>";
                        echo "<td><span class='label label-danger'>{$mesa['pedidos_cancelado']}</span></td>";
                        echo "<td>{$mesa['total_productos']}</td>";
                        echo "<td>";
                        
                        if ($tiene_activos) {
                            echo "<a href='index.php?action=registroPmesa&ida={$mesa['mesa_id']}' class='btn btn-sm btn-primary'>📋 Ver Mesa</a> ";
                            echo "<button onclick='testCancelarMesa({$mesa['mesa_id']})' class='btn btn-sm btn-danger'>❌ Test Cancelar</button>";
                        } else {
                            echo "<span class='text-muted'>Sin pedidos activos</span>";
                        }
                        
                        echo "</td>";
                        echo "</tr>";
                    }
                    
                    echo "</tbody>";
                    echo "</table>";
                } else {
                    echo "<div class='alert alert-info'>No hay mesas con pedidos</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-success">
        <div class="panel-heading">
            <h3 class="panel-title">🧪 Test Directo de Cancelación</h3>
        </div>
        <div class="panel-body">
            <form method="POST" onsubmit="return confirm('¿Está seguro de cancelar todos los pedidos de esta mesa?')">
                <div class="form-group">
                    <label for="mesa_test">Seleccionar Mesa para Test:</label>
                    <select name="mesa_test" id="mesa_test" class="form-control" required>
                        <option value="">-- Seleccionar Mesa --</option>
                        <?php
                        foreach ($mesas as $mesa) {
                            $tiene_activos = ($mesa['pedidos_borrador'] > 0 || $mesa['pedidos_enviado'] > 0);
                            if ($tiene_activos) {
                                echo "<option value='{$mesa['mesa_id']}'>{$mesa['mesa_nombre']} ({$mesa['pedidos_borrador']} borrador, {$mesa['pedidos_enviado']} enviado)</option>";
                            }
                        }
                        ?>
                    </select>
                </div>
                <button type="submit" name="test_cancelar" class="btn btn-danger">
                    🧪 Test Cancelar Mesa Seleccionada
                </button>
            </form>
            
            <?php
            if (isset($_POST['test_cancelar']) && !empty($_POST['mesa_test'])) {
                $mesa_id = (int)$_POST['mesa_test'];
                
                echo "<div class='alert alert-info'>";
                echo "<h4>🔄 Ejecutando test de cancelación para Mesa ID: {$mesa_id}</h4>";
                echo "</div>";
                
                try {
                    // Simular la sesión de mesa
                    $_SESSION['mesa'] = $mesa_id;
                    
                    require_once "../../models/crud.php";
                    require_once "../../controllers/controller.php";
                    
                    $controller = new MvcController();
                    $resultado = $controller->cancelarConPedidosController();
                    
                    if ($resultado == "sucess") {
                        echo "<div class='alert alert-success'>";
                        echo "<h4>✅ Test exitoso</h4>";
                        echo "<p>La mesa {$mesa_id} ha sido cancelada correctamente.</p>";
                        echo "<p><strong>Resultado:</strong> {$resultado}</p>";
                        echo "</div>";
                        
                        echo "<script>setTimeout(function(){ location.reload(); }, 2000);</script>";
                    } else {
                        echo "<div class='alert alert-danger'>";
                        echo "<h4>❌ Test falló</h4>";
                        echo "<p>Error al cancelar la mesa {$mesa_id}.</p>";
                        echo "<p><strong>Resultado:</strong> {$resultado}</p>";
                        echo "</div>";
                    }
                    
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>";
                    echo "<h4>❌ Excepción en test</h4>";
                    echo "<p>Error: " . $e->getMessage() . "</p>";
                    echo "</div>";
                }
            }
            ?>
        </div>
    </div>
    
    <hr>
    <div class="row">
        <div class="col-md-4">
            <a href="index.php?action=mesa" class="btn btn-primary btn-block">🔙 Volver a Mesas</a>
        </div>
        <div class="col-md-4">
            <a href="index.php?action=pedidosBarPendientes" class="btn btn-info btn-block">🍺 Ver Bar</a>
        </div>
        <div class="col-md-4">
            <a href="index.php?action=debug_estado_pedidos" class="btn btn-warning btn-block">📊 Debug Pedidos</a>
        </div>
    </div>
</div>

<script>
function testCancelarMesa(mesaId) {
    if (confirm('🧪 TEST: ¿Cancelar todos los pedidos de la Mesa ' + mesaId + '?\n\n' +
               '⚠️ Esto es una prueba de la nueva funcionalidad.\n' +
               '✅ Cancelará todos los pedidos pendientes\n' +
               '✅ Limpiará la mesa para nuevos pedidos')) {
        
        // Crear formulario dinámico para enviar el test
        var form = document.createElement('form');
        form.method = 'POST';
        form.style.display = 'none';
        
        var input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'mesa_test';
        input.value = mesaId;
        
        var submit = document.createElement('input');
        submit.type = 'hidden';
        submit.name = 'test_cancelar';
        submit.value = '1';
        
        form.appendChild(input);
        form.appendChild(submit);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

</body>
</html>
