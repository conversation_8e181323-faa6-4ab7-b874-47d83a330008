
<?php 
	ob_start();
	class controller<PERSON>istaVentas extends MvcController
		{			
			#-------------------------------------
				#vista Ventas Controller
			#------------------------------------
					public function vistaVentasGeneroController($fecha1,$fecha2)
						{
							//echo "<script>alert('Controlles fecah 1 ".$_POST["fecha2"]." ');</script>";
							$respuesta = VistaVentas::vistaVentasGeneroModel($fecha1,$fecha2);
								return $respuesta;
						}
			#-------------------------------------
				#vista Ventas Controller
			#------------------------------------
					public function vistaVentasController($fecha1,$fecha2)
						{
							//echo "<script>alert('Controlles fecah 1 ".$_POST["fecha2"]." ');</script>";
							$respuesta = VistaVentas::vistaVentasModel($fecha1,$fecha2);
								return $respuesta;
						}
			#-------------------------------------
				#vista Perdidas Controller
			#------------------------------------
					public function vistaPerdidasController($fecha1,$fecha2)
						{
							$respuesta = VistaVentas::vistaPerdidasModel($fecha1,$fecha2);
								return $respuesta;
						}
			#-------------------------------------
			#vista Product Report Aumento de Inventario
			#------------------------------------
			public static function vistaReporAumentoInventarioProductController($fecha1,$fecha2,$code,$sucursal)
				{
					//echo "<script>alert('Controlles fecah 1 ".$_POST["fecha2"]." ');</script>";
					$respuesta = VistaVentas::vistaReporAumentoInventarioProductModel($fecha1,$fecha2,$code,$sucursal);
						return $respuesta;
				}	
			#-------------------------------------
				#vista Inventario Bodega Controller
			#------------------------------------
					public function inventarioBodegaController()
						{
							$respuesta = VistaVentas::inventarioBodegaModel();
								return $respuesta;
						}	
			#-------------------------------------
				#vista Inventario Cocina Controller
			#------------------------------------
					public function inventarioCocinaController()
						{
							$respuesta = VistaVentas::inventarioCocinaModel();
								return $respuesta;
						}	
			#-------------------------------------
				#vista Inventario Cafeteria Controller
			#------------------------------------
					public function inventarioCafeteriaController()
						{
							$respuesta = VistaVentas::inventarioCafeteriaModel();
								return $respuesta;
						}	
			#-------------------------------------
				#Costo Producto Controller
			#------------------------------------
					public function CostoProductoController($idproducto)
						{
							$respuesta = VistaVentas::CostoProductoModel($idproducto);
								return $respuesta;
						}
			#-------------------------------------
				#vista Product Report Compra Controller
			#------------------------------------
					public static function vistaReporCompraProductController($fecha1,$fecha2,$code)
						{
							//echo "<script>alert('Controlles fecah 1 ".$_POST["fecha2"]." ');</script>";
							$respuesta = VistaVentas::vistaReporCompraProductModel($fecha1,$fecha2,$code);
								return $respuesta;
						}
			#-------------------------------------
				#vista Product Report Venta Controller
			#------------------------------------
					public static function vistaReporVentaProductController($fecha1,$fecha2,$code)
						{
							//echo "<script>alert('Controlles fecah 1 ".$_POST["fecha2"]." ');</script>";
							$respuesta = VistaVentas::vistaReporVentaProductModel($fecha1,$fecha2,$code);
								return $respuesta;
						}
			#-------------------------------------
				#Datos Producto Controller
			#------------------------------------
					public function DatosProductoController($codigo)
						{
							$respuesta = VistaVentas::DatosProductoModel($codigo);
								return $respuesta;
						}	
			#------------------------------------------
		#Reportes Excel diario contador
					public function reporteExcelController(){
						 //echo " Entro excel fecha1=".$_POST["fecha1"];
						if (isset($_POST["fecha1"]) and isset($_POST["fecha2"])) {
							$_SESSION['fecha1']=$_POST["fecha1"]." 00:00:01";
							$_SESSION['fecha2']=$_POST["fecha2"]." 23:59:59";


							echo' <script>window.open("reporteExcel2","_blank");</script>';
						}
					}		
		#---------------------------------------------------------------------------------------------------- 
		#Reportes Excel diario contador
					public function reporteExcel2Controller($fecha1,$fecha2){						
		                    //echo " Entro excel fecha1=".$fecha1;
							$datosController = array( "fecha1"=>$fecha1,
													  "fecha2"=>$fecha2);

							$respuesta = Datos::reporteExcel2Model($datosController);
							return 	$respuesta;				
						
					}
		#------------------------------------------------------------------------------------------------------------------------------------

		}			
			