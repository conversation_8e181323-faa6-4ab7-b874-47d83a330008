# BEGIN cPanel-generated php ini directives, do not edit
# Manual editing of this file may result in unexpected behavior.
# To make changes to this file, use the cPanel MultiPHP INI Editor (Home >> Software >> MultiPHP INI Editor)
# For more information, read our documentation (https://go.cpanel.net/EA4ModifyINI)
<IfModule php7_module>
   php_flag display_errors Off
   php_value max_execution_time 60
   php_value max_input_time 120
   php_value max_input_vars 10000
   php_value memory_limit 512M
   php_value post_max_size 1024M
   php_value session.gc_maxlifetime 1440
   php_value session.save_path "/var/cpanel/php/sessions/ea-php74"
   php_value upload_max_filesize 1024M
   php_flag zlib.output_compression On
</IfModule>
<IfModule lsapi_module>
   php_flag display_errors Off
   php_value max_execution_time 60
   php_value max_input_time 120
   php_value max_input_vars 10000
   php_value memory_limit 512M
   php_value post_max_size 1024M
   php_value session.gc_maxlifetime 1440
   php_value session.save_path "/var/cpanel/php/sessions/ea-php74"
   php_value upload_max_filesize 1024M
   php_flag zlib.output_compression On
</IfModule>
# END cPanel-generated php ini directives, do not edit