<?php
	$registroCsuministro = new controllerCompraSuministro();
	$proveedores = $registroCsuministro -> listaProveedoresController();
	if(isset($_GET["action"]))
		{	
			if($_GET["action"] == "okCs")
			{echo "Registro Exitoso";	}
		}
?>
<h1>REGISTRO DE COMPRA SUMINISTRO</h1><br>
<form method="post">	
	<table>
		<thead>
			<tr>
				<td colspan="2"></td>
			</tr>
		</thead>		
			<tr>
				<td><label> ROVEEDOR : </label></td>
				<td>
					<?php  
					# # %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  proveedores  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
						if($proveedores=="error")
							{	echo "debe registrar el proveedores"; }
						else{
								echo "<label></label>";
								$result='<select name="proveedores"  id="proveedores" autofocus="autofocus">';
								$result.=' <option value="-1">proveedores</option>';
								foreach ($proveedores as $row => $item)
								 	{	$result.=' <option value="'.$item["id"].'">'.$item["nombre"].'</option>';	}	
								 $result.='</select>';
								 echo $result;
							}  
					# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  End proveedores  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
					 ?> 
					<a href="registroProveedor" target="_blank"> Registrar proveedor</a>
				</td>
			</tr>
			<tr>
				<td><label> VALOR FACTURA: </label></td>
				<td><input type="text" placeholder="valor_compra" name="valor_compraCsuministroRegistro" required></td>
			</tr>
			<tr>
				<td><label> FECHA : </label></td>
				<td><input type="date" placeholder="AAAA-MM-DD HH:MM:SS" name="fecha_horaCsuministroRegistro" required></td>
			</tr>
			<tr>
				<td><label> No FACTURA : </label></td>
				<td><input type="text" placeholder="numero factura compra" name="numero_factura_compraCsuministroRegistro" required></td>
			</tr>
		<thead>
			<tr>
				<td colspan="2"></td>
			</tr>
		</thead>		
	</table>
	
	<input type="submit" value="Enviar">

</form>

<?php 
	$registroCsuministro -> registroCsuministroController(); 
?>
