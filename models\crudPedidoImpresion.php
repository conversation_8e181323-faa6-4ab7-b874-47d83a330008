<?php
/**
 * Clase para manejar la lógica de impresión por categorías en pedidos
 * Sistema simplificado con botones dinámicos por categoría
 */
class PedidoImpresion {
    
    /**
     * Obtener categorías de impresión presentes en un pedido
     * @param int $pedido_id - ID del pedido
     * @return array - Categorías con productos y botones a mostrar
     */
    public static function obtenerCategoriasDelPedido($pedido_id) {
        try {
            $conexion = new Conexion();
            $pdo = $conexion->conectar();
            
            // Obtener productos del pedido con sus categorías
            $stmt = $pdo->prepare("
                SELECT 
                    p.id as producto_id,
                    p.nombre as producto_nombre,
                    p.precio,
                    p.categoria as categoria_producto,
                    dp.cantidad,
                    dp.precio_unitario,
                    dp.subtotal,
                    COALESCE(ci.impresora, 'cocina') as categoria_impresora
                FROM detalle_pedidos dp
                INNER JOIN productos p ON dp.producto_id = p.id
                LEFT JOIN categoria_impresora ci ON p.categoria = ci.categoria AND ci.activo = 1
                WHERE dp.pedido_id = :pedido_id
                ORDER BY categoria_impresora, p.nombre
            ");
            
            $stmt->bindParam(":pedido_id", $pedido_id, PDO::PARAM_INT);
            $stmt->execute();
            $productos = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (empty($productos)) {
                return [
                    'categorias' => [],
                    'total_productos' => 0,
                    'botones_disponibles' => []
                ];
            }
            
            // Agrupar productos por categoría de impresora
            $categorias = [];
            $botones_disponibles = [];
            
            foreach ($productos as $producto) {
                $categoria_imp = $producto['categoria_impresora'];
                
                if (!isset($categorias[$categoria_imp])) {
                    $categorias[$categoria_imp] = [
                        'nombre' => self::obtenerNombreCategoria($categoria_imp),
                        'productos' => [],
                        'total_productos' => 0,
                        'total_precio' => 0
                    ];
                }
                
                $categorias[$categoria_imp]['productos'][] = $producto;
                $categorias[$categoria_imp]['total_productos'] += $producto['cantidad'];
                $categorias[$categoria_imp]['total_precio'] += $producto['subtotal'];
                
                // Agregar a botones disponibles si no está ya
                if (!in_array($categoria_imp, $botones_disponibles)) {
                    $botones_disponibles[] = $categoria_imp;
                }
            }
            
            return [
                'categorias' => $categorias,
                'total_productos' => count($productos),
                'botones_disponibles' => $botones_disponibles,
                'pedido_id' => $pedido_id
            ];
            
        } catch (Exception $e) {
            error_log("Error en obtenerCategoriasDelPedido: " . $e->getMessage());
            return [
                'categorias' => [],
                'total_productos' => 0,
                'botones_disponibles' => [],
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Generar contenido de impresión para una categoría específica
     * @param int $pedido_id
     * @param string $categoria_impresora
     * @return array
     */
    public static function generarContenidoImpresion($pedido_id, $categoria_impresora) {
        try {
            $conexion = new Conexion();
            $pdo = $conexion->conectar();
            
            // Obtener información del pedido
            $stmt = $pdo->prepare("
                SELECT 
                    ped.numero_pedido,
                    ped.mesa_id,
                    ped.fecha_pedido,
                    ped.estado,
                    m.numero as mesa_numero
                FROM pedidos ped
                LEFT JOIN mesas m ON ped.mesa_id = m.id
                WHERE ped.id = :pedido_id
            ");
            
            $stmt->bindParam(":pedido_id", $pedido_id, PDO::PARAM_INT);
            $stmt->execute();
            $pedido = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$pedido) {
                return ['success' => false, 'error' => 'Pedido no encontrado'];
            }
            
            // Obtener productos de la categoría específica
            $stmt = $pdo->prepare("
                SELECT 
                    p.nombre as producto_nombre,
                    p.precio,
                    dp.cantidad,
                    dp.precio_unitario,
                    dp.subtotal
                FROM detalle_pedidos dp
                INNER JOIN productos p ON dp.producto_id = p.id
                LEFT JOIN categoria_impresora ci ON p.categoria = ci.categoria AND ci.activo = 1
                WHERE dp.pedido_id = :pedido_id 
                AND COALESCE(ci.impresora, 'cocina') = :categoria
                ORDER BY p.nombre
            ");
            
            $stmt->bindParam(":pedido_id", $pedido_id, PDO::PARAM_INT);
            $stmt->bindParam(":categoria", $categoria_impresora, PDO::PARAM_STR);
            $stmt->execute();
            $productos = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (empty($productos)) {
                return ['success' => false, 'error' => 'No hay productos para esta categoría'];
            }
            
            // Generar contenido del ticket
            $contenido = self::generarTicket($pedido, $productos, $categoria_impresora);
            
            return [
                'success' => true,
                'contenido' => $contenido,
                'pedido' => $pedido,
                'productos' => $productos,
                'categoria' => $categoria_impresora,
                'total_productos' => count($productos)
            ];
            
        } catch (Exception $e) {
            error_log("Error en generarContenidoImpresion: " . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Generar ticket formateado para impresión
     */
    private static function generarTicket($pedido, $productos, $categoria) {
        $contenido = "";
        
        // Encabezado
        $contenido .= "MESA: " . ($pedido['mesa_numero'] ?? 'N/A') . "\n";
        $contenido .= "PEDIDO: " . $pedido['numero_pedido'] . "\n";
        $contenido .= "CATEGORIA: " . strtoupper($categoria) . "\n";
        $contenido .= "FECHA: " . date('d/m/Y H:i', strtotime($pedido['fecha_pedido'])) . "\n";
        $contenido .= "ESTADO: " . strtoupper($pedido['estado']) . "\n";
        $contenido .= "--------------------------------\n";
        
        // Productos
        $total_categoria = 0;
        foreach ($productos as $producto) {
            $cantidad = $producto['cantidad'];
            $nombre = substr($producto['producto_nombre'], 0, 25);
            $subtotal = $producto['subtotal'];
            $total_categoria += $subtotal;
            
            $contenido .= sprintf("%-25s %2d\n", $nombre, $cantidad);
            $contenido .= sprintf("  $%s\n", number_format($subtotal, 0));
        }
        
        // Total de la categoría
        $contenido .= "--------------------------------\n";
        $contenido .= sprintf("TOTAL %s: $%s\n", strtoupper($categoria), number_format($total_categoria, 0));
        $contenido .= "--------------------------------\n";
        
        // Pie
        $contenido .= "Hora impresion: " . date('H:i:s') . "\n";
        
        return $contenido;
    }
    
    /**
     * Obtener nombre amigable de la categoría
     */
    private static function obtenerNombreCategoria($categoria) {
        $nombres = [
            'bar' => '🍺 BAR',
            'cocina' => '🍳 COCINA', 
            'asados' => '🥩 ASADOS',
            'test' => '🧪 TEST'
        ];
        
        return $nombres[$categoria] ?? strtoupper($categoria);
    }
    
    /**
     * Obtener configuración de impresoras disponibles
     */
    public static function obtenerImpresorasDisponibles() {
        return [
            'bar' => ['ip' => '**************', 'puerto' => 9100, 'nombre' => '🍺 BAR'],
            'cocina' => ['ip' => '**************', 'puerto' => 9100, 'nombre' => '🍳 COCINA'],
            'asados' => ['ip' => '**************', 'puerto' => 9100, 'nombre' => '🥩 ASADOS'],
            'test' => ['ip' => '*************', 'puerto' => 9100, 'nombre' => '🧪 TEST']
        ];
    }
    
    /**
     * Registrar log de impresión
     */
    public static function registrarLogImpresion($pedido_id, $categoria, $metodo, $success, $ip_cliente = null) {
        try {
            $conexion = new Conexion();
            $pdo = $conexion->conectar();
            
            $stmt = $pdo->prepare("
                INSERT INTO logs_impresion_pedidos 
                (pedido_id, categoria, metodo, success, ip_cliente, fecha_hora) 
                VALUES 
                (:pedido_id, :categoria, :metodo, :success, :ip_cliente, NOW())
            ");
            
            $stmt->bindParam(":pedido_id", $pedido_id, PDO::PARAM_INT);
            $stmt->bindParam(":categoria", $categoria, PDO::PARAM_STR);
            $stmt->bindParam(":metodo", $metodo, PDO::PARAM_STR);
            $stmt->bindParam(":success", $success, PDO::PARAM_BOOL);
            $stmt->bindParam(":ip_cliente", $ip_cliente, PDO::PARAM_STR);
            
            $stmt->execute();
            
        } catch (Exception $e) {
            error_log("Error registrando log de impresión: " . $e->getMessage());
        }
    }
}
?>
