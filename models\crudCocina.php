<?php
require_once "conexion.php";
class DatosCocina extends Conexion
{
	#REGISTRO DE COCINA
	#-------------------------------------
	 public static function registroCocinaModel($datosModel, $tabla)
		{ //echo "<script>alert('CRUD mesero: ".$datosModel["mesero"]."');</script>";
		 $mesaPro=DatosCocina::buscarMesaModel($datosModel["mesa"]);
		 if ($mesaPro==0)
		 	{
		 	 echo "<script>alert('No hay pedido');</script>";
		 	}
		 else
		 	{
		 	 $stmt = Conexion::conectar();
		  	 date_default_timezone_set("America/Bogota");
		 	 $fecha_pedido=strftime("%Y-%m-%d %H:%M:%S");
		 	 try
				{	//echo "<script>alert('Entro  try');</script>";
				 $stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
				 $stmt->beginTransaction();
				  $consulta="INSERT INTO pedido_cocina (mesero_id, fecha) VALUES (".$datosModel["mesero"].", '".$fecha_pedido."')";
				 //echo " <br>--".$consulta." <br>--";
				 $stmt->exec($consulta);
				 $ultimo_id=$stmt->lastInsertId();	//ultimo pedido
				 foreach ($mesaPro as $row => $item)
					{	//echo "<script>alert('foreach SESSION recorrido ' );</script>";
					 $cocinaPro=DatosCocina::buscarCocinaModel($datosModel["mesa"], $item['productos_id']);
					 $dif=$item['cantidad']-$cocinaPro['sumcantidad'];
					 ///echo " <br>-- diferencia : ".$dif." <br>--";
					 if ($cocinaPro==0)
					 	{
					 	 $consulta1 = "INSERT INTO cocina (pedido_cocina, mesa_id, producto_id, cantidad, nota, fecha)
					 		VALUES (".$ultimo_id.", ".$datosModel['mesa'].", ".$item['productos_id'].",".$item['cantidad'].", '".$item['nota']."', '".$fecha_pedido."')";
						 //echo " <br>--vacio <br>--";
						 //echo " <br>--".$consulta1." <br>--";
						 $stmt->exec($consulta1);	//.$datosModel["efectivo"].
					 	}
					 elseif ($dif>0)
					 	{
					 	 $consulta1 = "INSERT INTO cocina (pedido_cocina, mesa_id, producto_id, cantidad, nota, fecha)
						 	VALUES (".$ultimo_id.", ".$datosModel['mesa'].", ".$item['productos_id'].",".$dif.", '".$item['nota']."', '".$fecha_pedido."')";
						 //echo " <br>--mayor <br>--";
						 //echo " <br>--".$consulta1." <br>--";
						 $stmt->exec($consulta1);	//.$datosModel["efectivo"].
					 	}
					 elseif ($dif<0)
					 	{
					 	 $consulta1 = "UPDATE cocina SET cantidad=cantidad+ $dif  WHERE producto_id=".$item['productos_id']." AND mesas_id=".$datosModel['mesa'];
						 //echo " <br>--Menor <br>--";
							//echo " <br>--".$consulta1." <br>--";
						 $stmt->exec($consulta1);	//.$datosModel["efectivo"].
					 	}

					}
				 $stmt->commit();
				 //echo "<script>alert('fin de cruD try que pasa');</script>";
				 return "success";
				 $stmt->close();
				}
			 catch (PDOException $e)
				{	echo "<script>alert('catch entro')</script>";
					$stmt->rollBack();
					print "Error!: ".$e->getMessage()."</br>";
					return "Error!: ".$e->getMessage()."</br>";
				} /*return "success";	*/
		 	}
		}
	#----------------------------------
	#BUSCAR COCINA
	#-------------------------------------
	 public static function buscarCocinaModel($datosModel, $producto)
		{
			$consulta = "SELECT pedido_cocina, mesa_id, producto_id, SUM(cantidad) AS sumcantidad, nota, fecha FROM cocina WHERE mesa_id = :mesa AND producto_id = :producto";
			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->bindParam(":mesa", $datosModel, PDO::PARAM_INT);
			$stmt->bindParam(":producto", $producto, PDO::PARAM_INT);
			//echo " <br>--".$consulta." <br>--";
			$stmt->execute();
			$r=$stmt->fetch();
			$c2=$stmt->rowCount();
			//echo '<br> C2='.$c2.'<br>';
			if ($c2>0)  {	return $r;	}
			else  {	return 0;	}
			$stmt->close();
		}
	#----------------------------------
	#BUSCAR MESA VENDIDO
	#-------------------------------------
	 public static function buscarMesaModel($datosModel)
		{
			$consulta="SELECT * FROM producto_vendido_mesa WHERE mesas_id = :mesa and cocina='si'";
			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->bindParam(":mesa", $datosModel, PDO::PARAM_INT);
			//echo " <br>--".$consulta." <br>--";
			$stmt->execute();
			$r=$stmt->fetchAll();
			$c2=$stmt->rowCount();
			//echo '<br> C2='.$c2.'<br>';
			if ($c2>0)  {	return $r;	}
			else  {	return 0;	}
			$stmt->close();
		}
	#----------------------------------
	#VISTA COCINA
	#-------------------------------------
	 public static function vistaCocinaModel($tabla)
		{
		 $consulta="SELECT c.pedido_cocina AS cpedido_cocina, c.mesa_id AS cmesa_id, c.producto_id AS cproducto_id, c.cantidad AS ccantidad, c.nota AS cnota, c.fecha AS cfecha, c.estado AS cestado, p.id AS pid, p.nombre AS pnombre,m.id AS mid, m.nombre AS mnombre
				FROM cocina c, productos p, mesas m
				WHERE c.producto_id=p.id AND m.id=c.mesa_id ORDER BY c.pedido_cocina DESC";
			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->execute();
			return $stmt->fetchAll();
			$stmt->close();
		}
	#----------------------------------
	#RESETEAR COCINA Y PEDIDO COCINA
	#-------------------------------------
	 public static function resetearModel($tabla)
		{//echo "<script>alert('Entro CRUD ".$datosModel['nombre']." no')</script>";
		 date_default_timezone_set("America/Bogota");
		 $fecha_creado=strftime("%Y-%m-%d %H:%M:%S");
		 $stmt = Conexion::conectar();
		 try
			{
			 $stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
			 $stmt->beginTransaction();
			 $consulta = "TRUNCATE TABLE cocina ";
			echo "<br>".$consulta."<br>";
				$stmt->exec($consulta);
				$consulta1 = "TRUNCATE TABLE pedido_cocina ";
				echo "<br>".$consulta1."<br>";
				$stmt->exec($consulta1);
				$stmt->commit();
				//echo "<script>alert('fin de cruD try que pasa');</script>";
				return "success";
				$stmt->close();
			 }
			catch (Exception $e)
			 {
			 	echo "<script>alert('catch error');</script>";
			 	$stmt->rollBack();
				print "Error!: ".$e->getMessage()."</br>";
				return "Error!: ".$e->getMessage()."</br>";
			 }

		}
	#-------------------------------------
	/*#EDITAR COCINA
	#-------------------------------------
	 public static function editarCocinaModel($datosModel, $tabla)
		{
			$stmt = Conexion::conectar()->prepare("SELECT * FROM $tabla WHERE id = :id");
			$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);
			$stmt->execute();
			return $stmt->fetch();
			$stmt->close();
		}
	#----------------------------------
	#ACTUALIZAR COCINA
	#-------------------------------------

		public static function actualizarCocinaModel($datosModel, $tabla)
			{ echo "<script>alert('Entro Actualizar Producto')</script>";
				$stmt = Conexion::conectar()->prepare("UPDATE $tabla SET nombre = :nombre, cupo = :cupo, descripcion = :descripcion WHERE id = :id");
				$stmt->bindParam(":nombre", $datosModel["nombre"], PDO::PARAM_STR);
				$stmt->bindParam(":cupo", $datosModel["cupo"], PDO::PARAM_STR);
				$stmt->bindParam(":descripcion", $datosModel["descripcion"], PDO::PARAM_STR);
				$stmt->bindParam(":id", $datosModel["id"], PDO::PARAM_INT);
				if($stmt->execute())
					{echo "<script>alert('Guardo Actualizar Roll')</script>";
						return "success";	}
				else{	return "error";			}
				$stmt->close();
			}
	#----------------------------------*/
	#TERMINADO
	#------------------------------------
	 public static function terminadoModel($datosModel, $tabla)
		{ 	//echo "<script>alert('Entro CRUD ".$datosModel["fecha_hora"]." es')</script>";
		 	$consulta = "UPDATE $tabla SET estado='Terminado' WHERE producto_id=:id AND fecha = :fecha_hora";
			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->bindParam(":id", $datosModel["productos_id"], PDO::PARAM_INT);
			$stmt->bindParam(":fecha_hora", $datosModel["fecha_hora"], PDO::PARAM_STR);
			if($stmt->execute())
				{	return "success";	}
			else{	return "error";		}
			$stmt->close();
		}
	#----------------------------------
}