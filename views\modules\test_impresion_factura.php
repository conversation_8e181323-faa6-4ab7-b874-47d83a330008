<?php
session_start();

echo "<!DOCTYPE html>";
echo "<html><head><title>Test Impresión Factura</title></head><body>";
echo "<h2>🖨️ Test de Impresión de Factura</h2>";

// Simular datos de sesión para test
$_SESSION["productos"] = [
    [
        'codigo' => 1,
        'nombre' => 'Producto Test 1',
        'precio' => 15000,
        'cantidad' => 2,
        'descuento' => 0,
        'total' => 30000
    ],
    [
        'codigo' => 2,
        'nombre' => 'Producto Test 2',
        'precio' => 8000,
        'cantidad' => 1,
        'descuento' => 0,
        'total' => 8000
    ]
];

$_SESSION["pedidos_id"] = 999;
$_SESSION["turno_cajero_id"] = 1;
$_SESSION["subtotal"] = 38000;
$_SESSION["total"] = 38000;
$_SESSION['idFactura'] = 999;
$_SESSION['propina'] = 0;
$_SESSION['mesaFactura'] = 'FACTURA_RAPIDA';
$_SESSION["efectivo"] = 40000;
$_SESSION["tarjeta"] = 0;
$_SESSION["bancolombia"] = 0;
$_SESSION["nequi"] = 0;
$_SESSION["daviplata"] = 0;
$_SESSION["cambio"] = 2000;

echo "<h3>✅ Variables de Sesión Configuradas</h3>";
echo "<p>He configurado variables de sesión de prueba para simular una factura.</p>";

echo "<h4>📊 Datos de la Factura de Prueba:</h4>";
echo "<ul>";
echo "<li><strong>Productos:</strong> " . count($_SESSION["productos"]) . "</li>";
echo "<li><strong>Subtotal:</strong> $" . number_format($_SESSION["subtotal"]) . "</li>";
echo "<li><strong>Total:</strong> $" . number_format($_SESSION["total"]) . "</li>";
echo "<li><strong>Efectivo:</strong> $" . number_format($_SESSION["efectivo"]) . "</li>";
echo "<li><strong>Cambio:</strong> $" . number_format($_SESSION["cambio"]) . "</li>";
echo "<li><strong>Factura ID:</strong> " . $_SESSION['idFactura'] . "</li>";
echo "</ul>";

?>

<h3>🖨️ Tests de Impresión</h3>

<div style="margin: 20px 0;">
    <h4>Test 1: Abrir PDF Directamente</h4>
    <p>Abre el PDF en una nueva ventana para verificar que se genere correctamente:</p>
    <button onclick="window.open('pdf.php', '_blank')" style="background: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;">
        📄 Abrir PDF de Prueba
    </button>
</div>

<div style="margin: 20px 0;">
    <h4>Test 2: Simular Respuesta AJAX</h4>
    <p>Simula la respuesta que envía ajaxFacturacionRapida.php:</p>
    <button onclick="simularRespuestaAjax()" style="background: #007bff; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;">
        🔧 Simular AJAX
    </button>
    
    <div id="resultado_ajax" style="margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 5px; min-height: 50px;">
        El resultado aparecerá aquí...
    </div>
</div>

<div style="margin: 20px 0;">
    <h4>Test 3: Test Completo de Facturación</h4>
    <p>Prueba todo el flujo de facturación rápida:</p>
    <button onclick="testFacturacionCompleta()" style="background: #dc3545; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;">
        🚀 Test Facturación Completa
    </button>
</div>

<h3>📋 Verificaciones</h3>
<div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;">
    <h4>✅ Cosas a Verificar:</h4>
    <ol>
        <li><strong>PDF se abre:</strong> El archivo pdf.php debe abrir en nueva ventana</li>
        <li><strong>Datos correctos:</strong> Los productos y totales deben aparecer</li>
        <li><strong>Impresión automática:</strong> Debe aparecer el diálogo de impresión</li>
        <li><strong>Formato correcto:</strong> La factura debe verse bien formateada</li>
    </ol>
</div>

<h3>🔧 Información de Debug</h3>
<div style="background: #e9ecef; padding: 10px; border-radius: 5px; font-family: monospace;">
    <p><strong>Archivo PDF:</strong> <?php echo __DIR__ . '/pdf.php'; ?></p>
    <p><strong>Sesión ID:</strong> <?php echo session_id(); ?></p>
    <p><strong>Variables de sesión configuradas:</strong> <?php echo count($_SESSION); ?></p>
</div>

<script>
function simularRespuestaAjax() {
    // Simular la respuesta exacta que envía ajaxFacturacionRapida.php
    const respuesta = '<script>alert("✅ Facturación rápida completada exitosamente\\n\\nFactura: 999");window.open("views/modules/pdf.php","_blank");</script>success_rapida_factura_999';
    
    document.getElementById('resultado_ajax').innerHTML = 
        '<h5>📤 Respuesta AJAX Simulada:</h5>' +
        '<div style="background: white; padding: 10px; border: 1px solid #ccc; border-radius: 3px; font-family: monospace; white-space: pre-wrap;">' + 
        respuesta + 
        '</div>' +
        '<p style="margin-top: 10px;"><strong>Ejecutando script...</strong></p>';
    
    // Ejecutar el script como lo haría el navegador
    setTimeout(function() {
        alert("✅ Facturación rápida completada exitosamente\n\nFactura: 999");
        window.open("pdf.php", "_blank");
        
        document.getElementById('resultado_ajax').innerHTML += 
            '<p style="color: green;">✅ Script ejecutado - PDF debería haberse abierto</p>';
    }, 1000);
}

function testFacturacionCompleta() {
    if (confirm('¿Realizar test completo de facturación rápida?\n\nEsto simulará todo el proceso incluyendo la impresión.')) {
        
        // Simular datos de facturación
        const datos = {
            productos: JSON.stringify([
                {id: 1, nombre: 'Producto Test', precio: 15000, cantidad: 1}
            ]),
            efectivo: 20000,
            tarjeta: 0,
            nequi: 0,
            daviplata: 0,
            bancolombia: 0,
            propina: 0,
            total: 15000,
            cedula: 'TEST_IMPRESION'
        };
        
        // Llamar al AJAX real
        fetch('ajaxFacturacionRapida.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams(datos)
        })
        .then(response => response.text())
        .then(data => {
            console.log('Respuesta completa:', data);
            
            // Buscar y ejecutar scripts en la respuesta
            const scriptMatch = data.match(/<script>(.*?)<\/script>/);
            if (scriptMatch) {
                eval(scriptMatch[1]);
            }
            
            alert('Test completo ejecutado. Revisa si se abrió el PDF.');
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error en test completo: ' + error);
        });
    }
}
</script>

<br><br>
<a href="../../index.php?action=facturacionRapida" style="background: #28a745; color: white; padding: 10px; text-decoration: none; border-radius: 5px;">🚀 Ir a Facturación Rápida</a>
<a href="pdf.php" target="_blank" style="background: #ffc107; color: black; padding: 10px; text-decoration: none; border-radius: 5px;">📄 Ver PDF Directo</a>

</body>
</html>
