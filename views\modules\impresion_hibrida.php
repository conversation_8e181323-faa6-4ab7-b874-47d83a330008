<?php
// Sistema de Impresión Híbrida - Con y Sin Proxy
echo "<h1>🔄 Sistema de Impresión Híbrida</h1>";
echo "<p><strong>Solución inteligente que funciona con o sin proxy, independiente de IP pública</strong></p>";

echo "<div style='background-color: #d1ecf1; padding: 15px; border-left: 4px solid #bee5eb; margin: 20px 0;'>";
echo "<h3>🎯 Características del Sistema Híbrido:</h3>";
echo "<ul>";
echo "<li>✅ <strong>Detección automática</strong> de red local vs externa</li>";
echo "<li>✅ <strong>Fallback inteligente</strong> cuando cambia la IP</li>";
echo "<li>✅ <strong>Impresión directa</strong> desde dispositivos locales</li>";
echo "<li>✅ <strong>Proxy opcional</strong> para servidor externo</li>";
echo "<li>✅ <strong>Test integrado</strong> desde portátil y celulares</li>";
echo "</ul>";
echo "</div>";

// Configuración actual
echo "<h2>📋 Configuración Actual del Sistema:</h2>";
try {
    require_once '../../models/conexion.php';
    $conexion = new Conexion();
    $pdo = $conexion->conectar();
    
    // Obtener configuración de impresoras
    $stmt = $pdo->query("SELECT * FROM impresoras ORDER BY nombre");
    $impresoras = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Crear tabla de configuración híbrida si no existe
    $pdo->exec("CREATE TABLE IF NOT EXISTS config_impresion (
        id INT AUTO_INCREMENT PRIMARY KEY,
        clave VARCHAR(100) NOT NULL UNIQUE,
        valor TEXT NOT NULL,
        descripcion TEXT,
        fecha_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
    
    // Insertar configuraciones por defecto
    $configs_default = [
        ['modo_impresion', 'hibrido', 'Modo de impresión: directo, proxy, hibrido'],
        ['ip_proxy', '***********', 'IP del proxy (tu portátil)'],
        ['puerto_proxy', '3000', 'Puerto del proxy'],
        ['timeout_conexion', '5', 'Timeout en segundos para conexiones'],
        ['reintentos_max', '3', 'Número máximo de reintentos'],
        ['red_local', '192.168.68', 'Prefijo de red local'],
        ['impresion_directa_habilitada', '1', 'Permitir impresión directa desde red local']
    ];
    
    foreach ($configs_default as $config) {
        $stmt = $pdo->prepare("INSERT IGNORE INTO config_impresion (clave, valor, descripcion) VALUES (?, ?, ?)");
        $stmt->execute($config);
    }
    
    // Obtener configuración actual
    $stmt = $pdo->query("SELECT * FROM config_impresion ORDER BY clave");
    $configuracion = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    echo "<p>⚠️ Error: " . $e->getMessage() . "</p>";
    exit;
}

// Mostrar configuración
echo "<div style='display: flex; gap: 20px; margin: 15px 0;'>";

// Impresoras
echo "<div style='flex: 1; border: 1px solid #ccc; padding: 15px; border-radius: 5px;'>";
echo "<h4>🖨️ Impresoras Configuradas:</h4>";
if ($impresoras) {
    echo "<table style='width: 100%; border-collapse: collapse;'>";
    echo "<tr style='background-color: #f8f9fa;'>";
    echo "<th style='border: 1px solid #ddd; padding: 5px; font-size: 12px;'>Nombre</th>";
    echo "<th style='border: 1px solid #ddd; padding: 5px; font-size: 12px;'>IP</th>";
    echo "<th style='border: 1px solid #ddd; padding: 5px; font-size: 12px;'>Puerto</th>";
    echo "<th style='border: 1px solid #ddd; padding: 5px; font-size: 12px;'>Estado</th>";
    echo "</tr>";
    
    foreach ($impresoras as $imp) {
        $estado = $imp['activa'] ? '✅' : '❌';
        echo "<tr>";
        echo "<td style='border: 1px solid #ddd; padding: 5px; font-size: 11px;'>{$imp['nombre']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 5px; font-size: 11px;'>{$imp['ip']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 5px; font-size: 11px;'>{$imp['puerto']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 5px; font-size: 11px;'>$estado</td>";
        echo "</tr>";
    }
    echo "</table>";
}
echo "</div>";

// Configuración del sistema
echo "<div style='flex: 1; border: 1px solid #ccc; padding: 15px; border-radius: 5px;'>";
echo "<h4>⚙️ Configuración del Sistema:</h4>";
if ($configuracion) {
    echo "<table style='width: 100%; border-collapse: collapse;'>";
    foreach ($configuracion as $config) {
        echo "<tr>";
        echo "<td style='border: 1px solid #ddd; padding: 5px; font-size: 11px; font-weight: bold;'>{$config['clave']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 5px; font-size: 11px;'>{$config['valor']}</td>";
        echo "</tr>";
    }
    echo "</table>";
}
echo "</div>";

echo "</div>";

// Actualizar configuración
if (isset($_POST['actualizar_config'])) {
    try {
        foreach ($_POST as $key => $value) {
            if (strpos($key, 'config_') === 0) {
                $clave = str_replace('config_', '', $key);
                $stmt = $pdo->prepare("UPDATE config_impresion SET valor = ? WHERE clave = ?");
                $stmt->execute([$value, $clave]);
            }
        }
        
        echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0;'>";
        echo "<h4>✅ Configuración Actualizada</h4>";
        echo "<p>Los cambios se han guardado correctamente</p>";
        echo "</div>";
        
        // Recargar configuración
        $stmt = $pdo->query("SELECT * FROM config_impresion ORDER BY clave");
        $configuracion = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
        echo "<h4>❌ Error</h4>";
        echo "<p>Error: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
}

// Formulario de configuración
echo "<h2>⚙️ Configurar Sistema Híbrido:</h2>";
echo "<form method='POST' style='background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 15px 0;'>";

$config_values = [];
foreach ($configuracion as $config) {
    $config_values[$config['clave']] = $config['valor'];
}

echo "<div style='display: grid; grid-template-columns: 1fr 1fr; gap: 20px;'>";

echo "<div>";
echo "<h4>🌐 Configuración de Red:</h4>";
echo "<label><strong>IP del Proxy (tu portátil):</strong></label><br>";
echo "<input type='text' name='config_ip_proxy' value='{$config_values['ip_proxy']}' style='width: 200px; padding: 5px; margin: 5px 0; border: 1px solid #ccc; border-radius: 3px;'><br>";

echo "<label><strong>Puerto del Proxy:</strong></label><br>";
echo "<input type='number' name='config_puerto_proxy' value='{$config_values['puerto_proxy']}' style='width: 100px; padding: 5px; margin: 5px 0; border: 1px solid #ccc; border-radius: 3px;'><br>";

echo "<label><strong>Red Local (prefijo):</strong></label><br>";
echo "<input type='text' name='config_red_local' value='{$config_values['red_local']}' style='width: 150px; padding: 5px; margin: 5px 0; border: 1px solid #ccc; border-radius: 3px;'><br>";
echo "</div>";

echo "<div>";
echo "<h4>⚡ Configuración de Rendimiento:</h4>";
echo "<label><strong>Modo de Impresión:</strong></label><br>";
echo "<select name='config_modo_impresion' style='width: 150px; padding: 5px; margin: 5px 0; border: 1px solid #ccc; border-radius: 3px;'>";
$modos = ['directo' => 'Directo (solo red local)', 'proxy' => 'Solo Proxy', 'hibrido' => 'Híbrido (inteligente)'];
foreach ($modos as $valor => $texto) {
    $selected = ($config_values['modo_impresion'] === $valor) ? 'selected' : '';
    echo "<option value='$valor' $selected>$texto</option>";
}
echo "</select><br>";

echo "<label><strong>Timeout (segundos):</strong></label><br>";
echo "<input type='number' name='config_timeout_conexion' value='{$config_values['timeout_conexion']}' min='1' max='30' style='width: 100px; padding: 5px; margin: 5px 0; border: 1px solid #ccc; border-radius: 3px;'><br>";

echo "<label><strong>Reintentos máximos:</strong></label><br>";
echo "<input type='number' name='config_reintentos_max' value='{$config_values['reintentos_max']}' min='1' max='10' style='width: 100px; padding: 5px; margin: 5px 0; border: 1px solid #ccc; border-radius: 3px;'><br>";

echo "<label><strong>Impresión directa habilitada:</strong></label><br>";
echo "<select name='config_impresion_directa_habilitada' style='width: 100px; padding: 5px; margin: 5px 0; border: 1px solid #ccc; border-radius: 3px;'>";
$habilitada = $config_values['impresion_directa_habilitada'] === '1' ? 'selected' : '';
$deshabilitada = $config_values['impresion_directa_habilitada'] === '0' ? 'selected' : '';
echo "<option value='1' $habilitada>Sí</option>";
echo "<option value='0' $deshabilitada>No</option>";
echo "</select><br>";
echo "</div>";

echo "</div>";

echo "<button type='submit' name='actualizar_config' style='background-color: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold; margin-top: 15px;'>💾 Guardar Configuración</button>";
echo "</form>";

// Explicación del sistema híbrido
echo "<h2>🧠 Cómo Funciona el Sistema Híbrido:</h2>";
echo "<div style='border: 1px solid #17a2b8; padding: 20px; margin: 15px 0; border-radius: 8px;'>";
echo "<h4>🔄 Lógica de Decisión Automática:</h4>";

echo "<ol>";
echo "<li><strong>🔍 Detectar origen de la solicitud:</strong></li>";
echo "<ul>";
echo "<li>Si viene de red local (192.168.68.x) → <strong>Impresión directa</strong></li>";
echo "<li>Si viene de internet → <strong>Usar proxy</strong></li>";
echo "</ul>";

echo "<li><strong>⚡ Intentar conexión:</strong></li>";
echo "<ul>";
echo "<li><strong>Modo directo:</strong> Conectar directamente a 192.168.68.120:9100</li>";
echo "<li><strong>Modo proxy:</strong> Conectar a ***********:3000/cocina</li>";
echo "<li><strong>Modo híbrido:</strong> Probar directo primero, luego proxy</li>";
echo "</ul>";

echo "<li><strong>🔄 Fallback automático:</strong></li>";
echo "<ul>";
echo "<li>Si falla la conexión directa → Intentar proxy</li>";
echo "<li>Si falla el proxy → Intentar conexión directa</li>";
echo "<li>Si ambos fallan → Reportar error</li>";
echo "</ul>";
echo "</ol>";

echo "<h4>💡 Ventajas del Sistema Híbrido:</h4>";
echo "<ul>";
echo "<li>✅ <strong>Funciona sin proxy</strong> cuando estás en red local</li>";
echo "<li>✅ <strong>Funciona con proxy</strong> cuando estás en internet</li>";
echo "<li>✅ <strong>Se adapta automáticamente</strong> a cambios de IP</li>";
echo "<li>✅ <strong>No requiere configuración manual</strong> por dispositivo</li>";
echo "<li>✅ <strong>Máxima compatibilidad</strong> con celulares y PCs</li>";
echo "</ul>";
echo "</div>";

// Botones de navegación
echo "<p style='margin-top: 30px;'>";
echo "<a href='solucion_cpanel.php' style='background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>← Solución cPanel</a>";
echo "<a href='test_impresion_hibrida.php' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🧪 Test Sistema Híbrido</a>";
echo "</p>";
?>
