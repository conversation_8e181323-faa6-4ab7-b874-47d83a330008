<?php

session_start();

if(!$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "../../models/conexion.php";

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Debug - Pedidos Mesa 7</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
    <style>
        .debug-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .sql-query {
            background-color: #f8f8f8;
            padding: 10px;
            border-left: 4px solid #007bff;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
        }
        .result-table {
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>

<div class="container">
    <h2>🔍 Debug - Pedidos Mesa 7</h2>
    <hr>
    
    <div class="debug-section">
        <h3>1. Todos los Pedidos de Mesa 7</h3>
        <?php
        $stmt = Conexion::conectar()->prepare("
            SELECT p.id, p.numero_pedido, p.estado, p.fecha_pedido, p.fecha_envio, p.fecha_entrega, p.mesa_id
            FROM pedidos p 
            WHERE p.mesa_id = 7 
            ORDER BY p.fecha_pedido DESC
        ");
        $stmt->execute();
        $pedidos_mesa7 = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div class='sql-query'>SELECT p.id, p.numero_pedido, p.estado, p.fecha_pedido, p.fecha_envio, p.fecha_entrega, p.mesa_id FROM pedidos p WHERE p.mesa_id = 7 ORDER BY p.fecha_pedido DESC</div>";
        
        if (count($pedidos_mesa7) > 0) {
            echo "<div class='result-table'>";
            echo "<table class='table table-striped'>";
            echo "<thead><tr><th>ID</th><th>Número</th><th>Estado</th><th>Fecha Pedido</th><th>Fecha Envío</th><th>Fecha Entrega</th></tr></thead>";
            echo "<tbody>";
            foreach ($pedidos_mesa7 as $pedido) {
                $clase = '';
                if ($pedido['estado'] == 'enviado') $clase = 'warning';
                if ($pedido['estado'] == 'entregado') $clase = 'success';
                if ($pedido['estado'] == 'borrador') $clase = 'info';
                
                echo "<tr class='{$clase}'>";
                echo "<td>{$pedido['id']}</td>";
                echo "<td>{$pedido['numero_pedido']}</td>";
                echo "<td><strong>{$pedido['estado']}</strong></td>";
                echo "<td>{$pedido['fecha_pedido']}</td>";
                echo "<td>{$pedido['fecha_envio']}</td>";
                echo "<td>{$pedido['fecha_entrega']}</td>";
                echo "</tr>";
            }
            echo "</tbody></table>";
            echo "</div>";
        } else {
            echo "<div class='alert alert-warning'>No se encontraron pedidos para la mesa 7</div>";
        }
        ?>
    </div>
    
    <div class="debug-section">
        <h3>2. Productos en Pedidos Enviados de Mesa 7</h3>
        <?php
        $stmt = Conexion::conectar()->prepare("
            SELECT p.id as pedido_id, p.numero_pedido, p.estado, pr.id as producto_id, pr.nombre, pr.categoria, pvm.cantidad
            FROM pedidos p
            INNER JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
            INNER JOIN productos pr ON pvm.productos_id = pr.id
            WHERE p.mesa_id = 7 AND p.estado = 'enviado'
            ORDER BY p.id, pr.nombre
        ");
        $stmt->execute();
        $productos_enviados = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div class='sql-query'>SELECT p.id as pedido_id, p.numero_pedido, p.estado, pr.id as producto_id, pr.nombre, pr.categoria, pvm.cantidad FROM pedidos p INNER JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id INNER JOIN productos pr ON pvm.productos_id = pr.id WHERE p.mesa_id = 7 AND p.estado = 'enviado' ORDER BY p.id, pr.nombre</div>";
        
        if (count($productos_enviados) > 0) {
            echo "<div class='result-table'>";
            echo "<table class='table table-striped'>";
            echo "<thead><tr><th>Pedido ID</th><th>Número</th><th>Producto</th><th>Categoría</th><th>Cantidad</th><th>Clasificación</th></tr></thead>";
            echo "<tbody>";
            foreach ($productos_enviados as $producto) {
                $clasificacion = 'COCINA';
                if (in_array($producto['categoria'], ['bebidas', 'cervezas', 'licores', 'vinos', 'cocteles', 'refrescos', 'jugos', 'agua', 'bar'])) {
                    $clasificacion = 'BAR';
                } elseif (in_array($producto['categoria'], ['carnes', 'parrilla', 'asados', 'pescados', 'mariscos'])) {
                    $clasificacion = 'ASADOS';
                }
                
                $clase = '';
                if ($clasificacion == 'BAR') $clase = 'info';
                if ($clasificacion == 'ASADOS') $clase = 'danger';
                if ($clasificacion == 'COCINA') $clase = 'warning';
                
                echo "<tr class='{$clase}'>";
                echo "<td>{$producto['pedido_id']}</td>";
                echo "<td>{$producto['numero_pedido']}</td>";
                echo "<td>{$producto['nombre']}</td>";
                echo "<td>{$producto['categoria']}</td>";
                echo "<td>{$producto['cantidad']}</td>";
                echo "<td><strong>{$clasificacion}</strong></td>";
                echo "</tr>";
            }
            echo "</tbody></table>";
            echo "</div>";
        } else {
            echo "<div class='alert alert-warning'>No se encontraron productos en pedidos enviados para la mesa 7</div>";
        }
        ?>
    </div>
    
    <div class="debug-section">
        <h3>3. Test de Consulta para BAR</h3>
        <?php
        $stmt = Conexion::conectar()->prepare("
            SELECT DISTINCT
                p.id as pedido_id,
                p.numero_pedido,
                p.fecha_envio,
                p.mesa_id,
                m.numero as mesa_numero,
                COUNT(pvm.id) as total_productos,
                GROUP_CONCAT(
                    CONCAT(pr.nombre, ' x', pvm.cantidad)
                    ORDER BY pr.nombre
                    SEPARATOR ', '
                ) as productos_detalle,
                SUM(pvm.cantidad * pr.precio) as total_precio
            FROM pedidos p
            INNER JOIN mesas m ON p.mesa_id = m.id
            INNER JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
            INNER JOIN productos pr ON pvm.productos_id = pr.id
            WHERE p.estado = 'enviado'
            AND pr.categoria IN ('bebidas', 'cervezas', 'licores', 'vinos', 'cocteles', 'refrescos', 'jugos', 'agua', 'bar')
            GROUP BY p.id, p.numero_pedido, p.fecha_envio, p.mesa_id, m.numero
            ORDER BY p.fecha_envio ASC
        ");
        $stmt->execute();
        $pedidos_bar = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div class='sql-query'>Consulta específica para BAR con filtro directo por categorías</div>";
        
        if (count($pedidos_bar) > 0) {
            echo "<div class='alert alert-success'>✅ Encontrados " . count($pedidos_bar) . " pedidos para BAR</div>";
            echo "<div class='result-table'>";
            echo "<table class='table table-striped'>";
            echo "<thead><tr><th>Pedido ID</th><th>Número</th><th>Mesa</th><th>Productos</th><th>Total</th></tr></thead>";
            echo "<tbody>";
            foreach ($pedidos_bar as $pedido) {
                echo "<tr>";
                echo "<td>{$pedido['pedido_id']}</td>";
                echo "<td>{$pedido['numero_pedido']}</td>";
                echo "<td>{$pedido['mesa_numero']}</td>";
                echo "<td>{$pedido['productos_detalle']}</td>";
                echo "<td>$" . number_format($pedido['total_precio'], 0) . "</td>";
                echo "</tr>";
            }
            echo "</tbody></table>";
            echo "</div>";
        } else {
            echo "<div class='alert alert-danger'>❌ No se encontraron pedidos para BAR</div>";
        }
        ?>
    </div>
    
    <div class="debug-section">
        <h3>4. Test de Consulta para COCINA</h3>
        <?php
        $stmt = Conexion::conectar()->prepare("
            SELECT DISTINCT
                p.id as pedido_id,
                p.numero_pedido,
                p.fecha_envio,
                p.mesa_id,
                m.numero as mesa_numero,
                COUNT(pvm.id) as total_productos,
                GROUP_CONCAT(
                    CONCAT(pr.nombre, ' x', pvm.cantidad)
                    ORDER BY pr.nombre
                    SEPARATOR ', '
                ) as productos_detalle,
                SUM(pvm.cantidad * pr.precio) as total_precio
            FROM pedidos p
            INNER JOIN mesas m ON p.mesa_id = m.id
            INNER JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
            INNER JOIN productos pr ON pvm.productos_id = pr.id
            WHERE p.estado = 'enviado'
            AND (pr.categoria NOT IN ('bebidas', 'cervezas', 'licores', 'vinos', 'cocteles', 'refrescos', 'jugos', 'agua', 'bar', 'carnes', 'parrilla', 'asados', 'pescados', 'mariscos')
                 OR pr.categoria IS NULL
                 OR pr.categoria = ''
                 OR pr.categoria = 'cocina'
                 OR pr.categoria = 'comida'
                 OR pr.categoria = 'platos'
                 OR pr.categoria = 'entradas'
                 OR pr.categoria = 'principales'
                 OR pr.categoria = 'postres')
            GROUP BY p.id, p.numero_pedido, p.fecha_envio, p.mesa_id, m.numero
            ORDER BY p.fecha_envio ASC
        ");
        $stmt->execute();
        $pedidos_cocina = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div class='sql-query'>Consulta específica para COCINA con filtro por exclusión</div>";
        
        if (count($pedidos_cocina) > 0) {
            echo "<div class='alert alert-success'>✅ Encontrados " . count($pedidos_cocina) . " pedidos para COCINA</div>";
            echo "<div class='result-table'>";
            echo "<table class='table table-striped'>";
            echo "<thead><tr><th>Pedido ID</th><th>Número</th><th>Mesa</th><th>Productos</th><th>Total</th></tr></thead>";
            echo "<tbody>";
            foreach ($pedidos_cocina as $pedido) {
                echo "<tr>";
                echo "<td>{$pedido['pedido_id']}</td>";
                echo "<td>{$pedido['numero_pedido']}</td>";
                echo "<td>{$pedido['mesa_numero']}</td>";
                echo "<td>{$pedido['productos_detalle']}</td>";
                echo "<td>$" . number_format($pedido['total_precio'], 0) . "</td>";
                echo "</tr>";
            }
            echo "</tbody></table>";
            echo "</div>";
        } else {
            echo "<div class='alert alert-danger'>❌ No se encontraron pedidos para COCINA</div>";
        }
        ?>
    </div>
    
    <div class="debug-section">
        <h3>5. Categorías de Productos Únicas</h3>
        <?php
        $stmt = Conexion::conectar()->prepare("
            SELECT DISTINCT categoria, COUNT(*) as total
            FROM productos 
            WHERE categoria IS NOT NULL AND categoria != ''
            GROUP BY categoria 
            ORDER BY categoria
        ");
        $stmt->execute();
        $categorias = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div class='result-table'>";
        echo "<table class='table table-striped'>";
        echo "<thead><tr><th>Categoría</th><th>Total Productos</th><th>Clasificación</th></tr></thead>";
        echo "<tbody>";
        foreach ($categorias as $cat) {
            $clasificacion = 'COCINA';
            if (in_array($cat['categoria'], ['bebidas', 'cervezas', 'licores', 'vinos', 'cocteles', 'refrescos', 'jugos', 'agua', 'bar'])) {
                $clasificacion = 'BAR';
            } elseif (in_array($cat['categoria'], ['carnes', 'parrilla', 'asados', 'pescados', 'mariscos'])) {
                $clasificacion = 'ASADOS';
            }
            
            $clase = '';
            if ($clasificacion == 'BAR') $clase = 'info';
            if ($clasificacion == 'ASADOS') $clase = 'danger';
            if ($clasificacion == 'COCINA') $clase = 'warning';
            
            echo "<tr class='{$clase}'>";
            echo "<td>{$cat['categoria']}</td>";
            echo "<td>{$cat['total']}</td>";
            echo "<td><strong>{$clasificacion}</strong></td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
        echo "</div>";
        ?>
    </div>
    
    <hr>
    <p><a href="pedidosCocinaPendientes" class="btn btn-warning">🍳 Ir a Cocina</a></p>
    <p><a href="pedidosBarPendientes" class="btn btn-info">🍺 Ir a Bar</a></p>
    <p><a href="pedidosAsadosPendientes" class="btn btn-danger">🥩 Ir a Asados</a></p>
</div>

</body>
</html>
