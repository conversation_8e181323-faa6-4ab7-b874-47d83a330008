-- Script de emergencia para restaurar funcionalidad básica
-- Solo ejecutar si hay problemas graves

USE ewogjwfm_macarena;

-- Agregar solo las columnas esenciales si no existen
ALTER TABLE pedidos 
ADD COLUMN IF NOT EXISTS estado ENUM('borrador', 'enviado', 'entregado', 'facturado') DEFAULT 'borrador',
ADD COLUMN IF NOT EXISTS mesa_id BIGINT(20) NULL,
ADD COLUMN IF NOT EXISTS numero_pedido VARCHAR(20) NULL;

-- Crear tabla de impresoras básica
CREATE TABLE IF NOT EXISTS impresoras (
  id INT AUTO_INCREMENT PRIMARY KEY,
  categoria ENUM('bar','cocina','asados') NOT NULL,
  ip VARCHAR(20) NOT NULL,
  puerto INT NOT NULL
);

-- Insertar configuración básica de impresoras
INSERT IGNORE INTO impresoras (categoria, ip, puerto) VALUES
('bar', '**************', 9100),
('cocina', '**************', 9100),
('asados', '**************', 9100);

-- Actualizar datos básicos
UPDATE pedidos SET estado = 'facturado' WHERE facturado = 's' AND estado IS NULL;
UPDATE pedidos SET estado = 'borrador' WHERE facturado = 'n' AND estado IS NULL;
UPDATE pedidos SET numero_pedido = CONCAT('P', LPAD(id, 6, '0')) WHERE numero_pedido IS NULL;

SELECT 'Configuración básica aplicada' as resultado;
