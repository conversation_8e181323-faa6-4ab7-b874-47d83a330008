<?php
// Test para diagnosticar el error 500 en ajaxFactura.php
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', '../../logs/error_500.log');

echo "<h1>🔍 Diagnóstico Error 500 - Mesa 10</h1>";

// Simular exactamente los datos que se envían desde registroPmesa.php
$datosTest = [
    'efectivo' => 50000,
    'bancolombia' => 0,
    'nequi' => 0,
    'daviplata' => 0,
    'tarjeta' => 0,
    'pago' => 1,
    'pcedula' => 'MARCO',
    'totalDescuento' => 0,
    'total' => 50000,
    'propina' => 0,
    'mesa' => 10,
    'tipoTarjeta' => 'credito',
    'optimizada' => true
];

echo "<h3>📋 Datos de Test:</h3>";
echo "<pre>" . print_r($datosTest, true) . "</pre>";

echo "<h3>🧪 Test 1: Verificar Archivos Requeridos</h3>";

$archivos = [
    '../../models/crud.php',
    '../../models/crudFacturaAja.php',
    '../../controllers/controller.php',
    '../../controllers/controllerFacturaAja.php'
];

foreach ($archivos as $archivo) {
    if (file_exists($archivo)) {
        echo "✅ $archivo - Existe<br>";
    } else {
        echo "❌ $archivo - NO EXISTE<br>";
    }
}

echo "<h3>🧪 Test 2: Simular Inclusión de Archivos</h3>";

try {
    require_once "../../models/crud.php";
    echo "✅ crud.php - Incluido correctamente<br>";
} catch (Exception $e) {
    echo "❌ crud.php - Error: " . $e->getMessage() . "<br>";
}

try {
    require_once "../../models/crudFacturaAja.php";
    echo "✅ crudFacturaAja.php - Incluido correctamente<br>";
} catch (Exception $e) {
    echo "❌ crudFacturaAja.php - Error: " . $e->getMessage() . "<br>";
}

try {
    require_once "../../controllers/controller.php";
    echo "✅ controller.php - Incluido correctamente<br>";
} catch (Exception $e) {
    echo "❌ controller.php - Error: " . $e->getMessage() . "<br>";
}

try {
    require_once "../../controllers/controllerFacturaAja.php";
    echo "✅ controllerFacturaAja.php - Incluido correctamente<br>";
} catch (Exception $e) {
    echo "❌ controllerFacturaAja.php - Error: " . $e->getMessage() . "<br>";
}

echo "<h3>🧪 Test 3: Verificar Conexión a Base de Datos</h3>";

try {
    require_once "../../models/conexion.php";
    $db = Conexion::conectar();
    echo "✅ Conexión a base de datos - OK<br>";
    
    // Test simple de consulta
    $stmt = $db->prepare("SELECT COUNT(*) as total FROM pedidos WHERE mesa_id = ?");
    $stmt->bindParam(1, $datosTest['mesa'], PDO::PARAM_INT);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "✅ Consulta test - Mesa 10 tiene {$result['total']} pedidos<br>";
    
} catch (Exception $e) {
    echo "❌ Error de base de datos: " . $e->getMessage() . "<br>";
}

echo "<h3>🧪 Test 4: Simular Procesamiento de ajaxFactura.php</h3>";

try {
    // Simular $_POST
    $_POST = $datosTest;
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Simulando ajaxFactura.php paso a paso:</h4>";
    
    // Paso 1: Variables
    $nequi = isset($_POST['nequi']) ? $_POST['nequi'] : 0;
    $daviplata = isset($_POST['daviplata']) ? $_POST['daviplata'] : 0;
    $bancolombia = isset($_POST['bancolombia']) ? $_POST['bancolombia'] : 0;
    $tipoTarjeta = isset($_POST['tipoTarjeta']) ? $_POST['tipoTarjeta'] : 'credito';
    $optimizada = isset($_POST['optimizada']) ? $_POST['optimizada'] : false;

    $queryString = $_POST['efectivo'];
    $tarjeta = $_POST['tarjeta'];
    $queryString1 = $_POST['pago'];
    $queryString2 = $_POST['pcedula'];
    $queryString3 = $_POST['total'];
    $totalDescuento = isset($_POST['totalDescuento']) ? $_POST['totalDescuento'] : 0;
    $propina = isset($_POST['propina']) ? $_POST['propina'] : 0;
    $mesa = $_POST['mesa'];
    
    echo "✅ Variables asignadas correctamente<br>";
    
    // Paso 2: Validación
    if($_POST['pago'] == 1) {
        $totalCuenta = $_POST['total'];
        $totalPagado = $queryString + $tarjeta + $nequi + $daviplata + $bancolombia + $totalDescuento;
        
        echo "✅ Validación: Total cuenta: $totalCuenta, Total pagado: $totalPagado<br>";
        
        if($totalPagado >= $totalCuenta) {
            echo "✅ Validación de pago exitosa<br>";
            
            // Paso 3: Intentar crear controlador
            try {
                $ajax = new controllerFacturaAja();
                echo "✅ Controlador creado correctamente<br>";
                
                echo "<h5>⚠️ Punto crítico: Llamada a facturaajaxController</h5>";
                echo "<p>Aquí es donde probablemente ocurre el error 500.</p>";
                echo "<p>Parámetros que se enviarían:</p>";
                echo "<ul>";
                echo "<li>efectivo: $queryString</li>";
                echo "<li>nequi: $nequi</li>";
                echo "<li>daviplata: $daviplata</li>";
                echo "<li>bancolombia: $bancolombia</li>";
                echo "<li>tarjeta: $tarjeta</li>";
                echo "<li>pago: $queryString1</li>";
                echo "<li>cedula: $queryString2</li>";
                echo "<li>propina: $propina</li>";
                echo "<li>mesa: $mesa</li>";
                echo "</ul>";
                
            } catch (Exception $e) {
                echo "❌ Error creando controlador: " . $e->getMessage() . "<br>";
            }
            
        } else {
            echo "❌ Validación de pago falló<br>";
        }
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "❌ Error en simulación: " . $e->getMessage() . "<br>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h3>🧪 Test 5: Test Directo del Controlador</h3>";

try {
    session_start();
    if (!isset($_SESSION["usuario"])) {
        $_SESSION["usuario"] = 15; // Mesero de la mesa 10
        $_SESSION["tipo_usuario"] = 1;
        $_SESSION["perfil"] = "administrador";
    }
    
    echo "<button onclick='testControladorDirecto()' style='background: #dc3545; color: white; padding: 10px; border: none; border-radius: 5px; cursor: pointer;'>";
    echo "⚠️ Test Controlador Directo (CUIDADO)";
    echo "</button>";
    echo "<p><em>Este test intentará ejecutar el controlador directamente. Úsalo solo para diagnóstico.</em></p>";
    
} catch (Exception $e) {
    echo "❌ Error en test directo: " . $e->getMessage() . "<br>";
}

echo "<div id='resultado' style='margin-top: 20px; padding: 15px; background: #fff; border: 1px solid #ddd; border-radius: 5px;'></div>";

echo "<br><a href='index.php?action=registroPmesa&ida=10' style='background: #007bff; color: white; padding: 10px; text-decoration: none;'>🔙 Volver a Mesa 10</a>";
?>

<script>
function testControladorDirecto() {
    if (confirm('⚠️ ADVERTENCIA: Este test intentará ejecutar la facturación real.\n\n¿Estás seguro de continuar?')) {
        
        document.getElementById('resultado').innerHTML = '<div style="color: blue;">⏳ Ejecutando test directo del controlador...</div>';
        
        fetch('ajaxFactura.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                efectivo: 50000,
                bancolombia: 0,
                nequi: 0,
                daviplata: 0,
                tarjeta: 0,
                pago: 1,
                pcedula: 'MARCO',
                totalDescuento: 0,
                total: 50000,
                propina: 0,
                mesa: 10,
                tipoTarjeta: 'credito',
                optimizada: true
            })
        })
        .then(response => {
            console.log('Status:', response.status);
            console.log('Headers:', response.headers);
            return response.text();
        })
        .then(text => {
            console.log('Respuesta completa:', text);
            
            let html = '<h4>📄 Respuesta del Test Directo:</h4>';
            html += '<p><strong>Status:</strong> ' + (text ? 'Respuesta recibida' : 'Sin respuesta') + '</p>';
            html += '<pre style="background: #f8f9fa; padding: 10px; border-radius: 5px; white-space: pre-wrap; max-height: 300px; overflow-y: auto;">' + text + '</pre>';
            
            if (text.includes('success')) {
                html += '<div style="color: green; font-weight: bold;">✅ Test exitoso</div>';
            } else if (text.includes('error') || text.includes('Error')) {
                html += '<div style="color: red; font-weight: bold;">❌ Error detectado</div>';
            } else {
                html += '<div style="color: orange; font-weight: bold;">⚠️ Respuesta inesperada</div>';
            }
            
            document.getElementById('resultado').innerHTML = html;
        })
        .catch(error => {
            console.error('Error completo:', error);
            
            let html = '<h4>❌ Error en Test Directo:</h4>';
            html += '<p><strong>Error:</strong> ' + error.message + '</p>';
            html += '<p>Este error confirma el problema en ajaxFactura.php</p>';
            
            document.getElementById('resultado').innerHTML = html;
        });
    }
}
</script>
