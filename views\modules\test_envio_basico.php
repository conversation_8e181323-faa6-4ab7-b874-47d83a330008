<?php
// Test básico sin impresión
require_once "../../models/conexion.php";
require_once "../../models/crudEstadoPedidos.php";

// Iniciar sesión
session_start();
if (!isset($_SESSION["usuario"])) {
    $_SESSION["usuario"] = 1;
    $_SESSION["tipo_usuario"] = 1;
    $_SESSION["perfil"] = "administrador";
}

echo "<h1>🔧 Test Básico de Envío (Sin Impresión)</h1>";

// Obtener un pedido borrador
try {
    $stmt = Conexion::conectar()->prepare("
        SELECT p.id, p.numero_pedido, p.mesa_id, p.estado, m.numero as mesa_numero
        FROM pedidos p 
        LEFT JOIN mesas m ON p.mesa_id = m.id 
        WHERE p.estado = 'borrador' 
        ORDER BY p.id DESC 
        LIMIT 1
    ");
    $stmt->execute();
    $pedido = $stmt->fetch();
    
    if ($pedido) {
        $pedidoId = $pedido['id'];
        echo "<h3>📋 Probando con pedido:</h3>";
        echo "<ul>";
        echo "<li><strong>ID:</strong> " . $pedido['id'] . "</li>";
        echo "<li><strong>Número:</strong> " . $pedido['numero_pedido'] . "</li>";
        echo "<li><strong>Mesa:</strong> " . $pedido['mesa_numero'] . "</li>";
        echo "<li><strong>Estado actual:</strong> " . $pedido['estado'] . "</li>";
        echo "</ul>";
        
        // Verificar que tenga productos
        echo "<h4>1. Verificando productos del pedido...</h4>";
        $productos = DatosEstadoPedidos::obtenerProductosPedidoModel($pedidoId);
        
        if (empty($productos)) {
            echo "❌ El pedido no tiene productos. Agregando producto de prueba...<br>";
            
            // Agregar un producto de prueba
            $stmtProducto = Conexion::conectar()->prepare("
                INSERT INTO producto_vendido_mesa (productos_id, mesas_id, pedidos_id, cantidad, nota)
                VALUES (1, ?, ?, 1, 'Producto de prueba')
            ");
            $stmtProducto->execute([$pedido['mesa_id'], $pedidoId]);
            
            echo "✅ Producto de prueba agregado<br>";
        } else {
            echo "✅ El pedido tiene " . count($productos) . " producto(s)<br>";
        }
        
        // Probar el envío básico (solo cambio de estado)
        echo "<h4>2. Probando envío básico (solo cambio de estado)...</h4>";
        
        $usuarioId = $_SESSION["usuario"];
        $resultado = DatosEstadoPedidos::enviarPedidoModel($pedidoId, $usuarioId);
        
        if ($resultado == "success") {
            echo "✅ <strong>¡Pedido enviado exitosamente!</strong><br>";
            echo "Estado cambiado de 'borrador' a 'enviado'<br>";
            
            // Verificar el cambio
            $stmtVerificar = Conexion::conectar()->prepare("
                SELECT estado, fecha_envio, usuario_envio 
                FROM pedidos 
                WHERE id = ?
            ");
            $stmtVerificar->execute([$pedidoId]);
            $estadoActual = $stmtVerificar->fetch();
            
            echo "<h4>3. Estado actual del pedido:</h4>";
            echo "<ul>";
            echo "<li><strong>Estado:</strong> " . $estadoActual['estado'] . "</li>";
            echo "<li><strong>Fecha envío:</strong> " . $estadoActual['fecha_envio'] . "</li>";
            echo "<li><strong>Usuario envío:</strong> " . $estadoActual['usuario_envio'] . "</li>";
            echo "</ul>";
            
        } else {
            echo "❌ Error al enviar el pedido: $resultado<br>";
        }
        
    } else {
        echo "❌ No hay pedidos borrador disponibles.<br>";
        echo "<button onclick='crearPedidoPrueba()' style='background: #007bff; color: white; padding: 10px; border: none; cursor: pointer;'>➕ Crear Pedido de Prueba</button>";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage();
}

echo "<br><br>";
echo "<h4>🔄 Acciones:</h4>";
echo "<a href='test_ajax_directo.php' style='background: #6c757d; color: white; padding: 10px; text-decoration: none; margin: 5px;'>🔧 Test AJAX</a>";
echo "<a href='test_envio_simple.php' style='background: #28a745; color: white; padding: 10px; text-decoration: none; margin: 5px;'>🔙 Test Simple</a>";
echo "<a href='../../index.php?action=crear_datos_prueba' style='background: #007bff; color: white; padding: 10px; text-decoration: none; margin: 5px;'>➕ Crear Datos</a>";
?>

<script>
function crearPedidoPrueba() {
    if (confirm('¿Crear un nuevo pedido de prueba?')) {
        fetch('../../index.php?action=crear_datos_prueba', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'crear_pedido_borrador=true'
        })
        .then(response => {
            alert('Pedido de prueba creado');
            location.reload();
        })
        .catch(error => {
            alert('Error: ' + error.message);
        });
    }
}
</script>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}

ul {
    background: #f8f9fa;
    padding: 10px;
    border: 1px solid #ddd;
    margin: 10px 0;
}

a {
    display: inline-block;
    text-decoration: none;
    border-radius: 4px;
}

a:hover {
    opacity: 0.8;
}
</style>
