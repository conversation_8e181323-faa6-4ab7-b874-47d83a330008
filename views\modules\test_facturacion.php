<?php
// Test de facturación con nuevos métodos de pago
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
if (!isset($_SESSION["usuario"])) {
    $_SESSION["usuario"] = 1;
    $_SESSION["tipo_usuario"] = 1;
    $_SESSION["perfil"] = "administrador";
}

require_once "../../models/conexion.php";
require_once "../../controllers/controllerEstadoPedidos.php";
require_once "../../models/crudEstadoPedidos.php";

$mesaId = isset($_GET['mesa']) ? $_GET['mesa'] : 1;

echo "<h1>🧾 Test de Facturación - Mesa $mesaId</h1>";

try {
    $controller = new ControllerEstadoPedidos();
    
    echo "<h3>1. 📋 Pedidos Disponibles para Facturar</h3>";
    $pedidos = $controller->obtenerPedidosMesaController($mesaId);
    
    $pedidosFacturables = array_filter($pedidos, function($pedido) {
        return in_array($pedido['estado'], ['enviado', 'entregado']) && $pedido['total_productos'] > 0;
    });
    
    if (!empty($pedidosFacturables)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Número</th><th>Estado</th><th>Productos</th><th>Fecha</th><th>Acción</th></tr>";
        
        foreach ($pedidosFacturables as $p) {
            echo "<tr>";
            echo "<td>{$p['id']}</td>";
            echo "<td>{$p['numero_pedido']}</td>";
            echo "<td>{$p['estado']}</td>";
            echo "<td>{$p['total_productos']}</td>";
            echo "<td>{$p['fecha_pedido']}</td>";
            echo "<td>";
            echo "<button onclick='testFacturar({$p['id']}, \"{$p['numero_pedido']}\")' style='background: #28a745; color: white; padding: 5px;'>💰 Test Facturar</button>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "❌ No hay pedidos disponibles para facturar<br>";
        echo "Los pedidos deben estar en estado 'enviado' o 'entregado' y tener productos.<br>";
    }
    
    echo "<h3>2. 💳 Métodos de Pago Disponibles</h3>";
    echo "<ul>";
    echo "<li>💵 Efectivo</li>";
    echo "<li>💳 Tarjeta</li>";
    echo "<li>📱 Nequi</li>";
    echo "<li>🏦 Bancolombia</li>";
    echo "<li>💰 Daviplata</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}

echo "<br><a href='index.php?action=registroPmesa&ida=$mesaId' style='background: #007bff; color: white; padding: 10px; text-decoration: none;'>🔙 Volver a Mesa $mesaId</a>";
?>

<script>
function testFacturar(pedidoId, numeroPedido) {
    if (confirm('¿Facturar pedido ' + numeroPedido + '?')) {
        // Simular datos de facturación
        const datosFactura = {
            efectivo: 50000,
            nequi: 0,
            daviplata: 0,
            bancolombia: 0,
            tarjeta: 0,
            pago: 1, // 1 = contado
            cedula: '12345678',
            propina: 0,
            mesa: <?=$mesaId?>
        };
        
        console.log('💰 Facturando pedido:', pedidoId, 'con datos:', datosFactura);
        
        // Crear formulario para enviar datos
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'ajaxFactura.php';
        
        // Agregar campos
        Object.keys(datosFactura).forEach(key => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = key;
            input.value = datosFactura[key];
            form.appendChild(input);
        });
        
        document.body.appendChild(form);
        
        // Enviar formulario
        form.submit();
    }
}

// Función alternativa con AJAX
function testFacturarAjax(pedidoId, numeroPedido) {
    if (confirm('¿Facturar pedido ' + numeroPedido + ' via AJAX?')) {
        const datosFactura = {
            efectivo: 50000,
            nequi: 0,
            daviplata: 0,
            bancolombia: 0,
            tarjeta: 0,
            pago: 1,
            cedula: '12345678',
            propina: 0,
            mesa: <?=$mesaId?>
        };
        
        fetch('ajaxFactura.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams(datosFactura)
        })
        .then(response => response.text())
        .then(text => {
            console.log('Respuesta facturación:', text);
            alert('Facturación completada. Ver consola para detalles.');
            setTimeout(() => location.reload(), 2000);
        })
        .catch(error => {
            console.error('Error facturación:', error);
            alert('Error en facturación: ' + error);
        });
    }
}
</script>
