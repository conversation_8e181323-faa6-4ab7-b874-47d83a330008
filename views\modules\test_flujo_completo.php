<?php
// Test del flujo completo: agregar productos -> enviar -> facturar
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
if (!isset($_SESSION["usuario"])) {
    $_SESSION["usuario"] = 1;
    $_SESSION["tipo_usuario"] = 1;
    $_SESSION["perfil"] = "administrador";
}

require_once "../../models/conexion.php";
require_once "../../controllers/controllerEstadoPedidos.php";
require_once "../../models/crudEstadoPedidos.php";

$mesaId = isset($_GET['mesa']) ? $_GET['mesa'] : 1;

echo "<h1>🔄 Test Flujo Completo - Mesa $mesaId</h1>";

try {
    $controller = new ControllerEstadoPedidos();
    
    echo "<h3>📊 Estado Actual de la Mesa</h3>";
    $pedidos = $controller->obtenerPedidosMesaController($mesaId);
    
    if (!empty($pedidos)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Número</th><th>Estado</th><th>Productos</th><th>Fecha</th></tr>";
        
        foreach ($pedidos as $p) {
            $style = '';
            if ($p['estado'] == 'borrador') $style = 'background-color: #fff3cd;';
            if ($p['estado'] == 'enviado') $style = 'background-color: #d4edda;';
            if ($p['estado'] == 'entregado') $style = 'background-color: #cce5ff;';
            
            echo "<tr style='$style'>";
            echo "<td>{$p['id']}</td>";
            echo "<td>{$p['numero_pedido']}</td>";
            echo "<td>{$p['estado']}</td>";
            echo "<td>{$p['total_productos']}</td>";
            echo "<td>{$p['fecha_pedido']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<small>";
        echo "🟡 Amarillo = Borrador (agregando productos)<br>";
        echo "🟢 Verde = Enviado (en cocina)<br>";
        echo "🔵 Azul = Entregado (listo para facturar)<br>";
        echo "</small>";
    } else {
        echo "<p>No hay pedidos activos en la mesa.</p>";
    }
    
    echo "<h3>🎯 Flujo Esperado del Sistema</h3>";
    echo "<ol>";
    echo "<li><strong>Agregar productos</strong> → Van al pedido borrador (🟡)</li>";
    echo "<li><strong>Enviar pedido</strong> → Cambia a enviado (🟢), se crea nuevo borrador vacío</li>";
    echo "<li><strong>Marcar entregado</strong> → Cambia a entregado (🔵)</li>";
    echo "<li><strong>Facturar</strong> → Pedido desaparece de la mesa (facturado)</li>";
    echo "</ol>";
    
    echo "<h3>🔧 Acciones Disponibles</h3>";
    
    // Buscar pedidos por estado
    $borradores = array_filter($pedidos, function($p) { return $p['estado'] == 'borrador' && $p['total_productos'] > 0; });
    $enviados = array_filter($pedidos, function($p) { return $p['estado'] == 'enviado'; });
    $entregados = array_filter($pedidos, function($p) { return $p['estado'] == 'entregado'; });
    
    if (!empty($borradores)) {
        echo "<h4>📤 Pedidos Listos para Enviar</h4>";
        foreach ($borradores as $p) {
            echo "<button onclick='enviarPedido({$p['id']})' style='background: #28a745; color: white; padding: 8px; margin: 5px;'>";
            echo "Enviar {$p['numero_pedido']} ({$p['total_productos']} productos)";
            echo "</button><br>";
        }
    }
    
    if (!empty($enviados)) {
        echo "<h4>✅ Pedidos para Marcar como Entregados</h4>";
        foreach ($enviados as $p) {
            echo "<button onclick='marcarEntregado({$p['id']})' style='background: #17a2b8; color: white; padding: 8px; margin: 5px;'>";
            echo "Entregar {$p['numero_pedido']}";
            echo "</button><br>";
        }
    }
    
    if (!empty($entregados)) {
        echo "<h4>💰 Pedidos Listos para Facturar</h4>";
        foreach ($entregados as $p) {
            echo "<button onclick='testFacturar({$p['id']}, \"{$p['numero_pedido']}\")' style='background: #dc3545; color: white; padding: 8px; margin: 5px;'>";
            echo "Facturar {$p['numero_pedido']}";
            echo "</button><br>";
        }
    }
    
    if (empty($borradores) && empty($enviados) && empty($entregados)) {
        echo "<p>✅ Mesa lista para nuevos pedidos. Vaya a la mesa para agregar productos.</p>";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}

echo "<br><a href='index.php?action=registroPmesa&ida=$mesaId' style='background: #007bff; color: white; padding: 10px; text-decoration: none;'>🔙 Ir a Mesa $mesaId</a>";
echo "<br><a href='test_flujo_completo.php?mesa=$mesaId' style='background: #6c757d; color: white; padding: 10px; text-decoration: none; margin: 5px;'>🔄 Actualizar</a>";
?>

<script>
function enviarPedido(pedidoId) {
    if (confirm('¿Enviar pedido ' + pedidoId + ' a cocina?')) {
        fetch('ajaxEstadoPedidos.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: 'enviar_pedido=true&pedido_id=' + pedidoId
        })
        .then(response => response.text())
        .then(text => {
            try {
                const data = JSON.parse(text);
                alert(data.status === 'success' ? '✅ ' + data.message : '❌ ' + data.message);
            } catch (e) {
                alert('Respuesta: ' + text);
            }
            setTimeout(() => location.reload(), 1000);
        });
    }
}

function marcarEntregado(pedidoId) {
    if (confirm('¿Marcar pedido ' + pedidoId + ' como entregado?')) {
        fetch('ajaxEstadoPedidos.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: 'entregar_pedido=true&pedido_id=' + pedidoId
        })
        .then(response => response.text())
        .then(text => {
            try {
                const data = JSON.parse(text);
                alert(data.status === 'success' ? '✅ ' + data.message : '❌ ' + data.message);
            } catch (e) {
                alert('Respuesta: ' + text);
            }
            setTimeout(() => location.reload(), 1000);
        });
    }
}

function testFacturar(pedidoId, numeroPedido) {
    if (confirm('¿Facturar pedido ' + numeroPedido + '?')) {
        // Simular facturación con datos básicos
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'ajaxFactura.php';
        
        const datos = {
            efectivo: 50000,
            nequi: 0,
            daviplata: 0,
            bancolombia: 0,
            tarjeta: 0,
            pago: 1,
            pcedula: '12345678',
            totalDescuento: 0,
            total: 30000,
            propina: 0,
            mesa: <?=$mesaId?>
        };
        
        Object.keys(datos).forEach(key => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = key;
            input.value = datos[key];
            form.appendChild(input);
        });
        
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
