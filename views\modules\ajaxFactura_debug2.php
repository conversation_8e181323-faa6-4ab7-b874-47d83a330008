<?php
// Versión debug 2 de ajaxFactura.php con controlador debug
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// OPTIMIZACIÓN PARA MESAS PESADAS
ini_set('max_execution_time', 300); // 5 minutos
ini_set('memory_limit', '512M');

echo "<!-- DEBUG2: Iniciando ajaxFactura_debug2.php -->\n";

try {
    echo "<!-- DEBUG2: Incluyendo archivos -->\n";
    
    require_once "../../models/crud.php";
    echo "<!-- DEBUG2: crud.php incluido -->\n";
    
    require_once "../../models/crudFacturaAja.php";
    echo "<!-- DEBUG2: crudFacturaAja.php incluido -->\n";
    
    require_once "../../controllers/controller.php";
    echo "<!-- DEBUG2: controller.php incluido -->\n";
    
    require_once "../../controllers/controllerFacturaAja_debug.php";
    echo "<!-- DEBUG2: controllerFacturaAja_debug.php incluido -->\n";

    // Log para debug de mesas pesadas
    error_log("FACTURACIÓN DEBUG2 INICIADA - Mesa: " . ($_POST['mesa'] ?? 'N/A') . " - Timestamp: " . date('Y-m-d H:i:s'));
    echo "<!-- DEBUG2: Log inicial registrado -->\n";

    // COMPATIBILIDAD: Manejar tanto el formato original como el nuevo
    $nequi = isset($_POST['nequi']) ? $_POST['nequi'] : 0;
    $daviplata = isset($_POST['daviplata']) ? $_POST['daviplata'] : 0;
    $bancolombia = isset($_POST['bancolombia']) ? $_POST['bancolombia'] : 0;
    $tipoTarjeta = isset($_POST['tipoTarjeta']) ? $_POST['tipoTarjeta'] : 'credito';
    $optimizada = isset($_POST['optimizada']) ? $_POST['optimizada'] : false;

    $queryString = $_POST['efectivo'] ?? 0;
    $tarjeta = $_POST['tarjeta'] ?? 0;
    $queryString1 = $_POST['pago'] ?? 1;
    $queryString2 = $_POST['pcedula'] ?? '';
    $queryString3 = $_POST['total'] ?? 0;
    $totalDescuento = isset($_POST['totalDescuento']) ? $_POST['totalDescuento'] : 0;
    $propina = isset($_POST['propina']) ? $_POST['propina'] : 0;
    $mesa = $_POST['mesa'] ?? 0;

    echo "<!-- DEBUG2: Variables asignadas -->\n";
    echo "<!-- DEBUG2: Mesa: $mesa, Total: $queryString3, Efectivo: $queryString -->\n";

    if($queryString1 == 1) {
        echo "<!-- DEBUG2: Procesando pago en efectivo -->\n";
        
        $totalCuenta = $queryString3;
        $totalPagado = $queryString + $tarjeta + $nequi + $daviplata + $bancolombia + $totalDescuento;
        
        echo "<!-- DEBUG2: Total cuenta: $totalCuenta, Total pagado: $totalPagado -->\n";
        
        error_log("FACTURACIÓN DEBUG2 - Mesa: $mesa, Total: $totalCuenta, Pagado: $totalPagado");

        if($totalPagado >= $totalCuenta) {
            echo "<!-- DEBUG2: Validación de pago exitosa -->\n";
            
            $tiempoInicio = microtime(true);
            error_log("PROCESAMIENTO DEBUG2 INICIADO - Mesa: $mesa");

            try {
                echo "<!-- DEBUG2: Creando controlador debug -->\n";
                $ajax = new controllerFacturaAja_debug();
                echo "<!-- DEBUG2: Controlador debug creado exitosamente -->\n";
                
                echo "<!-- DEBUG2: Llamando facturaajaxController debug -->\n";
                $resultado = $ajax->facturaajaxController($queryString, $nequi, $daviplata, $bancolombia, $tarjeta, $queryString1, $queryString2, $propina, $mesa);
                echo "<!-- DEBUG2: facturaajaxController debug completado con resultado: $resultado -->\n";

                $tiempoFin = microtime(true);
                $tiempoTotal = round(($tiempoFin - $tiempoInicio) * 1000, 2);
                error_log("FACTURACIÓN DEBUG2 COMPLETADA - Mesa: $mesa, Tiempo: {$tiempoTotal}ms");

                // Respuesta exitosa explícita
                if ($optimizada) {
                    echo "success_debug2_optimizada_mesa_$mesa";
                } else {
                    echo "success_debug2_mesa_$mesa";
                }

            } catch (Exception $e) {
                echo "<!-- DEBUG2: Error en controlador: " . $e->getMessage() . " -->\n";
                echo "<!-- DEBUG2: Stack trace: " . $e->getTraceAsString() . " -->\n";
                error_log("ERROR DEBUG2 EN FACTURACIÓN - Mesa: $mesa, Error: " . $e->getMessage());
                error_log("ERROR DEBUG2 Stack trace: " . $e->getTraceAsString());
                echo "<script>alert('Error debug2 en facturación: " . addslashes($e->getMessage()) . "')</script>";
            } catch (Error $e) {
                echo "<!-- DEBUG2: Error fatal: " . $e->getMessage() . " -->\n";
                echo "<!-- DEBUG2: Stack trace: " . $e->getTraceAsString() . " -->\n";
                error_log("ERROR DEBUG2 FATAL - Mesa: $mesa, Error: " . $e->getMessage());
                error_log("ERROR DEBUG2 FATAL Stack trace: " . $e->getTraceAsString());
                echo "<script>alert('Error debug2 fatal: " . addslashes($e->getMessage()) . "')</script>";
            }
        } else {
            echo "<!-- DEBUG2: Pago insuficiente -->\n";
            $faltante = $totalCuenta - $totalPagado;
            error_log("PAGO DEBUG2 INSUFICIENTE - Mesa: $mesa, Falta: $faltante");
            echo "<script>alert('Pago insuficiente. Total: $" . number_format($totalCuenta) . ", Pagado: $" . number_format($totalPagado) . ", Falta: $" . number_format($faltante) . "')</script>";
        }
    } else {
        echo "<!-- DEBUG2: Procesando pago a crédito -->\n";
        echo "<script>alert('Pago a crédito en modo debug2')</script>";
        $ajax = new controllerFacturaAja_debug();
        $ajax->facturaajaxController($queryString, $nequi, $daviplata, $bancolombia, $tarjeta, $queryString1, $queryString2, $propina, $mesa);
    }

} catch (ParseError $e) {
    echo "<!-- DEBUG2: Error de sintaxis: " . $e->getMessage() . " -->\n";
    error_log("ERROR DEBUG2 SINTAXIS: " . $e->getMessage());
    http_response_code(500);
    echo "Error de sintaxis: " . $e->getMessage();
} catch (Error $e) {
    echo "<!-- DEBUG2: Error fatal: " . $e->getMessage() . " -->\n";
    error_log("ERROR DEBUG2 FATAL: " . $e->getMessage());
    http_response_code(500);
    echo "Error fatal: " . $e->getMessage();
} catch (Exception $e) {
    echo "<!-- DEBUG2: Excepción: " . $e->getMessage() . " -->\n";
    error_log("ERROR DEBUG2 EXCEPCIÓN: " . $e->getMessage());
    http_response_code(500);
    echo "Excepción: " . $e->getMessage();
}

echo "<!-- DEBUG2: Finalizando ajaxFactura_debug2.php -->\n";
?>
