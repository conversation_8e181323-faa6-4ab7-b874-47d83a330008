<?php
// Test de impresión simple - Solo PHP, sin JavaScript
echo "<h1>⚡ Test Impresión Simple</h1>";
echo "<p><strong>Impresión directa desde PHP - Sin JavaScript</strong></p>";

// Incluir la clase de impresión
require_once '../../models/crudImpresionDirecta.php';

// Configuración de impresoras (fija)
$impresoras = [
    // CONFIGURACIÓN TEMPORAL PARA PRUEBAS (Red 192.168.18.x)
    'test' => ['ip' => '*************', 'puerto' => 9100, 'nombre' => '🧪 TEST'],

    // CONFIGURACIÓN PRODUCCIÓN (Red 192.168.68.x)
    'bar' => ['ip' => '**************', 'puerto' => 9100, 'nombre' => '🍺 BAR'],
    'cocina' => ['ip' => '**************', 'puerto' => 9100, 'nombre' => '🍳 COCINA'],
    'asados' => ['ip' => '**************', 'puerto' => 9100, 'nombre' => '🥩 ASADOS']
];

echo "<div style='background-color: #d1ecf1; padding: 15px; border-left: 4px solid #17a2b8; margin-bottom: 20px;'>";
echo "<h4>📡 Configuración Actual</h4>";
echo "<p><strong>Tu IP:</strong> " . $_SERVER['REMOTE_ADDR'] . "</p>";
echo "<p><strong>Impresoras configuradas:</strong></p>";
echo "<ul>";
foreach ($impresoras as $categoria => $config) {
    echo "<li><strong>{$config['nombre']}:</strong> {$config['ip']}:{$config['puerto']}</li>";
}
echo "</ul>";
echo "</div>";

// Procesar formularios
if ($_POST) {
    echo "<div style='border: 1px solid #007bff; padding: 20px; margin: 15px 0; border-radius: 8px;'>";
    
    if (isset($_POST['test_individual'])) {
        $categoria = $_POST['categoria'];
        $contenido = $_POST['contenido'];
        
        echo "<h4>🔄 Imprimiendo en: " . strtoupper($categoria) . "</h4>";
        
        $inicio = microtime(true);
        $resultado = ImpresionDirecta::imprimir($categoria, $contenido);
        $tiempo = round((microtime(true) - $inicio) * 1000, 2);
        
        if ($resultado['success']) {
            echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0;'>";
            echo "<h5>✅ Impresión Exitosa</h5>";
            echo "<p><strong>Categoría:</strong> {$resultado['categoria']}</p>";
            echo "<p><strong>IP:</strong> {$resultado['ip']}</p>";
            echo "<p><strong>Bytes enviados:</strong> {$resultado['bytes_enviados']}</p>";
            echo "<p><strong>Tiempo:</strong> {$tiempo}ms</p>";
            echo "<p><strong>Timestamp:</strong> {$resultado['timestamp']}</p>";
            echo "</div>";
        } else {
            echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
            echo "<h5>❌ Error de Impresión</h5>";
            echo "<p><strong>Categoría:</strong> {$resultado['categoria']}</p>";
            echo "<p><strong>Error:</strong> {$resultado['error']}</p>";
            echo "<p><strong>Tiempo:</strong> {$tiempo}ms</p>";
            echo "</div>";
        }
    }
    
    if (isset($_POST['test_masivo'])) {
        echo "<h4>🚀 Test Masivo - Todas las Impresoras</h4>";
        
        $contenidos = [
            'test' => "TEST MASIVO - PRUEBA\nProducto de prueba x1\nHora: " . date('H:i:s'),
            'bar' => "TEST MASIVO - BAR\nCerveza Corona x1\nHora: " . date('H:i:s'),
            'cocina' => "TEST MASIVO - COCINA\nBandeja Paisa x1\nHora: " . date('H:i:s'),
            'asados' => "TEST MASIVO - ASADOS\nChurrasco x1\nHora: " . date('H:i:s')
        ];
        
        $inicio_total = microtime(true);
        $exitosos = 0;
        
        foreach ($contenidos as $categoria => $contenido) {
            $inicio = microtime(true);
            $resultado = ImpresionDirecta::imprimir($categoria, $contenido);
            $tiempo = round((microtime(true) - $inicio) * 1000, 2);
            
            if ($resultado['success']) {
                echo "<div style='background-color: #d4edda; padding: 10px; border-left: 4px solid #28a745; margin: 10px 0;'>";
                echo "<p>✅ <strong>" . strtoupper($categoria) . ":</strong> Exitoso ({$resultado['bytes_enviados']} bytes, {$tiempo}ms)</p>";
                echo "</div>";
                $exitosos++;
            } else {
                echo "<div style='background-color: #f8d7da; padding: 10px; border-left: 4px solid #dc3545; margin: 10px 0;'>";
                echo "<p>❌ <strong>" . strtoupper($categoria) . ":</strong> {$resultado['error']} ({$tiempo}ms)</p>";
                echo "</div>";
            }
        }
        
        $tiempo_total = round((microtime(true) - $inicio_total) * 1000, 2);
        
        echo "<div style='background-color: #e7f3ff; padding: 15px; border-left: 4px solid #007bff; margin: 15px 0;'>";
        echo "<h5>📊 Resumen del Test Masivo</h5>";
        $total_impresoras = count($contenidos);
        echo "<p><strong>Exitosos:</strong> $exitosos/$total_impresoras impresoras</p>";
        echo "<p><strong>Tiempo total:</strong> {$tiempo_total}ms</p>";
        echo "<p><strong>Promedio por impresora:</strong> " . round($tiempo_total/$total_impresoras, 2) . "ms</p>";
        echo "</div>";
    }
    
    if (isset($_POST['test_conectividad'])) {
        echo "<h4>🔍 Test de Conectividad</h4>";
        
        $inicio_total = microtime(true);
        $estado = ImpresionDirecta::testConectividad();
        $tiempo_total = round((microtime(true) - $inicio_total) * 1000, 2);
        
        foreach ($estado as $categoria => $info) {
            if ($info['status'] === 'online') {
                echo "<div style='background-color: #d4edda; padding: 10px; border-left: 4px solid #28a745; margin: 10px 0;'>";
                echo "<p>✅ <strong>" . strtoupper($categoria) . ":</strong> Online ({$info['ip']}:{$info['puerto']})</p>";
                echo "</div>";
            } else {
                echo "<div style='background-color: #f8d7da; padding: 10px; border-left: 4px solid #dc3545; margin: 10px 0;'>";
                echo "<p>❌ <strong>" . strtoupper($categoria) . ":</strong> Offline - {$info['error']}</p>";
                echo "</div>";
            }
        }
        
        echo "<p><strong>Tiempo total de verificación:</strong> {$tiempo_total}ms</p>";
    }
    
    if (isset($_POST['test_pedido'])) {
        echo "<h4>🧾 Test Pedido Completo</h4>";
        
        $productos = [
            ['nombre' => 'Cerveza Corona', 'categoria' => 'bebidas', 'cantidad' => 2, 'precio' => 8000],
            ['nombre' => 'Bandeja Paisa', 'categoria' => 'comidas', 'cantidad' => 1, 'precio' => 25000],
            ['nombre' => 'Churrasco', 'categoria' => 'carnes', 'cantidad' => 1, 'precio' => 35000]
        ];
        
        $numero_pedido = 'P' . str_pad(rand(100, 999), 6, '0', STR_PAD_LEFT);
        
        $inicio_total = microtime(true);
        $resultados = ImpresionDirecta::imprimirPedido($productos, '5', $numero_pedido);
        $tiempo_total = round((microtime(true) - $inicio_total) * 1000, 2);
        
        echo "<p><strong>Pedido:</strong> $numero_pedido</p>";
        echo "<p><strong>Mesa:</strong> 5</p>";
        
        foreach ($resultados as $categoria => $resultado) {
            if ($resultado['success']) {
                echo "<div style='background-color: #d4edda; padding: 10px; border-left: 4px solid #28a745; margin: 10px 0;'>";
                echo "<p>✅ <strong>" . strtoupper($categoria) . ":</strong> Impreso exitosamente ({$resultado['bytes_enviados']} bytes)</p>";
                echo "</div>";
            } else {
                echo "<div style='background-color: #f8d7da; padding: 10px; border-left: 4px solid #dc3545; margin: 10px 0;'>";
                echo "<p>❌ <strong>" . strtoupper($categoria) . ":</strong> {$resultado['error']}</p>";
                echo "</div>";
            }
        }
        
        echo "<p><strong>Tiempo total:</strong> {$tiempo_total}ms</p>";
    }
    
    echo "</div>";
}

// Formularios de test
echo "<h2>🧪 Tests Disponibles</h2>";

// Test individual
echo "<form method='POST' style='background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>🖨️ Test Individual</h4>";
echo "<div style='margin: 15px 0;'>";
echo "<label><strong>Impresora:</strong></label><br>";
echo "<select name='categoria' required style='padding: 8px; border: 1px solid #ccc; border-radius: 3px; width: 200px;'>";
foreach ($impresoras as $categoria => $config) {
    echo "<option value='$categoria'>{$config['nombre']}</option>";
}
echo "</select>";
echo "</div>";
echo "<div style='margin: 15px 0;'>";
echo "<label><strong>Contenido:</strong></label><br>";
echo "<textarea name='contenido' rows='5' cols='50' required style='padding: 8px; border: 1px solid #ccc; border-radius: 3px; font-family: monospace;'>Mesa 1\nCerveza Corona x2\nTotal: $16.000</textarea>";
echo "</div>";
echo "<button type='submit' name='test_individual' style='background-color: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold; width: 100%;'>🖨️ Imprimir</button>";
echo "</form>";

// Test masivo
echo "<form method='POST' style='background-color: #fff3cd; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>🚀 Test Masivo</h4>";
echo "<p>Imprime en las 3 impresoras simultáneamente</p>";
echo "<button type='submit' name='test_masivo' style='background-color: #ffc107; color: #212529; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold; width: 100%;'>🚀 Imprimir en TODAS</button>";
echo "</form>";

// Test conectividad
echo "<form method='POST' style='background-color: #e7f3ff; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>🔍 Test Conectividad</h4>";
echo "<p>Verifica el estado de todas las impresoras</p>";
echo "<button type='submit' name='test_conectividad' style='background-color: #17a2b8; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold; width: 100%;'>🔍 Verificar Estado</button>";
echo "</form>";

// Test pedido completo
echo "<form method='POST' style='background-color: #d4edda; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>🧾 Test Pedido Completo</h4>";
echo "<p>Simula un pedido real con productos distribuidos automáticamente</p>";
echo "<button type='submit' name='test_pedido' style='background-color: #28a745; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold; width: 100%;'>🧾 Simular Pedido</button>";
echo "</form>";

// Información adicional
echo "<h2>💡 Información del Sistema</h2>";
echo "<div style='background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>⚡ Ventajas del Sistema Directo:</h4>";
echo "<ul>";
echo "<li>🚀 <strong>Velocidad:</strong> Conexión directa sin proxy</li>";
echo "<li>🔒 <strong>Confiabilidad:</strong> Sin dependencias externas</li>";
echo "<li>📱 <strong>Compatibilidad:</strong> Funciona desde móviles y PC</li>";
echo "<li>⚡ <strong>Eficiencia:</strong> Sin verificaciones de red</li>";
echo "</ul>";

echo "<h4>🔧 Configuración:</h4>";
echo "<ul>";
echo "<li><strong>Timeout:</strong> 3 segundos por impresora</li>";
echo "<li><strong>Formato:</strong> ESC/POS para impresoras térmicas</li>";
echo "<li><strong>Distribución:</strong> Automática por categoría de producto</li>";
echo "</ul>";
echo "</div>";

// Botones de navegación
echo "<p style='margin-top: 30px;'>";
echo "<a href='test_impresion_directo.php' style='background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>← Test con JavaScript</a>";
echo "<a href='ejemplo_impresion_directa.php' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>💡 Ejemplos de Código</a>";
echo "<a href='index.php?action=registroPmesa&ida=1' style='background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🪑 Mesa Real</a>";
echo "</p>";
?>
