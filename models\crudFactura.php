<?php

	#EXTENSIÓN DE CLASES: Los objetos pueden ser extendidos, y pueden heredar propiedades y métodos. Para definir una clase como extensión, debo definir una clase padre, y se utiliza dentro de una clase hija.

	require_once "conexion.php";

	class DatosFactura extends Conexion
		{
			
			#FACTURA
			#----------------------------------------------
				#REGISTRO DE FACTURA pedidos_id, turno_cajero_id, valor, fecha_venta, subtotal, iva, total, efectivo, debito, credito, cambio Factura factura
				#-------------------------------------
					public static function registroFacturaModel($datosModel, $tabla)
						{	echo "<script>alert('Entro CRUD')</script>";
							$consulta="INSERT INTO $tabla (pedidos_id, turno_cajero_id, valor, fecha_venta, subtotal, iva, total, efectivo, debito, credito, cambio) VALUES (:pedidos_id, :turno_cajero_id, :valor, :fecha_venta, :subtotal, :iva, :total, :efectivo, :debito, :credito, :cambio)";
								
							$stmt = Conexion::conectar()->prepare($consulta);
							$stmt->execute();
							$stmt->bindParam(":pedidos_id", $datosModel['pedidos_id'], PDO::PARAM_INT);
							$stmt->bindParam(":turno_cajero_id", $datosModel['turno_cajero_id'], PDO::PARAM_INT);
							$stmt->bindParam(":valor", $datosModel['valor'], PDO::PARAM_INT);						
							$stmt->bindParam(":fecha_venta", $datosModel['fecha_venta'], PDO::PARAM_STR);
							$stmt->bindParam(":subtotal", $datosModel['subtotal'], PDO::PARAM_INT);
							$stmt->bindParam(":iva", $datosModel['iva'], PDO::PARAM_INT);
							$stmt->bindParam(":total", $datosModel['total'], PDO::PARAM_INT);
							$stmt->bindParam(":efectivo", $datosModel['efectivo'], PDO::PARAM_INT);
							$stmt->bindParam(":debito", $datosModel['debito'], PDO::PARAM_INT);
							$stmt->bindParam(":credito", $datosModel['credito'], PDO::PARAM_INT);
							$stmt->bindParam(":cambio", $datosModel['cambio'], PDO::PARAM_INT);

							echo "<script>alert('Guardo'..)</script>";	
							echo "<script>alert('Entro CRUD ".$datosModel["subtotal"]."  q no')</script>";						

							if($stmt->execute())
								{	return "success";	}
							else{ return "error";		}
							$stmt->close();
						}

				#VISTA FACTURA
				#-------------------------------------

					public static function vistaFacturaModel($tabla){

						$stmt = Conexion::conectar()->prepare("SELECT id, pedidos_id, turno_cajero_id, valor, fecha_venta, subtotal, iva, total, efectivo, bancolombia, nequi, daviplata, valortarjeta, cambio, propina, mesa FROM $tabla ORDER BY fecha_venta DESC");
						$stmt->execute();
						#fetchAll(): Obtiene todas las filas de un conjunto de resultados asociado al objeto PDOStatement.
						return $stmt->fetchAll();
						$stmt->close();

					}

				#EDITAR FACTURA
				#-------------------------------------

					public static function editarFacturaModel($datosModel, $tabla){
						$stmt = Conexion::conectar()->prepare("SELECT id, pedidos_id, turno_cajero_id, valor, fecha_venta, subtotal, iva, total, efectivo, bancolombia, nequi, daviplata, valortarjeta, cambio, propina, mesa FROM $tabla WHERE id = :id");
						$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);
						$stmt->execute();
						return $stmt->fetch();
						$stmt->close();
					}

				#ACTUALIZAR FACTURA
				#-------------------------------------

					public static function actualizarFacturaModel($datosModel, $tabla){

						$stmt = Conexion::conectar()->prepare("UPDATE $tabla SET pedidos_id = :pedidos_id, turno_cajero_id= :turno_cajero_id, valor= :valor,  fecha_venta= :fecha_venta, subtotal= :subtotal, iva = iva, total = total, efectivo = efectivo, debito = debito, credito = credito, cambio = cambio WHERE id = :id");
						
						$stmt->bindParam(":pedidos_id", $datosModel["pedidos_id"], PDO::PARAM_INT);
						$stmt->bindParam(":turno_cajero_id", $datosModel["turno_cajero_id"], PDO::PARAM_STR);
						$stmt->bindParam(":valor", $datosModel["valor"], PDO::PARAM_STR);						
					
						$stmt->bindParam(":id", $datosModel["id"], PDO::PARAM_INT);
						

						if($stmt->execute())
							{	return "success";	}

						else{	return "error";		}
						$stmt->close();
					}


				#BORRAR FACTURA
				#------------------------------------
					public static function borrarFacturaModel($datosModel, $tabla){
						$stmt = Conexion::conectar()->prepare("DELETE FROM $tabla WHERE id = :id");					

						$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);

						if($stmt->execute()){
							return "success";
						}

						else{			return "error";		}
						$stmt->close();
					}
			#----------------------------------------------
		}