<?php
session_start();
require_once "../../models/conexion.php";

echo "<!DOCTYPE html>";
echo "<html><head><title>Test Facturación Rápida</title></head><body>";
echo "<h2>🧪 Test de Facturación Rápida</h2>";

// Verificar que los archivos existan
$archivos = [
    'facturacionRapida.php',
    'ajaxBuscarProductoRapido.php', 
    'ajaxFacturacionRapida.php'
];

echo "<h3>📁 Verificación de Archivos:</h3>";
foreach ($archivos as $archivo) {
    if (file_exists($archivo)) {
        echo "<p style='color: green;'>✅ $archivo - Existe</p>";
    } else {
        echo "<p style='color: red;'>❌ $archivo - No existe</p>";
    }
}

// Verificar productos en la base de datos
try {
    $db = Conexion::conectar();
    $sql = "SELECT COUNT(*) as total FROM productos";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<h3>📦 Productos Disponibles:</h3>";
    echo "<p><strong>Total productos:</strong> " . $result['total'] . "</p>";
    
    if ($result['total'] > 0) {
        // Mostrar algunos productos de ejemplo
        $sql = "SELECT id, nombre, precio FROM productos LIMIT 5";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $productos = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h4>Productos de ejemplo:</h4>";
        echo "<ul>";
        foreach ($productos as $producto) {
            echo "<li>" . $producto['nombre'] . " - $" . number_format($producto['precio']) . "</li>";
        }
        echo "</ul>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error conectando a la base de datos: " . $e->getMessage() . "</p>";
}

// Verificar rutas en navegación
echo "<h3>🔗 Enlaces de Navegación:</h3>";
echo "<p>Verifica que aparezca '🚀 FACTURACIÓN RÁPIDA' en:</p>";
echo "<ul>";
echo "<li>navegacion.php (administradores)</li>";
echo "<li>navegacion1.php (cajeros)</li>";
echo "</ul>";

// Verificar ruta en enlaces.php
echo "<h3>🛣️ Rutas del Sistema:</h3>";
$enlaces_content = file_get_contents('../../models/enlaces.php');
if (strpos($enlaces_content, 'facturacionRapida') !== false) {
    echo "<p style='color: green;'>✅ Ruta 'facturacionRapida' agregada a enlaces.php</p>";
} else {
    echo "<p style='color: red;'>❌ Ruta 'facturacionRapida' NO encontrada en enlaces.php</p>";
}

?>

<h3>🚀 Acceso Directo:</h3>
<a href="facturacionRapida.php" style="background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-size: 18px;">
    🚀 Abrir Facturación Rápida
</a>

<br><br>

<h3>🔗 Acceso por Ruta del Sistema:</h3>
<a href="../../index.php?action=facturacionRapida" style="background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-size: 18px;">
    🌐 Facturación Rápida (Sistema)
</a>

<br><br>

<h3>📋 Instrucciones de Uso:</h3>
<div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;">
    <h4>Cómo usar la Facturación Rápida:</h4>
    <ol>
        <li><strong>Buscar Productos:</strong> Escribe el nombre del producto en el campo de búsqueda</li>
        <li><strong>Seleccionar:</strong> Haz clic en el producto deseado para agregarlo</li>
        <li><strong>Ajustar Cantidad:</strong> Cambia la cantidad si es necesario</li>
        <li><strong>Forma de Pago:</strong> Ingresa los montos en efectivo, tarjeta, etc.</li>
        <li><strong>Facturar:</strong> Haz clic en "FACTURAR RÁPIDO"</li>
        <li><strong>Imprimir:</strong> Se abrirá automáticamente el PDF para imprimir</li>
    </ol>
    
    <h4>Características:</h4>
    <ul>
        <li>✅ Sin estados de pedidos - Facturación directa</li>
        <li>✅ Búsqueda rápida de productos</li>
        <li>✅ Múltiples formas de pago</li>
        <li>✅ Cálculo automático de cambio</li>
        <li>✅ Impresión automática de factura</li>
        <li>✅ Limpieza automática del formulario</li>
    </ul>
</div>

<br>
<a href="../../index.php?action=mesa" style="background: #6c757d; color: white; padding: 10px; text-decoration: none; border-radius: 5px;">🔙 Volver a Mesas</a>

</body>
</html>
