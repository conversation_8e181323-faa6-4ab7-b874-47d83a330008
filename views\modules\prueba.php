
<?php 
	$registroCsuministro = new controllerCompraSuministro();
	$proveedores = $registroCsuministro -> listaProveedoresController();
	$registroCsuministro -> registroCompraSuministroController();
 ?>

<link rel="stylesheet" type="text/css" href="../css/bootstrap.css">
<link rel="stylesheet" type="text/css" href="../css/bootstrap-theme.css">
<link rel="stylesheet" type="text/css" href="../estilo.css">
<link rel="stylesheet" type="text/css" href="../font-awesome/css/font-awesome.css">
<script src="../js/jquery-1.11.3.min.js" type="text/javascript"></script>
<script src="http://code.jquery.com/jquery-latest.js"></script>
<script type="text/javascript">
$(document).ready(function(){

$("#add").click(function(){
// Obtenemos el numero de columnas (td) que tiene la primera fila
// (tr) del id "tabla"
var tds=$("#tabla tr:first td").length;
// Obtenemos el total de filas (tr) del id "tabla"
var trs=$("#tabla tr").length;
var nuevaFila="<tr>";
cant = $('#contador-filas').val();
cant++;
$('#contador-filas').val(cant)
nuevaFila+="<td><input class='form-control' type='text' name='cantidad["+(cant)+"]' placeholder='cantidad"+(cant)+"' required /> </td>"+
"<td><input class='form-control' type='text' name='articulo["+(cant)+"]' placeholder='articulo"+(cant)+"' required /> </td>"+
"<td><input class='form-control' type='text' name='precio["+(cant)+"]' placeholder='precio"+(cant)+"' required /> </td>";
// Añadimos una columna con el numero total de columnas.
// Añadimos uno al total, ya que cuando cargamos los valores para la
// columna, todavia no esta añadida
nuevaFila+="</tr>";
$("#tabla").append(nuevaFila);
});
/**
* Funcion para eliminar la ultima columna de la tabla.
* Si unicamente queda una columna, esta no sera eliminada
*/
$("#del").click(function(){
// Obtenemos el total de filas (tr) del id "tabla"
var trs=$("#tabla tr").length;
if(trs>2)
{
// Eliminamos la ultima fila
cant--;
$('#contador-filas').val(cant)
$("#tabla tr:last").remove();

}
});
});
</script>

<style>
#add, #del {text-decoration:none;color:#fff;}
</style>

<?php
if (!isset($_POST['datos'])) 
{ 

?>
<div class="container-fluid">
<div class="row">
<center>
<h1>REGISTRO DE COMPRA SUMINISTRO</h1><br>
		<form action="" method="post" name="cal">
		<table>				 	
				<tr>
					<td align="right"><label> PROVEEDOR : </label></td>
					<td><?php  
						# # %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  proveedores  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
							if($proveedores=="error")
								{	echo "debe registrar el proveedores"; }
							else{
									echo "<label></label>";
									$result='<select name="proveedores" class="form-control"  id="proveedores">';
									$result.=' <option value="-1">proveedores</option>';
									foreach ($proveedores as $row => $item)
									 	{	$result.=' <option value="'.$item["id"].'">'.$item["nombre"].'</option>';	}	
									 $result.='</select>';
									 echo $result;
								}  
						# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  End proveedores  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
						 ?>&laquo;<a class="enlace" href="registroProveedor" target="_blank"> Registrar proveedor</a>&raquo;<br></td>
				</tr>
				<tr>
					<td align="right"><label>  VALOR FACTURA : </label></td>
					<td><input type="text" placeholder="valor_compra" class='form-control' name="valor_compraCsuministroRegistro" required></td>
				</tr>
				<tr>
					<td align="right"><label> FECHA : </label></td>
					<td><input type="date" placeholder="AAAA-MM-DD HH:MM:SS" class='form-control' name="fecha_horaCsuministroRegistro" required></td>
				</tr>
				<tr>
					<td align="right"><label> No FACTURA : </label></td>
					<td><input type="text" placeholder="numero factura compra" class='form-control' name="numero_factura_compraCsuministroRegistro" ></td>
				</tr>
				<tr>
					<td align="right"><label> ESTADO : </label></td>
					<td><div >
				  		<select name="pago" id="pago" class='form-control' onchange="habilitar(this.value);">
					  <option value="1">Cancelada</option>
					  <option value="2">Credito</option>
					  
					</select>
				  	</div></td>
				</tr>
				<tr>
					<td align="right"><label  > ABONO : </label></td>
					<td><input type="text" placeholder="$" class='form-control' name="abono" value="0" id="abono" ></td>
				</tr>
				
			</table>
		<br />
	</form>
			<h2>Detalle de la Factura</h2>
			<br >
<table class="table table-hover table-condensed" style="width: 40%" id="tabla">
<thead>
<tr class="info">
<td><b>campo 1</b></td>
<td><b>campo 2</b></td>
<td><b>campo 3</b></td>
</tr>
</thead>
<tbody>
<tr class="fila-0">
<input type="text" name="contador-filas" id="contador-filas" value="0" />
<td><input class="form-control" type="text" name="cantidad[0]" placeholder="cantidad" required /></td>
<td><input class="form-control" type="text" name="articulo[0]" placeholder="articulo" required /></td>
<td><input class="form-control" type="text" name="precio[0]" placeholder="precio" required /></td>
</tr>
</tbody>
</table>
<button id="add" class="btn btn-sm btn-success">Agregar</button>
<button id="del" class="btn btn-sm btn-danger">Eliminar</button>	
<button type="submit" name="datos" class="btn btn-sm btn-primary" >Guardar </button>
<button type="reset" name="borrar" class="btn btn-sm btn-warning" >Limpiar </button>
</form>
</center>
<?php
}
else
{
$cantidad[]= $_POST['cantidad'];
$articulo[]= $_POST['articulo'];
$precio[]= $_POST['precio'];


} 

?>

</div>
</div>

tabla remplasada
<label> PROVEEDOR : </label>
				<?php  
					# #%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  proveedores  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
						if($proveedores=="error")
							{	echo "debe registrar el proveedores"; }
						else{
								echo "<label></label>";
								$result='<select name="proveedores" class="form-control"  id="proveedores">';
								$result.=' <option value="-1">proveedores</option>';
								foreach ($proveedores as $row => $item)
								 	{	$result.=' <option value="'.$item["id"].'">'.$item["nombre"].'</option>';	}	
								 $result.='</select>';
								 echo $result;
							}  
					# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  End proveedores  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
					 ?>&laquo;<a class="enlace" href="registroProveedor" target="_blank"> Registrar proveedor</a>&raquo;<br>
						
				<label>  VALOR FACTURA : </label>	
				<input type="text" placeholder="valor_compra" class='form-control' name="valor_compraCsuministroRegistro" required>
					<label> FECHA : </label>
					<input type="date" placeholder="AAAA-MM-DD HH:MM:SS" class='form-control' name="fecha_horaCsuministroRegistro" required>
				
					<label> No FACTURA : </label>
					<input type="text" placeholder="numero factura compra" class='form-control' name="numero_factura_compraCsuministroRegistro" >
				
					<label> ESTADO : </label>
					<div >
				  		<select name="pago" id="pago" class='form-control' onchange="habilitar(this.value);">
					  <option value="1">Cancelada</option>
					  <option value="2">Credito</option>
					  
					</select>
				  	</div>
				
					<label  > ABONO : </label>
					<input type="text" placeholder="$" class='form-control' name="abono" value="0" id="abono" >


hhhhhhhhhhhhhhhhhhhh

<?php 
	$registroCsuministro = new controllerCompraSuministro();
	$proveedores = $registroCsuministro -> listaProveedoresController();
	$registroCsuministro -> registroCompraSuministroController();
 ?>
	
	<link rel="stylesheet" type="text/css" href="../css/bootstrap.css">
	<link rel="stylesheet" type="text/css" href="../css/bootstrap-theme.css">
	<link rel="stylesheet" type="text/css" href="../estilo.css">
	<link rel="stylesheet" type="text/css" href="../font-awesome/css/font-awesome.css">
	<script src="../js/jquery-1.11.3.min.js" type="text/javascript"></script>
	<script src="http://code.jquery.com/jquery-latest.js"></script>
	
	<script type="text/javascript">
	     /////////////Crear filas
	    	 function genera_tabla() 
	    	  {
				  // Obtener la referencia del elemento body
				  var body = document.getElementsByTagName("body")[0];				 
				  // Crea un elemento <table> y un elemento <tbody>
				  var tabla   = document.createElement("table");
				  var tblBody = document.createElement("tbody");				 
				  // Crea las celdas
				  for (var i = 0; i < 3; i++) 
				   {
					    // Crea las hileras de la tabla
					    var hilera = document.createElement("tr");				 
					    for (var j = 0; j < 3; j++) 
					     {
						      // Crea un elemento <td> y un nodo de texto, haz que el nodo de
						      // texto sea el contenido de <td>, ubica el elemento <td> al final
						      // de la hilera de la tabla
						      var celda = document.createElement("td");
						      var textoCelda = document.createTextNode("celda en la hilera "+i+", columna "+j);
						      celda.appendChild(textoCelda);
						      hilera.appendChild(celda);
				     	 }			 
					    // agrega la hilera al final de la tabla (al final del elemento tblbody)
					    tblBody.appendChild(hilera);
			  		}			 
				  // posiciona el <tbody> debajo del elemento <table>
				  tabla.appendChild(tblBody);
				  // appends <table> into <body>
				  body.appendChild(tabla);
				  // modifica el atributo "border" de la tabla y lo fija a "2";
				  tabla.setAttribute("border", "1");
				 }
	    	///////////fin crear filas

	    $(document).ready(function()
	    {
	        /**  * Funcion para añadir una nueva columna en la tabla*/
	         
	        $("#add").click(function()
	        	{
		            // Obtenemos el numero de filas (td) que tiene la primera columna
		            // (tr) del id "tabla"
		            var tds=$("#tabla tr:first td").length;
		            //variable de los imput creo
		            //var  nombre=$("#nombre");
		            var nombre = document.cal.nombre.value;
		            var cantidad = document.cal.cantidad.value;   
		            var precio = document.cal.precio.value;   
		            // Obtenemos el total de columnas (tr) del id "tabla"
		            var trs=$("#tabla tr").length;
		            var nuevaFila="<tr>";
		            ////////////////////////matriz ///////////
		            matriz= new Array(trs);
		            for(var j=0;j<trs;j++)
		             {
		             	matriz[j]=new Array(3);
		             }
		            for(var j=0;j<trs;j++)
		             {
		             	matriz[j]['suministro']=nombre;
		             	matriz[j]['cantidad']=cantidad;
		             	matriz[j]['precio']=cantidad;
		             	alert(matriz[j]['suministro']+" cantidad:"+matriz[j]['cantidad']);
		             }
		          //////////////////////////////////// 
		            for(var i=0;i<tds;i++)
		             {
		                // añadimos las columnas
		                if (i==0)
		                 {
		                 	nuevaFila+="<td><input class='form-control' type='text' name='nombre"+trs+"' value='"+nombre+ "' required /> </td>";
		                 	
		                 }
		                else
		                 {	if (i==1) 
		                 	 {
		                 		nuevaFila+="<td><input class='form-control' type='text' name='cantidad"+trs+"' value='"+cantidad+"' required /></td>";
		                 	 }
		                 	else
		                 	 {
		                 		nuevaFila+="<td><input class='form-control' type='text' name='precio"+trs+"' value='"+precio+"' required /></td>";
		                 	 }
		                 	//matriz[trs][i]=cantidad;
		                 }
		             }
		            // Añadimos una columna con el numero total de filas.
		            // Añadimos uno al total, ya que cuando cargamos los valores para la
		            // columna, todavia no esta añadida	            
		            nuevaFila+="<td>"+(trs)+" filas";
		            nuevaFila+="</tr>";
		            $("#tabla").append(nuevaFila);
		            document.cal.nombre.value="";
		            document.cal.cantidad.value="";
		            document.cal.precio.value="";
		            document.cal.fila.value=trs;
		            //alert( matriz[trs-1]['nombre']);
		        });
		 
		        /**
		         * Funcion para eliminar la ultima columna de la tabla.
		         * Si unicamente queda una columna, esta no sera eliminada
		         */
		        $("#del").click(function(){
	            // Obtenemos el total de columnas (tr) del id "tabla"
		            var trs=$("#tabla tr").length;
		            if(trs>1)
		            {
		                // Eliminamos la ultima columna
		                $("#tabla tr:last").remove();
		            }
		        });
		       // $("#destino").load("views/modules/ajaxFactura.php", {matriz: matriz,trs: trs, }, function(){
				        						 //alert("recibidos los datos por ajax efectivo"); 		 
				     						///});	
	    });

	    </script>

	    <style>
	    td, input {padding:5px;}
	    </style>
	
		<?php
			if (!isset($_POST['datos'])) 
			 { 
				//echo "<script>alert(' cantidad".$cantidad['1']."')</script>";
			 }
		?>
		<h1>REGISTRO DE COMPRA SUMINISTRO</h1><br>
		<form action="" method="post" name="cal">
			<div  class="col-md-3" style="background-color:#00f; border-radius: 15px 15px 15px 15px; padding:12px">	
				<label> PROVEEDOR : </label>
				<?php  
					# #%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  proveedores  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
						if($proveedores=="error")
							{	echo "debe registrar el proveedores"; }
						else{
								echo "<label></label>";
								$result='<select name="proveedores" class="form-control"  id="proveedores">';
								$result.=' <option value="-1">proveedores</option>';
								foreach ($proveedores as $row => $item)
								 	{	$result.=' <option value="'.$item["id"].'">'.$item["nombre"].'</option>';	}	
								 $result.='</select>';
								 echo $result;
							}  
					# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  End proveedores  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
					 ?>&laquo;<a class="enlace" href="registroProveedor" target="_blank"> Registrar proveedor</a>&raquo;<br>
						
				<label>  VALOR FACTURA : </label>	
				<input type="text" placeholder="valor_compra" class='form-control' name="valor_compraCsuministroRegistro" required>
					<label> FECHA : </label>
					<input type="date" placeholder="AAAA-MM-DD HH:MM:SS" class='form-control' name="fecha_horaCsuministroRegistro" required>
				
					<label> No FACTURA : </label>
					<input type="text" placeholder="numero factura compra" class='form-control' name="numero_factura_compraCsuministroRegistro" >
				
					<label> ESTADO : </label>
					<div >
				  		<select name="pago" id="pago" class='form-control' onchange="habilitar(this.value);">
					  <option value="1">Cancelada</option>
					  <option value="2">Credito</option>
					  
					</select>
				  	</div>
				
					<label  > ABONO : </label>
					<input type="text" placeholder="$" class='form-control' name="abono" value="0" id="abono" >
				
			</div>
			
		<br />
		<div  class="col-md-9" style="background-color:#FFF; border-radius: 15px 15px 15px 15px; padding:12px">	
			
			
			<table>
				<caption><H3>Detalle de la Factura</H3></caption>
				<tr>
					<td align="right">Suministro:</td>
					<td><input type="text" name="nombre" id="nombre"></td>
				</tr>
				<tr>
					<td align="right">Cantidad :</td>
					<td><input type="text" name="cantidad" id="cantidad"></td>
				</tr>
				<tr>
					<td align="right">Precio :</td>
					<td><input type="text" name="precio" id="precio" ></td>
				</tr>
			</table>
			 
			<input type="hidden" name="fila" id="fila"> <br>
			<input type="button" id="add" value="añadir una nueva fila desde jquery">
			<input type="button" id="del" value="eliminar la ultima fila desde jquery">
			<p>			
		    <table id="tabla" class="table" border=1>
		        <tr>
		            <td>suministro</td>
		            <td>Cantidad</td>
		            <td>Precio</td>
		            <!-- podemos añadir tantas columnas como deseemos -->
		            <!--<td>tercera columna</td>-->
		        </tr>
		    </table>
		    <input type="submit" value="Enviar">
		</div>
		
		<div id="destino">
			
		</div>

		</form>

		UUUUUUUUUUUU CAPITULO

		<?php

	$registroCsuministro = new controllerCompraSuministro();
	$proveedores = $registroCsuministro -> listaProveedoresController();
	$registroCsuministro -> registroCsuministroController();

	if(isset($_GET["action"]))
		{	
			if($_GET["action"] == "okCs")
			{echo "Registro Exitoso";	}
		}

?>


<script>
	<script>
		function habilitar(value)
		{
			//document.getElementById("abono").disabled=true;
			/*if(value=="1" || value==true)
			{
				// habilitamos
				document.getElementById("abono").disabled=false;
			}else if(value=="2" || value==false){
				// deshabilitamos
				document.getElementById("abono").disabled=true;
			}*/
		}
	</script>
    
   
</script>

<h1>REGISTRO DE COMPRA SUMINISTRO</h1><br>
<form method="post" >		
	<!--input type="text" placeholder="proveedor_id" name="proveedor_idCsuministroRegistro" required><br-->
	 

	<table>		 	
			<tr>
				<td align="right"><label> PROVEEDOR : </label></td>
				<td><?php  
					# # %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  proveedores  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
						if($proveedores=="error")
							{	echo "debe registrar el proveedores"; }
						else{
								echo "<label></label>";
								$result='<select name="proveedores"  id="proveedores">';
								$result.=' <option value="-1">proveedores</option>';
								foreach ($proveedores as $row => $item)
								 	{	$result.=' <option value="'.$item["id"].'">'.$item["nombre"].'</option>';	}	
								 $result.='</select>';
								 echo $result;
							}  
					# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  End proveedores  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  max="<?=$_SESSION["fecha"];
					 ?>&laquo;<a class="enlace" href="registroProveedor" target="_blank"> Registrar proveedor</a>&raquo;<br></td>
			</tr>
			<tr>
				<td align="right"><label>  VALOR FACTURA : </label></td>
				<td><input type="text" placeholder="valor_compra" name="valor_compraCsuministroRegistro" required></td>
			</tr>
			<tr>
				<td align="right"><label> FECHA : </label></td>
				<td><input type="datetime-local" placeholder="AAAA-MM-DD HH:MM:SS" name="fecha_horaCsuministroRegistro"  required></td>
			</tr>
			<tr>
				<td align="right"><label> No FACTURA : </label></td>
				<td><input type="text" placeholder="numero factura compra" name="numero_factura_compraCsuministroRegistro" ></td>
			</tr>
			<tr>
				<td align="right"><label> ESTADO : </label></td>
				<td><div >
			  		<select name="pago" id="pago" onchange="habilitar(this.value);">
				  <option value="1">Cancelada</option>
				  <option value="2">Credito</option>
				  
				</select>
			  	</div></td>
			</tr>
			<tr>
				<td align="right"><label  > ABONO : </label></td>
				<td><input type="text" placeholder="$" name="abono" value="0" id="abono" ></td>
			</tr>		
	</table>	
	<br><br><br>	
	<input type="submit" value="Enviar">
</form>