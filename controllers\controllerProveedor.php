
<?php 
ob_start();
class controllerProveedor extends MvcController
 {
	#REGISTRO DE PROVEEDOR
	#------------------------------------
	 public function registroProveedorController()
		{
			if(isset($_POST["nombreProveedorRegistro"]))
			 {	
				$datosController =array('nombre'=>$_POST["nombreProveedorRegistro"],
										'nit'=>$_POST["nitProveedorRegistro"],
										'representante'=>$_POST["representanteProveedorRegistro"],
										'direccion'=>$_POST["direccionProveedorRegistro"],
										'telefonos'=>$_POST["telefonosProveedorRegistro"]);
				echo "<script>alert('Entro Controller ".$datosController." no')</script>";	
				$respuesta = DatosProveedor::registroProveedorModel($datosController, "proveedores");
				if($respuesta == "success")
					{	header("location:index.php?action=okPv");	}
				else
					{	header("location:index.php");	}
			 }
		}
	#-------------------------------------
	#VISTA DE PROVEEDOR
	#------------------------------------
	 public function vistaProveedorController()
		{
			$respuesta = DatosProveedor::vistaProveedorModel("proveedores");
			foreach($respuesta as $row => $item)
			 {
				echo'<tr>						
						<td>'.$item["nombre"].'</td>						
						<td>'.$item["nit"].'</td>						
						<td>'.$item["representante"].'</td>						
						<td>'.$item["direccion"].'</td>						
						<td>'.$item["telefonos"].'</td>						
						<td><a href="index.php?action=editarProveedor&id='.$item["id"].'">Editar</a></td>
						<td><a href="index.php?action=proveedor&idBorrar='.$item["id"].'">Borrar</a></td>
					</tr>';
			 }
		}
	#--------------------------------------------

	#EDITAR PROVEEDOR
	#------------------------------------
	 public function editarProveedorController()
		{
			$datosController = $_GET["id"];
			$respuesta = DatosProveedor::editarProveedorModel($datosController, "proveedores");
			echo' nombre<input type="text" value="'.$respuesta["nombre"].'" name="nombreProveedorEditar" required>	
			nit <input type="text" value="'.$respuesta["nit"].'" name="nitProveedorEditar" required> 
			 representante<input type="text" value="'.$respuesta["representante"].'" name="representanteProveedorEditar" required> 
			 direccion<input type="text" value="'.$respuesta["direccion"].'" name="direccionProveedorEditar" required> 
			 telefonos<input type="text" value="'.$respuesta["telefonos"].'" name="telefonosProveedorEditar" required> 
			 <input type="hidden" value="'.$respuesta["id"].'" name="idProveedorEditar" required> 				
				 <input type="submit" value="Actualizar">';
		}
	#--------------------------------------------

	#ACTUALIZAR PROVEEDOR
	#------------------------------------
	 public function actualizarProveedorController()
		{ //echo "<script>alert('Entro Controller Actualizar Poveedor')</script>";
			if(isset($_POST["nombreProveedorEditar"]))
			 {
				$datosController = array(  "nombre"=>$_POST["nombreProveedorEditar"],			
														"nit"=>$_POST["nitProveedorEditar"],				
											"representante"=>$_POST["representanteProveedorEditar"],				
											"direccion"=>$_POST["direccionProveedorEditar"],				
											"telefonos"=>$_POST["telefonosProveedorEditar"],				
											"id"=>$_POST["idProveedorEditar"]);				
				$respuesta = DatosProveedor::actualizarProveedoroModel($datosController, "proveedores");
				if($respuesta == "success")
					{	header("location:index.php?action=cambioPv");	}
							else
					{	echo "error";	}
			 }				
		}
	#--------------------------------------------

	#BORRAR PROVEEDOR
	#------------------------------------
	 public function borrarProveedorController()
		{
			if(isset($_GET["idBorrar"]))
			 {
				$datosController = $_GET["idBorrar"];				
				$respuesta = DatosProveedor::borrarProveedorModel($datosController, "proveedores");
				if($respuesta == "success")
					{	header("location:index.php?action=proveedor");	}
			 }
		}			
   #---------------------------------

 }			
	