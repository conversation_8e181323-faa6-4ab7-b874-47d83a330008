<?php
// Archivo para crear datos de prueba en el sistema

echo "<h1>🧪 Crear Datos de Prueba</h1>";
echo "<hr>";

$db = Conexion::conectar();

echo "<h2>1. 📊 Estado Actual del Sistema</h2>";

// Verificar pedidos existentes
$stmt = $db->prepare("SELECT estado, COUNT(*) as total FROM pedidos GROUP BY estado");
$stmt->execute();
$estadosPedidos = $stmt->fetchAll();

echo "<h3>Pedidos por Estado:</h3>";
if (!empty($estadosPedidos)) {
    echo "<ul>";
    foreach ($estadosPedidos as $estado) {
        echo "<li>" . ucfirst($estado['estado']) . ": " . $estado['total'] . " pedidos</li>";
    }
    echo "</ul>";
} else {
    echo "❌ No hay pedidos en el sistema<br>";
}

// Verificar productos
$stmt = $db->prepare("SELECT categoria, COUNT(*) as total FROM productos GROUP BY categoria");
$stmt->execute();
$productosCategoria = $stmt->fetchAll();

echo "<h3>Productos por Categoría:</h3>";
if (!empty($productosCategoria)) {
    echo "<ul>";
    foreach ($productosCategoria as $cat) {
        echo "<li>" . ucfirst($cat['categoria']) . ": " . $cat['total'] . " productos</li>";
    }
    echo "</ul>";
} else {
    echo "❌ No hay productos en el sistema<br>";
}

echo "<h2>2. 🔧 Crear Datos de Prueba</h2>";

// Formulario para crear datos
echo "<form method='post' style='margin: 20px 0;'>";
echo "<div style='margin: 10px 0;'>";
echo "<button type='submit' name='crear_pedido_borrador' style='background: #6c757d; color: white; padding: 10px; border: none; margin: 5px;'>📝 Crear Pedido Borrador</button>";
echo "<button type='submit' name='crear_pedido_enviado' style='background: #ffc107; color: black; padding: 10px; border: none; margin: 5px;'>📤 Crear Pedido Enviado</button>";
echo "<button type='submit' name='crear_pedido_entregado' style='background: #28a745; color: white; padding: 10px; border: none; margin: 5px;'>✅ Crear Pedido Entregado</button>";
echo "</div>";
echo "<div style='margin: 10px 0;'>";
echo "<button type='submit' name='crear_productos_todas_categorias' style='background: #007bff; color: white; padding: 10px; border: none; margin: 5px;'>🛒 Crear Productos de Todas las Categorías</button>";
echo "</div>";
echo "<div style='margin: 10px 0;'>";
echo "<button type='submit' name='limpiar_datos' style='background: #dc3545; color: white; padding: 10px; border: none; margin: 5px;' onclick='return confirm(\"¿Estás seguro de limpiar todos los datos de prueba?\")'>🗑️ Limpiar Datos de Prueba</button>";
echo "</div>";
echo "</form>";

// Procesar acciones
if ($_POST) {
    try {
        if (isset($_POST['crear_pedido_borrador'])) {
            crearPedidoPrueba('borrador');
        } elseif (isset($_POST['crear_pedido_enviado'])) {
            crearPedidoPrueba('enviado');
        } elseif (isset($_POST['crear_pedido_entregado'])) {
            crearPedidoPrueba('entregado');
        } elseif (isset($_POST['crear_productos_todas_categorias'])) {
            crearProductosTodasCategorias();
        } elseif (isset($_POST['limpiar_datos'])) {
            limpiarDatosPrueba();
        }
        
        echo "<script>setTimeout(function(){ location.reload(); }, 2000);</script>";
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px 0;'>";
        echo "❌ Error: " . $e->getMessage();
        echo "</div>";
    }
}

function crearPedidoPrueba($estado) {
    global $db;
    
    // Crear pedido
    $stmt = $db->prepare("
        INSERT INTO pedidos (mesa_id, mesero_id, cedula_cliente, estado, fecha_pedido, fecha_envio, fecha_entrega, numero_pedido, facturado)
        VALUES (1, ?, '0', ?, NOW(), " . ($estado != 'borrador' ? 'NOW()' : 'NULL') . ", " . ($estado == 'entregado' ? 'NOW()' : 'NULL') . ", CONCAT('P', LPAD(FLOOR(RAND() * 10000), 4, '0')), 'n')
    ");
    $stmt->execute([$_SESSION["usuario"], $estado]);
    $pedidoId = $db->lastInsertId();
    
    // Agregar productos de diferentes categorías
    $categorias = ['bar', 'cocina', 'asados'];
    foreach ($categorias as $cat) {
        $stmt = $db->prepare("SELECT id FROM productos WHERE categoria = ? LIMIT 1");
        $stmt->execute([$cat]);
        $producto = $stmt->fetch();
        
        if ($producto) {
            $stmt = $db->prepare("
                INSERT INTO producto_vendido_mesa (productos_id, mesas_id, pedidos_id, cantidad, fecha_hora, mesero, descuento, codigo_descuento, nota, cocina)
                VALUES (?, 1, ?, ?, NOW(), ?, 0, '', 'Producto de prueba " . $cat . "', ?)
            ");
            $cantidad = rand(1, 3);
            $stmt->execute([$producto['id'], $pedidoId, $cantidad, $_SESSION["usuario"], $cat]);
        }
    }
    
    echo "<div style='background: #d4edda; color: #155724; padding: 10px; margin: 10px 0;'>";
    echo "✅ Pedido " . $estado . " creado exitosamente (ID: $pedidoId)";
    echo "</div>";
}

function crearProductosTodasCategorias() {
    global $db;
    
    $productos = [
        ['nombre' => 'Cerveza Corona', 'precio' => 8000, 'categoria' => 'bar'],
        ['nombre' => 'Mojito', 'precio' => 15000, 'categoria' => 'bar'],
        ['nombre' => 'Arroz con Pollo', 'precio' => 25000, 'categoria' => 'cocina'],
        ['nombre' => 'Pasta Carbonara', 'precio' => 22000, 'categoria' => 'cocina'],
        ['nombre' => 'Churrasco', 'precio' => 35000, 'categoria' => 'asados'],
        ['nombre' => 'Costillas BBQ', 'precio' => 32000, 'categoria' => 'asados']
    ];
    
    $creados = 0;
    foreach ($productos as $prod) {
        // Verificar si ya existe
        $stmt = $db->prepare("SELECT id FROM productos WHERE nombre = ?");
        $stmt->execute([$prod['nombre']]);
        if (!$stmt->fetch()) {
            // Crear producto
            $stmt = $db->prepare("
                INSERT INTO productos (nombre, precio, categoria, codigo, descripcion, stock)
                VALUES (?, ?, ?, CONCAT('COD', FLOOR(RAND() * 1000)), 'Producto de prueba', 100)
            ");
            $stmt->execute([$prod['nombre'], $prod['precio'], $prod['categoria']]);
            $creados++;
        }
    }
    
    echo "<div style='background: #d4edda; color: #155724; padding: 10px; margin: 10px 0;'>";
    echo "✅ $creados productos creados exitosamente";
    echo "</div>";
}

function limpiarDatosPrueba() {
    global $db;
    
    // Eliminar productos de prueba
    $stmt = $db->prepare("DELETE FROM producto_vendido_mesa WHERE nota LIKE '%prueba%'");
    $stmt->execute();
    
    // Eliminar pedidos de prueba (que no tengan productos)
    $stmt = $db->prepare("
        DELETE FROM pedidos 
        WHERE id NOT IN (SELECT DISTINCT pedidos_id FROM producto_vendido_mesa WHERE pedidos_id IS NOT NULL)
        AND numero_pedido LIKE 'P%'
    ");
    $stmt->execute();
    
    echo "<div style='background: #d1ecf1; color: #0c5460; padding: 10px; margin: 10px 0;'>";
    echo "🧹 Datos de prueba limpiados";
    echo "</div>";
}

echo "<h2>3. 🔗 Enlaces Útiles</h2>";
echo "<a href='index.php?action=debug_cocina' style='background: #ffc107; color: black; padding: 10px; text-decoration: none; margin: 5px; display: inline-block;'>🍳 Debug Cocina</a><br>";
echo "<a href='index.php?action=pantallaCocina&categoria=cocina' style='background: #28a745; color: white; padding: 10px; text-decoration: none; margin: 5px; display: inline-block;'>🍳 Pantalla Cocina</a><br>";
echo "<a href='index.php?action=pantallaCocina&categoria=bar' style='background: #007bff; color: white; padding: 10px; text-decoration: none; margin: 5px; display: inline-block;'>🍺 Pantalla Bar</a><br>";
echo "<a href='index.php?action=pantallaCocina&categoria=asados' style='background: #dc3545; color: white; padding: 10px; text-decoration: none; margin: 5px; display: inline-block;'>🥩 Pantalla Asados</a><br>";
echo "<a href='index.php?action=diagnostico' style='background: #6c757d; color: white; padding: 10px; text-decoration: none; margin: 5px; display: inline-block;'>🔧 Diagnóstico</a><br>";

?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}

button {
    cursor: pointer;
    border-radius: 5px;
    font-weight: bold;
}

button:hover {
    opacity: 0.8;
}
</style>
