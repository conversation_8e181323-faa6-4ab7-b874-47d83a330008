<h1>REGISTRO DE SUMINISTRO</h1>

<form method="post">

	<table>
		<thead> 	
			<tr > <td colspan="6" ></td> </tr>
		</thead>
		<tr>
			<td> <label> Codigo  : </label></td><td><input type="text" placeholder="codigo" name="codigo" required>	 </td>
		</tr>
		<tr>
			<td><label> Nombre  : </label></td>
			<td><input type="text" placeholder="nombre" name="nombreSuministroRegistro" required></td>
		</tr>

		<tr>
			<td><label> Cantidad  : </label></td>
			<td><input type="text" placeholder="cantidad" name="cantidadSuministroRegistro" required></td>
		</tr>
		<tr>
			<td><label> Precio Compra : </label></td>
			<td><input type="text" placeholder="precio" name="precioSuministroRegistro" required></td>
		</tr>
		<tr>
			<td><label> Minimo stop : </label></td>
			<td><input type="text" placeholder="MINIMO" name="cantidadMSuministroRegistro" required></td>
		</tr>
		
		</label><input type="hidden" placeholder="MINIMO" value="1" name="tipo" required>
		<thead> 	
			<tr > <td colspan="6" ></td> </tr>
		</thead>
		
	</table><br>

	<input type="submit" value="Enviar">

</form>

<?php

	$registro = new controllerSuministro();
	$registro -> registroSuministroController();

	if(isset($_GET["action"]))

		{	if($_GET["action"] == "okp")
				{	echo "Registro Exitoso";	}
		}
		/*<select name="tipo" id="tipo">
				  <option value="1">Suministro</option>
				  <option value="2">Producto</option>
	</select><br>
	<label> precio Venta: </label><input type="text" placeholder="precio" name="precioRegistro"><br>
		
		 */

?>
