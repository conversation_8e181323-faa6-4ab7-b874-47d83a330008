<?php
// Simular el comportamiento del sistema de rutas
session_start();

// Simular sesión válida para test
$_SESSION["validar"] = true;
$_SESSION["tipo_usuario"] = 1; // Administrador
$_SESSION["usuario"] = 1;
$_SESSION["persona"] = "Test Usuario";

echo "<!DOCTYPE html>";
echo "<html><head><title>Test Simulación de Ruta</title></head><body>";
echo "<h2>🔧 Test de Simulación de Ruta</h2>";

// Simular el comportamiento de models/enlaces.php
function simularEnlaces($enlaces, $usuario) {
    echo "<h3>🔍 Simulando enlaces.php</h3>";
    echo "<p><strong>Enlace solicitado:</strong> $enlaces</p>";
    echo "<p><strong>Tipo de usuario:</strong> $usuario</p>";
    
    $module = null;
    
    // Simular la primera condición (línea 6 del archivo original)
    if($enlaces == "ingresar" || $enlaces == "pagina1" || $enlaces == "consultar" || $enlaces == "editar" || $enlaces == "salir" || $enlaces == "editarp" || $enlaces == "buscar" || $enlaces == "inde" || $enlaces == "usuarios" || $enlaces == "pdf" || $enlaces == "pdf1" || $enlaces == "facturaCopia" || $enlaces == "facturacionRapida" || $enlaces == "diagnostico") {
        $module = "views/modules/".$enlaces.".php";
        echo "<p style='color: green;'>✅ Coincide con primera condición general</p>";
        echo "<p><strong>Módulo asignado:</strong> $module</p>";
        return $module;
    }
    
    // Simular condición para usuario tipo 3 (cajeros)
    if ($usuario == 3 or $usuario == 10 or $usuario == 4) {
        if($enlaces == "ingresar" || $enlaces == "pagina1" || $enlaces == "consultar" || $enlaces == "editar" || $enlaces == "salir" || $enlaces == "editarp" || $enlaces == "buscar" || $enlaces == "inde" || $enlaces == "usuarios" || $enlaces == "pdf" || $enlaces == "buscarProducto" || $enlaces == "facturacionRapida" || $enlaces == "diagnostico") {
            $module = "views/modules/".$enlaces.".php";
            echo "<p style='color: green;'>✅ Coincide con condición de cajero (usuario 3)</p>";
            echo "<p><strong>Módulo asignado:</strong> $module</p>";
            return $module;
        }
    }
    
    // Simular condición para usuario tipo 1 (administrador)
    if ($usuario == 1) {
        if($enlaces == "ingresar" || $enlaces == "pagina1" || $enlaces == "consultar" || $enlaces == "editar" || $enlaces == "salir" || $enlaces == "editarp" || $enlaces == "buscar" || $enlaces == "inde" || $enlaces == "usuarios" || $enlaces == "pdf" || $enlaces == "pdf1" || $enlaces == "buscarProducto" || $enlaces == "facturacionRapida" || $enlaces == "diagnostico") {
            $module = "views/modules/".$enlaces.".php";
            echo "<p style='color: green;'>✅ Coincide con condición de administrador (usuario 1)</p>";
            echo "<p><strong>Módulo asignado:</strong> $module</p>";
            return $module;
        }
    }
    
    echo "<p style='color: red;'>❌ No coincide con ninguna condición</p>";
    return null;
}

// Test con diferentes tipos de usuario
$enlaces_test = "facturacionRapida";

echo "<div style='background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
echo "<h4>Test 1: Usuario Administrador (tipo 1)</h4>";
$resultado1 = simularEnlaces($enlaces_test, 1);
echo "</div>";

echo "<div style='background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
echo "<h4>Test 2: Usuario Cajero (tipo 3)</h4>";
$resultado2 = simularEnlaces($enlaces_test, 3);
echo "</div>";

// Verificar si el archivo existe
echo "<h3>📁 Verificación de Archivo:</h3>";
$archivo_esperado = "facturacionRapida.php";
if (file_exists($archivo_esperado)) {
    echo "<p style='color: green;'>✅ El archivo $archivo_esperado existe</p>";
    
    // Intentar incluir el archivo
    echo "<h4>🔧 Test de Inclusión:</h4>";
    try {
        ob_start();
        include $archivo_esperado;
        $contenido = ob_get_clean();
        
        if (strlen($contenido) > 0) {
            echo "<p style='color: green;'>✅ El archivo se incluye correctamente</p>";
            echo "<p><strong>Tamaño del contenido:</strong> " . strlen($contenido) . " caracteres</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ El archivo se incluye pero no genera contenido visible</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error al incluir el archivo: " . $e->getMessage() . "</p>";
    }
    
} else {
    echo "<p style='color: red;'>❌ El archivo $archivo_esperado NO existe</p>";
}

// Test de URL real
echo "<h3>🌐 Test de URLs Reales:</h3>";
echo "<p>Prueba estas URLs después de iniciar sesión:</p>";

$base_url = "https://" . $_SERVER['HTTP_HOST'];
$urls_test = [
    "$base_url/index.php?action=facturacionRapida" => "Ruta con index.php",
    "$base_url/facturacionRapida" => "Ruta amigable",
    "$base_url/views/modules/facturacionRapida.php" => "Archivo directo"
];

foreach ($urls_test as $url => $descripcion) {
    echo "<p><a href='$url' target='_blank' style='color: #007bff;'>🔗 $descripcion</a></p>";
}

// Información de debug
echo "<h3>🐛 Información de Debug:</h3>";
echo "<div style='background: #e9ecef; padding: 10px; border-radius: 5px; font-family: monospace;'>";
echo "<p><strong>Sesión actual:</strong></p>";
echo "<pre>" . print_r($_SESSION, true) . "</pre>";
echo "<p><strong>Directorio actual:</strong> " . __DIR__ . "</p>";
echo "<p><strong>Archivo actual:</strong> " . __FILE__ . "</p>";
echo "</div>";

?>

<h3>📋 Próximos Pasos:</h3>
<div style="background: #d4edda; padding: 15px; border-radius: 5px;">
    <ol>
        <li>Verifica que tengas una sesión válida iniciada</li>
        <li>Prueba las URLs de arriba en orden</li>
        <li>Si la ruta amigable no funciona, usa la ruta con index.php</li>
        <li>Si nada funciona, usa el archivo directo</li>
    </ol>
</div>

<br>
<a href="../../index.php?action=mesa" style="background: #6c757d; color: white; padding: 10px; text-decoration: none; border-radius: 5px;">🔙 Volver a Mesas</a>

</body>
</html>
