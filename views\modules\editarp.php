<?php ob_start();
#session_start();


if(!$_SESSION["validar"]){

	header("location:ingresar");

	exit();

}

?>

<h1>EDITAR PRODUCTO</h1>


<form method="post">
	
	<?php

	//$idp=$_GET['id'];

	$editarProducto = new MvcController();
	$respuesta=$editarProducto -> editarProductoController();
	$editarProducto -> actualizarProductoController();

	if ($respuesta=="vacio") {
		echo "Vacio no hay resultados";
	}else{ echo "Si hay resultados";}


	#echo $result;
	echo'<input type="hidden" value="'.$respuesta["id"].'" name="idEditar">
						 
						 <input type="text" value="'.$respuesta["nombre"].'" name="nombreEditar" required>
						 <input type="text" value="'.$respuesta["descripcion"].'" name="descripcionEditar" required>
						 
						 <input type="submit" value="Actualizar">';

	ob_flush();
	?>


</form>



