<?php
session_start();
require_once "../../models/conexion.php";

// Verificar si se recibió mesa por GET
$mesaId = $_GET['mesa'] ?? 1;

echo "<!DOCTYPE html>";
echo "<html><head><title>Verificar Mesa en Ventas</title></head><body>";
echo "<h2>🔍 Verificar Mesa en Tabla Ventas</h2>";
echo "<p><strong>Mesa ID a verificar:</strong> $mesaId</p>";

try {
    $db = Conexion::conectar();
    
    // Verificar últimas ventas de esta mesa
    $sql = "SELECT v.*, p.numero_pedido, p.fecha_pedido 
            FROM ventas v 
            LEFT JOIN pedidos p ON v.pedidos_id = p.id 
            WHERE v.mesa = ? 
            ORDER BY v.fecha_venta DESC 
            LIMIT 10";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([$mesaId]);
    $ventas = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>📊 Últimas Ventas de Mesa $mesaId:</h3>";
    
    if (count($ventas) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th>ID Venta</th><th>Mesa</th><th>Pedido</th><th>Total</th><th>Fecha</th><th>Efectivo</th><th>Tarjeta</th><th>Otros</th>";
        echo "</tr>";
        
        foreach ($ventas as $venta) {
            $otros = $venta['bancolombia'] + $venta['nequi'] + $venta['daviplata'];
            echo "<tr>";
            echo "<td>" . $venta['id'] . "</td>";
            echo "<td style='font-weight: bold; color: " . ($venta['mesa'] == $mesaId ? 'green' : 'red') . ";'>" . $venta['mesa'] . "</td>";
            echo "<td>" . ($venta['numero_pedido'] ?? 'N/A') . "</td>";
            echo "<td>$" . number_format($venta['total']) . "</td>";
            echo "<td>" . $venta['fecha_venta'] . "</td>";
            echo "<td>$" . number_format($venta['efectivo']) . "</td>";
            echo "<td>$" . number_format($venta['valortarjeta']) . "</td>";
            echo "<td>$" . number_format($otros) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Verificar si hay ventas con mesa incorrecta
        $ventasIncorrectas = array_filter($ventas, function($v) use ($mesaId) {
            return $v['mesa'] != $mesaId;
        });
        
        if (count($ventasIncorrectas) > 0) {
            echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin-top: 10px;'>";
            echo "<strong>⚠️ PROBLEMA DETECTADO:</strong> Hay " . count($ventasIncorrectas) . " ventas con mesa incorrecta.";
            echo "</div>";
        } else {
            echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin-top: 10px;'>";
            echo "<strong>✅ CORRECTO:</strong> Todas las ventas tienen la mesa correcta ($mesaId).";
            echo "</div>";
        }
        
    } else {
        echo "<p style='color: orange;'>⚠️ No hay ventas registradas para la mesa $mesaId</p>";
    }
    
    // Verificar todas las mesas con ventas recientes
    echo "<h3>📋 Resumen de Ventas por Mesa (Últimas 24 horas):</h3>";
    
    $sqlResumen = "SELECT mesa, COUNT(*) as total_ventas, SUM(total) as total_dinero, MAX(fecha_venta) as ultima_venta
                   FROM ventas 
                   WHERE fecha_venta >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                   GROUP BY mesa 
                   ORDER BY mesa";
    
    $stmtResumen = $db->prepare($sqlResumen);
    $stmtResumen->execute();
    $resumen = $stmtResumen->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($resumen) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th>Mesa</th><th>Total Ventas</th><th>Total Dinero</th><th>Última Venta</th>";
        echo "</tr>";
        
        foreach ($resumen as $mesa) {
            $esLaMesa = $mesa['mesa'] == $mesaId;
            echo "<tr style='" . ($esLaMesa ? 'background: #d4edda;' : '') . "'>";
            echo "<td style='font-weight: bold;'>" . $mesa['mesa'] . ($esLaMesa ? ' ← ESTA MESA' : '') . "</td>";
            echo "<td>" . $mesa['total_ventas'] . "</td>";
            echo "<td>$" . number_format($mesa['total_dinero']) . "</td>";
            echo "<td>" . $mesa['ultima_venta'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No hay ventas en las últimas 24 horas</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}

?>

<h3>🧪 Test de Facturación</h3>
<p>Haz una facturación de prueba para verificar que la mesa se registre correctamente:</p>
<a href="test_mesa_correcta.php?mesa=<?=$mesaId?>" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
    🚀 Test Facturación Mesa <?=$mesaId?>
</a>

<br><br>
<a href="index.php?action=registroPmesa&ida=<?=$mesaId?>" style="background: #007bff; color: white; padding: 10px; text-decoration: none; border-radius: 5px;">🔙 Volver a Mesa <?=$mesaId?></a>

</body>
</html>
