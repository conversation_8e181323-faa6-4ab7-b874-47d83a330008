<?php
	$registro = new controllerCiudadProveedor();
	$ciudad =$registro -> listaCiudadesController();
	$proveedores =$registro -> listaProveedoresController();
	$registro -> registroCiudadProveedorController();
	if(isset($_GET["action"]))
		{	if($_GET["action"] == "okCPv")
				{	echo "Registro Exitoso";	}
		}
?>


<h1>REGISTRO DE CIUDAD PROVEEDOR</h1>
<form method="post">
	<?php 

# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  ciudad  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
# 
	echo "<label>CIUDAD :</label>";
	if($ciudad=="error")
		{	echo "debe registrar el Ciudad"; }
	else{
			$resultt=' <select name="ciudad"  id="ciudad">';
			$resultt.=' <option value="-1">Seleccione una Ciudad</option>';
			foreach ($ciudad as $row => $item)
			 	{	$resultt.=' <option value="'.$item["id"].'">'.$item["nombre"].'</option>';	}	
			 $resultt.='</select>';
			 echo $resultt." <br>";
		}
# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  End ciudad  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
# 
# 	# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  proveedores  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
 	echo "<label>PROVEEDOR : 	 </label>";
	if($proveedores=="error")
		{	echo "debe registrar el proveedores"; }
	else{			
			$result=' <select name="proveedores"  id="proveedores">';
			$result.=' <option value="-1">Proveedores</option>';
			foreach ($proveedores as $row => $item)
			 	{	$result.=' <option value="'.$item["id"].'">'.$item["nombre"].'</option>';	}	
			 $result.='</select>';
			 echo $result." <br>";
		}
# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  End proveedores  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
 ?>
	<!--input type="text" placeholder="idDepartamento" name="idDepartamentoCiudadRegistro" required-->

	<label> Nombre de la sucursal : </label>
	<input type="text" placeholder="nombre de la sucursal" name="nombreRegistro" required><br>	
	
	<input type="submit" value="Enviar">

	<TABLE>
		<tr>
			<td>
				
			</td>

			<td>
				
			</td>
		</tr>
		<tr>
			<td>
				
			</td>

			<td>
				
			</td>
		</tr>
		<tr>
			<td>
				
			</td>

			<td>
				
			</td>
		</tr>
	</TABLE>

</form>


