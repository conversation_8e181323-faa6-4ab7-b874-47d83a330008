<!DOCTYPE html>
<html>
<head>
    <title>Test AJAX Productos</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .result { background: white; padding: 10px; border: 1px solid #ccc; border-radius: 3px; margin: 10px 0; }
        input[type="text"] { padding: 10px; width: 300px; border: 1px solid #ccc; border-radius: 3px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .producto-sugerencia { padding: 10px; border-bottom: 1px solid #eee; cursor: pointer; }
        .producto-sugerencia:hover { background: #f8f9fa; }
    </style>
</head>
<body>

<div class="container">
    <h1>🧪 Test AJAX - Búsqueda de Productos</h1>
    
    <div class="test-section">
        <h3>🔍 Test de Búsqueda</h3>
        <p>Escribe para buscar productos en tiempo real:</p>
        
        <input type="text" id="buscar_producto" placeholder="Escribe el nombre del producto..." onkeyup="buscarProducto()">
        <button onclick="testManual()">Buscar Manual</button>
        
        <div id="lista_productos" class="result" style="min-height: 100px;">
            Los resultados aparecerán aquí...
        </div>
    </div>
    
    <div class="test-section">
        <h3>📊 Tests Automáticos</h3>
        <button onclick="testConexion()">Test Conexión</button>
        <button onclick="testProductos()">Test Productos DB</button>
        <button onclick="testBusquedaA()">Test Búsqueda "a"</button>
        <button onclick="testBusquedaComida()">Test Búsqueda "comida"</button>
        
        <div id="resultados_auto" class="result">
            Los resultados de tests automáticos aparecerán aquí...
        </div>
    </div>
    
    <div class="test-section">
        <h3>🔧 Debug Info</h3>
        <div id="debug_info" class="result">
            <p><strong>URL Base:</strong> <?php echo $_SERVER['HTTP_HOST']; ?></p>
            <p><strong>Directorio:</strong> <?php echo __DIR__; ?></p>
            <p><strong>Archivo AJAX:</strong> ajaxBuscarProductoRapido.php</p>
        </div>
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
// Función de búsqueda automática (como en facturación rápida)
function buscarProducto() {
    const termino = document.getElementById('buscar_producto').value;
    
    if (termino.length < 2) {
        document.getElementById('lista_productos').innerHTML = 'Escribe al menos 2 caracteres...';
        return;
    }
    
    document.getElementById('lista_productos').innerHTML = '⏳ Buscando...';
    
    $.ajax({
        url: 'ajaxBuscarProductoRapido.php',
        type: 'POST',
        data: { termino: termino },
        success: function(response) {
            console.log('✅ Respuesta AJAX:', response);
            document.getElementById('lista_productos').innerHTML = response;
        },
        error: function(xhr, status, error) {
            console.error('❌ Error AJAX:', error, 'Status:', status, 'Response:', xhr.responseText);
            document.getElementById('lista_productos').innerHTML = 
                '<div style="color: red;">❌ Error AJAX:<br>Status: ' + status + '<br>Error: ' + error + '<br>Response: ' + xhr.responseText + '</div>';
        }
    });
}

// Test manual
function testManual() {
    const termino = document.getElementById('buscar_producto').value || 'test';
    document.getElementById('buscar_producto').value = termino;
    buscarProducto();
}

// Test de conexión
function testConexion() {
    document.getElementById('resultados_auto').innerHTML = '⏳ Probando conexión...';
    
    $.ajax({
        url: 'debug_productos_ajax.php',
        type: 'GET',
        success: function(response) {
            document.getElementById('resultados_auto').innerHTML = '✅ Conexión OK - Ver debug completo en nueva ventana';
            window.open('debug_productos_ajax.php', '_blank');
        },
        error: function(xhr, status, error) {
            document.getElementById('resultados_auto').innerHTML = '❌ Error de conexión: ' + error;
        }
    });
}

// Test de productos en DB
function testProductos() {
    document.getElementById('resultados_auto').innerHTML = '⏳ Verificando productos en DB...';
    
    $.ajax({
        url: 'ajaxBuscarProductoRapido.php',
        type: 'POST',
        data: { termino: '' },
        success: function(response) {
            document.getElementById('resultados_auto').innerHTML = '✅ Respuesta del servidor:<br>' + response;
        },
        error: function(xhr, status, error) {
            document.getElementById('resultados_auto').innerHTML = '❌ Error: ' + error;
        }
    });
}

// Test búsqueda específica
function testBusquedaA() {
    testBusquedaEspecifica('a');
}

function testBusquedaComida() {
    testBusquedaEspecifica('comida');
}

function testBusquedaEspecifica(termino) {
    document.getElementById('resultados_auto').innerHTML = '⏳ Buscando "' + termino + '"...';
    
    $.ajax({
        url: 'ajaxBuscarProductoRapido.php',
        type: 'POST',
        data: { termino: termino },
        success: function(response) {
            document.getElementById('resultados_auto').innerHTML = 
                '✅ Búsqueda de "' + termino + '":<br><div style="border: 1px solid #ccc; padding: 10px; background: white;">' + response + '</div>';
        },
        error: function(xhr, status, error) {
            document.getElementById('resultados_auto').innerHTML = '❌ Error buscando "' + termino + '": ' + error;
        }
    });
}

// Función para seleccionar producto (como en facturación rápida)
function seleccionarProducto(id, nombre, precio) {
    alert('Producto seleccionado:\nID: ' + id + '\nNombre: ' + nombre + '\nPrecio: $' + precio);
}

// Test automático al cargar
document.addEventListener('DOMContentLoaded', function() {
    console.log('🧪 Test AJAX Productos cargado');
    
    // Test automático después de 2 segundos
    setTimeout(function() {
        document.getElementById('buscar_producto').value = 'a';
        buscarProducto();
    }, 2000);
});
</script>

<br>
<a href="../../index.php?action=facturacionRapida" style="background: #28a745; color: white; padding: 10px; text-decoration: none; border-radius: 5px;">🚀 Ir a Facturación Rápida</a>
<a href="debug_productos_ajax.php" style="background: #ffc107; color: black; padding: 10px; text-decoration: none; border-radius: 5px;">🔧 Debug Completo</a>

</body>
</html>
