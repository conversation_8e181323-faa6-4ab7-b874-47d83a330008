<?php

session_start();

if(!isset($_SESSION["validar"]) || !$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "models/crud.php";
require_once "controllers/controller.php";

// Log para debug
error_log("cancelarMesa: Iniciando proceso de cancelación via ruta amigable");

try {
    $ajax = new MvcController();
    $r = $ajax->cancelarConPedidosController();

    if ($r == "sucess") {
        error_log("cancelarMesa: Cancelación exitosa");
        echo '<script>
            alert("✅ Mesa cancelada exitosamente. Todos los pedidos han sido cancelados.");
            location.href="index.php?action=mesa";
        </script>';
        echo "<h1>✅ Mesa cancelada exitosamente</h1>";
        echo "<p>Todos los pedidos han sido cancelados y la mesa está lista para nuevos pedidos.</p>";
    } else {
        error_log("cancelarMesa: Error en cancelación");
        echo '<script>
            alert("❌ Error al cancelar la mesa. Intente nuevamente.");
            location.href="index.php?action=mesa";
        </script>';
        echo "<h1>❌ Error al cancelar</h1>";
        echo "<p>No se pudo cancelar la mesa. Verifique los logs del servidor.</p>";
    }
} catch (Exception $e) {
    error_log("cancelarMesa: Excepción - " . $e->getMessage());
    echo '<script>
        alert("❌ Error interno del servidor.");
        location.href="index.php?action=mesa";
    </script>';
    echo "<h1>❌ Error interno</h1>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

?>
