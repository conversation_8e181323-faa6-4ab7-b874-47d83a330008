<?php
require_once "../../models/crud.php";
require_once "../../models/crudFacturaCompraSuministro.php";
require_once "../../controllers/controller.php";
require_once "../../controllers/controllerFacturaCompraSuministro.php";
	ini_set("session.cookie_lifetime","28800");
	ini_set("session.gc_maxlifetime","28800");
//echo "<script>alert('Entro al ajax Suministro ".$_POST['compra']."');</script>";
 if(isset($_POST["compra"]) and $_POST['compra']>0)  // !(empty($var))
	{
		//echo "<script>alert('Si entro al ajax factura if');</script>";
		$queryString = $_POST["compra"];
		//echo $queryString;
		$ajax=new controllerFacturaCompraSuministro();
		$ajax->actualizarFacturaSumistroController($queryString);
		//echo "<script>alert('entro ajax ultimo pro')</script>";

	}