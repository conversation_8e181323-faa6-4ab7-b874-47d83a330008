<style type="text/css">
	.rojo{color:red;}
</style>
<?php
$inventario = new controllerVistaVentas();
//$inventario -> reporteExcelController();
			$bodegas=$inventario->inventarioBodegaController();	
			$cafeterias=$inventario->inventarioCafeteriaController();
			$cocinas=$inventario->inventarioCocinaController();

		if ($_SESSION["tipo_usuario"]==1)
		 {						
			$bodega= '<h3>Inventario Bodega</h3><table border="1">
				<thead>
					<tr> 
						<td><b>Suministro</b></td>
						<td><b>Cantidad</b></td>
						<td><b>Precio</b></td>
						<td><b>Subtotal</b></td>
					</tr>
				</thead>';
				$suma_bodega=0;
			foreach($bodegas as $row => $item){
			  if ($item["cantidad"]>0) 
				{
					$suma_bodega+=$item["subtotal"];
					$bodega.='<tr>
							<td>'.$item["nombre"].'</td>						
							<td>'.$item["cantidad"].'</td>						
							<td>'.$item["precio"].'</td>						
							<td>'.round($item["subtotal"],2).'</td>					
						</tr>';	
				}
				
			}
			//<td><h4><b>'.money_format('%(#1n', $suma_bodega).'</b></h4></td>
			setlocale(LC_MONETARY, 'en_US');
			$bodega.=  '<tr class="filaTotal">			
							<td colspan="3"><h4><b>Valor Inventario Bodega</b></h4></td>						
												
							<td><h4><b>'.$suma_bodega.'</b></h4></td>					
						</tr>
					</table>';
		 }

			$cafeteria= '<br><br><h3>Inventario Tienda</h3><table border="1">
					<thead>
					<tr> 
						<td><b>Suministro</b></td>
						<td><b>Cantidad</b></td>
						<td><b>Precio</b></td>
						<td><b>Subtotal</b></td>
					</tr>
					</thead>';
				$suma_cafeteria=0;
			foreach($cafeterias as $row => $item){
				$suma_cafeteria+=$item["subtotal"];
			 if ($item["cantidad"]!=0)
			  {
				if ($item["cantidad"]<0) {$class='rojo';}else{$class='';}
				$cafeteria.='<tr>
							<td>'.$item["nombre"].'</td>						
							<td class="'.$class.'">'.$item["cantidad"].'</td>						
							<td class="'.$class.'">'.$item["precio"].'</td>						
							<td class="'.$class.'">'.round($item["subtotal"],2).'</td>					
						</tr>';	
			  }
			}
			//<td><h4><b>'.money_format('%(#1n', $suma_cafeteria).'</b></h4></td>
			setlocale(LC_MONETARY, 'en_US');
			$cafeteria.=  '<tr class="filaTotal">													
							<td colspan="3"><h4><b>Valor Inventario Tienda</b></h4></td>							
							<td><h4><b>'.$suma_cafeteria.'</b></h4></td>					
						</tr>
					</table>';
	if ($_SESSION["tipo_usuario"]==1)
	 {	$supertotal=$suma_bodega+$suma_cafeteria;}
	else {$supertotal=$suma_cafeteria;}
	//<h1>INVENTARIO TOTAL (<?=money_format('%(#1n', $supertotal);?)</h1>
?>
<h1>INVENTARIO TOTAL (<?=$supertotal;?>)</h1>
<?php
	echo $bodega;
	echo $cafeteria;	
	if(isset($_GET["action"]))
		{	if($_GET["action"] == "cambio")
				{	echo "Cambio Exitoso";	}
		}
?>