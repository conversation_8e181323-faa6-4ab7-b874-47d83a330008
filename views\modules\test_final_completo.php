<?php
// Test final completo del sistema
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
if (!isset($_SESSION["usuario"])) {
    $_SESSION["usuario"] = 1;
    $_SESSION["tipo_usuario"] = 1;
    $_SESSION["perfil"] = "administrador";
}

require_once "../../models/conexion.php";
require_once "../../controllers/controllerEstadoPedidos.php";
require_once "../../models/crudEstadoPedidos.php";
require_once "../../models/crudPedidoMesaVendido.php";

$mesaId = isset($_GET['mesa']) ? $_GET['mesa'] : 1;

echo "<h1>🔍 Test Final Completo - Mesa $mesaId</h1>";

try {
    $controller = new ControllerEstadoPedidos();
    $db = Conexion::conectar();
    
    echo "<h3>1. 📋 Productos Visibles en la Mesa (Solo Borradores)</h3>";
    
    // Usar la misma función que usa la mesa
    $productosVista = DatosPedidoMesaVendido::vistaPmesaModel($mesaId, "producto_vendido_mesa");
    
    if (!empty($productosVista)) {
        echo "<div class='alert alert-info'>✅ Mesa muestra " . count($productosVista) . " producto(s)</div>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Producto</th><th>Cantidad</th><th>Estado Pedido</th><th>Número Pedido</th></tr>";
        
        foreach ($productosVista as $producto) {
            $color = ($producto['estado'] == 'borrador') ? '#d4edda' : '#f8d7da';
            echo "<tr style='background-color: $color;'>";
            echo "<td>{$producto['nombrepr']}</td>";
            echo "<td>{$producto['cantidadpvm']}</td>";
            echo "<td>{$producto['estado']}</td>";
            echo "<td>{$producto['numero_pedido']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Verificar que todos sean borrador
        $noBorador = array_filter($productosVista, function($p) { return $p['estado'] !== 'borrador'; });
        if (empty($noBorador)) {
            echo "<div class='alert alert-success'>✅ CORRECTO: Todos los productos mostrados son de pedidos borrador</div>";
        } else {
            echo "<div class='alert alert-danger'>❌ ERROR: Hay productos de pedidos no borrador en la vista</div>";
        }
    } else {
        echo "<div class='alert alert-success'>✅ Mesa vacía - Lista para nuevos pedidos</div>";
    }
    
    echo "<h3>2. 📊 Todos los Pedidos de la Mesa (Para Diagnóstico)</h3>";
    
    // Mostrar todos los pedidos para diagnóstico
    $sql = "SELECT * FROM pedidos WHERE mesa_id = ? AND estado NOT IN ('facturado', 'cancelado') ORDER BY fecha_pedido DESC";
    $stmt = $db->prepare($sql);
    $stmt->bindParam(1, $mesaId, PDO::PARAM_INT);
    $stmt->execute();
    $todosPedidos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($todosPedidos)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Número</th><th>Estado</th><th>Fecha</th><th>Productos</th></tr>";
        
        foreach ($todosPedidos as $pedido) {
            // Contar productos
            $sqlProductos = "SELECT COUNT(*) as total FROM producto_vendido_mesa WHERE pedidos_id = ?";
            $stmtProductos = $db->prepare($sqlProductos);
            $stmtProductos->bindParam(1, $pedido['id'], PDO::PARAM_INT);
            $stmtProductos->execute();
            $totalProductos = $stmtProductos->fetch(PDO::FETCH_ASSOC)['total'];
            
            $color = '';
            if ($pedido['estado'] == 'borrador') $color = '#fff3cd';
            if ($pedido['estado'] == 'enviado') $color = '#d4edda';
            if ($pedido['estado'] == 'entregado') $color = '#cce5ff';
            
            echo "<tr style='background-color: $color;'>";
            echo "<td>{$pedido['id']}</td>";
            echo "<td>{$pedido['numero_pedido']}</td>";
            echo "<td>{$pedido['estado']}</td>";
            echo "<td>{$pedido['fecha_pedido']}</td>";
            echo "<td>$totalProductos</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<small>";
        echo "🟡 Amarillo = Borrador (visible en mesa)<br>";
        echo "🟢 Verde = Enviado (oculto en mesa, disponible para facturar)<br>";
        echo "🔵 Azul = Entregado (oculto en mesa, listo para facturar)<br>";
        echo "</small>";
    } else {
        echo "<p>No hay pedidos activos.</p>";
    }
    
    echo "<h3>3. 💰 Pedidos Disponibles para Facturar</h3>";
    
    // Verificar pedidos para facturar
    $sqlFacturar = "SELECT * FROM pedidos WHERE mesa_id = ? AND estado IN ('enviado', 'entregado') ORDER BY fecha_pedido DESC";
    $stmtFacturar = $db->prepare($sqlFacturar);
    $stmtFacturar->bindParam(1, $mesaId, PDO::PARAM_INT);
    $stmtFacturar->execute();
    $pedidosFacturar = $stmtFacturar->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($pedidosFacturar)) {
        echo "<div class='alert alert-info'>✅ Hay " . count($pedidosFacturar) . " pedido(s) disponible(s) para facturar</div>";
        foreach ($pedidosFacturar as $p) {
            echo "<p>📋 {$p['numero_pedido']} - Estado: {$p['estado']}</p>";
        }
    } else {
        echo "<div class='alert alert-warning'>⚠️ No hay pedidos disponibles para facturar</div>";
    }
    
    echo "<h3>4. ✅ Resumen del Estado</h3>";
    
    $borradores = array_filter($todosPedidos, function($p) { return $p['estado'] == 'borrador'; });
    $enviados = array_filter($todosPedidos, function($p) { return $p['estado'] == 'enviado'; });
    $entregados = array_filter($todosPedidos, function($p) { return $p['estado'] == 'entregado'; });
    
    echo "<ul>";
    echo "<li><strong>Pedidos Borrador:</strong> " . count($borradores) . " (visibles en mesa)</li>";
    echo "<li><strong>Pedidos Enviados:</strong> " . count($enviados) . " (ocultos en mesa, disponibles para facturar)</li>";
    echo "<li><strong>Pedidos Entregados:</strong> " . count($entregados) . " (ocultos en mesa, listos para facturar)</li>";
    echo "</ul>";
    
    if (count($productosVista) == 0 && (count($enviados) > 0 || count($entregados) > 0)) {
        echo "<div class='alert alert-success'>";
        echo "<h4>✅ SISTEMA FUNCIONANDO CORRECTAMENTE</h4>";
        echo "<p>La mesa está 'desocupada' (no muestra productos) pero hay pedidos enviados/entregados disponibles para facturar.</p>";
        echo "</div>";
    } elseif (count($productosVista) > 0 && count($borradores) > 0) {
        echo "<div class='alert alert-info'>";
        echo "<h4>📝 Mesa con Pedido Activo</h4>";
        echo "<p>Hay productos en borrador visibles en la mesa (comportamiento normal).</p>";
        echo "</div>";
    } elseif (count($productosVista) == 0 && count($borradores) == 0 && count($enviados) == 0 && count($entregados) == 0) {
        echo "<div class='alert alert-success'>";
        echo "<h4>✅ Mesa Completamente Libre</h4>";
        echo "<p>No hay pedidos activos. Mesa lista para nuevos pedidos.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}

echo "<br><a href='index.php?action=registroPmesa&ida=$mesaId' style='background: #007bff; color: white; padding: 10px; text-decoration: none;'>🔙 Ir a Mesa $mesaId</a>";
echo "<br><a href='test_final_completo.php?mesa=$mesaId' style='background: #6c757d; color: white; padding: 10px; text-decoration: none; margin: 5px;'>🔄 Actualizar</a>";
?>

<style>
.alert {
    padding: 10px;
    margin: 10px 0;
    border-radius: 5px;
}
.alert-success { background-color: #d4edda; border: 1px solid #c3e6cb; }
.alert-info { background-color: #d1ecf1; border: 1px solid #bee5eb; }
.alert-warning { background-color: #fff3cd; border: 1px solid #ffeaa7; }
.alert-danger { background-color: #f8d7da; border: 1px solid #f5c6cb; }
</style>
