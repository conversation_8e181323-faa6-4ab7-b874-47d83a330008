<?php
// Test específico para el PDF de turno
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
if (!isset($_SESSION["usuario"])) {
    $_SESSION["usuario"] = 15;
    $_SESSION["tipo_usuario"] = 1;
    $_SESSION["perfil"] = "administrador";
}

require_once "../../models/conexion.php";

echo "<h1>📄 Test PDF de Turno</h1>";
echo "<p>Verificando y simulando datos para el PDF de cierre de turno</p>";

try {
    $db = Conexion::conectar();
    $usuario = $_SESSION["usuario"];
    
    // Buscar el último turno del usuario
    $sqlUltimoTurno = "SELECT t.id, t.persona_id, t.fecha_inicio, t.fecha_final, t.cantidad_inicio, t.cantidad_final,
                              p.nombre, p.apellidos
                       FROM turnos_cajeros t
                       LEFT JOIN personas p ON t.persona_id = p.id
                       WHERE t.persona_id = ?
                       ORDER BY t.id DESC LIMIT 1";
    $stmtTurno = $db->prepare($sqlUltimoTurno);
    $stmtTurno->execute([$usuario]);
    $ultimoTurno = $stmtTurno->fetch(PDO::FETCH_ASSOC);
    
    if ($ultimoTurno) {
        echo "<h3>1. 📋 Último Turno Encontrado</h3>";
        echo "<div class='alert alert-info'>";
        echo "<p><strong>ID:</strong> {$ultimoTurno['id']}</p>";
        echo "<p><strong>Usuario:</strong> {$ultimoTurno['nombre']} {$ultimoTurno['apellidos']}</p>";
        echo "<p><strong>Inicio:</strong> {$ultimoTurno['fecha_inicio']}</p>";
        echo "<p><strong>Estado:</strong> " . (($ultimoTurno['fecha_final'] == '0000-00-00 00:00:00') ? 'Activo' : 'Cerrado') . "</p>";
        echo "</div>";
        
        $turnoId = $ultimoTurno['id'];
        
        // Obtener ventas del turno
        $sqlVentas = "SELECT v.id, v.fecha_venta, v.total, v.efectivo, v.valortarjeta, v.bancolombia, v.nequi, v.daviplata, v.mesa
                      FROM ventas v 
                      WHERE v.turno_cajero_id = ? 
                      ORDER BY v.fecha_venta ASC";
        $stmtVentas = $db->prepare($sqlVentas);
        $stmtVentas->execute([$turnoId]);
        $ventas = $stmtVentas->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>2. 💰 Configurando Variables de Sesión</h3>";
        
        // Configurar variables de sesión necesarias para el PDF
        $_SESSION["inicio"] = $ultimoTurno['fecha_inicio'];
        $_SESSION["turno"] = $turnoId;
        
        if (!empty($ventas)) {
            $totalVentas = 0;
            $totalEfectivo = 0;
            $reporteTurno = array();
            
            foreach ($ventas as $index => $venta) {
                $totalVentas += $venta['total'];
                $totalEfectivo += $venta['efectivo'];
                
                $reporteTurno[$index] = array(
                    'vfecha' => $venta['fecha_venta'],
                    'vid' => $venta['id'],
                    'vtotal' => $venta['total']
                );
            }
            
            $_SESSION["turnoV"] = $reporteTurno;
            $_SESSION["ventas"] = $totalVentas;
            $_SESSION["efectivo_turno"] = $totalEfectivo;
            $_SESSION["ingreso"] = 0; // Otros ingresos
            $_SESSION["Egreso"] = 0;  // Egresos
            $_SESSION["propina"] = 0; // Propinas
            
            echo "<div class='alert alert-success'>";
            echo "<h4>✅ Variables Configuradas</h4>";
            echo "<p><strong>Ventas encontradas:</strong> " . count($ventas) . "</p>";
            echo "<p><strong>Total ventas:</strong> $" . number_format($totalVentas) . "</p>";
            echo "<p><strong>Total efectivo:</strong> $" . number_format($totalEfectivo) . "</p>";
            echo "</div>";
            
            echo "<h3>3. 📊 Detalle de Ventas</h3>";
            echo "<table class='table table-striped'>";
            echo "<thead><tr><th>ID</th><th>Fecha</th><th>Mesa</th><th>Total</th><th>Efectivo</th></tr></thead>";
            echo "<tbody>";
            
            foreach ($ventas as $venta) {
                echo "<tr>";
                echo "<td>{$venta['id']}</td>";
                echo "<td>{$venta['fecha_venta']}</td>";
                echo "<td>{$venta['mesa']}</td>";
                echo "<td>$" . number_format($venta['total']) . "</td>";
                echo "<td>$" . number_format($venta['efectivo']) . "</td>";
                echo "</tr>";
            }
            
            echo "</tbody></table>";
            
        } else {
            echo "<div class='alert alert-warning'>";
            echo "<h4>⚠️ No hay ventas en este turno</h4>";
            echo "<p>Configurando variables con valores por defecto</p>";
            echo "</div>";
            
            // Configurar variables por defecto
            $_SESSION["turnoV"] = array();
            $_SESSION["ventas"] = 0;
            $_SESSION["efectivo_turno"] = 0;
            $_SESSION["ingreso"] = 0;
            $_SESSION["Egreso"] = 0;
            $_SESSION["propina"] = 0;
        }
        
        echo "<h3>4. 🧪 Test del PDF</h3>";
        echo "<div class='text-center'>";
        echo "<a href='pdfTurno' target='_blank' class='btn btn-primary btn-lg'>📄 Abrir PDF de Turno</a>";
        echo "<p class='mt-2'><small>El PDF se abrirá en una nueva ventana con los datos configurados</small></p>";
        echo "</div>";
        
        echo "<h3>5. 🔍 Variables de Sesión Configuradas</h3>";
        echo "<div class='alert alert-info'>";
        echo "<pre>";
        echo "SESSION['inicio']: " . $_SESSION["inicio"] . "\n";
        echo "SESSION['turno']: " . $_SESSION["turno"] . "\n";
        echo "SESSION['ventas']: " . $_SESSION["ventas"] . "\n";
        echo "SESSION['efectivo_turno']: " . $_SESSION["efectivo_turno"] . "\n";
        echo "SESSION['turnoV']: " . count($_SESSION["turnoV"]) . " registros\n";
        echo "</pre>";
        echo "</div>";
        
        echo "<h3>6. 🔧 Simulación de Cierre</h3>";
        echo "<div class='alert alert-warning'>";
        echo "<p><strong>Nota:</strong> Para probar el cierre completo, el sistema debería:</p>";
        echo "<ol>";
        echo "<li>Calcular el total de efectivo esperado</li>";
        echo "<li>Permitir al usuario ingresar la cantidad final</li>";
        echo "<li>Actualizar el turno con fecha_final y cantidad_final</li>";
        echo "<li>Abrir automáticamente el PDF</li>";
        echo "</ol>";
        echo "</div>";
        
    } else {
        echo "<div class='alert alert-danger'>";
        echo "<h4>❌ No se encontraron turnos</h4>";
        echo "<p>No hay turnos registrados para el usuario actual.</p>";
        echo "</div>";
        
        // Crear datos de prueba para el PDF
        echo "<h3>🧪 Creando Datos de Prueba</h3>";
        $_SESSION["inicio"] = date('Y-m-d H:i:s', strtotime('-8 hours'));
        $_SESSION["turno"] = 999;
        $_SESSION["ventas"] = 150000;
        $_SESSION["efectivo_turno"] = 100000;
        $_SESSION["ingreso"] = 0;
        $_SESSION["Egreso"] = 0;
        $_SESSION["propina"] = 5000;
        
        $_SESSION["turnoV"] = array(
            0 => array('vfecha' => date('Y-m-d H:i:s', strtotime('-2 hours')), 'vid' => 101, 'vtotal' => 75000),
            1 => array('vfecha' => date('Y-m-d H:i:s', strtotime('-1 hour')), 'vid' => 102, 'vtotal' => 75000)
        );
        
        echo "<div class='alert alert-success'>";
        echo "<p>✅ Datos de prueba creados</p>";
        echo "<a href='pdfTurno' target='_blank' class='btn btn-primary'>📄 Ver PDF con Datos de Prueba</a>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>❌ Error en el test</h4>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>

<style>
.table { margin-top: 10px; border-collapse: collapse; width: 100%; }
.table th, .table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
.table th { background-color: #f5f5f5; }
.table-striped tbody tr:nth-child(odd) { background-color: #f9f9f9; }
.alert { margin: 15px 0; padding: 15px; border-radius: 5px; }
.alert-info { background-color: #d9edf7; border-color: #bce8f1; color: #31708f; }
.alert-success { background-color: #dff0d8; border-color: #d6e9c6; color: #3c763d; }
.alert-warning { background-color: #fcf8e3; border-color: #faebcc; color: #8a6d3b; }
.alert-danger { background-color: #f2dede; border-color: #ebccd1; color: #a94442; }
.btn { padding: 10px 20px; margin: 5px; text-decoration: none; border-radius: 5px; display: inline-block; }
.btn-primary { background-color: #337ab7; color: white; }
.btn-lg { padding: 15px 30px; font-size: 16px; }
.text-center { text-align: center; }
.mt-2 { margin-top: 10px; }
pre { background-color: #f5f5f5; padding: 10px; border-radius: 3px; }
</style>
