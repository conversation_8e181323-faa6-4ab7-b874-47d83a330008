
<?php 
ob_start();
class controllerActivo extends MvcController
 {
	#REGISTRO DE Activo nombre, descripcion, fecha_creado, fecha_vencimiento, persona_id, fecha_modificacion, punto_id
	#------------------------------------
	 public function registroActivoController()
		{//echo "<script>alert('que pasa ".$_POST["cantidad"]." no')</script>";
			if(isset($_POST["nombre"]))
			 {	
				$datosController =array('nombre'=>$_POST["nombre"],
										'descripcion'=>$_POST["descripcion"],
										'fecha_vencimiento'=>$_POST["fecha_vencimiento"],
										'cantidad'=>$_POST["cantidad"],
										'persona_id'=>$_SESSION["usuario"]	);
				//echo "<script>alert('Cantidad ".$datosController['cantidad']." no')</script>";	
				/**/$respuesta = DatosActivo::registroActivoModel($datosController, "activo");
				if($respuesta == "success")
				 {	header("location:index.php?action=okAc");	}
				else
				 {	header("location:index.php");	}
			 }
		}
	#-------------------------------------
	#VISTA DE Activo
	#------------------------------------
	 public function vistaActivoController()
		{
			$respuesta = DatosActivo::vistaActivoModel("activo");
			$i=1;
			foreach($respuesta as $row => $item)
			 {
				echo'<tr>						
					<td>'.$i.'</td>																	
					<td>'.$item["nombre"].'</td>																	
					<td>'.$item["cantidad"].'</td>																	
					<td>'.$item["descripcion"].'</td>																	
					<td>'.$item["fecha_vencimiento"].'</td>	
					<td><a href="index.php?action=editarActivo&id='.$item["id"].'">Editar</a></td>
					<td><a href="index.php?action=activo&idBorrar='.$item["id"].'">Borrar</a></td>
				</tr>';
				$i++;
			 }
		}
	#------------------------------------
	#EDITAR Activo
	#------------------------------------
	 public function editarActivoController()
		{
			$datosController = $_GET["id"];
			$respuesta = DatosActivo::editarActivoModel($datosController, "activo");
			echo' Nombre
			<input type="text" value="'.$respuesta["nombre"].'" name="nombre" required>									 
				<br>cantidad
			<input type="text" value="'.$respuesta["cantidad"].'" name="cantidad" required>									 
			 	<br>descripcion
			<input type="text" value="'.$respuesta["descripcion"].'" name="descripcion" required>									 
				<br>fecha_vencimiento
			<input type="text" value="'.$respuesta["fecha_vencimiento"].'" name="fecha_vencimiento" required>									 
				<br>											 
			 <input type="hidden" value="'.$respuesta["id"].'" name="idActivoEditar" required> 	<br>			
				 <input type="submit" value="Actualizar">';
		}
	#-------------------------------------
	#ACTUALIZAR Activo
	#------------------------------------
	 public function actualizarActivoController()
		{//echo "<script>alert('Entro Controller Actualizar Activo')</script>";

		 if(isset($_POST["nombre"]))
			{
				$datosController = array(  "nombre"=>$_POST["nombre"],																
											"cantidad"=>$_POST["cantidad"],																
											"descripcion"=>$_POST["descripcion"],																
											"fecha_vencimiento"=>$_POST["fecha_vencimiento"],													
											"id"=>$_POST["idActivoEditar"]);				
				$respuesta = DatosActivo::actualizarActivoModel($datosController, "activo");
				if($respuesta == "success")
					{	header("location:index.php?action=cambioAc");	}
				else
					{	echo "error";	}
			}				
		}
	#------------------------------------
	#BORRAR Activo
	#------------------------------------
	 public function borrarActivoController()
		{
		  if(isset($_GET["idBorrar"]))
			{
				$datosController = $_GET["idBorrar"];				
				$respuesta = DatosActivo::borrarActivoModel($datosController, "activo");
				if($respuesta == "success")
					{	header("location:index.php?action=activo");	}
			}
		}			
	#---------------------------------

 }			
	