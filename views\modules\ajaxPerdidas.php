<?php
//echo "<script>alert('Entro al ajax Ventas ');</script>";
require_once "../../models/crud.php";
require_once "../../models/crudVistaVentas.php";
require_once "../../controllers/controller.php";
require_once "../../controllers/controllerVistaVentas.php";
	ini_set("session.cookie_lifetime","28800");
	ini_set("session.gc_maxlifetime","28800");
//echo "<script>alert('Hasta aqui todo bien ');</script>";

	if(isset($_POST["fecha1"]) and $_POST['fecha1']!='' and isset($_POST["fecha2"]) and $_POST['fecha2']!='')  // !(empty($var))
		{
			//echo "<script>alert('Si entro al ajax Ventas');</script>";
			$fecha1 = $_POST["fecha1"];
			$fecha2 = $_POST["fecha2"];

			//echo $queryString;
			setlocale(LC_MONETARY, 'es_CO');
			$ajax=new controllerVistaVentas();
			$resultado=$ajax->vistaPerdidasController($fecha1,$fecha2);
			//echo "<script>alert('entro ajax ultimo pro')</script>";
			//echo "<div>Dios es Amor</div>";
			echo '<table border="1">
					<tr>
						<td><b>Nombre Suministro</b></td>
						<td><b>Cantidad dada de Baja</b></td>
						<td><b>Precio </b></td>
						<td><b>Subtotal</b></td>
					</tr>';
              $perdida_total=0;
			foreach($resultado as $row => $item)
				{
					 $perdida_total+=$item["subtotal"];
					echo'<tr>
							<td>'.$item["nombre"].'</td>
							<td>'.$item["cantp"].'</td>
							<td>'.$item["precio"].'</td>
							<td><b>'.money_format('%(#1n', $item["subtotal"]).'</b></td>
						</tr>';
				}

				$perdida_total=money_format('%(#1n', $perdida_total);
			echo '<tr>
						<td></td>
						<td><b></b></td>
						<td><h3><b>Total</b></h3></td>
						<td><b>'.$perdida_total.'</b></td>
					</tr>
				</table>'	;
		}