<?php

session_start();

if(!isset($_SESSION["validar"]) || !$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "models/conexion.php";

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Debug: Error usuario_id NULL</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
</head>
<body>

<div class="container">
    <h2>🔧 Debug: Error usuario_id NULL</h2>
    <hr>
    
    <div class="alert alert-danger">
        <h4>❌ Error Detectado:</h4>
        <p><strong>SQLSTATE[23000]:</strong> Integrity constraint violation: 1048</p>
        <p><strong>Column 'usuario_id' cannot be null</strong></p>
        <p><strong>Ubicación:</strong> Inserción en tabla pedidos durante facturación</p>
    </div>
    
    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">🔍 Estructura de la Tabla pedidos</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                echo "<h5>📊 Estructura actual:</h5>";
                $stmt = Conexion::conectar()->prepare("DESCRIBE pedidos");
                $stmt->execute();
                $estructura = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                echo "<table class='table table-striped'>";
                echo "<thead>";
                echo "<tr>";
                echo "<th>Campo</th>";
                echo "<th>Tipo</th>";
                echo "<th>Null</th>";
                echo "<th>Key</th>";
                echo "<th>Default</th>";
                echo "<th>Extra</th>";
                echo "</tr>";
                echo "</thead>";
                echo "<tbody>";
                
                $campos_problematicos = [];
                foreach ($estructura as $campo) {
                    $row_class = '';
                    if ($campo['Field'] == 'usuario_id' && $campo['Null'] == 'NO') {
                        $row_class = 'danger';
                        $campos_problematicos[] = $campo['Field'];
                    } elseif (strpos($campo['Field'], 'usuario') !== false || strpos($campo['Field'], 'mesero') !== false) {
                        $row_class = 'warning';
                    }
                    
                    echo "<tr class='{$row_class}'>";
                    echo "<td><strong>{$campo['Field']}</strong></td>";
                    echo "<td>{$campo['Type']}</td>";
                    echo "<td>{$campo['Null']}</td>";
                    echo "<td>{$campo['Key']}</td>";
                    echo "<td>{$campo['Default']}</td>";
                    echo "<td>{$campo['Extra']}</td>";
                    echo "</tr>";
                }
                echo "</tbody></table>";
                
                if (count($campos_problematicos) > 0) {
                    echo "<div class='alert alert-warning'>";
                    echo "<h6>⚠️ Campos problemáticos encontrados:</h6>";
                    echo "<ul>";
                    foreach ($campos_problematicos as $campo) {
                        echo "<li><code>{$campo}</code> - No permite NULL pero no se está proporcionando valor</li>";
                    }
                    echo "</ul>";
                    echo "</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-warning">
        <div class="panel-heading">
            <h3 class="panel-title">🔍 Consulta Problemática</h3>
        </div>
        <div class="panel-body">
            <h5>❌ Consulta actual en facturaajaxModel():</h5>
            <div class="well">
                <code>
                INSERT INTO pedidos (mesero_id, facturado, cedula_cliente)<br>
                VALUES ($mesero, 'n', '$clienteCedula')
                </code>
            </div>
            
            <h5>⚠️ Problema:</h5>
            <p>La consulta no incluye el campo <code>usuario_id</code> que aparentemente es requerido (NOT NULL).</p>
            
            <h5>✅ Posibles soluciones:</h5>
            <ol>
                <li><strong>Agregar usuario_id a la consulta</strong> - Usar el ID del usuario en sesión</li>
                <li><strong>Usar mesero_id como usuario_id</strong> - Si son el mismo campo</li>
                <li><strong>Modificar la tabla</strong> - Permitir NULL o agregar valor por defecto</li>
            </ol>
        </div>
    </div>
    
    <div class="panel panel-primary">
        <div class="panel-heading">
            <h3 class="panel-title">👤 Información de Sesión</h3>
        </div>
        <div class="panel-body">
            <?php
            echo "<h5>📋 Variables de sesión disponibles:</h5>";
            echo "<table class='table table-condensed'>";
            echo "<thead><tr><th>Variable</th><th>Valor</th></tr></thead>";
            echo "<tbody>";
            
            $session_vars = ['usuario', 'validar', 'mesa', 'clientep', 'nombre', 'apellidos'];
            foreach ($session_vars as $var) {
                $valor = isset($_SESSION[$var]) ? $_SESSION[$var] : 'No definida';
                echo "<tr>";
                echo "<td><code>\$_SESSION['{$var}']</code></td>";
                echo "<td><strong>{$valor}</strong></td>";
                echo "</tr>";
            }
            echo "</tbody></table>";
            
            if (isset($_SESSION['usuario'])) {
                echo "<div class='alert alert-success'>";
                echo "<h6>✅ Usuario disponible para usar como usuario_id:</h6>";
                echo "<p>Valor: <code>{$_SESSION['usuario']}</code></p>";
                echo "</div>";
            } else {
                echo "<div class='alert alert-danger'>";
                echo "<h6>❌ No hay usuario en sesión</h6>";
                echo "<p>Esto puede causar problemas adicionales.</p>";
                echo "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-success">
        <div class="panel-heading">
            <h3 class="panel-title">🔧 Solución Recomendada</h3>
        </div>
        <div class="panel-body">
            <h5>✅ Modificar la consulta INSERT en facturaajaxModel():</h5>
            
            <h6>Cambiar de:</h6>
            <div class="well">
                <code style="color: red;">
                INSERT INTO pedidos (mesero_id, facturado, cedula_cliente)<br>
                VALUES ($mesero, 'n', '$clienteCedula')
                </code>
            </div>
            
            <h6>A:</h6>
            <div class="well">
                <code style="color: green;">
                INSERT INTO pedidos (mesero_id, usuario_id, facturado, cedula_cliente)<br>
                VALUES ($mesero, $usuario_id, 'n', '$clienteCedula')
                </code>
            </div>
            
            <p>Donde <code>$usuario_id</code> se obtiene de <code>$_SESSION['usuario']</code> o se usa el mismo valor que <code>$mesero</code>.</p>
            
            <div class="alert alert-info">
                <h6>💡 Implementación:</h6>
                <p>Agregar antes de la consulta:</p>
                <code>$usuario_id = isset($_SESSION['usuario']) ? $_SESSION['usuario'] : $mesero;</code>
            </div>
        </div>
    </div>
    
    <div class="panel panel-default">
        <div class="panel-heading">
            <h3 class="panel-title">🧪 Verificar Datos Existentes</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                echo "<h5>📊 Últimos pedidos en la tabla:</h5>";
                $stmt_pedidos = Conexion::conectar()->prepare("
                    SELECT id, numero_pedido, mesero_id, usuario_id, facturado, cedula_cliente, fecha_pedido 
                    FROM pedidos 
                    ORDER BY fecha_pedido DESC 
                    LIMIT 5
                ");
                $stmt_pedidos->execute();
                $ultimos_pedidos = $stmt_pedidos->fetchAll(PDO::FETCH_ASSOC);
                
                if (count($ultimos_pedidos) > 0) {
                    echo "<table class='table table-condensed'>";
                    echo "<thead>";
                    echo "<tr>";
                    echo "<th>ID</th>";
                    echo "<th>Número</th>";
                    echo "<th>Mesero ID</th>";
                    echo "<th>Usuario ID</th>";
                    echo "<th>Facturado</th>";
                    echo "<th>Cliente</th>";
                    echo "<th>Fecha</th>";
                    echo "</tr>";
                    echo "</thead>";
                    echo "<tbody>";
                    
                    foreach ($ultimos_pedidos as $pedido) {
                        echo "<tr>";
                        echo "<td>{$pedido['id']}</td>";
                        echo "<td>{$pedido['numero_pedido']}</td>";
                        echo "<td>{$pedido['mesero_id']}</td>";
                        echo "<td>" . ($pedido['usuario_id'] ?: '<span class="text-danger">NULL</span>') . "</td>";
                        echo "<td>{$pedido['facturado']}</td>";
                        echo "<td>{$pedido['cedula_cliente']}</td>";
                        echo "<td><small>{$pedido['fecha_pedido']}</small></td>";
                        echo "</tr>";
                    }
                    
                    echo "</tbody></table>";
                } else {
                    echo "<div class='alert alert-info'>No hay pedidos en la tabla</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <hr>
    <div class="row">
        <div class="col-md-3">
            <a href="index.php?action=test_facturacion_corregida" class="btn btn-primary btn-block">🔙 Test Facturación</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=mesa" class="btn btn-info btn-block">🪑 Ver Mesas</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=registroPmesa&ida=5" class="btn btn-warning btn-block">🧪 Test Mesa 5</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=diagnostico" class="btn btn-success btn-block">📊 Diagnóstico</a>
        </div>
    </div>
</div>

</body>
</html>
