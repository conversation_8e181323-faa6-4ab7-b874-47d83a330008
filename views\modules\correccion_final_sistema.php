<?php

session_start();

if(!isset($_SESSION["validar"]) || !$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "models/conexion.php";

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Corrección Final: Sistema Completamente Limpio</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
</head>
<body>

<div class="container">
    <h2>🔧 Corrección Final: Sistema Completamente Limpio</h2>
    <hr>
    
    <div class="alert alert-warning">
        <h4>⚠️ Detalle Detectado:</h4>
        <p>La verificación anterior muestra que una mesa todavía tiene múltiples pedidos borrador. Esto puede ser porque la consulta incluye pedidos cancelados.</p>
        <p><strong>Vamos a hacer una limpieza final y verificación correcta.</strong></p>
    </div>
    
    <?php
    if (isset($_POST['limpieza_final'])) {
        try {
            $stmt = Conexion::conectar();
            $stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $stmt->beginTransaction();
            
            echo "<div class='alert alert-info'>";
            echo "<h5>🔄 Ejecutando limpieza final...</h5>";
            echo "</div>";
            
            // 1. Verificar estado actual de pedidos
            echo "<p><strong>1. Verificando estado actual...</strong></p>";
            $stmt_verificar = $stmt->prepare("
                SELECT 
                    mesa_id,
                    estado,
                    COUNT(*) as cantidad
                FROM pedidos 
                WHERE mesa_id IS NOT NULL
                GROUP BY mesa_id, estado
                ORDER BY mesa_id, estado
            ");
            $stmt_verificar->execute();
            $estados_actuales = $stmt_verificar->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($estados_actuales as $estado) {
                echo "<p>Mesa {$estado['mesa_id']}: {$estado['cantidad']} pedidos en estado '{$estado['estado']}'</p>";
            }
            
            // 2. Eliminar pedidos borrador duplicados (mantener solo el más reciente por mesa)
            echo "<p><strong>2. Eliminando pedidos borrador duplicados...</strong></p>";
            
            $stmt_duplicados = $stmt->prepare("
                SELECT mesa_id, COUNT(*) as cantidad
                FROM pedidos 
                WHERE estado = 'borrador' AND mesa_id IS NOT NULL
                GROUP BY mesa_id
                HAVING cantidad > 1
            ");
            $stmt_duplicados->execute();
            $mesas_duplicadas = $stmt_duplicados->fetchAll(PDO::FETCH_ASSOC);
            
            $total_eliminados = 0;
            foreach ($mesas_duplicadas as $mesa) {
                $mesa_id = $mesa['mesa_id'];
                
                // Obtener el pedido más reciente
                $stmt_reciente = $stmt->prepare("
                    SELECT id FROM pedidos 
                    WHERE mesa_id = ? AND estado = 'borrador'
                    ORDER BY fecha_pedido DESC, id DESC
                    LIMIT 1
                ");
                $stmt_reciente->execute([$mesa_id]);
                $pedido_reciente = $stmt_reciente->fetch();
                
                if ($pedido_reciente) {
                    // Eliminar productos de pedidos antiguos
                    $stmt_eliminar_productos = $stmt->prepare("
                        DELETE FROM producto_vendido_mesa 
                        WHERE pedidos_id IN (
                            SELECT id FROM pedidos 
                            WHERE mesa_id = ? AND estado = 'borrador' AND id != ?
                        )
                    ");
                    $stmt_eliminar_productos->execute([$mesa_id, $pedido_reciente['id']]);
                    
                    // Eliminar pedidos antiguos
                    $stmt_eliminar_pedidos = $stmt->prepare("
                        DELETE FROM pedidos 
                        WHERE mesa_id = ? AND estado = 'borrador' AND id != ?
                    ");
                    $stmt_eliminar_pedidos->execute([$mesa_id, $pedido_reciente['id']]);
                    $eliminados = $stmt_eliminar_pedidos->rowCount();
                    
                    echo "<p class='text-success'>✅ Mesa {$mesa_id}: Eliminados {$eliminados} pedidos borrador duplicados</p>";
                    $total_eliminados += $eliminados;
                }
            }
            
            // 3. Limpiar productos huérfanos
            echo "<p><strong>3. Limpiando productos huérfanos...</strong></p>";
            $stmt_huerfanos = $stmt->prepare("
                DELETE pvm FROM producto_vendido_mesa pvm
                LEFT JOIN pedidos p ON pvm.pedidos_id = p.id
                WHERE p.id IS NULL
            ");
            $stmt_huerfanos->execute();
            $huerfanos_eliminados = $stmt_huerfanos->rowCount();
            echo "<p class='text-success'>✅ {$huerfanos_eliminados} productos huérfanos eliminados</p>";
            
            // 4. Limpiar mesas sin nombre o con ID NULL
            echo "<p><strong>4. Limpiando registros con mesa_id NULL...</strong></p>";
            $stmt_null_mesa = $stmt->prepare("
                UPDATE pedidos SET mesa_id = NULL WHERE mesa_id = 0 OR mesa_id = ''
            ");
            $stmt_null_mesa->execute();
            
            $stmt_eliminar_null = $stmt->prepare("
                DELETE FROM pedidos WHERE mesa_id IS NULL AND estado = 'borrador'
            ");
            $stmt_eliminar_null->execute();
            $null_eliminados = $stmt_eliminar_null->rowCount();
            echo "<p class='text-success'>✅ {$null_eliminados} pedidos sin mesa eliminados</p>";
            
            $stmt->commit();
            
            echo "<div class='alert alert-success'>";
            echo "<h5>🎉 ¡Limpieza final completada!</h5>";
            echo "<p><strong>Pedidos duplicados eliminados:</strong> {$total_eliminados}</p>";
            echo "<p><strong>Productos huérfanos eliminados:</strong> {$huerfanos_eliminados}</p>";
            echo "<p><strong>Pedidos sin mesa eliminados:</strong> {$null_eliminados}</p>";
            echo "</div>";
            
        } catch (Exception $e) {
            if ($stmt->inTransaction()) {
                $stmt->rollBack();
            }
            echo "<div class='alert alert-danger'>";
            echo "<h5>❌ Error en limpieza final:</h5>";
            echo "<p>" . $e->getMessage() . "</p>";
            echo "</div>";
        }
    }
    ?>
    
    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">🔍 Estado Actual Detallado</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                echo "<h5>📊 Pedidos por mesa y estado (solo activos):</h5>";
                $stmt_detalle = Conexion::conectar()->prepare("
                    SELECT 
                        COALESCE(m.nombre, CONCAT('Mesa ', p.mesa_id)) as mesa_nombre,
                        p.mesa_id,
                        p.estado,
                        COUNT(*) as cantidad,
                        GROUP_CONCAT(p.id ORDER BY p.fecha_pedido DESC) as pedidos_ids,
                        GROUP_CONCAT(p.numero_pedido ORDER BY p.fecha_pedido DESC) as numeros_pedidos
                    FROM pedidos p
                    LEFT JOIN mesas m ON p.mesa_id = m.id
                    WHERE p.mesa_id IS NOT NULL
                    GROUP BY p.mesa_id, p.estado
                    ORDER BY p.mesa_id, 
                        CASE p.estado
                            WHEN 'borrador' THEN 1
                            WHEN 'enviado' THEN 2
                            WHEN 'entregado' THEN 3
                            WHEN 'facturado' THEN 4
                            WHEN 'cancelado' THEN 5
                        END
                ");
                $stmt_detalle->execute();
                $detalles = $stmt_detalle->fetchAll(PDO::FETCH_ASSOC);
                
                if (count($detalles) > 0) {
                    echo "<table class='table table-condensed'>";
                    echo "<thead>";
                    echo "<tr>";
                    echo "<th>Mesa</th>";
                    echo "<th>Estado</th>";
                    echo "<th>Cantidad</th>";
                    echo "<th>IDs Pedidos</th>";
                    echo "<th>Números</th>";
                    echo "<th>Problema</th>";
                    echo "</tr>";
                    echo "</thead>";
                    echo "<tbody>";
                    
                    $mesa_anterior = null;
                    $borradores_por_mesa = [];
                    
                    foreach ($detalles as $detalle) {
                        $clase = '';
                        $problema = '';
                        
                        // Contar borradores por mesa
                        if ($detalle['estado'] == 'borrador') {
                            $borradores_por_mesa[$detalle['mesa_id']] = $detalle['cantidad'];
                        }
                        
                        switch ($detalle['estado']) {
                            case 'borrador':
                                $clase = ($detalle['cantidad'] > 1) ? 'danger' : 'info';
                                $problema = ($detalle['cantidad'] > 1) ? '⚠️ Múltiples borrador' : '✅ Normal';
                                break;
                            case 'enviado':
                                $clase = 'warning';
                                $problema = '📤 En cocina';
                                break;
                            case 'entregado':
                                $clase = 'primary';
                                $problema = '✅ Listo para facturar';
                                break;
                            case 'facturado':
                                $clase = 'success';
                                $problema = '💰 Facturado';
                                break;
                            case 'cancelado':
                                $clase = 'danger';
                                $problema = '❌ Cancelado';
                                break;
                        }
                        
                        echo "<tr class='{$clase}'>";
                        echo "<td><strong>{$detalle['mesa_nombre']}</strong></td>";
                        echo "<td>{$detalle['estado']}</td>";
                        echo "<td><span class='badge'>{$detalle['cantidad']}</span></td>";
                        echo "<td><small>{$detalle['pedidos_ids']}</small></td>";
                        echo "<td><small>{$detalle['numeros_pedidos']}</small></td>";
                        echo "<td>{$problema}</td>";
                        echo "</tr>";
                    }
                    
                    echo "</tbody></table>";
                    
                    // Verificar si hay problemas
                    $problemas_encontrados = false;
                    foreach ($borradores_por_mesa as $mesa_id => $cantidad) {
                        if ($cantidad > 1) {
                            $problemas_encontrados = true;
                            break;
                        }
                    }
                    
                    if ($problemas_encontrados) {
                        echo "<div class='alert alert-warning'>";
                        echo "<h6>⚠️ Se encontraron mesas con múltiples pedidos borrador</h6>";
                        echo "<p>Es necesario ejecutar la limpieza final para corregir esto.</p>";
                        echo "</div>";
                    } else {
                        echo "<div class='alert alert-success'>";
                        echo "<h6>✅ Todas las mesas tienen máximo 1 pedido borrador</h6>";
                        echo "<p>El sistema está funcionando correctamente.</p>";
                        echo "</div>";
                    }
                    
                } else {
                    echo "<div class='alert alert-info'>No hay pedidos activos en el sistema</div>";
                }
                
                echo "<h5>🪑 Solo mesas con productos activos (no facturados/cancelados):</h5>";
                $stmt_activas = Conexion::conectar()->prepare("
                    SELECT 
                        m.id,
                        m.nombre,
                        COUNT(pvm.productos_id) as productos_count,
                        GROUP_CONCAT(DISTINCT p.estado) as estados_pedidos
                    FROM mesas m
                    LEFT JOIN producto_vendido_mesa pvm ON m.id = pvm.mesas_id
                    LEFT JOIN pedidos p ON pvm.pedidos_id = p.id
                    WHERE p.estado IN ('borrador', 'enviado', 'entregado')
                    GROUP BY m.id, m.nombre
                    HAVING productos_count > 0
                    ORDER BY productos_count DESC
                ");
                $stmt_activas->execute();
                $mesas_activas = $stmt_activas->fetchAll(PDO::FETCH_ASSOC);
                
                if (count($mesas_activas) > 0) {
                    echo "<table class='table table-condensed'>";
                    echo "<thead>";
                    echo "<tr>";
                    echo "<th>Mesa</th>";
                    echo "<th>Productos</th>";
                    echo "<th>Estados</th>";
                    echo "<th>Acciones</th>";
                    echo "</tr>";
                    echo "</thead>";
                    echo "<tbody>";
                    
                    foreach ($mesas_activas as $mesa) {
                        echo "<tr class='success'>";
                        echo "<td><strong>{$mesa['nombre']}</strong></td>";
                        echo "<td><span class='badge badge-info'>{$mesa['productos_count']}</span></td>";
                        echo "<td><small>{$mesa['estados_pedidos']}</small></td>";
                        echo "<td>";
                        echo "<a href='index.php?action=registroPmesa&ida={$mesa['id']}' class='btn btn-xs btn-primary'>Ver</a>";
                        echo "</td>";
                        echo "</tr>";
                    }
                    
                    echo "</tbody></table>";
                } else {
                    echo "<div class='alert alert-success'>";
                    echo "<h6>✅ No hay mesas con productos activos</h6>";
                    echo "<p>Todas las mesas están limpias o facturadas.</p>";
                    echo "</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-success">
        <div class="panel-heading">
            <h3 class="panel-title">🛠️ Ejecutar Limpieza Final</h3>
        </div>
        <div class="panel-body">
            <h5>🧹 Limpieza Final Completa:</h5>
            <p>Esta acción realizará una limpieza definitiva del sistema:</p>
            
            <ul>
                <li>✅ <strong>Eliminar pedidos borrador duplicados</strong> (mantiene solo el más reciente)</li>
                <li>✅ <strong>Limpiar productos huérfanos</strong> (sin pedido asociado)</li>
                <li>✅ <strong>Eliminar registros con mesa_id NULL</strong></li>
                <li>✅ <strong>Verificar consistencia</strong> de datos</li>
            </ul>
            
            <form method="POST">
                <button type="submit" name="limpieza_final" class="btn btn-success btn-lg">
                    🧹 Ejecutar Limpieza Final
                </button>
            </form>
            
            <div class="alert alert-info" style="margin-top: 15px;">
                <h6>💡 Esta limpieza es más agresiva y definitiva:</h6>
                <ol>
                    <li>Elimina completamente pedidos duplicados (no los cancela)</li>
                    <li>Limpia todos los productos huérfanos</li>
                    <li>Corrige inconsistencias en mesa_id</li>
                    <li>Deja el sistema completamente limpio</li>
                </ol>
            </div>
        </div>
    </div>
    
    <hr>
    <div class="row">
        <div class="col-md-3">
            <a href="index.php?action=verificacion_final_limpieza" class="btn btn-primary btn-block">🔙 Verificación Anterior</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=mesa" class="btn btn-info btn-block">🪑 Ver Mesas</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=registroPmesa&ida=5" class="btn btn-warning btn-block">🧪 Test Mesa 5</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=diagnostico" class="btn btn-success btn-block">📊 Diagnóstico</a>
        </div>
    </div>
</div>

</body>
</html>
