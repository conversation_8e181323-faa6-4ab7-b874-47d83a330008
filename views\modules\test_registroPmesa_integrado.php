<?php
// Test de la integración del sistema de impresión en registroPmesa.php
echo "<h1>🧪 Test registroPmesa.php Integrado</h1>";
echo "<p><strong>Verificar que la integración del sistema de impresión por categorías funciona correctamente</strong></p>";

// Simular sesión de mesa
if (!isset($_SESSION)) {
    session_start();
}
$_SESSION["mesa"] = 1;
$_SESSION["validar"] = true;
$_SESSION["tipo_usuario"] = 1;
$_SESSION["turno"] = 1;

echo "<div style='background-color: #d1ecf1; padding: 15px; border-left: 4px solid #17a2b8; margin-bottom: 20px;'>";
echo "<h4>📋 Estado de la Integración</h4>";
echo "<p><strong>Mesa simulada:</strong> {$_SESSION['mesa']}</p>";
echo "<p><strong>Archivo modificado:</strong> views/modules/registroPmesa.php</p>";
echo "</div>";

// Verificar que los archivos necesarios existen
$archivos_necesarios = [
    'views/modules/componente_impresion_categorias.php',
    'views/modules/ajax_impresion_categoria.php',
    'views/modules/ajax_cargar_componente_impresion.php'
];

$archivos_faltantes = [];
foreach ($archivos_necesarios as $archivo) {
    if (!file_exists("../../$archivo")) {
        $archivos_faltantes[] = $archivo;
    }
}

if (empty($archivos_faltantes)) {
    echo "<div style='background-color: #d4edda; padding: 10px; border-left: 4px solid #28a745; margin: 10px 0;'>";
    echo "<p>✅ <strong>Todos los archivos necesarios están disponibles</strong></p>";
    echo "</div>";
} else {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
    echo "<h4>❌ Archivos faltantes</h4>";
    echo "<ul>";
    foreach ($archivos_faltantes as $archivo) {
        echo "<li>$archivo</li>";
    }
    echo "</ul>";
    echo "</div>";
}

// Verificar que el componente se puede cargar
try {
    require_once '../../views/modules/componente_impresion_categorias.php';
    
    echo "<div style='background-color: #d4edda; padding: 10px; border-left: 4px solid #28a745; margin: 10px 0;'>";
    echo "<p>✅ <strong>Componente de impresión cargado correctamente</strong></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
    echo "<h4>❌ Error cargando componente</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

// Verificar modificaciones en registroPmesa.php
$archivo_registroPmesa = '../../views/modules/registroPmesa.php';
if (file_exists($archivo_registroPmesa)) {
    $contenido = file_get_contents($archivo_registroPmesa);
    
    $verificaciones = [
        'Inclusión del componente' => "require_once 'views/modules/componente_impresion_categorias.php'",
        'Llamada a mostrarBotonesImpresionCategorias' => 'mostrarBotonesImpresionCategorias',
        'Función cargarBotonesImpresion' => 'function cargarBotonesImpresion',
        'AJAX para cargar componente' => 'ajax_cargar_componente_impresion.php'
    ];
    
    echo "<h2>🔍 Verificación de Modificaciones</h2>";
    
    foreach ($verificaciones as $descripcion => $buscar) {
        if (strpos($contenido, $buscar) !== false) {
            echo "<div style='background-color: #d4edda; padding: 10px; border-left: 4px solid #28a745; margin: 10px 0;'>";
            echo "<p>✅ <strong>$descripcion:</strong> Encontrado</p>";
            echo "</div>";
        } else {
            echo "<div style='background-color: #f8d7da; padding: 10px; border-left: 4px solid #dc3545; margin: 10px 0;'>";
            echo "<p>❌ <strong>$descripcion:</strong> No encontrado</p>";
            echo "</div>";
        }
    }
    
} else {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
    echo "<h4>❌ Archivo registroPmesa.php no encontrado</h4>";
    echo "</div>";
}

// Test de funcionalidad con datos simulados
echo "<h2>🧪 Test de Funcionalidad</h2>";

if (function_exists('mostrarBotonesImpresionCategorias')) {
    echo "<div style='background-color: #e7f3ff; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h4>🎯 Simulación de Pedido Enviado</h4>";
    echo "<p>Así se verían los botones en un pedido real:</p>";
    
    // Simular un pedido enviado
    echo "<div style='border: 1px solid #dee2e6; padding: 15px; margin: 10px 0; border-radius: 5px; background-color: white;'>";
    echo "<h5>Pedido: P000456 - Mesa 1 - Estado: enviado</h5>";
    echo "<p>Productos: 3 | Fecha: " . date('d/m/Y H:i') . "</p>";
    
    // Aquí se mostrarían los botones (simulado)
    echo "<div style='background-color: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 8px; border: 1px solid #dee2e6;'>";
    echo "<h6 style='color: #495057; margin-bottom: 15px;'>🖨️ Impresión por Categorías - P000456</h6>";
    echo "<div style='display: flex; flex-wrap: wrap; gap: 10px;'>";
    echo "<button style='background-color: #007bff; color: white; padding: 6px 12px; border: none; border-radius: 3px; cursor: pointer; font-size: 11px; font-weight: bold;'>🔄 Cargar Botones</button>";
    echo "</div>";
    echo "<p style='font-size: 11px; color: #6c757d; margin-top: 10px;'>Al hacer clic en 'Cargar Botones', aparecerán los botones específicos para las categorías que tengan productos (BAR, COCINA, ASADOS)</p>";
    echo "</div>";
    
    echo "</div>";
    echo "</div>";
    
} else {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
    echo "<h4>❌ Función no disponible</h4>";
    echo "<p>La función mostrarBotonesImpresionCategorias no está disponible</p>";
    echo "</div>";
}

// Instrucciones para probar
echo "<h2>📋 Instrucciones para Probar</h2>";

echo "<div style='background-color: #fff3cd; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>🎯 Cómo Probar la Integración:</h4>";
echo "<ol>";
echo "<li><strong>Ir a una mesa:</strong> <a href='../../index.php?action=registroPmesa&ida=1' target='_blank'>Mesa 1</a></li>";
echo "<li><strong>Agregar productos:</strong> Agregar productos de diferentes categorías (bebidas, comidas, carnes)</li>";
echo "<li><strong>Enviar pedido:</strong> Hacer clic en 'Enviar Pedido a Cocina'</li>";
echo "<li><strong>Verificar botones:</strong> Después de enviar, deberían aparecer los botones de impresión</li>";
echo "<li><strong>Probar impresión:</strong> Hacer clic en 'Cargar Botones' y luego en cada categoría</li>";
echo "</ol>";

echo "<h4>✅ Qué Esperar:</h4>";
echo "<ul>";
echo "<li>Después de enviar un pedido, aparece una sección '🖨️ Impresión por Categorías'</li>";
echo "<li>Al hacer clic en 'Cargar Botones', aparecen botones específicos para cada categoría</li>";
echo "<li>Solo aparecen botones para categorías que tienen productos</li>";
echo "<li>Al hacer clic en un botón de categoría, se abre ventana de impresión</li>";
echo "</ul>";
echo "</div>";

// Estado de la integración
echo "<h2>📊 Estado de la Integración</h2>";

$estado_integracion = [
    'Componente incluido' => file_exists('../../views/modules/componente_impresion_categorias.php'),
    'AJAX configurado' => file_exists('../../views/modules/ajax_impresion_categoria.php'),
    'Carga dinámica' => file_exists('../../views/modules/ajax_cargar_componente_impresion.php'),
    'registroPmesa modificado' => strpos(file_get_contents($archivo_registroPmesa), 'mostrarBotonesImpresionCategorias') !== false
];

$total_items = count($estado_integracion);
$items_completados = array_sum($estado_integracion);
$porcentaje = round(($items_completados / $total_items) * 100);

echo "<div style='background-color: #e7f3ff; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>🎯 Progreso de Integración: $porcentaje% ($items_completados/$total_items)</h4>";

foreach ($estado_integracion as $item => $completado) {
    $icono = $completado ? '✅' : '❌';
    $color = $completado ? '#28a745' : '#dc3545';
    echo "<p style='color: $color;'>$icono <strong>$item</strong></p>";
}

if ($porcentaje == 100) {
    echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0;'>";
    echo "<h5>🎉 ¡Integración Completa!</h5>";
    echo "<p>El sistema de impresión por categorías está completamente integrado en registroPmesa.php</p>";
    echo "</div>";
} else {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
    echo "<h5>⚠️ Integración Incompleta</h5>";
    echo "<p>Algunos componentes faltan o no están configurados correctamente</p>";
    echo "</div>";
}

echo "</div>";

// Botones de navegación
echo "<p style='margin-top: 30px;'>";
echo "<a href='../../index.php?action=registroPmesa&ida=1' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🪑 Probar Mesa 1</a>";
echo "<a href='demo_impresion_categorias.php' style='background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🎯 Demo</a>";
echo "<a href='test_integracion_impresion.php' style='background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔧 Test Técnico</a>";
echo "</p>";
?>
