<?php session_start();
require_once "../../models/crud.php";
require_once "../../models/crudFacturaAja.php";
require_once "../../models/cruddevolucion.php";
require_once "../../models/crudPedidoMesaProducto.php";
require_once "../../controllers/controller.php";
require_once "../../controllers/controllerFacturaAja.php";
require_once "../../controllers/controllerdevolucion.php";
	ini_set("session.cookie_lifetime","28800");
	ini_set("session.gc_maxlifetime","28800");
//$devolucion -> totalDevolucionController();
if(isset($_POST['pcedula']))
	{
		$queryString = $_POST['pcedula'];
		//echo $queryString;	//echo'<br> <script>alert("Entro al ajax");</script> <br>';
		$ajax=new controllerFacturaAja();
		$r=$ajax->buscarCedulaController(strtoupper($queryString));
	 if ($r==0)
		{	echo' <h3>No existe este cliente</h3>';	//echo "<script>alert('No existe este cliente porque')</script>";

		}
	 else
		{	/*echo' <script>alert("Señor");</script>';
				echo'<form name="calculo" method="post" >
						<label for=""><b>FACTURA No</b></label>
						<input value="'.$r['fid'].'" type="text" placeholder="Placa ejemplo UQM889" class="mayuscula"  name="placa" id="placa" size="38" readonly="true"   required><br>
					</form>';*/
				echo $r["nombre"] ." ".$r["apellidos"];
				 $_SESSION["clientep"]=$queryString;
				 //echo "<script>alert('Mesa No Session: ".$_SESSION["clientep"]." ')</script>";
		}
	}
else
	{	echo "<script>alert('Intentelo mas tarde')</script>";	}