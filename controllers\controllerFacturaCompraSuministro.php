<?php
ob_start();
class controllerFacturaCompraSuministro extends MvcController
{
	#REGISTRO DE FACTURA COMPRA SUMINISTRO tabla temporar pendiente factura
	#------------------------------------
	 public function registroFacturaSumistroPController()
		{//echo "<script>alert('Entro Controller ')</script>";
			//session_start();
		 if(isset($_POST["cantidad"]) and isset($_POST["suministros"]))
			{	//echo "<script>alert('Entro Controller  IF')</script>";
				$idS = DatosFacturaCompraSuministro::bucarIdSuministroPModel($_POST["sid"]);
				$sucursal = DatosFacturaCompraSuministro::bucarCantidadSuministroSucursalModel($_POST["sid"]);
				$sucantidad=$sucursal['sucantidad'];
				$costoActual=$_POST["costo"];

			 if ($idS==0)
			 	{//(id, proveedor_id, valor_factura, fecha_factura, numero_factura, pago, abono, suministro_id, cantidad, precio, precioV, descuento, facturado)
			 	 $unidad=0;
				 $IVA=$_POST["iva"];
				 $IVA1=$IVA/100;
				 $cantp=str_replace(",", ".", $_POST["cantidad"]);
				 $desc=$_POST["precioC"]* $_POST["descuento"]/100;
				 $desTotal=$_POST["precioC"]-$desc;
				 $IVAtotal=$desTotal+$desTotal*$IVA1;
				 $unidad=$IVAtotal/$_POST["cantidad"];

				 if($_POST["costo"]>0 and $sucantidad>0){
				 	    $suma_promedios= $IVAtotal+ $sucantidad*$costoActual;
				 	    $cantidad_total=$sucantidad+$cantp;
				 	    $costo=round($suma_promedios/$cantidad_total);

						//$costo=round(($unidad+$costoActual)/2);

						//$base=(round($costoActual/(1+$IVA1),2)+round($unidad/(1+$IVA1),2))/2;
						//$impuesto=$costo-$base;
					}else{
						$costo=$unidad;
						//$base=round($costo/(1+$IVA1),2);
						//$impuesto=$costo-$base;
					}


				 $datosController =array('suministro_id'=>$_POST["sid"],
				 						'compra_id'=>$_POST["nfactura"],
				 						'cantidad'=>$_POST["cantidad"],
				 						'precio'=>$_POST["precioC"],
				 						'precioU'=>$unidad,
				 						'descuento'=>$_POST["descuento"],
				 						'iva'=>$_POST["iva"],
				 						'proveedor_id'=>$_SESSION["proveedor_id"],
				 						'valor_factura'=>$_SESSION["valorT"],
				 						'fecha_factura'=>$_SESSION["fecha_factura"],
				 						'numero_factura'=>$_SESSION["facturaN"],
				 						'pago'=>$_SESSION["pago"],
				 						'abono'=>$_SESSION["abono"],
				 						'precio'=>$_POST["precioC"]);
				 //echo "<script>alert('Entro Controller ".$datosController['cantidad']." no')</script>";
				 $respuesta = DatosFacturaCompraSuministro::registroFacturaSumistroPModel($datosController, "pendiente_factura");
				 if($respuesta== "success")
					{	header("location:index.php?action=registroFacturaSuministro&ida=".$_SESSION["ver"]);	}
				 else
					{	header("location:index.php?action=falloFs");	}
				/**/}
			 else
			 	{	echo "<script>alert('Este producto ya esta en la factura, verifique')</script>";	}
			}
		}
	#------------------------------------
	#REGISTRO DE FACTURA COMPRA SUMINISTRO
	#------------------------------------
	 public function registroFacturaSumistroController()
		{//echo "<script>alert('Entro Controller ')</script>";
			//session_start();
		 if(isset($_POST["cantidad"]) and isset($_POST["suministros"]))
			{	echo "<script>alert('Entro Controller  IF')</script>";
				$idS = DatosFacturaCompraSuministro::bucarIdSuministroModel($_POST["suministros"]);
				$datosController =array('suministro_id'=>$idS,
										'compra_id'=>$_POST["nfactura"],
										'cantidad'=>$_POST["cantidad"],
										'precioV'=>$_POsST["precioV"],
										'descuento'=>$_POST["descuento"],
										'precio'=>$_POST["precioC"]);
				//echo "<script>alert('Entro Controller ".$datosController['cantidad']." no')</script>";
				$respuesta = DatosFacturaCompraSuministro::registroFacturaSumistroModel($datosController, "detalle_factura_suministro");
				if($respuesta== "success")
					{	header("location:index.php?action=registroFacturaSuministro&ida=".$_SESSION["ver"]);	}
				else
					{	header("location:index.php?action=falloFs");	}
			}
		}
	#------------------------------------
	#VISTA DE FACTURA COMPRA SUMINISTRO  Pendiente
	#------------------------------------
	 public function vistaFacturaSumistroPController()
		{  session_start();
		 //(id, proveedor_id, valor_factura, fecha_factura, numero_factura, pago, abono, suministro_id, cantidad, precio, descuento, facturado)
			$pendiente = DatosFacturaCompraSuministro::pendiente_factura($_SESSION["proveedor_id"]);
			$total=0;
			$totalsinIVA=0;
			$totalpagar=0;
		 foreach($pendiente as $row => $item)
			{
				$unidad=0;
				$IVA=$item["pfiva"];
				$IVA1=$IVA/100;
				$desc=$item["pfprecio"]* $item["pfdescuento"]/100;
				$desTotal=$item["pfprecio"]-$desc;
				$IVAtotal=$desTotal+$desTotal*$IVA1;
				$unidad=$IVAtotal/$item["pfcantidad"];
				$_SESSION["facturaN"]=$item["pfnumero_factura"];
				$_SESSION["valorT"]=$item["pfvalor_factura"];
				$totalpagar=$totalpagar+$IVAtotal;
				$total=$total+$item["pfprecio"];
				$totalsinIVA=$totalsinIVA+$desTotal;
				echo'
					<tr>
						<td>'.$item["scodigo"].'</td>
						<td>'.$item["snombre"].'</td>
						<td>'.$item["pfcantidad"].'</td>
						<td>'.$item["pfprecio"].'</td>
						<td>'.$item["pfdescuento"].'</td>
						<td>'.$desTotal.'</td>
						<td>'.$IVA.'</td>
						<td>'.$unidad.'</td>
						<td>'.$IVAtotal.'</td>

						<td>
							<a href="index.php?action=registroFacturaSuministro&idBorrarP='.$item["pfid"].'">Borrar</a>
						</td>
					</tr>';
			}
		$dif=$_SESSION["valorT"]-$total;
			echo'
				<tr class="filaTotal">
					<td colspan="3" >Total</td>
					<td colspan="2" >'.$total.'</td>
					<td colspan="3" >'.$totalsinIVA.'</td>
					<td colspan="2" ><input type="hidden" name="totalpagar" value="'.$totalpagar.'" readonly >'.$totalpagar.'</td>
				</tr> ';

			//echo '<h2> Diferencia Total precio : $'.$total.'</h2>';<a href="index.php?action=editarSuministroFactura&idEditar='.$item["pfid"].'">Editar</a>
			//echo "<script>alert('Diferencia Total precio : $".$total."')</script>";
		}
	#---------------------------------------------
	#VISTA DE FACTURA COMPRA SUMINISTRO  tabla detalle
	#------------------------------------
	 public function vistaFacturaSumistroController()
		{   //echo "<script>alert('Entro Controller  no factura')</script>";
			//session_start();
			if ($_GET["id"]>0 )
				{
					$_SESSION["ver"]= $_GET["id"]; // este es el id de la compra
				}
			$respuesta = DatosFacturaCompraSuministro::vistaFacturaSumistroModel($_SESSION["ver"]);
			$totalpagar=0;
			$total=0;
			$facturNo=0;
			$facturValor=0;
			foreach($respuesta as $row => $item1)
				{
				 if ($facturNo==0)
					{
						$facturNo=$item1["csnf"];
						$facturValor=$item1["csvalor"];
						break;
					}
				}
			echo '<div ><h3> Factura No : '.$facturNo.'&nbsp;&nbsp;  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;  Valor : $'.$facturValor.'</h3></div><br>
					<h4>DETALLE DE FACTURA</h4>
				<table border="1" class="table table-hover">
					<thead>
						<tr>
							<th> CODIGO</th>
							<th>NOMBRE</th>
							<th>CANTIDAD</th>
							<th>PRECIO</th>
							<th>DESC%</th>
							<th>SIN IVA</th>
							<th>IVA%</th>
							<th>VALOR Ud</th>
							<th>TOTAL PAGAR</th>
							<th></th>
						</tr>
					</thead>
					<tbody>';

			 $formateador2 = new NumberFormatter( 'es_CO', NumberFormatter::CURRENCY );
			 $formateador2->setAttribute( $formateador2::FRACTION_DIGITS, 0 );
			 $formateador = new NumberFormatter( 'en_US', NumberFormatter::DECIMAL );
			 //$formateador2->format();

		 foreach($respuesta as $row => $item)
			{
				$unidad=0;
				$IVA=0;
				$IVA1=$IVA/100;
				$desc=$item["fsprecio"]* $item["fsdescuento"]/100;
				$desTotal=$item["fsprecio"]-$desc;
				$IVAtotal=$desTotal+$desTotal*$IVA1;
				$unidad=$IVAtotal/$item["fscantidad"];
				$_SESSION["facturaN"]=$item["csnf"];
				$_SESSION["valorT"]=$item["csvalor"];
				$totalpagar=$totalpagar+$IVAtotal;
				$total=$total+$item["fsprecio"];
				echo'<tr>
						<td>'.$item["scodigo"].'</td>
						<td>'.$item["snombre"].'</td>
						<td>'.$item["fscantidad"].'</td>
						<td>'.$item["fsprecio"].'</td>
						<td>'.$item["fsdescuento"].'</td>
						<td>'.$desTotal.'</td>
						<td>'.$IVA.'</td>
						<td>'.$unidad.'</td>
						<td>'.$IVAtotal.'</td>
						<td>'.$item["fsfacturado"].'</td>

					</tr>';
			}
				$total=$_SESSION["valorT"]-$total;
				echo '</tbody>
					<thead>
						<tr > <td colspan="10" ></td> </tr>
					</thead>
				</table>	 ' ;
			/*
			<td>
								<a href="index.php?action=editarSuministroFactura&idEditar='.$item["sid"].'">Editar</a>
								<a href="index.php?action=registroFacturaSuministro&idBorrar='.$item["sid"].'">Borrar</a>
							</td>
			 echo '<h2> Diferencia Total precio : $'.$total.'</h2>';
			//echo "<script>alert('Diferencia Total precio : $".$total."')</script>";*/
		}
	#---------------------------------------------
	#ACTUALIZAR SUMINISTRO boveda y vaciar pendiente
	#---------------------------------------
	 public function actualizarFacturaSumistroPfController($dato)
		{	//echo "<script>alert('controles ajax guardar ".$dato."');</script>";
		 if ($dato>0)
			{
				//echo "<script>alert('controles ajax guardar  IF".$dato."');</script>";
			 $respuesta=DatosFacturaCompraSuministro::actualizarFacturaSuministroPfModen($dato, "detalle_factura_suministro");
			 if ($respuesta=="success")
				{
					unset($_SESSION["ver"]);
					unset($_SESSION["proveedor"]);
					unset($_SESSION["valorT"]);
					unset($_SESSION["facturaN"]);
					unset($_SESSION["abono"]);
					unset($_SESSION["fecha_factura"]);
					unset($_SESSION["pago"]);

					echo' <script>alert("Guardo exitosamente");/*location.href ="Csuministro";*/</script>';
				}
			 else{ header("location:index.php?action=falloFs"); }
			}
		}
	#---------------------------------------
	#ACTUALIZAR SUMINISTRO FACTURA segun factura
	#---------------------------------------
	 public function actualizarFacturaSumistroController($dato)
		{	//echo "<script>alert('controles ajax guardar ".$dato."');</script>";
		 if ($dato>0)
			{
				//echo "<script>alert('controles ajax guardar  IF".$dato."');</script>";
			 $respuesta=DatosFacturaCompraSuministro::actualizarFacturaSuministroModen($dato, "detalle_factura_suministro");
			 if ($respuesta=="success")
				{
					unset($_SESSION["ver"]);
					unset($_SESSION["proveedor"]);
					unset($_SESSION["valorT"]);
					unset($_SESSION["facturaN"]);

					echo' <script>alert("Guardo exitosamente");/*location.href ="Csuministro";*/</script>';
				}
			 else{ header("location:index.php?action=falloFs"); }
			}
		}
	#---------------------------------------
	#EDITAR FACTURA COMPRA SUMINISTRO
	#------------------------------------
	 public function editarFacturaSumistroController()
		{
			$datosController = $_GET["id"];
			$respuesta = DatosFacturaCompraSuministro::editarFacturaSumistrotroModel($datosController, "detalle_factura_suministro");
			echo' <input type="text" value="'.$respuesta["proveedor_id"].'" name="proveedor_idEditar" required>
			 <input type="text" value="'.$respuesta["valor_compra"].'" name="valor_compraEditar" required>
			 <input type="text" value="'.$respuesta["fecha_hora"].'" name="fecha_horaEditar" required>
			 <input type="text" value="'.$respuesta["numero_factura_compra"].'" name="numero_factura_compraEditar" required>
			 <input type="hidden" value="'.$respuesta["id"].'" name="idEditar" required>
				 <input type="submit" value="Actualizar">';
		}
	#---------------------------------------
	#ACTUALIZAR FACTURA COMPRA SUMINISTRO
	#------------------------------------
		public function ctualizarFacturaSumistroController()
			{//echo "<script>alert('Entro Controller Actualizar Producto')</script>";

				if(isset($_POST["proveedor_idEditar"]))
					{
						$datosController = array(  "proveedor_id"=>$_POST["proveedor_idEditar"],
													"valor_compra"=>$_POST["valor_compraEditar"],
													"fecha_hora"=>$_POST["fecha_horaEditar"],
													"numero_factura_compra"=>$_POST["numero_factura_compraEditar"],
													"id"=>$_POST["idEditar"]);
						$respuesta = DatosFacturaCompraSuministro::actualizarFacturaSumistroModel($datosController, "detalle_factura_suministro");
						if($respuesta == "success")
							{	header("location:index.php?action=cambioCs");	}
						else
							{	echo "error";	}
					}
			}
	#----------------------------------------
	#BORRAR FACTURA COMPRA SUMINISTRO  tabla pendiente
	#------------------------------------
	 public function borrarFacturaSumistroPController()
		{
		 if(isset($_GET["idBorrarP"]))
			{
				$datosController = $_GET["idBorrarP"];
				$respuesta = DatosFacturaCompraSuministro::borrarFacturaSumistroPModel($datosController, "pendiente_factura");
				if($respuesta == "success")
					{	header("location:index.php?action=registroFacturaSuministro");	}
			}
		}
	#---------------------------------
	#BORRAR FACTURA COMPRA SUMINISTRO
	#------------------------------------
	 public function borrarFacturaSumistroController()
		{
		 if(isset($_GET["idBorrar"]))
			{
				$datosController = $_GET["idBorrar"];
				$respuesta = DatosFacturaCompraSuministro::borrarFacturaSumistroModel($datosController, "detalle_factura_suministro");
				if($respuesta == "success")
					{	header("location:index.php?action=registroFacturaSuministro");	}
			}
		}
	#---------------------------------
	#EDITAR LOS SUMINISTRO DE LA FACTURA "CANTIDAD"tabla pendiente
	#------------------------------------
	 public function editarSumistroPController()
		{
			$datosController = array(  "suministro_id"=>$_GET["idEditar"]);
			$respuesta = DatosFacturaCompraSuministro::editarFacturaSumistroPModel($datosController, "pendiente_factura");
			echo' <table>
					<tr>
						<td align="right">Cantidad:</td>
						<td><input type="text" value="'.$respuesta["cantidad"].'" name="cantidad" required> </td>
					</tr>
					<tr>
						<td align="right">Valor compra Unidad:</td>
						<td><input type="text" value="'.$respuesta["precio"].'" name="precio" required></td>
					</tr>
			</table>
			<input type="hidden" value="'.$respuesta["suministro_id"].'" name="suministro_id" required>
			  <br>
			<input type="submit" value="Actualizar">';

		}
	#---------------------------------
	#EDITAR LOS SUMINISTRO DE LA FACTURA "CANTIDAD" v actualizarFacturaSumistroCModel
	#------------------------------------
	 public function editarSumistroController()
		{
			$datosController = array(  "suministro_id"=>$_GET["idEditar"],
										"compra_suministro_id"=>$_SESSION["ver"]);
			$respuesta = DatosFacturaCompraSuministro::editarFacturaSumistroCModel($datosController, "detalle_factura_suministro");
			echo' <table>
					<tr>
						<td align="right">Cantidad:</td>
						<td><input type="text" value="'.$respuesta["cantidad"].'" name="cantidad" required> </td>
					</tr>
					<tr>
						<td align="right">Valor compra Unidad:</td>
						<td><input type="text" value="'.$respuesta["precio"].'" name="precio" required></td>
					</tr>

			</table>
			<input type="hidden" value="'.$respuesta["suministro_id"].'" name="suministro_id" required>
			 <input type="hidden" value="'.$respuesta["compra_suministro_id"].'" name="compra_suministro_id" required>

			  <br>
			<input type="submit" value="Actualizar">';

		}
	#---------------------------------
	#ACTUALIZAR FACTURA COMPRA SUMINISTRO  tabla pendiente
	#------------------------------------
	 public function actualizarFacturaSumistroPController()
		{	//echo "<script>alert('Actualizar ".$_POST["suministro_id"]."  otro ".$_POST["compra_suministro_id"]."')</script>";
		 if(isset($_POST["suministro_id"]) )
			{ //echo "<script>alert(' if Actualizar ".$_POST["suministro_id"]."  otro ".$_POST["compra_suministro_id"]."')</script>";
				$datosController = array(  "suministro_id"=>$_POST["suministro_id"],
											"cantidad"=>$_POST["cantidad"],
											"precio"=>$_POST["precio"]);
				$respuesta = DatosFacturaCompraSuministro::actualizarFacturaSumistroPModel($datosController, "detalle_factura_suministro");
				if($respuesta == "success")
					{	header("location:index.php?action=registroFacturaSuministro");	}
				else
					{	echo "error";	}
			}
		}
	#----------------------------------------
	#ACTUALIZAR FACTURA COMPRA SUMINISTRO
	#------------------------------------
	 public function actualizarFacturaSumistroCController()
		{	//echo "<script>alert('Actualizar ".$_POST["suministro_id"]."  otro ".$_POST["compra_suministro_id"]."')</script>";
		 if(isset($_POST["suministro_id"]) && isset($_POST["compra_suministro_id"]))
			{
				//echo "<script>alert(' if Actualizar ".$_POST["suministro_id"]."  otro ".$_POST["compra_suministro_id"]."')</script>";
				$datosController = array(  "suministro_id"=>$_POST["suministro_id"],
											"compra_suministro_id"=>$_POST["compra_suministro_id"],
											"cantidad"=>$_POST["cantidad"],

											"precio"=>$_POST["precio"]);
				$respuesta = DatosFacturaCompraSuministro::actualizarFacturaSumistroCModel($datosController, "detalle_factura_suministro");
				if($respuesta == "success")
					{	header("location:index.php?action=registroFacturaSuministro&id=142");	}
				else
					{	echo "error";	}
			}
		}
	#----------------------------------------
	#LISTA Productos
	#---------------------------------------
		public function listaProductos1Controller()
			{
				$respuesta = Datos::listaProductosModel("productos");
				return 	$respuesta;
			}
	#---------------------------------------
	#LISTA Suministros
	#---------------------------------------
		public function listaSuministros1Controller()
			{
				$respuesta = Datos::listaSuministrosModel("suministros");
				return 	$respuesta;
			}
	#---------------------------------------
}
