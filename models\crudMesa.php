<?php
require_once "conexion.php";
class DatosMesa extends Conexion
{
	#REGISTRO DE MESA
	#-------------------------------------
	 public static function registroMesaModel($datosModel, $tabla){
				//echo "<script>alert('Entro CRUD ".$datosModel['nombre']." no')</script>";
		$consulta="INSERT INTO $tabla (nombre) VALUES (:nombre)";
		//echo "<script>alert('Entro CRUD ".$consulta." no')</script>";
		$stmt = Conexion::conectar()->prepare($consulta);
		
		$stmt->bindParam(":nombre", $datosModel['nombre'], PDO::PARAM_STR);
		//$stmt->execute();

		//echo "<script>alert('Guardo')</script>";

		if($stmt->execute())
			{	return "success";	}
		else{ return "error";	}
		$stmt->close();
		}
	#----------------------------------
	#VISTA MESA
	#-------------------------------------
	 public static function vistaMesaModel($tabla)
		{
			$stmt = Conexion::conectar()->prepare("SELECT id, nombre, cupo, descripcion, estado  FROM $tabla");
			$stmt->execute();
			return $stmt->fetchAll();
			$stmt->close();
		}
	#----------------------------------
	#VISTA MESAS DEL MESERO
	#-------------------------------------
	 public static function vistaMesasMeseroModel($tabla, $persona_id)
		{
			try{

				// Conexión a la base de datos
            	$conexion = Conexion::conectar();

				$sql = ("SELECT m.id as id, m.nombre as nombre, m.cupo, m.descripcion, m.estado, m.ubicacion FROM $tabla m INNER JOIN personas p ON m.id BETWEEN p.inicio AND p.fin WHERE p.id = :persona_id and p.inicio > 0 AND p.fin > 0 ORDER BY m.id");
				//echo '<br>'.$sql. '<br>';
				$stmt = $conexion->prepare($sql);
            
	            // Binding del parámetro
	            $stmt->bindParam(':persona_id', $persona_id, PDO::PARAM_INT);
	            
	            // Ejecución de la consulta
	            $stmt->execute();
	            
	            // Obtener resultados
	            $resultados = $stmt->fetchAll(PDO::FETCH_ASSOC);
	            
	            // Cerrar statement
	            $stmt = null;

	            // Verificar si se encontraron resultados
		        if (empty($resultados)) {
		            return 0; // No se encontraron mesas para el mesero
		        }
	            
	            return $resultados;

			} catch (PDOException $e) {
	            // Log del error (recomendado usar un sistema de logging real)
	            error_log("Error en obtenerMesasPorMesero: " . $e->getMessage());
	            
	            // En producción, no mostrar detalles del error
	            throw new Exception("Error al obtener las mesas del mesero");
	            //return "Error!: ".$e->getMessage()."</br>";
	        }
		}
	#----------------------------------
	#EDITAR MESA
	#-------------------------------------
	 public static function editarMesaModel($datosModel, $tabla)
		{
			$stmt = Conexion::conectar()->prepare("SELECT * FROM $tabla WHERE id = :id");
			$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);
			$stmt->execute();
			return $stmt->fetch();
			$stmt->close();
		}
	#----------------------------------
	#ACTUALIZAR MESA
	#-------------------------------------
	 public static function actualizarMesaModel($datosModel, $tabla)
		{ echo "<script>alert('Entro Actualizar Producto')</script>";
			$stmt = Conexion::conectar()->prepare("UPDATE $tabla SET nombre = :nombre, cupo = :cupo, descripcion = :descripcion WHERE id = :id");
			$stmt->bindParam(":nombre", $datosModel["nombre"], PDO::PARAM_STR);
			$stmt->bindParam(":cupo", $datosModel["cupo"], PDO::PARAM_STR);
			$stmt->bindParam(":descripcion", $datosModel["descripcion"], PDO::PARAM_STR);
			$stmt->bindParam(":id", $datosModel["id"], PDO::PARAM_INT);
			if($stmt->execute())
				{echo "<script>alert('Guardo Actualizar Roll')</script>";
					return "success";	}
			else{	return "error";			}
			$stmt->close();
		}
	#----------------------------------
	#BORRAR MESA
	#------------------------------------
	 public static function borrarMesaModel($datosModel, $tabla)		{
			$stmt = Conexion::conectar()->prepare("DELETE FROM $tabla WHERE id = :id");
			$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);
			if($stmt->execute())
				{	return "success";	}
			else{	return "error";		}
			$stmt->close();
		}
	#----------------------------------
}