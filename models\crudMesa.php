<?php
require_once "conexion.php";
class DatosMesa extends Conexion
{
	#REGISTRO DE MESA
	#-------------------------------------
	 public static function registroMesaModel($datosModel, $tabla){
				//echo "<script>alert('Entro CRUD ".$datosModel['nombre']." no')</script>";
		$consulta="INSERT INTO $tabla (nombre) VALUES (:nombre)";
		//echo "<script>alert('Entro CRUD ".$consulta." no')</script>";
		$stmt = Conexion::conectar()->prepare($consulta);
		$stmt->execute();
		$stmt->bindParam(":nombre", $datosModel['nombre'], PDO::PARAM_STR);

		//echo "<script>alert('Guardo')</script>";

		if($stmt->execute())
			{	return "success";	}
		else{ return "error";	}
		$stmt->close();
		}
	#----------------------------------
	#VISTA MESA
	#-------------------------------------
	 public static function vistaMesaModel($tabla)
		{
			$stmt = Conexion::conectar()->prepare("SELECT id, nombre, cupo, descripcion, estado  FROM $tabla");
			$stmt->execute();
			return $stmt->fetchAll();
			$stmt->close();
		}
	#----------------------------------
	#EDITAR MESA
	#-------------------------------------
	 public static function editarMesaModel($datosModel, $tabla)
		{
			$stmt = Conexion::conectar()->prepare("SELECT * FROM $tabla WHERE id = :id");
			$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);
			$stmt->execute();
			return $stmt->fetch();
			$stmt->close();
		}
	#----------------------------------
	#ACTUALIZAR MESA
	#-------------------------------------
	 public static function actualizarMesaModel($datosModel, $tabla)
		{ echo "<script>alert('Entro Actualizar Producto')</script>";
			$stmt = Conexion::conectar()->prepare("UPDATE $tabla SET nombre = :nombre, cupo = :cupo, descripcion = :descripcion WHERE id = :id");
			$stmt->bindParam(":nombre", $datosModel["nombre"], PDO::PARAM_STR);
			$stmt->bindParam(":cupo", $datosModel["cupo"], PDO::PARAM_STR);
			$stmt->bindParam(":descripcion", $datosModel["descripcion"], PDO::PARAM_STR);
			$stmt->bindParam(":id", $datosModel["id"], PDO::PARAM_INT);
			if($stmt->execute())
				{echo "<script>alert('Guardo Actualizar Roll')</script>";
					return "success";	}
			else{	return "error";			}
			$stmt->close();
		}
	#----------------------------------
	#BORRAR MESA
	#------------------------------------
	 public static function borrarMesaModel($datosModel, $tabla)		{
			$stmt = Conexion::conectar()->prepare("DELETE FROM $tabla WHERE id = :id");
			$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);
			if($stmt->execute())
				{	return "success";	}
			else{	return "error";		}
			$stmt->close();
		}
	#----------------------------------
}