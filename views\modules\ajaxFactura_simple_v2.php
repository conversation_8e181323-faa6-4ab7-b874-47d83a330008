<?php
// Versión ultra-simplificada para facturar mesa 10
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// CONFIGURACIÓN EXTREMA PARA MESAS PESADAS
ini_set('max_execution_time', 600); // 10 minutos
ini_set('memory_limit', '1024M'); // 1GB

session_start();

try {
    error_log("FACTURACIÓN SIMPLE V2 INICIADA - Mesa: " . ($_POST['mesa'] ?? 'N/A') . " - Timestamp: " . date('Y-m-d H:i:s'));
    
    // Verificar sesión
    if (!isset($_SESSION["usuario"])) {
        $_SESSION["usuario"] = 15; // Usuario por defecto para mesa 10
        $_SESSION["tipo_usuario"] = 1;
        $_SESSION["perfil"] = "administrador";
    }
    
    // Incluir archivos necesarios
    require_once "../../models/conexion.php";
    require_once "../../models/crudFacturaAja.php";
    
    // Validar datos POST
    if (!isset($_POST['mesa']) || !isset($_POST['total']) || !isset($_POST['pago'])) {
        throw new Exception("Datos POST incompletos");
    }
    
    $mesa = $_POST['mesa'];
    $total = $_POST['total'];
    $efectivo = $_POST['efectivo'] ?? 0;
    $tarjeta = $_POST['tarjeta'] ?? 0;
    $nequi = $_POST['nequi'] ?? 0;
    $daviplata = $_POST['daviplata'] ?? 0;
    $bancolombia = $_POST['bancolombia'] ?? 0;
    $propina = $_POST['propina'] ?? 0;
    $totalDescuento = $_POST['totalDescuento'] ?? 0;
    $cedula = $_POST['pcedula'] ?? 'MARCO';
    $pago = $_POST['pago'] ?? 1;
    
    error_log("FACTURACIÓN SIMPLE V2 - Parámetros: Mesa=$mesa, Total=$total, Efectivo=$efectivo");
    
    // Validar pago
    $totalPagado = $efectivo + $tarjeta + $nequi + $daviplata + $bancolombia + $totalDescuento;
    
    if ($totalPagado < $total) {
        $faltante = $total - $totalPagado;
        echo "<script>alert('Pago insuficiente. Falta: $" . number_format($faltante) . "')</script>";
        exit;
    }
    
    error_log("FACTURACIÓN SIMPLE V2 - Validación de pago exitosa");
    
    // PROCESAMIENTO ULTRA-SIMPLIFICADO
    $db = Conexion::conectar();
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Configuración extrema para mesas pesadas
    $db->exec("SET SESSION innodb_lock_wait_timeout = 600");
    $db->exec("SET SESSION lock_wait_timeout = 600");
    
    $db->beginTransaction();
    
    try {
        error_log("FACTURACIÓN SIMPLE V2 - Iniciando transacción");
        
        // 1. Obtener productos para facturar (SIMPLIFICADO)
        $productos = DatosFacturaAja::obtenerProductosParaFacturar($mesa);
        $totalProductos = count($productos);
        error_log("FACTURACIÓN SIMPLE V2 - Productos obtenidos: $totalProductos");
        
        if ($totalProductos == 0) {
            throw new Exception("No hay productos para facturar");
        }
        
        // 2. Crear nuevo pedido de facturación
        $mesero = $productos[0]["mesero"] ?? $_SESSION["usuario"];
        $fecha = date('Y-m-d H:i:s');
        
        $sql = "INSERT INTO pedidos (mesero_id, facturado, cedula_cliente, fecha_pedido) VALUES (?, 'n', ?, ?)";
        $stmt = $db->prepare($sql);
        $stmt->execute([$mesero, $cedula, $fecha]);
        $pedidoId = $db->lastInsertId();
        
        error_log("FACTURACIÓN SIMPLE V2 - Pedido creado: $pedidoId");
        
        // 3. Insertar productos SIMPLIFICADO con manejo de duplicados
        $sqlProducto = "INSERT INTO pedido_productos_mesa (productos_id, mesas_id, pedidos_id, cantidad, precio, descuento, codigo_descuento) VALUES (?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE cantidad = cantidad + VALUES(cantidad), precio = VALUES(precio), descuento = VALUES(descuento)";
        $stmtProducto = $db->prepare($sqlProducto);

        $totalCalculado = 0;
        $productosInsertados = 0;
        foreach ($productos as $producto) {
            $descuento = ($producto["preciopr"] * ($producto["descuentopvm"] / 100));
            $precioConDescuento = $producto["preciopr"] - $descuento;
            $subtotal = $precioConDescuento * $producto["cantidadpvm"];
            $totalCalculado += $subtotal;

            try {
                $stmtProducto->execute([
                    $producto["idpr"],
                    $producto["idmesa"],
                    $pedidoId,
                    $producto["cantidadpvm"],
                    $producto["preciopr"],
                    $producto["descuentopvm"],
                    $producto["pvmcodigo_descuento"]
                ]);
                $productosInsertados++;
            } catch (PDOException $e) {
                error_log("FACTURACIÓN SIMPLE V2 - Error insertando producto " . $producto["idpr"] . ": " . $e->getMessage());
                // Continuar con el siguiente producto
            }
        }
        
        error_log("FACTURACIÓN SIMPLE V2 - Productos insertados: $productosInsertados/$totalProductos, Total calculado: $totalCalculado");
        
        // 4. Crear venta SIMPLIFICADA
        $turnoSql = "SELECT id FROM turnos_cajeros WHERE persona_id = ? AND fecha_final = '0000-00-00 00:00:00' ORDER BY id DESC LIMIT 1";
        $turnoStmt = $db->prepare($turnoSql);
        $turnoStmt->execute([$_SESSION["usuario"]]);
        $turno = $turnoStmt->fetchColumn() ?: 1;
        
        $iva = 8;
        $totalFinal = $totalCalculado + $propina;
        $cambio = $totalPagado - $totalFinal;
        
        $ventaSql = "INSERT INTO ventas (pedidos_id, turno_cajero_id, valor, fecha_venta, subtotal, iva, total, efectivo, bancolombia, nequi, daviplata, valortarjeta, cambio, propina, mesa) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $ventaStmt = $db->prepare($ventaSql);
        $ventaStmt->execute([
            $pedidoId, $turno, $totalCalculado, $fecha, $totalCalculado, $iva, $totalFinal,
            $efectivo, $bancolombia, $nequi, $daviplata, $tarjeta, $cambio, $propina, $mesa
        ]);
        
        $facturaId = $db->lastInsertId();
        error_log("FACTURACIÓN SIMPLE V2 - Venta creada: $facturaId");
        
        // 5. Actualizar estado de pedidos a facturado
        $updatePedidos = "UPDATE pedidos SET estado = 'facturado', fecha_entrega = NOW() WHERE mesa_id = ? AND estado IN ('borrador', 'enviado', 'entregado')";
        $updateStmt = $db->prepare($updatePedidos);
        $updateStmt->execute([$mesa]);
        
        error_log("FACTURACIÓN SIMPLE V2 - Pedidos marcados como facturados");
        
        // 6. Limpiar mesa SIMPLIFICADO
        $db->exec("DELETE FROM producto_vendido_mesa WHERE mesas_id = $mesa");
        $db->exec("UPDATE mesas SET descripcion = '', estado = '' WHERE id = $mesa");
        
        error_log("FACTURACIÓN SIMPLE V2 - Mesa limpiada");
        
        // 7. Configurar variables de sesión
        $_SESSION["pedidos_id"] = $pedidoId;
        $_SESSION["turno_cajero_id"] = $turno;
        $_SESSION["subtotal"] = $totalCalculado;
        $_SESSION["total"] = $totalFinal;
        $_SESSION['idFactura'] = $facturaId;
        $_SESSION['propina'] = $propina;
        $_SESSION['mesaFactura'] = $mesa;
        $_SESSION["efectivo"] = $efectivo;
        $_SESSION["tarjeta"] = $tarjeta;
        $_SESSION["bancolombia"] = $bancolombia;
        $_SESSION["nequi"] = $nequi;
        $_SESSION["daviplata"] = $daviplata;
        $_SESSION["cambio"] = $cambio;
        
        $db->commit();
        
        error_log("FACTURACIÓN SIMPLE V2 COMPLETADA - Mesa: $mesa, Factura: $facturaId");
        
        // Respuesta de éxito
        echo '<script>alert("✅ Facturación V2 completada exitosamente\\n\\nMesa 10 liberada\\nFactura: ' . $facturaId . '\\n\\nProductos insertados: ' . $productosInsertados . '/' . $totalProductos . '\\nTotal: $' . number_format($totalCalculado) . '");window.open("pdf","_blank");location.href ="mesa";</script>';
        echo "success_simple_v2_mesa_$mesa";
        
    } catch (Exception $e) {
        $db->rollBack();
        error_log("FACTURACIÓN SIMPLE V2 ERROR: " . $e->getMessage());
        echo "<script>alert('Error en facturación V2: " . addslashes($e->getMessage()) . "')</script>";
        echo "error_simple_v2: " . $e->getMessage();
    }
    
} catch (Exception $e) {
    error_log("FACTURACIÓN SIMPLE V2 ERROR GENERAL: " . $e->getMessage());
    echo "<script>alert('Error general V2: " . addslashes($e->getMessage()) . "')</script>";
    echo "error_general_v2: " . $e->getMessage();
}
?>
