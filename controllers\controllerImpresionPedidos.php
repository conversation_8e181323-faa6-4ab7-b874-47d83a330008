<?php

class ControllerImpresionPedidos {
    
    /*=============================================
    IMPRIMIR PEDIDO POR CATEGORIA
    =============================================*/
    public function imprimirPedidoPorCategoriaController($pedidoId) {

        try {
            // Obtener impresoras desde el modelo
            $impresoras = DatosImpresionPedidos::obtenerImpresorasModel("impresoras");
            $impresorasArray = array();

            if (empty($impresoras)) {
                // Si no hay impresoras configuradas, log y continuar
                error_log("No hay impresoras configuradas para el pedido $pedidoId");
                return true; // Retornar true para no bloquear el envío
            }

            foreach ($impresoras as $imp) {
                $impresorasArray[$imp['categoria']] = array(
                    'ip' => $imp['ip'],
                    'port' => $imp['puerto']
                );
            }

            // Obtener productos del pedido
            $productos = DatosImpresionPedidos::obtenerProductosPedidoModel($pedidoId);

            if (empty($productos)) {
                error_log("No hay productos en el pedido $pedidoId para imprimir");
                return false;
            }

            // Agrupar productos por categoría
            $porCategoria = array();
            $mesaNumero = '';

            foreach ($productos as $producto) {
                $cat = $producto['categoria'] ? $producto['categoria'] : 'cocina';
                if (!isset($porCategoria[$cat])) {
                    $porCategoria[$cat] = array();
                }
                $porCategoria[$cat][] = $producto;

                // Obtener número de mesa (solo una vez)
                if (empty($mesaNumero)) {
                    $mesaNumero = $producto['mesa_numero'];
                }
            }

            // Imprimir en cada impresora correspondiente
            foreach ($porCategoria as $categoria => $items) {
                if (isset($impresorasArray[$categoria])) {
                    $this->imprimirEnImpresoraController(
                        $impresorasArray[$categoria]['ip'],
                        $impresorasArray[$categoria]['port'],
                        $items,
                        $categoria,
                        $mesaNumero,
                        $pedidoId
                    );
                } else {
                    error_log("No hay impresora configurada para la categoría: $categoria");
                }
            }

            return true;

        } catch (Exception $e) {
            error_log("Error en impresión del pedido $pedidoId: " . $e->getMessage());
            return true; // Retornar true para no bloquear el envío del pedido
        }
    }
    
    /*=============================================
    IMPRIMIR FACTURA POR CATEGORIA
    =============================================*/
    public function imprimirFacturaPorCategoriaController($pedidoId) {
        
        // Obtener impresoras desde el modelo
        $impresoras = DatosImpresionPedidos::obtenerImpresorasModel("impresoras");
        $impresorasArray = array();
        
        foreach ($impresoras as $imp) {
            $impresorasArray[$imp['categoria']] = array(
                'ip' => $imp['ip'],
                'port' => $imp['puerto']
            );
        }
        
        // Obtener productos de la facturación
        $productos = DatosImpresionPedidos::obtenerProductosFacturacionModel($pedidoId);
        
        if (empty($productos)) {
            return false;
        }
        
        // Agrupar productos por categoría
        $porCategoria = array();
        
        foreach ($productos as $producto) {
            $cat = $producto['categoria'] ? $producto['categoria'] : 'cocina';
            if (!isset($porCategoria[$cat])) {
                $porCategoria[$cat] = array();
            }
            $porCategoria[$cat][] = $producto;
        }
        
        // Imprimir en cada impresora correspondiente
        $mesa = isset($_SESSION["mesa"]) ? $_SESSION["mesa"] : 'N/A';
        
        foreach ($porCategoria as $categoria => $items) {
            if (isset($impresorasArray[$categoria])) {
                $this->imprimirFacturaEnImpresoraController(
                    $impresorasArray[$categoria]['ip'],
                    $impresorasArray[$categoria]['port'],
                    $items,
                    $categoria,
                    $mesa,
                    $pedidoId
                );
            }
        }
        
        return true;
    }
    
    /*=============================================
    IMPRIMIR EN IMPRESORA ESPECIFICA - PEDIDO
    =============================================*/
    private function imprimirEnImpresoraController($ip, $port, $productos, $categoria, $mesa, $pedidoId) {
        
        // Crear contenido del ticket
        $contenido = "\n";
        $contenido .= "================================\n";
        $contenido .= "       NUEVO PEDIDO\n";
        $contenido .= "    " . strtoupper($categoria) . "\n";
        $contenido .= "================================\n";
        $contenido .= "FECHA: " . date('d/m/Y H:i:s') . "\n";
        $contenido .= "MESA: " . $mesa . "\n";
        $contenido .= "PEDIDO: " . $pedidoId . "\n";
        $contenido .= "--------------------------------\n";
        
        foreach ($productos as $p) {
            $contenido .= sprintf("%-20s x%s\n", 
                substr($p['nombre'], 0, 20), 
                $p['cantidad']
            );
            
            // Agregar nota si existe
            if (!empty($p['nota'])) {
                $contenido .= "  NOTA: " . $p['nota'] . "\n";
            }
        }
        
        $contenido .= "--------------------------------\n";
        $contenido .= "TOTAL ITEMS: " . count($productos) . "\n";
        $contenido .= "================================\n";
        $contenido .= "\n\n\n\n\n";
        
        // Enviar a impresora
        $this->enviarAImpresoraController($ip, $port, $contenido);
    }
    
    /*=============================================
    IMPRIMIR EN IMPRESORA ESPECIFICA - FACTURA
    =============================================*/
    private function imprimirFacturaEnImpresoraController($ip, $port, $productos, $categoria, $mesa, $pedidoId) {
        
        // Crear contenido del ticket de facturación
        $contenido = "\n";
        $contenido .= "================================\n";
        $contenido .= "      FACTURA - COCINA\n";
        $contenido .= "    " . strtoupper($categoria) . "\n";
        $contenido .= "================================\n";
        $contenido .= "FECHA: " . date('d/m/Y H:i:s') . "\n";
        $contenido .= "MESA: " . $mesa . "\n";
        $contenido .= "PEDIDO: " . $pedidoId . "\n";
        $contenido .= "--------------------------------\n";
        
        foreach ($productos as $p) {
            $contenido .= sprintf("%-20s x%s\n", 
                substr($p['nombre'], 0, 20), 
                $p['cantidad']
            );
        }
        
        $contenido .= "--------------------------------\n";
        $contenido .= "*** PEDIDO FACTURADO ***\n";
        $contenido .= "================================\n";
        $contenido .= "\n\n\n\n\n";
        
        // Enviar a impresora
        $this->enviarAImpresoraController($ip, $port, $contenido);
    }
    
    /*=============================================
    ENVIAR DATOS A IMPRESORA TCP/IP
    =============================================*/
    private function enviarAImpresoraController($ip, $port, $contenido) {
        
        // Intentar conexión con timeout de 5 segundos
        $fp = @fsockopen($ip, $port, $errno, $errstr, 5);
        
        if ($fp) {
            // Enviar comandos ESC/POS para impresora térmica
            $init = chr(27) . chr(64); // Inicializar impresora
            $cut = chr(29) . chr(86) . chr(65) . chr(3); // Cortar papel
            
            fwrite($fp, $init);
            fwrite($fp, $contenido);
            fwrite($fp, $cut);
            fclose($fp);
            
            return true;
        } else {
            // Log del error (opcional)
            error_log("Error conectando a impresora $ip:$port - $errstr ($errno)");
            return false;
        }
    }
    
    /*=============================================
    OBTENER ULTIMO PEDIDO DE MESA
    =============================================*/
    public function obtenerUltimoPedidoMesaController($mesaId) {
        $respuesta = DatosImpresionPedidos::obtenerUltimoPedidoMesaModel($mesaId);
        return $respuesta;
    }

    /*=============================================
    REIMPRIMIR CATEGORIA ESPECIFICA DE PEDIDO
    =============================================*/
    public function reimprimirCategoriaPedidoController($pedidoId, $categoria) {

        // Obtener impresoras desde el modelo
        $impresoras = DatosImpresionPedidos::obtenerImpresorasModel("impresoras");
        $impresoraCategoria = null;

        foreach ($impresoras as $imp) {
            if ($imp['categoria'] == $categoria) {
                $impresoraCategoria = array(
                    'ip' => $imp['ip'],
                    'port' => $imp['puerto']
                );
                break;
            }
        }

        if (!$impresoraCategoria) {
            return false;
        }

        // Obtener productos del pedido para esta categoría
        $productos = DatosImpresionPedidos::obtenerProductosPedidoModel($pedidoId);

        if (empty($productos)) {
            return false;
        }

        // Filtrar productos por categoría
        $productosFiltrados = array();
        $mesaNumero = '';

        foreach ($productos as $producto) {
            if ($producto['categoria'] == $categoria) {
                $productosFiltrados[] = $producto;

                // Obtener número de mesa (solo una vez)
                if (empty($mesaNumero)) {
                    $mesaNumero = $producto['mesa_numero'];
                }
            }
        }

        if (empty($productosFiltrados)) {
            return false;
        }

        // Imprimir con marca de reimpresión
        $this->imprimirReimpresionEnImpresoraController(
            $impresoraCategoria['ip'],
            $impresoraCategoria['port'],
            $productosFiltrados,
            $categoria,
            $mesaNumero,
            $pedidoId
        );

        return true;
    }

    /*=============================================
    IMPRIMIR REIMPRESION EN IMPRESORA ESPECIFICA
    =============================================*/
    private function imprimirReimpresionEnImpresoraController($ip, $port, $productos, $categoria, $mesa, $pedidoId) {

        // Crear contenido del ticket de reimpresión
        $contenido = "\n";
        $contenido .= "================================\n";
        $contenido .= "    *** REIMPRESION ***\n";
        $contenido .= "       " . strtoupper($categoria) . "\n";
        $contenido .= "================================\n";
        $contenido .= "FECHA: " . date('d/m/Y H:i:s') . "\n";
        $contenido .= "MESA: " . $mesa . "\n";
        $contenido .= "PEDIDO: " . $pedidoId . "\n";
        $contenido .= "--------------------------------\n";

        foreach ($productos as $p) {
            $contenido .= sprintf("%-20s x%s\n",
                substr($p['nombre'], 0, 20),
                $p['cantidad']
            );

            // Agregar nota si existe
            if (!empty($p['nota'])) {
                $contenido .= "  NOTA: " . $p['nota'] . "\n";
            }
        }

        $contenido .= "--------------------------------\n";
        $contenido .= "*** REIMPRESION ***\n";
        $contenido .= "TOTAL ITEMS: " . count($productos) . "\n";
        $contenido .= "================================\n";
        $contenido .= "\n\n\n\n\n";

        // Enviar a impresora
        $this->enviarAImpresoraController($ip, $port, $contenido);
    }
}