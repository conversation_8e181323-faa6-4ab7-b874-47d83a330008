<?php
// Versión simple de ajaxFactura.php con mejor manejo de errores
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// OPTIMIZACIÓN PARA MESAS PESADAS
ini_set('max_execution_time', 300); // 5 minutos
ini_set('memory_limit', '512M');

echo "<!-- SIMPLE: Iniciando ajaxFactura_simple.php -->\n";

try {
    echo "<!-- SIMPLE: Incluyendo archivos -->\n";
    
    require_once "../../models/crud.php";
    echo "<!-- SIMPLE: crud.php incluido -->\n";
    
    require_once "../../models/crudFacturaAja.php";
    echo "<!-- SIMPLE: crudFacturaAja.php incluido -->\n";
    
    require_once "../../controllers/controller.php";
    echo "<!-- SIMPLE: controller.php incluido -->\n";
    
    require_once "../../controllers/controllerFacturaAja.php";
    echo "<!-- SIMPLE: controllerFacturaAja.php incluido -->\n";

    // Log para debug de mesas pesadas
    error_log("FACTURACIÓN SIMPLE INICIADA - Mesa: " . ($_POST['mesa'] ?? 'N/A') . " - Timestamp: " . date('Y-m-d H:i:s'));
    echo "<!-- SIMPLE: Log inicial registrado -->\n";

    // Verificar que tenemos los datos necesarios
    if (!isset($_POST['mesa']) || !isset($_POST['pago']) || !isset($_POST['total'])) {
        throw new Exception("Datos POST incompletos");
    }

    // COMPATIBILIDAD: Manejar tanto el formato original como el nuevo
    $nequi = isset($_POST['nequi']) ? $_POST['nequi'] : 0;
    $daviplata = isset($_POST['daviplata']) ? $_POST['daviplata'] : 0;
    $bancolombia = isset($_POST['bancolombia']) ? $_POST['bancolombia'] : 0;
    $tipoTarjeta = isset($_POST['tipoTarjeta']) ? $_POST['tipoTarjeta'] : 'credito';
    $optimizada = isset($_POST['optimizada']) ? $_POST['optimizada'] : false;

    $queryString = $_POST['efectivo'] ?? 0;
    $tarjeta = $_POST['tarjeta'] ?? 0;
    $queryString1 = $_POST['pago'] ?? 1;
    $queryString2 = $_POST['pcedula'] ?? '';
    $queryString3 = $_POST['total'] ?? 0;
    $totalDescuento = isset($_POST['totalDescuento']) ? $_POST['totalDescuento'] : 0;
    $propina = isset($_POST['propina']) ? $_POST['propina'] : 0;
    $mesa = $_POST['mesa'] ?? 0;

    echo "<!-- SIMPLE: Variables asignadas -->\n";
    echo "<!-- SIMPLE: Mesa: $mesa, Total: $queryString3, Efectivo: $queryString -->\n";

    if($queryString1 == 1) {
        echo "<!-- SIMPLE: Procesando pago en efectivo -->\n";
        
        $totalCuenta = $queryString3;
        $totalPagado = $queryString + $tarjeta + $nequi + $daviplata + $bancolombia + $totalDescuento;
        
        echo "<!-- SIMPLE: Total cuenta: $totalCuenta, Total pagado: $totalPagado -->\n";
        
        error_log("FACTURACIÓN SIMPLE - Mesa: $mesa, Total: $totalCuenta, Pagado: $totalPagado");

        if($totalPagado >= $totalCuenta) {
            echo "<!-- SIMPLE: Validación de pago exitosa -->\n";
            
            $tiempoInicio = microtime(true);
            error_log("PROCESAMIENTO SIMPLE INICIADO - Mesa: $mesa");

            try {
                echo "<!-- SIMPLE: Creando controlador original -->\n";
                $ajax = new controllerFacturaAja();
                echo "<!-- SIMPLE: Controlador original creado exitosamente -->\n";
                
                echo "<!-- SIMPLE: Llamando facturaajaxController original -->\n";
                
                // Capturar cualquier output del controlador
                ob_start();
                $resultado = $ajax->facturaajaxController($queryString, $nequi, $daviplata, $bancolombia, $tarjeta, $queryString1, $queryString2, $propina, $mesa);
                $outputControlador = ob_get_clean();
                
                echo "<!-- SIMPLE: facturaajaxController completado con resultado: $resultado -->\n";
                echo "<!-- SIMPLE: Output del controlador: $outputControlador -->\n";

                $tiempoFin = microtime(true);
                $tiempoTotal = round(($tiempoFin - $tiempoInicio) * 1000, 2);
                error_log("FACTURACIÓN SIMPLE COMPLETADA - Mesa: $mesa, Tiempo: {$tiempoTotal}ms");

                // Mostrar el output del controlador si existe
                if (!empty($outputControlador)) {
                    echo $outputControlador;
                }

                // Respuesta exitosa explícita
                if ($resultado == "success") {
                    echo "success_simple_mesa_$mesa";
                } else {
                    echo "error_simple_mesa_$mesa: $resultado";
                }

            } catch (Exception $e) {
                echo "<!-- SIMPLE: Error en controlador: " . $e->getMessage() . " -->\n";
                echo "<!-- SIMPLE: Stack trace: " . $e->getTraceAsString() . " -->\n";
                error_log("ERROR SIMPLE EN FACTURACIÓN - Mesa: $mesa, Error: " . $e->getMessage());
                error_log("ERROR SIMPLE Stack trace: " . $e->getTraceAsString());
                echo "<script>alert('Error simple en facturación: " . addslashes($e->getMessage()) . "')</script>";
            } catch (Error $e) {
                echo "<!-- SIMPLE: Error fatal: " . $e->getMessage() . " -->\n";
                echo "<!-- SIMPLE: Stack trace: " . $e->getTraceAsString() . " -->\n";
                error_log("ERROR SIMPLE FATAL - Mesa: $mesa, Error: " . $e->getMessage());
                error_log("ERROR SIMPLE FATAL Stack trace: " . $e->getTraceAsString());
                echo "<script>alert('Error simple fatal: " . addslashes($e->getMessage()) . "')</script>";
            }
        } else {
            echo "<!-- SIMPLE: Pago insuficiente -->\n";
            $faltante = $totalCuenta - $totalPagado;
            error_log("PAGO SIMPLE INSUFICIENTE - Mesa: $mesa, Falta: $faltante");
            echo "<script>alert('Pago insuficiente. Total: $" . number_format($totalCuenta) . ", Pagado: $" . number_format($totalPagado) . ", Falta: $" . number_format($faltante) . "')</script>";
        }
    } else {
        echo "<!-- SIMPLE: Procesando pago a crédito -->\n";
        echo "<script>alert('Pago a crédito en modo simple')</script>";
        $ajax = new controllerFacturaAja();
        $ajax->facturaajaxController($queryString, $nequi, $daviplata, $bancolombia, $tarjeta, $queryString1, $queryString2, $propina, $mesa);
    }

} catch (ParseError $e) {
    echo "<!-- SIMPLE: Error de sintaxis: " . $e->getMessage() . " -->\n";
    error_log("ERROR SIMPLE SINTAXIS: " . $e->getMessage());
    http_response_code(500);
    echo "Error de sintaxis: " . $e->getMessage();
} catch (Error $e) {
    echo "<!-- SIMPLE: Error fatal: " . $e->getMessage() . " -->\n";
    error_log("ERROR SIMPLE FATAL: " . $e->getMessage());
    http_response_code(500);
    echo "Error fatal: " . $e->getMessage();
} catch (Exception $e) {
    echo "<!-- SIMPLE: Excepción: " . $e->getMessage() . " -->\n";
    error_log("ERROR SIMPLE EXCEPCIÓN: " . $e->getMessage());
    http_response_code(500);
    echo "Excepción: " . $e->getMessage();
}

echo "<!-- SIMPLE: Finalizando ajaxFactura_simple.php -->\n";
?>
