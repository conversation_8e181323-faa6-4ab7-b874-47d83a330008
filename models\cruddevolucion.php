<?php

	#EXTENSIÓN DE CLASES: Los objetos pueden ser extendidos, y pueden heredar propiedades y métodos. Para definir una clase como extensión, debo definir una clase padre, y se utiliza dentro de una clase hija.

	require_once "conexion.php";
	require_once "crudPedidoMesaVendido.php";
	require_once "crudTurno.php";
	require_once "crud.php";
	

	class Datosdevolucion extends Conexion
		{
			
			#	devolucion  #idproducto idPedidon fid cantidad precio
			#------------------------------------			
			
				public static function registroDevolucionModel( $datosModel)
				 	{	
				 		/*echo "<script>alert('Entro crud producto; ".$datosModel["idproducto"]."');</script>";
				 		$devolucion = buscarDevolucionModel($datosModel["fid"], "devoluciones");
				 		$sigue = 1;

				 		echo "<script>alert('Entro crud producto; ".$sigue."');</script>";
				 			foreach ($devolucion as $rows => $value) 
						 		{
						 			if ($value["dfacturaid"] == $datosController["fid"] && $value["dproductoid"] == $datosController["idproducto"] && $value["dcantidad"] >= $datosController["cantidad"] ) 
							 			{
							 				$sigue = 0;
							 			}
						 		}*/
				 		/*if ($sigue == 1) 
				 			{*/
							  /////////////////// cargas la lista de todo los Suministro///////////////////
								$consultaSuministro  = Datos::productoListaSuministroDevolucionModel($datosModel["idproducto"]);
								//var_dump($consultaSuministro);
								$i = 0;
									
									$cantidadP = $datosModel["cantidad"] * $consultaSuministro["spcantidad"] ; echo "<br> --".$consultaSuministro["spcantidad"]."<br>";
									$sumi =  $consultaSuministro["sid"]; echo "<br> --".$sumi."<br>";
									$precio = $consultaSuministro["precio"]*$datosModel["cantidad"]; 
									$turno	= DatosTurno::editarTurnoModel($datosModel["usuario"], "turnos_cajeros");
											
							  ////////////////// cargas la lista ///////////////////	
								$stmt = Conexion::conectar(); 
															
								date_default_timezone_set("America/Bogota");
								$fecha_devolucion=strftime("%Y-%m-%d %H:%M:%S");
								//echo "<script>alert('Entro crud Usuario ".$fecha_factura."  ');</script>";
								
								try{	
										echo "<script>alert('Entro  try');</script>";								
										$stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);  
										$stmt->beginTransaction();
										//  Insertando en la tabla devoluciones/									 
										$consulta = "INSERT INTO devoluciones(turnos_cajeros_id, ventas_id, motivo, producto_id, cantidad, fecha_devolucion, precio, pedido_id) VALUES (".$turno["id"].", ".$datosModel["fid"].", 'Devolución', ".$datosModel["idproducto"].", ".$datosModel["cantidad"].", '".$fecha_devolucion."', ".$datosModel["precio"].", ".$datosModel["idpedido"].")";
										//echo "<script>alert('insertar tabla venta o factura ".$consulta2." ' );</script>";
										echo " --".$datosModel["idpedido"];
										echo "<br> --".$consulta;
										$stmt->exec($consulta);	

										$producto_id = $datosModel["idproducto"]; //echo "<br> --".$producto_id."<br>";
										$pedidos_id= $datosModel["idpedido"]; //echo "<br> --".$pedidos_id."<br>";
										$consulta2="UPDATE  pedido_productos_mesa  SET  cantidad=cantidad - $cantidadP  WHERE productos_id=$producto_id and pedidos_id=$pedidos_id ";
										echo "<br><br> ".$consulta2." <br>";
										$stmt->exec($consulta2);
											
										$consulta3="UPDATE sucursal SET  cantidad= cantidad + $cantidadP  WHERE suministro_id = ".$sumi;
										echo "<br><br> ".$consulta3." <br>";
										$stmt->exec($consulta3);

										$consulta4="UPDATE ventas SET  subtotal=subtotal - $precio WHERE id=".$datosModel["fid"]; 
										echo "<br><br> ".$consulta4." <br>";
										$stmt->exec($consulta4);

										//echo "<script>alert('fin de cruz try')</script>";
										$stmt->commit();
										//echo "<script>alert('fin de cruD try que pasa');</script>";
										return "success"; 
										$stmt->close();						
									} 
								catch (PDOException $e)
									{	echo "<script>alert('catch entro')</script>";
										  $stmt->rollBack(); 
										print "Error!: ".$e->getMessage()."</br>";
										return "Error!: ".$e->getMessage()."</br>";   
									} 
					/*	}
						else
						return "success";*/
					
					}
			#------------------------------------
			
			#BUSCAR DEVOLUCIONES DE UNA FACTURA
			#--------------------------------------
				public static function buscarDevolucionModel($factura, $tabla)
					{	
						echo "<script>alert('Entro CRUD=".$factura." forma ');</script>";
						$consulta="SELECT d.id AS did, d.turnos_cajeros_id AS dturno, d.ventas_id AS dfacturaid, d.motivo AS dmotivo, d.producto_id AS dproductoid, d.cantidad AS dcantidad, d.fecha_devolucion AS dfecha, d.precio AS dprecio, d.pedido_id AS dpedido, pro.nombre AS pronombre FROM devoluciones d, productos pro WHERE pro.id = d.producto_id AND ventas_id=$factura";		
							//echo $consulta;
							//$stmt->bindParam(":facturaid", $factura, PDO::PARAM_INT);										
							$stmt = Conexion::conectar()->prepare($consulta);
							$stmt->execute();
							$r=$stmt->fetchAll();

							$c=$stmt->rowCount();
							//echo '<br> C2='.$c2.'<br>';
							if ($c>0) 
								{	return $r;	}
							else
								{	return 0;	}
							$stmt->close();
					}
			#--------------------------------------
			
			#BUSCAR TODAS LAS DEVOLUCIONES
			#--------------------------------------
				public static function devolucionTotalModel()
					{	
						echo "<script>alert('Entro CRUD=".$factura." forma ');</script>";
						$consulta="SELECT id, turnos_cajeros_id, ventas_id, motivo, producto_id, cantidad, fecha_devolucion, precio, pedido_id 
								FROM devoluciones";
					
							//echo $consulta;
													
							$stmt = Conexion::conectar()->prepare($consulta);
							$stmt->execute();
							$r=$stmt->fetchAll();

							$c2=$stmt->rowCount();
							//echo '<br> C2='.$c2.'<br>';
							if ($c2>0) 
								{	return $r;	}
							else
								{	return 0;	}
							$stmt->close();
					}
			#--------------------------------------

			#BUSCAR FACTURA
			#--------------------------------------
				public static function buscarFacturadevolucionModel($factura)
					{	
						echo "<script>alert('Entro CRUD=".$factura." forma ');</script>";
						$consulta="SELECT ppm.productos_id as productoid, ppm.cantidad as cantidad, ppm.precio as precio, f.pedidos_id as pedido, f.fecha_venta as fechaventa, f.total as total,  f.id as fid, pr.nombre as prnombre from ventas f, pedidos p, pedido_productos_mesa ppm, productos pr where pr.id=ppm.productos_id AND f.pedidos_id=p.id AND p.id=ppm.pedidos_id  AND f.id=$factura";
					
							//echo $consulta;
													
							$stmt = Conexion::conectar()->prepare($consulta);
							$stmt->execute();
							$r=$stmt->fetchAll();

							$c2=$stmt->rowCount();
							//echo '<br> C2='.$c2.'<br>';
							if ($c2>0) 
								{	return $r;	}
							else
								{	return 0;	}
							$stmt->close();
					}
			#--------------------------------------
			#
			#EDITAR DEVOLUCIÓN
			#-------------------------------------
				public static function parcialDevolucionModel($datosModel, $tabla)
					{
						$stmt = Conexion::conectar()->prepare("SELECT productos_id, pedidos_id, cantidad  FROM $tabla WHERE productos_id=:productos_id and pedidos_id=:pedidos_id");
						$stmt->bindParam(":productos_id", $datosModel["idproducto"], PDO::PARAM_INT);	
						$stmt->bindParam(":pedidos_id", $datosModel["idpedido"], PDO::PARAM_INT);	
						//$stmt->bindParam(":proos_id", $datosModel["pedido"], PDO::PARAM_INT);	
						$stmt->execute();
						return $stmt->fetch();
						$stmt->close();
					}

			#ACTUALIZAR DEVOLUCIÓN
			#-------------------------------------
				public static function actualizarDevolucionModel($datosModel, $tabla)
					{
						echo "<script>alert('entro cruz Suministro')</script>";
						$consulta = "UPDATE $tabla SET  cantidad = :cantidad WHERE id = :id ";
						echo "<script>alert('entro cruz Suministro ".$consulta."')</script>";

						$stmt = Conexion::conectar()->prepare($consulta);			
									
						$stmt->bindParam(":id", $datosModel["id"], PDO::PARAM_INT);				
						$stmt->bindParam(":cantidad", $datosModel["cantidad"], PDO::PARAM_INT);	

						echo "<script>alert('centro proceso')</script>";
						if($stmt->execute())
							{echo "<script>alert('Guardo Actualizar Proveedor')</script>";return "success";	}
						else{echo "<script>alert('Error base de dato serve')</script>";	return "error";			}
						$stmt->close();
					}
				#---------------------------------------------

			#EDITAR DEVOLUCIÓN
			#-------------------------------------
				public static function editarDevolucionModel($datosModel, $tabla)
					{
						$stmt = Conexion::conectar()->prepare("SELECT  id, cantidad  FROM $tabla WHERE id=:id ");
						$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);	
							
						//$stmt->bindParam(":proos_id", $datosModel["pedido"], PDO::PARAM_INT);	
						$stmt->execute();
						return $stmt->fetch();
						$stmt->close();
					}
			#--------------------------------------------

			##ELIMINAR
				#------------------------------------
					public static function eliminarDevolucionModel($datosModel, $tabla)
						{
							$stmt = Conexion::conectar()->prepare("DELETE FROM $tabla WHERE id = :id");
							$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);
							if($stmt->execute())
								{	return "success";	}
							else{			return "error";		}
							$stmt->close();
						}
				#----------------------------------------------	
			#----------------------------------------------
			#BUSCAR Ultimas facturas
			#--------------------------------------
			 public static function ultimasFacturaModel($sucursal)
				{

					if($sucursal==1){$ventas='ventas';}
					else if($sucursal==2){$ventas='ventas2';}
					else if($sucursal==3){$ventas='ventas';}
					else if($sucursal==4){$ventas='ventas4';}
					else if($sucursal==5){$ventas='ventas5';}
					else if($sucursal==6){$ventas='ventas6';}
					else if($sucursal==7){$ventas='ventas7';}
				 //echo "<script>alert('Entro CRUD=".$factura." forma ');</script>";
				 //$consulta="SELECT * FROM $ventas WHERE punto_id=$sucursal ORDER BY id DESC LIMIT 100";
				 $consulta="SELECT * FROM $ventas ORDER BY id DESC LIMIT 100";
					//echo $consulta;
				 $stmt = Conexion::conectar()->prepare($consulta);
				 $stmt->execute();
				 $r=$stmt->fetchAll();
				 $c2=$stmt->rowCount();
				 //echo '<br> C2='.$c2.'<br>';
				 if ($c2>0)
					{	return $r;	}
				 else
					{	return 0;	}
				 $stmt->close();
				}
			#----------------------------------------------
			#BUSCAR Ultimas facturas Crédito
			#--------------------------------------
			 public static function ultimasFacturaCreditoModel($sucursal)
				{

					
				 //echo "<script>alert('Entro CRUD=".$factura." forma ');</script>";
				 $consulta="SELECT id, pedidos_id, turno_cajero_id, valor, fecha_credito, subtotal, iva, total, descuento, punto_id FROM creditos ORDER BY id DESC LIMIT 100";
					//echo $consulta;
				 $stmt = Conexion::conectar()->prepare($consulta);
				 $stmt->execute();
				 $r=$stmt->fetchAll();
				 $c2=$stmt->rowCount();
				 //echo '<br> C2='.$c2.'<br>';
				 if ($c2>0)
					{	return $r;	}
				 else
					{	return 0;	}
				 $stmt->close();
				}
			#----------------------------------------------	
		}