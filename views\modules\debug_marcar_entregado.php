<?php

session_start();

if(!isset($_SESSION["validar"]) || !$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "models/crudPedidosCategorias.php";

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Debug Marcar Entregado</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
</head>
<body>

<div class="container">
    <h2>🔍 Debug: <PERSON><PERSON> En<PERSON>gado</h2>
    <hr>
    
    <div class="panel panel-danger">
        <div class="panel-heading">
            <h3 class="panel-title">🚨 Error 500 al marcar entregado</h3>
        </div>
        <div class="panel-body">
            <p><strong>Vamos a simular la función de marcar entregado para identificar el error.</strong></p>
        </div>
    </div>
    
    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">📋 Pedidos disponibles para test</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                $pedidos_bar = DatosPedidosCategorias::obtenerPedidosPendientesModel('bar');
                echo "<p><strong>Pedidos pendientes en BAR:</strong> " . count($pedidos_bar) . "</p>";
                
                if (count($pedidos_bar) > 0) {
                    echo "<table class='table table-striped'>";
                    echo "<thead><tr><th>ID</th><th>Pedido</th><th>Mesa</th><th>Productos</th><th>Acción</th></tr></thead>";
                    echo "<tbody>";
                    foreach ($pedidos_bar as $pedido) {
                        echo "<tr>";
                        echo "<td>{$pedido['pedido_id']}</td>";
                        echo "<td>{$pedido['numero_pedido']}</td>";
                        echo "<td>{$pedido['mesa_numero']}</td>";
                        echo "<td>{$pedido['productos_detalle']}</td>";
                        echo "<td><button class='btn btn-sm btn-success' onclick='testMarcarEntregado({$pedido['pedido_id']})'>Test Marcar</button></td>";
                        echo "</tr>";
                    }
                    echo "</tbody></table>";
                } else {
                    echo "<div class='alert alert-warning'>No hay pedidos pendientes para test</div>";
                }
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error al obtener pedidos: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-warning">
        <div class="panel-heading">
            <h3 class="panel-title">🧪 Test Manual: Marcar Entregado</h3>
        </div>
        <div class="panel-body">
            <div id="resultado-test"></div>
            
            <?php
            if (isset($_POST['test_pedido_id'])) {
                echo "<h4>Ejecutando test para pedido ID: {$_POST['test_pedido_id']}</h4>";
                
                try {
                    // Verificar que el usuario esté en sesión
                    if (!isset($_SESSION['usuario'])) {
                        echo "<div class='alert alert-danger'>❌ Error: No hay usuario en sesión. \$_SESSION['usuario'] no existe.</div>";
                        echo "<p><strong>Contenido de \$_SESSION:</strong></p>";
                        echo "<pre>" . print_r($_SESSION, true) . "</pre>";
                    } else {
                        echo "<div class='alert alert-info'>✅ Usuario en sesión: ID {$_SESSION['usuario']}</div>";

                        // Test de la función marcarPedidoEntregadoModel
                        $resultado = DatosPedidosCategorias::marcarPedidoEntregadoModel($_POST['test_pedido_id'], $_SESSION['usuario']);
                        
                        if ($resultado == "success") {
                            echo "<div class='alert alert-success'>✅ Pedido marcado como entregado exitosamente</div>";
                        } else {
                            echo "<div class='alert alert-danger'>❌ Error al marcar pedido: {$resultado}</div>";
                        }
                    }
                    
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>❌ Excepción capturada: " . $e->getMessage() . "</div>";
                    echo "<p><strong>Trace:</strong></p>";
                    echo "<pre>" . $e->getTraceAsString() . "</pre>";
                }
            }
            ?>
            
            <form method="POST" style="margin-top: 20px;">
                <div class="form-group">
                    <label>ID del pedido a marcar como entregado:</label>
                    <input type="number" name="test_pedido_id" class="form-control" placeholder="Ej: 4" required>
                </div>
                <button type="submit" class="btn btn-warning">🧪 Test Marcar Entregado</button>
            </form>
        </div>
    </div>
    
    <div class="panel panel-success">
        <div class="panel-heading">
            <h3 class="panel-title">🔍 Verificar tabla usuarios</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                // Verificar si existe la tabla usuarios
                $stmt = Conexion::conectar()->prepare("SHOW TABLES LIKE 'usuarios'");
                $stmt->execute();
                $tabla_usuarios = $stmt->fetch();
                
                if ($tabla_usuarios) {
                    echo "<div class='alert alert-success'>✅ La tabla 'usuarios' existe</div>";
                    
                    // Verificar estructura
                    $stmt = Conexion::conectar()->prepare("DESCRIBE usuarios");
                    $stmt->execute();
                    $estructura = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    
                    echo "<h4>Estructura de tabla usuarios:</h4>";
                    echo "<table class='table table-condensed'>";
                    echo "<thead><tr><th>Campo</th><th>Tipo</th><th>Key</th></tr></thead>";
                    echo "<tbody>";
                    foreach ($estructura as $campo) {
                        echo "<tr><td>{$campo['Field']}</td><td>{$campo['Type']}</td><td>{$campo['Key']}</td></tr>";
                    }
                    echo "</tbody></table>";
                    
                } else {
                    echo "<div class='alert alert-danger'>❌ La tabla 'usuarios' NO existe</div>";
                    echo "<p>Esto puede causar el error 500 en el LEFT JOIN con usuarios.</p>";
                    
                    // Verificar si existe tabla 'personas'
                    $stmt = Conexion::conectar()->prepare("SHOW TABLES LIKE 'personas'");
                    $stmt->execute();
                    $tabla_personas = $stmt->fetch();
                    
                    if ($tabla_personas) {
                        echo "<div class='alert alert-info'>✅ Pero la tabla 'personas' SÍ existe</div>";
                        echo "<p><strong>Posible solución:</strong> Cambiar 'usuarios' por 'personas' en las consultas.</p>";
                    }
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error al verificar tablas: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-primary">
        <div class="panel-heading">
            <h3 class="panel-title">📊 Información de sesión</h3>
        </div>
        <div class="panel-body">
            <h4>Contenido de $_SESSION:</h4>
            <pre><?php print_r($_SESSION); ?></pre>
        </div>
    </div>
    
    <hr>
    <div class="row">
        <div class="col-md-4">
            <a href="index.php?action=pedidosBarPendientes" class="btn btn-info btn-block">🔙 Volver a Bar</a>
        </div>
        <div class="col-md-4">
            <a href="index.php?action=test_enum_problema" class="btn btn-primary btn-block">🧪 Test Anterior</a>
        </div>
        <div class="col-md-4">
            <a href="index.php?action=pedidosCocinaPendientes" class="btn btn-warning btn-block">🍳 Ir a Cocina</a>
        </div>
    </div>
</div>

<script>
function testMarcarEntregado(pedidoId) {
    document.querySelector('input[name="test_pedido_id"]').value = pedidoId;
    document.querySelector('form').submit();
}
</script>

</body>
</html>
