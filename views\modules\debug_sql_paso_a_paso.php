<?php

session_start();

if(!isset($_SESSION["validar"]) || !$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "models/conexion.php";

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Debug SQL Paso a Paso</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
</head>
<body>

<div class="container">
    <h2>🔍 Debug SQL Paso a Paso</h2>
    <hr>
    
    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">Paso 1: Pedidos básicos</h3>
        </div>
        <div class="panel-body">
            <?php
            $stmt = Conexion::conectar()->prepare("SELECT id, numero_pedido, estado, mesa_id FROM pedidos WHERE estado = 'enviado'");
            $stmt->execute();
            $pedidos = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "<p><strong>Pedidos enviados:</strong> " . count($pedidos) . "</p>";
            foreach ($pedidos as $p) {
                echo "<p>- ID: {$p['id']}, Número: {$p['numero_pedido']}, Mesa: {$p['mesa_id']}</p>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-warning">
        <div class="panel-heading">
            <h3 class="panel-title">Paso 2: JOIN con mesas</h3>
        </div>
        <div class="panel-body">
            <?php
            $stmt = Conexion::conectar()->prepare("
                SELECT p.id, p.numero_pedido, p.mesa_id, m.nombre as mesa_nombre
                FROM pedidos p
                INNER JOIN mesas m ON p.mesa_id = m.id
                WHERE p.estado = 'enviado'
            ");
            $stmt->execute();
            $pedidos_mesas = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "<p><strong>Pedidos con mesas:</strong> " . count($pedidos_mesas) . "</p>";
            foreach ($pedidos_mesas as $p) {
                echo "<p>- {$p['numero_pedido']} | Mesa: {$p['mesa_nombre']} (ID: {$p['mesa_id']})</p>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-success">
        <div class="panel-heading">
            <h3 class="panel-title">Paso 3: JOIN con producto_vendido_mesa</h3>
        </div>
        <div class="panel-body">
            <?php
            $stmt = Conexion::conectar()->prepare("
                SELECT p.id, p.numero_pedido, p.mesa_id, m.nombre as mesa_nombre, pvm.id as pvm_id, pvm.productos_id
                FROM pedidos p
                INNER JOIN mesas m ON p.mesa_id = m.id
                INNER JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
                WHERE p.estado = 'enviado'
            ");
            $stmt->execute();
            $pedidos_pvm = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "<p><strong>Pedidos con productos vendidos:</strong> " . count($pedidos_pvm) . "</p>";
            foreach ($pedidos_pvm as $p) {
                echo "<p>- {$p['numero_pedido']} | Mesa: {$p['mesa_nombre']} | PVM ID: {$p['pvm_id']} | Producto ID: {$p['productos_id']}</p>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-primary">
        <div class="panel-heading">
            <h3 class="panel-title">Paso 4: JOIN completo con productos</h3>
        </div>
        <div class="panel-body">
            <?php
            $stmt = Conexion::conectar()->prepare("
                SELECT p.id, p.numero_pedido, p.mesa_id, m.nombre as mesa_nombre, 
                       pvm.id as pvm_id, pvm.productos_id, pr.nombre as producto_nombre, pr.categoria
                FROM pedidos p
                INNER JOIN mesas m ON p.mesa_id = m.id
                INNER JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
                INNER JOIN productos pr ON pvm.productos_id = pr.id
                WHERE p.estado = 'enviado'
            ");
            $stmt->execute();
            $pedidos_completo = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "<p><strong>JOIN completo:</strong> " . count($pedidos_completo) . " registros</p>";
            foreach ($pedidos_completo as $p) {
                echo "<p>- {$p['numero_pedido']} | Mesa: {$p['mesa_nombre']} | Producto: {$p['producto_nombre']} | Categoría: '{$p['categoria']}'</p>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-danger">
        <div class="panel-heading">
            <h3 class="panel-title">Paso 5: Filtro por categoría BAR</h3>
        </div>
        <div class="panel-body">
            <?php
            $stmt = Conexion::conectar()->prepare("
                SELECT p.id, p.numero_pedido, p.mesa_id, m.nombre as mesa_nombre, 
                       pvm.id as pvm_id, pvm.productos_id, pr.nombre as producto_nombre, pr.categoria
                FROM pedidos p
                INNER JOIN mesas m ON p.mesa_id = m.id
                INNER JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
                INNER JOIN productos pr ON pvm.productos_id = pr.id
                WHERE p.estado = 'enviado'
                AND pr.categoria = 'bar'
            ");
            $stmt->execute();
            $pedidos_bar = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "<p><strong>Filtro BAR:</strong> " . count($pedidos_bar) . " registros</p>";
            foreach ($pedidos_bar as $p) {
                echo "<p>- {$p['numero_pedido']} | Mesa: {$p['mesa_nombre']} | Producto: {$p['producto_nombre']} | Categoría: '{$p['categoria']}'</p>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-default">
        <div class="panel-heading">
            <h3 class="panel-title">Paso 6: Consulta con GROUP BY (como en el modelo)</h3>
        </div>
        <div class="panel-body">
            <?php
            $stmt = Conexion::conectar()->prepare("
                SELECT DISTINCT
                    p.id as pedido_id,
                    p.numero_pedido,
                    p.fecha_envio,
                    p.mesa_id,
                    m.nombre as mesa_numero,
                    COUNT(pvm.id) as total_productos,
                    GROUP_CONCAT(
                        CONCAT(pr.nombre, ' x', pvm.cantidad)
                        ORDER BY pr.nombre
                        SEPARATOR ', '
                    ) as productos_detalle,
                    SUM(pvm.cantidad * pr.precio) as total_precio
                FROM pedidos p
                INNER JOIN mesas m ON p.mesa_id = m.id
                INNER JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
                INNER JOIN productos pr ON pvm.productos_id = pr.id
                WHERE p.estado = 'enviado'
                AND pr.categoria = 'bar'
                GROUP BY p.id, p.numero_pedido, p.fecha_envio, p.mesa_id, m.nombre
                ORDER BY p.fecha_envio ASC
            ");
            $stmt->execute();
            $pedidos_agrupados = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "<p><strong>Consulta final con GROUP BY:</strong> " . count($pedidos_agrupados) . " pedidos</p>";
            foreach ($pedidos_agrupados as $p) {
                echo "<p>- {$p['numero_pedido']} | Mesa: {$p['mesa_numero']} | Productos: {$p['productos_detalle']} | Total: $" . number_format($p['total_precio'], 0) . "</p>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">Paso 7: Verificar estructura de tabla producto_vendido_mesa</h3>
        </div>
        <div class="panel-body">
            <?php
            $stmt = Conexion::conectar()->prepare("DESCRIBE producto_vendido_mesa");
            $stmt->execute();
            $estructura = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "<p><strong>Estructura de producto_vendido_mesa:</strong></p>";
            echo "<table class='table table-condensed'>";
            echo "<tr><th>Campo</th><th>Tipo</th><th>Null</th><th>Key</th><th>Default</th></tr>";
            foreach ($estructura as $campo) {
                echo "<tr><td>{$campo['Field']}</td><td>{$campo['Type']}</td><td>{$campo['Null']}</td><td>{$campo['Key']}</td><td>{$campo['Default']}</td></tr>";
            }
            echo "</table>";
            ?>
        </div>
    </div>
    
    <hr>
    <div class="row">
        <div class="col-md-6">
            <a href="index.php?action=test_consultas_corregidas" class="btn btn-primary btn-block">🔙 Volver a Test</a>
        </div>
        <div class="col-md-6">
            <a href="index.php?action=debug_consultas_pendientes" class="btn btn-success btn-block">🔍 Debug SQL</a>
        </div>
    </div>
</div>

</body>
</html>
