<?php
/*session_start();
if(!$_SESSION["validar"]){
	header("location:index.php?action=ingresar");
	exit();
}*/
date_default_timezone_set("America/Bogota");
//$fecha_actual=strftime("%Y-%m-%d %H:%M:%S");
$cadena = array("January", "February","March","April", "May", "June","July","August","September", "October","November","December");
$reemplazo = array("Enero", "Febrero","Marzo","Abril", "Mayo", "Junio","Julio","Agosto","Septiembre", "Octubre","Noviembre","Diciembre");
$fecha_actual=strftime("%Y-%B-%d %H:%M:%S"); //%B Nombre completo del mes en Ingles
$fecha_final=str_replace($cadena, $reemplazo, $fecha_actual);
?>

<h1>PRODUCTOS EN COCINA  <?=$fecha_final;?></h1>

	<table border="1" class="table table-hover" >
		
		<thead>
			
			<tr>
				
				<th>CODIGO</th>
				<th>NOMBRES</th>
				<th>CANTIDAD</th>	
			</tr>

		</thead>

		<tbody>
			
			<?php

				$vistaUsuari = new controllerSuministro();
				$vistaUsuari -> vistaSucursalCocinaController();
				//$vistaUsuari -> borrarSuministroController();

			?>

		</tbody>

	</table>

<?php

if(isset($_GET["action"]))
	{	if($_GET["action"] == "cambio")
			{	echo "Cambio Exitoso";	}
	}

?>




