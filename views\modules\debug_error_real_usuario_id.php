<?php

session_start();

if(!isset($_SESSION["validar"]) || !$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "models/conexion.php";

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Debug: Error Real usuario_id</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
</head>
<body>

<div class="container">
    <h2>🔍 Debug: Error Real usuario_id</h2>
    <hr>
    
    <div class="alert alert-warning">
        <h4>🤔 Investigación del Error</h4>
        <p>El error "Column 'usuario_id' cannot be null" está ocurriendo, pero la tabla <code>pedidos</code> NO tiene un campo <code>usuario_id</code>.</p>
        <p><strong>Esto significa que el error viene de otra tabla.</strong></p>
    </div>
    
    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">🔍 Buscar Tablas con Campo usuario_id</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                echo "<h5>📊 Tablas que contienen campo 'usuario_id':</h5>";
                $stmt = Conexion::conectar()->prepare("
                    SELECT TABLE_NAME, COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_SCHEMA = DATABASE() 
                    AND COLUMN_NAME = 'usuario_id'
                    ORDER BY TABLE_NAME
                ");
                $stmt->execute();
                $tablas_usuario_id = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (count($tablas_usuario_id) > 0) {
                    echo "<table class='table table-striped'>";
                    echo "<thead>";
                    echo "<tr>";
                    echo "<th>Tabla</th>";
                    echo "<th>Campo</th>";
                    echo "<th>Tipo</th>";
                    echo "<th>Permite NULL</th>";
                    echo "<th>Valor por Defecto</th>";
                    echo "</tr>";
                    echo "</thead>";
                    echo "<tbody>";
                    
                    foreach ($tablas_usuario_id as $tabla) {
                        $row_class = $tabla['IS_NULLABLE'] == 'NO' ? 'danger' : 'success';
                        echo "<tr class='{$row_class}'>";
                        echo "<td><strong>{$tabla['TABLE_NAME']}</strong></td>";
                        echo "<td>{$tabla['COLUMN_NAME']}</td>";
                        echo "<td>{$tabla['DATA_TYPE']}</td>";
                        echo "<td>{$tabla['IS_NULLABLE']}</td>";
                        echo "<td>" . ($tabla['COLUMN_DEFAULT'] ?: 'Ninguno') . "</td>";
                        echo "</tr>";
                    }
                    
                    echo "</tbody></table>";
                    
                    echo "<div class='alert alert-info'>";
                    echo "<h6>💡 Análisis:</h6>";
                    echo "<p>Las tablas marcadas en <span class='text-danger'>rojo</span> tienen usuario_id como NOT NULL y pueden causar el error.</p>";
                    echo "</div>";
                } else {
                    echo "<div class='alert alert-success'>";
                    echo "<h6>✅ No hay tablas con campo usuario_id</h6>";
                    echo "<p>Esto confirma que el error puede venir de otro lugar o ser un error de configuración.</p>";
                    echo "</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-warning">
        <div class="panel-heading">
            <h3 class="panel-title">🔍 Buscar Campos Similares</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                echo "<h5>📊 Campos que contienen 'usuario' en su nombre:</h5>";
                $stmt = Conexion::conectar()->prepare("
                    SELECT TABLE_NAME, COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_SCHEMA = DATABASE() 
                    AND COLUMN_NAME LIKE '%usuario%'
                    ORDER BY TABLE_NAME, COLUMN_NAME
                ");
                $stmt->execute();
                $campos_usuario = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (count($campos_usuario) > 0) {
                    echo "<table class='table table-condensed'>";
                    echo "<thead>";
                    echo "<tr>";
                    echo "<th>Tabla</th>";
                    echo "<th>Campo</th>";
                    echo "<th>Tipo</th>";
                    echo "<th>Permite NULL</th>";
                    echo "<th>Valor por Defecto</th>";
                    echo "</tr>";
                    echo "</thead>";
                    echo "<tbody>";
                    
                    foreach ($campos_usuario as $campo) {
                        $row_class = $campo['IS_NULLABLE'] == 'NO' ? 'warning' : '';
                        echo "<tr class='{$row_class}'>";
                        echo "<td><strong>{$campo['TABLE_NAME']}</strong></td>";
                        echo "<td>{$campo['COLUMN_NAME']}</td>";
                        echo "<td>{$campo['DATA_TYPE']}</td>";
                        echo "<td>{$campo['IS_NULLABLE']}</td>";
                        echo "<td>" . ($campo['COLUMN_DEFAULT'] ?: 'Ninguno') . "</td>";
                        echo "</tr>";
                    }
                    
                    echo "</tbody></table>";
                } else {
                    echo "<div class='alert alert-info'>No se encontraron campos con 'usuario' en el nombre</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-primary">
        <div class="panel-heading">
            <h3 class="panel-title">🔍 Verificar Estructura de Tablas Relacionadas</h3>
        </div>
        <div class="panel-body">
            <?php
            $tablas_importantes = ['pedidos', 'ventas', 'creditos', 'deudas', 'turnos_cajeros'];
            
            foreach ($tablas_importantes as $tabla) {
                try {
                    echo "<h6>📋 Tabla: {$tabla}</h6>";
                    $stmt = Conexion::conectar()->prepare("DESCRIBE {$tabla}");
                    $stmt->execute();
                    $estructura = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    
                    echo "<div class='row'>";
                    echo "<div class='col-md-12'>";
                    echo "<table class='table table-condensed'>";
                    echo "<thead>";
                    echo "<tr>";
                    echo "<th>Campo</th>";
                    echo "<th>Tipo</th>";
                    echo "<th>Null</th>";
                    echo "<th>Key</th>";
                    echo "<th>Default</th>";
                    echo "</tr>";
                    echo "</thead>";
                    echo "<tbody>";
                    
                    foreach ($estructura as $campo) {
                        $row_class = '';
                        if (strpos($campo['Field'], 'usuario') !== false) {
                            $row_class = $campo['Null'] == 'NO' ? 'danger' : 'warning';
                        }
                        
                        echo "<tr class='{$row_class}'>";
                        echo "<td><strong>{$campo['Field']}</strong></td>";
                        echo "<td>{$campo['Type']}</td>";
                        echo "<td>{$campo['Null']}</td>";
                        echo "<td>{$campo['Key']}</td>";
                        echo "<td>{$campo['Default']}</td>";
                        echo "</tr>";
                    }
                    
                    echo "</tbody></table>";
                    echo "</div>";
                    echo "</div>";
                    echo "<hr>";
                    
                } catch (Exception $e) {
                    echo "<div class='alert alert-warning'>Tabla {$tabla} no existe o error: " . $e->getMessage() . "</div>";
                }
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-success">
        <div class="panel-heading">
            <h3 class="panel-title">💡 Posibles Causas del Error</h3>
        </div>
        <div class="panel-body">
            <h5>🤔 El error "usuario_id cannot be null" puede venir de:</h5>
            <ol>
                <li><strong>Tabla ventas o creditos</strong> - Durante la inserción de la factura</li>
                <li><strong>Tabla turnos_cajeros</strong> - Al verificar el turno del cajero</li>
                <li><strong>Triggers de base de datos</strong> - Que se ejecutan automáticamente</li>
                <li><strong>Otra función</strong> - Que se ejecuta durante el proceso de facturación</li>
                <li><strong>Error de configuración</strong> - En la estructura de la base de datos</li>
            </ol>
            
            <div class="alert alert-info">
                <h6>🔍 Próximos pasos:</h6>
                <ul>
                    <li>Revisar las consultas INSERT en las tablas ventas y creditos</li>
                    <li>Verificar si hay triggers que requieran usuario_id</li>
                    <li>Comprobar la función de turnos de cajero</li>
                    <li>Activar logs detallados para identificar la consulta exacta que falla</li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="panel panel-default">
        <div class="panel-heading">
            <h3 class="panel-title">🧪 Test de Inserción Simple</h3>
        </div>
        <div class="panel-body">
            <p>Vamos a probar una inserción simple en la tabla pedidos para verificar que funciona:</p>
            
            <?php
            if (isset($_POST['test_insert'])) {
                try {
                    $mesero_test = isset($_SESSION['usuario']) ? $_SESSION['usuario'] : 1;
                    $cedula_test = '12345678';
                    
                    $stmt = Conexion::conectar();
                    $stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                    
                    $consulta_test = "INSERT INTO pedidos (mesero_id, facturado, cedula_cliente) VALUES ({$mesero_test}, 'n', '{$cedula_test}')";
                    
                    echo "<div class='alert alert-info'>";
                    echo "<h6>🔄 Ejecutando consulta de prueba:</h6>";
                    echo "<code>{$consulta_test}</code>";
                    echo "</div>";
                    
                    $stmt->exec($consulta_test);
                    $ultimo_id = $stmt->lastInsertId();
                    
                    echo "<div class='alert alert-success'>";
                    echo "<h6>✅ Inserción exitosa</h6>";
                    echo "<p>Pedido creado con ID: {$ultimo_id}</p>";
                    echo "</div>";
                    
                    // Limpiar el pedido de prueba
                    $stmt->exec("DELETE FROM pedidos WHERE id = {$ultimo_id}");
                    echo "<div class='alert alert-info'><small>Pedido de prueba eliminado</small></div>";
                    
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>";
                    echo "<h6>❌ Error en inserción de prueba:</h6>";
                    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
                    echo "<p><strong>Código:</strong> " . $e->getCode() . "</p>";
                    echo "</div>";
                }
            }
            ?>
            
            <form method="POST">
                <button type="submit" name="test_insert" class="btn btn-warning">
                    🧪 Probar Inserción en Tabla pedidos
                </button>
            </form>
        </div>
    </div>
    
    <hr>
    <div class="row">
        <div class="col-md-3">
            <a href="index.php?action=debug_error_usuario_id" class="btn btn-primary btn-block">🔙 Debug Usuario ID</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=mesa" class="btn btn-info btn-block">🪑 Ver Mesas</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=registroPmesa&ida=5" class="btn btn-warning btn-block">🧪 Test Mesa 5</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=diagnostico" class="btn btn-success btn-block">📊 Diagnóstico</a>
        </div>
    </div>
</div>

</body>
</html>
