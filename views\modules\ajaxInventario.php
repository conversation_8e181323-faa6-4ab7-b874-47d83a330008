<?php
//echo "<script>alert('Entro al ajax Ventas ');</script>";
require_once "../../models/crud.php";
require_once "../../models/crudVistaVentas.php";
require_once "../../controllers/controller.php";
require_once "../../controllers/controllerVistaVentas.php";
	ini_set("session.cookie_lifetime","28800");
	ini_set("session.gc_maxlifetime","28800");
//echo "<script>alert('Hasta aqui todo bien ');</script>";

	if(isset($_POST["fecha1"]) and $_POST['fecha1']!='' and isset($_POST["fecha2"]) and $_POST['fecha2']!='')  // !(empty($var))
		{
			//echo "<script>alert('Si entro al ajax Ventas');</script>";
			$fecha1 = $_POST["fecha1"];
			$fecha2 = $_POST["fecha2"];

			//echo $queryString;
			$ajax=new controllerVistaVentas();
			$resultado=$ajax->vistaVentasController($fecha1,$fecha2);
			//echo "<script>alert('entro ajax ultimo pro')</script>";
			//echo "<div>Dios es Amor</div>";
			echo '<table border="1">
					<tr>
						<td><b>Nombre Producto</b></td>
						<td><b>Cantidad Vendida</b></td>
						<td><b>Precio de venta</b></td>
						<td><b>Total vendido</b></td>
						<td><b>Costo Unidad</b></td>
						<td><b>Total Costo</b></td>
					</tr>';
              $venta_total=0;
              $costo_compra_total=0;
              $utilidad=0;
			foreach($resultado as $row => $item)
				{
					 $costosp=$ajax->CostoProductoController($item["idp"]);
					 $costototal=$costosp["Valor_Compra_Producto"]*$item["cantp"];
					 $venta_total+=$item["Subtotal_por_producto"];
					 $costo_compra_total+=$costototal;
					echo'<tr>
							<td>'.$item["nombre"].'</td>
							<td>'.$item["cantp"].'</td>
							<td>'.$item["precio"].'</td>
							<td>'.$item["Subtotal_por_producto"].'</td>
							<td>'.round($costosp["Valor_Compra_Producto"],1).'</td>
							<td>'.round($costototal,1).'</td>
						</tr>';
				}
				setlocale(LC_MONETARY, 'en_US');
				$utilidad=	$venta_total-$costo_compra_total;
				$utilidad=money_format('%(#1n', $utilidad);
			echo '<tr>
						<td></td>
						<td><b></b></td>
						<td><h3><b>Total</b></h3></td>
						<td><b>'.money_format('%(#1n', $venta_total).'</b></td>
						<td><b></b></td>
						<td><b>'.money_format('%(#1n', $costo_compra_total).'</b></td>
					</tr>
					<tr>
						<td></td>
						<td><b></b></td>
						<td><h3><b>Utilidad</b></h3></td>
						<td><b>'.$utilidad.'</b></td>
						<td><b></b></td>
						<td><b></b></td>
					</tr>
				</table>'	;
		}