<?php

session_start();

if(!$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "../../models/conexion.php";

// Procesar creación de mesas si se envió el formulario
if ($_POST && isset($_POST['crear_mesas'])) {
    try {
        $conexion = Conexion::conectar();
        
        // Verificar si ya existen mesas
        $stmt = $conexion->prepare("SELECT COUNT(*) as total FROM mesas");
        $stmt->execute();
        $resultado = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($resultado['total'] > 0) {
            echo "<div class='alert alert-warning'>⚠️ Ya existen {$resultado['total']} mesas en la base de datos.</div>";
        } else {
            // Crear mesas básicas (1 al 20)
            $mesas_creadas = 0;
            for ($i = 1; $i <= 20; $i++) {
                $stmt = $conexion->prepare("
                    INSERT INTO mesas (numero, capacidad, estado, activo) 
                    VALUES (?, 4, 'disponible', 1)
                ");
                if ($stmt->execute([$i])) {
                    $mesas_creadas++;
                }
            }
            
            echo "<div class='alert alert-success'>";
            echo "✅ <strong>Mesas creadas exitosamente!</strong><br>";
            echo "📊 <strong>Total creadas:</strong> {$mesas_creadas} mesas<br>";
            echo "🏠 <strong>Numeración:</strong> Mesa 1 a Mesa {$mesas_creadas}<br>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>❌ Error al crear mesas: " . $e->getMessage() . "</div>";
    }
}

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Crear Mesas Básicas</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
</head>
<body>

<div class="container">
    <h2>🏠 Crear Mesas Básicas</h2>
    <hr>
    
    <div class="alert alert-info">
        <strong>💡 Propósito:</strong> Esta herramienta crea las mesas básicas necesarias para el funcionamiento del sistema de pedidos.
    </div>
    
    <div class="panel panel-default">
        <div class="panel-heading">
            <h3 class="panel-title">📊 Estado Actual de Mesas</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                $stmt = Conexion::conectar()->prepare("SELECT COUNT(*) as total FROM mesas");
                $stmt->execute();
                $total_mesas = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
                
                if ($total_mesas > 0) {
                    echo "<div class='alert alert-success'>";
                    echo "✅ <strong>Mesas existentes:</strong> {$total_mesas} mesas en la base de datos";
                    echo "</div>";
                    
                    // Mostrar las mesas existentes
                    $stmt = Conexion::conectar()->prepare("SELECT * FROM mesas ORDER BY numero LIMIT 10");
                    $stmt->execute();
                    $mesas = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    
                    if (count($mesas) > 0) {
                        echo "<h4>Primeras 10 mesas:</h4>";
                        echo "<table class='table table-striped'>";
                        echo "<thead><tr><th>ID</th><th>Número</th><th>Capacidad</th><th>Estado</th><th>Activo</th></tr></thead>";
                        echo "<tbody>";
                        foreach ($mesas as $mesa) {
                            echo "<tr>";
                            echo "<td>{$mesa['id']}</td>";
                            echo "<td><strong>Mesa {$mesa['numero']}</strong></td>";
                            echo "<td>{$mesa['capacidad']} personas</td>";
                            echo "<td>{$mesa['estado']}</td>";
                            echo "<td>" . ($mesa['activo'] ? 'Sí' : 'No') . "</td>";
                            echo "</tr>";
                        }
                        echo "</tbody></table>";
                        
                        if ($total_mesas > 10) {
                            echo "<p><em>... y " . ($total_mesas - 10) . " mesas más</em></p>";
                        }
                    }
                    
                } else {
                    echo "<div class='alert alert-warning'>";
                    echo "⚠️ <strong>No hay mesas en la base de datos</strong><br>";
                    echo "Es necesario crear mesas para poder generar pedidos de prueba.";
                    echo "</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>❌ Error al consultar mesas: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-primary">
        <div class="panel-heading">
            <h3 class="panel-title">🚀 Crear Mesas Básicas</h3>
        </div>
        <div class="panel-body">
            <p><strong>Esta acción creará 20 mesas básicas:</strong></p>
            <ul>
                <li>📊 <strong>Numeración:</strong> Mesa 1, Mesa 2, ... Mesa 20</li>
                <li>👥 <strong>Capacidad:</strong> 4 personas por mesa</li>
                <li>✅ <strong>Estado:</strong> Disponible</li>
                <li>🔄 <strong>Activo:</strong> Sí</li>
            </ul>
            
            <form method="post">
                <button type="submit" name="crear_mesas" class="btn btn-primary btn-lg" onclick="return confirm('¿Estás seguro de crear las mesas básicas?')">
                    <i class="glyphicon glyphicon-plus"></i> Crear 20 Mesas Básicas
                </button>
            </form>
        </div>
    </div>
    
    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">📋 Próximos Pasos</h3>
        </div>
        <div class="panel-body">
            <p>Después de crear las mesas:</p>
            <ol>
                <li>✅ <strong>Crear pedido de prueba</strong> usando la herramienta correspondiente</li>
                <li>✅ <strong>Verificar pedidos pendientes</strong> en las páginas de cocina/bar/asados</li>
                <li>✅ <strong>Probar funcionalidad completa</strong> del sistema</li>
            </ol>
        </div>
    </div>
    
    <hr>
    <div class="row">
        <div class="col-md-4">
            <a href="views/modules/crear_pedido_prueba.php" class="btn btn-success btn-block">🧪 Crear Pedido de Prueba</a>
        </div>
        <div class="col-md-4">
            <a href="views/modules/cambiar_estado_pedido.php" class="btn btn-warning btn-block">🔧 Cambiar Estado Pedidos</a>
        </div>
        <div class="col-md-4">
            <a href="views/modules/test_flujo_completo_pendientes.php" class="btn btn-info btn-block">🔄 Test Flujo Completo</a>
        </div>
    </div>
</div>

</body>
</html>
