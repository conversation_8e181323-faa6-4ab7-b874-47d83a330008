<?php
require_once '../../models/crudEstadoPedidos.php';
require_once '../../controllers/controllerEstadoPedidos.php';

$mesa = $_GET['mesa'] ?? 2;
$controller = new ControllerEstadoPedidos();

echo "<h2>🧪 Test: Enviar Pedido Borrador - Mesa $mesa</h2>";

// 1. Obtener pedido borrador
$pedidoBorrador = $controller->obtenerPedidoBorradorController($mesa);

if (!$pedidoBorrador) {
    echo "<p>❌ No hay pedido borrador para la mesa $mesa</p>";
    echo "<a href='test_agregar_producto.php?mesa=$mesa' class='btn btn-primary'>Agregar Producto Primero</a>";
    exit;
}

echo "<h3>📋 Pedido Borrador Actual:</h3>";
echo "<p><strong>ID:</strong> {$pedidoBorrador['id']}</p>";
echo "<p><strong>Número:</strong> {$pedidoBorrador['numero_pedido']}</p>";
echo "<p><strong>Estado:</strong> {$pedidoBorrador['estado']}</p>";

// 2. Verificar productos en el pedido
$productos = DatosEstadoPedidos::obtenerProductosPedidoModel($pedidoBorrador['id']);
echo "<h3>🛒 Productos en el Pedido:</h3>";

if (empty($productos)) {
    echo "<p>❌ El pedido no tiene productos. No se puede enviar.</p>";
    echo "<a href='test_agregar_producto.php?mesa=$mesa' class='btn btn-primary'>Agregar Producto Primero</a>";
    exit;
}

$total = 0;
foreach ($productos as $producto) {
    $subtotal = $producto['cantidad'] * $producto['precio'];
    $total += $subtotal;
    echo "<p>- {$producto['nombre']} (Cantidad: {$producto['cantidad']}, Precio: \${$producto['precio']}, Subtotal: \${$subtotal})</p>";
}
echo "<p><strong>💰 Total del Pedido: \${$total}</strong></p>";

// 3. Enviar el pedido
echo "<h3>📤 Enviando Pedido...</h3>";

// Simular sesión de usuario
session_start();
if (!isset($_SESSION["usuario"])) {
    $_SESSION["usuario"] = 1;
}

$resultado = $controller->enviarPedidoController($pedidoBorrador['id'], $_SESSION["usuario"]);

if ($resultado == "success") {
    echo "<p>✅ Pedido enviado exitosamente</p>";

    // Verificar el estado después de enviar
    $pedidosActualizados = DatosEstadoPedidos::obtenerPedidosMesaEstadoModel($mesa);
    echo "<h3>📋 Estado Después de Enviar:</h3>";

    foreach ($pedidosActualizados as $pedido) {
        if ($pedido['id'] == $pedidoBorrador['id']) {
            echo "<p><strong>Estado:</strong> {$pedido['estado']}</p>";
            echo "<p><strong>Fecha Envío:</strong> {$pedido['fecha_envio']}</p>";
            break;
        }
    }

    // Verificar que se creó un nuevo pedido borrador
    $nuevoBorrador = $controller->obtenerPedidoBorradorController($mesa);
    if ($nuevoBorrador && $nuevoBorrador['id'] != $pedidoBorrador['id']) {
        echo "<h3>📝 Nuevo Pedido Borrador Creado:</h3>";
        echo "<p><strong>ID:</strong> {$nuevoBorrador['id']}</p>";
        echo "<p><strong>Número:</strong> {$nuevoBorrador['numero_pedido']}</p>";
        echo "<p><strong>Estado:</strong> {$nuevoBorrador['estado']}</p>";
    } else {
        echo "<p>⚠️ No se creó un nuevo pedido borrador automáticamente</p>";
    }

} else {
    echo "<p>❌ Error al enviar el pedido: $resultado</p>";
}

echo "<hr>";
echo "<h3>🔄 Verificar Facturación Actualizada</h3>";
echo "<a href='test_facturacion.php?mesa=$mesa' class='btn btn-primary'>Ver Facturación Actualizada</a>";
echo " | ";
echo "<a href='test_prefacturacion.php?mesa=$mesa' class='btn btn-warning'>Prefacturación</a>";
echo " | ";
echo "<a href='test_agregar_producto.php?mesa=$mesa' class='btn btn-success'>Agregar Más Productos</a>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}

.btn {
    display: inline-block;
    padding: 8px 16px;
    margin: 5px;
    text-decoration: none;
    border-radius: 4px;
    color: white;
}

.btn-primary { background-color: #007bff; }
.btn-success { background-color: #28a745; }
.btn-warning { background-color: #ffc107; color: #212529; }

h2, h3 { color: #333; }
p { margin: 8px 0; }
hr { margin: 20px 0; }
</style>
