<?php
// Test de impresión usando Sockets TCP puros
echo "<h1>🔌 Test Impresión con Sockets</h1>";
echo "<p><strong>Conexión TCP directa a impresoras - Máxima velocidad y eficiencia</strong></p>";

// Incluir la clase de sockets
require_once '../../models/crudImpresionSocket.php';

// Verificar si la extensión de sockets está disponible
if (!extension_loaded('sockets')) {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
    echo "<h4>❌ Extensión de Sockets No Disponible</h4>";
    echo "<p>La extensión 'sockets' de PHP no está habilitada en este servidor.</p>";
    echo "<p>Para habilitar sockets, agregar en php.ini: <code>extension=sockets</code></p>";
    echo "</div>";
    exit;
}

echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0;'>";
echo "<h4>✅ Extensión de Sockets Disponible</h4>";
echo "<p>PHP Sockets habilitado - Listo para conexiones TCP directas</p>";
echo "</div>";

// Mostrar configuración
$config = ImpresionSocket::obtenerConfiguracion();

echo "<div style='background-color: #d1ecf1; padding: 15px; border-left: 4px solid #17a2b8; margin-bottom: 20px;'>";
echo "<h4>🔧 Configuración de Sockets</h4>";
echo "<p><strong>Tu IP:</strong> " . $_SERVER['REMOTE_ADDR'] . "</p>";
echo "<p><strong>Método:</strong> " . strtoupper($config['metodo']) . "</p>";
echo "<p><strong>Timeout Conexión:</strong> {$config['timeout_conexion']} segundos</p>";
echo "<p><strong>Timeout Escritura:</strong> {$config['timeout_escritura']} segundos</p>";

echo "<h5>🖨️ Impresoras Configuradas:</h5>";
echo "<table style='width: 100%; border-collapse: collapse; margin-top: 10px;'>";
echo "<tr style='background-color: #bee5eb;'>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Categoría</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>IP</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Puerto</th>";
echo "</tr>";

foreach ($config['impresoras'] as $categoria => $impresora) {
    echo "<tr>";
    echo "<td style='border: 1px solid #ddd; padding: 8px; font-weight: bold;'>" . strtoupper($categoria) . "</td>";
    echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$impresora['ip']}</td>";
    echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$impresora['puerto']}</td>";
    echo "</tr>";
}
echo "</table>";
echo "</div>";

// Procesar formularios
if ($_POST) {
    echo "<div style='border: 1px solid #007bff; padding: 20px; margin: 15px 0; border-radius: 8px;'>";
    
    if (isset($_POST['test_socket'])) {
        $categoria = $_POST['categoria'];
        $contenido = $_POST['contenido'];
        
        echo "<h4>🔌 Imprimiendo con Socket TCP en: " . strtoupper($categoria) . "</h4>";
        
        $resultado = ImpresionSocket::imprimir($categoria, $contenido);
        
        if ($resultado['success']) {
            echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0;'>";
            echo "<h5>✅ Impresión Socket Exitosa</h5>";
            echo "<p><strong>Categoría:</strong> {$resultado['categoria']}</p>";
            echo "<p><strong>IP:</strong> {$resultado['ip']}:{$resultado['puerto']}</p>";
            echo "<p><strong>Bytes enviados:</strong> {$resultado['bytes_enviados']}</p>";
            echo "<p><strong>Tiempo:</strong> {$resultado['tiempo_ms']}ms</p>";
            echo "<p><strong>Método:</strong> {$resultado['metodo']}</p>";
            echo "<p><strong>Timestamp:</strong> {$resultado['timestamp']}</p>";
            echo "</div>";
        } else {
            echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
            echo "<h5>❌ Error en Socket</h5>";
            echo "<p><strong>Categoría:</strong> {$resultado['categoria']}</p>";
            echo "<p><strong>Error:</strong> {$resultado['error']}</p>";
            echo "<p><strong>Tiempo:</strong> {$resultado['tiempo_ms']}ms</p>";
            echo "<p><strong>Método:</strong> {$resultado['metodo']}</p>";
            echo "</div>";
        }
    }
    
    if (isset($_POST['test_conectividad_socket'])) {
        echo "<h4>🔍 Test de Conectividad con Sockets</h4>";
        
        $estado = ImpresionSocket::testConectividad();
        
        foreach ($estado as $categoria => $info) {
            if ($info['status'] === 'online') {
                echo "<div style='background-color: #d4edda; padding: 10px; border-left: 4px solid #28a745; margin: 10px 0;'>";
                echo "<p>✅ <strong>" . strtoupper($categoria) . ":</strong> Online ({$info['ip']}:{$info['puerto']}) - {$info['tiempo_ms']}ms</p>";
                echo "</div>";
            } else {
                echo "<div style='background-color: #f8d7da; padding: 10px; border-left: 4px solid #dc3545; margin: 10px 0;'>";
                echo "<p>❌ <strong>" . strtoupper($categoria) . ":</strong> {$info['status']} - {$info['error']} ({$info['tiempo_ms']}ms)</p>";
                echo "</div>";
            }
        }
    }
    
    if (isset($_POST['test_pedido_socket'])) {
        echo "<h4>🧾 Test Pedido Completo con Sockets</h4>";
        
        $productos = [
            ['nombre' => 'Cerveza Corona', 'categoria' => 'bebidas', 'cantidad' => 2, 'precio' => 8000],
            ['nombre' => 'Bandeja Paisa', 'categoria' => 'comidas', 'cantidad' => 1, 'precio' => 25000],
            ['nombre' => 'Churrasco', 'categoria' => 'carnes', 'cantidad' => 1, 'precio' => 35000]
        ];
        
        $resultado_pedido = ImpresionSocket::imprimirPedido($productos, '5');
        
        echo "<p><strong>Pedido:</strong> {$resultado_pedido['numero_pedido']}</p>";
        echo "<p><strong>Mesa:</strong> {$resultado_pedido['mesa']}</p>";
        echo "<p><strong>Tiempo Total:</strong> {$resultado_pedido['tiempo_total_ms']}ms</p>";
        
        foreach ($resultado_pedido['resultados'] as $categoria => $resultado) {
            if ($resultado['success']) {
                echo "<div style='background-color: #d4edda; padding: 10px; border-left: 4px solid #28a745; margin: 10px 0;'>";
                echo "<p>✅ <strong>" . strtoupper($categoria) . ":</strong> Socket exitoso ({$resultado['bytes_enviados']} bytes, {$resultado['tiempo_ms']}ms)</p>";
                echo "</div>";
            } else {
                echo "<div style='background-color: #f8d7da; padding: 10px; border-left: 4px solid #dc3545; margin: 10px 0;'>";
                echo "<p>❌ <strong>" . strtoupper($categoria) . ":</strong> {$resultado['error']} ({$resultado['tiempo_ms']}ms)</p>";
                echo "</div>";
            }
        }
    }
    
    if (isset($_POST['test_masivo_socket'])) {
        echo "<h4>🚀 Test Masivo con Sockets</h4>";
        
        $categorias = ['test', 'bar', 'cocina', 'asados'];
        $inicio_total = microtime(true);
        $exitosos = 0;
        
        foreach ($categorias as $categoria) {
            $contenido = "TEST MASIVO SOCKET - " . strtoupper($categoria) . "\n";
            $contenido .= "Hora: " . date('H:i:s') . "\n";
            $contenido .= "Método: Socket TCP\n";
            $contenido .= "Producto de prueba x1";
            
            $resultado = ImpresionSocket::imprimir($categoria, $contenido);
            
            if ($resultado['success']) {
                echo "<div style='background-color: #d4edda; padding: 10px; border-left: 4px solid #28a745; margin: 10px 0;'>";
                echo "<p>✅ <strong>" . strtoupper($categoria) . ":</strong> Socket exitoso ({$resultado['bytes_enviados']} bytes, {$resultado['tiempo_ms']}ms)</p>";
                echo "</div>";
                $exitosos++;
            } else {
                echo "<div style='background-color: #f8d7da; padding: 10px; border-left: 4px solid #dc3545; margin: 10px 0;'>";
                echo "<p>❌ <strong>" . strtoupper($categoria) . ":</strong> {$resultado['error']} ({$resultado['tiempo_ms']}ms)</p>";
                echo "</div>";
            }
        }
        
        $tiempo_total = round((microtime(true) - $inicio_total) * 1000, 2);
        
        echo "<div style='background-color: #e7f3ff; padding: 15px; border-left: 4px solid #007bff; margin: 15px 0;'>";
        echo "<h5>📊 Resumen Test Masivo Socket</h5>";
        $total_impresoras = count($categorias);
        echo "<p><strong>Exitosos:</strong> $exitosos/$total_impresoras impresoras</p>";
        echo "<p><strong>Tiempo total:</strong> {$tiempo_total}ms</p>";
        echo "<p><strong>Promedio por impresora:</strong> " . round($tiempo_total/$total_impresoras, 2) . "ms</p>";
        echo "</div>";
    }
    
    echo "</div>";
}

// Formularios de test
echo "<h2>🧪 Tests con Sockets TCP</h2>";

// Test individual
echo "<form method='POST' style='background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>🔌 Test Socket Individual</h4>";
echo "<div style='margin: 15px 0;'>";
echo "<label><strong>Impresora:</strong></label><br>";
echo "<select name='categoria' required style='padding: 8px; border: 1px solid #ccc; border-radius: 3px; width: 200px;'>";
echo "<option value='test' selected>🧪 TEST (192.168.18.50)</option>";
echo "<option value='bar'>🍺 BAR (192.168.68.121)</option>";
echo "<option value='cocina'>🍳 COCINA (192.168.68.120)</option>";
echo "<option value='asados'>🥩 ASADOS (192.168.68.122)</option>";
echo "</select>";
echo "</div>";
echo "<div style='margin: 15px 0;'>";
echo "<label><strong>Contenido:</strong></label><br>";
echo "<textarea name='contenido' rows='5' cols='50' required style='padding: 8px; border: 1px solid #ccc; border-radius: 3px; font-family: monospace;'>Mesa 1\nCerveza Corona x2\nTotal: $16.000\nMétodo: Socket TCP</textarea>";
echo "</div>";
echo "<button type='submit' name='test_socket' style='background-color: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold; width: 100%;'>🔌 Imprimir con Socket</button>";
echo "</form>";

// Test conectividad
echo "<form method='POST' style='background-color: #e7f3ff; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>🔍 Test Conectividad Socket</h4>";
echo "<p>Verifica conexión TCP directa a todas las impresoras</p>";
echo "<button type='submit' name='test_conectividad_socket' style='background-color: #17a2b8; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold; width: 100%;'>🔍 Test Conectividad TCP</button>";
echo "</form>";

// Test pedido
echo "<form method='POST' style='background-color: #d4edda; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>🧾 Test Pedido Socket</h4>";
echo "<p>Simula pedido completo distribuido por sockets TCP</p>";
echo "<button type='submit' name='test_pedido_socket' style='background-color: #28a745; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold; width: 100%;'>🧾 Pedido con Sockets</button>";
echo "</form>";

// Test masivo
echo "<form method='POST' style='background-color: #fff3cd; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>🚀 Test Masivo Socket</h4>";
echo "<p>Imprime en las 3 impresoras usando sockets TCP simultáneamente</p>";
echo "<button type='submit' name='test_masivo_socket' style='background-color: #ffc107; color: #212529; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold; width: 100%;'>🚀 Test Masivo TCP</button>";
echo "</form>";

// Información técnica
echo "<h2>🔧 Información Técnica de Sockets</h2>";
echo "<div style='background-color: #e7f3ff; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>⚡ Ventajas de Sockets TCP:</h4>";
echo "<ul>";
echo "<li>🚀 <strong>Máxima velocidad:</strong> Conexión directa sin overhead HTTP</li>";
echo "<li>🔒 <strong>Más confiable:</strong> Control total de la conexión</li>";
echo "<li>⚡ <strong>Menor latencia:</strong> Sin headers HTTP ni JSON</li>";
echo "<li>🎯 <strong>Específico:</strong> Diseñado para impresoras de red</li>";
echo "<li>📱 <strong>Compatible:</strong> Funciona desde cualquier dispositivo</li>";
echo "</ul>";

echo "<h4>🔧 Configuración Técnica:</h4>";
echo "<ul>";
echo "<li><strong>Protocolo:</strong> TCP/IP (AF_INET, SOCK_STREAM)</li>";
echo "<li><strong>Timeout conexión:</strong> {$config['timeout_conexion']} segundos</li>";
echo "<li><strong>Timeout escritura:</strong> {$config['timeout_escritura']} segundos</li>";
echo "<li><strong>Formato datos:</strong> ESC/POS optimizado</li>";
echo "<li><strong>Codificación:</strong> PC437 (compatible impresoras térmicas)</li>";
echo "</ul>";

echo "<h4>📊 Comparación de Métodos:</h4>";
echo "<table style='width: 100%; border-collapse: collapse; margin-top: 10px;'>";
echo "<tr style='background-color: #bee5eb;'>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Método</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Velocidad</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Confiabilidad</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px;'>Complejidad</th>";
echo "</tr>";
echo "<tr>";
echo "<td style='border: 1px solid #ddd; padding: 8px; font-weight: bold;'>Socket TCP</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px; color: green;'>⚡ Muy Alta</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px; color: green;'>🔒 Muy Alta</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px; color: orange;'>🔧 Media</td>";
echo "</tr>";
echo "<tr>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>HTTP/REST</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px; color: orange;'>🐌 Media</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px; color: orange;'>⚠️ Media</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px; color: green;'>✅ Baja</td>";
echo "</tr>";
echo "<tr>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>fsockopen()</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px; color: green;'>🚀 Alta</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px; color: orange;'>⚠️ Media</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px; color: green;'>✅ Baja</td>";
echo "</tr>";
echo "</table>";
echo "</div>";

// Botones de navegación
echo "<p style='margin-top: 30px;'>";
echo "<a href='test_impresion_simple.php' style='background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>← Test Simple</a>";
echo "<a href='ejemplo_impresion_directa.php' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>💡 Ejemplos</a>";
echo "<a href='index.php?action=registroPmesa&ida=1' style='background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🪑 Mesa Real</a>";
echo "</p>";
?>
