<?php
	if(isset($_GET["action"]))
		{	if($_GET["action"] =="listo" )
			{echo "Registro Exitoso";	}
		}
?>
<script type="text/javascript">
	// Función autocompletar --------------------------
	   function autocompletar() {
			 var minimo_letras = 2; // minimo letras visibles en el autocompletar
			 var palabra = $('#nombrepr').val();
			 //Contamos el valor del input mediante una condicional
			 if (palabra.length >= minimo_letras) {
			 $.ajax({
			 url: 'views/modules/ajaxAutocompleteS.php',
			 type: 'POST',
			 data: {palabra:palabra},
			 success:function(data){
			 $('#lista_id').show();
			 $('#lista_id').html(data);
			 }
			 });
			 } else {
			 //ocultamos la lista
			 $('#lista_id').hide();
			 }
		}

		// Funcion Mostrar valores
		function set_item(opciones,id) {
		 // Cambiar el valor del formulario input
		 $('#nombrepr').val(opciones);
		 $('#suministros').val(id);
		 // ocultar lista de proposiciones
		 $('#lista_id').hide();
		}
	// -------------------------------------------------
</script>
<style type="text/css">
	.etiqueta {
	 width: 120px;
	 float: left;
	 line-height: 28px;
	 font-size: 20px;
	}
	.input_container {
	 height: 30px;
	 float: left;
	}
	.input_container input {
	 height: 20px;
	 width: 260px;
	 padding: 3px;
	 border: 1px solid #cccccc;
	 border-radius: 0;
	}
	.input_container ul {
	 width: 270px;
	 border: 1px solid #eaeaea;
	 position: absolute;
	 z-index: 9;
	 background: #f3f3f3;
	 list-style: none;
	 margin-left: 5px;
	margin-top: -3px;
	}
	.input_container ul li {
	 padding: 2px;
	}
	.input_container ul li:hover {
	 background: #eaeaea;
	}
	#country_list_id {
	 display: none;
	}
</style>
 <div class="row">
      <div class="col-md-9">
      	<form name="calculo" method="post" >
		  	<div ><h2>Receta Producto</h2></div><br>
		  	<!-- <input type="hidden"  name="pago" id="pago" value="1">-->
			<table border="1" class="table table-hover">
				<thead>
					<tr>
						<th>COD. B</th>
						<th>NOMBRE</th>
						<th>Pid</th>
						<th>Sid</th>
						<th>CANT</th>
						<th></th>
						<th></th>
					</tr>
				</thead>
				<tbody>
					<?php
						$registroSuministroP = new controllerSuministroProducto();
						$registroSuministroP -> detalleSuministroPController();
						$registroSuministroP -> borrarSuministroPController();
					?>
				</tbody>
			</table>
			<span id="destino" > </span>
			<p><a class="btn btn-primary btn-lg"  role="button" onclick="cancelarPedido();" name="cancelar" value="cancelar">Cancelar &raquo;</a>

			<!--<a class="btn btn-primary btn-lg"  role="button" onclick="cocina();" name="factura" value="factura">Cocina &raquo;</a></p>
			<input type="submit"  value="Facturar">-->
		</form>
      </div>
	 <div class="col-md-3" style="background-color:#ACADB4; border-radius: 15px 15px 15px 15px; padding:12px"></p>
		<form method="post" action="index.php?action=registroReseta&id=<?=$_SESSION["producto"];?>">
		 	<div ><h4>Producto <?php echo 'Pedido : '.$_SESSION["producto"];?></h4></div><br>

			<label>  CANT : </label><input type="text" placeholder="1" name="cantidades" value="1" autofocus="autofocus" required><br>
			<label>  Codi : </label><input type="text" placeholder="Suministro" name="suministros" id="suministros"  required><br>

			<input type="hidden"  name="productos" value="<?=$_SESSION["producto"];?>" >
			<input type="submit" value="Enviar"><br>
			<div class="input_container">
			<label>Nombre:</label>
			<input type="text" autocomplete="off" id="nombrepr" placeholder="PRODUCTO" name="nombrepr" onkeyup="autocompletar()" >
			<ul id="lista_id"></ul><br>
		</div>
				<br><br><br><br><br>
			<br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br>
			</div>
		</form>

		<?php
		// Procesar el formulario DESPUÉS de mostrarlo
		if ($_POST) {
			$registroSuministroP = new controllerSuministroProducto();
			$registroSuministroP -> registroSuministroPController();
		}
		?>
	 </div>
</div>
