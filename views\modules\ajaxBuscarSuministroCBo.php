<?php
require_once "../../models/crud.php";
require_once "../../models/crudSuministro.php";
require_once "../../controllers/controller.php";
require_once "../../controllers/controllerSuministro.php";
	ini_set("session.cookie_lifetime","28800");
	ini_set("session.gc_maxlifetime","28800");
		//echo'<br> <script>alert("Entro al ajax Buscar Suministros '.$_POST['placa'].'");</script> <br>';
		if(isset($_POST['placa']))
		 {
			$queryString = $_POST['placa'];
			$genero = $_POST['genero'];
			$cont=0;
			//echo'<br> <script>alert("Entro al ajax");</script> <br>';
			$ajax=new controllerSuministro();
			$r=$ajax->ajaxBuscarSuministroController($queryString);
			if ($r>0)
			 {	//echo' <script>alert("Se encontraron resultados");</script>';
				echo'<form method="post"  >
					<table  class="table table-hover">
				 	 <thead>
			 			<tr >
			 			 <th>No</th><th>Nombre</th>  <th>Codigo Barra</th><th>Precio</th> <th>Bodega</th>
			 			</tr>
				 	 </thead>
				 	 <tbody>';
				foreach ($r as $row => $item)
				 {	$v=$ajax->ventaController($item['pid']);
				 	if ($v>0)
				 	 { $c=$v['cantidad'];	}
				 	else { $c=0;}
				 	if ($item['scantidad']>0)
				 	 {$cont++;
				 		echo'<tr >
 					 		<td>'.$cont.'</td>
 					 		<td>'.$item['snombre'].'</td>
 					 		<td>'.$item['scodigo'].'</td>
 					 		<td>'.$item['pprecio'].'</td>
 					 		<td>'.$item['scantidad'].'</td>

 					 	</tr>';
				 	 }
 				 }
				echo'	</tbody>
 					 	</table></form>';
			 }
			else
			 {	echo' <h3>No se encontraron Resultados</h3>';	}
		 }
		else
		 {

		}
?>