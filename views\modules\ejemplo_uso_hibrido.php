<?php
// Ejemplo de uso del Sistema de Impresión Híbrida
echo "<h1>📝 Ejemplo de Uso del Sistema Híbrido</h1>";
echo "<p><strong>Cómo integrar la impresión híbrida en el sistema Macarena</strong></p>";

echo "<div style='background-color: #d1ecf1; padding: 15px; border-left: 4px solid #bee5eb; margin: 20px 0;'>";
echo "<h3>🎯 Casos de Uso:</h3>";
echo "<ul>";
echo "<li>✅ <strong>Enviar pedido a cocina</strong> automáticamente por categoría</li>";
echo "<li>✅ <strong>Imprimir ticket</strong> desde celular del mesero</li>";
echo "<li>✅ <strong>Reimprimir orden</strong> desde cualquier dispositivo</li>";
echo "<li>✅ <strong>Funcionar sin configuración</strong> cuando cambie la IP</li>";
echo "</ul>";
echo "</div>";

// Ejemplo 1: Impresión por categoría
echo "<h2>📋 Ejemplo 1: Impresión Automática por Categoría</h2>";
echo "<div style='border: 1px solid #007bff; padding: 20px; margin: 15px 0; border-radius: 8px;'>";
echo "<h4>🍽️ Escenario: Enviar pedido a cocina</h4>";

echo "<pre style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; font-size: 12px;'>";
echo htmlspecialchars('<?php
// En el controlador de pedidos
require_once "../models/crudImpresionHibrida.php";

function enviarPedidoACocina($pedido_id) {
    $impresion = new CrudImpresionHibrida();
    
    // Obtener productos del pedido
    $productos = obtenerProductosPedido($pedido_id);
    
    // Agrupar por categoría
    $productos_por_categoria = [];
    foreach ($productos as $producto) {
        $categoria = $producto["categoria"];
        if (!isset($productos_por_categoria[$categoria])) {
            $productos_por_categoria[$categoria] = [];
        }
        $productos_por_categoria[$categoria][] = $producto;
    }
    
    // Imprimir en cada impresora según categoría
    $resultados = [];
    foreach ($productos_por_categoria as $categoria => $productos_categoria) {
        
        // Generar ticket para esta categoría
        $ticket = generarTicketCategoria($pedido_id, $categoria, $productos_categoria);
        
        // Imprimir automáticamente
        $resultado = $impresion->imprimirPorCategoria($categoria, $ticket);
        
        $resultados[$categoria] = $resultado;
        
        // Registrar en log
        $impresion->registrarLog($resultado);
    }
    
    return $resultados;
}

function generarTicketCategoria($pedido_id, $categoria, $productos) {
    $ticket = "=== PEDIDO #$pedido_id ===\\n";
    $ticket .= "Categoria: $categoria\\n";
    $ticket .= "Fecha: " . date("Y-m-d H:i:s") . "\\n";
    $ticket .= "========================\\n\\n";
    
    foreach ($productos as $producto) {
        $ticket .= "• {$producto[\'nombre\']} x{$producto[\'cantidad\']}\\n";
        if (!empty($producto[\'observaciones\'])) {
            $ticket .= "  Obs: {$producto[\'observaciones\']}\\n";
        }
    }
    
    $ticket .= "\\n========================\\n";
    $ticket .= "Total productos: " . count($productos) . "\\n\\n\\n";
    
    return $ticket;
}
?>');
echo "</pre>";
echo "</div>";

// Ejemplo 2: Impresión desde celular
echo "<h2>📱 Ejemplo 2: Impresión desde Celular (JavaScript)</h2>";
echo "<div style='border: 1px solid #28a745; padding: 20px; margin: 15px 0; border-radius: 8px;'>";
echo "<h4>📲 Escenario: Mesero imprime desde su celular</h4>";

echo "<pre style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; font-size: 12px;'>";
echo htmlspecialchars('// JavaScript para aplicación móvil
class ImpresionMovil {
    constructor() {
        this.baseUrl = "https://macarena.anconclub.com";
    }
    
    async imprimirPedido(pedidoId, categoria) {
        try {
            // El sistema híbrido detecta automáticamente si usar proxy o directo
            const response = await fetch(`${this.baseUrl}/api/imprimir-pedido.php`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    pedido_id: pedidoId,
                    categoria: categoria,
                    dispositivo: "movil"
                })
            });
            
            const resultado = await response.json();
            
            if (resultado.success) {
                this.mostrarExito(`Impreso en ${resultado.impresora} (${resultado.metodo_usado})`);
            } else {
                this.mostrarError(`Error: ${resultado.error}`);
            }
            
        } catch (error) {
            this.mostrarError(`Error de conexión: ${error.message}`);
        }
    }
    
    async reimprimirTicket(numeroTicket) {
        try {
            const response = await fetch(`${this.baseUrl}/api/reimprimir.php`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    numero_ticket: numeroTicket
                })
            });
            
            const resultado = await response.json();
            
            if (resultado.success) {
                this.mostrarExito("Ticket reimpreso correctamente");
            } else {
                this.mostrarError(`Error: ${resultado.error}`);
            }
            
        } catch (error) {
            this.mostrarError(`Error: ${error.message}`);
        }
    }
    
    mostrarExito(mensaje) {
        // Mostrar notificación de éxito
        alert(`✅ ${mensaje}`);
    }
    
    mostrarError(mensaje) {
        // Mostrar notificación de error
        alert(`❌ ${mensaje}`);
    }
}

// Uso
const impresion = new ImpresionMovil();

// Imprimir pedido específico
document.getElementById("btn-imprimir").addEventListener("click", () => {
    const pedidoId = document.getElementById("pedido-id").value;
    const categoria = document.getElementById("categoria").value;
    impresion.imprimirPedido(pedidoId, categoria);
});');
echo "</pre>";
echo "</div>";

// Ejemplo 3: API endpoint
echo "<h2>🌐 Ejemplo 3: API Endpoint para el Sistema</h2>";
echo "<div style='border: 1px solid #6f42c1; padding: 20px; margin: 15px 0; border-radius: 8px;'>";
echo "<h4>🔗 Crear endpoint: api/imprimir-pedido.php</h4>";

echo "<pre style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; font-size: 12px;'>";
echo htmlspecialchars('<?php
// api/imprimir-pedido.php
header("Content-Type: application/json");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");

if ($_SERVER["REQUEST_METHOD"] === "OPTIONS") {
    exit(0);
}

require_once "../models/crudImpresionHibrida.php";
require_once "../models/crudPedidos.php";

try {
    $input = json_decode(file_get_contents("php://input"), true);
    
    if (!$input || !isset($input["pedido_id"])) {
        throw new Exception("Datos inválidos");
    }
    
    $pedido_id = $input["pedido_id"];
    $categoria = $input["categoria"] ?? null;
    $dispositivo = $input["dispositivo"] ?? "web";
    
    $impresion = new CrudImpresionHibrida();
    $pedidos = new CrudPedidos();
    
    if ($categoria) {
        // Imprimir solo una categoría específica
        $productos = $pedidos->obtenerProductosPorCategoria($pedido_id, $categoria);
        $ticket = generarTicketCategoria($pedido_id, $categoria, $productos);
        $resultado = $impresion->imprimirPorCategoria($categoria, $ticket);
        
    } else {
        // Imprimir todo el pedido (todas las categorías)
        $resultados = enviarPedidoACocina($pedido_id);
        $resultado = [
            "success" => true,
            "resultados_por_categoria" => $resultados,
            "total_categorias" => count($resultados)
        ];
    }
    
    // Registrar en log
    $impresion->registrarLog($resultado);
    
    echo json_encode($resultado);
    
} catch (Exception $e) {
    echo json_encode([
        "success" => false,
        "error" => $e->getMessage()
    ]);
}
?>');
echo "</pre>";
echo "</div>";

// Ejemplo 4: Integración en el sistema existente
echo "<h2>🔧 Ejemplo 4: Integración en Sistema Existente</h2>";
echo "<div style='border: 1px solid #fd7e14; padding: 20px; margin: 15px 0; border-radius: 8px;'>";
echo "<h4>🔄 Modificar controlador existente</h4>";

echo "<pre style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; font-size: 12px;'>";
echo htmlspecialchars('<?php
// En controllerPedidos.php - Modificar función existente

// ANTES (método antiguo)
function enviarPedido($mesa_id) {
    // Código antiguo que no funcionaba bien...
    $ip_impresora = "**************";
    $resultado = @fsockopen($ip_impresora, 9100, $errno, $errstr, 5);
    // ...
}

// DESPUÉS (método híbrido)
function enviarPedido($mesa_id) {
    require_once "../models/crudImpresionHibrida.php";
    
    $impresion = new CrudImpresionHibrida();
    
    // Obtener pedido
    $pedido = obtenerPedidoPorMesa($mesa_id);
    
    if (!$pedido) {
        return ["success" => false, "error" => "Pedido no encontrado"];
    }
    
    // Cambiar estado del pedido
    actualizarEstadoPedido($pedido["id"], "enviado");
    
    // Imprimir automáticamente por categorías
    $resultados = [];
    $productos = obtenerProductosPedido($pedido["id"]);
    
    // Agrupar por categoría
    $por_categoria = [];
    foreach ($productos as $producto) {
        $categoria = $producto["categoria"];
        $por_categoria[$categoria][] = $producto;
    }
    
    // Imprimir cada categoría
    foreach ($por_categoria as $categoria => $productos_cat) {
        $ticket = generarTicket($pedido, $productos_cat);
        $resultado = $impresion->imprimirPorCategoria($categoria, $ticket);
        $resultados[$categoria] = $resultado;
    }
    
    return [
        "success" => true,
        "pedido_id" => $pedido["id"],
        "resultados" => $resultados
    ];
}
?>');
echo "</pre>";
echo "</div>";

// Ventajas del sistema
echo "<h2>✅ Ventajas del Sistema Híbrido:</h2>";
echo "<div style='background-color: #d4edda; padding: 20px; border-left: 4px solid #28a745; margin: 20px 0;'>";
echo "<ul>";
echo "<li>🔄 <strong>Adaptación automática:</strong> Funciona con o sin proxy</li>";
echo "<li>📱 <strong>Compatible con móviles:</strong> Celulares y tablets</li>";
echo "<li>🌐 <strong>Independiente de IP:</strong> No importa si cambia la IP pública</li>";
echo "<li>⚡ <strong>Fallback inteligente:</strong> Si falla un método, prueba otro</li>";
echo "<li>📊 <strong>Logs detallados:</strong> Registro de todos los intentos</li>";
echo "<li>🎯 <strong>Impresión por categoría:</strong> Automática según producto</li>";
echo "<li>🔧 <strong>Fácil integración:</strong> Solo cambiar unas líneas de código</li>";
echo "</ul>";
echo "</div>";

// Pasos de implementación
echo "<h2>🚀 Pasos para Implementar:</h2>";
echo "<div style='background-color: #fff3cd; padding: 20px; border-left: 4px solid #ffc107; margin: 20px 0;'>";
echo "<ol>";
echo "<li><strong>Configurar sistema híbrido:</strong> Test → 🔄 Impresión Híbrida</li>";
echo "<li><strong>Mapear categorías:</strong> Asignar cada categoría a su impresora</li>";
echo "<li><strong>Probar funcionamiento:</strong> Test → 🧪 Test Sistema Híbrido</li>";
echo "<li><strong>Integrar en código:</strong> Usar CrudImpresionHibrida en controladores</li>";
echo "<li><strong>Crear API endpoints:</strong> Para uso desde celulares</li>";
echo "<li><strong>Probar desde celular:</strong> Verificar funcionamiento móvil</li>";
echo "</ol>";
echo "</div>";

// Botones de navegación
echo "<p style='margin-top: 30px;'>";
echo "<a href='impresion_hibrida.php' style='background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>← Configurar Sistema</a>";
echo "<a href='test_impresion_hibrida.php' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🧪 Probar Sistema</a>";
echo "</p>";
?>
