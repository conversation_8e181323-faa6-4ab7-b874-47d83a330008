<?php
require_once "conexion.php";

class DatosEgresoIngreso extends Conexion
{
	#REGISTRO DE INGRESO
	#-------------------------------------
	 public static function registroIngresoModel($datosModel, $tabla)
		{//echo "<script>alert('Entro CRUD  no')</script>";
			date_default_timezone_set("America/Bogota");
			$fecha_ingreso=strftime("%Y-%m-%d %H:%M:%S");

			$consulta="INSERT INTO $tabla (turno_cajero_id, motivo, cantidad, fecha_hora, punto_id) VALUES (:turno, :motivo,:cantidad, :fecha_hora, :punto_id)";
			//echo "<script>alert('Entro CRUD ".$consulta." no')</script>";
			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->execute();
			$stmt->bindParam(":turno", $datosModel['turno'], PDO::PARAM_INT);
			$stmt->bindParam(":motivo", $datosModel['motivo'], PDO::PARAM_STR);
			$stmt->bindParam(":cantidad", $datosModel['cantidad'], PDO::PARAM_INT);
			$stmt->bindParam(":punto_id", $datosModel['punto_id'], PDO::PARAM_INT);
			$stmt->bindParam(":fecha_hora", $fecha_ingreso, PDO::PARAM_STR);
			//echo "<script>alert('Guardo')</script>";
			if($stmt->execute())
				{	return "success";	}
			else{ return "error";	}
			$stmt->close();
		}
	#-------------------------------------
	#VISTA INGRESO
	#-------------------------------------
	 public static function vistaIngresoModel($tabla, $turno)
		{
			$stmt = Conexion::conectar()->prepare("SELECT i.id AS iid, i.turno_cajero_id AS iturno_cajero_id, i.motivo AS imotivo, i.cantidad AS icantidad, i.fecha_hora AS ifecha_hora, i.punto_id AS ipunto_id, pun.nombre AS punnombre, pun.id AS punid FROM $tabla i, punto pun WHERE i.punto_id=pun.id AND i.turno_cajero_id=$turno");
			$stmt->execute();
			return $stmt->fetchAll();
			$stmt->close();
		}
	#-------------------------------------
	#EDITAR INGRESO
	#-------------------------------------
	 public static function editarIngresoModel($datosModel, $tabla)
		{
			$stmt = Conexion::conectar()->prepare("SELECT id, turno_cajero_id, motivo, cantidad, fecha_hora FROM $tabla WHERE id = :id");
			$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);
			$stmt->execute();
			return $stmt->fetch();
			$stmt->close();
		}
	#-------------------------------------
	#ACTUALIZAR INGRESO
	#-------------------------------------
	 public static function actualizarIngresoModel($datosModel, $tabla)
		{// echo "<script>alert('Entro Actualizar Producto')</script>";
			date_default_timezone_set("America/Bogota");
			$fecha_ingreso=strftime("%Y-%m-%d %H:%M:%S");
			$stmt = Conexion::conectar()->prepare("UPDATE $tabla SET motivo = :motivo, cantidad =:cantidad, fecha_hora =:fecha_hora WHERE id = :id");
			$stmt->bindParam(":motivo", $datosModel['motivo'], PDO::PARAM_STR);
			$stmt->bindParam(":cantidad", $datosModel['cantidad'], PDO::PARAM_INT);
			$stmt->bindParam(":fecha_hora", $fecha_ingreso, PDO::PARAM_STR);
			$stmt->bindParam(":id", $datosModel["id"], PDO::PARAM_INT);
			if($stmt->execute())
				{echo "<script>alert('Guardo Actualizar Ingreso')</script>";return "success";	}
			else{	return "error";			}
			$stmt->close();
		}
	#-------------------------------------

	#BORRAR INGRESO
	#------------------------------------
		public static function borrarIngresoModel($datosModel, $tabla)
		{
			$stmt = Conexion::conectar()->prepare("DELETE FROM $tabla WHERE id = :id");
			$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);
			if($stmt->execute())
				{	return "success";	}
			else{	return "error";		}
			$stmt->close();
		}
#----------------------------------------------

###EGRESO
#----------------------------------------------
	#REGISTRO DE EGRESO
	#-------------------------------------
	 public static function registroEgresoModel($datosModel, $tabla)
		{//echo "<script>alert('Entro CRUD  no')</script>";
			date_default_timezone_set("America/Bogota");
			$fecha_ingreso=strftime("%Y-%m-%d %H:%M:%S");

			$consulta="INSERT INTO $tabla (turno_cajero_id, motivo, cantidad, fecha_hora, punto_id) VALUES (:turno, :motivo,:cantidad, :fecha_hora, :punto_id)";
			//echo "<script>alert('Entro CRUD ".$consulta." no')</script>";
			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->execute();
			$stmt->bindParam(":turno", $datosModel['turno'], PDO::PARAM_INT);
			$stmt->bindParam(":motivo", $datosModel['motivo'], PDO::PARAM_STR);
			$stmt->bindParam(":cantidad", $datosModel['cantidad'], PDO::PARAM_INT);
			$stmt->bindParam(":punto_id", $datosModel['punto_id'], PDO::PARAM_INT);
			$stmt->bindParam(":fecha_hora", $fecha_ingreso, PDO::PARAM_STR);
			//echo "<script>alert('Guardo')</script>";
			if($stmt->execute())
				{	return "success";	}
			else{ return "error";	}
			$stmt->close();
		}
	#-------------------------------------

	#VISTA ENGRESO
	#-------------------------------------
	 public static function vistaEgresoModel($tabla, $turno)
		{
			$stmt = Conexion::conectar()->prepare("SELECT i.id AS iid, i.turno_cajero_id AS iturno_cajero_id, i.motivo AS imotivo, i.cantidad AS icantidad, i.fecha_hora AS ifecha_hora, i.punto_id AS ipunto_id, pun.nombre AS punnombre, pun.id AS punid FROM $tabla i, punto pun order by i.id DESC");
			$stmt->execute();
			return $stmt->fetchAll();
			$stmt->close();
		}

	#EDITAR ENGRESO
	#-------------------------------------

		public static function editarEgresoModel($datosModel, $tabla)
			{
				$stmt = Conexion::conectar()->prepare("SELECT id, turno_cajero_id, motivo, cantidad, fecha_hora FROM $tabla WHERE id = :id");
				$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);
				$stmt->execute();
				return $stmt->fetch();
				$stmt->close();
			}

	#ACTUALIZAR INGRESO
	#-------------------------------------
	public static function actualizarEgresoModel($datosModel, $tabla)
		{// echo "<script>alert('Entro Actualizar Producto')</script>";
			date_default_timezone_set("America/Bogota");
			$fecha_ingreso=strftime("%Y-%m-%d %H:%M:%S");
			$stmt = Conexion::conectar()->prepare("UPDATE $tabla SET motivo = :motivo, cantidad =:cantidad, fecha_hora =:fecha_hora WHERE id = :id");
			$stmt->bindParam(":motivo", $datosModel['motivo'], PDO::PARAM_STR);
			$stmt->bindParam(":cantidad", $datosModel['cantidad'], PDO::PARAM_INT);
			$stmt->bindParam(":fecha_hora", $fecha_ingreso, PDO::PARAM_STR);
			$stmt->bindParam(":id", $datosModel["id"], PDO::PARAM_INT);
			if($stmt->execute())
				{echo "<script>alert('Guardo Actualizar Ingreso')</script>";return "success";	}
			else{	return "error";			}
			$stmt->close();
		}
	#-------------------------------------

	#BORRAR ENGRESO
	#------------------------------------
		public static function borrarEgresoModel($datosModel, $tabla)
		{
			$stmt = Conexion::conectar()->prepare("DELETE FROM $tabla WHERE id = :id");
			$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);
			if($stmt->execute())
				{	return "success";	}
			else{	return "error";		}
			$stmt->close();
		}
   #----------------------------------------------
 }