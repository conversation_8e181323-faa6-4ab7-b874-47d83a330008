# Implementación del Sistema de Gestión de Pedidos por Estados

## Resumen de la Implementación

Se ha implementado un sistema completo de gestión de pedidos que permite:

1. **Gestión de Estados de Pedidos**: borrador → enviado → entregado → facturado
2. **Impresión Automática por Categorías**: bar, cocina, asados
3. **Control de Permisos por Rol**: restricciones para modificar pedidos enviados
4. **Pantalla de Cocina**: para marcar pedidos como entregados
5. **Descuento Automático de Inventario**: al entregar pedidos
6. **Reimpresión de Pedidos**: en caso de fallos

## Archivos Creados/Modificados

### Base de Datos
- `sql/actualizacion_pedidos_estados.sql` - Script de migración de BD

### Modelos (CRUD)
- `models/crudEstadoPedidos.php` - Gestión de estados de pedidos
- `models/crudImpresionPedidos.php` - Gestión de impresiones
- `models/crudPedidoMesaVendido.php` - Actualizado para nuevo sistema

### Controladores
- `controllers/controllerEstadoPedidos.php` - Lógica de negocio de estados
- `controllers/controllerImpresionPedidos.php` - Lógica de impresión
- `controllers/controllerPedidoMesaVendido.php` - Actualizado

### Vistas
- `views/modules/registroPmesa.php` - Interfaz principal actualizada
- `views/modules/pantallaCocina.php` - Nueva pantalla para cocina
- `views/modules/ajaxEstadoPedidos.php` - AJAX para gestión de estados
- `views/modules/ajaxInventarioDescuento.php` - AJAX para inventario

## Flujo del Sistema

### 1. Creación de Pedidos
- Al agregar productos se crea/usa un pedido en estado "borrador"
- Los productos se asocian al pedido actual
- Solo se muestran productos del pedido borrador en la mesa

### 2. Envío a Cocina
- Botón "Enviar Pedido a Cocina" cambia estado a "enviado"
- Se imprime automáticamente en impresoras por categoría:
  - Bar: **************:9100
  - Cocina: **************:9100
  - Asados: **************:9100

### 3. Gestión en Cocina
- Pantalla especial para cada área (bar/cocina/asados)
- Visualización de pedidos pendientes
- Botón para marcar como "entregado"
- Opción de reimpresión

### 4. Entrega y Descuento
- Al marcar como entregado se descuenta automáticamente del inventario
- Se actualiza el estado a "entregado"

### 5. Facturación
- Al facturar se cambia estado a "facturado"
- Se imprime resumen en impresoras correspondientes

## Permisos por Rol

### Administradores (tipo_usuario = 1)
- Pueden modificar cualquier pedido en cualquier estado
- Pueden cancelar pedidos
- Acceso completo a todas las funciones

### Cajeros (tipo_usuario = 3)
- Solo pueden modificar pedidos en estado "borrador"
- No pueden modificar pedidos enviados
- Pueden facturar y crear pedidos

### Meseros (tipo_usuario = 2)
- Solo pueden modificar sus propios pedidos en estado "borrador"
- Pueden crear y enviar pedidos

### Cocina (tipo_usuario = 4)
- Acceso a pantalla de cocina
- Pueden marcar pedidos como entregados
- Pueden reimprimir pedidos

## Configuración de Impresoras

Las impresoras están configuradas en la tabla `impresoras`:

```sql
INSERT INTO `impresoras` (`categoria`, `ip`, `puerto`) VALUES
('bar', '**************', 9100),
('cocina', '**************', 9100),
('asados', '**************', 9100);
```

## URLs del Sistema

- **Mesa**: `https://macarena.anconclub.com/index.php?action=registroPmesa&ida=1`
- **Pantalla Bar**: `https://macarena.anconclub.com/views/modules/pantallaCocina.php?categoria=bar`
- **Pantalla Cocina**: `https://macarena.anconclub.com/views/modules/pantallaCocina.php?categoria=cocina`
- **Pantalla Asados**: `https://macarena.anconclub.com/views/modules/pantallaCocina.php?categoria=asados`

## Características Técnicas

### Compatibilidad
- PHP 7.4+ (compatible con PHP 8.x)
- MySQL 5.7+
- Impresoras térmicas ESC/POS vía TCP/IP

### Seguridad
- Validación de permisos en cada operación
- Sanitización de datos de entrada
- Control de sesiones

### Rendimiento
- Consultas optimizadas con índices
- Carga asíncrona de datos
- Auto-refresh inteligente

## Instalación

1. **Ejecutar script de migración**:
   ```sql
   SOURCE sql/actualizacion_pedidos_estados.sql;
   ```

2. **Verificar configuración de impresoras**:
   - Comprobar IPs y puertos en tabla `impresoras`
   - Verificar conectividad de red

3. **Configurar permisos**:
   - Verificar tipos de usuario en tabla `personas`
   - Asignar roles apropiados

## Uso del Sistema

### Para Meseros/Cajeros:
1. Acceder a mesa: `registroPmesa&ida=X`
2. Agregar productos al pedido borrador
3. Hacer clic en "Enviar Pedido a Cocina"
4. El pedido se imprime automáticamente en las áreas correspondientes

### Para Cocina:
1. Acceder a pantalla de cocina correspondiente
2. Ver pedidos pendientes en tiempo real
3. Preparar productos
4. Marcar como "Entregado" cuando esté listo

### Para Facturación:
1. Los pedidos entregados aparecen en la lista de la mesa
2. Usar botones "Facturar" o "PreFacturar" como antes
3. Se imprime resumen final en impresoras

## Solución de Problemas

### Impresoras no imprimen:
- Verificar conectividad de red
- Comprobar IPs en tabla `impresoras`
- Revisar logs de errores en servidor

### Permisos denegados:
- Verificar tipo_usuario en sesión
- Comprobar estado del pedido
- Solo administradores pueden modificar pedidos enviados

### Inventario no se descuenta:
- Verificar relaciones en `suministros_productos`
- Comprobar stock en tabla `sucursal`
- Revisar logs de errores

## Mantenimiento

- **Backup regular** de la base de datos
- **Monitoreo** de logs de errores
- **Limpieza periódica** de registros antiguos en `pedidos_historial`
- **Verificación** de conectividad con impresoras

## Soporte

Para soporte técnico o modificaciones adicionales, contactar al desarrollador con:
- Logs de errores específicos
- Descripción detallada del problema
- Pasos para reproducir el issue
