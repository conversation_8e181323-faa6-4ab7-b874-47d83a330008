<?php

session_start();

if(!isset($_SESSION["validar"]) || !$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "models/conexion.php";

// Procesar creación de pedido si se envió el formulario
if ($_POST && isset($_POST['crear_pedido'])) {
    echo "<div class='alert alert-info'>🔄 Procesando pedido...</div>";

    try {
        $conexion = Conexion::conectar();
        $conexion->beginTransaction();

        // Debug: Verificar datos recibidos
        echo "<div class='alert alert-info'>";
        echo "📊 <strong>Datos recibidos:</strong><br>";
        echo "Mesa ID: " . $_POST['mesa_id'] . "<br>";
        echo "Usuario sesión: " . $_SESSION["usuario"] . "<br>";
        echo "Productos seleccionados: " . count($_POST['productos'] ?? []) . "<br>";
        echo "</div>";

        // 1. Crear el pedido
        $numero_pedido = 'P' . str_pad(rand(1, 9999), 6, '0', STR_PAD_LEFT);
        $mesa_id = $_POST['mesa_id'];
        $mesero_id = $_SESSION["usuario"];

        echo "<div class='alert alert-info'>📝 Creando pedido: {$numero_pedido}</div>";

        $stmt = $conexion->prepare("
            INSERT INTO pedidos (numero_pedido, mesa_id, mesero_id, cedula_cliente, estado, fecha_pedido, fecha_envio, facturado)
            VALUES (?, ?, ?, '0', 'enviado', NOW(), NOW(), 'n')
        ");
        $resultado = $stmt->execute([$numero_pedido, $mesa_id, $mesero_id]);

        if (!$resultado) {
            throw new Exception("Error al insertar pedido: " . implode(", ", $stmt->errorInfo()));
        }

        $pedido_id = $conexion->lastInsertId();
        echo "<div class='alert alert-success'>✅ Pedido creado con ID: {$pedido_id}</div>";
        
        // 2. Agregar productos al pedido
        $productos_seleccionados = $_POST['productos'] ?? [];

        echo "<div class='alert alert-info'>🍽️ Agregando {" . count($productos_seleccionados) . "} productos...</div>";

        foreach ($productos_seleccionados as $producto_id) {
            $cantidad = $_POST['cantidad_' . $producto_id] ?? 1;

            echo "<div class='alert alert-info'>📦 Procesando producto ID: {$producto_id}, Cantidad: {$cantidad}</div>";

            // Obtener información del producto para determinar la categoría
            $stmt_producto = $conexion->prepare("SELECT categoria FROM productos WHERE id = ?");
            $stmt_producto->execute([$producto_id]);
            $producto = $stmt_producto->fetch(PDO::FETCH_ASSOC);
            $categoria_cocina = $producto['categoria'] ?? 'cocina';

            $stmt = $conexion->prepare("
                INSERT INTO producto_vendido_mesa (pedidos_id, productos_id, mesas_id, cantidad, fecha_hora, mesero, descuento, codigo_descuento, nota, cocina)
                VALUES (?, ?, ?, ?, NOW(), ?, 0, '', 'Producto de prueba', ?)
            ");
            $resultado_producto = $stmt->execute([$pedido_id, $producto_id, $mesa_id, $cantidad, $_SESSION["usuario"], $categoria_cocina]);

            if (!$resultado_producto) {
                throw new Exception("Error al insertar producto {$producto_id}: " . implode(", ", $stmt->errorInfo()));
            }

            echo "<div class='alert alert-success'>✅ Producto {$producto_id} agregado correctamente</div>";
        }

        $conexion->commit();

        echo "<div class='alert alert-success'>";
        echo "✅ <strong>Pedido creado exitosamente!</strong><br>";
        echo "📋 <strong>Número:</strong> {$numero_pedido}<br>";
        echo "🏠 <strong>Mesa:</strong> {$mesa_id}<br>";
        echo "📦 <strong>Productos:</strong> " . count($productos_seleccionados) . "<br>";
        echo "<a href='index.php?action=debug_pedidos_creados' class='btn btn-info'>🔍 Ver Debug</a>";
        echo "</div>";

    } catch (Exception $e) {
        if (isset($conexion)) {
            $conexion->rollBack();
        }
        echo "<div class='alert alert-danger'>❌ Error al crear pedido: " . $e->getMessage() . "</div>";
        echo "<div class='alert alert-warning'>🔧 Detalles del error: " . $e->getFile() . " línea " . $e->getLine() . "</div>";
    }
}

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Crear Pedido de Prueba</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
</head>
<body>

<div class="container">
    <h2>🧪 Crear Pedido de Prueba</h2>
    <hr>
    
    <div class="alert alert-info">
        <strong>💡 Propósito:</strong> Esta herramienta crea pedidos de prueba para verificar el funcionamiento del sistema de pedidos pendientes.
    </div>
    
    <form method="post">
        <div class="panel panel-default">
            <div class="panel-heading">
                <h3 class="panel-title">📋 Configuración del Pedido</h3>
            </div>
            <div class="panel-body">
                <div class="form-group">
                    <label for="mesa_id">Mesa:</label>
                    <select name="mesa_id" id="mesa_id" class="form-control" required>
                        <option value="">Seleccionar mesa...</option>
                        <?php
                        try {
                            $stmt = Conexion::conectar()->prepare("SELECT id, nombre FROM mesas ORDER BY nombre");
                            $stmt->execute();
                            $mesas = $stmt->fetchAll(PDO::FETCH_ASSOC);

                            if (count($mesas) > 0) {
                                foreach ($mesas as $mesa) {
                                    echo "<option value='{$mesa['id']}'>{$mesa['nombre']}</option>";
                                }
                            } else {
                                echo "<option value=''>No hay mesas disponibles</option>";
                            }
                        } catch (Exception $e) {
                            echo "<option value=''>Error: " . $e->getMessage() . "</option>";
                        }
                        ?>
                    </select>
                </div>
            </div>
        </div>
        
        <div class="panel panel-default">
            <div class="panel-heading">
                <h3 class="panel-title">🍽️ Seleccionar Productos</h3>
            </div>
            <div class="panel-body">
                <div class="row">
                    <?php
                    try {
                        $stmt = Conexion::conectar()->prepare("
                            SELECT id, nombre, categoria, precio
                            FROM productos
                            ORDER BY categoria, nombre
                            LIMIT 20
                        ");
                        $stmt->execute();
                        $productos = $stmt->fetchAll(PDO::FETCH_ASSOC);

                        $categorias_agrupadas = [];
                        foreach ($productos as $producto) {
                            $cat = $producto['categoria'] ?: 'Sin categoría';
                            $categorias_agrupadas[$cat][] = $producto;
                        }

                        foreach ($categorias_agrupadas as $categoria => $productos_cat) {
                        echo "<div class='col-md-4'>";
                        echo "<h4>{$categoria}</h4>";
                        
                        foreach ($productos_cat as $producto) {
                            $clasificacion = 'COCINA';
                            if (in_array($producto['categoria'], ['bebidas', 'cervezas', 'licores', 'vinos', 'cocteles', 'refrescos', 'jugos', 'agua', 'bar'])) {
                                $clasificacion = 'BAR';
                            } elseif (in_array($producto['categoria'], ['carnes', 'parrilla', 'asados', 'pescados', 'mariscos'])) {
                                $clasificacion = 'ASADOS';
                            }
                            
                            $clase_label = '';
                            if ($clasificacion == 'BAR') $clase_label = 'label-info';
                            if ($clasificacion == 'ASADOS') $clase_label = 'label-danger';
                            if ($clasificacion == 'COCINA') $clase_label = 'label-warning';
                            
                            echo "<div class='checkbox'>";
                            echo "<label>";
                            echo "<input type='checkbox' name='productos[]' value='{$producto['id']}' onchange='toggleCantidad({$producto['id']})'>";
                            echo " {$producto['nombre']} ";
                            echo "<span class='label {$clase_label}'>{$clasificacion}</span>";
                            echo "<br><small>$" . number_format($producto['precio'], 0) . "</small>";
                            echo "</label>";
                            echo "<div id='cantidad_{$producto['id']}' style='display: none; margin-left: 20px;'>";
                            echo "<input type='number' name='cantidad_{$producto['id']}' value='1' min='1' max='10' class='form-control' style='width: 80px; display: inline-block;'>";
                            echo " <small>unidades</small>";
                            echo "</div>";
                            echo "</div>";
                        }

                        echo "</div>";
                    }

                    } catch (Exception $e) {
                        echo "<div class='alert alert-danger'>Error al cargar productos: " . $e->getMessage() . "</div>";
                    }
                    ?>
                </div>
            </div>
        </div>
        
        <div class="panel panel-success">
            <div class="panel-heading">
                <h3 class="panel-title">🚀 Crear Pedido</h3>
            </div>
            <div class="panel-body">
                <p><strong>El pedido se creará con estado "enviado"</strong> para que aparezca inmediatamente en las páginas de pedidos pendientes.</p>
                
                <button type="submit" name="crear_pedido" class="btn btn-success btn-lg">
                    <i class="glyphicon glyphicon-plus"></i> Crear Pedido de Prueba
                </button>
            </div>
        </div>
    </form>
    
    <hr>
    <div class="row">
        <div class="col-md-3">
            <a href="pedidosCocinaPendientes" class="btn btn-warning btn-block">🍳 Ver Cocina Pendientes</a>
        </div>
        <div class="col-md-3">
            <a href="pedidosBarPendientes" class="btn btn-info btn-block">🍺 Ver Bar Pendientes</a>
        </div>
        <div class="col-md-3">
            <a href="pedidosAsadosPendientes" class="btn btn-danger btn-block">🥩 Ver Asados Pendientes</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=cambiar_estado_pedido" class="btn btn-primary btn-block">🔧 Cambiar Estados</a>
        </div>
    </div>
</div>

<script>
function toggleCantidad(productoId) {
    var checkbox = document.querySelector('input[name="productos[]"][value="' + productoId + '"]');
    var cantidadDiv = document.getElementById('cantidad_' + productoId);
    
    if (checkbox.checked) {
        cantidadDiv.style.display = 'block';
    } else {
        cantidadDiv.style.display = 'none';
    }
}
</script>

</body>
</html>
