<script type="text/javascript">

	function lookup(inputString) {
		if(inputString.length == 0) {
			// Hide the suggestion box.
			$('#suggestions').hide();
		} else {

			$("#destino").load("views/modules/ajaxConductor.php", {cedula: $("#cedula").val()}, function(){
         //alert("recibidos los datos por ajax");		 
     		 });
		}
	} // lookup	

	function buscarPlaca(inputString) {
		if(inputString.length == 0) {
			// Hide the suggestion box.
			$('#suggestions').hide();
		} else {
				//alert("Entro en el else de placa");
			$("#destino").load("views/modules/ajaxPlaca.php", {placa: $("#placa").val()}, function(){
         //alert("recibidos los datos por ajax");		 
     		 });
		}
	} // placa

	function aMayusculas(obj,id){
	    obj = obj.toUpperCase();
	    document.getElementById(id).value = obj;
	}	



</script>
<style>
	.mayuscula{  
text-transform: uppercase;  
}
.rojo {
	color: red;font-weight: bold;
}

</style>

<h1>REGISTRO DE TRACTOMULAS EN EL PARQUEADERO</h1>

<form method="post">.
	<label for=""><b>PLACA</b></label><input type="text" placeholder="Placa ejemplo UQM889" class="mayuscula"  name="placa" id="placa" size="38"  onkeyup="buscarPlaca(this.value);" required><br>
	<label for=""><b>CEDULA</b></label><input type="text" placeholder="Cedula del conductor" name="cedula" id="cedula" size="38" onkeyup="lookup(this.value);" required>	<br>	
	<span id="destino" > </span>
	<label for=""><b>CONDUCTOR</b></label><input name="nombre" placeholder="Nombre del conductor" type="text" id="nombre" size="35" class="mayuscula"  required/><br>
	<label for=""><b>CELULAR</b></label> <input name="celular" placeholder="Celular del conductor" type="text" id="celular" size="38"  required/><br>
	<input type="submit" value="Enviar"><br>
	<input name="mensaje" type="text" id="mensaje" readonly="true" />
</form>

<br><br>

<h3 class="rojo">Ya pueden realizar permisos en el sistema en (ENTRADA DE VEHICULOS-->Permisos)</h3>


<?php
if(!$_SESSION["validar"]){

	header("location:ingresar");
	exit();

}


$registro = new MvcController();
$registro -> registroOrdenController();

if(isset($_GET["action"])){

	if($_GET["action"] == "ok"){
		echo "Registro Exitoso";	
	}

}

/*$fecha1 = new DateTime("2017-02-27 10:28:52");
$fecha2 = new DateTime("1980-09-03 02:33:45");
$fecha = $fecha2->diff($fecha1);
printf('%d años, %d meses, %d días, %d horas, %d minutos', $fecha->y, $fecha->m, $fecha->d, $fecha->h, $fecha->i);*/

?>