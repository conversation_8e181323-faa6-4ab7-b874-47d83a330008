<?php
ob_start();
class controllerSuministro extends Mvc<PERSON>ontroller
 {
	#---------------------Buscar Suministro Nombre en Compra	------------
	 public function ajaxBuscarSuministroCompraController($datosController)
		{
			//echo' <script>alert("Se encontraron resultados controller");</script>';
			$respuesta = DatosSuministro::ajaxBuscarSuministroCompraModel($datosController);
			//$venta = DatosSuministro::ventas($item['pid']);
			return $respuesta;
		}
	#-----------------------------------------------------------------------
	#---------------------Buscar Proceso por Titulo	------------
	 public function ajaxBuscarSuministroController($datosController)
		{
			//echo' <script>alert("Se encontraron resultados controller");</script>';
			$respuesta = DatosSuministro::ajaxBuscarSuministroModel($datosController);
			//$venta = DatosSuministro::ventas($item['pid']);
			return $respuesta;
		}
	#-----------------------------------------------------------------------
	#VISTA DE SUMINISTRO
	#------------------------------------
	 public function ajaxBuscarSuministro1Controller($datosController, $iniPagina=FALSE)
		{	session_start();
			$usuario=$_SESSION["tipo_usuario"];
			//$inicio=$_POST["inicio"];
			$inicio=1;
			$_SESSION["con"]=$rango=10;
			$respuesta1 = DatosSuministro::vistaSuministroModel("suministros");
			$total=count($respuesta1);
			$pagTotal=$total/$rango;
			$pagTotal=ceil($pagTotal);
			$ini=($inicio-1)*$rango;
			echo "<script>alert('total de fila =".$total." Inicio =".$ini." pagina =".$pagTotal."')</script>";
		//	$respuesta = DatosSuministro::vistaSuministroModel("suministros");
			$respuesta = DatosSuministro::vistaSuministroModel("suministros", $ini ,$rango);


			for ($pag=1; $pag <=$pagTotal ; $pag++)
			  { echo '
			 	  <td ><a href="index.php?action=suministro1&pagi1" >'.$pag.'</a></td>';
			  }
			echo '</tr></table>';
			return $respuesta;
		}
	#-------------------------------------
	#EDITAR SUCURSAL
	#------------------------------------
	 public function editarSucursalController()
		{	//session_start();
			$datosController =array('suministro'=>$_GET["sid"],
									'punto_id'=>$_SESSION["punto_id"]);
			$respuesta = DatosSuministro::editarSucusalModel($datosController, "sucursal");
			//echo "<script>alert('suministro  ".$respuesta["snombre"]." vvv".$respuesta["sudescuentoP"]." ');</script>";
			echo'
			<table>
				<tr>
					<td align="right"> Nombre: </td>
					<td> '.$respuesta["snombre"].'</td>
				</tr>
				<tr>
					<td align="right"> Laboratorio: </td>
					<td>'.$respuesta["slaboratorio"].'</td>
				</tr>
				<tr>
					<td align="right">	Aunmento o descuento : </td>
					<td><input type="text" value="'.$respuesta["sudescuentoP"].'" name="sudescuentoP" readonly></td>
				</tr>
				<tr>
					<td align="right">	sucantidad : </td>
					<td><input type="text" value="'.$respuesta["sucantidad"].'" name="sucantidad" required></td>
				</tr>
				<!--<tr>
					<td align="right">	Descripción : </td>
					<td><input type="text" value="'.$respuesta["sudescripcion"].'" name="sudescripcion" readonly></td>
				</tr>-->
			</table>
			 <input type="hidden" value="'.$respuesta["sid"].'" name="sid" required> 	<br>
			 <input type="hidden" value="'.$respuesta["supunto_id"].'" name="supunto_id" required> 	<br>
				 <input type="submit" value="Actualizar">';
		}
	#-------------------------------------
	#EDITAR SUCURSAL 2 Act
	#------------------------------------
	 public function editarSucursalNewController()
		{	//session_start();
			$datosController =array('suministro'=>$_GET["sid"],
									'punto_id'=>$_SESSION["punto_id"]);
			$respuesta = DatosSuministro::editarSucusalModel($datosController, "sucursal");
			//echo "<script>alert('suministro  ".$respuesta["snombre"]." vvv".$respuesta["sudescuentoP"]." ');</script>";
			echo'
			<table>
				<tr>
					<td align="right"> Nombre: </td>
					<td> '.$respuesta["snombre"].'</td>
				</tr>
				<tr>
					<td align="right"> Codigo: </td>
					<td>'.$respuesta["scodigo"].'</td>
				</tr>
				<tr>
					<td align="right">	sucantidad : </td>
					<td><input type="text" value="'.$respuesta["sucantidad"].'" name="sucantidad" required></td>
				</tr>
			</table>
			 <input type="hidden" value="'.$respuesta["sid"].'" name="sid" required> 	<br>
			 <input type="hidden" value="'.$respuesta["supunto_id"].'" name="supunto_id" required> 	<br>
				 <input type="submit" value="Actualizar">';
		}
	#ACTUALIZAR SUCULSAL
	#------------------------------------
	 public function actualizarSucursalController()
		{//echo "<script>alert('Entro Controller Actualizar Producto')</script>";
			if(isset($_POST["sucantidad"]))
			 {
				$datosController = array(  	"sucantidad"=>$_POST["sucantidad"],
											"sid"=>$_POST["sid"],
											"supunto_id"=>$_POST["supunto_id"]);
					$respuesta = DatosSuministro::actualizarSucursalModel($datosController, "sucursal");
					if($respuesta == "success")
						{	header("location:index.php?action=sucursal");	}
					else {	echo "error";	}
			 }
		}
	/**///-----------------------------------------------
	#-------------------------------------------$pagTotal
	#---------------------Buscar Codigó Barra	------------
	 public function ventaController($datosController)
		{
			$respuesta = DatosSuministro::ventasProducto($datosController);
			return $respuesta;
		}
	#-----------------------------------------------------------------------
	#---------------------Buscar Codigó Barra	------------
	 public function ajaxBuscarCodigoController($datosController)
		{
			$respuesta = DatosSuministro::ajaxBuscarCodigoModel($datosController);
			return $respuesta;
		}
	#-------------------------------------------------------------------
	#---------------------Buscar Codigó Barra	------------
	 public function ajaxBuscarCodigo1Controller($datosController)
		{
			$respuesta = DatosSuministro::ajaxBuscarCodigo1Model($datosController);
			return $respuesta;
		}
	#------------------------------------------------------------------
	#Registro de Perdidas Controller
	#------------------------------------------------------
	 public function registroPerdidasController()
		{	session_start();
			$usuario=$_SESSION["usuario"];
			if(isset($_POST["cantidad"]) and isset($_POST["suministro_id"])){

				if ($_POST["suministro_id"]>0)
				 {
					$datosController =array('suministro_id'=>$_POST["suministro_id"],
											'usuario'=>$usuario,
											'cantidad'=>$_POST["cantidad"]);
					$respuesta = DatosSuministro::registroPerdidasModel($datosController);
					if($respuesta == "success")
					 { echo '<script>alert(" Registro Perdida exitosamente");location.href="registroPerdida";</script>';}
					else{ echo '<script>alert("No se guardo intentelo nuevamente");history.back(1);</script>';	}
				 }
				else {	echo '<script>alert("Debe seleccionar un Suministro");history.back(1);</script>'; }
			}
		}
	#----------------------------------------
	#REGISTRO DE SUMINISTRO solo
	#------------------------------------
	 public function registroSuministroSoloController()
		{	//echo "<script>alert('Entro Nombre =".$_POST["nombreSuministroRegistro"]." ')</script>";
		 if(isset($_POST["nombreSuministroRegistro"]))
			{	echo "<script>alert('if suministro=".$_POST["nombreSuministroRegistro"]." ')</script>";
				$datosController =array('codigo'=>$_POST["codigo"],
										'nombre'=>$_POST["nombreSuministroRegistro"],
										'tiposuministro'=>$_POST["tiposuministro"],
										'unidad_id'=>$_POST["unidad_id"],
										'cantidad'=>$_POST["cantidadSuministroRegistro"],
										'iva'=>$_POST["iva"],
										'precio'=>$_POST["precioSuministroRegistro"],
										'cantidad_minima'=>$_POST["cantidadMSuministroRegistro"]);
				$respuesta = DatosSuministro::registroSuministroSoloModel($datosController, "suministros");
				//echo "<script>alert('precio Compra ".$respuesta." no')</script>";
				if($respuesta == "success")	 {	header("location:suministro");	}
				else
				 {	echo "<script>alert('No se pudo guardar, intente nuevamente o comuniquece con el administrador ')</script>";
					header("location:registroSuministro");
				 }/**/
			}
		 ///else echo "<script>alert('Error nombre =".$_POST["nombreSuministroRegistro"]." ')</script>";
		}
	#-------------------------------------
	#REGISTRO DE SUMINISTRO
	#------------------------------------
	 public function registroSuministroController()
		{	//echo "<script>alert('Entro Nombre =".$_POST["nombreSuministroRegistro"]." ')</script>";
		 if(isset($_POST["nombreSuministroRegistro"]))
			{	echo "<script>alert('if suministro=".$_POST["nombreSuministroRegistro"]." ')</script>";
				$datosController =array('codigo'=>$_POST["codigo"],
										'nombre'=>$_POST["nombreSuministroRegistro"],
										'tiposuministro'=>$_POST["tiposuministro"],
										'cantidad'=>$_POST["cantidadSuministroRegistro"],
										'precio'=>$_POST["precioSuministroRegistro"],
										'precioV'=>$_POST["precioRegistro"],

										'unidad_id'=>$_POST["unidad_id"],
										'cantidad_minima'=>$_POST["cantidadMSuministroRegistro"]);
				$respuesta = DatosSuministro::registroSuministroModel($datosController, "suministros");
				//echo "<script>alert('precio Compra ".$respuesta." no')</script>";
				if($respuesta == "success")	 {	header("location:suministro");	}
				else
				 {	echo "<script>alert('No se pudo guardar, intente nuevamente o comuniquece con el administrador ')</script>";
					header("location:registroProductoSuministro");
				 }
			}
		 ///else echo "<script>alert('Error nombre =".$_POST["nombreSuministroRegistro"]." ')</script>";
		}
	#-------------------------------------
	 public function suministroController()
	 {	$respuesta = DatosSuministro::vistaSuministroModel("suministros",1,1);
	 	return $respuesta;
	 }
	#VISTA DE SUMINISTRO simple
	#------------------------------------
	 public function vistaSuministro3Controller()
		{	//session_start();

			//echo "<script>alert('total de fila =".$total." Inicio =".$ini." pagina =".$pagTotal."')</script>";
			$respuesta = DatosSuministro::vistaSuministro1Model("suministros");
			$i=0;
			foreach($respuesta as $row => $item)
			 {
			 	$cont=$i +1;
			 	echo'<tr>
			 		<td>'.$cont.'</td>

					<td>'.$item["scodigo"].'<input type="hidden" value="'.$item["scodigo"].'" name="codigo'.$i.'" ></td>
					<td>'.$item["snombre"].'</td>
					<td><input type="hidden" value="'.$item["scantidad"].'" name="cantidad'.$i.'" tyle="width: 30px;">'.$item["scantidad"].'</td>';
					if ($_SESSION["tipo_usuario"]==1)
					 {	echo'<td>'.$item["sprecio"].'</td>' ;}
					echo'<td>'.$item["pprecio"].'</td>	';
				if ($_SESSION["tipo_usuario"]==1)
				 {echo '
					<td><a href="index.php?action=editarSuministro&id='.$item["sid"].'">Editar</a> <a href="index.php?action=suministro&idBorrar='.$item["sid"].'&cod='.$item["scodigo"].'">Borrar</a></td>';
				 }

			  echo '</tr>';
			  $i++;
			 }
		echo '</tbody>
			  </table > ';
		}
	#-------------------------------------------$pagTotal
	#VISTA DE SUMINISTRO2
	#------------------------------------
	 public function vistaSuministro2Controller()
		{	//session_start();

			//echo "<script>alert('total de fila =".$total." Inicio =".$ini." pagina =".$pagTotal."')</script>";
			$respuesta = DatosSuministro::vistaSuministro1Model("suministros");
			$i=0;
			foreach($respuesta as $row => $item)
			 {
			 	$cont=$i +1;
			 	echo'<tr>
			 		<td>'.$cont.'</td>

					<td>'.$item["scodigo"].'<input type="hidden" value="'.$item["scodigo"].'" name="codigo'.$i.'" ></td>
					<td>'.$item["snombre"].'</td>
					<td><input type="hidden" value="'.$item["scantidad"].'" name="cantidad'.$i.'" tyle="width: 30px;">'.$item["scantidad"].'</td>';
					if ($_SESSION["tipo_usuario"]==1)
					 {	echo'<td>'.$item["sprecio"].'</td>' ;}
					echo'<td>'.$item["pprecio"].'</td>
					<td align="center"><input type="checkbox" value="'.$item["sid"].'" name="sid'.$i.'" ></td>	';
				if ($_SESSION["tipo_usuario"]==1)
				 {echo '
					<td><a href="index.php?action=editarSuministro&id='.$item["sid"].'">Editar</a> <a href="index.php?action=suministro&idBorrar='.$item["sid"].'&cod='.$item["scodigo"].'">Borrar</a></td>';
				 }

			  echo '</tr>';
			  $i++;
			 }
		echo '</tbody>
			  </table > ';
		}
	#-------------------------------------------$pagTotal
	#VISTA DE SUMINISTRO
	#------------------------------------
	 public function vistaSuministroController()
		{	//session_start();
			$usuario=$_SESSION["tipo_usuario"];
			$inicio=$_GET["pagina1"];
			$_SESSION["con"]=$rango=50;
			$respuesta1 = DatosSuministro::vistaSuministroModel("suministros");
			$total=count($respuesta1);
			$_SESSION["totalpag"]=$pagTotal=$total/$rango;
			$pagTotal=ceil($pagTotal);
			$ini=($inicio-1)*$rango;
			if ($ini<0) {	$ini=0;	}
			//echo "<script>alert('total de fila =".$total." Inicio =".$ini." pagina =".$pagTotal."')</script>";
			$respuesta = DatosSuministro::vistaSuministroModel("suministros", $ini ,$rango);
			$f=$ini+1; $i=0;
			foreach($respuesta as $row => $item)
			 {
			 	echo'<tr>
					<td>'.$f.'</td>
					<td>'.$item["scodigo"].'<input type="hidden" value="'.$item["scodigo"].'" name="codigo'.$i.'" ></td>
					<td align="center">'.$item["snombre"].'</td>
					';
					if ($_SESSION["tipo_usuario"]==1)
					 {	echo'<td align="right"">'.$item["sprecio"].'</td>' ;}
					echo'
					<!--td align="center">'.$item["siva"].'</td-->
					<td align="center">'.$item["unombre"].'</td>
					';
				if ($_SESSION["tipo_usuario"]==1)
				 {echo '
						<td align="center">
							<input type="text" value="'.$item["scantidad"].'" name="cantidad'.$i.'" Style="width: 50px;"> /'.$item["scantidad"].'
						</td>
					<td align="center">
						<input type="hidden" name="suid'.$i.'" value="'.$item["sid"].'">
						<input type="checkbox" name="sid'.$i.'" value="'.$item["sid"].'">
					</td>
					<td><a href="index.php?action=editarSuministro&id='.$item["sid"].'">Editar</a> <a href="index.php?action=suministro&idBorrar='.$item["sid"].'&cod='.$item["scodigo"].'">Borrar</a></td>';
				 }
				$f++;$i++;
			  echo '</tr>';
			 }
		echo '</tbody>
				<thead>
					<tr> <td colspan="12"></td></tr>
				</thead>
			  </table >
			  <tableclass="orden">
			   <tr>';
			for ($pag=1; $pag <=$pagTotal ; $pag++)
			  { echo '
			 	  <td ><a href="index.php?action=suministro&pagina1='.$pag.'">'.$pag.'</a></td>';
			  }
			echo '</tr></table>';
		}
	#-------------------------------------------$pagTotal
	#VISTA DE SUMINISTRO cargar sucursal
	#------------------------------------
	 public function vistaSuministro1Controller()
		{	session_start();
			$usuario=$_SESSION["tipo_usuario"];
			$respuesta = DatosSuministro::vistaSuministroModel("suministros");
			echo'<table  class="table table-hover">
		 	 <thead align="center">
	 			<tr >
	 			 <th>No</th><th>Codigo Barra</th><th>Nombre</th><th>Precio</th> <th>Bodega</th><th>*</th>
	 			</tr>
		 	 </thead>
		 	 <tbody align="center">';
			$i=1;
			foreach($respuesta as $row => $item)
			 {
				if ($item["scantidad"]>0)
				 {echo'<tr>
						<td>'.$i.'</td>
						<td>'.$item["scodigo"].'</td>
						<td>'.$item["snombre"].'</td>
						<td>'.$item["pprecio"].'</td>
						<td><input type="text" style="width: 40px;" name="cantidad'.$i.'" value="'.$item["scantidad"].'"/></td>
						<td><input type="checkbox" name="sid'.$i.'" value="'.$item["sid"].'" /></td>
					</tr>';
					$i++;
				 }
			 } $_SESSION["con"]=$i;
		}
	#-------------------------------------------
	#LISTA DE SUMINISTROS
	#------------------------------------
	 public function listaSuministroController()
		{
			$respuesta = DatosSuministro::listaSuministroModel("suministros");
			return $respuesta;
		}
	#-------------------------------------------
	#EDITAR SUMINISTRO id, codigo, tipo_suministro, nombre, cantidad, precio, cantidad_minima, tipo, tamaño, color, tipo_id, activo FROM suministros
	#------------------------------------
	 public function editarSuministroController()
		{
			$datosController = $_GET["id"];
			$respuesta = DatosSuministro::editarSuministroModel($datosController, "suministros");
			echo'
			<table>
				<tr>
					<td align="right">codigo:</td>
					<td>	<input type="text" value="'.$respuesta["scodigo"].'" name="scodigo" required>	</td>
				</tr>
				<tr>
					<td align="right"> snombre: </td>
					<td> <input type="text" value="'.$respuesta["snombre"].'" name="snombre" required>	</td>
				</tr>

				<tr>
					<td align="right"> cantidad: </td>
					<td>	<input type="text" value="'.$respuesta["scantidad"].'" name="scantidad" required>	</td>
				</tr>
				<tr>
					<td align="right">	precio Compra: 	</td>
					<td><input type="text" value="'.$respuesta["sprecio"].'" name="sprecio" required></td>
				</tr>
				<tr>
					<td align="right">	Precio Venta : </td>
					<td><input type="text" value="'.$respuesta["pprecio"].'" name="pprecio" required></td>

				</tr>
			</table>

			 <input type="hidden" value="'.$respuesta["pid"].'" name="pid" required> 	<br>
			 <input type="hidden" value="'.$respuesta["sid"].'" name="idSuministroEditar" required> 	<br>
				 <input type="submit" value="Actualizar">';
		}
	#ACTUALIZAR SUMINISTRO
	#------------------------------------
	 public function actualizarSuministroController()
		{//echo "<script>alert('Entro Controller Actualizar Producto')</script>";
			if(isset($_POST["snombre"]))
			 {
				$datosController = array(  	"scodigo"=>$_POST["scodigo"],
											"snombre"=>$_POST["snombre"],
											"scantidad"=>$_POST["scantidad"],
											"sprecio"=>$_POST["sprecio"],
											"pprecio"=>$_POST["pprecio"],
											"pid"=>$_POST["pid"],
											"id"=>$_POST["idSuministroEditar"]);
					$respuesta = DatosSuministro::actualizarSuministroModel($datosController, "suministros");
					if($respuesta == "success")
						{	header("location:index.php?action=cambioS");	}
					else {	echo "error";	}
			 }
		}
	//-----------------------------------------------
	#BORRAR SUMINISTRO
	#------------------------------------
	 public function borrarSuministroController()
		{
			if(isset($_GET["idBorrar"]))
			 {
				//$datosController = $_GET["idBorrar"];
				$datosController =array('id'=>$_GET["idBorrar"],
										'codigo'=>$_GET["cod"]);
				//echo "<script>alert('entro CRUD".$datosController["id"]." codigo: ".$datosController["codigo"]." get:".$_GET["cod"]."' );</script>";
				$respuesta = DatosSuministro::borrarSuministroModel($datosController, "suministros");
				if($respuesta == "success")
					{	header("location:index.php?action=suministro");	}
				else{	echo "<script>alert('Nose guardo ' );</script>";}
			 }
		}
	#-----------------------------------
	#DESCARGADA SUCURSAL DE SUMINISTRO
	#------------------------------------
	 public function descargarSucursalController()
		{	//echo "<script>alert('Controller entrar1 que pasa".$_POST["codigo"]." ')</script>";
			if(isset($_POST["codigo"]))
			 {
				$datosController =array('codigo'=>$_POST["codigo"],
										 'punto_id'=>$_POST["puntos"],
										'cantidad'=>$_POST["cantidadSuministroRegistro"]);
				//echo "<script>alert('Entro Controller ".$datosController." no')</script>";
			 	//echo "<script>alert('Este es el codigo".$datosController["codigo"]." ')</script>";
			 	$respuesta = DatosSuministro::descargarSucursalModel($datosController, "sucursal");
			 	//echo "<script>alert('respuesta ".$datosController["codigo"]." ')</script>";
				 /*if($respuesta == "success")
					{
						header("location:index.php?action=okcS");
					}
				else
					{
						echo "<script>alert('No cargo los producto intente nuevamente ')</script>";
						header("location:cargarSucursal");
					}
				*/
			 }
		}
	#-------------------------------------
	#CARGADA SUCURSAL DE SUMINISTRO POR UNIDAD
	#------------------------------------
	 public function cargarSucursalController()
		{//echo "<script>alert('Controller codigo : ".$_POST["codigo"]."  puntos : ".$_POST["puntos"]." cantidadSuministroRegistro : ".$_POST["cantidadSuministroRegistro"]."')</script>";
			if(isset($_POST["codigo"]))
			 {
				$datosController =array('codigo'=>$_POST["codigo"],
										 'punto_id'=>$_POST["puntos"],
										 'cajero'=>$_SESSION["usuario"],
										'cantidad'=>$_POST["cantidadSuministroRegistro"]);
				//echo "<script>alert('Este es el codigo".$datosController["codigo"]." ')</script>";
			 	$respuesta = DatosSuministro::cargarSucursalModel($datosController, "sucursal");
			 }
		}
	#-------------------------------------
	#CARGAR SUCURSAL DE SUMINISTRO MAXIVO
	#------------------------------------
	 public function cargarSucursal1Controller()
	  	{ //echo "<script>alert('Controller SID xx:".$_POST['puntos']."')</script>";
		 $punto=$_POST["puntos"];
		 $con=0;
		 if (-1==$punto)
			{
				echo "<script>alert('Seleciones una sucursal')</script>";
				header("location:suministro");
			}
		 else
			{
			 if ($_GET["pagina1"] <=$_SESSION["totalpag"])
				{
					$_SESSION["pagina"]=$_GET["pagina1"]+1 ;
				}
				//echo "<script>alert('Controller SID:".$_SESSION["pagina"]." ')</script>";
			 $sucursal=$_POST["puntos"];
			 for ($i=0; $i <=$_SESSION["con"] ; $i++)
				{
				 if(isset($_POST["sid".$i]) && $_POST["sid".$i]>0 )
					{	//echo "<script>alert('Controller SID:".$_POST["sid".$i]." cantidad: ".$_POST["cantidad".$i]." codigo: ".$_POST["codigo".$i]."suid: ".$_POST["suid".$i]."')</script>";
					 $datosController[$con] =array('sid'=>$_POST["sid".$i],
					 							'suid'=>$_POST["suid".$i],
												'cantidad'=>$_POST["cantidad".$i]);
					 $con++;
					}
				}
			 $respuesta  = DatosSuministro::cargarSucursal1Model($datosController, $sucursal, $con);
			 /**/if ($respuesta =="success")
				{
				 echo'<script>alert("Listo");</script>';
				 header("location:suministro");
				 /*if ($_SESSION["pagina"] <=$_SESSION["totalpag"])
					{
						$_SESSION["pagina"]=$_GET["pagina1"];
						header("location:index.php?action=suministro&pagina1=".$_SESSION["pagina"]);
					}
				 else
					 {	echo'<script>alert("listo");location.href ="suministro";</script>';	}
			 	*/}
			}
	   	}
	#-------------------------------------
	#CONSULTAR SUCURSAL 	<td>'.$item["pprecio"].'</td>
	#------------------------------------
	 public function vistaSucursalController()
	  { //session_start();
		$usuario=$_SESSION["tipo_usuario"];
		$respuesta = DatosSuministro::vistaSucursalModel("sucursal");

			 $formateador2 = new NumberFormatter( 'es_CO', NumberFormatter::CURRENCY );
			 $formateador2->setAttribute( $formateador2::FRACTION_DIGITS, 0 );
			 $formateador = new NumberFormatter( 'en_US', NumberFormatter::DECIMAL );
			 //$formateador2->format();

		foreach($respuesta as $row => $item)
		 {  //if ($item["sucantidad"]>0){}	
			if ($usuario==1){$costo=$item["pcosto"];}else{$costo="";}
				echo'<tr align="center">
					<td>'.$item["scodigo"].'</td>
					<td>'.$item["snombre"].'</td>
					<td>'.$item["sucantidad"].'</td>
					<!--<td align=right>'.$formateador2->format($item["pprecio"]).'</td>-->
					<td align=right>'.$formateador2->format($costo).'</td>
					<td><a href="index.php?action=editarSucursal&sid='.$item["sid"].'">Editar</a> <a href="index.php?action=sucursalBorrar&idBorrar='.$item["sid"].'&cod='.$item["scodigo"].'">Borrar</a></td>
				</tr>';
			
		 }
	  }
	#-------------------------------------<td><a href="index.php?action=editarSuministro&id='.$item["id"].'">Editar</a></td> <td><a href="index.php?action=suministro&idBorrar='.$item["id"].'">Borrar</a></td>

	#-------------------------------------
	#CONSULTAR SUCURSAL Cocina
	#------------------------------------
	 public function vistaSucursalCocinaController()
	 {
		$respuesta = DatosSuministro::vistaSucursalCocinaModel("sucursal");
		foreach($respuesta as $row => $item)
			{
			echo'<tr>
					<td>'.$item["scodigo"].'</td>
					<td>'.$item["snombre"].'</td>
					<td>'.$item["sucantidad"].'</td>
				</tr>';
			}
	 }
	#-------------------------------------
	#BÓVEDA
	#------------------------------------
	 public function bovedaController()
		{
			//echo "<script>alert('Controller entrar".$_POST["codigo"]." ')</script>";
			if(isset($_POST["codigo"]))
				{
					$datosController =array('codigo'=>$_POST["codigo"],
											'cantidad'=>$_POST["cantidadSuministroRegistro"]);
					//echo "<script>alert('Entro Controller ".$datosController." no')</script>";

				 	//echo "<script>alert('Este es el codigo".$datosController["codigo"]." ')</script>";
				 	$respuesta = DatosSuministro::cargarSucursalModel($datosController, "sucursal");
				 	//echo "<script>alert('respuesta ".$datosController["codigo"]." ')</script>";
					 /*if($respuesta == "success")
						{
							header("location:index.php?action=okcS");
						}
					else
						{
							echo "<script>alert('No cargo los producto intente nuevamente ')</script>";
							header("location:cargarSucursal");
						}
					*/
				}
		}
	#-------------------------------------
 }
