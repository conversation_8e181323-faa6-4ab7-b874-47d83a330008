<?php
class ImpresionPedidosController {
    private $impresoras = [
        'bar' => ['ip' => '**************', 'port' => 9100],
        'cocina' => ['ip' => '**************', 'port' => 9100],
        'asados' => ['ip' => '**************', 'port' => 9100]
    ];

    public function imprimirPedidoPorCategoria($pedidoId, $db) {
        // Obtener productos del pedido con su categoría
        $stmt = $db->prepare("
            SELECT p.nombre, ppm.cantidad, p.categoria
            FROM pedido_productos_mesa ppm
            JOIN productos p ON ppm.productos_id = p.id
            WHERE ppm.pedidos_id = ?
        ");
        $stmt->execute([$pedidoId]);
        $productos = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Agrupar productos por categoría
        $porCategoria = [];
        foreach ($productos as $producto) {
            $cat = $producto['categoria'] ?? 'bar';
            if (!isset($porCategoria[$cat])) $porCategoria[$cat] = [];
            $porCategoria[$cat][] = $producto;
        }

        // Imprimir en cada impresora correspondiente
        foreach ($porCategoria as $categoria => $items) {
            if (isset($this->impresoras[$categoria])) {
                $this->imprimirEnImpresora(
                    $this->impresoras[$categoria]['ip'],
                    $this->impresoras[$categoria]['port'],
                    $items,
                    $categoria
                );
            }
        }
    }

    private function imprimirEnImpresora($ip, $port, $productos, $categoria) {
        $contenido = "PEDIDO - " . strtoupper($categoria) . "\n";
        foreach ($productos as $p) {
            $contenido .= "{$p['nombre']} x {$p['cantidad']}\n";
        }
        $contenido .= "\n----------------------\n";

        $fp = @fsockopen($ip, $port, $errno, $errstr, 5);
        if ($fp) {
            fwrite($fp, $contenido);
            fclose($fp);
        }
    }

    // ...existing methods...
}