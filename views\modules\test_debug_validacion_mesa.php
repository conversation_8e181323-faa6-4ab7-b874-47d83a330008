<!DOCTYPE html>
<html>
<head>
    <title>🔧 Debug Validación Mesa</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1000px; margin: 0 auto; }
        .card { background: #f8f9fa; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #007bff; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .warning { border-left-color: #ffc107; background: #fff3cd; }
        .success { border-left-color: #28a745; background: #d4edda; }
        .btn { padding: 15px 30px; margin: 10px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold; font-size: 16px; cursor: pointer; border: none; }
        .btn-primary { background: #007bff; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-success { background: #28a745; color: white; }
        h1 { color: #007bff; text-align: center; }
        h2 { color: #495057; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .form-group { margin: 15px 0; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input { width: 200px; padding: 8px; border: 1px solid #ccc; border-radius: 3px; }
        .result { background: white; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; margin: 10px 0; min-height: 50px; }
        .debug-info { background: #e9ecef; padding: 10px; border-radius: 3px; font-family: monospace; margin: 10px 0; }
    </style>
</head>
<body>

<div class="container">
    <h1>🔧 Debug Validación de Pagos - Mesa</h1>
    <p style="text-align: center; font-size: 18px; color: #6c757d;">
        Diagnóstico de por qué no funciona la validación en registroPmesa.php
    </p>

    <div class="card error">
        <h2>❌ Problema Identificado</h2>
        <p>La validación está implementada en registroPmesa.php pero puede no estar funcionando por:</p>
        
        <ul>
            <li>🔍 <strong>Función formatCurrency():</strong> Puede estar devolviendo valores incorrectos</li>
            <li>🔍 <strong>Campos de entrada:</strong> Los IDs de los campos pueden no existir</li>
            <li>🔍 <strong>Valores formateados:</strong> Los valores pueden estar en formato de moneda</li>
            <li>🔍 <strong>Timing:</strong> La validación puede ejecutarse antes de que se carguen los valores</li>
        </ul>
    </div>

    <div class="card">
        <h2>🧪 Simulador de Función formatCurrency()</h2>
        <p>Vamos a probar la función formatCurrency() que usa registroPmesa.php:</p>
        
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div>
                <h4>📝 Campos de Prueba</h4>
                <div class="form-group">
                    <label>Total:</label>
                    <input type="text" id="total_debug" value="50000" onchange="testFormatCurrency()">
                </div>
                <div class="form-group">
                    <label>Efectivo:</label>
                    <input type="text" id="efectivo_debug" value="30000" onchange="testFormatCurrency()">
                </div>
                <div class="form-group">
                    <label>Tarjeta:</label>
                    <input type="text" id="tarjeta_debug" value="15000" onchange="testFormatCurrency()">
                </div>
                <div class="form-group">
                    <label>Nequi:</label>
                    <input type="text" id="nequi_debug" value="0" onchange="testFormatCurrency()">
                </div>
                <div class="form-group">
                    <label>Daviplata:</label>
                    <input type="text" id="daviplata_debug" value="0" onchange="testFormatCurrency()">
                </div>
                <div class="form-group">
                    <label>Bancolombia:</label>
                    <input type="text" id="bancolombia_debug" value="0" onchange="testFormatCurrency()">
                </div>
            </div>
            
            <div>
                <h4>🔧 Resultados de formatCurrency()</h4>
                <div id="resultados_format" class="debug-info">
                    Los resultados aparecerán aquí...
                </div>
            </div>
        </div>
        
        <button class="btn btn-primary" onclick="testValidacionCompleta()">🧪 Test Validación Completa</button>
        <button class="btn btn-danger" onclick="testPagoInsuficiente()">❌ Test Pago Insuficiente</button>
        <button class="btn btn-success" onclick="testPagoCorrecto()">✅ Test Pago Correcto</button>
        
        <div id="resultado_validacion" class="result">
            Los resultados de validación aparecerán aquí...
        </div>
    </div>

    <div class="card warning">
        <h2>🔍 Análisis del Código</h2>
        <p>Revisando el código de registroPmesa.php línea 477:</p>
        
        <div class="debug-info">
let total=formatCurrency(document.getElementById('total'));
        </div>
        
        <p><strong>Posible problema:</strong> La función formatCurrency() puede estar devolviendo un valor formateado en lugar del valor numérico puro.</p>
        
        <h4>🔧 Solución Propuesta:</h4>
        <p>Cambiar la obtención del total para usar el valor numérico directamente:</p>
        
        <div class="debug-info">
// En lugar de:
let total=formatCurrency(document.getElementById('total'));

// Usar:
let total = parseInt(document.getElementById('total').value.replace(/\D/g, '')) || 0;
        </div>
    </div>

    <div class="card">
        <h2>🔗 Enlaces de Prueba</h2>
        <p>Prueba la validación en una mesa real:</p>
        
        <a href="../../index.php?action=registroPmesa&ida=5" class="btn btn-primary" target="_blank">
            🏠 Mesa 5 (Test Real)
        </a>
        
        <a href="test_validacion_pagos_mesa.php" class="btn btn-success" target="_blank">
            🧪 Test de Validación
        </a>
    </div>

</div>

<script>
// Función formatCurrency() copiada de registroPmesa.php
function formatCurrency(input) {
    if (!input || !input.value) {
        return 0;
    }

    let cleanValue = input.value.replace(/\D/g, '');
    let value = parseInt(cleanValue);

    // Si no es un número válido, usar 0
    if (isNaN(value)) {
        value = 0;
    }

    let options = { style: 'currency', currency: 'COP', minimumFractionDigits: 0, maximumFractionDigits: 0 };
    let formatter = new Intl.NumberFormat('es-CO', options);
    input.value = formatter.format(value);

    // Devolver el valor numérico sin formato
    return value;
}

function testFormatCurrency() {
    const campos = ['total_debug', 'efectivo_debug', 'tarjeta_debug', 'nequi_debug', 'daviplata_debug', 'bancolombia_debug'];
    
    let html = '<h5>🔧 Resultados de formatCurrency():</h5>';
    
    campos.forEach(campo => {
        const elemento = document.getElementById(campo);
        const valorOriginal = elemento.value;
        const valorFormateado = formatCurrency(elemento);
        
        html += '<p><strong>' + campo.replace('_debug', '') + ':</strong><br>';
        html += 'Valor original: "' + valorOriginal + '"<br>';
        html += 'Valor devuelto: ' + valorFormateado + '<br>';
        html += 'Campo después: "' + elemento.value + '"</p>';
    });
    
    document.getElementById('resultados_format').innerHTML = html;
}

function testValidacionCompleta() {
    // Simular la lógica exacta de registroPmesa.php
    let efectivo = formatCurrency(document.getElementById('efectivo_debug'));
    let tarjeta = formatCurrency(document.getElementById('tarjeta_debug'));
    let nequi = formatCurrency(document.getElementById('nequi_debug'));
    let daviplata = formatCurrency(document.getElementById('daviplata_debug'));
    let bancolombia = formatCurrency(document.getElementById('bancolombia_debug'));
    let total = formatCurrency(document.getElementById('total_debug'));
    
    let totalPagado = efectivo + tarjeta + nequi + daviplata + bancolombia;
    
    let html = '<h4>🧪 Test de Validación Completa:</h4>';
    html += '<div class="debug-info">';
    html += '<strong>Valores obtenidos:</strong><br>';
    html += 'efectivo: ' + efectivo + '<br>';
    html += 'tarjeta: ' + tarjeta + '<br>';
    html += 'nequi: ' + nequi + '<br>';
    html += 'daviplata: ' + daviplata + '<br>';
    html += 'bancolombia: ' + bancolombia + '<br>';
    html += 'total: ' + total + '<br>';
    html += 'totalPagado: ' + totalPagado + '<br>';
    html += '</div>';
    
    if (totalPagado < total) {
        let faltante = total - totalPagado;
        html += '<div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin-top: 10px;">';
        html += '<strong>❌ VALIDACIÓN FUNCIONANDO</strong><br>';
        html += 'Pago insuficiente detectado correctamente<br>';
        html += 'Falta: $' + faltante.toLocaleString();
        html += '</div>';
    } else {
        let cambio = totalPagado - total;
        html += '<div style="background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin-top: 10px;">';
        html += '<strong>✅ PAGO VÁLIDO</strong><br>';
        html += 'Cambio: $' + cambio.toLocaleString();
        html += '</div>';
    }
    
    document.getElementById('resultado_validacion').innerHTML = html;
}

function testPagoInsuficiente() {
    // Configurar valores para pago insuficiente
    document.getElementById('total_debug').value = '50000';
    document.getElementById('efectivo_debug').value = '30000';
    document.getElementById('tarjeta_debug').value = '15000';
    document.getElementById('nequi_debug').value = '0';
    document.getElementById('daviplata_debug').value = '0';
    document.getElementById('bancolombia_debug').value = '0';
    
    testFormatCurrency();
    setTimeout(testValidacionCompleta, 100);
}

function testPagoCorrecto() {
    // Configurar valores para pago correcto
    document.getElementById('total_debug').value = '50000';
    document.getElementById('efectivo_debug').value = '30000';
    document.getElementById('tarjeta_debug').value = '0';
    document.getElementById('nequi_debug').value = '25000';
    document.getElementById('daviplata_debug').value = '0';
    document.getElementById('bancolombia_debug').value = '0';
    
    testFormatCurrency();
    setTimeout(testValidacionCompleta, 100);
}

// Test automático al cargar
document.addEventListener('DOMContentLoaded', function() {
    testFormatCurrency();
});
</script>

</body>
</html>
