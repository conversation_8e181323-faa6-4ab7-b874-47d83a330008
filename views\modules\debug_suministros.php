<?php
// Debug específico para el error de suministros
echo "<h1>🐛 Debug Suministros - <PERSON><PERSON><PERSON></h1>";
echo "<p><strong>Captura del error exacto al guardar suministro</strong></p>";

// Habilitar reporte de errores
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    require_once '../../models/conexion.php';
    require_once '../../models/crudSuministroProducto.php';
    require_once '../../controllers/controllerSuministroProducto.php';
    
    echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0;'>";
    echo "<h4>✅ Archivos cargados correctamente</h4>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
    echo "<h4>❌ Error cargando archivos</h4>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
    exit;
}

// Simular el proceso de guardado
echo "<h2>🧪 Simulación del Proceso de Guardado:</h2>";

if (isset($_POST['test_guardar'])) {
    echo "<div style='border: 1px solid #007bff; padding: 20px; margin: 15px 0; border-radius: 8px;'>";
    echo "<h4>🔄 Ejecutando proceso de guardado...</h4>";
    
    $producto_id = intval($_POST['producto_id']);
    $codigo_suministro = trim($_POST['codigo_suministro']);
    $cantidad = floatval($_POST['cantidad']);
    
    echo "<p><strong>Datos recibidos:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Producto ID:</strong> $producto_id</li>";
    echo "<li><strong>Código Suministro:</strong> '$codigo_suministro'</li>";
    echo "<li><strong>Cantidad:</strong> $cantidad</li>";
    echo "</ul>";
    
    // Paso 1: Verificar que el producto existe
    echo "<h5>📋 Paso 1: Verificar Producto</h5>";
    try {
        $conexion = new Conexion();
        $pdo = $conexion->conectar();
        
        $stmt = $pdo->prepare("SELECT id, nombre FROM productos WHERE id = :id");
        $stmt->bindParam(":id", $producto_id, PDO::PARAM_INT);
        $stmt->execute();
        $producto = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($producto) {
            echo "<p>✅ <strong>Producto encontrado:</strong> {$producto['nombre']} (ID: {$producto['id']})</p>";
        } else {
            echo "<p>❌ <strong>Error:</strong> Producto con ID $producto_id no encontrado</p>";
            echo "</div>";
            return;
        }
        
    } catch (Exception $e) {
        echo "<p>❌ <strong>Error verificando producto:</strong> " . $e->getMessage() . "</p>";
        echo "</div>";
        return;
    }
    
    // Paso 2: Buscar suministro por código
    echo "<h5>📦 Paso 2: Buscar Suministro</h5>";
    try {
        $resultado_suministro = DatosSuministroProducto::suministroCodigoModel($codigo_suministro);
        
        if ($resultado_suministro) {
            echo "<p>✅ <strong>Suministro encontrado:</strong></p>";
            echo "<ul>";
            echo "<li><strong>ID:</strong> {$resultado_suministro['id']}</li>";
            echo "<li><strong>Código:</strong> {$resultado_suministro['codigo']}</li>";
            echo "<li><strong>Nombre:</strong> {$resultado_suministro['nombre']}</li>";
            echo "<li><strong>Activo:</strong> {$resultado_suministro['activo']}</li>";
            echo "</ul>";
        } else {
            echo "<p>❌ <strong>Error:</strong> No se encontró suministro con código '$codigo_suministro'</p>";
            
            // Buscar suministros similares
            echo "<p><strong>Suministros con códigos similares:</strong></p>";
            $stmt = $pdo->prepare("SELECT id, codigo, nombre FROM suministros WHERE codigo LIKE :codigo AND activo = 's' LIMIT 5");
            $codigo_like = "%".$codigo_suministro."%";
            $stmt->bindParam(":codigo", $codigo_like, PDO::PARAM_STR);
            $stmt->execute();
            $similares = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if ($similares) {
                echo "<ul>";
                foreach ($similares as $similar) {
                    echo "<li>{$similar['codigo']} - {$similar['nombre']}</li>";
                }
                echo "</ul>";
            } else {
                echo "<p>No se encontraron suministros similares</p>";
            }
            
            echo "</div>";
            return;
        }
        
    } catch (Exception $e) {
        echo "<p>❌ <strong>Error buscando suministro:</strong> " . $e->getMessage() . "</p>";
        echo "</div>";
        return;
    }
    
    // Paso 3: Verificar si ya existe la relación
    echo "<h5>🔗 Paso 3: Verificar Relación Existente</h5>";
    try {
        $stmt = $pdo->prepare("SELECT id, cantidades FROM suministros_productos WHERE producto_id = :producto_id AND suministro_id = :suministro_id");
        $stmt->bindParam(":producto_id", $producto_id, PDO::PARAM_INT);
        $stmt->bindParam(":suministro_id", $resultado_suministro['id'], PDO::PARAM_INT);
        $stmt->execute();
        $relacion_existente = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($relacion_existente) {
            echo "<p>⚠️ <strong>Relación ya existe:</strong> ID {$relacion_existente['id']}, Cantidad actual: {$relacion_existente['cantidades']}</p>";
            echo "<p>Se actualizará la cantidad existente</p>";
        } else {
            echo "<p>✅ <strong>Nueva relación:</strong> Se creará una nueva entrada</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ <strong>Error verificando relación:</strong> " . $e->getMessage() . "</p>";
        echo "</div>";
        return;
    }
    
    // Paso 4: Ejecutar el guardado
    echo "<h5>💾 Paso 4: Ejecutar Guardado</h5>";
    try {
        $datosController = array(
            'producto_id' => $producto_id,
            'codigo' => $codigo_suministro,
            'cantidades' => $cantidad
        );
        
        echo "<p><strong>Datos para el modelo:</strong></p>";
        echo "<pre>" . print_r($datosController, true) . "</pre>";
        
        $respuesta = DatosSuministroProducto::registroSuministroPModel($datosController, "suministros_productos");
        
        echo "<p><strong>Respuesta del modelo:</strong> '$respuesta'</p>";
        
        switch($respuesta) {
            case "success":
                echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0;'>";
                echo "<h5>✅ ¡Guardado Exitoso!</h5>";
                echo "<p>El suministro se asignó correctamente al producto</p>";
                echo "</div>";
                break;
            case "error_suministro_no_encontrado":
                echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
                echo "<h5>❌ Suministro No Encontrado</h5>";
                echo "<p>No se encontró el suministro con código: $codigo_suministro</p>";
                echo "</div>";
                break;
            case "error_datos_incompletos":
                echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
                echo "<h5>❌ Datos Incompletos</h5>";
                echo "<p>Faltan datos requeridos para el guardado</p>";
                echo "</div>";
                break;
            default:
                echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
                echo "<h5>❌ Error Desconocido</h5>";
                echo "<p>Respuesta inesperada: $respuesta</p>";
                echo "</div>";
                break;
        }
        
    } catch (Exception $e) {
        echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
        echo "<h5>❌ Excepción Durante el Guardado</h5>";
        echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
        echo "<p><strong>Archivo:</strong> " . $e->getFile() . "</p>";
        echo "<p><strong>Línea:</strong> " . $e->getLine() . "</p>";
        echo "<p><strong>Stack Trace:</strong></p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
        echo "</div>";
    }
    
    // Paso 5: Verificar resultado final
    echo "<h5>🔍 Paso 5: Verificar Resultado</h5>";
    try {
        $stmt = $pdo->prepare("
            SELECT sp.id, sp.cantidades, s.codigo, s.nombre as suministro_nombre, p.nombre as producto_nombre
            FROM suministros_productos sp
            INNER JOIN suministros s ON sp.suministro_id = s.id
            INNER JOIN productos p ON sp.producto_id = p.id
            WHERE sp.producto_id = :producto_id AND s.codigo = :codigo
        ");
        $stmt->bindParam(":producto_id", $producto_id, PDO::PARAM_INT);
        $stmt->bindParam(":codigo", $codigo_suministro, PDO::PARAM_STR);
        $stmt->execute();
        $resultado_final = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($resultado_final) {
            echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0;'>";
            echo "<h5>✅ Verificación Final Exitosa</h5>";
            echo "<p><strong>Producto:</strong> {$resultado_final['producto_nombre']}</p>";
            echo "<p><strong>Suministro:</strong> {$resultado_final['suministro_nombre']} ({$resultado_final['codigo']})</p>";
            echo "<p><strong>Cantidad:</strong> {$resultado_final['cantidades']}</p>";
            echo "<p><strong>ID Relación:</strong> {$resultado_final['id']}</p>";
            echo "</div>";
        } else {
            echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
            echo "<h5>❌ Verificación Final Fallida</h5>";
            echo "<p>No se encontró la relación en la base de datos después del guardado</p>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ <strong>Error en verificación final:</strong> " . $e->getMessage() . "</p>";
    }
    
    echo "</div>";
}

// Formulario de test
echo "<h2>🧪 Formulario de Test:</h2>";
echo "<form method='POST' style='background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>🎯 Probar Guardado de Suministro:</h4>";

echo "<div style='margin: 15px 0;'>";
echo "<label><strong>ID del Producto:</strong></label><br>";
echo "<input type='number' name='producto_id' value='78' required style='padding: 8px; border: 1px solid #ccc; border-radius: 3px; width: 100px;'>";
echo "<small style='display: block; color: #666;'>ID del producto al que asignar el suministro</small>";
echo "</div>";

echo "<div style='margin: 15px 0;'>";
echo "<label><strong>Código del Suministro:</strong></label><br>";
echo "<input type='text' name='codigo_suministro' placeholder='Ej: 123456' required style='padding: 8px; border: 1px solid #ccc; border-radius: 3px; width: 200px;'>";
echo "<small style='display: block; color: #666;'>Código del suministro a asignar</small>";
echo "</div>";

echo "<div style='margin: 15px 0;'>";
echo "<label><strong>Cantidad:</strong></label><br>";
echo "<input type='number' name='cantidad' value='1' step='0.01' min='0.01' required style='padding: 8px; border: 1px solid #ccc; border-radius: 3px; width: 100px;'>";
echo "<small style='display: block; color: #666;'>Cantidad del suministro necesaria</small>";
echo "</div>";

echo "<button type='submit' name='test_guardar' style='background-color: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold;'>🧪 Probar Guardado</button>";
echo "</form>";

// Mostrar suministros disponibles para referencia
echo "<h2>📦 Suministros Disponibles para Prueba:</h2>";
try {
    $conexion = new Conexion();
    $pdo = $conexion->conectar();
    
    $stmt = $pdo->query("SELECT id, codigo, nombre FROM suministros WHERE activo = 's' ORDER BY codigo LIMIT 15");
    $suministros = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($suministros) {
        echo "<div style='background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<table style='width: 100%; border-collapse: collapse;'>";
        echo "<tr style='background-color: #ffeaa7;'>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Código</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Nombre</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Acción</th>";
        echo "</tr>";
        
        foreach ($suministros as $suministro) {
            echo "<tr>";
            echo "<td style='border: 1px solid #ddd; padding: 8px; font-weight: bold;'>{$suministro['codigo']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$suministro['nombre']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>";
            echo "<button onclick=\"document.querySelector('input[name=codigo_suministro]').value='{$suministro['codigo']}'\" style='background-color: #28a745; color: white; padding: 4px 8px; border: none; border-radius: 3px; cursor: pointer; font-size: 12px;'>Usar</button>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p>⚠️ Error al obtener suministros: " . $e->getMessage() . "</p>";
}

// Botones de navegación
echo "<p style='margin-top: 30px;'>";
echo "<a href='test_suministros_productos.php' style='background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>← Test General</a>";
echo "<a href='index.php?action=registroReseta&id=78' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🧪 Probar en Sistema Real</a>";
echo "</p>";
?>
