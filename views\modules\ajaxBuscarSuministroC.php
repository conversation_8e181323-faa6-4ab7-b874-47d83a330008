<?php
require_once "../../models/crud.php";
require_once "../../models/crudSuministro.php";
require_once "../../controllers/controller.php";
require_once "../../controllers/controllerSuministro.php";
	ini_set("session.cookie_lifetime","28800");
	ini_set("session.gc_maxlifetime","28800");
		//echo'<br> <script>alert("Entro al ajax Buscar Suministros '.$_POST['placa'].'");</script> <br>';
	 if(isset($_POST['placa']))
		 {
			$queryString = $_POST['placa'];
			//echo'<br> <script>alert("Entro al ajax if");</script> <br>';
			$ajax=new controllerSuministro();
			$r=$ajax->ajaxBuscarCodigoController($queryString);
			if ($r>0)
			 {	//echo' <script>alert("Se encontraron resultados");</script>';
				echo'<form method="post"  >
					<table  class="table table-hover">
				 	 <thead align="center">
			 			<tr >
			 			 <th>Nombre</th>  <th>Codigo <PERSON></th> <th>Referencia</th> <th>Genero</th> <th>Marca</th> <th>Talla</th><th>Color</th> <th>Precio</th> <th>Bodega</th> <th>Sucursal</th> <th>Venta</th>
			 			</tr>
				 	 </thead>
				 	 <tbody align="center">';
				foreach ($r as $row => $item)
				 {	$v=$ajax->ventaController($item['pid']);
				 	if ($v>0)
				 	 { $c=$v['cantidad'];	}
				 	else { $c=0;}
 					echo'<tr >
 					 		<td>'.$item['snombre'].'</td>
 					 		<td>'.$item['scodigo'].'</td>
 					 		<td>'.$item['sreferencia'].'</td>
 					 		<td>'.$item['sgenero'].'</td>
 					 		<td>'.$item['smarca'].'</td>
 					 		<td>'.$item['stalla'].'</td>
 					 		<td>'.$item['scolor'].'</td>
 					 		<td>'.$item['pprecio'].'</td>
 					 		<td>'.$item['scantidad'].'</td>
 					 		<td>'.$item['sucantidad'].'</td>

 					 		<td>'.$c.'</td>
 					 	</tr>';
 				 }
				echo'	</tbody>
 					 	</table></form>';
			 }
			else
			 {	echo' <h3>No se encontraron Resultados</h3>';	}
		 }




?>