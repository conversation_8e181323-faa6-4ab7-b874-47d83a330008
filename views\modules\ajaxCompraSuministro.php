<?php
	require_once "../../models/crud.php";
	require_once "../../models/crudFacturaAja.php";
	require_once "../../controllers/controller.php";
	require_once "../../controllers/controllerCompraSuministro.php";
	ini_set("session.cookie_lifetime","28800");
	ini_set("session.gc_maxlifetime","28800");
	$registroCsuministro = new controllerCompraSuministro();
	$proveedores = $registroCsuministro -> listaProveedoresController();
	$registroCsuministro -> registroCompraSuministroController();


	echo "<script>alert('entro al ajax  Factura')</script>";
	//echo "<script>alert('entro al ajax   tipo de pago: ".$_POST['pago']." efectivo: ".$_POST['efectivo']." total: ".$_POST['total']."Cedula: ".$_POST['pcedula']."')</script>";

	$matriz = $_POST['matriz'];//pago en efectivo
	$fila = $_POST['trs']; //tipo de pago
	$_SESSION["compra"]=$matriz;
	for ($i=0; $i <$fila ; $i++)
	 {

		echo "<script>alert('producto".$matriz[$i]['suministro']." cantidad".$matriz[$i]['cantidad']." precio ".$matriz[$i]['precio']." ')</script>";
	 }
