<!DOCTYPE html>
<html>
<head>
    <title>🧪 Test Mesa Validación Real</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1000px; margin: 0 auto; }
        .card { background: #f8f9fa; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #007bff; }
        .success { border-left-color: #28a745; background: #d4edda; }
        .warning { border-left-color: #ffc107; background: #fff3cd; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .btn { padding: 15px 30px; margin: 10px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold; font-size: 16px; cursor: pointer; border: none; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        h1 { color: #007bff; text-align: center; }
        h2 { color: #495057; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
    </style>
</head>
<body>

<div class="container">
    <h1>🧪 Test Mesa Validación Real</h1>
    <p style="text-align: center; font-size: 18px; color: #6c757d;">
        Prueba la validación de pagos en una mesa real
    </p>

    <div class="card success">
        <h2>✅ Correcciones Implementadas</h2>
        <p>He implementado las siguientes correcciones en registroPmesa.php:</p>
        
        <ul>
            <li>✅ <strong>Obtención del total corregida:</strong> Ahora obtiene el valor numérico directamente</li>
            <li>✅ <strong>Debug agregado:</strong> Muestra valores en la consola para diagnóstico</li>
            <li>✅ <strong>Validación mejorada:</strong> Compara correctamente suma de pagos vs total</li>
            <li>✅ <strong>Mensajes detallados:</strong> Muestra exactamente cuánto falta y en qué formas</li>
        </ul>
    </div>

    <div class="card">
        <h2>🧪 Instrucciones de Prueba</h2>
        <p>Para probar la validación de pagos en una mesa real:</p>
        
        <ol>
            <li><strong>Ve a una mesa:</strong> Haz clic en "Mesa 5" abajo</li>
            <li><strong>Agrega productos:</strong> Si no hay productos, agrega algunos</li>
            <li><strong>Ve a facturación:</strong> Haz clic en el botón de facturar</li>
            <li><strong>Prueba pago insuficiente:</strong>
                <ul>
                    <li>Si el total es $50,000</li>
                    <li>Pon efectivo: $30,000</li>
                    <li>Pon tarjeta: $15,000</li>
                    <li>Deja los demás en $0</li>
                    <li><strong>Resultado esperado:</strong> Error "Falta: $5,000"</li>
                </ul>
            </li>
            <li><strong>Prueba pago correcto:</strong>
                <ul>
                    <li>Ajusta los valores para que sumen igual o más que el total</li>
                    <li><strong>Resultado esperado:</strong> Confirmación y facturación exitosa</li>
                </ul>
            </li>
            <li><strong>Revisa la consola:</strong> Abre F12 para ver los valores de debug</li>
        </ol>
    </div>

    <div class="card warning">
        <h2>🔍 Qué Buscar en la Consola</h2>
        <p>Cuando hagas clic en "Facturar", deberías ver en la consola del navegador (F12):</p>
        
        <div style="background: #e9ecef; padding: 10px; border-radius: 3px; font-family: monospace; margin: 10px 0;">
🔧 DEBUG VALIDACIÓN PAGOS:<br>
Total: 50000<br>
Efectivo: 30000<br>
Tarjeta: 15000<br>
Nequi: 0<br>
Daviplata: 0<br>
Bancolombia: 0<br>
Total Pagado: 45000<br>
Diferencia: -5000
        </div>
        
        <p><strong>Si ves estos valores correctamente, la validación debería funcionar.</strong></p>
    </div>

    <div class="card error">
        <h2>❌ Posibles Problemas</h2>
        <p>Si la validación aún no funciona, puede ser por:</p>
        
        <ul>
            <li>🔍 <strong>Campo total no encontrado:</strong> El ID 'total' puede no existir</li>
            <li>🔍 <strong>Valores formateados:</strong> Los campos pueden tener formato de moneda</li>
            <li>🔍 <strong>Timing:</strong> La validación se ejecuta antes de cargar valores</li>
            <li>🔍 <strong>JavaScript deshabilitado:</strong> Los alerts pueden estar bloqueados</li>
        </ul>
        
        <h4>🔧 Si no funciona:</h4>
        <ol>
            <li>Abre la consola del navegador (F12)</li>
            <li>Ve a la pestaña "Console"</li>
            <li>Intenta facturar y revisa los valores de debug</li>
            <li>Reporta qué valores aparecen en la consola</li>
        </ol>
    </div>

    <div class="card">
        <h2>🔗 Enlaces de Prueba</h2>
        <p>Usa estos enlaces para probar:</p>
        
        <a href="../../index.php?action=registroPmesa&ida=5" class="btn btn-primary" target="_blank">
            🏠 Mesa 5 (Test Principal)
        </a>
        
        <a href="../../index.php?action=registroPmesa&ida=3" class="btn btn-success" target="_blank">
            🏠 Mesa 3 (Alternativa)
        </a>
        
        <a href="../../index.php?action=mesa" class="btn btn-warning" target="_blank">
            📋 Lista de Mesas
        </a>
        
        <a href="test_debug_validacion_mesa.php" class="btn btn-danger" target="_blank">
            🔧 Debug Simulado
        </a>
    </div>

    <div class="card">
        <h2>📋 Casos de Prueba Específicos</h2>
        
        <h4>Caso 1: Pago Insuficiente</h4>
        <div style="background: #f8d7da; padding: 10px; border-radius: 3px; margin: 5px 0;">
            <strong>Total:</strong> $50,000<br>
            <strong>Efectivo:</strong> $30,000<br>
            <strong>Tarjeta:</strong> $15,000<br>
            <strong>Otros:</strong> $0<br>
            <strong>Resultado esperado:</strong> ❌ Error - Falta $5,000
        </div>
        
        <h4>Caso 2: Pago Exacto</h4>
        <div style="background: #d4edda; padding: 10px; border-radius: 3px; margin: 5px 0;">
            <strong>Total:</strong> $50,000<br>
            <strong>Efectivo:</strong> $30,000<br>
            <strong>Nequi:</strong> $20,000<br>
            <strong>Otros:</strong> $0<br>
            <strong>Resultado esperado:</strong> ✅ Éxito - Cambio $0
        </div>
        
        <h4>Caso 3: Pago con Cambio</h4>
        <div style="background: #d1ecf1; padding: 10px; border-radius: 3px; margin: 5px 0;">
            <strong>Total:</strong> $50,000<br>
            <strong>Efectivo:</strong> $60,000<br>
            <strong>Otros:</strong> $0<br>
            <strong>Resultado esperado:</strong> ✅ Éxito - Cambio $10,000
        </div>
    </div>

    <div class="card success">
        <h2>🎯 Objetivo</h2>
        <p style="font-size: 18px;">
            El objetivo es que la validación funcione igual que en la facturación rápida:
            <strong>No permitir facturar si la suma de las 5 formas de pago es menor al total.</strong>
        </p>
        
        <div style="text-align: center; margin-top: 20px;">
            <a href="../../index.php?action=facturacionRapida" class="btn btn-success">
                🚀 Comparar con Facturación Rápida
            </a>
        </div>
    </div>

</div>

<script>
// Función para ayudar con el debug
function mostrarInstrucciones() {
    alert('📋 Instrucciones de Debug:\n\n' +
          '1. Ve a una mesa (Mesa 5)\n' +
          '2. Agrega productos si no hay\n' +
          '3. Ve a facturación\n' +
          '4. Abre la consola (F12)\n' +
          '5. Pon valores de pago insuficiente\n' +
          '6. Haz clic en Facturar\n' +
          '7. Revisa los valores en la consola\n' +
          '8. Verifica si aparece el error de pago insuficiente');
}

// Mostrar instrucciones al cargar
document.addEventListener('DOMContentLoaded', function() {
    console.log('🧪 Test de validación de mesa cargado');
    console.log('📋 Abre una mesa y prueba la validación de pagos');
});
</script>

</body>
</html>
