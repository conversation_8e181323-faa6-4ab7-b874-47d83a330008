
<?php 
	ob_start();
	class controller<PERSON><PERSON><PERSON> extends MvcController
		{
			
			##TURNO
			#---------------------------------
				#REGISTRO DE TURNO persona_id, fecha_inicio, fecha_final,  cantidad_inicio, cantidad_final
				#------------------------------------
					public function registroPropinaController()
						{	//echo "<script>alert('Entro CONTROl   q no')</script>";
							$turno = Datos::editarPropinaModel($_SESSION['usuario'], "turnos_cajeros");
							session_start();	
							if ($turno['fecha_inicial']== 0 ) 
								{	
									if(isset($_POST["fecha_inicioPropinaRegistro"]) && isset($_POST["cantidad_inicioPropinaRegistro"]))
										{	
											$datosController =array('persona_id'=>$_SESSION['usuario'] ,																
																	'fecha_final'=>$_POST["fecha_finalPropinaRegistro"],															
																	'cantidad_inicio'=>$_POST["cantidad_inicioPropinaRegistro"],
																	'cantidad_final'=>$_POST["cantidad_finalPropinaRegistro"]);
											echo "<script>alert('Entro Controller ".$datosController['cantidad_finalPropinaRegistro']." no');</script>";	
											$respuesta = Datos::registroPropinaModel($datosController, "turnos_cajeros");
											if($respuesta == "success")
												{	header("location:index.php?action=okT");	}
											else
												{	header("location:index.php");	}
										}
								}
						}

				#VISTA DE TURNO
				#------------------------------------
					public function vistaPropinaController()
						{
							$respuesta = Datos::vistaPropinaModel("turnos_cajeros");
							foreach($respuesta as $row => $item)
								{
								echo'<tr>						
										<td>'.$item["persona_id"].'</td>						
										<td>'.$item["fecha_inicio"].'</td>						
										<td>'.$item["fecha_final"].'</td>						
															
										<td>'.$item["cantidad_inicio"].'</td>						
										<td>'.$item["cantidad_final"].'</td>						
										<td><a href="index.php?action=editarPropina&id='.$item["id"].'">Editar</a></td>
										<td><a href="index.php?action=turno&idBorrar='.$item["id"].'">Borrar</a></td>
									</tr>';
								}
						}

				#EDITAR TURNO Ver Cerrar turno
				#------------------------------------
					public function editarPropinaController()
						{	
							session_start();
							$datosController = $_SESSION['usuario'] ;
							$respuesta = Datos::editarPropinaModel($datosController, "turnos_cajeros");
							echo' <input type="hidden" value="'.$respuesta["persona_id"].'" name="persona_idPropinaEditar" required>	

							 fecha inicial <input type="text" readonly value="'.$respuesta["fecha_inicio"].'" name="fecha_inicioPropinaEditar" required> 
							 fecha final <input type="text" value="'.$respuesta["fecha_final"].'" name="fecha_finalPropinaEditar" required> 
							
							Valor Caja inicio <input type="text" readonly value="'.$respuesta["cantidad_inicio"].'" name="cantidad_inicioPropinaEditar" required> 					 
							 Valor Caja final <input type="text" value="'.$respuesta["cantidad_final"].'" name="cantidad_finalPropinaEditar" required> 
							 <input type="hidden" value="'.$respuesta["id"].'" name="idPropinaEditar" required> 				
								 <input type="submit" value="Actualizar">';
						}

				#ACTUALIZAR TURNO  Guardar Cerrar turno
				#------------------------------------
					public function actualizarPropinaController()
						{echo "<script>alert('Entro Controller Actualizar Producto')</script>";

							if(isset($_POST["persona_idPropinaEditar"]))
								{
									$datosController = array(  "persona_id"=>$_POST["persona_idPropinaEditar"],			
																"fecha_inicio"=>$_POST["fecha_inicioPropinaEditar"],				
																"fecha_final"=>$_POST["fecha_finalPropinaEditar"],				
																			
																"cantidad_inicio"=>$_POST["cantidad_inicioPropinaEditar"],				
																"cantidad_final"=>$_POST["cantidad_finalPropinaEditar"],				
																"id"=>$_POST["idPropinaEditar"]);				
									$respuesta = Datos::actualizarPropinaModel($datosController, "turnos_cajeros");
									if($respuesta == "success")
										{	header("location:index.php?action=cambioT");	}
									else
										{	echo "error";	}
								}				
						}

				#BORRAR TURNO
				#------------------------------------
					public function borrarPropinaController()
						{
							if(isset($_GET["idBorrar"]))
								{
									$datosController = $_GET["idBorrar"];				
									$respuesta = Datos::borrarPropinaModel($datosController, "turnos_cajeros");
									if($respuesta == "success")
										{	header("location:index.php?action=turno");	}
								}
						}			
			#----------------------------------------------
		}			
			