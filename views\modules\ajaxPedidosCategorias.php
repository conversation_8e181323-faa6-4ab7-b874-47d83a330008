<?php

// Habilitar reporte de errores para debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

// Verificar que el usuario esté autenticado
if (!isset($_SESSION["validar"]) || !$_SESSION["validar"]) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'No autorizado']);
    exit();
}

// Verificar permisos
$usuario_tipo = $_SESSION["tipo_usuario"];
if ($usuario_tipo != 1 && $usuario_tipo != 4 && $usuario_tipo != 5 && $usuario_tipo != 6) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'Sin permisos']);
    exit();
}

header('Content-Type: application/json');

try {
    require_once __DIR__ . "/../../controllers/controllerPedidosCategorias.php";
} catch (Exception $e) {
    echo json_encode(['success' => false, 'error' => 'Error cargando controlador: ' . $e->getMessage()]);
    exit();
}

try {
    $controller = new ControllerPedidosCategorias();
    
    // Obtener datos de la petición
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data) {
        $data = $_POST; // Fallback para formularios normales
    }
    
    $accion = $data['accion'] ?? $_GET['accion'] ?? '';
    $categoria = $data['categoria'] ?? $_GET['categoria'] ?? '';
    
    switch ($accion) {
        case 'obtener_pendientes':
            if (empty($categoria)) {
                throw new Exception('Categoría requerida');
            }
            
            $pedidos = $controller->mostrarPedidosPendientesController($categoria);
            echo json_encode([
                'success' => true,
                'pedidos' => $pedidos,
                'total' => count($pedidos)
            ]);
            break;
            
        case 'obtener_entregados':
            if (empty($categoria)) {
                throw new Exception('Categoría requerida');
            }
            
            $pedidos = $controller->mostrarPedidosEntregadosDiaController($categoria);
            echo json_encode([
                'success' => true,
                'pedidos' => $pedidos,
                'total' => count($pedidos)
            ]);
            break;
            
        case 'marcar_entregado':
            $pedido_id = $data['pedido_id'] ?? '';
            $categoria = $data['categoria'] ?? '';

            if (empty($pedido_id)) {
                throw new Exception('ID de pedido requerido');
            }

            if (empty($categoria)) {
                throw new Exception('Categoría requerida');
            }

            if (!isset($_SESSION["usuario"])) {
                throw new Exception('Usuario no identificado');
            }

            // Log para debug
            error_log("AJAX: Intentando marcar pedido {$pedido_id} categoría {$categoria} como entregado por usuario {$_SESSION['usuario']}");

            require_once __DIR__ . "/../../models/crudPedidosCategorias.php";
            $resultado = DatosPedidosCategorias::marcarPedidoEntregadoCategoriaModel($pedido_id, $categoria, $_SESSION["usuario"]);

            // Log del resultado
            error_log("AJAX: Resultado de marcarPedidoEntregadoCategoriaModel: " . $resultado);

            if ($resultado == "success") {
                echo json_encode([
                    'success' => true,
                    'message' => "Pedido marcado como entregado en {$categoria}",
                    'pedido_id' => $pedido_id,
                    'categoria' => $categoria,
                    'usuario_id' => $_SESSION["usuario"]
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'error' => 'Error al marcar el pedido como entregado: ' . $resultado,
                    'pedido_id' => $pedido_id,
                    'categoria' => $categoria,
                    'usuario_id' => $_SESSION["usuario"]
                ]);
            }
            break;
            
        case 'obtener_estadisticas':
            if (empty($categoria)) {
                throw new Exception('Categoría requerida');
            }
            
            $estadisticas = $controller->obtenerEstadisticasDiaController($categoria);
            echo json_encode([
                'success' => true,
                'estadisticas' => $estadisticas
            ]);
            break;
            
        case 'obtener_productos_pedido':
            $pedido_id = $data['pedido_id'] ?? '';
            
            if (empty($pedido_id) || empty($categoria)) {
                throw new Exception('ID de pedido y categoría requeridos');
            }
            
            $productos = $controller->obtenerProductosPedidoCategoriaController($pedido_id, $categoria);
            echo json_encode([
                'success' => true,
                'productos' => $productos,
                'total' => count($productos)
            ]);
            break;
            
        case 'verificar_permisos':
            $categoria_solicitada = $data['categoria'] ?? '';
            $permisos = [];
            
            // Verificar permisos por categoría
            switch ($categoria_solicitada) {
                case 'cocina':
                    $permisos['puede_ver'] = ($usuario_tipo == 1 || $usuario_tipo == 4);
                    $permisos['puede_marcar_entregado'] = ($usuario_tipo == 1 || $usuario_tipo == 4);
                    break;
                case 'bar':
                    $permisos['puede_ver'] = ($usuario_tipo == 1 || $usuario_tipo == 5);
                    $permisos['puede_marcar_entregado'] = ($usuario_tipo == 1 || $usuario_tipo == 5);
                    break;
                case 'asados':
                    $permisos['puede_ver'] = ($usuario_tipo == 1 || $usuario_tipo == 6);
                    $permisos['puede_marcar_entregado'] = ($usuario_tipo == 1 || $usuario_tipo == 6);
                    break;
                default:
                    $permisos['puede_ver'] = ($usuario_tipo == 1);
                    $permisos['puede_marcar_entregado'] = ($usuario_tipo == 1);
            }
            
            echo json_encode([
                'success' => true,
                'permisos' => $permisos,
                'usuario_tipo' => $usuario_tipo
            ]);
            break;
            
        case 'ping':
            // Endpoint simple para verificar conectividad
            echo json_encode([
                'success' => true,
                'timestamp' => date('Y-m-d H:i:s'),
                'usuario' => $_SESSION["persona"] ?? 'Desconocido'
            ]);
            break;
            
        default:
            throw new Exception('Acción no válida: ' . $accion);
    }
    
} catch (Exception $e) {
    error_log("Error en ajaxPedidosCategorias: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}

?>
