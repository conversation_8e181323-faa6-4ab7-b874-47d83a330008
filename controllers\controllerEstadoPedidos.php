<?php

class ControllerEstadoPedidos {
    
    /*=============================================
    CREAR NUEVO PEDIDO
    =============================================*/
    public function crearPedidoController($mesaId, $meseroId, $cedulaCliente) {
        
        $datos = array(
            "mesa_id" => $mesaId,
            "mesero_id" => $meseroId,
            "cedula_cliente" => $cedulaCliente
        );
        
        $respuesta = DatosEstadoPedidos::crearPedidoModel($datos);
        return $respuesta;
    }
    
    /*=============================================
    OBTENER O CREAR PEDIDO BORRADOR
    =============================================*/
    public function obtenerPedidoBorradorController($mesaId) {

        try {
            // Verificar si existe un pedido en borrador para esta mesa
            $pedidoBorrador = DatosEstadoPedidos::obtenerPedidoBorradorMesaModel($mesaId);

            if ($pedidoBorrador) {
                return $pedidoBorrador;
            }

            // Si no existe, crear uno nuevo
            $meseroId = isset($_SESSION["usuario"]) ? $_SESSION["usuario"] : 1;
            $cedulaCliente = isset($_SESSION["clientep"]) ? $_SESSION["clientep"] : '0';

            $nuevoPedidoId = $this->crearPedidoController($mesaId, $meseroId, $cedulaCliente);

            if ($nuevoPedidoId != "error" && is_numeric($nuevoPedidoId)) {
                // Obtener el pedido recién creado
                $pedidoCreado = DatosEstadoPedidos::obtenerPedidoBorradorMesaModel($mesaId);
                if ($pedidoCreado) {
                    return $pedidoCreado;
                }
            }

            throw new Exception("No se pudo crear el pedido borrador para la mesa " . $mesaId);

        } catch (Exception $e) {
            error_log("Error en obtenerPedidoBorradorController: " . $e->getMessage());
            return false;
        }
    }
    
    /*=============================================
    ENVIAR PEDIDO A COCINA
    =============================================*/
    public function enviarPedidoController($pedidoId) {
        
        $usuarioId = isset($_SESSION["usuario"]) ? $_SESSION["usuario"] : 1;
        
        // Verificar que el pedido tenga productos
        $productos = DatosEstadoPedidos::obtenerProductosPedidoModel($pedidoId);
        
        if (empty($productos)) {
            return array("status" => "error", "message" => "El pedido no tiene productos");
        }
        
        // Cambiar estado a enviado
        $respuesta = DatosEstadoPedidos::enviarPedidoModel($pedidoId, $usuarioId);
        
        if ($respuesta == "success") {

            $resultadoImpresion = false;

            try {
                // Intentar cargar e imprimir en las impresoras correspondientes
                $controllerImpresionPath = __DIR__ . "/controllerImpresionPedidos.php";
                $crudImpresionPath = __DIR__ . "/../models/crudImpresionPedidos.php";

                if (file_exists($controllerImpresionPath) && file_exists($crudImpresionPath)) {
                    if (!class_exists('ControllerImpresionPedidos')) {
                        require_once $controllerImpresionPath;
                    }
                    if (!class_exists('DatosImpresionPedidos')) {
                        require_once $crudImpresionPath;
                    }

                    $controllerImpresion = new ControllerImpresionPedidos();
                    $resultadoImpresion = $controllerImpresion->imprimirPedidoPorCategoriaController($pedidoId);
                } else {
                    error_log("Archivos de impresión no encontrados - continuando sin imprimir");
                }

            } catch (Exception $e) {
                // Si falla la impresión, log del error pero continuar
                error_log("Error en impresión del pedido $pedidoId: " . $e->getMessage());
            }

            // RESTAURADO: Crear automáticamente un nuevo pedido borrador para la mesa
            try {
                $pedidoEnviado = DatosEstadoPedidos::obtenerPedidoPorIdModel($pedidoId);
                if ($pedidoEnviado) {
                    $mesaId = $pedidoEnviado['mesa_id'];
                    $meseroId = isset($_SESSION["usuario"]) ? $_SESSION["usuario"] : 1;
                    $cedulaCliente = isset($_SESSION["clientep"]) ? $_SESSION["clientep"] : '0';

                    $nuevoPedidoId = $this->crearPedidoController($mesaId, $meseroId, $cedulaCliente);
                    if ($nuevoPedidoId != "error" && is_numeric($nuevoPedidoId)) {
                        error_log("✅ Nuevo pedido borrador creado automáticamente: ID $nuevoPedidoId para mesa $mesaId");
                    }
                }
            } catch (Exception $e) {
                error_log("❌ Error al crear nuevo pedido borrador automático: " . $e->getMessage());
            }

            return array(
                "status" => "success",
                "message" => "Pedido enviado correctamente",
                "impresion" => $resultadoImpresion
            );
        }
        
        return array("status" => "error", "message" => "Error al enviar el pedido");
    }
    
    /*=============================================
    MARCAR PEDIDO COMO ENTREGADO
    =============================================*/
    public function entregarPedidoController($pedidoId) {
        
        $usuarioId = isset($_SESSION["usuario"]) ? $_SESSION["usuario"] : 1;
        
        $respuesta = DatosEstadoPedidos::entregarPedidoModel($pedidoId, $usuarioId);
        
        if ($respuesta == "success") {
            
            // Descontar del inventario
            $this->descontarInventarioController($pedidoId);
            
            return array("status" => "success", "message" => "Pedido marcado como entregado");
        }
        
        return array("status" => "error", "message" => "Error al marcar pedido como entregado");
    }
    
    /*=============================================
    DESCONTAR INVENTARIO
    =============================================*/
    private function descontarInventarioController($pedidoId) {

        // Obtener productos del pedido
        $productos = DatosEstadoPedidos::obtenerProductosPedidoModel($pedidoId);

        $db = Conexion::conectar();

        foreach ($productos as $producto) {

            try {
                // Buscar el producto por nombre para obtener su ID
                $stmtProducto = $db->prepare("SELECT id FROM productos WHERE nombre = ?");
                $stmtProducto->execute([$producto['nombre']]);
                $productoData = $stmtProducto->fetch();

                if ($productoData) {
                    $productoId = $productoData['id'];

                    // Obtener suministros asociados al producto
                    $stmtSuministros = $db->prepare("
                        SELECT sp.suministro_id, sp.cantidades
                        FROM suministros_productos sp
                        WHERE sp.producto_id = ?
                    ");
                    $stmtSuministros->execute([$productoId]);
                    $suministros = $stmtSuministros->fetchAll();

                    // Descontar cada suministro
                    foreach ($suministros as $suministro) {
                        $cantidadADescontar = $suministro['cantidades'] * $producto['cantidad'];

                        $stmtDescuento = $db->prepare("
                            UPDATE sucursal
                            SET cantidad = GREATEST(0, cantidad - ?)
                            WHERE suministro_id = ? AND punto_id = 1
                        ");
                        $stmtDescuento->execute([$cantidadADescontar, $suministro['suministro_id']]);

                        // Log del descuento (opcional)
                        error_log("Descontado del inventario: Suministro ID {$suministro['suministro_id']}, Cantidad: {$cantidadADescontar}");
                    }
                }

            } catch (Exception $e) {
                // Log del error pero continuar con otros productos
                error_log("Error al descontar inventario para producto {$producto['nombre']}: " . $e->getMessage());
            }
        }
    }
    
    /*=============================================
    OBTENER PEDIDOS DE UNA MESA
    =============================================*/
    public function obtenerPedidosMesaController($mesaId, $estado = null) {
        
        $respuesta = DatosEstadoPedidos::obtenerPedidosMesaEstadoModel($mesaId, $estado);
        return $respuesta;
    }
    
    /*=============================================
    VERIFICAR PERMISOS DE MODIFICACION
    =============================================*/
    public function puedeModificarPedidoController($pedidoId) {
        
        $usuarioId = isset($_SESSION["usuario"]) ? $_SESSION["usuario"] : 0;
        $rolUsuario = isset($_SESSION["perfil"]) ? $_SESSION["perfil"] : 'mesero';
        
        return DatosEstadoPedidos::puedeModificarPedidoModel($pedidoId, $usuarioId, $rolUsuario);
    }
    
    /*=============================================
    REIMPRIMIR PEDIDO
    =============================================*/
    public function reimprimirPedidoController($pedidoId, $categoria = null, $motivo = "Reimpresión solicitada") {
        
        $usuarioId = isset($_SESSION["usuario"]) ? $_SESSION["usuario"] : 1;
        
        // Registrar la reimpresión
        if ($categoria) {
            $datosReimpresion = array(
                "pedido_id" => $pedidoId,
                "categoria" => $categoria,
                "usuario_id" => $usuarioId,
                "motivo" => $motivo
            );
            
            DatosEstadoPedidos::registrarReimpresionModel($datosReimpresion);
        }
        
        // Imprimir
        try {
            $controllerImpresionPath = __DIR__ . "/controllerImpresionPedidos.php";
            $crudImpresionPath = __DIR__ . "/../models/crudImpresionPedidos.php";

            if (!file_exists($controllerImpresionPath) || !file_exists($crudImpresionPath)) {
                return array("status" => "error", "message" => "Sistema de impresión no disponible");
            }

            if (!class_exists('ControllerImpresionPedidos')) {
                require_once $controllerImpresionPath;
            }
            if (!class_exists('DatosImpresionPedidos')) {
                require_once $crudImpresionPath;
            }

            $controllerImpresion = new ControllerImpresionPedidos();
        } catch (Exception $e) {
            return array("status" => "error", "message" => "Error cargando sistema de impresión: " . $e->getMessage());
        }
        
        if ($categoria) {
            // Reimprimir solo una categoría específica
            $resultado = $controllerImpresion->reimprimirCategoriaPedidoController($pedidoId, $categoria);
        } else {
            // Reimprimir todo el pedido
            $resultado = $controllerImpresion->imprimirPedidoPorCategoriaController($pedidoId);
        }
        
        if ($resultado) {
            return array("status" => "success", "message" => "Pedido reimpreso correctamente");
        }
        
        return array("status" => "error", "message" => "Error al reimprimir el pedido");
    }
    
    /*=============================================
    OBTENER PRODUCTOS DEL PEDIDO ACTUAL
    =============================================*/
    public function obtenerProductosPedidoController($pedidoId) {
        
        $respuesta = DatosEstadoPedidos::obtenerProductosPedidoModel($pedidoId);
        return $respuesta;
    }
    
    /*=============================================
    OBTENER HISTORIAL DEL PEDIDO
    =============================================*/
    public function obtenerHistorialPedidoController($pedidoId) {
        
        $respuesta = DatosEstadoPedidos::obtenerHistorialPedidoModel($pedidoId);
        return $respuesta;
    }
    
    /*=============================================
    OBTENER PEDIDOS PENDIENTES POR CATEGORIA
    =============================================*/
    public function obtenerPedidosPendientesCategoriaController($categoria) {
        
        $respuesta = DatosEstadoPedidos::obtenerPedidosPendientesCategoriaModel($categoria);
        return $respuesta;
    }
    
    /*=============================================
    VALIDAR ESTADO DEL PEDIDO
    =============================================*/
    public function validarEstadoPedidoController($pedidoId, $estadoEsperado) {
        
        $stmt = Conexion::conectar()->prepare("SELECT estado FROM pedidos WHERE id = ?");
        $stmt->bindParam(1, $pedidoId, PDO::PARAM_INT);
        $stmt->execute();
        $resultado = $stmt->fetch();
        $stmt->close();
        $stmt = null;
        
        if ($resultado && $resultado['estado'] == $estadoEsperado) {
            return true;
        }
        
        return false;
    }
    
    /*=============================================
    CANCELAR PEDIDO (SOLO ADMINISTRADORES)
    =============================================*/
    public function cancelarPedidoController($pedidoId, $motivo = "") {
        
        $rolUsuario = isset($_SESSION["perfil"]) ? $_SESSION["perfil"] : 'mesero';
        
        if ($rolUsuario != 'administrador') {
            return array("status" => "error", "message" => "No tiene permisos para cancelar pedidos");
        }
        
        $usuarioId = isset($_SESSION["usuario"]) ? $_SESSION["usuario"] : 1;
        
        $stmt = Conexion::conectar()->prepare("
            UPDATE pedidos 
            SET estado = 'cancelado', usuario_entrega = ?
            WHERE id = ? AND estado IN ('borrador', 'enviado')
        ");
        $stmt->bindParam(1, $usuarioId, PDO::PARAM_INT);
        $stmt->bindParam(2, $pedidoId, PDO::PARAM_INT);
        
        if($stmt->execute()) {
            return array("status" => "success", "message" => "Pedido cancelado correctamente");
        }
        
        return array("status" => "error", "message" => "Error al cancelar el pedido");
    }
}
