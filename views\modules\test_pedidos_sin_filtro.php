<?php

session_start();

if(!$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "models/conexion.php";

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Test - Todos los Pedidos Pendientes</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
    <style>
        .panel-danger .panel-heading {
            background-color: #d9534f !important;
            border-color: #d43f3a !important;
            color: white !important;
        }
        .panel-warning .panel-heading {
            background-color: #f0ad4e !important;
            border-color: #eea236 !important;
            color: white !important;
        }
        .panel-info .panel-heading {
            background-color: #5bc0de !important;
            border-color: #46b8da !important;
            color: white !important;
        }
        .refresh-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body>

<div class="refresh-btn">
    <button onclick="location.reload()" class="btn btn-info btn-sm">
        <i class="glyphicon glyphicon-refresh"></i> Actualizar
    </button>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <h2>🔍 Test - Todos los Pedidos Pendientes (Sin Filtro)</h2>
            <hr>
        </div>
    </div>

<?php

// Obtener TODOS los pedidos enviados sin filtro de categoría
$stmt = Conexion::conectar()->prepare("
    SELECT DISTINCT
        p.id as pedido_id,
        p.numero_pedido,
        p.fecha_envio,
        p.mesa_id,
        m.numero as mesa_numero,
        COUNT(pvm.id) as total_productos,
        GROUP_CONCAT(
            CONCAT(pr.nombre, ' x', pvm.cantidad, ' (', pr.categoria, ')') 
            ORDER BY pr.nombre 
            SEPARATOR ', '
        ) as productos_detalle,
        SUM(pvm.cantidad * pr.precio) as total_precio
    FROM pedidos p
    INNER JOIN mesas m ON p.mesa_id = m.id
    INNER JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
    INNER JOIN productos pr ON pvm.productos_id = pr.id
    WHERE p.estado = 'enviado'
    GROUP BY p.id, p.numero_pedido, p.fecha_envio, p.mesa_id, m.numero
    ORDER BY p.fecha_envio ASC
");
$stmt->execute();
$todos_pedidos = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<div class='row'>";
echo "<div class='col-md-12'>";
echo "<div class='alert alert-info'>";
echo "<h4>📊 Estadísticas</h4>";
echo "<p><strong>Total pedidos enviados:</strong> " . count($todos_pedidos) . "</p>";
echo "</div>";
echo "</div>";
echo "</div>";

if (empty($todos_pedidos)) {
    echo "<div class='alert alert-warning text-center'>";
    echo "<h3>⚠️ No hay pedidos pendientes</h3>";
    echo "<p>No se encontraron pedidos con estado 'enviado'.</p>";
    echo "</div>";
} else {
    echo "<div class='panel panel-default'>";
    echo "<div class='panel-heading'>";
    echo "<h3 class='panel-title'>📋 Todos los Pedidos Pendientes - " . count($todos_pedidos) . " pedido(s)</h3>";
    echo "</div>";
    echo "<div class='panel-body'>";
    
    foreach ($todos_pedidos as $pedido) {
        // Calcular tiempo de espera
        $ahora = new DateTime();
        $envio = new DateTime($pedido['fecha_envio']);
        $diferencia = $ahora->diff($envio);
        
        if ($diferencia->h > 0) {
            $tiempo_espera = $diferencia->h . "h " . $diferencia->i . "m";
        } else {
            $tiempo_espera = $diferencia->i . " min";
        }
        
        // Determinar clase de urgencia
        $minutos = $diferencia->h * 60 + $diferencia->i;
        if ($minutos > 30) {
            $clase_urgencia = 'danger';  // Rojo - Muy urgente
        } elseif ($minutos > 15) {
            $clase_urgencia = 'warning'; // Amarillo - Urgente
        } else {
            $clase_urgencia = 'info';    // Azul - Normal
        }
        
        echo "<div class='panel panel-{$clase_urgencia}' style='margin-bottom: 15px;'>";
        echo "<div class='panel-heading'>";
        echo "<div class='row'>";
        echo "<div class='col-md-6'>";
        echo "<h4 style='margin: 0;'>";
        echo "<strong>{$pedido['numero_pedido']}</strong> - Mesa {$pedido['mesa_numero']}";
        echo "</h4>";
        echo "</div>";
        echo "<div class='col-md-3 text-center'>";
        echo "<span class='label label-{$clase_urgencia}' style='font-size: 12px;'>";
        echo "⏱️ {$tiempo_espera}";
        echo "</span>";
        echo "</div>";
        echo "<div class='col-md-3 text-right'>";
        echo "<form method='post' style='display: inline;'>";
        echo "<input type='hidden' name='pedido_id' value='{$pedido['pedido_id']}'>";
        echo "<button type='submit' class='btn btn-success btn-sm'>";
        echo "<i class='glyphicon glyphicon-ok'></i> Marcar Entregado";
        echo "</button>";
        echo "</form>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        
        echo "<div class='panel-body'>";
        echo "<div class='row'>";
        echo "<div class='col-md-8'>";
        echo "<h5><strong>Productos:</strong></h5>";
        echo "<p style='font-size: 14px;'>{$pedido['productos_detalle']}</p>";
        echo "</div>";
        echo "<div class='col-md-4 text-right'>";
        echo "<h5><strong>Total:</strong> $" . number_format($pedido['total_precio'], 0) . "</h5>";
        echo "<small>Enviado: " . date('H:i', strtotime($pedido['fecha_envio'])) . "</small>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    
    echo "</div>";
    echo "</div>";
}

// Procesar marcado como entregado
if (isset($_POST["pedido_id"]) && isset($_SESSION["id"])) {
    $pedido_id = $_POST["pedido_id"];
    $usuario_id = $_SESSION["id"];
    
    $stmt = Conexion::conectar()->prepare("
        UPDATE pedidos 
        SET estado = 'entregado', 
            fecha_entrega = NOW(), 
            usuario_entrega = :usuario_id 
        WHERE id = :pedido_id 
        AND estado = 'enviado'
    ");
    
    $stmt->bindParam(":pedido_id", $pedido_id, PDO::PARAM_INT);
    $stmt->bindParam(":usuario_id", $usuario_id, PDO::PARAM_INT);
    
    if ($stmt->execute()) {
        echo '<script>
            alert("Pedido marcado como entregado exitosamente");
            if(window.history.replaceState) {
                window.history.replaceState(null, null, window.location.href);
            }
            window.location = "' . $_SERVER['REQUEST_URI'] . '";
        </script>';
    } else {
        echo '<script>
            alert("Error al marcar el pedido como entregado");
        </script>';
    }
}

?>

    <div class="row">
        <div class="col-md-12">
            <hr>
            <p><a href="pedidosCocinaPendientes" class="btn btn-primary">← Volver a Pedidos Cocina</a></p>
        </div>
    </div>
</div>

</body>
</html>
