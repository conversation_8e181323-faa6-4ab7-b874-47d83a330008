<?php
 if(!$_SESSION["validar"])
	{
		header("location:index.php?action=ingresar");
		exit();
	}
if(isset($_GET["action"]))
	{	
		if($_GET["action"] == "okFs")
			{	echo "Registro Exitoso";	}						
	}

		/*ini_set('display_errors', 1);
		ini_set('display_startup_errors', 1);
		error_reporting(E_ALL);*/

?>

<script type="text/javascript">	
	// ------FACTURAR--------------
	 function guardarSuministro() 		
		{	var compra2 = document.calculo.compra.value;
			if (confirm('confirme los detalles de la Facturar?'+compra2)) 
				{		//setTimeout('location.href="views/modules/ajaxFactura.php"',500);	
							var compra = document.calculo.compra.value;
							//alert("enviando factura # "+compra); 
							$("#destino").load("views/modules/ajaxSuministro.php", {compra: compra}, function(){
    						 //alert("recibidos los datos por ajax compra"); 		 
 						});	
				}				
		} 
	// ------FACTURAR FIN//			
</script>

<!--$_session['idcompra']  	$_session['proveedor']	$_session['valorT']	$_session['facturaN']     registroCsuministroController  -->
<div class="row">
	      <div class="col-md-12">
	      	<form name="calculo" method="post" >
	      		
				<input type="hidden" name="compra" id="compra" value="<?=$_SESSION["ver"];?>" required>		
			  				
						<?php
							$vistaFsuministro = new controllerFacturaCompraSuministro();
							$vistaFsuministro -> vistaFacturaSumistroController();
							$vistaFsuministro -> borrarFacturaSumistroController();			
							//$vistaFsuministro -> actualizarFacturaSumistroController();			
						?>						
								
				<span id="destino" > </span>		
				<!--<a class="btn btn-primary btn-lg"  role="button" onclick="guardarSuministro();" name="guardar" value="Guardar">Guardar &raquo;</a>	
				<input type="submit"  value="Facturar">-->
			 </form>				
	      </div>
	      
