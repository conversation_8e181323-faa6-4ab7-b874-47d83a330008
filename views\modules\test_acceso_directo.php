<!DOCTYPE html>
<html>
<head>
    <title>🚀 Acceso Directo - Facturación Rápida</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 800px; margin: 0 auto; }
        .card { background: #f8f9fa; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #007bff; }
        .success { border-left-color: #28a745; background: #d4edda; }
        .warning { border-left-color: #ffc107; background: #fff3cd; }
        .btn { padding: 15px 30px; margin: 10px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold; font-size: 18px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        h1 { color: #007bff; text-align: center; }
        h2 { color: #495057; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
    </style>
</head>
<body>

<div class="container">
    <h1>🚀 Facturación Rápida - Acceso Directo</h1>
    
    <div class="card success">
        <h2>✅ Solución Implementada</h2>
        <p>He solucionado el problema de las URLs amigables implementando acceso directo a los archivos.</p>
        <p>Ahora el sistema funciona de manera más confiable sin depender de las reglas de reescritura del servidor.</p>
    </div>

    <div class="card">
        <h2>🔗 Nuevas Rutas de Acceso</h2>
        <p>El sistema ahora usa rutas directas que son más confiables:</p>
        
        <h3>Desde el Menú Principal:</h3>
        <p>El botón "🚀 FACTURACIÓN RÁPIDA" en la navegación ahora apunta directamente al archivo.</p>
        
        <h3>Desde la Página de Mesas:</h3>
        <p>El botón en la página de mesas también usa la ruta directa.</p>
        
        <h3>Acceso Directo:</h3>
        <a href="facturacionRapida.php" class="btn btn-success">
            🚀 ABRIR FACTURACIÓN RÁPIDA
        </a>
    </div>

    <div class="card">
        <h2>🧪 Tests de Verificación</h2>
        <p>Usa estos enlaces para verificar que todo funcione:</p>
        
        <a href="facturacionRapida.php" class="btn btn-primary" target="_blank">
            📄 Archivo Directo
        </a>
        
        <a href="../../index.php?action=mesa" class="btn btn-warning" target="_blank">
            🏠 Ir a Mesas (Ver Menú)
        </a>
        
        <a href="debug_ruta_facturacion.php" class="btn btn-warning" target="_blank">
            🔧 Debug de Rutas
        </a>
    </div>

    <div class="card warning">
        <h2>⚠️ Importante</h2>
        <ul>
            <li><strong>Sesión Requerida:</strong> Debes estar logueado para acceder</li>
            <li><strong>Ruta Actualizada:</strong> Los menús ahora usan rutas directas</li>
            <li><strong>Más Confiable:</strong> No depende de configuraciones del servidor</li>
        </ul>
    </div>

    <div class="card">
        <h2>📋 Cómo Acceder Ahora</h2>
        <ol>
            <li><strong>Inicia Sesión:</strong> Ve a la página de login del sistema</li>
            <li><strong>Ve a Mesas:</strong> Accede a la página principal de mesas</li>
            <li><strong>Busca el Botón:</strong> Encontrarás "🚀 FACTURACIÓN RÁPIDA" en:
                <ul>
                    <li>La barra de navegación superior</li>
                    <li>La página de mesas (botón verde)</li>
                </ul>
            </li>
            <li><strong>Haz Clic:</strong> El botón te llevará directamente al sistema</li>
        </ol>
    </div>

    <div class="card success">
        <h2>🎉 Problema Solucionado</h2>
        <p>La página en blanco era causada por problemas con las URLs amigables del servidor.</p>
        <p>Ahora el sistema usa rutas directas que funcionan de manera más confiable.</p>
        
        <div style="text-align: center; margin-top: 20px;">
            <a href="../../index.php?action=ingresar" class="btn btn-success">
                🔐 Iniciar Sesión y Probar
            </a>
        </div>
    </div>

    <?php
    // Verificar estado de archivos
    echo "<div class='card'>";
    echo "<h2>📊 Estado de Archivos</h2>";
    
    $archivos = [
        'facturacionRapida.php' => 'Interfaz principal',
        'ajaxBuscarProductoRapido.php' => 'Búsqueda AJAX',
        'ajaxFacturacionRapida.php' => 'Procesamiento AJAX'
    ];
    
    foreach ($archivos as $archivo => $descripcion) {
        if (file_exists($archivo)) {
            echo "<p style='color: green;'>✅ <strong>$archivo</strong> - $descripcion</p>";
        } else {
            echo "<p style='color: red;'>❌ <strong>$archivo</strong> - $descripcion (FALTA)</p>";
        }
    }
    echo "</div>";
    ?>

</div>

</body>
</html>
