<?php
// Inicializar Sistema de Impresión Híbrida
echo "<h1>🚀 Inicializar Sistema de Impresión Híbrida</h1>";
echo "<p><strong>Configuración inicial del sistema híbrido</strong></p>";

$errores = [];
$exitos = [];

try {
    require_once '../../models/conexion.php';
    $conexion = new Conexion();
    $pdo = $conexion->conectar();
    
    echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0;'>";
    echo "<h4>✅ Conexión a base de datos exitosa</h4>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
    echo "<h4>❌ Error de conexión a base de datos</h4>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
    exit;
}

// Ejecutar inicialización si se solicita
if (isset($_POST['inicializar'])) {
    
    echo "<h2>🔧 Ejecutando Inicialización...</h2>";
    
    // 1. Crear tabla de configuración
    try {
        $sql_config = "CREATE TABLE IF NOT EXISTS config_impresion (
            id INT AUTO_INCREMENT PRIMARY KEY,
            clave VARCHAR(100) NOT NULL UNIQUE,
            valor TEXT NOT NULL,
            descripcion TEXT,
            fecha_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        $pdo->exec($sql_config);
        $exitos[] = "Tabla 'config_impresion' creada/verificada";
        
    } catch (Exception $e) {
        $errores[] = "Error creando tabla config_impresion: " . $e->getMessage();
    }
    
    // 2. Insertar configuraciones por defecto
    try {
        $configs_default = [
            ['modo_impresion', 'hibrido', 'Modo de impresión: directo, proxy, hibrido'],
            ['ip_proxy', '***********', 'IP del proxy (tu portátil)'],
            ['puerto_proxy', '3000', 'Puerto del proxy'],
            ['timeout_conexion', '5', 'Timeout en segundos para conexiones'],
            ['reintentos_max', '3', 'Número máximo de reintentos'],
            ['red_local', '192.168.68', 'Prefijo de red local'],
            ['impresion_directa_habilitada', '1', 'Permitir impresión directa desde red local']
        ];
        
        $stmt = $pdo->prepare("INSERT IGNORE INTO config_impresion (clave, valor, descripcion) VALUES (?, ?, ?)");
        
        foreach ($configs_default as $config) {
            $stmt->execute($config);
        }
        
        $exitos[] = "Configuraciones por defecto insertadas";
        
    } catch (Exception $e) {
        $errores[] = "Error insertando configuraciones: " . $e->getMessage();
    }
    
    // 3. Crear tabla de mapeo categoría-impresora
    try {
        $sql_categoria = "CREATE TABLE IF NOT EXISTS categoria_impresora (
            id INT AUTO_INCREMENT PRIMARY KEY,
            categoria VARCHAR(100) NOT NULL,
            impresora VARCHAR(50) NOT NULL,
            activo BOOLEAN DEFAULT TRUE,
            fecha_creacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_categoria (categoria)
        )";
        
        $pdo->exec($sql_categoria);
        $exitos[] = "Tabla 'categoria_impresora' creada/verificada";
        
    } catch (Exception $e) {
        $errores[] = "Error creando tabla categoria_impresora: " . $e->getMessage();
    }
    
    // 4. Crear tabla de logs
    try {
        $sql_logs = "CREATE TABLE IF NOT EXISTS logs_impresion (
            id INT AUTO_INCREMENT PRIMARY KEY,
            impresora VARCHAR(50) NOT NULL,
            metodo VARCHAR(20) NOT NULL,
            success BOOLEAN NOT NULL,
            tiempo_ms DECIMAL(10,2),
            error TEXT,
            ip_cliente VARCHAR(45),
            user_agent TEXT,
            fecha_hora TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_fecha (fecha_hora),
            INDEX idx_impresora (impresora),
            INDEX idx_success (success)
        )";
        
        $pdo->exec($sql_logs);
        $exitos[] = "Tabla 'logs_impresion' creada/verificada";
        
    } catch (Exception $e) {
        $errores[] = "Error creando tabla logs_impresion: " . $e->getMessage();
    }
    
    // 5. Verificar tabla de impresoras
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM impresoras");
        $total_impresoras = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        if ($total_impresoras > 0) {
            $exitos[] = "Tabla 'impresoras' verificada ($total_impresoras impresoras encontradas)";
        } else {
            $errores[] = "Tabla 'impresoras' está vacía - necesitas configurar impresoras primero";
        }
        
    } catch (Exception $e) {
        $errores[] = "Error verificando tabla impresoras: " . $e->getMessage();
    }
    
    // 6. Configurar mapeo automático por defecto
    try {
        // Obtener categorías de productos
        $stmt = $pdo->query("SELECT DISTINCT categoria FROM productos WHERE categoria IS NOT NULL AND categoria != '' ORDER BY categoria");
        $categorias = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        // Obtener impresoras
        $stmt = $pdo->query("SELECT nombre FROM impresoras WHERE activa = 1 ORDER BY nombre");
        $impresoras = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if ($categorias && $impresoras) {
            // Mapeo automático básico
            $mapeo_automatico = [
                'Bebidas' => 'BAR',
                'Cervezas' => 'BAR',
                'Licores' => 'BAR',
                'Vinos' => 'BAR',
                'Comidas' => 'COCINA',
                'Platos' => 'COCINA',
                'Entradas' => 'COCINA',
                'Sopas' => 'COCINA',
                'Carnes' => 'ASADOS',
                'Parrilla' => 'ASADOS',
                'Asados' => 'ASADOS'
            ];
            
            $stmt = $pdo->prepare("INSERT IGNORE INTO categoria_impresora (categoria, impresora) VALUES (?, ?)");
            $mapeos_creados = 0;
            
            foreach ($categorias as $categoria) {
                $impresora_asignada = null;
                
                // Buscar mapeo automático
                foreach ($mapeo_automatico as $patron => $impresora) {
                    if (stripos($categoria, $patron) !== false && in_array($impresora, $impresoras)) {
                        $impresora_asignada = $impresora;
                        break;
                    }
                }
                
                // Si no hay mapeo automático, usar la primera impresora disponible
                if (!$impresora_asignada && !empty($impresoras)) {
                    $impresora_asignada = $impresoras[0];
                }
                
                if ($impresora_asignada) {
                    $stmt->execute([$categoria, $impresora_asignada]);
                    $mapeos_creados++;
                }
            }
            
            if ($mapeos_creados > 0) {
                $exitos[] = "Mapeo automático creado para $mapeos_creados categorías";
            }
        }
        
    } catch (Exception $e) {
        $errores[] = "Error configurando mapeo automático: " . $e->getMessage();
    }
    
    // Mostrar resultados
    if (!empty($exitos)) {
        echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0;'>";
        echo "<h4>✅ Inicialización Exitosa</h4>";
        echo "<ul>";
        foreach ($exitos as $exito) {
            echo "<li>$exito</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    if (!empty($errores)) {
        echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
        echo "<h4>❌ Errores Encontrados</h4>";
        echo "<ul>";
        foreach ($errores as $error) {
            echo "<li>$error</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    if (empty($errores)) {
        echo "<div style='background-color: #d1ecf1; padding: 15px; border-left: 4px solid #17a2b8; margin: 15px 0;'>";
        echo "<h4>🎉 Sistema Híbrido Inicializado Correctamente</h4>";
        echo "<p>Ahora puedes usar:</p>";
        echo "<ul>";
        echo "<li><a href='impresion_hibrida.php'>🔄 Configurar Sistema Híbrido</a></li>";
        echo "<li><a href='test_impresion_hibrida.php'>🧪 Probar Sistema Híbrido</a></li>";
        echo "</ul>";
        echo "</div>";
    }
}

// Verificar estado actual
echo "<h2>📋 Estado Actual del Sistema:</h2>";

$tablas_requeridas = [
    'config_impresion' => 'Configuración del sistema híbrido',
    'categoria_impresora' => 'Mapeo categoría → impresora',
    'logs_impresion' => 'Logs de impresión',
    'impresoras' => 'Configuración de impresoras',
    'productos' => 'Productos con categorías'
];

echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<table style='width: 100%; border-collapse: collapse;'>";
echo "<tr style='background-color: #e9ecef;'>";
echo "<th style='border: 1px solid #ddd; padding: 8px; text-align: left;'>Tabla</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px; text-align: left;'>Descripción</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px; text-align: center;'>Estado</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px; text-align: center;'>Registros</th>";
echo "</tr>";

foreach ($tablas_requeridas as $tabla => $descripcion) {
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM $tabla");
        $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        $estado = "✅ Existe";
        $color = "#d4edda";
    } catch (Exception $e) {
        $total = "N/A";
        $estado = "❌ No existe";
        $color = "#f8d7da";
    }
    
    echo "<tr style='background-color: $color;'>";
    echo "<td style='border: 1px solid #ddd; padding: 8px; font-weight: bold;'>$tabla</td>";
    echo "<td style='border: 1px solid #ddd; padding: 8px;'>$descripcion</td>";
    echo "<td style='border: 1px solid #ddd; padding: 8px; text-align: center;'>$estado</td>";
    echo "<td style='border: 1px solid #ddd; padding: 8px; text-align: center;'>$total</td>";
    echo "</tr>";
}

echo "</table>";
echo "</div>";

// Formulario de inicialización
echo "<h2>🚀 Inicializar Sistema:</h2>";
echo "<div style='background-color: #fff3cd; padding: 20px; border-left: 4px solid #ffc107; margin: 20px 0;'>";
echo "<h4>⚠️ Importante:</h4>";
echo "<p>Este proceso creará las tablas necesarias y configurará el sistema híbrido con valores por defecto.</p>";
echo "<p><strong>Es seguro ejecutarlo múltiples veces</strong> - no sobrescribirá datos existentes.</p>";
echo "</div>";

echo "<form method='POST' style='margin: 20px 0;'>";
echo "<button type='submit' name='inicializar' style='background-color: #007bff; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold; font-size: 16px;'>🚀 Inicializar Sistema Híbrido</button>";
echo "</form>";

// Botones de navegación
echo "<p style='margin-top: 30px;'>";
echo "<a href='diagnostico' style='background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>← Diagnóstico</a>";
echo "<a href='impresion_hibrida.php' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔄 Configurar Sistema</a>";
echo "</p>";
?>
