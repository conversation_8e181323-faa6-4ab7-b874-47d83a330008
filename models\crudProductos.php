<?php
require_once "conexion.php";
class DatosProductos extends Conexion
{
	#REGISTRO DE PRODUCTOS
	#-------------------------------------
	 public static function registroProductoModel($datosModel, $tabla)
		{	//echo "<script>alert('CRUD  codigo :".$datosModel["codigo"]." nombre :".$datosModel["nombre"]." precio :".$datosModel["precio"]." ')</script>";
			$consulta="INSERT INTO $tabla (codigo, nombre, precio, cocina) VALUES (:codigo, :nom, :precio, :cocina)";
			//echo "<script>alert('Entro CRUD ".$consulta." no')</script>";
			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->bindParam(":codigo", $datosModel['codigo'], PDO::PARAM_STR);
			$stmt->bindParam(":nom", $datosModel['nombre'], PDO::PARAM_STR);
			$stmt->bindParam(":cocina", $datosModel['cocina'], PDO::PARAM_STR);
			$stmt->bindParam(":precio", $datosModel['precio'], PDO::PARAM_INT);
			//echo "<script>alert('Guardo')</script>";
			echo "<br>".$consulta."<br>";
			if($stmt->execute())
				{	/*$ultimo_id=$stmt->lastInsertId();	//ultimo pedido
					echo "<script>alert('Entro CRUD ".$ultimo_id." no')</script>";
					session_start();
					$_SESSION["prod"]=$ultimo_id;
					echo "<br>".$_SESSION["prod"]."<br>";*/
					return "success";
				}
			else{ return "error";	}
			$stmt->close();
		}
	#------------------------------------------------------
	#VISTA PRODUCTOS
	#-------------------------------------
	 public static function vistaProductoModel($tabla)
		{	try {
					$stmt = Conexion::conectar()->prepare("SELECT id, codigo, nombre, precio, categoria FROM $tabla where activo='s' order by nombre ");
					$stmt->execute();

					return $stmt->fetchAll();
					$stmt->close();

				}
			catch (Exception $e)
				{					//$stmt->rollBack();
					print "Error!: ".$e->getMessage()."</br>";
					return "Error!: ".$e->getMessage()."</br>";
				}
		}
	#-------------------------------------

	#EDITAR PRODUCTOS
	#-------------------------------------
	 public static function editarProductoModel($datosModel, $tabla)
		{
			$stmt = Conexion::conectar()->prepare("SELECT id, codigo, nombre, precio, cocina, categoria FROM $tabla WHERE id = :id");
			$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);
			$stmt->execute();
			return $stmt->fetch();
			$stmt->close();
		}
	#------------------------------------------
	#ACTUALIZAR PRODUCTOS
	#-------------------------------------
		public static function actualizarProductoModel($datosModel, $tabla)
			{ 	try {
						echo "<script>alert('Entro Actualizar Producto')</script>";
						$stmt = Conexion::conectar()->prepare("UPDATE $tabla SET codigo = :codigo, nombre = :nombre, precio =:precio, categoria =:categoria WHERE id = :id");
						$stmt->bindParam(":codigo", $datosModel["codigo"], PDO::PARAM_STR);
						$stmt->bindParam(":nombre", $datosModel["nombre"], PDO::PARAM_STR);
						$stmt->bindParam(":precio", $datosModel["precio"], PDO::PARAM_INT);
						$stmt->bindParam(":categoria", $datosModel["categoria"], PDO::PARAM_INT);
						$stmt->bindParam(":id", $datosModel["id"], PDO::PARAM_INT);
						if($stmt->execute())
							{echo "<script>alert('Guardo Actualizar Producto')</script>";return "success";	}
						else{	return "error";			}
						$stmt->close();
					}
				catch (Exception $e)
					{					//$stmt->rollBack();
						print "Error!: ".$e->getMessage()."</br>";
						return "Error!: ".$e->getMessage()."</br>";
					}
			}

	#BORRAR PRODUCTOS
	#------------------------------------
		public static function borrarProductoModel($datosModel, $tabla)
			{
				try {
						$stmt = Conexion::conectar()->prepare("UPDATE $tabla SET  activo='n' WHERE id = :id");
						$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);
						if($stmt->execute())
							{	return "success";	}
						else{	return "error";		}
						$stmt->close();
					}
				catch (Exception $e)
					{					//$stmt->rollBack();
						print "Error!: ".$e->getMessage()."</br>";
						return "Error!: ".$e->getMessage()."</br>";
					}
			}
#----------------------------------------------
}