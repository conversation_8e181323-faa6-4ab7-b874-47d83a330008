const express = require("express");
const net = require("net");
const cors = require("cors");
const mysql = require("mysql2/promise");

const app = express();
const PORT = 3000;

// Configuración de base de datos
const DB_CONFIG = {
    host: "localhost",
    user: "root", // Ajustar según tu configuración
    password: "", // Ajustar según tu configuración
    database: "ewogjwfm_macarena"
};

// Middleware completo para celulares
app.use(cors({
    origin: "*",
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization", "X-Requested-With"],
    credentials: true
}));

app.use(express.json({ limit: "10mb" }));
app.use(express.raw({ type: "*/*", limit: "10mb" }));
app.use(express.urlencoded({ extended: true }));

// Headers adicionales para móviles
app.use((req, res, next) => {
    res.header("Access-Control-Allow-Origin", "*");
    res.header("Access-Control-Allow-Methods", "GET,PUT,POST,DELETE,OPTIONS");
    res.header("Access-Control-Allow-Headers", "Content-Type, Authorization, Content-Length, X-Requested-With");
    
    // Responder a preflight requests
    if (req.method === "OPTIONS") {
        res.sendStatus(200);
    } else {
        next();
    }
});

// Configuración de impresoras
const IMPRESORAS = {
    "COCINA": { ip: "**************", puerto: 9100 },
    "BAR": { ip: "**************", puerto: 9100 },
    "ASADOS": { ip: "**************", puerto: 9100 }
};

// Función para obtener mapeo de categorías
async function obtenerMapeoCategoria() {
    try {
        const connection = await mysql.createConnection(DB_CONFIG);
        const [rows] = await connection.execute(
            "SELECT categoria, impresora FROM categoria_impresora WHERE activo = TRUE"
        );
        await connection.end();
        
        const mapeo = {};
        rows.forEach(row => {
            mapeo[row.categoria] = row.impresora;
        });
        return mapeo;
    } catch (error) {
        console.error("Error obteniendo mapeo:", error);
        return {};
    }
}

// Función para enviar a impresora
function enviarAImpresora(nombreImpresora, datos, res) {
    const config = IMPRESORAS[nombreImpresora.toUpperCase()];
    if (!config) {
        return res.status(404).json({ 
            error: `Impresora ${nombreImpresora} no encontrada`,
            impresoras_disponibles: Object.keys(IMPRESORAS)
        });
    }

    const client = new net.Socket();
    let respuesta = "";

    client.setTimeout(10000); // 10 segundos timeout

    client.connect(config.puerto, config.ip, () => {
        console.log(`✅ Conectado a ${nombreImpresora} (${config.ip}:${config.puerto})`);
        
        if (datos && datos.length > 0) {
            client.write(datos);
            console.log(`📄 Enviado ${datos.length} bytes a ${nombreImpresora}`);
        }
    });

    client.on("data", (data) => {
        respuesta += data.toString();
    });

    client.on("close", () => {
        console.log(`🔌 Conexión cerrada con ${nombreImpresora}`);
        res.json({ 
            success: true, 
            impresora: nombreImpresora,
            ip: config.ip,
            puerto: config.puerto,
            respuesta: respuesta || "Enviado correctamente",
            timestamp: new Date().toISOString()
        });
    });

    client.on("error", (err) => {
        console.error(`❌ Error con ${nombreImpresora}:`, err.message);
        res.status(500).json({ 
            error: `Error conectando a ${nombreImpresora}: ${err.message}`,
            impresora: nombreImpresora,
            ip: config.ip,
            puerto: config.puerto
        });
    });

    client.on("timeout", () => {
        console.error(`⏰ Timeout con ${nombreImpresora}`);
        client.destroy();
        res.status(408).json({ 
            error: `Timeout conectando a ${nombreImpresora}`,
            impresora: nombreImpresora,
            ip: config.ip
        });
    });
}

// Rutas directas para impresoras
app.all("/cocina", (req, res) => {
    enviarAImpresora("COCINA", req.body, res);
});

app.all("/bar", (req, res) => {
    enviarAImpresora("BAR", req.body, res);
});

app.all("/asados", (req, res) => {
    enviarAImpresora("ASADOS", req.body, res);
});

// Ruta para imprimir por categoría
app.post("/imprimir-categoria", async (req, res) => {
    try {
        const { categoria, datos } = req.body;
        
        if (!categoria) {
            return res.status(400).json({ error: "Categoría requerida" });
        }
        
        const mapeo = await obtenerMapeoCategoria();
        const impresora = mapeo[categoria];
        
        if (!impresora) {
            return res.status(404).json({ 
                error: `No hay impresora configurada para la categoría: ${categoria}`,
                mapeo_disponible: mapeo
            });
        }
        
        console.log(`📋 Imprimiendo categoría "${categoria}" en impresora "${impresora}"`);
        enviarAImpresora(impresora, datos, res);
        
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Ruta para obtener configuración
app.get("/config", async (req, res) => {
    try {
        const mapeo = await obtenerMapeoCategoria();
        res.json({
            impresoras: IMPRESORAS,
            mapeo_categorias: mapeo,
            proxy_info: {
                version: "2.0",
                mobile_optimized: true,
                timestamp: new Date().toISOString()
            }
        });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Ruta de estado
app.get("/status", (req, res) => {
    res.json({
        proxy: "Proxy de Impresoras Macarena Mobile",
        version: "2.0",
        mobile_support: true,
        impresoras: IMPRESORAS,
        endpoints: ["/cocina", "/bar", "/asados", "/imprimir-categoria", "/config"],
        timestamp: new Date().toISOString()
    });
});

// Ruta principal
app.get("/", (req, res) => {
    res.json({
        mensaje: "🖨️ Proxy de Impresoras Macarena - Versión Móvil",
        version: "2.0",
        mobile_optimized: true,
        rutas_directas: ["/cocina", "/bar", "/asados"],
        rutas_inteligentes: ["/imprimir-categoria"],
        configuracion: ["/config", "/status"],
        impresoras: Object.keys(IMPRESORAS)
    });
});

// Iniciar servidor
app.listen(PORT, "0.0.0.0", () => {
    console.log(`🚀 Proxy móvil de impresoras ejecutándose en puerto ${PORT}`);
    console.log(`📡 Accesible desde: http://***********:${PORT}`);
    console.log(`📱 Optimizado para celulares y tablets`);
    console.log(`🖨️ Impresoras configuradas:`, Object.keys(IMPRESORAS));
    console.log(`🏷️ Soporte para impresión por categoría activado`);
});
