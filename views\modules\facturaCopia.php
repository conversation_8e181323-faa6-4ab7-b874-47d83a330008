<?php
	if(isset($_GET["action"]))
		{	if($_GET["action"] == "okPm")
			{echo "Registro Exitoso";	}
		}
?>
<script type="text/javascript">
	// ------FACTURAR--------------
	 function buscarfactura()
		{
		 var factura = $('#factura').val();
		 var pago = $('#pago').val();
		 //alert("Buscar factura funtion" + factura);
		 if (confirm('Buscar Facturar No :' + factura+' ? '))
			{		//setTimeout('location.href="views/modules/ajaxFactura.php"',500);
			 $("#destino").load("views/modules/ajaxFacturaBuscar.php", {factura: factura, pago: pago }, function(){
						 //alert("recibidos los datos por ajax efectivo");
						});
			}
		}
	// ------FACTURAR FIN//		".$_SESSION["mesa"]."   <div id="destino1" name="destino1" ></div>
</script>
	<div class="row">
	    <div class="col-md-12">
	      	<form  method="post" >
	      	<div align="right">
	  		 <label>Tipo de Pago:</label>
	  		 <select name="pago" id="pago">
			  <option value="1">Efectivo</option>
			  <option value="2">Credito</option>
			 </select>
		  	</div>
				<label for=""><b>factura : </b></label><input type="text" placeholder="No factura" name="factura" id="factura" size="18" autofocus  required>
				<a class="btn btn-primary btn-lg"  role="button" onclick="buscarfactura();" name="factura" value="factura">Buscar Facturar &raquo;</a></p>
			</form>
				<div id="destino">	</div>
	    </div>
	</div>
	<div class="row">
		<div class="col-md-6 col-xs-12">
			<?php
		$devolucion = new controllerdevolucion();
		$devolucion -> ultimasFacturaController();
		//$devolucion -> buscarFacturaController();
		//$devolucion -> actualizarDevolucionController();
	?>
		</div>
		<div class="col-md-6 col-xs-12">
			<?php
		$devolucion = new controllerdevolucion();
		$devolucion -> ultimasFacturaCreditoController();
		//$devolucion -> buscarFacturaController();
		//$devolucion -> actualizarDevolucionController();
	?>
		</div>
	</div>
	