<?php

session_start();

if(!isset($_SESSION["validar"]) || !$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Test AJAX Marcar Entregado</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
</head>
<body>

<div class="container">
    <h2>🧪 Test: AJAX Marcar Entregado</h2>
    <hr>
    
    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">📊 Información de sesión</h3>
        </div>
        <div class="panel-body">
            <p><strong>Usuario:</strong> <?php echo $_SESSION['persona'] ?? 'No definido'; ?></p>
            <p><strong>ID Usuario:</strong> <?php echo $_SESSION['usuario'] ?? 'No definido'; ?></p>
            <p><strong>Tipo Usuario:</strong> <?php echo $_SESSION['tipo_usuario'] ?? 'No definido'; ?></p>
            <p><strong>Validar:</strong> <?php echo $_SESSION['validar'] ? 'Sí' : 'No'; ?></p>
        </div>
    </div>
    
    <div class="panel panel-warning">
        <div class="panel-heading">
            <h3 class="panel-title">🧪 Test 1: Ping al AJAX</h3>
        </div>
        <div class="panel-body">
            <button onclick="testPing()" class="btn btn-info">🏓 Test Ping</button>
            <div id="ping-result" style="margin-top: 10px;"></div>
        </div>
    </div>
    
    <div class="panel panel-success">
        <div class="panel-heading">
            <h3 class="panel-title">🧪 Test 2: Marcar Entregado</h3>
        </div>
        <div class="panel-body">
            <div class="form-group">
                <label>ID del pedido:</label>
                <input type="number" id="pedido-id" class="form-control" value="4" placeholder="Ej: 4">
            </div>
            <button onclick="testMarcarEntregado()" class="btn btn-success">✅ Test Marcar Entregado</button>
            <div id="marcar-result" style="margin-top: 10px;"></div>
        </div>
    </div>
    
    <div class="panel panel-primary">
        <div class="panel-heading">
            <h3 class="panel-title">🧪 Test 3: Obtener Pedidos Pendientes</h3>
        </div>
        <div class="panel-body">
            <button onclick="testObtenerPendientes('bar')" class="btn btn-info">🍺 Obtener BAR</button>
            <button onclick="testObtenerPendientes('cocina')" class="btn btn-warning">🍳 Obtener COCINA</button>
            <div id="pendientes-result" style="margin-top: 10px;"></div>
        </div>
    </div>
    
    <div class="panel panel-danger">
        <div class="panel-heading">
            <h3 class="panel-title">📋 Log de Errores</h3>
        </div>
        <div class="panel-body">
            <div id="error-log" style="background: #f5f5f5; padding: 10px; min-height: 100px; font-family: monospace; font-size: 12px;"></div>
            <button onclick="clearLog()" class="btn btn-sm btn-default">🗑️ Limpiar Log</button>
        </div>
    </div>
    
    <hr>
    <div class="row">
        <div class="col-md-4">
            <a href="index.php?action=pedidosBarPendientes" class="btn btn-info btn-block">🔙 Volver a Bar</a>
        </div>
        <div class="col-md-4">
            <a href="index.php?action=debug_marcar_entregado" class="btn btn-primary btn-block">🔍 Debug Anterior</a>
        </div>
        <div class="col-md-4">
            <a href="index.php?action=pedidosCocinaPendientes" class="btn btn-warning btn-block">🍳 Ir a Cocina</a>
        </div>
    </div>
</div>

<script>
function log(message) {
    const timestamp = new Date().toLocaleTimeString();
    const logDiv = document.getElementById('error-log');
    logDiv.innerHTML += `[${timestamp}] ${message}\n`;
    logDiv.scrollTop = logDiv.scrollHeight;
}

function clearLog() {
    document.getElementById('error-log').innerHTML = '';
}

function testPing() {
    log('🏓 Iniciando test de ping...');
    
    $.ajax({
        url: 'views/modules/ajaxPedidosCategorias.php',
        method: 'POST',
        dataType: 'json',
        data: JSON.stringify({
            accion: 'ping'
        }),
        contentType: 'application/json',
        success: function(response) {
            log('✅ Ping exitoso: ' + JSON.stringify(response));
            $('#ping-result').html('<div class="alert alert-success">✅ Ping exitoso: ' + JSON.stringify(response) + '</div>');
        },
        error: function(xhr, status, error) {
            log('❌ Error en ping: ' + status + ' - ' + error);
            log('❌ Respuesta: ' + xhr.responseText);
            $('#ping-result').html('<div class="alert alert-danger">❌ Error: ' + status + ' - ' + error + '<br>Respuesta: ' + xhr.responseText + '</div>');
        }
    });
}

function testMarcarEntregado() {
    const pedidoId = document.getElementById('pedido-id').value;
    
    if (!pedidoId) {
        alert('Por favor ingrese un ID de pedido');
        return;
    }
    
    log('✅ Iniciando test de marcar entregado para pedido: ' + pedidoId);
    
    $.ajax({
        url: 'views/modules/ajaxPedidosCategorias.php',
        method: 'POST',
        dataType: 'json',
        data: JSON.stringify({
            accion: 'marcar_entregado',
            pedido_id: pedidoId
        }),
        contentType: 'application/json',
        success: function(response) {
            log('✅ Marcar entregado exitoso: ' + JSON.stringify(response));
            $('#marcar-result').html('<div class="alert alert-success">✅ Éxito: ' + JSON.stringify(response) + '</div>');
        },
        error: function(xhr, status, error) {
            log('❌ Error en marcar entregado: ' + status + ' - ' + error);
            log('❌ Respuesta: ' + xhr.responseText);
            $('#marcar-result').html('<div class="alert alert-danger">❌ Error: ' + status + ' - ' + error + '<br>Respuesta: ' + xhr.responseText + '</div>');
        }
    });
}

function testObtenerPendientes(categoria) {
    log('📋 Iniciando test de obtener pendientes para: ' + categoria);
    
    $.ajax({
        url: 'views/modules/ajaxPedidosCategorias.php',
        method: 'POST',
        dataType: 'json',
        data: JSON.stringify({
            accion: 'obtener_pendientes',
            categoria: categoria
        }),
        contentType: 'application/json',
        success: function(response) {
            log('✅ Obtener pendientes exitoso: ' + JSON.stringify(response));
            $('#pendientes-result').html('<div class="alert alert-success">✅ ' + categoria.toUpperCase() + ': ' + response.total + ' pedidos encontrados</div>');
        },
        error: function(xhr, status, error) {
            log('❌ Error en obtener pendientes: ' + status + ' - ' + error);
            log('❌ Respuesta: ' + xhr.responseText);
            $('#pendientes-result').html('<div class="alert alert-danger">❌ Error: ' + status + ' - ' + error + '<br>Respuesta: ' + xhr.responseText + '</div>');
        }
    });
}

// Auto-ejecutar ping al cargar
$(document).ready(function() {
    log('🚀 Página cargada, ejecutando ping automático...');
    testPing();
});
</script>

</body>
</html>
