<?php
// Test de debug para envío de pedidos
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
if (!isset($_SESSION["usuario"])) {
    $_SESSION["usuario"] = 1;
    $_SESSION["tipo_usuario"] = 1;
    $_SESSION["perfil"] = "administrador";
}

require_once "../../models/conexion.php";
require_once "../../controllers/controllerEstadoPedidos.php";
require_once "../../models/crudEstadoPedidos.php";

$mesaId = isset($_GET['mesa']) ? $_GET['mesa'] : 1;

echo "<h1>🔍 Debug Envío de Pedidos - Mesa $mesaId</h1>";

// Simular el proceso completo paso a paso
try {
    $controller = new ControllerEstadoPedidos();
    
    echo "<h3>PASO 1: Estado Inicial</h3>";
    $pedidosIniciales = $controller->obtenerPedidosMesaController($mesaId);
    echo "Pedidos activos: " . count($pedidosIniciales) . "<br>";
    
    $borradorInicial = $controller->obtenerPedidoBorradorController($mesaId);
    if ($borradorInicial) {
        echo "Pedido borrador actual: {$borradorInicial['numero_pedido']} (ID: {$borradorInicial['id']})<br>";
        $productosIniciales = DatosEstadoPedidos::obtenerProductosPedidoModel($borradorInicial['id']);
        echo "Productos en borrador: " . count($productosIniciales) . "<br>";
        
        if (count($productosIniciales) > 0) {
            echo "<h3>PASO 2: Enviar Pedido</h3>";
            echo "Enviando pedido ID: {$borradorInicial['id']}<br>";
            
            $resultado = $controller->enviarPedidoController($borradorInicial['id']);
            echo "Resultado del envío: " . print_r($resultado, true) . "<br>";
            
            if ($resultado['status'] == 'success') {
                echo "✅ Pedido enviado exitosamente<br>";
                
                echo "<h3>PASO 3: Verificar Estado del Pedido Enviado</h3>";
                $pedidoEnviado = DatosEstadoPedidos::obtenerPedidoPorIdModel($borradorInicial['id']);
                echo "Estado del pedido enviado: {$pedidoEnviado['estado']}<br>";
                
                echo "<h3>PASO 4: Buscar Nuevo Pedido Borrador</h3>";
                $nuevoBorrador = $controller->obtenerPedidoBorradorController($mesaId);
                
                if ($nuevoBorrador) {
                    if ($nuevoBorrador['id'] != $borradorInicial['id']) {
                        echo "✅ ÉXITO: Nuevo pedido borrador creado automáticamente<br>";
                        echo "- Nuevo ID: {$nuevoBorrador['id']}<br>";
                        echo "- Nuevo Número: {$nuevoBorrador['numero_pedido']}<br>";
                        echo "- Estado: {$nuevoBorrador['estado']}<br>";
                    } else {
                        echo "❌ PROBLEMA: Se devolvió el mismo pedido borrador<br>";
                        echo "- ID devuelto: {$nuevoBorrador['id']}<br>";
                        echo "- ID original: {$borradorInicial['id']}<br>";
                    }
                } else {
                    echo "❌ PROBLEMA: No se encontró pedido borrador después del envío<br>";
                }
                
                echo "<h3>PASO 5: Estado Final de la Mesa</h3>";
                $pedidosFinales = $controller->obtenerPedidosMesaController($mesaId);
                echo "Pedidos activos finales: " . count($pedidosFinales) . "<br>";
                
                if (!empty($pedidosFinales)) {
                    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
                    echo "<tr><th>ID</th><th>Número</th><th>Estado</th><th>Productos</th></tr>";
                    foreach ($pedidosFinales as $p) {
                        $style = ($p['id'] == $borradorInicial['id']) ? 'background-color: #ffeb3b;' : '';
                        echo "<tr style='$style'>";
                        echo "<td>{$p['id']}</td>";
                        echo "<td>{$p['numero_pedido']}</td>";
                        echo "<td>{$p['estado']}</td>";
                        echo "<td>{$p['total_productos']}</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                    echo "<small>* Fila amarilla = pedido original enviado</small><br>";
                }
                
                // Test manual de creación de pedido borrador
                echo "<h3>PASO 6: Test Manual de Creación</h3>";
                echo "<button onclick='testCrearBorrador()' style='background: #28a745; color: white; padding: 10px;'>🧪 Crear Pedido Borrador Manualmente</button><br>";
                
            } else {
                echo "❌ Error al enviar pedido: {$resultado['message']}<br>";
            }
        } else {
            echo "⚠️ El pedido borrador no tiene productos. Agregue productos y vuelva a probar.<br>";
        }
    } else {
        echo "❌ No hay pedido borrador para enviar<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error en el test: " . $e->getMessage() . "<br>";
    echo "Stack trace: " . $e->getTraceAsString() . "<br>";
}

echo "<br><a href='index.php?action=registroPmesa&ida=$mesaId' style='background: #007bff; color: white; padding: 10px; text-decoration: none;'>🔙 Volver a Mesa $mesaId</a>";
echo "<br><a href='test_envio_debug.php?mesa=$mesaId' style='background: #6c757d; color: white; padding: 10px; text-decoration: none; margin: 5px;'>🔄 Repetir Test</a>";
?>

<script>
function testCrearBorrador() {
    const mesaId = <?=$mesaId?>;
    console.log('🧪 Test manual: Creando pedido borrador para mesa', mesaId);
    
    fetch('ajaxEstadoPedidos.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'obtener_pedido_borrador=true&mesa_id=' + mesaId
    })
    .then(response => response.text())
    .then(text => {
        console.log('📋 Respuesta del servidor:', text);
        try {
            const data = JSON.parse(text);
            if (data && data.id) {
                alert('✅ Pedido borrador creado: ' + data.numero_pedido + ' (ID: ' + data.id + ')');
            } else {
                alert('⚠️ Respuesta inesperada: ' + text);
            }
        } catch (e) {
            alert('❌ Error parseando respuesta: ' + text);
        }
        
        // Recargar para ver cambios
        setTimeout(() => location.reload(), 2000);
    })
    .catch(error => {
        console.error('❌ Error:', error);
        alert('Error de conexión: ' + error);
    });
}
</script>
