<?php

#EXTENSIÓN DE CLASES: Los objetos pueden ser extendidos, y pueden heredar propiedades y métodos. Para definir una clase como extensión, debo definir una clase padre, y se utiliza dentro de una clase hija.

require_once "conexion.php";
class DatosUsuario extends Conexion
{
	#REGISTRO DE USUARIOS
	#-------------------------------------
	 public static function registroUsuarioModel($datosModel, $tabla)
		{
			date_default_timezone_set("America/Bogota");
			$fecha_creado=strftime("%Y-%m-%d %H:%M:%S");
			$stmt = Conexion::conectar();
			try
			 {//echo '<script>alert("entro CRUD");</script>';
			 	$stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
				$stmt->beginTransaction();
				$consultar="INSERT INTO $tabla (ciudad_id, roles_id, cedula, nombre, apellidos, fecha_nacimiento, direccion, telefono, email, fecha_registro, activo, usuario, clave, pregunta,respuesta)
					VALUES (".$datosModel["ciudad_id"].", ".$datosModel["roles_id"].", '".$datosModel["cedula"]."', '".$datosModel["nombre"]."', '".$datosModel["apellidos"]."', '".$datosModel["fecha_nacimiento"]."', '".$datosModel["direccion"]."', '".$datosModel["telefono"]."', '".$datosModel["email"]."', '".$fecha_creado."', '".$datosModel["activo"]."', '".$datosModel["usuario"]."', '".$datosModel["clave"]."', '".$datosModel["pregunta"]."', '".$datosModel["respuesta"]."')";

					///echo "<script>alert('consultar:".$consultar."');</script>";
				echo "<br".$consultar."<br";
				$stmt->exec($consultar);
				$idTablatura = $stmt->lastInsertId();	//ultimo id
				$cambio=$idTablatura.'-Ciudad cod'.$datosModel["ciudad_id"].'- roll'.$datosModel["roles_id"].'- cedula'.$datosModel["cedula"].'- nombre'.$datosModel["nombre"].'- apellidos'.$datosModel["apellidos"].'- fecha_nacimiento'.$datosModel["fecha_nacimiento"].'- direccion'.$datosModel["direccion"].'- telefono'.$datosModel["telefono"].'- email'.$datosModel["email"].'- usuario'.$datosModel["usuario"].'- clave'.$datosModel["clave"].'-activo'.$datosModel["activo"];
				$consultarA="INSERT INTO auditoria(tabla, accion, persona_id, fecha_accion, cambio)
				VALUES ('".$tabla."', 'INSERT', ".$_SESSION["usuario"].", '$fecha_creado', '$cambio')";
				echo "<br".$consultarA."<br";
				$stmt->exec($consultarA);
				$stmt->commit();
				//echo "<script>alert('fin de cruD try que pasa');</script>";
				return "success";
				$stmt->close();
			 }
			catch (Exception $e)
			 {
			 	echo "<script>alert('catch error');</script>";
			 	$stmt->rollBack();
				print "Error!: ".$e->getMessage()."</br>";
				return "Error!: ".$e->getMessage()."</br>";
			 }
		}
	#-------------------------------------
	#INGRESO USUARIO
	#-------------------------------------
	 public static function ingresoUsuarioModel($datosModel, $tabla)
		{
			$stmt = Conexion::conectar()->prepare("SELECT id, punto_id, roles_id, nombre, apellidos, usuario, clave FROM $tabla WHERE usuario = :usuario  ");
			$stmt->bindParam(":usuario", $datosModel["usuario"], PDO::PARAM_STR);
			$stmt->execute();
			#fetch(): Obtiene una fila de un conjunto de resultados asociado al objeto PDOStatement.
			return $stmt->fetch();
			$stmt->close();
		}
	#-------------------------------------------------------
	#VISTA USUARIOS CONSULTAR
	#-------------------------------------
		public static function vistaUsuarioModel($tabla)
			{
				try {
						$consulta="SELECT p.id AS pid, p.ciudad_id AS pciudad_id, p.roles_id AS proles_id, p.cedula AS pcedula, p.nombre AS pnombre, p.apellidos AS papellidos, p.fecha_nacimiento AS pfecha_nacimiento, p.direccion AS pdireccion, p.telefono AS ptelefono, p.email AS pemail, p.fecha_registro AS pfecha_registro, p.activo AS pactivo, p.usuario AS pusuario, p.clave AS pclave, p.pregunta AS ppregunta, p.respuesta AS prespuesta,
							r.id AS rid, r.nombre AS rnombre
						FROM $tabla p, roles r WHERE p.roles_id=r.id";
						//echo "<script>alert('Entro CRUD ".$consulta." no')</script>";
						$stmt = Conexion::conectar()->prepare($consulta);
						//$stmt = Conexion::conectar()->prepare("SELECT p.id, tp.nombre as usu, p.nombres, p.apellidos, p.cedula, p.celular, p.usuario, p.pass FROM $tabla p, tipo_persona tp where p.tipo_persona_id=tp.id");

						$stmt->execute();
						#fetchAll(): Obtiene todas las filas de un conjunto de resultados asociado al objeto PDOStatement.
						return $stmt->fetchAll();
						$stmt->close();
					}
				catch (Exception $e)
					{					//$stmt->rollBack();
						print "Error!: ".$e->getMessage()."</br>";
						return "Error!: ".$e->getMessage()."</br>";
					}

			}
	#-----------------------------------------------------
	##EDITAR USUARIO
	#-------------------------------------
	 public static function editarUsuarioModel($datosModel, $tabla)
		{
			$stmt = Conexion::conectar()->prepare("SELECT p.id AS pid, p.ciudad_id AS pciudad_id, p.roles_id AS proles_id, p.cedula AS pcedula, p.nombre AS pnombre, p.apellidos AS papellidos, p.fecha_nacimiento AS pfecha_nacimiento, p.direccion AS pdireccion, p.telefono AS ptelefono, p.email AS pemail, p.fecha_registro AS pfecha_registro, p.activo AS pactivo, p.usuario AS pusuario, p.clave AS pclave, p.pregunta AS ppregunta, p.respuesta AS prespuesta,
				r.id AS rid, r.nombre AS rnombre
				FROM $tabla p, roles r
				WHERE p.roles_id=r.id AND p.id = :id");
			$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);
			$stmt->execute();
			return $stmt->fetch();
			$stmt->close();
		}
	#---------------------------------------------------------
	#ACTUALIZAR USUARIO  ciudad_id, roles_id, cedula, nombre, apellidos, fecha_nacimiento, direccion, telefono, email, fecha_registro, activo, usuario, clave, pregunta,respuesta
	#-------------------------------------
	 public static function actualizarUsuarioModel($datosModel, $tabla)
		{
		 $consulta = "UPDATE $tabla SET ciudad_id=".$datosModel["ciudad_id"].", roles_id = ".$datosModel["roles_id"].", cedula = '".$datosModel["cedula"]."', nombre = '".$datosModel["nombre"]."', apellidos =' ".$datosModel["apellidos"]."',  fecha_nacimiento = '".$datosModel["fecha_nacimiento"]."', direccion='".$datosModel["direccion"]."', telefono='".$datosModel["telefono"]."', email='".$datosModel["email"]."', fecha_registro='".$datosModel["fecha_registro"]."', activo='".$datosModel["activo"]."', usuario = '".$datosModel["usuario"]."', clave = '".$datosModel["clave"]."', pregunta='".$datosModel["pregunta"]."', respuesta='".$datosModel["respuesta"]."' WHERE id = '".$datosModel["id"]."'";
		 $stmt = Conexion::conectar()->prepare($consulta);
		 if($stmt->execute())	{	return "success";}
		 else{	return "error";	}
		 $stmt->close();
		}
	#-------------------------------------
	#BORRAR USUARIO
	#------------------------------------
	 public static function borrarUsuarioModel($datosModel, $tabla)
		{
			date_default_timezone_set("America/Bogota");
			$fecha_creado=strftime("%Y-%m-%d %H:%M:%S");
			$stmt = Conexion::conectar();
		 try
			{//echo '<script>alert("entro CRUD");</script>';
			 	$stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
				$stmt->beginTransaction();
				$consultar = "DELETE FROM $tabla WHERE id =".$datosModel;
			    echo "<br".$consultar."<br";
				$stmt->exec($consultar);
				$cambio=$datosModel;
				$consultarA="INSERT INTO auditoria(tabla, accion, persona_id, fecha_accion, cambio)
				VALUES ('".$tabla."', 'DELETE', ".$_SESSION["usuario"].", '$fecha_creado', '$cambio')";
				echo "<br".$consultarA."<br";
				$stmt->exec($consultarA);
				$stmt->commit();
				//echo "<script>alert('fin de cruD try que pasa');</script>";
				return "success";
				$stmt->close();
			 }
			catch (Exception $e)
			 {
			 	echo "<script>alert('catch error');</script>";
			 	$stmt->rollBack();
				echo "Error!: ".$e->getMessage()."</br>";
				return "Error!: ".$e->getMessage()."</br>";
			 }

		}
	#-------------------------------------
 }