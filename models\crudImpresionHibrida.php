<?php
/**
 * CRUD para Sistema de Impresión Híbrida
 * Maneja impresión directa y por proxy automáticamente
 */

class CrudImpresionHibrida {
    private $pdo;
    private $config;
    
    public function __construct() {
        require_once 'conexion.php';
        $conexion = new Conexion();
        $this->pdo = $conexion->conectar();
        $this->cargarConfiguracion();
    }
    
    /**
     * Cargar configuración del sistema híbrido
     */
    private function cargarConfiguracion() {
        try {
            $stmt = $this->pdo->query("SELECT clave, valor FROM config_impresion");
            $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $this->config = [];
            foreach ($rows as $row) {
                $this->config[$row['clave']] = $row['valor'];
            }
            
            // Valores por defecto si no existen
            $defaults = [
                'modo_impresion' => 'hibrido',
                'ip_proxy' => '***********',
                'puerto_proxy' => '3000',
                'timeout_conexion' => '5',
                'reintentos_max' => '3',
                'red_local' => '192.168.68',
                'impresion_directa_habilitada' => '1'
            ];
            
            foreach ($defaults as $clave => $valor) {
                if (!isset($this->config[$clave])) {
                    $this->config[$clave] = $valor;
                }
            }
            
        } catch (Exception $e) {
            // Si no existe la tabla, usar valores por defecto
            $this->config = [
                'modo_impresion' => 'hibrido',
                'ip_proxy' => '***********',
                'puerto_proxy' => '3000',
                'timeout_conexion' => '5',
                'reintentos_max' => '3',
                'red_local' => '192.168.68',
                'impresion_directa_habilitada' => '1'
            ];
        }
    }
    
    /**
     * Detectar si el cliente está en red local
     */
    private function esRedLocal() {
        $ip_cliente = $_SERVER['REMOTE_ADDR'] ?? '';
        return strpos($ip_cliente, $this->config['red_local']) === 0;
    }
    
    /**
     * Obtener impresora por nombre
     */
    public function obtenerImpresora($nombre) {
        try {
            $stmt = $this->pdo->prepare("SELECT * FROM impresoras WHERE nombre = ? AND activa = 1");
            $stmt->execute([$nombre]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Obtener impresora por categoría de producto
     */
    public function obtenerImpresoraPorCategoria($categoria) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT i.* FROM impresoras i 
                INNER JOIN categoria_impresora ci ON i.nombre = ci.impresora 
                WHERE ci.categoria = ? AND ci.activo = 1 AND i.activa = 1
            ");
            $stmt->execute([$categoria]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Imprimir directamente a la impresora (red local)
     */
    private function imprimirDirecto($ip, $puerto, $datos) {
        $resultado = [
            'success' => false,
            'metodo' => 'directo',
            'ip' => $ip,
            'puerto' => $puerto,
            'error' => '',
            'tiempo' => 0
        ];
        
        $inicio = microtime(true);
        
        try {
            $socket = @fsockopen($ip, $puerto, $errno, $errstr, (int)$this->config['timeout_conexion']);
            
            if (!$socket) {
                $resultado['error'] = "No se pudo conectar: $errstr ($errno)";
                return $resultado;
            }
            
            if (!empty($datos)) {
                $bytes_enviados = fwrite($socket, $datos);
                if ($bytes_enviados === false) {
                    $resultado['error'] = "Error al enviar datos";
                    fclose($socket);
                    return $resultado;
                }
            }
            
            fclose($socket);
            
            $resultado['success'] = true;
            $resultado['tiempo'] = round((microtime(true) - $inicio) * 1000, 2);
            
        } catch (Exception $e) {
            $resultado['error'] = $e->getMessage();
        }
        
        return $resultado;
    }
    
    /**
     * Imprimir a través del proxy
     */
    private function imprimirProxy($nombreImpresora, $datos) {
        $resultado = [
            'success' => false,
            'metodo' => 'proxy',
            'url' => '',
            'error' => '',
            'tiempo' => 0
        ];
        
        $inicio = microtime(true);
        
        try {
            $url = "http://{$this->config['ip_proxy']}:{$this->config['puerto_proxy']}/" . strtolower($nombreImpresora);
            $resultado['url'] = $url;
            
            $context = stream_context_create([
                'http' => [
                    'method' => 'POST',
                    'header' => [
                        'Content-Type: text/plain',
                        'Content-Length: ' . strlen($datos)
                    ],
                    'content' => $datos,
                    'timeout' => (int)$this->config['timeout_conexion']
                ]
            ]);
            
            $response = @file_get_contents($url, false, $context);
            
            if ($response === false) {
                $resultado['error'] = "No se pudo conectar al proxy";
                return $resultado;
            }
            
            $json_response = json_decode($response, true);
            
            if ($json_response && isset($json_response['success']) && $json_response['success']) {
                $resultado['success'] = true;
                $resultado['respuesta'] = $json_response['respuesta'] ?? 'Enviado correctamente';
            } else {
                $resultado['error'] = $json_response['error'] ?? 'Error desconocido del proxy';
            }
            
            $resultado['tiempo'] = round((microtime(true) - $inicio) * 1000, 2);
            
        } catch (Exception $e) {
            $resultado['error'] = $e->getMessage();
        }
        
        return $resultado;
    }
    
    /**
     * Imprimir por categoría (función principal)
     */
    public function imprimirPorCategoria($categoria, $datos) {
        $impresora = $this->obtenerImpresoraPorCategoria($categoria);
        
        if (!$impresora) {
            return [
                'success' => false,
                'error' => "No hay impresora configurada para la categoría: $categoria"
            ];
        }
        
        return $this->imprimir($impresora['nombre'], $datos);
    }
    
    /**
     * Función principal de impresión híbrida
     */
    public function imprimir($nombreImpresora, $datos) {
        $impresora = $this->obtenerImpresora($nombreImpresora);
        
        if (!$impresora) {
            return [
                'success' => false,
                'error' => "Impresora '$nombreImpresora' no encontrada o inactiva"
            ];
        }
        
        $es_red_local = $this->esRedLocal();
        $modo = $this->config['modo_impresion'];
        $reintentos = (int)$this->config['reintentos_max'];
        
        $resultados = [];
        $metodos_a_probar = [];
        
        // Determinar qué métodos probar según el modo y la red
        switch ($modo) {
            case 'directo':
                $metodos_a_probar = ['directo'];
                break;
                
            case 'proxy':
                $metodos_a_probar = ['proxy'];
                break;
                
            case 'hibrido':
            default:
                if ($es_red_local && $this->config['impresion_directa_habilitada'] === '1') {
                    $metodos_a_probar = ['directo', 'proxy'];
                } else {
                    $metodos_a_probar = ['proxy', 'directo'];
                }
                break;
        }
        
        // Probar cada método
        foreach ($metodos_a_probar as $metodo) {
            for ($intento = 1; $intento <= $reintentos; $intento++) {
                if ($metodo === 'directo') {
                    $resultado = $this->imprimirDirecto($impresora['ip'], $impresora['puerto'], $datos);
                } else {
                    $resultado = $this->imprimirProxy($impresora['nombre'], $datos);
                }
                
                $resultado['intento'] = $intento;
                $resultado['impresora'] = $nombreImpresora;
                $resultados[] = $resultado;
                
                // Si fue exitoso, terminar
                if ($resultado['success']) {
                    return [
                        'success' => true,
                        'metodo_usado' => $metodo,
                        'impresora' => $nombreImpresora,
                        'tiempo' => $resultado['tiempo'],
                        'intentos' => $intento,
                        'detalles' => $resultado,
                        'todos_los_resultados' => $resultados
                    ];
                }
                
                // Pausa entre reintentos
                if ($intento < $reintentos) {
                    usleep(500000); // 0.5 segundos
                }
            }
        }
        
        // Si llegamos aquí, todos los métodos fallaron
        return [
            'success' => false,
            'error' => 'Todos los métodos de impresión fallaron',
            'impresora' => $nombreImpresora,
            'metodos_probados' => $metodos_a_probar,
            'total_intentos' => count($resultados),
            'todos_los_resultados' => $resultados
        ];
    }
    
    /**
     * Obtener estado de todas las impresoras
     */
    public function obtenerEstadoImpresoras() {
        try {
            $stmt = $this->pdo->query("SELECT * FROM impresoras WHERE activa = 1 ORDER BY nombre");
            $impresoras = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $estados = [];
            
            foreach ($impresoras as $impresora) {
                $estado = [
                    'nombre' => $impresora['nombre'],
                    'ip' => $impresora['ip'],
                    'puerto' => $impresora['puerto'],
                    'directo' => false,
                    'proxy' => false,
                    'tiempo_directo' => 0,
                    'tiempo_proxy' => 0
                ];
                
                // Test directo
                $resultado_directo = $this->imprimirDirecto($impresora['ip'], $impresora['puerto'], '');
                $estado['directo'] = $resultado_directo['success'];
                $estado['tiempo_directo'] = $resultado_directo['tiempo'];
                
                // Test proxy
                $resultado_proxy = $this->imprimirProxy($impresora['nombre'], '');
                $estado['proxy'] = $resultado_proxy['success'];
                $estado['tiempo_proxy'] = $resultado_proxy['tiempo'];
                
                $estados[] = $estado;
            }
            
            return $estados;
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * Registrar log de impresión
     */
    public function registrarLog($resultado) {
        try {
            // Crear tabla de logs si no existe
            $this->pdo->exec("CREATE TABLE IF NOT EXISTS logs_impresion (
                id INT AUTO_INCREMENT PRIMARY KEY,
                impresora VARCHAR(50) NOT NULL,
                metodo VARCHAR(20) NOT NULL,
                success BOOLEAN NOT NULL,
                tiempo_ms DECIMAL(10,2),
                error TEXT,
                ip_cliente VARCHAR(45),
                user_agent TEXT,
                fecha_hora TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )");
            
            $stmt = $this->pdo->prepare("
                INSERT INTO logs_impresion (impresora, metodo, success, tiempo_ms, error, ip_cliente, user_agent) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $resultado['impresora'] ?? '',
                $resultado['metodo_usado'] ?? '',
                $resultado['success'] ? 1 : 0,
                $resultado['tiempo'] ?? 0,
                $resultado['error'] ?? null,
                $_SERVER['REMOTE_ADDR'] ?? '',
                $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);
            
        } catch (Exception $e) {
            // Ignorar errores de log
        }
    }
}
?>
