<?php
// Archivo de depuración para verificar qué está pasando en la mesa

// Activar reporte de errores para debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 Debug Mesa " . $_SESSION["mesa"] . "</h1>";
echo "<hr>";

$mesaId = $_SESSION["mesa"];

// Verificar que la conexión funcione
try {
    $db = Conexion::conectar();
    echo "<div style='background: #d4edda; color: #155724; padding: 10px; margin: 10px 0;'>";
    echo "✅ Conexión a base de datos exitosa";
    echo "</div>";
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px 0;'>";
    echo "❌ Error de conexión: " . $e->getMessage();
    echo "</div>";
    exit;
}

echo "<h2>1. 📊 Información de la Mesa</h2>";
echo "Mesa ID: " . $mesaId . "<br>";
echo "Usuario: " . $_SESSION["usuario"] . "<br>";
echo "Tipo Usuario: " . $_SESSION["tipo_usuario"] . "<br>";

echo "<h2>2. 🔍 Verificar Pedidos en la Mesa</h2>";

// Consulta directa para ver todos los pedidos de la mesa
$stmt = $db->prepare("SELECT * FROM pedidos WHERE mesa_id = ? ORDER BY fecha_pedido DESC");
$stmt->execute([$mesaId]);
$todosPedidos = $stmt->fetchAll();

echo "<strong>Total pedidos en la mesa:</strong> " . count($todosPedidos) . "<br>";

if (!empty($todosPedidos)) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>Número</th><th>Estado</th><th>Facturado</th><th>Fecha</th></tr>";
    foreach ($todosPedidos as $pedido) {
        echo "<tr>";
        echo "<td>" . $pedido['id'] . "</td>";
        echo "<td>" . $pedido['numero_pedido'] . "</td>";
        echo "<td>" . $pedido['estado'] . "</td>";
        echo "<td>" . $pedido['facturado'] . "</td>";
        echo "<td>" . $pedido['fecha_pedido'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "❌ No hay pedidos en la mesa<br>";
}

echo "<h2>3. 🛒 Verificar Productos en producto_vendido_mesa</h2>";

// Consulta directa para ver todos los productos de la mesa
$stmt = $db->prepare("
    SELECT pvm.*, pr.nombre as producto_nombre, p.numero_pedido, p.estado as pedido_estado
    FROM producto_vendido_mesa pvm
    LEFT JOIN productos pr ON pvm.productos_id = pr.id
    LEFT JOIN pedidos p ON pvm.pedidos_id = p.id
    WHERE pvm.mesas_id = ?
    ORDER BY pvm.fecha_hora DESC
");
$stmt->execute([$mesaId]);
$todosProductos = $stmt->fetchAll();

echo "<strong>Total productos en la mesa:</strong> " . count($todosProductos) . "<br>";

if (!empty($todosProductos)) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Producto</th><th>Cantidad</th><th>Pedido ID</th><th>Número Pedido</th><th>Estado Pedido</th><th>Fecha</th></tr>";
    foreach ($todosProductos as $producto) {
        echo "<tr>";
        echo "<td>" . $producto['producto_nombre'] . "</td>";
        echo "<td>" . $producto['cantidad'] . "</td>";
        echo "<td>" . $producto['pedidos_id'] . "</td>";
        echo "<td>" . $producto['numero_pedido'] . "</td>";
        echo "<td>" . $producto['pedido_estado'] . "</td>";
        echo "<td>" . $producto['fecha_hora'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "❌ No hay productos en la mesa<br>";
}

echo "<h2>4. 🔧 Probar Controladores</h2>";

try {
    require_once "controllers/controllerEstadoPedidos.php";
    require_once "models/crudEstadoPedidos.php";
    require_once "models/crudPedidoMesaVendido.php";
    
    $controllerEstado = new ControllerEstadoPedidos();
    
    echo "<h3>4.1 Pedido Borrador</h3>";
    $pedidoBorrador = $controllerEstado->obtenerPedidoBorradorController($mesaId);
    if ($pedidoBorrador) {
        echo "✅ Pedido borrador: " . $pedidoBorrador['numero_pedido'] . " (ID: " . $pedidoBorrador['id'] . ")<br>";
    } else {
        echo "❌ No hay pedido borrador<br>";
    }
    
    echo "<h3>4.2 Todos los Pedidos de la Mesa</h3>";
    $pedidosMesa = $controllerEstado->obtenerPedidosMesaController($mesaId);
    echo "Pedidos encontrados: " . count($pedidosMesa) . "<br>";
    
    if (!empty($pedidosMesa)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Pedido</th><th>Estado</th><th>Productos</th><th>Fecha</th></tr>";
        foreach ($pedidosMesa as $pedido) {
            echo "<tr>";
            echo "<td>" . $pedido['numero_pedido'] . "</td>";
            echo "<td>" . $pedido['estado'] . "</td>";
            echo "<td>" . $pedido['total_productos'] . "</td>";
            echo "<td>" . $pedido['fecha_pedido'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>4.3 Vista de Productos (método actual)</h3>";
    $productosVista = DatosPedidoMesaVendido::vistaPmesaModel($mesaId, "producto_vendido_mesa");
    echo "Productos en vista: " . count($productosVista) . "<br>";
    
    if (!empty($productosVista)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Producto</th><th>Cantidad</th><th>Precio</th><th>Estado</th></tr>";
        foreach ($productosVista as $prod) {
            echo "<tr>";
            echo "<td>" . $prod['nombrepr'] . "</td>";
            echo "<td>" . $prod['cantidadpvm'] . "</td>";
            echo "<td>$" . $prod['preciopr'] . "</td>";
            echo "<td>" . $prod['estado'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}

echo "<h2>5. 🧪 Acciones de Prueba</h2>";
echo "<a href='index.php?action=registroPmesa&ida=" . $mesaId . "' class='btn' style='background: #007bff; color: white; padding: 10px; text-decoration: none; margin: 5px; display: inline-block;'>🔗 Ir a Mesa " . $mesaId . "</a><br>";
echo "<a href='index.php?action=test_simple_mesa' class='btn' style='background: #28a745; color: white; padding: 10px; text-decoration: none; margin: 5px; display: inline-block;'>🧪 Test Simple</a><br>";
echo "<a href='index.php?action=diagnostico' class='btn' style='background: #ffc107; color: black; padding: 10px; text-decoration: none; margin: 5px; display: inline-block;'>🔧 Diagnóstico</a><br>";

?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}

table {
    background-color: white;
    width: 100%;
    max-width: 800px;
}

th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #007bff;
    color: white;
}

.btn {
    border-radius: 5px;
    font-weight: bold;
}

.btn:hover {
    opacity: 0.8;
}
</style>
