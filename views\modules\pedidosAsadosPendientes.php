<?php

session_start();

if(!$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

// Verificar permisos (administradores y personal de asados)
if($_SESSION["tipo_usuario"] != 1 && $_SESSION["tipo_usuario"] != 6) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>Acceso Denegado</h4>";
    echo "<p>No tiene permisos para acceder a esta sección.</p>";
    echo "</div>";
    exit();
}

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Asados - Pedidos Pendientes</title>
    <style>
        .panel-danger .panel-heading {
            background-color: #d9534f !important;
            border-color: #d43f3a !important;
            color: white !important;
        }
        .panel-warning .panel-heading {
            background-color: #f0ad4e !important;
            border-color: #eea236 !important;
            color: white !important;
        }
        .panel-info .panel-heading {
            background-color: #dc3545 !important;
            border-color: #c82333 !important;
            color: white !important;
        }
        .tiempo-urgente {
            animation: parpadeo 1s infinite;
        }
        @keyframes parpadeo {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .panel-body {
            padding: 15px;
        }
        .btn-entregado {
            background-color: #dc3545;
            border-color: #c82333;
            color: white;
            font-weight: bold;
        }
        .btn-entregado:hover {
            background-color: #c82333;
            border-color: #bd2130;
            color: white;
        }
        .estadisticas-header {
            margin-bottom: 30px;
        }
        .pedido-item {
            border-left: 5px solid #dc3545;
            margin-bottom: 15px;
        }
        .pedido-urgente {
            border-left-color: #d9534f !important;
        }
        .pedido-warning {
            border-left-color: #f0ad4e !important;
        }
        .refresh-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        .asados-theme {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .tiempo-coccion {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
    <script>
        // Auto-refresh cada 45 segundos (asados toma más tiempo)
        setTimeout(function() {
            location.reload();
        }, 45000);
        
        // Sonido de notificación para pedidos urgentes
        function verificarPedidosUrgentes() {
            var pedidosUrgentes = document.querySelectorAll('.panel-danger');
            if (pedidosUrgentes.length > 0) {
                console.log('¡Hay ' + pedidosUrgentes.length + ' pedidos urgentes en asados!');
            }
        }
        
        window.onload = function() {
            verificarPedidosUrgentes();
        };
    </script>
</head>
<body>

<div class="refresh-btn">
    <button onclick="location.reload()" class="btn btn-danger btn-sm">
        <i class="glyphicon glyphicon-refresh"></i> Auto-refresh: 45s
    </button>
</div>

<?php

require_once "controllers/controllerPedidosCategorias.php";

$controller = new ControllerPedidosCategorias();

// Ya no procesamos formularios aquí, usamos AJAX

// Mostrar vista de pedidos pendientes
$controller->vistaPedidosPendientesController('asados');

?>

<script>
// Función para marcar pedido como entregado con AJAX
function marcarEntregado(pedidoId, elemento) {
    if (!confirm('¿Está seguro de marcar este pedido como entregado?')) {
        return;
    }

    // Deshabilitar el botón mientras se procesa
    var boton = elemento;
    var textoOriginal = boton.innerHTML;
    boton.disabled = true;
    boton.innerHTML = '<i class="glyphicon glyphicon-refresh glyphicon-spin"></i> Procesando...';

    // Realizar petición AJAX
    var xhr = new XMLHttpRequest();
    xhr.open('POST', 'views/modules/ajaxPedidosCategorias.php', true);
    xhr.setRequestHeader('Content-Type', 'application/json');

    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                try {
                    var response = JSON.parse(xhr.responseText);
                    if (response.success) {
                        // Animar la eliminación del pedido
                        var panelPedido = elemento.closest('.panel');
                        panelPedido.style.transition = 'all 0.5s ease';
                        panelPedido.style.opacity = '0';
                        panelPedido.style.transform = 'translateX(100%)';

                        setTimeout(function() {
                            panelPedido.remove();
                            actualizarContadores();
                            verificarSinPedidos();
                        }, 500);

                        // Mostrar notificación de éxito
                        mostrarNotificacion('Pedido marcado como entregado exitosamente', 'success');
                    } else {
                        throw new Error(response.message || 'Error desconocido');
                    }
                } catch (e) {
                    console.error('Error:', e);
                    alert('Error al procesar la respuesta: ' + e.message);
                    // Restaurar botón
                    boton.disabled = false;
                    boton.innerHTML = textoOriginal;
                }
            } else {
                alert('Error de conexión. Código: ' + xhr.status);
                // Restaurar botón
                boton.disabled = false;
                boton.innerHTML = textoOriginal;
            }
        }
    };

    xhr.send(JSON.stringify({
        accion: 'marcar_entregado',
        categoria: 'asados',
        pedido_id: pedidoId
    }));
}

// Función para actualizar contadores
function actualizarContadores() {
    var pendientes = document.querySelectorAll('.panel-default .panel-body .panel').length;

    // Actualizar título de la página
    if (pendientes > 0) {
        document.title = '(' + pendientes + ') Asados - Pedidos Pendientes';
    } else {
        document.title = 'Asados - Pedidos Pendientes';
    }

    // Actualizar título del panel
    var panelTitle = document.querySelector('.panel-default .panel-title');
    if (panelTitle) {
        panelTitle.textContent = '📋 Pedidos Pendientes - ' + pendientes + ' pedido(s)';
    }

    // Actualizar estadísticas si existen
    var estadPendientes = document.querySelector('.panel-primary .panel-body h2');
    if (estadPendientes) {
        estadPendientes.textContent = pendientes;
    }
}

// Función para verificar si no hay pedidos y mostrar mensaje
function verificarSinPedidos() {
    var pendientes = document.querySelectorAll('.panel-default .panel-body .panel').length;
    var panelBody = document.querySelector('.panel-default .panel-body');

    if (pendientes === 0 && panelBody) {
        panelBody.innerHTML = `
            <div class="alert alert-info text-center">
                <h3>🥩 ¡Excelente!</h3>
                <p>No hay pedidos pendientes en Asados.</p>
            </div>
        `;
    }
}

// Función para mostrar notificaciones
function mostrarNotificacion(mensaje, tipo) {
    var notificacion = document.createElement('div');
    notificacion.className = 'alert alert-' + tipo + ' alert-dismissible';
    notificacion.style.position = 'fixed';
    notificacion.style.top = '70px';
    notificacion.style.right = '20px';
    notificacion.style.zIndex = '9999';
    notificacion.style.minWidth = '300px';
    notificacion.innerHTML = `
        <button type="button" class="close" onclick="this.parentElement.remove()">
            <span>&times;</span>
        </button>
        ${mensaje}
    `;

    document.body.appendChild(notificacion);

    // Auto-eliminar después de 3 segundos
    setTimeout(function() {
        if (notificacion.parentElement) {
            notificacion.remove();
        }
    }, 3000);
}

// Prevenir envío de formularios (ahora usamos AJAX)
document.addEventListener('DOMContentLoaded', function() {
    var forms = document.querySelectorAll('form');
    forms.forEach(function(form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault(); // Prevenir envío normal del formulario
        });
    });

    actualizarContadores();
});
</script>

</body>
</html>
