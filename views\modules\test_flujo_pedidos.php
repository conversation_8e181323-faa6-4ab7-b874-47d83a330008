<?php
// Archivo de prueba para verificar el flujo completo de pedidos

echo "<h1>🧪 Prueba del Flujo de Pedidos</h1>";
echo "<hr>";

$mesaId = $_SESSION["mesa"] ?? 1;

try {
    require_once "controllers/controllerEstadoPedidos.php";
    require_once "models/crudEstadoPedidos.php";
    require_once "models/crudPedidoMesaVendido.php";
    
    $controllerEstado = new ControllerEstadoPedidos();
    
    echo "<h2>1. 📋 Verificar Pedido Borrador</h2>";
    $pedidoBorrador = $controllerEstado->obtenerPedidoBorradorController($mesaId);
    
    if ($pedidoBorrador) {
        echo "✅ Pedido borrador encontrado: " . $pedidoBorrador['numero_pedido'] . "<br>";
        echo "📅 Fecha: " . $pedidoBorrador['fecha_pedido'] . "<br>";
    } else {
        echo "❌ No se pudo obtener/crear pedido borrador<br>";
    }
    
    echo "<h2>2. 🛒 Productos en la Mesa</h2>";
    $productos = DatosPedidoMesaVendido::obtenerProductosEntregadosMesaModel($mesaId);
    
    if (!empty($productos)) {
        echo "✅ Productos encontrados: " . count($productos) . "<br>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Producto</th><th>Cantidad</th><th>Precio</th><th>Estado Pedido</th></tr>";
        
        $total = 0;
        foreach ($productos as $producto) {
            $subtotal = $producto['cantidadpvm'] * $producto['preciopr'];
            $total += $subtotal;
            echo "<tr>";
            echo "<td>" . $producto['nombrepr'] . "</td>";
            echo "<td>" . $producto['cantidadpvm'] . "</td>";
            echo "<td>$" . number_format($producto['preciopr']) . "</td>";
            echo "<td>" . $producto['estado'] . "</td>";
            echo "</tr>";
        }
        echo "<tr><td colspan='3'><strong>Total</strong></td><td><strong>$" . number_format($total) . "</strong></td></tr>";
        echo "</table>";
    } else {
        echo "⚠️ No hay productos en la mesa<br>";
    }
    
    echo "<h2>3. 📊 Estado de Pedidos en la Mesa</h2>";
    $pedidos = $controllerEstado->obtenerPedidosMesaController($mesaId);
    
    if (!empty($pedidos)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Número</th><th>Estado</th><th>Fecha Pedido</th><th>Productos</th></tr>";
        
        foreach ($pedidos as $pedido) {
            echo "<tr>";
            echo "<td>" . $pedido['numero_pedido'] . "</td>";
            echo "<td>" . $pedido['estado'] . "</td>";
            echo "<td>" . $pedido['fecha_pedido'] . "</td>";
            echo "<td>" . $pedido['total_productos'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "⚠️ No hay pedidos en la mesa<br>";
    }
    
    echo "<h2>4. 🔧 Acciones de Prueba</h2>";
    
    if ($pedidoBorrador) {
        echo "<div style='margin: 10px 0;'>";
        echo "<button onclick='enviarPedido(" . $pedidoBorrador['id'] . ")' style='background: #28a745; color: white; padding: 10px; border: none; margin: 5px;'>📤 Enviar Pedido a Cocina</button>";
        echo "</div>";
    }
    
    // Buscar pedidos enviados para poder entregarlos
    $pedidosEnviados = $controllerEstado->obtenerPedidosMesaController($mesaId, 'enviado');
    if (!empty($pedidosEnviados)) {
        foreach ($pedidosEnviados as $pedido) {
            echo "<div style='margin: 10px 0;'>";
            echo "<button onclick='entregarPedido(" . $pedido['id'] . ")' style='background: #007bff; color: white; padding: 10px; border: none; margin: 5px;'>✅ Marcar como Entregado: " . $pedido['numero_pedido'] . "</button>";
            echo "</div>";
        }
    }
    
    echo "<h2>5. 💰 Simulación de Facturación</h2>";
    $productosFacturacion = DatosPedidoMesaVendido::obtenerProductosEntregadosMesaModel($mesaId);
    if (!empty($productosFacturacion)) {
        $totalFacturacion = 0;
        foreach ($productosFacturacion as $prod) {
            $totalFacturacion += $prod['cantidadpvm'] * $prod['preciopr'];
        }
        echo "💵 Total a facturar: $" . number_format($totalFacturacion) . "<br>";
        echo "<button onclick='alert(\"Funcionalidad de facturación lista\")' style='background: #ffc107; color: black; padding: 10px; border: none; margin: 5px;'>🧾 Simular Facturación</button>";
    } else {
        echo "⚠️ No hay productos para facturar<br>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px 0;'>";
    echo "❌ Error: " . $e->getMessage();
    echo "</div>";
}

echo "<h2>6. 🔗 Enlaces Útiles</h2>";
echo "<a href='index.php?action=registroPmesa&ida=" . $mesaId . "' style='background: #007bff; color: white; padding: 10px; text-decoration: none; margin: 5px; display: inline-block;'>🔗 Ir a Mesa " . $mesaId . "</a><br>";
echo "<a href='index.php?action=debug_mesa' style='background: #6c757d; color: white; padding: 10px; text-decoration: none; margin: 5px; display: inline-block;'>🔍 Debug Mesa</a><br>";
echo "<a href='index.php?action=pantallaCocina&categoria=cocina' style='background: #28a745; color: white; padding: 10px; text-decoration: none; margin: 5px; display: inline-block;'>🍳 Pantalla Cocina</a><br>";
echo "<a href='index.php?action=diagnostico' style='background: #ffc107; color: black; padding: 10px; text-decoration: none; margin: 5px; display: inline-block;'>🔧 Diagnóstico</a><br>";

?>

<script>
function enviarPedido(pedidoId) {
    if (confirm('¿Enviar pedido a cocina?')) {
        fetch('views/modules/ajaxEstadoPedidos.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'enviar_pedido=true&pedido_id=' + pedidoId
        })
        .then(response => response.json())
        .then(data => {
            alert(data.message);
            location.reload();
        })
        .catch(error => {
            alert('Error: ' + error);
        });
    }
}

function entregarPedido(pedidoId) {
    if (confirm('¿Marcar pedido como entregado?')) {
        fetch('views/modules/ajaxEstadoPedidos.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'entregar_pedido=true&pedido_id=' + pedidoId
        })
        .then(response => response.json())
        .then(data => {
            alert(data.message);
            location.reload();
        })
        .catch(error => {
            alert('Error: ' + error);
        });
    }
}
</script>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}

table {
    background-color: white;
    width: 100%;
    max-width: 600px;
}

th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #007bff;
    color: white;
}

button {
    cursor: pointer;
    border-radius: 5px;
    font-weight: bold;
}

button:hover {
    opacity: 0.8;
}
</style>
