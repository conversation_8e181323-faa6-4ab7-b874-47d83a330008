<?php
// Test de envío de pedidos con productos reales
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
if (!isset($_SESSION["usuario"])) {
    $_SESSION["usuario"] = 1;
    $_SESSION["tipo_usuario"] = 1;
    $_SESSION["perfil"] = "administrador";
}

try {
    require_once "../../models/conexion.php";
    require_once "../../controllers/controllerEstadoPedidos.php";
    require_once "../../models/crudEstadoPedidos.php";
} catch (Exception $e) {
    echo "Error cargando archivos: " . $e->getMessage();
    exit;
}

$mesaId = isset($_GET['mesa']) ? $_GET['mesa'] : 1;

echo "<h1>🧪 Test Envío con Productos Reales - Mesa $mesaId</h1>";

try {
    $controller = new ControllerEstadoPedidos();
    $db = Conexion::conectar();

    echo "<h3>1. 📋 Estado Actual de la Mesa</h3>";
    $pedidosActuales = $controller->obtenerPedidosMesaController($mesaId);
    echo "Pedidos activos: " . count($pedidosActuales) . "<br>";

    if (!empty($pedidosActuales)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Número</th><th>Estado</th><th>Productos</th></tr>";
        foreach ($pedidosActuales as $p) {
            echo "<tr>";
            echo "<td>{$p['id']}</td>";
            echo "<td>{$p['numero_pedido']}</td>";
            echo "<td>{$p['estado']}</td>";
            echo "<td>{$p['total_productos']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }

    echo "<h3>2. 🔍 Buscar Pedido Borrador</h3>";
    $pedidoBorrador = $controller->obtenerPedidoBorradorController($mesaId);

    if ($pedidoBorrador) {
        echo "✅ Pedido borrador encontrado:<br>";
        echo "- ID: {$pedidoBorrador['id']}<br>";
        echo "- Número: {$pedidoBorrador['numero_pedido']}<br>";
        echo "- Estado: {$pedidoBorrador['estado']}<br>";

        // Verificar productos en el pedido
        $productos = DatosEstadoPedidos::obtenerProductosPedidoModel($pedidoBorrador['id']);
        echo "- Productos: " . count($productos) . "<br>";

        if (count($productos) > 0) {
            echo "<h3>3. 📤 Enviar Pedido</h3>";
            echo "Enviando pedido ID: {$pedidoBorrador['id']}<br>";

            $resultado = $controller->enviarPedidoController($pedidoBorrador['id']);
            echo "Resultado: " . print_r($resultado, true) . "<br>";

            if ($resultado['status'] == 'success') {
                echo "✅ Pedido enviado exitosamente<br>";

                echo "<h3>4. 🔄 Verificar Nuevo Estado</h3>";
                sleep(1); // Esperar un momento

                $nuevoBorrador = $controller->obtenerPedidoBorradorController($mesaId);
                if ($nuevoBorrador && $nuevoBorrador['id'] != $pedidoBorrador['id']) {
                    echo "✅ Nuevo pedido borrador creado: ID {$nuevoBorrador['id']}<br>";
                } else {
                    echo "❌ No se creó nuevo pedido borrador automáticamente<br>";
                }
            }
        } else {
            echo "⚠️ El pedido no tiene productos. Agregue productos manualmente y vuelva a probar.<br>";
        }
    } else {
        echo "❌ No se pudo crear/obtener pedido borrador<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error en el test: " . $e->getMessage() . "<br>";
    echo "Stack trace: " . $e->getTraceAsString() . "<br>";
}

echo "<br><a href='index.php?action=registroPmesa&ida=$mesaId' style='background: #007bff; color: white; padding: 10px; text-decoration: none;'>🔙 Volver a Mesa $mesaId</a>";
echo "<br><a href='test_envio_con_productos.php?mesa=$mesaId' style='background: #28a745; color: white; padding: 10px; text-decoration: none; margin: 5px;'>🔄 Repetir Test</a>";
?>
