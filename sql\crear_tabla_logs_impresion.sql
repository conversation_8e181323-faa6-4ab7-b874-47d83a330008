-- Crear tabla para logs de impresión por categorías
CREATE TABLE IF NOT EXISTS logs_impresion_pedidos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    pedido_id INT NOT NULL,
    categoria VARCHAR(50) NOT NULL,
    metodo VARCHAR(100) NOT NULL DEFAULT 'ventana_impresion',
    success BOOLEAN NOT NULL DEFAULT TRUE,
    ip_cliente VARCHAR(45),
    datos_enviados TEXT,
    fecha_hora TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_pedido_categoria (pedido_id, categoria),
    INDEX idx_fecha (fecha_hora)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Comentarios para documentación
ALTER TABLE logs_impresion_pedidos 
COMMENT = 'Logs de impresión por categorías para pedidos del restaurante';

ALTER TABLE logs_impresion_pedidos 
MODIFY COLUMN pedido_id INT NOT NULL COMMENT 'ID del pedido impreso',
MODIFY COLUMN categoria VARCHAR(50) NOT NULL COMMENT 'Categoría impresa: bar, cocina, asados',
MODIFY COLUMN metodo VARCHAR(100) NOT NULL DEFAULT 'ventana_impresion' COMMENT 'Método de impresión utilizado',
MODIFY COLUMN success BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Si la impresión fue exitosa',
MODIFY COLUMN ip_cliente VARCHAR(45) COMMENT 'IP del dispositivo que solicitó la impresión',
MODIFY COLUMN datos_enviados TEXT COMMENT 'Contenido enviado a imprimir (opcional)',
MODIFY COLUMN fecha_hora TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Fecha y hora de la impresión';
