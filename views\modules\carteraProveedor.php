<?php
#session_start();
if(!$_SESSION["validar"])
	{	header("location:ingresar");	exit();	}
#ciudad_id, roles_id, cedula, nombre, apellidos, fecha_nacimiento, direccion, telefono, email, fecha_registro, activo, usuario, clave, pregunta,respuesta echo $_SESSION["persona"];
?>
<div class="table-responsive">
<h1>	DEUDAS A PROVEEDORES</h1>
	<table border="1" class="table table-hover" align="center">
		<thead>
			<tr>
				<th>No </th>
				<th>Proveedor</th>
				<th>NIT</th>
				<th>Valor deuda </th>			
				<th> </th>	
			</tr>
		</thead>
		<tbody>
			<?php
			$cliente = new controllerAbonosProveedor();
			$cliente -> deudaTotalController();
			//$cliente -> proveedorController();
			?>
		</tbody>
	</table>
</div>
<?php
/*if(isset($_GET["action"]))
{

	if($_GET["action"] == "cambio")
		{	echo "Cambio Exitoso";	}

}*/
//set_time_limit(0);
//phpinfo();

?>




