<?php
require_once "../../models/crud.php";
require_once "../../models/crudSuministro.php";
require_once "../../controllers/controller.php";
require_once "../../controllers/controllerSuministro.php";
//echo'<br> <script>alert("Entro al ajax Buscar Suministros '.$_POST['placa'].'");</script> <br>';
if(isset($_POST['keyword'])) 
 {
  $queryString = $_POST['keyword'];
  //$genero = $_POST['genero'];
  //$cont=0;
  //echo'<br> <script>alert("Entro al ajax");</script> <br>';
  $ajax=new controllerSuministro();
  $r=$ajax->ajaxBuscarSuministroController($queryString);
  if ($r>0)
	{
	 ?>
	 <ul id="country-list">
	 <?php
	 foreach($r as $country) 
		{
		 ?>
		 <li onClick="selectCountry('<?php echo $country["snombre"]; ?>');"><?php echo $country["snombre"]; ?></li>
		 <?php } ?>
		 </ul>
		 <?php 
		}
  else
	{	echo' <h3>No se encontraron Resultados</h3>';	} } ?>
?>