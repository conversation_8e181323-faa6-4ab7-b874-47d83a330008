<?php	

	if(isset($_GET["action"]))
		{	if($_GET["action"] == "okPm")
			{echo "Registro Exitoso";	}
		}
	$registroP = new controllerPedido();
	$registroP -> registroPedidoController();
	$mesero_idPedidoRegistro = $registroP -> registroPedidoController();

?>


<h1>REGISTRO DE PEDIDO </h1>

<form method="post">

	<label> Nombre Mesero : </label>
	<!--<?php /*
		# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  Roles  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
			if($mesero_idPedidoRegistro=="error")
				{	echo "debe registrar el Mesero"; }
			else{
					echo "<label>Id Mesero </label>";
					$resultt='<select name="mesero_idPedidoRegistro"  id="mesero_idPedidoRegistro">';
					$resultt.=' <option value="-1">Seleccione una mesero_idPedidoRegistro</option>';
					foreach ($mesero_idPedidoRegistro as $row => $item)
					 	{	$resultt.=' <option value="'.$item["pid"].'">'.$item["pnombre"].'</option>';	}	
					 $resultt.='</select>';
					 echo $resultt." <br>";
				}
		# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  End Roles  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
 	*/?>-->
	<input type="text" placeholder="Mesero" name="mesero_idPedidoRegistro" required><br> 	
	<label> factura : </label>
	<input type="text" placeholder="No factura" name="facturadoPedidoRegistro" value="n"><br>	
	
	<input type="submit" value="Enviar">

</form>

<?php 

	$registroP = new controllerPedido();
	$registroP -> registroPedidoController();

?>