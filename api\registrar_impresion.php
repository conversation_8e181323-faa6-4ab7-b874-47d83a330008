<?php
// API para registrar logs de impresión
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Método no permitido']);
    exit;
}

$input = file_get_contents('php://input');
$data = json_decode($input, true);

if (!$data || !isset($data['pedido_id']) || !isset($data['categoria'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Datos incompletos']);
    exit;
}

try {
    require_once '../models/conexion.php';
    require_once '../models/crudPedidoImpresion.php';
    
    $pedido_id = intval($data['pedido_id']);
    $categoria = $data['categoria'];
    $metodo = $data['metodo'] ?? 'manual';
    $ip_cliente = $_SERVER['REMOTE_ADDR'];
    
    PedidoImpresion::registrarLogImpresion($pedido_id, $categoria, $metodo, true, $ip_cliente);
    
    echo json_encode([
        'success' => true,
        'pedido_id' => $pedido_id,
        'categoria' => $categoria,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Error interno: ' . $e->getMessage()
    ]);
}
?>
