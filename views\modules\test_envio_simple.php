<?php
// Test simple para envío de pedidos sin impresión
require_once "../../models/conexion.php";
require_once "../../models/crudEstadoPedidos.php";
require_once "../../controllers/controllerEstadoPedidos.php";

// Iniciar sesión para testing
session_start();
if (!isset($_SESSION["usuario"])) {
    $_SESSION["usuario"] = 1;
    $_SESSION["tipo_usuario"] = 1;
    $_SESSION["perfil"] = "administrador";
}

echo "<h1>🧪 Test Simple de Envío de Pedidos</h1>";

// Obtener pedidos borrador
$controller = new ControllerEstadoPedidos();

try {
    $stmt = Conexion::conectar()->prepare("
        SELECT p.id, p.numero_pedido, p.mesa_id, p.estado, m.numero as mesa_numero
        FROM pedidos p 
        LEFT JOIN mesas m ON p.mesa_id = m.id 
        WHERE p.estado = 'borrador' 
        ORDER BY p.id DESC 
        LIMIT 5
    ");
    $stmt->execute();
    $pedidosBorrador = $stmt->fetchAll();
    
    echo "<h3>📋 Pedidos Borrador Disponibles:</h3>";
    if (!empty($pedidosBorrador)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'><th>ID</th><th>Número</th><th>Mesa</th><th>Estado</th><th>Acción</th></tr>";
        foreach ($pedidosBorrador as $pedido) {
            echo "<tr>";
            echo "<td>" . $pedido['id'] . "</td>";
            echo "<td>" . $pedido['numero_pedido'] . "</td>";
            echo "<td>Mesa " . $pedido['mesa_numero'] . "</td>";
            echo "<td>" . $pedido['estado'] . "</td>";
            echo "<td><button onclick='enviarPedidoSimple(" . $pedido['id'] . ")' style='background: #28a745; color: white; padding: 8px; border: none; cursor: pointer;'>📤 Enviar</button></td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "❌ No hay pedidos borrador disponibles.<br>";
        echo "<button onclick='crearPedidoPrueba()' style='background: #007bff; color: white; padding: 10px; border: none; cursor: pointer;'>➕ Crear Pedido de Prueba</button>";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage();
}

echo "<h3>📊 Log de Actividad:</h3>";
echo "<div id='log' style='background: #f8f9fa; padding: 10px; border: 1px solid #ddd; height: 300px; overflow-y: scroll; font-family: monospace; font-size: 12px;'></div>";

echo "<br><a href='index.php?action=test_ajax' style='background: #6c757d; color: white; padding: 10px; text-decoration: none;'>🔙 Volver al Test Completo</a>";
?>

<script>
function log(message) {
    const logDiv = document.getElementById('log');
    const timestamp = new Date().toLocaleTimeString();
    logDiv.innerHTML += `[${timestamp}] ${message}\n`;
    logDiv.scrollTop = logDiv.scrollHeight;
}

function enviarPedidoSimple(pedidoId) {
    log(`📤 Iniciando envío de pedido ${pedidoId}...`);
    
    fetch('ajaxEstadoPedidos.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'enviar_pedido=true&pedido_id=' + pedidoId
    })
    .then(response => {
        log(`📡 Respuesta HTTP recibida, status: ${response.status}`);
        return response.text();
    })
    .then(text => {
        log(`📄 Respuesta como texto: ${text}`);
        try {
            const data = JSON.parse(text);
            log(`✅ JSON parseado exitosamente: ${JSON.stringify(data)}`);
            
            if (data.status === 'success') {
                log(`🎉 ¡Pedido enviado exitosamente!`);
                alert('✅ Pedido enviado correctamente');
                setTimeout(() => location.reload(), 2000);
            } else {
                log(`⚠️ Error en el envío: ${data.message}`);
                alert('❌ Error: ' + data.message);
            }
        } catch (e) {
            log(`❌ Error parseando JSON: ${e.message}`);
            log(`📄 Texto recibido: ${text}`);
            alert('❌ Error: Respuesta no válida del servidor');
        }
    })
    .catch(error => {
        log(`❌ Error de red: ${error.message}`);
        alert('❌ Error de conexión: ' + error.message);
    });
}

function crearPedidoPrueba() {
    log('📝 Creando pedido de prueba...');
    
    fetch('../../index.php?action=crear_datos_prueba', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'crear_pedido_borrador=true'
    })
    .then(response => {
        log('📡 Pedido de prueba creado');
        setTimeout(() => location.reload(), 2000);
    })
    .catch(error => {
        log('❌ Error creando pedido: ' + error.message);
    });
}

// Log inicial
document.addEventListener('DOMContentLoaded', function() {
    log('🚀 Sistema de test cargado');
    log('📋 Listo para enviar pedidos');
});
</script>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}

table {
    width: 100%;
    max-width: 800px;
}

th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
}

button:hover {
    opacity: 0.8;
}
</style>
