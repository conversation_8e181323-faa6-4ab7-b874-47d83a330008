<?php
// Test de conectividad de red desde el servidor
echo "<h1>🌐 Test de Red del Servidor</h1>";
echo "<p><strong>Fecha/Hora:</strong> " . date('Y-m-d H:i:s') . "</p>";

// Información del servidor
echo "<h2>🖥️ Información del Servidor:</h2>";
echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<ul>";
echo "<li><strong>IP del Servidor:</strong> " . $_SERVER['SERVER_ADDR'] . "</li>";
echo "<li><strong>Servidor Web:</strong> " . $_SERVER['SERVER_SOFTWARE'] . "</li>";
echo "<li><strong>PHP Version:</strong> " . phpversion() . "</li>";
echo "<li><strong>Host:</strong> " . $_SERVER['HTTP_HOST'] . "</li>";
echo "<li><strong>User Agent:</strong> " . $_SERVER['HTTP_USER_AGENT'] . "</li>";
echo "</ul>";
echo "</div>";

// Test de conectividad a diferentes IPs
echo "<h2>🔍 Test de Conectividad desde el Servidor:</h2>";

$ips_test = [
    '**************' => 'COCINA (confirmada por ping)',
    '**************' => 'BAR (configurada)',
    '**************' => 'ASADOS (configurada)',
    '**************' => 'Tu computadora',
    '*******' => 'Google DNS (test internet)',
    '************' => 'Gateway/Router probable'
];

foreach ($ips_test as $ip => $descripcion) {
    echo "<div style='border: 1px solid #ccc; padding: 15px; margin: 10px 0;'>";
    echo "<h3>📡 Test: $ip ($descripcion)</h3>";
    
    // Test puerto 9100 (impresoras)
    if (strpos($ip, '192.168.68.') === 0 && $ip !== '************' && $ip !== '**************') {
        echo "<h4>Puerto 9100 (Impresora):</h4>";
        $start_time = microtime(true);
        $fp = @fsockopen($ip, 9100, $errno, $errstr, 10);
        $end_time = microtime(true);
        $tiempo = round(($end_time - $start_time) * 1000, 2);
        
        if ($fp) {
            echo "<p>✅ <strong>CONECTADA</strong> - Tiempo: {$tiempo}ms</p>";
            fclose($fp);
        } else {
            echo "<p>❌ <strong>NO CONECTADA</strong> - Error: $errstr ($errno) - Tiempo: {$tiempo}ms</p>";
        }
    }
    
    // Test puerto 80 (web)
    echo "<h4>Puerto 80 (HTTP):</h4>";
    $start_time = microtime(true);
    $fp = @fsockopen($ip, 80, $errno, $errstr, 5);
    $end_time = microtime(true);
    $tiempo = round(($end_time - $start_time) * 1000, 2);
    
    if ($fp) {
        echo "<p>✅ <strong>CONECTADA</strong> - Tiempo: {$tiempo}ms</p>";
        fclose($fp);
    } else {
        echo "<p>❌ <strong>NO CONECTADA</strong> - Error: $errstr ($errno) - Tiempo: {$tiempo}ms</p>";
    }
    
    // Test ping simulado (puerto 443)
    echo "<h4>Puerto 443 (HTTPS):</h4>";
    $start_time = microtime(true);
    $fp = @fsockopen($ip, 443, $errno, $errstr, 5);
    $end_time = microtime(true);
    $tiempo = round(($end_time - $start_time) * 1000, 2);
    
    if ($fp) {
        echo "<p>✅ <strong>CONECTADA</strong> - Tiempo: {$tiempo}ms</p>";
        fclose($fp);
    } else {
        echo "<p>❌ <strong>NO CONECTADA</strong> - Error: $errstr ($errno) - Tiempo: {$tiempo}ms</p>";
    }
    
    echo "</div>";
}

// Test de resolución DNS
echo "<h2>🌍 Test de Resolución DNS:</h2>";
echo "<div style='border: 1px solid #ccc; padding: 15px; margin: 10px 0;'>";

$dominios = ['google.com', 'macarena.anconclub.com', 'anconclub.com'];
foreach ($dominios as $dominio) {
    $ip_resuelto = gethostbyname($dominio);
    if ($ip_resuelto !== $dominio) {
        echo "<p>✅ <strong>$dominio</strong> → $ip_resuelto</p>";
    } else {
        echo "<p>❌ <strong>$dominio</strong> → No se pudo resolver</p>";
    }
}
echo "</div>";

// Información de red del servidor
echo "<h2>🔧 Configuración de Red del Servidor:</h2>";
echo "<div style='background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";

// Intentar obtener información de red
if (function_exists('exec')) {
    echo "<h4>Comando ifconfig/ipconfig (si está disponible):</h4>";
    $output = [];
    $return_var = 0;
    
    // Intentar diferentes comandos según el sistema
    @exec('ifconfig 2>/dev/null', $output, $return_var);
    if (empty($output)) {
        @exec('ipconfig 2>nul', $output, $return_var);
    }
    if (empty($output)) {
        @exec('ip addr show 2>/dev/null', $output, $return_var);
    }
    
    if (!empty($output)) {
        echo "<pre style='background-color: #f8f9fa; padding: 10px; border-radius: 3px; max-height: 300px; overflow-y: auto;'>";
        echo htmlspecialchars(implode("\n", array_slice($output, 0, 20))); // Mostrar solo las primeras 20 líneas
        echo "</pre>";
    } else {
        echo "<p>⚠️ No se pudo ejecutar comandos de red del sistema</p>";
    }
} else {
    echo "<p>⚠️ Función exec() no disponible</p>";
}

echo "</div>";

// Diagnóstico y recomendaciones
echo "<h2>🔍 Diagnóstico:</h2>";
echo "<div style='background-color: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 10px 0;'>";
echo "<h4>Análisis de la situación:</h4>";
echo "<ul>";
echo "<li>✅ <strong>Tu computadora (**************)</strong> puede hacer ping a las impresoras</li>";
echo "<li>❌ <strong>El servidor web</strong> no puede conectarse a las impresoras en puerto 9100</li>";
echo "<li>🔍 <strong>Posibles causas:</strong></li>";
echo "<ul>";
echo "<li><strong>Firewall del servidor:</strong> Bloquea conexiones salientes al puerto 9100</li>";
echo "<li><strong>Red diferente:</strong> El servidor está en una red diferente a las impresoras</li>";
echo "<li><strong>Restricciones del hosting:</strong> El proveedor bloquea conexiones TCP a puertos no estándar</li>";
echo "<li><strong>Configuración de red:</strong> Problemas de routing entre el servidor y la red local</li>";
echo "</ul>";
echo "</ul>";

echo "<h4>💡 Soluciones recomendadas:</h4>";
echo "<ol>";
echo "<li><strong>Verificar la IP del servidor:</strong> ¿Está en la misma red 192.168.68.x?</li>";
echo "<li><strong>Contactar al administrador del servidor/hosting</strong> para:</li>";
echo "<ul>";
echo "<li>Verificar si hay firewall bloqueando puerto 9100</li>";
echo "<li>Confirmar si se permiten conexiones TCP salientes</li>";
echo "<li>Revisar la configuración de red del servidor</li>";
echo "</ul>";
echo "<li><strong>Alternativa temporal:</strong> Configurar un proxy/bridge en tu computadora</li>";
echo "<li><strong>Verificar VPN/Túnel:</strong> Si el servidor está en la nube, puede necesitar VPN</li>";
echo "</ol>";
echo "</div>";

// Botón para volver
echo "<p><a href='actualizar_impresoras.php' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🔧 Actualizar Impresoras</a>";
echo "<a href='diagnostico' style='background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>← Volver al Diagnóstico</a></p>";
?>
