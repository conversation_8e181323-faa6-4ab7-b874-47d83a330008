<?php

#EXTENSIÓN DE CLASES: Los objetos pueden ser extendidos, y pueden heredar propiedades y métodos. Para definir una clase como extensión, debo definir una clase padre, y se utiliza dentro de una clase hija.
require_once "conexion.php";
class DatosProveedor extends Conexion
 {
	#REGISTRO DE PROVEEDOR
	#-------------------------------------
	 public static function registroProveedorModel($datosModel, $tabla)
		{	echo "<script>alert('Entro CRUD ".$datosModel["nombre"]." es')</script>";	
			$consulta="INSERT INTO $tabla (nombre, nit, representante, direccion, telefonos) VALUES (:nombre, :nit, :representante, :direccion, :telefonos)";
			//echo "<script>alert('Entro CRUD ".$consulta." no')</script>";	
			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->execute();			
			$stmt->bindParam(":nombre", $datosModel["nombre"], PDO::PARAM_STR);
			$stmt->bindParam(":nit", $datosModel["nit"], PDO::PARAM_INT);
			$stmt->bindParam(":representante", $datosModel["representante"], PDO::PARAM_STR);
			$stmt->bindParam(":direccion", $datosModel["direccion"], PDO::PARAM_STR);			
			$stmt->bindParam(":telefonos", $datosModel["telefonos"], PDO::PARAM_STR);
			echo "<script>alert('Guardo')</script>";
			if($stmt->execute())
				{	return "success";	}
			else{ return "error";	}
			$stmt->close();
		}
	#-------------------------------------
	#VISTA PROVEEDOR
	#-------------------------------------
	 public static function vistaProveedorModel($tabla)
		{
			$stmt = Conexion::conectar()->prepare("SELECT id, nombre, nit, representante, direccion, telefonos FROM $tabla");	
			$stmt->execute();				
			return $stmt->fetchAll();
			$stmt->close();
		}
	#-------------------------------------

	#EDITAR PROVEEDOR
	#-------------------------------------
	 public static function editarProveedorModel($datosModel, $tabla)
		{
			$stmt = Conexion::conectar()->prepare("SELECT id, nombre, nit, representante, direccion, telefonos FROM $tabla WHERE id = :id");
			$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);	
			$stmt->execute();
			return $stmt->fetch();
			$stmt->close();
		}
	#-------------------------------------

	#ACTUALIZAR PROVEEDOR
	#-------------------------------------
	 public static function actualizarProveedoroModel($datosModel, $tabla)
		{
			echo "<script>alert('entro cruz Suministro')</script>";
			$stmt = Conexion::conectar()->prepare("UPDATE $tabla SET nombre = :nombre, nit= :nit, representante =:representante, direccion = :direccion, telefonos=:telefonos WHERE id = :id");			
			$stmt->bindParam(":nombre", $datosModel["nombre"], PDO::PARAM_STR);
			$stmt->bindParam(":nit", $datosModel["nit"], PDO::PARAM_INT);
			$stmt->bindParam(":representante", $datosModel["representante"], PDO::PARAM_STR);
			$stmt->bindParam(":direccion", $datosModel["direccion"], PDO::PARAM_STR);				
			$stmt->bindParam(":telefonos", $datosModel["telefonos"], PDO::PARAM_STR);
			$stmt->bindParam(":id", $datosModel["id"], PDO::PARAM_INT);

			echo "<script>alert('centro proceso')</script>";
			if($stmt->execute())
				{echo "<script>alert('Guardo Actualizar Proveedor')</script>";return "success";	}
			else{echo "<script>alert('Error base de dato serve')</script>";	return "error";			}
			$stmt->close();
		}
	#-------------------------------------


	#BORRAR PROVEEDOR
	#------------------------------------
	 public static function borrarProveedorModel($datosModel, $tabla)
		{
			$stmt = Conexion::conectar()->prepare("DELETE FROM $tabla WHERE id = :id");
			$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);
			if($stmt->execute())
				{	return "success";	}
			else{	return "error";		}
			$stmt->close();
		}
	#----------------------------------------------	
 }