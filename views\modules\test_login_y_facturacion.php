<!DOCTYPE html>
<html>
<head>
    <title>Test Login y Facturación Rápida</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .btn { padding: 10px 20px; margin: 5px; text-decoration: none; border-radius: 5px; display: inline-block; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
    </style>
</head>
<body>

<h2>🧪 Test Completo: Login + Facturación Rápida</h2>

<div class="test-section">
    <h3>📋 Instrucciones:</h3>
    <ol>
        <li>Primero inicia sesión en el sistema</li>
        <li>Luego prueba las rutas de facturación rápida</li>
        <li>Verifica que todo funcione correctamente</li>
    </ol>
</div>

<div class="test-section">
    <h3>🔐 Paso 1: Iniciar Sesión</h3>
    <p>Haz clic aquí para ir a la página de login:</p>
    <a href="../../index.php?action=ingresar" class="btn btn-primary" target="_blank">
        🔐 Ir a Login
    </a>
    <p><small>Usa tus credenciales normales del sistema</small></p>
</div>

<div class="test-section">
    <h3>🚀 Paso 2: Probar Facturación Rápida</h3>
    <p>Después de iniciar sesión, prueba estas rutas:</p>
    
    <h4>Opción A: Ruta del Sistema (Recomendada)</h4>
    <a href="../../index.php?action=facturacionRapida" class="btn btn-success" target="_blank">
        🌐 index.php?action=facturacionRapida
    </a>
    
    <h4>Opción B: URL Amigable</h4>
    <a href="../../facturacionRapida" class="btn btn-success" target="_blank">
        🚀 /facturacionRapida
    </a>
    
    <h4>Opción C: Desde el Menú</h4>
    <p>Ve a MESAS y busca el botón "🚀 FACTURACIÓN RÁPIDA" en el menú superior</p>
    <a href="../../index.php?action=mesa" class="btn btn-warning" target="_blank">
        🏠 Ir a Mesas
    </a>
</div>

<div class="test-section">
    <h3>🔍 Verificación de Estado</h3>
    <p>Verifica que aparezcan estos elementos:</p>
    <ul>
        <li>✅ Campo de búsqueda de productos</li>
        <li>✅ Lista de productos seleccionados</li>
        <li>✅ Campos de forma de pago (efectivo, tarjeta, etc.)</li>
        <li>✅ Botón "FACTURAR RÁPIDO"</li>
        <li>✅ Cálculo automático de totales</li>
    </ul>
</div>

<div class="test-section">
    <h3>🧪 Test de Funcionalidad</h3>
    <p>Una vez en la facturación rápida, prueba:</p>
    <ol>
        <li><strong>Buscar un producto:</strong> Escribe en el campo de búsqueda</li>
        <li><strong>Seleccionar producto:</strong> Haz clic en un producto de la lista</li>
        <li><strong>Verificar cálculos:</strong> Que se actualicen los totales</li>
        <li><strong>Ingresar pago:</strong> Pon un monto en efectivo</li>
        <li><strong>Facturar:</strong> Haz clic en "FACTURAR RÁPIDO"</li>
        <li><strong>Verificar PDF:</strong> Que se abra automáticamente para imprimir</li>
    </ol>
</div>

<div class="test-section">
    <h3>🐛 Solución de Problemas</h3>
    <p>Si algo no funciona:</p>
    
    <h4>Problema: "Sesión no válida"</h4>
    <ul>
        <li>Asegúrate de haber iniciado sesión correctamente</li>
        <li>Verifica que tu usuario tenga permisos</li>
        <li>Intenta cerrar sesión y volver a entrar</li>
    </ul>
    
    <h4>Problema: "Página no encontrada"</h4>
    <ul>
        <li>Verifica que el archivo facturacionRapida.php exista</li>
        <li>Prueba la ruta directa: views/modules/facturacionRapida.php</li>
        <li>Verifica la configuración del .htaccess</li>
    </ul>
    
    <h4>Problema: "No aparecen productos"</h4>
    <ul>
        <li>Verifica que haya productos en la base de datos</li>
        <li>Revisa la conexión a la base de datos</li>
        <li>Verifica los archivos AJAX</li>
    </ul>
</div>

<div class="test-section">
    <h3>📞 Acceso Directo de Emergencia</h3>
    <p>Si las rutas del sistema no funcionan, usa este acceso directo:</p>
    <a href="facturacionRapida.php" class="btn btn-warning" target="_blank">
        ⚡ Acceso Directo (facturacionRapida.php)
    </a>
    <p><small>Nota: Debes tener sesión iniciada para que funcione</small></p>
</div>

<script>
// Verificar si hay sesión activa
function verificarSesion() {
    fetch('../../index.php?action=mesa')
        .then(response => {
            if (response.url.includes('ingresar')) {
                document.getElementById('estado-sesion').innerHTML = 
                    '<span class="error">❌ No hay sesión activa</span>';
            } else {
                document.getElementById('estado-sesion').innerHTML = 
                    '<span class="success">✅ Sesión activa</span>';
            }
        })
        .catch(error => {
            document.getElementById('estado-sesion').innerHTML = 
                '<span class="warning">⚠️ No se pudo verificar</span>';
        });
}

// Verificar al cargar la página
window.onload = function() {
    verificarSesion();
};
</script>

<div class="test-section">
    <h3>📊 Estado de Sesión</h3>
    <p>Estado actual: <span id="estado-sesion">🔄 Verificando...</span></p>
</div>

</body>
</html>
