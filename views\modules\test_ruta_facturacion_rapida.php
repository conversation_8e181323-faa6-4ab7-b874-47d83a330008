<?php
session_start();

// Simular usuario para test
if (!isset($_SESSION["validar"])) {
    $_SESSION["validar"] = true;
    $_SESSION["tipo_usuario"] = 1; // Administrador
    $_SESSION["usuario"] = 1;
    $_SESSION["persona"] = "Test Usuario";
}

echo "<!DOCTYPE html>";
echo "<html><head><title>Test Ruta Facturación Rápida</title></head><body>";
echo "<h2>🧪 Test de Ruta Facturación Rápida</h2>";

echo "<h3>📊 Estado de la Sesión:</h3>";
echo "<p><strong>Usuario validado:</strong> " . ($_SESSION["validar"] ? "Sí" : "No") . "</p>";
echo "<p><strong>Tipo de usuario:</strong> " . ($_SESSION["tipo_usuario"] ?? "No definido") . "</p>";
echo "<p><strong>ID Usuario:</strong> " . ($_SESSION["usuario"] ?? "No definido") . "</p>";

echo "<h3>🔗 Tests de Rutas:</h3>";

// Test 1: Ruta con index.php
echo "<h4>Test 1: Ruta con index.php</h4>";
echo "<a href='../../index.php?action=facturacionRapida' target='_blank' style='background: #007bff; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>";
echo "🌐 index.php?action=facturacionRapida";
echo "</a>";

echo "<br><br>";

// Test 2: Ruta amigable
echo "<h4>Test 2: Ruta amigable (URL rewrite)</h4>";
echo "<a href='../../facturacionRapida' target='_blank' style='background: #28a745; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>";
echo "🚀 /facturacionRapida";
echo "</a>";

echo "<br><br>";

// Test 3: Archivo directo
echo "<h4>Test 3: Archivo directo</h4>";
echo "<a href='facturacionRapida.php' target='_blank' style='background: #ffc107; color: black; padding: 10px; text-decoration: none; border-radius: 5px;'>";
echo "📄 facturacionRapida.php";
echo "</a>";

echo "<br><br>";

// Verificar archivos
echo "<h3>📁 Verificación de Archivos:</h3>";
$archivos = [
    'facturacionRapida.php' => 'Interfaz principal',
    'ajaxBuscarProductoRapido.php' => 'Búsqueda de productos',
    'ajaxFacturacionRapida.php' => 'Procesamiento AJAX'
];

foreach ($archivos as $archivo => $descripcion) {
    if (file_exists($archivo)) {
        echo "<p style='color: green;'>✅ $archivo - $descripcion</p>";
    } else {
        echo "<p style='color: red;'>❌ $archivo - $descripcion (NO EXISTE)</p>";
    }
}

// Verificar enlaces.php
echo "<h3>🛣️ Verificación de enlaces.php:</h3>";
$enlaces_content = file_get_contents('../../models/enlaces.php');

$rutas_verificar = [
    'facturacionRapida' => 'Ruta principal',
    'usuario==1' => 'Sección administrador',
    'usuario==3' => 'Sección cajero'
];

foreach ($rutas_verificar as $buscar => $descripcion) {
    if (strpos($enlaces_content, $buscar) !== false) {
        echo "<p style='color: green;'>✅ $descripcion encontrada</p>";
    } else {
        echo "<p style='color: red;'>❌ $descripcion NO encontrada</p>";
    }
}

// Test de inclusión manual
echo "<h3>🔧 Test de Inclusión Manual:</h3>";
echo "<p>Intentando incluir el archivo directamente...</p>";

try {
    // Simular el comportamiento del sistema de enlaces
    $enlaces = "facturacionRapida";
    $usuario = $_SESSION["tipo_usuario"];
    
    if ($usuario == 1) {
        if ($enlaces == "facturacionRapida") {
            $module = "views/modules/facturacionRapida.php";
            echo "<p style='color: green;'>✅ Ruta válida para administrador: $module</p>";
        }
    }
    
    if ($usuario == 3 or $usuario == 10 or $usuario == 4) {
        if ($enlaces == "facturacionRapida") {
            $module = "views/modules/facturacionRapida.php";
            echo "<p style='color: green;'>✅ Ruta válida para cajero: $module</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}

?>

<h3>🔍 Debug de URLs:</h3>
<div style="background: #f8f9fa; padding: 15px; border-radius: 5px;">
    <p><strong>URL Base:</strong> <?php echo $_SERVER['HTTP_HOST']; ?></p>
    <p><strong>Directorio actual:</strong> <?php echo __DIR__; ?></p>
    <p><strong>URL completa esperada:</strong> https://<?php echo $_SERVER['HTTP_HOST']; ?>/index.php?action=facturacionRapida</p>
    <p><strong>URL amigable esperada:</strong> https://<?php echo $_SERVER['HTTP_HOST']; ?>/facturacionRapida</p>
</div>

<h3>📋 Instrucciones de Solución:</h3>
<div style="background: #d1ecf1; padding: 15px; border-radius: 5px;">
    <p>Si las rutas no funcionan, verifica:</p>
    <ol>
        <li>Que el archivo <code>facturacionRapida.php</code> exista en <code>views/modules/</code></li>
        <li>Que la ruta esté agregada en <code>models/enlaces.php</code> para tu tipo de usuario</li>
        <li>Que el archivo <code>.htaccess</code> esté configurado correctamente</li>
        <li>Que tengas una sesión válida iniciada</li>
    </ol>
</div>

<br>
<a href="../../index.php?action=mesa" style="background: #6c757d; color: white; padding: 10px; text-decoration: none; border-radius: 5px;">🔙 Volver a Mesas</a>

</body>
</html>
