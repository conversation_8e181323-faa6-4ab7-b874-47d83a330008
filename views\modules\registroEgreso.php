<?php
if ($_SESSION["turno"]==null)
	{
	 echo "<script>alert('Inicie turno')</script>";
	 header("location:registroTurno");
	}
	$registroEgreso = new controllerEgresoIngreso();
	$registroEgreso -> registroEgresoController();
	$puntos = $registroEgreso -> listaPuntosController();
	if(isset($_GET["action"]))
		{	if($_GET["action"] == "okI") 	{	echo "Registro Exitoso";	}		}
?>
<h1>REGISTRO DE EGRESO</h1>
<form method="post">
	<label> Motivo : </label>
	<textarea placeholder="motivo" name="motivoEgresoRegistro" cols="30" rows="5" autofocus="autofocus" required></textarea><br>
	<label> Cantidad : </label>
	<input type="text" placeholder="cantidad" name="cantidadEgresoRegistro" style="width: 15em; " required><br>
	<label> Sucursal  : </label>Principal<input type="hidden" placeholder="cantidad" name="puntos" id="puntos" style="width: 15em; " value="1" required>
	<?php/*
		# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  puntos  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

				if($puntos=="error")
				 {	echo "debe registrar el Sucursal"; }
				else
				 {	echo "<label></label>";
					$result='<select name="puntos"  id="puntos"  required>';
					foreach ($puntos as $row => $item)
					 {
					 	 $result.=' <option value="'.$item["id"].'">'.$item["nombre"].'</option>';
					  if ($_SESSION["tipo_usuario"]==1)
					   {  $result.=' <option value="'.$item["id"].'">'.$item["nombre"].'</option>';   }
					 elseif ($_SESSION["punto_id"]==$item["id"])
					  {	$result.=' <option value="'.$item["id"].'">'.$item["nombre"].'</option>'; }
					  }
					 $result.='</select>';
					 echo $result;
				 }*/

		# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  End puntos  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  max="<?=$_SESSION["fecha"];
	?><br>
	<input  type="submit" value="Enviar">
</form>
