<?php
// API de impresión optimizada para móviles
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Manejar preflight OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Solo permitir POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Método no permitido']);
    exit;
}

// Obtener datos
$input = file_get_contents('php://input');
$data = json_decode($input, true);

if (!$data) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Datos JSON inválidos']);
    exit;
}

// Validar datos requeridos
if (!isset($data['categoria']) || !isset($data['contenido'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Faltan parámetros: categoria y contenido']);
    exit;
}

$categoria = $data['categoria'];
$contenido = $data['contenido'];
$dispositivo = $data['dispositivo'] ?? 'desconocido';
$ip_cliente = $_SERVER['REMOTE_ADDR'];

// Log de la solicitud
error_log("📱 Solicitud impresión móvil - Categoría: $categoria, IP: $ip_cliente, Dispositivo: $dispositivo");

try {
    require_once '../models/conexion.php';
    $conexion = new Conexion();
    $pdo = $conexion->conectar();
    
    // Buscar configuración de la impresora
    $stmt = $pdo->prepare("SELECT ip, puerto FROM impresoras WHERE categoria = :categoria");
    $stmt->bindParam(":categoria", $categoria, PDO::PARAM_STR);
    $stmt->execute();
    $impresora = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$impresora) {
        http_response_code(404);
        echo json_encode(['success' => false, 'error' => "Impresora no encontrada para categoría: $categoria"]);
        exit;
    }
    
    $ip_impresora = $impresora['ip'];
    $puerto_impresora = $impresora['puerto'];
    
    // Determinar método de impresión
    $en_red_local = strpos($ip_cliente, '192.168.68.') === 0;
    $metodo = $en_red_local ? 'directo_movil' : 'proxy';
    
    $resultado = false;
    $error_mensaje = '';
    
    if ($metodo === 'directo_movil') {
        // Intentar impresión directa (simulada desde servidor)
        $resultado = intentarImpresionDirecta($ip_impresora, $puerto_impresora, $contenido, $categoria);
        
        if (!$resultado) {
            // Fallback al proxy
            $metodo = 'proxy_fallback';
            $resultado = intentarImpresionProxy($categoria, $contenido);
        }
    } else {
        // Usar proxy directamente
        $resultado = intentarImpresionProxy($categoria, $contenido);
    }
    
    // Registrar en logs
    registrarLogImpresion($pdo, $categoria, $metodo, $resultado, $ip_cliente, $contenido);
    
    if ($resultado) {
        echo json_encode([
            'success' => true,
            'metodo' => $metodo,
            'impresora' => $categoria,
            'ip_impresora' => $ip_impresora,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    } else {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Error en todos los métodos de impresión',
            'metodo_intentado' => $metodo,
            'impresora' => $categoria
        ]);
    }
    
} catch (Exception $e) {
    error_log("❌ Error en API impresión: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Error interno del servidor',
        'detalle' => $e->getMessage()
    ]);
}

// Función para impresión directa
function intentarImpresionDirecta($ip, $puerto, $contenido, $categoria) {
    try {
        $socket = @fsockopen($ip, $puerto, $errno, $errstr, 10);
        
        if (!$socket) {
            error_log("❌ No se pudo conectar a $ip:$puerto - $errno: $errstr");
            return false;
        }
        
        // Comandos ESC/POS
        $datos = "\x1B\x40"; // Reset
        $datos .= "\x1B\x61\x01"; // Centrar
        $datos .= "=== MACARENA RESTAURANT ===\n";
        $datos .= "\x1B\x61\x00"; // Alinear izquierda
        $datos .= "Categoria: " . strtoupper($categoria) . "\n";
        $datos .= "Fecha: " . date('Y-m-d H:i:s') . "\n";
        $datos .= "Dispositivo: Móvil\n";
        $datos .= "--------------------------------\n";
        $datos .= $contenido;
        $datos .= "\n--------------------------------\n\n\n";
        $datos .= "\x1D\x56\x42\x00"; // Cortar papel
        
        $resultado = fwrite($socket, $datos);
        fclose($socket);
        
        if ($resultado) {
            error_log("✅ Impresión directa exitosa en $categoria ($ip:$puerto)");
            return true;
        }
        
        return false;
        
    } catch (Exception $e) {
        error_log("❌ Error impresión directa: " . $e->getMessage());
        return false;
    }
}

// Función para impresión via proxy
function intentarImpresionProxy($categoria, $contenido) {
    try {
        // Obtener configuración del proxy
        require_once '../models/conexion.php';
        $conexion = new Conexion();
        $pdo = $conexion->conectar();
        
        $stmt = $pdo->query("SELECT valor FROM config_impresion WHERE clave = 'ip_proxy'");
        $ip_proxy = $stmt->fetch(PDO::FETCH_ASSOC)['valor'] ?? '***********';
        
        $stmt = $pdo->query("SELECT valor FROM config_impresion WHERE clave = 'puerto_proxy'");
        $puerto_proxy = $stmt->fetch(PDO::FETCH_ASSOC)['valor'] ?? '3000';
        
        // Preparar datos para el proxy
        $datos_proxy = json_encode([
            'impresora' => $categoria,
            'contenido' => $contenido,
            'timestamp' => time(),
            'origen' => 'api_movil'
        ]);
        
        // Intentar enviar al proxy
        $context = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => "Content-Type: application/json\r\n",
                'content' => $datos_proxy,
                'timeout' => 10
            ]
        ]);
        
        $resultado = @file_get_contents("http://$ip_proxy:$puerto_proxy/imprimir", false, $context);
        
        if ($resultado) {
            $respuesta = json_decode($resultado, true);
            if ($respuesta && $respuesta['success']) {
                error_log("✅ Impresión via proxy exitosa en $categoria");
                return true;
            }
        }
        
        error_log("❌ Error en proxy: " . ($resultado ?: 'Sin respuesta'));
        return false;
        
    } catch (Exception $e) {
        error_log("❌ Error impresión proxy: " . $e->getMessage());
        return false;
    }
}

// Función para registrar logs
function registrarLogImpresion($pdo, $categoria, $metodo, $success, $ip_cliente, $contenido) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO logs_impresion 
            (impresora, metodo, success, ip_cliente, datos_enviados, categoria, fecha_hora) 
            VALUES 
            (:impresora, :metodo, :success, :ip_cliente, :datos_enviados, :categoria, NOW())
        ");
        
        $stmt->bindParam(":impresora", $categoria, PDO::PARAM_STR);
        $stmt->bindParam(":metodo", $metodo, PDO::PARAM_STR);
        $stmt->bindParam(":success", $success, PDO::PARAM_BOOL);
        $stmt->bindParam(":ip_cliente", $ip_cliente, PDO::PARAM_STR);
        $stmt->bindParam(":datos_enviados", $contenido, PDO::PARAM_STR);
        $stmt->bindParam(":categoria", $categoria, PDO::PARAM_STR);
        
        $stmt->execute();
        
    } catch (Exception $e) {
        error_log("⚠️ Error registrando log: " . $e->getMessage());
    }
}
?>
