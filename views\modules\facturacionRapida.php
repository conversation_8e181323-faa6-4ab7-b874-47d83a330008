<?php
/*session_start();*/
if(!$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

// Detectar rutas automáticamente
if (file_exists("models/conexion.php")) {
    require_once "models/conexion.php";
    require_once "models/crudFacturaAja.php";
} elseif (file_exists("../../models/conexion.php")) {
    require_once "../../models/conexion.php";
    require_once "../../models/crudFacturaAja.php";
} else {
    die("Error: No se pueden encontrar los archivos de modelo");
}

// Obtener lista de productos para autocompletar
$productos = Datos::listaProductosModel("productos");
?>


    <script src="jquery-2.1.1.min.js"></script>
    <style>
        .factura-rapida {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .producto-item {
            background: white;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        .total-section {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .btn-facturar {
            background: #28a745;
            color: white;
            font-size: 18px;
            padding: 15px 30px;
        }
        .lista-productos {
            max-height: 300px;
            overflow-y: auto;
        }
        #lista_productos {
            position: absolute;
            z-index: 1000;
            background: white;
            border: 1px solid #ccc;
            max-height: 200px;
            overflow-y: auto;
            width: 100%;
        }
        .producto-sugerencia {
            padding: 10px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
        }
        .producto-sugerencia:hover {
            background: #f8f9fa;
        }
        .btn-facturar-superior {
            font-size: 14px;
            padding: 8px 16px;
            white-space: nowrap;
        }
        .btn-facturar-superior:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* Mejoras para móviles */
        @media (max-width: 768px) {
            .factura-rapida {
                padding: 15px;
                margin: 10px 0;
            }

            .btn-facturar-superior {
                font-size: 12px;
                padding: 6px 12px;
            }

            .total-section {
                padding: 10px;
            }

            .producto-item {
                padding: 8px;
            }

            .form-group {
                margin-bottom: 10px;
            }

            .form-group label {
                font-size: 14px;
                margin-bottom: 3px;
            }

            .form-control {
                font-size: 14px;
                padding: 6px 8px;
            }
        }

        /* Espaciado entre formas de pago */
        .row + .row {
            margin-top: 5px;
        }

        /* Mejorar apariencia de labels */
        .form-group label {
            font-weight: 600;
            color: #495057;
        }
    </style>



<!-- Contenedor principal -->
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <h2>🚀 Facturación Rápida</h2>
            <p class="text-muted">Sistema de facturación directa sin estados de pedidos</p>
        </div>
    </div>

    <div class="row">
        <!-- Sección de Productos -->
        <div class="col-xs-12 col-md-8">
            <div class="factura-rapida">
                <h4>📦 Agregar Productos</h4>
                
                <!-- Buscador de productos -->
                <div class="form-group" style="position: relative;">
                    <label>Buscar Producto:</label>
                    <input type="text" id="buscar_producto" class="form-control" placeholder="Escriba el nombre del producto..." onkeyup="buscarProducto()" autocomplete="off">
                    <div id="lista_productos" style="display: none;"></div>
                </div>

                <!-- Productos seleccionados -->
                <div class="lista-productos">
                    <h5>Productos Seleccionados:</h5>
                    <div id="productos_seleccionados">
                        <p class="text-muted">No hay productos seleccionados</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sección de Facturación -->
        <div class="col-xs-12 col-md-4">
            <div class="factura-rapida">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                    <h4>💰 Resumen de Factura</h4>
                    <button type="button" class="btn btn-success btn-sm btn-facturar-superior" onclick="facturarRapido()" id="btn_facturar_superior" disabled>
                        ⚡ Facturar Rápido
                    </button>
                </div>
                
                <form name="facturacion" id="form_facturacion">
                    <div class="total-section">
                        <div class="form-group">
                            <label>Subtotal:</label>
                            <input type="text" id="subtotal" class="form-control" readonly value="$0">
                        </div>
                        
                        <div class="form-group">
                            <label>Propina:</label>
                            <input type="number" id="propina" class="form-control" value="0" onchange="calcularTotal()">
                        </div>
                        
                        <div class="form-group">
                            <label><strong>Total a Pagar:</strong></label>
                            <input type="text" id="total" class="form-control" readonly value="$0" style="font-weight: bold; font-size: 18px;">
                        </div>
                    </div>

                    <h5>💳 Forma de Pago</h5>

                    <!-- Formas de pago en dos columnas -->
                    <div class="row">
                        <div class="col-xs-6 col-sm-6">
                            <div class="form-group">
                                <label>💵 Efectivo:</label>
                                <input type="number" id="efectivo" class="form-control" value="0" onchange="calcularCambio()">
                            </div>
                        </div>
                        <div class="col-xs-6 col-sm-6">
                            <div class="form-group">
                                <label>💳 Tarjeta:</label>
                                <input type="number" id="tarjeta" class="form-control" value="0" onchange="calcularCambio()">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-xs-6 col-sm-6">
                            <div class="form-group">
                                <label>📱 Nequi:</label>
                                <input type="number" id="nequi" class="form-control" value="0" onchange="calcularCambio()">
                            </div>
                        </div>
                        <div class="col-xs-6 col-sm-6">
                            <div class="form-group">
                                <label>📲 Daviplata:</label>
                                <input type="number" id="daviplata" class="form-control" value="0" onchange="calcularCambio()">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-xs-12 col-sm-6">
                            <div class="form-group">
                                <label>🏦 Bancolombia:</label>
                                <input type="number" id="bancolombia" class="form-control" value="0" onchange="calcularCambio()">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Cédula Cliente:</label>
                        <input type="text" id="cedula_cliente" class="form-control" placeholder="Opcional">
                    </div>

                    <div class="total-section">
                        <div class="form-group">
                            <label>Cambio:</label>
                            <input type="text" id="cambio" class="form-control" readonly value="$0">
                        </div>
                    </div>

                    <button type="button" class="btn btn-facturar btn-block" onclick="facturarRapido()">
                        🚀 FACTURAR RÁPIDO
                    </button>
                </form>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div id="resultado_facturacion"></div>
        </div>
    </div>
</div> <!-- Fin contenedor principal -->

<script>
let productosSeleccionados = [];
let totalFactura = 0;

// Buscar productos
function buscarProducto() {
    const termino = document.getElementById('buscar_producto').value;
    
    if (termino.length < 2) {
        document.getElementById('lista_productos').style.display = 'none';
        return;
    }
    
    $.ajax({
        url: 'views/modules/ajaxBuscarProductoRapido.php',
        type: 'POST',
        data: { termino: termino },
        success: function(response) {
            document.getElementById('lista_productos').innerHTML = response;
            document.getElementById('lista_productos').style.display = 'block';
        }
    });
}

// Seleccionar producto
function seleccionarProducto(id, nombre, precio) {
    // Verificar si ya está seleccionado
    const existe = productosSeleccionados.find(p => p.id === id);
    if (existe) {
        existe.cantidad++;
    } else {
        productosSeleccionados.push({
            id: id,
            nombre: nombre,
            precio: parseFloat(precio),
            cantidad: 1
        });
    }
    
    actualizarListaProductos();
    calcularTotal();
    
    // Limpiar búsqueda
    document.getElementById('buscar_producto').value = '';
    document.getElementById('lista_productos').style.display = 'none';
}

// Actualizar lista de productos seleccionados
function actualizarListaProductos() {
    const container = document.getElementById('productos_seleccionados');
    
    if (productosSeleccionados.length === 0) {
        container.innerHTML = '<p class="text-muted">No hay productos seleccionados</p>';
        return;
    }
    
    let html = '';
    productosSeleccionados.forEach((producto, index) => {
        const subtotal = producto.precio * producto.cantidad;
        html += `
            <div class="producto-item">
                <div class="row">
                    <div class="col-xs-12 col-sm-6 col-md-6">
                        <strong>${producto.nombre}</strong><br>
                        <small>$${producto.precio.toLocaleString()}</small>
                    </div>
                    <div class="col-xs-4 col-sm-2 col-md-3">
                        <input type="number" value="${producto.cantidad}" min="1"
                               onchange="cambiarCantidad(${index}, this.value)"
                               class="form-control form-control-sm">
                    </div>
                    <div class="col-xs-4 col-sm-3 col-md-2">
                        <strong>$${subtotal.toLocaleString()}</strong>
                    </div>
                    <div class="col-xs-4 col-sm-1 col-md-1">
                        <button type="button" class="btn btn-sm btn-danger" onclick="eliminarProducto(${index})">×</button>
                    </div>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

// Cambiar cantidad
function cambiarCantidad(index, nuevaCantidad) {
    if (nuevaCantidad > 0) {
        productosSeleccionados[index].cantidad = parseInt(nuevaCantidad);
        actualizarListaProductos();
        calcularTotal();
    }
}

// Eliminar producto
function eliminarProducto(index) {
    productosSeleccionados.splice(index, 1);
    actualizarListaProductos();
    calcularTotal();
}

// Calcular total
function calcularTotal() {
    let subtotal = 0;
    productosSeleccionados.forEach(producto => {
        subtotal += producto.precio * producto.cantidad;
    });

    const propina = parseFloat(document.getElementById('propina').value) || 0;
    totalFactura = subtotal + propina;

    document.getElementById('subtotal').value = '$' + subtotal.toLocaleString();
    document.getElementById('total').value = '$' + totalFactura.toLocaleString();

    // NUEVO: Automáticamente poner el total en efectivo para agilizar
    if (totalFactura > 0) {
        document.getElementById('efectivo').value = totalFactura;
        // Limpiar otros campos de pago
        document.getElementById('tarjeta').value = '';
        document.getElementById('nequi').value = '';
        document.getElementById('daviplata').value = '';
        document.getElementById('bancolombia').value = '';

        // Habilitar botón superior
        document.getElementById('btn_facturar_superior').disabled = false;
    } else {
        // Deshabilitar botón superior si no hay productos
        document.getElementById('btn_facturar_superior').disabled = true;
    }

    calcularCambio();
}

// Calcular cambio
function calcularCambio() {
    const efectivo = parseFloat(document.getElementById('efectivo').value) || 0;
    const tarjeta = parseFloat(document.getElementById('tarjeta').value) || 0;
    const nequi = parseFloat(document.getElementById('nequi').value) || 0;
    const daviplata = parseFloat(document.getElementById('daviplata').value) || 0;
    const bancolombia = parseFloat(document.getElementById('bancolombia').value) || 0;
    
    const totalPagado = efectivo + tarjeta + nequi + daviplata + bancolombia;
    const cambio = totalPagado - totalFactura;
    
    document.getElementById('cambio').value = '$' + cambio.toLocaleString();
}

// Facturar rápido
function facturarRapido() {
    if (productosSeleccionados.length === 0) {
        alert('Debe agregar al menos un producto');
        return;
    }
    
    const totalPagado = 
        (parseFloat(document.getElementById('efectivo').value) || 0) +
        (parseFloat(document.getElementById('tarjeta').value) || 0) +
        (parseFloat(document.getElementById('nequi').value) || 0) +
        (parseFloat(document.getElementById('daviplata').value) || 0) +
        (parseFloat(document.getElementById('bancolombia').value) || 0);
    
    if (totalPagado < totalFactura) {
        alert('El pago es insuficiente. Falta: $' + (totalFactura - totalPagado).toLocaleString());
        return;
    }
    
    //if (confirm('¿Confirmar facturación rápida por $' + totalFactura.toLocaleString() + '?')) {
        const datos = {
            productos: JSON.stringify(productosSeleccionados),
            efectivo: document.getElementById('efectivo').value,
            tarjeta: document.getElementById('tarjeta').value,
            nequi: document.getElementById('nequi').value,
            daviplata: document.getElementById('daviplata').value,
            bancolombia: document.getElementById('bancolombia').value,
            propina: document.getElementById('propina').value,
            total: totalFactura,
            cedula: document.getElementById('cedula_cliente').value
        };
        
        document.getElementById('resultado_facturacion').innerHTML = 
            '<div class="alert alert-info">⏳ Procesando facturación rápida...</div>';
        
        $.ajax({
            url: 'views/modules/ajaxFacturacionRapida.php',
            type: 'POST',
            data: datos,
            success: function(response) {
                console.log('Respuesta completa:', response);

                // Ejecutar cualquier script que venga en la respuesta
                const scriptMatch = response.match(/<script>(.*?)<\/script>/);
                if (scriptMatch) {
                    console.log('Ejecutando script:', scriptMatch[1]);
                    eval(scriptMatch[1]);
                }

                if (response.includes('success_rapida') || response.includes('Facturación rápida completada')) {
                    // Limpiar formulario
                    productosSeleccionados = [];
                    actualizarListaProductos();
                    calcularTotal();
                    document.getElementById('form_facturacion').reset();

                    document.getElementById('resultado_facturacion').innerHTML =
                        '<div class="alert alert-success">✅ Facturación rápida completada exitosamente<br>📄 PDF abierto para impresión</div>';

                    // Abrir PDF como respaldo si el script no funcionó
                    setTimeout(function() {
                        window.open('pdf', '_blank');
                    }, 1000);

                } else {
                    document.getElementById('resultado_facturacion').innerHTML =
                        '<div class="alert alert-danger">❌ Error en facturación: ' + response + '</div>';
                }
            },
            error: function(xhr, status, error) {
                document.getElementById('resultado_facturacion').innerHTML = 
                    '<div class="alert alert-danger">❌ Error de conexión: ' + error + '</div>';
            }
        });
    //}//Habilitar o deshabilitar para que no pregunte en facturación rápida.
}

// Ocultar lista al hacer clic fuera
document.addEventListener('click', function(e) {
    if (!e.target.closest('#buscar_producto') && !e.target.closest('#lista_productos')) {
        document.getElementById('lista_productos').style.display = 'none';
    }
});
</script>