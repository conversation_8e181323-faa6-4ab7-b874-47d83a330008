
<?php 
	$registroCsuministro = new controllerCompraSuministro();
	$proveedores = $registroCsuministro -> listaProveedoresController();
	$registroCsuministro -> registroCompraSuministroController();
 ?>
	
	<link rel="stylesheet" type="text/css" href="../css/bootstrap.css">
	<link rel="stylesheet" type="text/css" href="../css/bootstrap-theme.css">
	<link rel="stylesheet" type="text/css" href="../estilo.css">
	<link rel="stylesheet" type="text/css" href="../font-awesome/css/font-awesome.css">
	<script src="../js/jquery-1.11.3.min.js" type="text/javascript"></script>
	<script src="http://code.jquery.com/jquery-latest.js"></script>
	
	<script type="text/javascript">
	     /////////////Crear filas
	    	 function genera_tabla() 
	    	  {
				  // Obtener la referencia del elemento body
				  var body = document.getElementsByTagName("body")[0];				 
				  // Crea un elemento <table> y un elemento <tbody>
				  var tabla   = document.createElement("table");
				  var tblBody = document.createElement("tbody");				 
				  // Crea las celdas
				  for (var i = 0; i < 3; i++) 
				   {
					    // Crea las hileras de la tabla
					    var hilera = document.createElement("tr");				 
					    for (var j = 0; j < 3; j++) 
					     {
						      // Crea un elemento <td> y un nodo de texto, haz que el nodo de
						      // texto sea el contenido de <td>, ubica el elemento <td> al final
						      // de la hilera de la tabla
						      var celda = document.createElement("td");
						      var textoCelda = document.createTextNode("celda en la hilera "+i+", columna "+j);
						      celda.appendChild(textoCelda);
						      hilera.appendChild(celda);
				     	 }			 
					    // agrega la hilera al final de la tabla (al final del elemento tblbody)
					    tblBody.appendChild(hilera);
			  		}			 
				  // posiciona el <tbody> debajo del elemento <table>
				  tabla.appendChild(tblBody);
				  // appends <table> into <body>
				  body.appendChild(tabla);
				  // modifica el atributo "border" de la tabla y lo fija a "2";
				  tabla.setAttribute("border", "1");
				 }
	    	///////////fin crear filas

	    $(document).ready(function()
	    {
	        /**  * Funcion para añadir una nueva columna en la tabla*/
	         
	        $("#add").click(function()
	        	{
		            // Obtenemos el numero de filas (td) que tiene la primera columna
		            // (tr) del id "tabla"
		            var tds=$("#tabla tr:first td").length;
		            //variable de los imput creo
		            //var  nombre=$("#nombre");
		            var nombre = document.cal.nombre.value;
		            var cantidad = document.cal.cantidad.value;   
		            var precio = document.cal.precio.value;   
		            // Obtenemos el total de columnas (tr) del id "tabla"
		            var trs=$("#tabla tr").length;
		            var nuevaFila="<tr>";
		            ////////////////////////matriz ///////////
		            matriz= new Array(trs);
		            for(var j=0;j<trs;j++)
		             {
		             	matriz[j]=new Array(3);
		             }
		            for(var j=0;j<trs;j++)
		             {
		             	matriz[j]['suministro']=nombre;
		             	matriz[j]['cantidad']=cantidad;
		             	matriz[j]['precio']=precio;
		             	//alert(matriz[j]['suministro']+" cantidad:"+matriz[j]['cantidad']);
		             }
		          //////////////////////////////////// 
		            for(var i=0;i<tds;i++)
		             {
		                // añadimos las columnas
		                if (i==0)
		                 {
		                 	nuevaFila+="<td><input class='form-control' type='text' name='nombre"+trs+"' value='"+nombre+ "' required /> </td>";
		                 	
		                 }
		                else
		                 {	if (i==1) 
		                 	 {
		                 		nuevaFila+="<td><input class='form-control' type='text' name='cantidad"+trs+"' value='"+cantidad+"' required /></td>";
		                 	 }
		                 	else
		                 	 {
		                 		nuevaFila+="<td><input class='form-control' type='text' name='precio"+trs+"' value='"+precio+"' required /></td>";
		                 	 }
		                 	//matriz[trs][i]=cantidad;
		                 }
		             }
		            // Añadimos una columna con el numero total de filas.
		            // Añadimos uno al total, ya que cuando cargamos los valores para la
		            // columna, todavia no esta añadida	            
		            nuevaFila+="<td>"+(trs)+" ";
		            nuevaFila+="</tr>";
		            $("#tabla").append(nuevaFila);
		            document.cal.nombre.value="";
		            document.cal.cantidad.value="";
		            document.cal.precio.value="";
		            document.cal.fila.value=trs;
		            //alert( matriz[trs-1]['nombre']);
		        });
		 
		        /**
		         * Funcion para eliminar la ultima columna de la tabla.
		         * Si unicamente queda una columna, esta no sera eliminada
		         */
		        $("#del").click(function(){
	            // Obtenemos el total de columnas (tr) del id "tabla"
		            var trs=$("#tabla tr").length;
		            if(trs>1)
		            {
		                // Eliminamos la ultima columna
		                $("#tabla tr:last").remove();
		            }
		        });
		       // $("#destino").load("views/modules/ajaxFactura.php", {matriz: matriz,trs: trs, }, function(){
				        						 //alert("recibidos los datos por ajax efectivo"); 		 
				     						///});	
	    });

	    </script>

	    <style>
	    td, input {padding:5px;}
	    </style>
	
		<?php
			if (!isset($_POST['datos'])) 
			 { 
				//echo "<script>alert(' cantidad".$cantidad['1']."')</script>";
			 }
		?>
		<h1>REGISTRO DE COMPRA SUMINISTRO</h1>
		<br>
		<br>
		<br>
		<form action="" method="post" name="cal">
			<div>
				
			
			<div  class="col-md-4" style="background-color:#00f; border-radius: 15px 15px 15px 15px; padding:12px">	
				<h4 >Datos de la Factura</h4>
				<table>		 	
			<tr>
				<td align="right"><label> PROVEEDOR </label></td>
				<td><?php  
					# # %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  proveedores  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
						if($proveedores=="error")
							{	echo "debe registrar el proveedores"; }
						else{
								echo "<label></label>";
								$result='<select name="proveedores"  id="proveedores">';
								$result.=' <option value="-1">proveedores</option>';
								foreach ($proveedores as $row => $item)
								 	{	$result.=' <option value="'.$item["id"].'">'.$item["nombre"].'</option>';	}	
								 $result.='</select>';
								 echo $result;
							}  
					# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  End proveedores  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  max="<?=$_SESSION["fecha"];
					 ?>&laquo;<a class="enlace" href="registroProveedor" target="_blank"> Registrar proveedor</a>&raquo;<br></td>
			</tr>
			<tr>
				<td align="right"><label>  VALOR FACTURA  </label></td>
				<td><input type="text" placeholder="valor_compra" name="valor_compraCsuministroRegistro" required></td>
			</tr>
			<tr>
				<td align="right"><label> FECHA </label></td>
				<td><input type="date" placeholder="AAAA-MM-DD" name="fecha_horaCsuministroRegistro"  required></td>
			</tr>
			<tr>
				<td align="right"><label> No FACTURA  </label></td>
				<td><input type="text" placeholder="numero factura compra" name="numero_factura_compraCsuministroRegistro" ></td>
			</tr>
			<tr>
				<td align="right"><label> ESTADO  </label></td>
				<td><div >
			  		<select name="pago" id="pago" onchange="habilitar(this.value);">
				  <option value="1">Cancelada</option>
				  <option value="2">Credito</option>
				  
				</select>
			  	</div></td>
			</tr>
			<tr>
				<td align="right"><label  > ABONO  </label></td>
				<td><input type="text" placeholder="$" name="abono" value="0" id="abono" ></td>
			</tr>		
	</table>
				
			</div>
			
		
		<div  class="col-md-8" style="background-color:#FFF; border-radius: 15px 15px 15px 15px; padding:12px">	
			
			<H3>Detalle de la Factura</H3>
			<table>
				
				<tr>
					<td align="right">Suministro:</td>
					<td><input type="text" name="nombre" id="nombre"></td>
				
					<td align="right">Cantidad</td>
					<td><input type="text" name="cantidad" id="cantidad"></td>
				
					<td align="right">Precio:</td>
					<td><input type="text" name="precio" id="precio" ></td>
				</tr>
			</table>
			 
			<input type="hidden" name="fila" id="fila"> <br>
			<input type="button" id="add" value="Agregar" class="btn btn-sm btn-success">
			<input type="button" id="del" value="eliminar la ultima fila " class="btn btn-sm btn-danger">
			<button type="submit" name="datos" class="btn btn-sm btn-primary" >Guardar </button>
			<button type="reset" name="borrar" class="btn btn-sm btn-warning" >Limpiar </button>
			<p>			
		    <table id="tabla" class="table" border=1>
		        <tr>
		            <td>Suministro</td>
		            <td>Cantidad</td>
		            <td>Precio</td>
		            <!-- podemos añadir tantas columnas como deseemos -->
		            <!--<td>tercera columna</td><input type="submit" value="Enviar">-->
		        </tr>
		    </table>
		    
		</div>
		
		<div id="destino">	</div>
		</div>
		</form>



