<script type="text/javascript">

	//------------------------- <PERSON><PERSON> Buscar------------------------
		function buscarPlaca1(inputString)
		 {
			if(inputString.length == 0)
			 {	// Hide the suggestion box.
				$('#suggestions').hide();
			 }
			else
			  {	//alert("Entro en el else de placa");
				$("#dos").load("views/modules/ajaxBuscarSuministro.php", {placa: $("#dedo").val()}, function(){

	     		 });
			 }
		 }
	//------------------ Nombre cerrar--------------------
	//------------------------- codigo <PERSON>car------------------------
		function buscarPlaca2(inputString)
		 {
			if(inputString.length == 0)
			 {	// Hide the suggestion box.
				$('#suggestions').hide();
			 }
			else
			  {	//alert("Entro en el else de placa");
				$("#dos").load("views/modules/ajaxBuscarSuministroCo.php", {placa: $("#codigo").val()}, function(){

	     		 });
			 }
		 }
	//------------------ Nombre cerrar--------------------

	//-------------- Convierte a Mayusculas  -------------------------------------
		function aMayusculas(obj,id)
		 {
		    obj = obj.toUpperCase();
		    document.getElementById(id).value = obj;
		 }
	//------------------cierre-------------------- style="width: 350px;"
</script>
<h1>BUSCAR SUMINISTRO</h1>

<form method="post">

	<label for=""><b>Nombre:</b></label><input type="text" placeholder="Nombre Suministros" class="mayuscula"  name="dedo" id="dedo" size="38" onkeyup="buscarPlaca1(this.value);"  >&nbsp;&nbsp;&nbsp;&nbsp;

	<label for=""><b>Codigo Barra : </b></label><input type="text" placeholder="0345" class="mayuscula"  name="codigo" id="codigo" size="38" onkeyup="buscarPlaca2(this.value);"><br>

</form>
<br>
<div id="dos"></div>

<?php
	$registro1 = new controllerSuministro();
	$registro1 -> cargarSucursalController();

	if(isset($_GET["action"]))

		{	if($_GET["action"] == "okcS")
				{	echo "Registro Exitoso";	}
		}
?>
