<?php
require_once "models/crud.php";
require_once "controllers/controller.php";

ini_set("session.cookie_lifetime","28800");
ini_set("session.gc_maxlifetime","28800");

$ajax = new MvcController();
$r = $ajax->cancelarController();

if ($r == "sucess") {
    echo '<script>location.href="index.php?action=mesa";</script>';
    echo "<h1>✅ Mesa cancelada exitosamente</h1>";
    echo "<p>Todos los pedidos han sido cancelados y la mesa está lista para nuevos pedidos.</p>";
} else {
    echo "<script>alert('No se pudo cancelar la mesa. Intente nuevamente.');
          location.href='index.php?action=mesa';</script>";
    echo "<h1>❌ Error al cancelar</h1>";
    echo "<p>No se pudo cancelar la mesa.</p>";
}
?>
