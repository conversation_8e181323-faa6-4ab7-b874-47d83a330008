<?php

session_start();

if(!isset($_SESSION["validar"]) || !$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "models/conexion.php";
require_once "models/crudPedidoMesaVendido.php";

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Debug: Mesa 10 - Error de Facturación</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
</head>
<body>

<div class="container">
    <h2>🔧 Debug: Mesa 10 - Error de Facturación</h2>
    <hr>
    
    <div class="alert alert-danger">
        <h4>❌ Error Detectado:</h4>
        <p><strong>SQLSTATE[42000]:</strong> Syntax error or access violation: 1064</p>
        <p><strong>Error SQL:</strong> near ', 'n', '1')' at line 1</p>
        <p><strong>Causa probable:</strong> Variable $mesero está vacía en la consulta INSERT</p>
    </div>
    
    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">🔍 Datos de la Mesa 10</h3>
        </div>
        <div class="panel-body">
            <?php
            $mesa_id = 10;
            
            try {
                echo "<h5>📊 Productos en producto_vendido_mesa:</h5>";
                $respuesta = DatosPedidoMesaVendido::vistaPmesaModel($mesa_id, "producto_vendido_mesa");
                
                if (count($respuesta) > 0) {
                    echo "<table class='table table-striped'>";
                    echo "<thead>";
                    echo "<tr>";
                    echo "<th>ID Producto</th>";
                    echo "<th>Nombre</th>";
                    echo "<th>Precio</th>";
                    echo "<th>Cantidad</th>";
                    echo "<th>Mesa ID</th>";
                    echo "<th>Mesero</th>";
                    echo "<th>Descuento</th>";
                    echo "</tr>";
                    echo "</thead>";
                    echo "<tbody>";
                    
                    foreach ($respuesta as $item) {
                        $mesero_class = empty($item['mesero']) ? 'danger' : 'success';
                        echo "<tr class='{$mesero_class}'>";
                        echo "<td>{$item['idpr']}</td>";
                        echo "<td>{$item['nombrepr']}</td>";
                        echo "<td>$" . number_format($item['preciopr'], 0) . "</td>";
                        echo "<td>{$item['cantidadpvm']}</td>";
                        echo "<td>{$item['idmesa']}</td>";
                        echo "<td><strong>" . ($item['mesero'] ?: 'NULL/VACÍO') . "</strong></td>";
                        echo "<td>{$item['descuentopvm']}%</td>";
                        echo "</tr>";
                    }
                    
                    echo "</tbody></table>";
                    
                    // Verificar el primer elemento que causaría el error
                    $primer_item = $respuesta[0];
                    echo "<div class='alert alert-warning'>";
                    echo "<h6>⚠️ Primer elemento (que se usa para $mesero):</h6>";
                    echo "<p><strong>Mesero:</strong> " . var_export($primer_item['mesero'], true) . "</p>";
                    echo "<p><strong>¿Está vacío?:</strong> " . (empty($primer_item['mesero']) ? 'SÍ' : 'NO') . "</p>";
                    echo "<p><strong>¿Es numérico?:</strong> " . (is_numeric($primer_item['mesero']) ? 'SÍ' : 'NO') . "</p>";
                    echo "</div>";
                    
                } else {
                    echo "<div class='alert alert-warning'>No hay productos en producto_vendido_mesa para la mesa 10</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-warning">
        <div class="panel-heading">
            <h3 class="panel-title">🔍 Consulta SQL Simulada</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                $respuesta = DatosPedidoMesaVendido::vistaPmesaModel($mesa_id, "producto_vendido_mesa");
                
                if (count($respuesta) > 0) {
                    $mesero = $respuesta[0]["mesero"];
                    $clienteCedula = "1"; // Valor de ejemplo
                    
                    echo "<h5>🔧 Simulación de la consulta problemática:</h5>";
                    
                    echo "<h6>Variables:</h6>";
                    echo "<ul>";
                    echo "<li><strong>\$mesero:</strong> " . var_export($mesero, true) . "</li>";
                    echo "<li><strong>\$clienteCedula:</strong> " . var_export($clienteCedula, true) . "</li>";
                    echo "</ul>";
                    
                    echo "<h6>Consulta generada:</h6>";
                    $consulta_simulada = "INSERT INTO pedidos (mesero_id, facturado, cedula_cliente) VALUES (".$mesero.", 'n', '".$clienteCedula."')";
                    echo "<div class='well'>";
                    echo "<code>{$consulta_simulada}</code>";
                    echo "</div>";
                    
                    if (empty($mesero)) {
                        echo "<div class='alert alert-danger'>";
                        echo "<h6>❌ PROBLEMA IDENTIFICADO:</h6>";
                        echo "<p>La variable \$mesero está vacía, lo que genera una consulta inválida:</p>";
                        echo "<code>INSERT INTO pedidos (mesero_id, facturado, cedula_cliente) VALUES (, 'n', '1')</code>";
                        echo "</div>";
                    } else {
                        echo "<div class='alert alert-success'>";
                        echo "<h6>✅ Consulta parece correcta</h6>";
                        echo "<p>La variable \$mesero tiene un valor válido.</p>";
                        echo "</div>";
                    }
                    
                } else {
                    echo "<div class='alert alert-warning'>No se pueden simular consultas sin datos</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error en simulación: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-primary">
        <div class="panel-heading">
            <h3 class="panel-title">👤 Información de Sesión</h3>
        </div>
        <div class="panel-body">
            <?php
            echo "<h5>📋 Variables de sesión relevantes:</h5>";
            echo "<table class='table table-condensed'>";
            echo "<thead><tr><th>Variable</th><th>Valor</th></tr></thead>";
            echo "<tbody>";
            
            $session_vars = ['usuario', 'validar', 'mesa', 'clientep', 'nombre'];
            foreach ($session_vars as $var) {
                $valor = isset($_SESSION[$var]) ? $_SESSION[$var] : 'No definida';
                echo "<tr>";
                echo "<td><code>\$_SESSION['{$var}']</code></td>";
                echo "<td><strong>{$valor}</strong></td>";
                echo "</tr>";
            }
            echo "</tbody></table>";
            
            echo "<h5>🔧 Valor de fallback para mesero:</h5>";
            $mesero_fallback = isset($_SESSION['usuario']) ? $_SESSION['usuario'] : 1;
            echo "<p><strong>Fallback:</strong> {$mesero_fallback}</p>";
            ?>
        </div>
    </div>
    
    <div class="panel panel-success">
        <div class="panel-heading">
            <h3 class="panel-title">🔧 Solución Implementada</h3>
        </div>
        <div class="panel-body">
            <h5>✅ Validación agregada en facturaajaxModel():</h5>
            <div class="well">
                <code>
                // VALIDACIÓN: Verificar que mesero no esté vacío<br>
                if (empty($mesero) || !is_numeric($mesero)) {<br>
                &nbsp;&nbsp;&nbsp;&nbsp;$mesero = isset($_SESSION['usuario']) ? $_SESSION['usuario'] : 1;<br>
                }
                </code>
            </div>
            
            <h5>💡 Cómo funciona:</h5>
            <ol>
                <li>Si $mesero está vacío o no es numérico</li>
                <li>Usa $_SESSION['usuario'] como fallback</li>
                <li>Si no hay usuario en sesión, usa 1 como valor por defecto</li>
                <li>Registra logs detallados para debug</li>
            </ol>
            
            <div class="alert alert-info">
                <h6>🧪 Próximo paso:</h6>
                <p>Intenta facturar la mesa 10 nuevamente. La validación debería prevenir el error y usar un valor válido para mesero_id.</p>
            </div>
        </div>
    </div>
    
    <div class="panel panel-default">
        <div class="panel-heading">
            <h3 class="panel-title">📊 Verificación de Datos Relacionados</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                echo "<h5>🪑 Información de la Mesa 10:</h5>";
                $stmt_mesa = Conexion::conectar()->prepare("SELECT * FROM mesas WHERE id = 10");
                $stmt_mesa->execute();
                $mesa_info = $stmt_mesa->fetch(PDO::FETCH_ASSOC);
                
                if ($mesa_info) {
                    echo "<table class='table table-condensed'>";
                    foreach ($mesa_info as $campo => $valor) {
                        echo "<tr><td><strong>{$campo}:</strong></td><td>{$valor}</td></tr>";
                    }
                    echo "</table>";
                } else {
                    echo "<div class='alert alert-warning'>Mesa 10 no encontrada</div>";
                }
                
                echo "<h5>📋 Pedidos existentes para Mesa 10:</h5>";
                $stmt_pedidos = Conexion::conectar()->prepare("
                    SELECT id, numero_pedido, mesero_id, mesa_id, estado, facturado, fecha_pedido 
                    FROM pedidos 
                    WHERE mesa_id = 10 
                    ORDER BY fecha_pedido DESC 
                    LIMIT 5
                ");
                $stmt_pedidos->execute();
                $pedidos = $stmt_pedidos->fetchAll(PDO::FETCH_ASSOC);
                
                if (count($pedidos) > 0) {
                    echo "<table class='table table-condensed'>";
                    echo "<thead>";
                    echo "<tr>";
                    echo "<th>ID</th>";
                    echo "<th>Número</th>";
                    echo "<th>Mesero ID</th>";
                    echo "<th>Mesa ID</th>";
                    echo "<th>Estado</th>";
                    echo "<th>Facturado</th>";
                    echo "<th>Fecha</th>";
                    echo "</tr>";
                    echo "</thead>";
                    echo "<tbody>";
                    
                    foreach ($pedidos as $pedido) {
                        echo "<tr>";
                        echo "<td>{$pedido['id']}</td>";
                        echo "<td>{$pedido['numero_pedido']}</td>";
                        echo "<td>{$pedido['mesero_id']}</td>";
                        echo "<td>{$pedido['mesa_id']}</td>";
                        echo "<td>{$pedido['estado']}</td>";
                        echo "<td>{$pedido['facturado']}</td>";
                        echo "<td><small>{$pedido['fecha_pedido']}</small></td>";
                        echo "</tr>";
                    }
                    
                    echo "</tbody></table>";
                } else {
                    echo "<div class='alert alert-info'>No hay pedidos previos para la mesa 10</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <hr>
    <div class="row">
        <div class="col-md-3">
            <a href="index.php?action=registroPmesa&ida=10" class="btn btn-primary btn-block">🪑 Ver Mesa 10</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=registrarDetalleFactura&ida=10" class="btn btn-warning btn-block">💰 Intentar Facturar</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=mesa" class="btn btn-info btn-block">🏠 Volver a Mesas</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=verificacion_final_facturacion" class="btn btn-success btn-block">📊 Verificación Final</a>
        </div>
    </div>
</div>

</body>
</html>
