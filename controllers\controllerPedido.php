
<?php 
	ob_start();
	class controllerPedido extends MvcController
		{
			
			##PEDIDO  id, mesero_id, facturado
			#----------------------------------------------				
				#REGISTRO DE PEDIDO
				#------------------------------------
					public function registroPedidoController()
						{
							if(isset($_POST["mesero_idPedidoRegistro"]))
								{	
									$datosController =array('mesero_id'=>$_POST["mesero_idPedidoRegistro"],
															'facturado'=>$_POST["facturadoPedidoRegistro"]);
									//echo "<script>alert('Entro Controller ".$datosController["mesero_idPedidoRegistro"]." no')</script>";	
									$respuesta = DatosPedido::registroPedidoModel($datosController, "pedidos");
									if($respuesta == "success")
										{	header("location:index.php?action=okPe");	}
									else
										{	header("location:index.php?action=registroPmesa");	}
								}
						}

				

				#VISTA DE PEDIDO
				#------------------------------------
					public function vistaPedidoController()
						{
							$respuesta = DatosPedido::vistaPedidoModel("pedidos");
							foreach($respuesta as $row => $item)
								{
								echo'<tr>						
										<td>'.$item["mesero_id"].'</td>																	
										<td>'.$item["facturado"].'</td>																	
										<td><a href="index.php?action=editarPedido&id='.$item["id"].'">Editar</a></td>
										<td><a href="index.php?action=pedido&idBorrar='.$item["id"].'">Borrar</a></td>
									</tr>';
								}
						}

				#EDITAR PEDIDO
				#------------------------------------
					public function editarPedidoController()
						{
							$datosController = $_GET["id"];
							$respuesta = DatosPedido::editarPedidoModel($datosController, "pedidos");
							echo' <input type="text" value="'.$respuesta["mesero_id"].'" name="mesero_idEditar" required>									 
							 <input type="text" value="'.$respuesta["facturado"].'" name="facturadoEditar" > 				
							 <input type="hidden" value="'.$respuesta["id"].'" name="idEditar" required> 				
								 <input type="submit" value="Actualizar">';
						}

				#ACTUALIZAR PEDIDO
				#------------------------------------
					public function actualizarPedidoController()
						{//echo "<script>alert('Entro Controller Actualizar Mesa')</script>";
							if(isset($_POST["mesero_idEditar"]))
								{
									$datosController = array(  "mesero_id"=>$_POST["mesero_idEditar"],																	
																"facturado"=>$_POST["facturadoEditar"],				
																"id"=>$_POST["idEditar"]);				
									$respuesta = DatosPedido::actualizarPedidoModel($datosController, "pedidos");
									if($respuesta == "success")
										{	header("location:index.php?action=cambioPe");	}
									else
										{	echo "error";	}
								}				
						}

				#BORRAR PEDIDO
				#------------------------------------
					public function borrarPedidoController()
						{
							if(isset($_GET["idBorrar"]))
								{
									$datosController = $_GET["idBorrar"];				
									$respuesta = DatosPedido::borrarPedidoModel($datosController, "pedidos");
									if($respuesta == "success")
										{	header("location:index.php?action=pedido");	}
								}
						}			
			#--------------------------------- 

		}			
			