<?php
session_start();

echo "<h2>🧪 Test Copia de Factura Corregida</h2>";

// Incluir archivos necesarios
require_once "../../models/conexion.php";

$factura_test = isset($_GET['factura']) ? $_GET['factura'] : 376;

echo "<p><strong>Factura de prueba:</strong> $factura_test</p>";

try {
    $db = Conexion::conectar();
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 1. Obtener datos de la factura
    echo "<h3>1. 📄 Datos de la Factura</h3>";
    $sql = "SELECT 
                v.id as factura_id,
                v.pedidos_id,
                v.total,
                v.fecha_venta,
                v.mesa,
                p.numero_pedido
            FROM ventas v
            LEFT JOIN pedidos p ON v.pedidos_id = p.id
            WHERE v.id = ?";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([$factura_test]);
    $factura = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$factura) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<strong>❌ Factura no encontrada:</strong> $factura_test";
        echo "</div>";
        exit;
    }
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><td><strong>ID Factura</strong></td><td>{$factura['factura_id']}</td></tr>";
    echo "<tr><td><strong>Pedido ID</strong></td><td>{$factura['pedidos_id']}</td></tr>";
    echo "<tr><td><strong>Número Pedido</strong></td><td>{$factura['numero_pedido']}</td></tr>";
    echo "<tr><td><strong>Mesa</strong></td><td>{$factura['mesa']}</td></tr>";
    echo "<tr><td><strong>Total</strong></td><td style='background: #ffeb3b;'>$" . number_format($factura['total']) . "</td></tr>";
    echo "<tr><td><strong>Fecha</strong></td><td>{$factura['fecha_venta']}</td></tr>";
    echo "</table>";
    
    // 2. Comparar método ANTERIOR vs NUEVO
    echo "<h3>2. 🔍 Comparación: Método Anterior vs Nuevo</h3>";
    
    // MÉTODO ANTERIOR (sin agrupar)
    echo "<h4>❌ Método ANTERIOR (Sin Agrupar)</h4>";
    $sqlAnterior = "SELECT 
                        pr.id as producto_id,
                        pr.nombre as producto_nombre,
                        pr.codigo as producto_codigo,
                        ppm.cantidad,
                        ppm.precio,
                        ppm.descuento,
                        (ppm.cantidad * ppm.precio) as subtotal_producto,
                        ((ppm.cantidad * ppm.precio) * (ppm.descuento / 100)) as descuento_aplicado
                     FROM pedido_productos_mesa ppm
                     JOIN productos pr ON ppm.productos_id = pr.id
                     WHERE ppm.pedidos_id = ?
                     ORDER BY pr.nombre";
    
    $stmtAnterior = $db->prepare($sqlAnterior);
    $stmtAnterior->execute([$factura['pedidos_id']]);
    $productosAnterior = $stmtAnterior->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($productosAnterior)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; font-size: 12px;'>";
        echo "<tr style='background: #ffcdd2;'>";
        echo "<th>Código</th><th>Producto</th><th>Cantidad</th><th>Precio</th><th>Subtotal</th>";
        echo "</tr>";
        
        $totalAnterior = 0;
        $registrosAnterior = 0;
        
        foreach ($productosAnterior as $producto) {
            $subtotalConDescuento = $producto['subtotal_producto'] - $producto['descuento_aplicado'];
            $totalAnterior += $subtotalConDescuento;
            $registrosAnterior++;
            
            echo "<tr>";
            echo "<td>{$producto['producto_codigo']}</td>";
            echo "<td>{$producto['producto_nombre']}</td>";
            echo "<td style='text-align: center;'>{$producto['cantidad']}</td>";
            echo "<td style='text-align: right;'>$" . number_format($producto['precio']) . "</td>";
            echo "<td style='text-align: right;'>$" . number_format($subtotalConDescuento) . "</td>";
            echo "</tr>";
        }
        
        echo "<tr style='background: #ffeb3b; font-weight: bold;'>";
        echo "<td colspan='4'>TOTAL ANTERIOR ($registrosAnterior registros)</td>";
        echo "<td style='text-align: right;'>$" . number_format($totalAnterior) . "</td>";
        echo "</tr>";
        echo "</table>";
    }
    
    // MÉTODO NUEVO (agrupado)
    echo "<h4>✅ Método NUEVO (Agrupado)</h4>";
    $sqlNuevo = "SELECT 
                        pr.id as producto_id,
                        pr.nombre as producto_nombre,
                        pr.codigo as producto_codigo,
                        SUM(ppm.cantidad) as cantidad,
                        ppm.precio,
                        AVG(ppm.descuento) as descuento,
                        (SUM(ppm.cantidad) * ppm.precio) as subtotal_producto,
                        ((SUM(ppm.cantidad) * ppm.precio) * (AVG(ppm.descuento) / 100)) as descuento_aplicado
                     FROM pedido_productos_mesa ppm
                     JOIN productos pr ON ppm.productos_id = pr.id
                     WHERE ppm.pedidos_id = ?
                     GROUP BY pr.id, pr.nombre, pr.codigo, ppm.precio, ppm.descuento
                     ORDER BY pr.nombre";
    
    $stmtNuevo = $db->prepare($sqlNuevo);
    $stmtNuevo->execute([$factura['pedidos_id']]);
    $productosNuevo = $stmtNuevo->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($productosNuevo)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; font-size: 12px;'>";
        echo "<tr style='background: #c8e6c9;'>";
        echo "<th>Código</th><th>Producto</th><th>Cantidad Total</th><th>Precio</th><th>Subtotal</th>";
        echo "</tr>";
        
        $totalNuevo = 0;
        $registrosNuevo = 0;
        
        foreach ($productosNuevo as $producto) {
            $subtotalConDescuento = $producto['subtotal_producto'] - $producto['descuento_aplicado'];
            $totalNuevo += $subtotalConDescuento;
            $registrosNuevo++;
            
            echo "<tr>";
            echo "<td>{$producto['producto_codigo']}</td>";
            echo "<td>{$producto['producto_nombre']}</td>";
            echo "<td style='text-align: center; background: #e8f5e8;'><strong>{$producto['cantidad']}</strong></td>";
            echo "<td style='text-align: right;'>$" . number_format($producto['precio']) . "</td>";
            echo "<td style='text-align: right;'>$" . number_format($subtotalConDescuento) . "</td>";
            echo "</tr>";
        }
        
        echo "<tr style='background: #4caf50; color: white; font-weight: bold;'>";
        echo "<td colspan='4'>TOTAL NUEVO ($registrosNuevo registros)</td>";
        echo "<td style='text-align: right;'>$" . number_format($totalNuevo) . "</td>";
        echo "</tr>";
        echo "</table>";
    }
    
    // 3. Comparación final
    echo "<h3>3. ⚖️ Comparación Final</h3>";
    
    $diferenciaTotales = $totalAnterior - $totalNuevo;
    $diferenciaRegistros = $registrosAnterior - $registrosNuevo;
    $diferenciaConFactura = $factura['total'] - $totalNuevo;
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><td><strong>Total Factura Original</strong></td><td style='background: #ffeb3b;'>$" . number_format($factura['total']) . "</td></tr>";
    echo "<tr><td><strong>Total Método Anterior</strong></td><td style='background: #ffcdd2;'>$" . number_format($totalAnterior) . "</td></tr>";
    echo "<tr><td><strong>Total Método Nuevo</strong></td><td style='background: #c8e6c9;'>$" . number_format($totalNuevo) . "</td></tr>";
    echo "<tr><td><strong>Registros Anterior</strong></td><td>$registrosAnterior</td></tr>";
    echo "<tr><td><strong>Registros Nuevo</strong></td><td>$registrosNuevo</td></tr>";
    echo "<tr><td><strong>Reducción de registros</strong></td><td><strong>$diferenciaRegistros</strong></td></tr>";
    
    if ($diferenciaConFactura == 0) {
        echo "<tr><td><strong>Coincidencia con factura</strong></td><td style='background: #4caf50; color: white;'><strong>✅ PERFECTO ($0 diferencia)</strong></td></tr>";
    } else {
        echo "<tr><td><strong>Diferencia con factura</strong></td><td style='background: #f44336; color: white;'><strong>❌ $" . number_format($diferenciaConFactura) . "</strong></td></tr>";
    }
    echo "</table>";
    
    // 4. Resultado
    if ($diferenciaConFactura == 0 && $diferenciaRegistros > 0) {
        echo "<div style='background: #c8e6c9; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>🎉 ¡CORRECCIÓN EXITOSA!</h4>";
        echo "<ul>";
        echo "<li>✅ <strong>Total coincide</strong> perfectamente con la factura original</li>";
        echo "<li>✅ <strong>Productos agrupados</strong> correctamente (reducción de $diferenciaRegistros registros)</li>";
        echo "<li>✅ <strong>Copia de factura</strong> ahora muestra los datos correctos</li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>⚠️ Revisar Resultados</h4>";
        echo "<p>Verifica si los totales coinciden y si la agrupación es correcta.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<strong>❌ Error:</strong> " . $e->getMessage();
    echo "</div>";
}

?>

<style>
table {
    font-size: 12px;
    width: 100%;
}
th {
    background-color: #f5f5f5;
    padding: 8px;
    text-align: left;
}
td {
    padding: 6px;
    border: 1px solid #ddd;
}
</style>

<div style="margin: 20px 0; padding: 15px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px;">
    <h4>🔧 Corrección Implementada en Copia de Factura</h4>
    <p><strong>Archivo modificado:</strong> <code>views/modules/ajaxFacturaBuscar.php</code></p>
    <p><strong>Cambio:</strong> Agregado <code>GROUP BY</code> y <code>SUM(cantidad)</code> para agrupar productos iguales en la copia de factura.</p>
    <p><strong>Resultado:</strong> Ahora la copia de factura muestra los mismos totales y productos agrupados que la factura original.</p>
</div>

<div style="margin: 20px 0; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;">
    <h4>🧪 Probar Copia de Factura</h4>
    <p>Para probar la corrección:</p>
    <ol>
        <li>Ve a <strong>Factura Copia</strong> en el menú</li>
        <li>Busca la factura <strong>376</strong> o <strong>380</strong></li>
        <li>Verifica que los productos aparezcan agrupados</li>
        <li>Confirma que el total coincida con la factura original</li>
    </ol>
</div>
