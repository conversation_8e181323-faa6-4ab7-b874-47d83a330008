<?php
// Script para crear la tabla de logs de impresión
echo "<h1>🗄️ Crear Tabla de Logs de Impresión</h1>";
echo "<p><strong>Crear la tabla necesaria para registrar las impresiones por categorías</strong></p>";

try {
    require_once '../../models/conexion.php';
    $conexion = new Conexion();
    $pdo = $conexion->conectar();
    
    echo "<div style='background-color: #d4edda; padding: 10px; border-left: 4px solid #28a745; margin: 10px 0;'>";
    echo "<p>✅ <strong>Conexión a base de datos exitosa</strong></p>";
    echo "</div>";
    
    // SQL para crear la tabla
    $sql = "
    CREATE TABLE IF NOT EXISTS logs_impresion_pedidos (
        id INT AUTO_INCREMENT PRIMARY KEY,
        pedido_id INT NOT NULL,
        categoria VARCHAR(50) NOT NULL,
        metodo VARCHAR(100) NOT NULL DEFAULT 'ventana_impresion',
        success BOOLEAN NOT NULL DEFAULT TRUE,
        ip_cliente VARCHAR(45),
        datos_enviados TEXT,
        fecha_hora TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_pedido_categoria (pedido_id, categoria),
        INDEX idx_fecha (fecha_hora)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    COMMENT = 'Logs de impresión por categorías para pedidos del restaurante'
    ";
    
    $pdo->exec($sql);
    
    echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0;'>";
    echo "<h4>✅ Tabla Creada Exitosamente</h4>";
    echo "<p>La tabla 'logs_impresion_pedidos' ha sido creada o ya existía.</p>";
    echo "</div>";
    
    // Verificar la estructura de la tabla
    $stmt = $pdo->query("DESCRIBE logs_impresion_pedidos");
    $columnas = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>📋 Estructura de la Tabla</h2>";
    echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<table style='width: 100%; border-collapse: collapse;'>";
    echo "<tr style='background-color: #e9ecef;'>";
    echo "<th style='border: 1px solid #ddd; padding: 8px; text-align: left;'>Campo</th>";
    echo "<th style='border: 1px solid #ddd; padding: 8px; text-align: left;'>Tipo</th>";
    echo "<th style='border: 1px solid #ddd; padding: 8px; text-align: left;'>Nulo</th>";
    echo "<th style='border: 1px solid #ddd; padding: 8px; text-align: left;'>Clave</th>";
    echo "<th style='border: 1px solid #ddd; padding: 8px; text-align: left;'>Por Defecto</th>";
    echo "</tr>";
    
    foreach ($columnas as $columna) {
        echo "<tr>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>{$columna['Field']}</strong></td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$columna['Type']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$columna['Null']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$columna['Key']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$columna['Default']}</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    echo "</div>";
    
    // Verificar si hay datos
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM logs_impresion_pedidos");
    $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    echo "<div style='background-color: #e7f3ff; padding: 15px; border-left: 4px solid #007bff; margin: 15px 0;'>";
    echo "<h4>📊 Estado de la Tabla</h4>";
    echo "<p><strong>Registros actuales:</strong> $total</p>";
    echo "<p><strong>Estado:</strong> Lista para usar</p>";
    echo "</div>";
    
    // Insertar un registro de prueba
    if (isset($_POST['insertar_prueba'])) {
        $stmt = $pdo->prepare("
            INSERT INTO logs_impresion_pedidos 
            (pedido_id, categoria, metodo, success, ip_cliente) 
            VALUES 
            (999, 'test', 'prueba_sistema', 1, ?)
        ");
        $stmt->execute([$_SERVER['REMOTE_ADDR']]);
        
        echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0;'>";
        echo "<h4>✅ Registro de Prueba Insertado</h4>";
        echo "<p>Se ha insertado un registro de prueba en la tabla.</p>";
        echo "</div>";
        
        // Actualizar el conteo
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM logs_impresion_pedidos");
        $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        echo "<p><strong>Registros actuales:</strong> $total</p>";
    }
    
    // Mostrar últimos registros si existen
    if ($total > 0) {
        $stmt = $pdo->query("
            SELECT * FROM logs_impresion_pedidos 
            ORDER BY fecha_hora DESC 
            LIMIT 5
        ");
        $registros = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h2>📋 Últimos Registros</h2>";
        echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<table style='width: 100%; border-collapse: collapse; font-size: 12px;'>";
        echo "<tr style='background-color: #e9ecef;'>";
        echo "<th style='border: 1px solid #ddd; padding: 6px;'>ID</th>";
        echo "<th style='border: 1px solid #ddd; padding: 6px;'>Pedido</th>";
        echo "<th style='border: 1px solid #ddd; padding: 6px;'>Categoría</th>";
        echo "<th style='border: 1px solid #ddd; padding: 6px;'>Método</th>";
        echo "<th style='border: 1px solid #ddd; padding: 6px;'>Éxito</th>";
        echo "<th style='border: 1px solid #ddd; padding: 6px;'>IP</th>";
        echo "<th style='border: 1px solid #ddd; padding: 6px;'>Fecha</th>";
        echo "</tr>";
        
        foreach ($registros as $registro) {
            echo "<tr>";
            echo "<td style='border: 1px solid #ddd; padding: 6px;'>{$registro['id']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 6px;'>{$registro['pedido_id']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 6px;'>{$registro['categoria']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 6px;'>{$registro['metodo']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 6px;'>" . ($registro['success'] ? '✅' : '❌') . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 6px;'>{$registro['ip_cliente']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 6px;'>" . date('d/m/Y H:i:s', strtotime($registro['fecha_hora'])) . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
    echo "<h4>❌ Error</h4>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Archivo:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Línea:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

// Formulario para insertar registro de prueba
echo "<h2>🧪 Pruebas</h2>";
echo "<form method='POST' style='background-color: #fff3cd; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>🧪 Insertar Registro de Prueba</h4>";
echo "<p>Insertar un registro de prueba para verificar que la tabla funciona correctamente</p>";
echo "<button type='submit' name='insertar_prueba' style='background-color: #ffc107; color: #212529; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold;'>🧪 Insertar Prueba</button>";
echo "</form>";

// Información sobre la tabla
echo "<h2>💡 Información de la Tabla</h2>";
echo "<div style='background-color: #e7f3ff; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>🗄️ Propósito de la Tabla:</h4>";
echo "<ul>";
echo "<li><strong>pedido_id:</strong> ID del pedido que se imprimió</li>";
echo "<li><strong>categoria:</strong> Categoría impresa (bar, cocina, asados)</li>";
echo "<li><strong>metodo:</strong> Método de impresión utilizado</li>";
echo "<li><strong>success:</strong> Si la impresión fue exitosa</li>";
echo "<li><strong>ip_cliente:</strong> IP del dispositivo que solicitó la impresión</li>";
echo "<li><strong>fecha_hora:</strong> Timestamp de cuándo se realizó la impresión</li>";
echo "</ul>";

echo "<h4>📊 Beneficios:</h4>";
echo "<ul>";
echo "<li>🔍 <strong>Trazabilidad:</strong> Saber qué se imprimió, cuándo y desde dónde</li>";
echo "<li>📈 <strong>Estadísticas:</strong> Analizar patrones de impresión</li>";
echo "<li>🐛 <strong>Debugging:</strong> Identificar problemas de impresión</li>";
echo "<li>📋 <strong>Auditoría:</strong> Registro completo de todas las impresiones</li>";
echo "</ul>";
echo "</div>";

// Botones de navegación
echo "<p style='margin-top: 30px;'>";
echo "<a href='test_registroPmesa_integrado.php' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>✅ Test Integración</a>";
echo "<a href='demo_impresion_categorias.php' style='background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🎯 Demo</a>";
echo "<a href='../../index.php?action=registroPmesa&ida=1' style='background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🪑 Mesa Real</a>";
echo "</p>";
?>
