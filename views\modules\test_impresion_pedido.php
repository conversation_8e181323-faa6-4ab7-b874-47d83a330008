<?php
// Test de impresión con pedido real
echo "<h1>🧾 Test de Impresión con Pedido Real</h1>";
echo "<p><strong>Simular impresión de un pedido completo con productos de diferentes categorías</strong></p>";

// Habilitar reporte de errores
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    require_once '../../models/conexion.php';
    $conexion = new Conexion();
    $pdo = $conexion->conectar();
    
    echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0;'>";
    echo "<h4>✅ Conexión a base de datos exitosa</h4>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
    echo "<h4>❌ Error de conexión a base de datos</h4>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
    exit;
}

// Función para simular impresión
function simularImpresion($ip, $puerto, $contenido, $categoria) {
    $fp = @fsockopen($ip, $puerto, $errno, $errstr, 10);
    if ($fp) {
        // Comandos ESC/POS para impresora térmica
        $datos = "\x1B\x40"; // Reset
        $datos .= "\x1B\x61\x01"; // Centrar
        $datos .= "=== MACARENA RESTAURANT ===\n";
        $datos .= "\x1B\x61\x00"; // Alinear izquierda
        $datos .= "Categoria: " . strtoupper($categoria) . "\n";
        $datos .= "Fecha: " . date('Y-m-d H:i:s') . "\n";
        $datos .= "--------------------------------\n";
        $datos .= $contenido;
        $datos .= "--------------------------------\n\n\n";
        $datos .= "\x1D\x56\x42\x00"; // Cortar papel
        
        $resultado = fwrite($fp, $datos);
        fclose($fp);
        return $resultado;
    }
    return false;
}

// Test de impresión de pedido
if (isset($_POST['test_pedido'])) {
    $mesa_numero = $_POST['mesa_numero'];
    $productos_seleccionados = $_POST['productos'] ?? [];
    
    echo "<div style='border: 1px solid #007bff; padding: 20px; margin: 15px 0; border-radius: 8px;'>";
    echo "<h4>🧾 Procesando pedido para Mesa $mesa_numero</h4>";
    
    if (empty($productos_seleccionados)) {
        echo "<p>❌ <strong>Error:</strong> No se seleccionaron productos</p>";
        echo "</div>";
    } else {
        // Agrupar productos por categoría de impresora
        $productos_por_impresora = [];
        
        foreach ($productos_seleccionados as $producto_id) {
            try {
                // Obtener información del producto
                $stmt = $pdo->prepare("SELECT id, nombre, precio, categoria FROM productos WHERE id = :id");
                $stmt->bindParam(":id", $producto_id, PDO::PARAM_INT);
                $stmt->execute();
                $producto = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($producto) {
                    // Buscar a qué impresora va este producto
                    $stmt = $pdo->prepare("SELECT impresora FROM categoria_impresora WHERE categoria = :categoria AND activo = 1");
                    $stmt->bindParam(":categoria", $producto['categoria'], PDO::PARAM_STR);
                    $stmt->execute();
                    $mapeo = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    $impresora_categoria = $mapeo['impresora'] ?? 'cocina'; // Default a cocina
                    
                    if (!isset($productos_por_impresora[$impresora_categoria])) {
                        $productos_por_impresora[$impresora_categoria] = [];
                    }
                    
                    $productos_por_impresora[$impresora_categoria][] = $producto;
                }
                
            } catch (Exception $e) {
                echo "<p>⚠️ Error procesando producto ID $producto_id: " . $e->getMessage() . "</p>";
            }
        }
        
        echo "<p>✅ <strong>Productos agrupados por impresora:</strong></p>";
        echo "<ul>";
        foreach ($productos_por_impresora as $impresora => $productos) {
            echo "<li><strong>$impresora:</strong> " . count($productos) . " productos</li>";
        }
        echo "</ul>";
        
        // Imprimir en cada impresora
        foreach ($productos_por_impresora as $impresora_categoria => $productos) {
            echo "<h5>🖨️ Imprimiendo en: $impresora_categoria</h5>";
            
            try {
                // Buscar configuración de la impresora
                $stmt = $pdo->prepare("SELECT ip, puerto FROM impresoras WHERE categoria = :categoria");
                $stmt->bindParam(":categoria", $impresora_categoria, PDO::PARAM_STR);
                $stmt->execute();
                $impresora = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($impresora) {
                    // Generar contenido para imprimir
                    $contenido = "MESA: $mesa_numero\n";
                    $contenido .= "PEDIDO: P" . str_pad(rand(1000, 9999), 6, '0', STR_PAD_LEFT) . "\n\n";
                    
                    $total = 0;
                    foreach ($productos as $producto) {
                        $cantidad = 1; // Por simplicidad, cantidad 1
                        $subtotal = $producto['precio'] * $cantidad;
                        $total += $subtotal;
                        
                        $contenido .= sprintf("%-20s %2d\n", substr($producto['nombre'], 0, 20), $cantidad);
                        $contenido .= sprintf("  $%s\n", number_format($subtotal, 0));
                    }
                    
                    $contenido .= "\nTOTAL: $" . number_format($total, 0) . "\n";
                    
                    // Intentar imprimir
                    $resultado = simularImpresion($impresora['ip'], $impresora['puerto'], $contenido, $impresora_categoria);
                    
                    if ($resultado) {
                        echo "<div style='background-color: #d4edda; padding: 10px; border-left: 4px solid #28a745; margin: 10px 0;'>";
                        echo "<p>✅ <strong>Impresión exitosa</strong> en {$impresora['ip']}:{$impresora['puerto']}</p>";
                        echo "<p>Bytes enviados: $resultado</p>";
                        echo "</div>";
                    } else {
                        echo "<div style='background-color: #f8d7da; padding: 10px; border-left: 4px solid #dc3545; margin: 10px 0;'>";
                        echo "<p>❌ <strong>Error de impresión</strong> en {$impresora['ip']}:{$impresora['puerto']}</p>";
                        echo "</div>";
                    }
                    
                } else {
                    echo "<p>❌ <strong>Error:</strong> No se encontró configuración para impresora $impresora_categoria</p>";
                }
                
            } catch (Exception $e) {
                echo "<p>❌ <strong>Error:</strong> " . $e->getMessage() . "</p>";
            }
        }
        
        echo "</div>";
    }
}

// Formulario de test
echo "<h2>🧪 Simular Pedido para Impresión</h2>";
echo "<form method='POST' style='background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>🪑 Configurar Pedido de Prueba:</h4>";

echo "<div style='margin: 15px 0;'>";
echo "<label><strong>Número de Mesa:</strong></label><br>";
echo "<input type='number' name='mesa_numero' value='1' min='1' max='50' required style='padding: 8px; border: 1px solid #ccc; border-radius: 3px; width: 100px;'>";
echo "</div>";

echo "<div style='margin: 15px 0;'>";
echo "<label><strong>Productos a incluir:</strong></label><br>";

// Obtener productos disponibles agrupados por categoría
try {
    $stmt = $pdo->query("
        SELECT p.id, p.nombre, p.precio, p.categoria, 
               COALESCE(ci.impresora, 'cocina') as impresora_destino
        FROM productos p
        LEFT JOIN categoria_impresora ci ON p.categoria = ci.categoria AND ci.activo = 1
        ORDER BY p.categoria, p.nombre
        LIMIT 20
    ");
    $productos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($productos) {
        $categoria_actual = '';
        foreach ($productos as $producto) {
            if ($categoria_actual != $producto['categoria']) {
                if ($categoria_actual != '') echo "</div>";
                $categoria_actual = $producto['categoria'];
                echo "<div style='margin: 10px 0; padding: 10px; background-color: #e9ecef; border-radius: 3px;'>";
                echo "<strong>{$producto['categoria']} → {$producto['impresora_destino']}</strong><br>";
            }
            
            echo "<label style='display: block; margin: 5px 0;'>";
            echo "<input type='checkbox' name='productos[]' value='{$producto['id']}' style='margin-right: 8px;'>";
            echo "{$producto['nombre']} - \${$producto['precio']}";
            echo "</label>";
        }
        if ($categoria_actual != '') echo "</div>";
    } else {
        echo "<p>⚠️ No se encontraron productos en la base de datos</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error obteniendo productos: " . $e->getMessage() . "</p>";
}

echo "</div>";

echo "<button type='submit' name='test_pedido' style='background-color: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold;'>🧾 Simular Pedido e Imprimir</button>";
echo "</form>";

// Mostrar configuración actual
echo "<h2>⚙️ Configuración Actual del Sistema</h2>";

echo "<div style='background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h5>🖨️ Impresoras Configuradas:</h5>";
try {
    $stmt = $pdo->query("SELECT categoria, ip, puerto FROM impresoras ORDER BY categoria");
    $impresoras = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($impresoras) {
        echo "<table style='width: 100%; border-collapse: collapse;'>";
        echo "<tr style='background-color: #ffeaa7;'>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Categoría</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>IP</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Puerto</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Estado</th>";
        echo "</tr>";
        
        foreach ($impresoras as $imp) {
            $fp = @fsockopen($imp['ip'], $imp['puerto'], $errno, $errstr, 3);
            $estado = $fp ? "<span style='color: green;'>✅ Online</span>" : "<span style='color: red;'>❌ Offline</span>";
            if ($fp) fclose($fp);
            
            echo "<tr>";
            echo "<td style='border: 1px solid #ddd; padding: 8px; font-weight: bold;'>{$imp['categoria']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$imp['ip']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$imp['puerto']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>$estado</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>❌ No hay impresoras configuradas</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}
echo "</div>";

echo "<div style='background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h5>🔗 Mapeo Categorías → Impresoras:</h5>";
try {
    $stmt = $pdo->query("SELECT categoria, impresora FROM categoria_impresora WHERE activo = 1 ORDER BY categoria");
    $mapeos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($mapeos) {
        echo "<ul>";
        foreach ($mapeos as $mapeo) {
            echo "<li><strong>{$mapeo['categoria']}</strong> → {$mapeo['impresora']}</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>❌ No hay mapeos configurados</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}
echo "</div>";

// Botones de navegación
echo "<p style='margin-top: 30px;'>";
echo "<a href='test_impresion_completo.php' style='background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>← Test Completo</a>";
echo "<a href='index.php?action=registroPmesa&ida=1' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🪑 Mesa Real</a>";
echo "<a href='pantallaCocina?categoria=cocina' style='background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🍳 Pantalla Cocina</a>";
echo "</p>";
?>
