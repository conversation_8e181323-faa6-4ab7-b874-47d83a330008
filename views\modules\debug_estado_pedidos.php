<?php

session_start();

if(!isset($_SESSION["validar"]) || !$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "models/crudPedidosCategorias.php";
require_once "models/conexion.php";

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Debug Estado Pedidos</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
</head>
<body>

<div class="container">
    <h2>🔍 Debug: Estado Actual de Pedidos</h2>
    <hr>
    
    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">📊 Estado actual de todos los pedidos</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                $stmt = Conexion::conectar()->prepare("
                    SELECT p.id, p.numero_pedido, p.estado, p.mesa_id, m.nombre as mesa_nombre,
                           p.fecha_pedido, p.fecha_envio, p.fecha_entrega,
                           COUNT(pvm.productos_id) as total_productos
                    FROM pedidos p
                    LEFT JOIN mesas m ON p.mesa_id = m.id
                    LEFT JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
                    GROUP BY p.id, p.numero_pedido, p.estado, p.mesa_id, m.nombre, p.fecha_pedido, p.fecha_envio, p.fecha_entrega
                    ORDER BY p.id DESC
                    LIMIT 10
                ");
                $stmt->execute();
                $pedidos = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                echo "<table class='table table-striped'>";
                echo "<thead><tr><th>ID</th><th>Número</th><th>Estado</th><th>Mesa</th><th>Productos</th><th>Fecha Pedido</th><th>Fecha Envío</th><th>Fecha Entrega</th></tr></thead>";
                echo "<tbody>";
                
                foreach ($pedidos as $pedido) {
                    $clase = '';
                    switch ($pedido['estado']) {
                        case 'borrador': $clase = 'warning'; break;
                        case 'enviado': $clase = 'info'; break;
                        case 'entregado': $clase = 'success'; break;
                        case 'facturado': $clase = 'default'; break;
                    }
                    
                    echo "<tr class='{$clase}'>";
                    echo "<td>{$pedido['id']}</td>";
                    echo "<td><strong>{$pedido['numero_pedido']}</strong></td>";
                    echo "<td><span class='label label-{$clase}'>{$pedido['estado']}</span></td>";
                    echo "<td>{$pedido['mesa_nombre']}</td>";
                    echo "<td>{$pedido['total_productos']}</td>";
                    echo "<td>" . ($pedido['fecha_pedido'] ? date('H:i', strtotime($pedido['fecha_pedido'])) : '-') . "</td>";
                    echo "<td>" . ($pedido['fecha_envio'] ? date('H:i', strtotime($pedido['fecha_envio'])) : '-') . "</td>";
                    echo "<td>" . ($pedido['fecha_entrega'] ? date('H:i', strtotime($pedido['fecha_entrega'])) : '-') . "</td>";
                    echo "</tr>";
                }
                
                echo "</tbody></table>";
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-warning">
        <div class="panel-heading">
            <h3 class="panel-title">🍺 Pedidos pendientes en BAR</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                $pedidos_bar = DatosPedidosCategorias::obtenerPedidosPendientesModel('bar');
                echo "<p><strong>Total pedidos pendientes en BAR:</strong> " . count($pedidos_bar) . "</p>";
                
                if (count($pedidos_bar) > 0) {
                    echo "<table class='table table-striped'>";
                    echo "<thead><tr><th>ID</th><th>Pedido</th><th>Mesa</th><th>Productos</th><th>Total</th><th>Acción</th></tr></thead>";
                    echo "<tbody>";
                    foreach ($pedidos_bar as $pedido) {
                        echo "<tr>";
                        echo "<td>{$pedido['pedido_id']}</td>";
                        echo "<td>{$pedido['numero_pedido']}</td>";
                        echo "<td>{$pedido['mesa_numero']}</td>";
                        echo "<td>{$pedido['productos_detalle']}</td>";
                        echo "<td>$" . number_format($pedido['total_precio'], 0) . "</td>";
                        echo "<td><button class='btn btn-sm btn-success' onclick='testMarcarEntregado({$pedido['pedido_id']})'>Test Marcar</button></td>";
                        echo "</tr>";
                    }
                    echo "</tbody></table>";
                } else {
                    echo "<div class='alert alert-success'>✅ No hay pedidos pendientes en BAR</div>";
                }
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-success">
        <div class="panel-heading">
            <h3 class="panel-title">🧪 Test específico de marcar entregado</h3>
        </div>
        <div class="panel-body">
            <div id="test-result"></div>
            
            <?php
            if (isset($_POST['test_pedido_id'])) {
                echo "<h4>🧪 Ejecutando test para pedido ID: {$_POST['test_pedido_id']}</h4>";
                
                try {
                    // Verificar que el pedido existe y está en estado 'enviado'
                    $stmt = Conexion::conectar()->prepare("SELECT * FROM pedidos WHERE id = ? AND estado = 'enviado'");
                    $stmt->execute([$_POST['test_pedido_id']]);
                    $pedido = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    if (!$pedido) {
                        echo "<div class='alert alert-danger'>❌ El pedido ID {$_POST['test_pedido_id']} no existe o no está en estado 'enviado'</div>";
                    } else {
                        echo "<div class='alert alert-info'>✅ Pedido encontrado: {$pedido['numero_pedido']} - Estado: {$pedido['estado']}</div>";
                        
                        // Test de la función marcarPedidoEntregadoModel
                        $resultado = DatosPedidosCategorias::marcarPedidoEntregadoModel($_POST['test_pedido_id'], $_SESSION['usuario']);
                        
                        if ($resultado == "success") {
                            echo "<div class='alert alert-success'>✅ Pedido marcado como entregado exitosamente</div>";
                            
                            // Verificar el cambio
                            $stmt = Conexion::conectar()->prepare("SELECT estado, fecha_entrega, usuario_entrega FROM pedidos WHERE id = ?");
                            $stmt->execute([$_POST['test_pedido_id']]);
                            $pedido_actualizado = $stmt->fetch(PDO::FETCH_ASSOC);
                            
                            echo "<div class='alert alert-info'>";
                            echo "<strong>Estado actualizado:</strong><br>";
                            echo "- Estado: {$pedido_actualizado['estado']}<br>";
                            echo "- Fecha entrega: {$pedido_actualizado['fecha_entrega']}<br>";
                            echo "- Usuario entrega: {$pedido_actualizado['usuario_entrega']}";
                            echo "</div>";
                            
                        } else {
                            echo "<div class='alert alert-danger'>❌ Error al marcar pedido: {$resultado}</div>";
                        }
                    }
                    
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>❌ Excepción: " . $e->getMessage() . "</div>";
                }
            }
            ?>
            
            <form method="POST" style="margin-top: 20px;">
                <div class="form-group">
                    <label>ID del pedido a marcar como entregado:</label>
                    <input type="number" name="test_pedido_id" class="form-control" placeholder="Ej: 8" required>
                </div>
                <button type="submit" class="btn btn-warning">🧪 Test Marcar Entregado</button>
            </form>
        </div>
    </div>
    
    <div class="panel panel-primary">
        <div class="panel-heading">
            <h3 class="panel-title">📋 Historial de cambios</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                $stmt = Conexion::conectar()->prepare("
                    SELECT h.*, p.numero_pedido 
                    FROM pedidos_historial h
                    LEFT JOIN pedidos p ON h.pedido_id = p.id
                    ORDER BY h.fecha_cambio DESC
                    LIMIT 10
                ");
                $stmt->execute();
                $historial = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (count($historial) > 0) {
                    echo "<table class='table table-condensed'>";
                    echo "<thead><tr><th>Pedido</th><th>Estado Anterior</th><th>Estado Nuevo</th><th>Usuario</th><th>Fecha</th><th>Observaciones</th></tr></thead>";
                    echo "<tbody>";
                    foreach ($historial as $h) {
                        echo "<tr>";
                        echo "<td>{$h['numero_pedido']}</td>";
                        echo "<td><span class='label label-warning'>{$h['estado_anterior']}</span></td>";
                        echo "<td><span class='label label-success'>{$h['estado_nuevo']}</span></td>";
                        echo "<td>{$h['usuario_id']}</td>";
                        echo "<td>" . date('H:i:s', strtotime($h['fecha_cambio'])) . "</td>";
                        echo "<td>{$h['observaciones']}</td>";
                        echo "</tr>";
                    }
                    echo "</tbody></table>";
                } else {
                    echo "<div class='alert alert-info'>No hay historial de cambios</div>";
                }
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <hr>
    <div class="row">
        <div class="col-md-4">
            <a href="index.php?action=pedidosBarPendientes" class="btn btn-info btn-block">🔙 Volver a Bar</a>
        </div>
        <div class="col-md-4">
            <a href="index.php?action=test_ajax_marcar_entregado" class="btn btn-primary btn-block">🧪 Test AJAX</a>
        </div>
        <div class="col-md-4">
            <a href="index.php?action=pedidosCocinaPendientes" class="btn btn-warning btn-block">🍳 Ir a Cocina</a>
        </div>
    </div>
</div>

<script>
function testMarcarEntregado(pedidoId) {
    document.querySelector('input[name="test_pedido_id"]').value = pedidoId;
    document.querySelector('form').submit();
}
</script>

</body>
</html>
