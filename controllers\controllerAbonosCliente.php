<?php
ob_start();
class controllerAbonosCliente extends <PERSON>vc<PERSON><PERSON>roller
{
	#DEUDAS Y ABONOS DEL CLIENTE
	#BUSCAR TODOS LAS DUEDA DE LOS CLIENTES
	#----------------------------------
	 public function deudaTodoController()
		{	//echo "<script>alert('entro controle buscar abono')</script>";
		 	$respuesta =DatosAbonosCliente::deudaTodoModel();
		 	$total=count($respuesta);
		 	$con =0;
		 	$control =0;
		   foreach($respuesta as $row => $item)
			{ if ($control==0)
				{	//echo "<script>alert('control  cliente:".$item["dcliente_id"]." -total:".$total."')</script>";
					$deuda =DatosAbonosCliente::buscardeudaFacturaModel($item["dcredito_id"]);
					$cliente[$con]= array('dcliente_id' => $item["dcliente_id"],
													 'dcuenta' => $deuda["dcuenta"] ,
													 'cliente' => $item["pnombre"] . $item["pellidos"] ,
													 'pcedula' => $item["pcedula"] ,
													 'dcliente_id' => $item["dcliente_id"]);
					$control++;
				}
			 else
				{	//echo "<script>alert('control else cliente:".$item["dcliente_id"]." -total:".$cliente[$con]['dcliente_id']." -Base:".$cliente[$con]['dcliente_id']."')</script>";
					if ($cliente[$con]['dcliente_id'] == $item["dcliente_id"])
					 {	 //	echo "<script>alert('deuda: ".$cliente[$con]['dcuenta']."')</script>";
					 	$deuda =DatosAbonosCliente::buscardeudaFacturaModel($item["dcredito_id"]);
						$cliente[$con]['dcuenta']= $cliente[$con]['dcuenta'] + $deuda["dcuenta"];
					 }
					else
					 {	//echo "<script>alert('else  cliente:".$item["dcliente_id"]." -cuenta')</script>";
					 	$deuda =DatosAbonosCliente::buscardeudaFacturaModel($item["dcredito_id"]);
					 	//echo " cada deuda: ". $item['dcuenta'];
					 	//echo "<script>alert('deuda: ".$item['dcuenta']."')</script>";
						 	$con++;
						 	$cliente[$con]= array('dcliente_id' => $item["dcliente_id"],
														 'dcuenta' => $deuda["dcuenta"] ,
														 'cliente' => $item["pnombre"] . $item["pellidos"] ,
														 'pcedula' => $item["pcedula"] ,
														 'dcliente_id' => $item["dcliente_id"]);
					 }// echo "<script>alert('contador ".$con."')</script>";
				}
		}
		 for ($i=0; $i <=$con ; $i++)
			{ 	//echo '<br>';		//echo " deuda: ". $cliente[$i]['dcuenta'];
			 $s=$i+1;
			 echo'
			 	<tr>
					<td>'.$s.'</td>
					<td>'.$cliente[$i]["cliente"].'</td>
					<td>'.$cliente[$i]["pcedula"].'</td>
					<td>'.$cliente[$i]["dcuenta"].'</td>
					<td><a href="index.php?action=carteraUsuario&cliente='.$cliente[$i]["pcedula"].'"><button>Ver</button></a> </td>
				</tr>';
				$_SESSION["cedulaD"]=$cliente[$i]["pcedula"];
			}
		}
	#---------------------------------
	##ASIGNAR CLIENTE
	#------------------------------------
	 public function clienteController()
		{	//echo "<script>alert('entro controle')</script>";
		 if(isset($_GET["cliente"]))
			{
				$_SESSION["cedulaD"] = $_GET["cliente"];
				header("location:index.php?action=deudacliente");
			}
		}
	#---------------------------------
	#ABONOS  id, id_personas, turnos_cajeros_id, fecha_bono, descripsion, valor
	#--------------------------------
	 public function abonoClienteController()
		{//echo " <script>  alert('Controles Abonos descripsion: ".$_POST["descripsion"]."  turno: ".$_SESSION["turno"]."  deuda: ".$_GET["id_deuda"]."  valorAbono: ".$_POST["valorAbono"]."')  </script> ";
			//session_start();
			if (isset($_POST["valorAbono"]))
			 {
				$datosController= array('descripcion' => $_POST["descripcion"],
									 	'turno' => $_SESSION["turno"] ,
									 	'deuda' => $_GET["id_deuda"] ,
									 	'valorAbono' => $_POST["valorAbono"]);
				//echo " <script>  alert('descripsion: ".$datosController["descripsion"]."  turno: ".$datosController["turno"]."  deuda: ".$datosController["deuda"]."  valorAbono: ".$datosController["valorAbono"]."')  </script> ";
				$respuesta =DatosAbonosCliente::abonoClienteModel($datosController, "abonos");
				if($respuesta == "success")
					{	////// nuevo 17-01  10:36
						//$vaciarMesa = DatosFacturaAja::cancelarController();
						//header("location:mesa");
						echo' <script>alert("Pago realizado exitosamente");window.open("pdf1","_blank");location.href ="index.php?action=deudacliente";</script>';
					}
				else
					{	echo' <script>alert("No se guardo, intente nuevamente IF");</script>';
						//header("location:index.php?action=okPm");
					}
			 }
			//else echo' <script>alert("error");</script>';
		}
	#---------------------------------
	#BUSCAR DEUDA CLIENTE
	#------------------------------------
	 public function buscardeudaClienteController()
		{	//$cedulaD=$_SESSION["cedulaD"];
			//if($cedulaD>0)
				//{
				//$datosController = $_POST["buscarDeudaCliente"];
				$datosController = $_SESSION["cedulaD"];
				//$datosController = "57461800";
			//	echo "<script>alert('entro Controller Deuda del cliente ".$datosController."')</script>";
				$respuesta = DatosAbonosCliente::buscardeudaClienteModel($datosController);
				if ($respuesta==0)
		 {echo' <script>alert("No aparece deuda, verificar nit");location.href ="index.php?action=buscarDeudacliente";</script>';}
				echo' Sr/a: '.$_SESSION ["cliente"]. ' <br>
				<table border="1" class="table table-hover">
					<thead>
						<tr>
							<th>No Factura</th>
							<th>Fecha</th>
							<th>Valor Factura</th>
							<th>Abonos</th>
							<th>Debe</th>
							<th>Pago</th>
						</tr>
					</thead>
					<tbody>';
				$cont = 0;
			 	$abonos=0;     // Guardar total de todos los abonos
			 	$ValorF=0;     // Guardar total de todos los valor de factura
			 	$debes=0;      // Guardar total de todos los lo q deben
			 	$a=0;      // cantidad de abono
			 foreach ($respuesta as $rows => $item)
				{
				 	$abono = DatosAbonosCliente::abonoModel($item["dcreditoid"]);
				 	$abonoTotal=0; // si tiene abonoS
				 	if ($abono>0)
				 	 {
				 		foreach ($abono as $rows => $value)
				 		 {
				 			$abonoTotal= $abonoTotal + $value["avalor"];
				 			$listaAbonos[$a]= array(	'dcredito_id' => $value['dcredito_id'],
				 										'dabonos_id' =>$value['dabonos_id'] ,
				 										'afecha_bono' => $value['afecha_bono'],
				 										'adescripsion' => $value['adescripsion'],
				 										'avalor' => $value['avalor']
				 									);
				 			$a++;
				 		 }
				 	 }
				 	$debe = $item["ctotal"] -$abonoTotal; //lo q se debe por factura
					echo'<tr>
								<td>'.$item["dcreditoid"].'</td>
								<td>'.$item["cfecha"].'</td>
								<td>'.$item["ctotal"].'</td>
								<td>'.$abonoTotal.'</td>
								<td>'.$debe.'</td>
								<td><a href="index.php?action=acobonoDeuda&id_deuda='.$item["dcreditoid"].'">Abonar</a></td>
							</tr>';
						$abonos = $abonos + $abonoTotal;
						$ValorF = $ValorF + $item["ctotal"];
						$debes = $debes + $debe;
						$_SESSION["deudas"][$cont] = array('id_credito' =>$item["dcreditoid"],
														'ctotal' =>$item["ctotal"]);
					}
									$_SESSION ["cliente"]= $item ["pnombre"]. " ". $item ["papellidos"];
					echo' <tr class="filaTotal">
							<td colspan="2">Total</td>
							<td> '.$ValorF.' </td>
							<td>'.$abonos.'</td>
							<td colspan="2">'.$debes .'</td>
						</tr>
					</tbody>
				</table> ';
				$cont++;
				if ($abono>0)
				 {
				 		echo'
				 			<table border="1" class="table table-hover">
								<caption>Abonos</caption>
								<thead>
									<tr>
										<th>No </th>
										<th>No Factura</th>
										<th>Fecha de Abono </th>
										<th>Descripsion</th>
										<th>Valor</th>
									</tr>
								</thead>
								<tbody> ';
									$c=1;
								 for ($i=0; $i <$a ; $i++)
									{
										echo'<tr >
												<td> '.$c.' </td>
												<td>'.$listaAbonos[$i]["dcredito_id"].'</td>

												<td>'.$listaAbonos[$i]["afecha_bono"].'</td>
												<td>'.$listaAbonos[$i]["adescripsion"].'</td>
												<td>$'.$listaAbonos[$i]["avalor"].'</td>
											</tr>';
										$c++;
									}
									/*foreach ($abono as $rows => $ite)
			 		 				 { //$ite["aturnos_cajeros_id"]
			 		 				 	echo'<tr >
												<td> '.$c.' </td>
												<td>'.$ite["aid"].'</td>
												<td>'.$ite["afecha_bono"].'</td>
												<td>'.$ite["adescripsion"].'</td>
												<td>'.$ite["avalor"].'</td>
											</tr>';
											$c++;
			 		 				 }
									*/echo'<tr class="filaTotal">
										<td colspan="4">Total</td>
										<td>$'.$abonos.'</td>
									</tr>
								</tbody>
							</table> ';
			 		 }
		}
	#---------------------------------------------<input type="submit" value="Abono">
	#BUSCAR ABONOS
	#----------------------------------
	 public function buscarAbonosCController()
		{	//echo "<script>alert('entro controle buscar abono')</script>";
		 	$datosController= $_GET["id_deuda"];
		 	$respuesta =DatosAbonosCliente::abonoModel($datosController);
		 if ($respuesta>0)
			{
		 	 $i=0;
		 	 echo '
		 	  <table border="1" class="table">
		 		<caption >ABONOS DE LA FACTURA</caption>
		 		<thead>
		 			<tr>
		 				<th>Descripsión</th>
		 				<th>Fecha</th>
		 				<th>No</th>
		 				<th>Valor</th>
		 			</tr>
		 		</thead>
		 		<tbody>';
		 $abonoT=0;
		 foreach ($respuesta as $rows => $item)
			{ $i++;
			 $abonoTc= $abonoT+$i ;
		 	 $abonoT= $abonoT+$item ["avalor"] ;
		 	 echo'
		 		<tr>
 					<td>'.$item ["adescripsion"] .'</td>
 					<td>'.$item ["afecha_bono"] .'</td>
 					<td>'.$i.'</td>
 					<td>'.$item ["avalor"] .'</td>
	 			</tr>';
		 			 }
		 	 echo'
		 	 	<tr class="filaTotal">
 					<td colspan="2">Total Abono</td>
 					<td >'.$i.' </td>
 					<td >$'.$abonoT.' </td>
 				</tr>
 			</tbody>
 		 </table>';
		 	}
		}
	#---------------------------------
	#BORRAR PEDIDO MESA VENTA
	#------------------------------------
	 public function borrarAbonosClienteController()
		{	//echo "<script>alert('entro controle')</script>";
		 if(isset($_GET["idBorrar"]))
			{	//echo "<script>alert('entro controle IF')</script>";
				$datosController =array('fecha_hora' => $_GET["fecha_horaBorrar"],
									'productos_id' => $_GET["idBorrar"]);
				$respuesta = DatosAbonosCliente::borrarAbonosClienteModel($datosController, "producto_vendido_mesa");
				if($respuesta == "success")
					{	header("location:index.php?action=registroPmesa");	}
			}
		}
	#---------------------------------
	#ASIGNAR CLIENTE
	#----------------------------------
	 public function asignarClienteController()
		{	//echo "<script>alert('entro controle')</script>";
		 	//session_start();
		 if(isset($_POST["buscarDeudaCliente"]))
			{
			 	$_SESSION["cedulaD"]= $_POST['buscarDeudaCliente'];
			 	header("location:deudacliente");
			 }
		}
	#---------------------------------

}
