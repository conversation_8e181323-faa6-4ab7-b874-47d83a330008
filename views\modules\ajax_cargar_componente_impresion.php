<?php
// AJAX para cargar el componente de impresión por categorías
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo "<h4>❌ Método no permitido</h4>";
    exit;
}

$input = file_get_contents('php://input');
$data = json_decode($input, true);

if (!$data || !isset($data['pedido_id'])) {
    echo "<h4>❌ Datos incompletos</h4>";
    exit;
}

try {
    require_once 'componente_impresion_categorias.php';
    
    $pedido_id = intval($data['pedido_id']);
    $numero_pedido = $data['numero_pedido'] ?? "P$pedido_id";
    $mesa_id = $data['mesa_id'] ?? 1;
    $mesero = $data['mesero'] ?? 1;
    
    echo "<h4>🧪 Test del Componente de Impresión</h4>";
    echo "<p><strong>Pedido:</strong> $numero_pedido | <strong>Mesa:</strong> $mesa_id</p>";

    // Primero diagnosticar qué productos tiene el pedido
    require_once '../../models/conexion.php';
    $conexion = new Conexion();
    $pdo = $conexion->conectar();

    echo "<div style='background-color: #e7f3ff; padding: 10px; border-left: 4px solid #007bff; margin: 10px 0;'>";
    echo "<h5>🔍 Diagnóstico del Pedido</h5>";

    // Verificar si el pedido existe
    $stmt = $pdo->prepare("SELECT * FROM pedidos WHERE id = :pedido_id");
    $stmt->bindParam(":pedido_id", $pedido_id, PDO::PARAM_INT);
    $stmt->execute();
    $pedido_info = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($pedido_info) {
        echo "<p>✅ <strong>Pedido encontrado:</strong> {$pedido_info['numero_pedido']} - Estado: {$pedido_info['estado']}</p>";
    } else {
        echo "<p>❌ <strong>Pedido no encontrado en tabla 'pedidos'</strong></p>";
    }

    // Verificar productos del pedido
    $stmt = $pdo->prepare("
        SELECT
            p.id as producto_id,
            p.nombre as producto_nombre,
            p.categoria as categoria_producto,
            pvm.cantidad,
            p.precio as precio_unitario,
            CASE
                WHEN p.categoria IN ('bebidas', 'cervezas', 'licores', 'vinos', 'cocteles', 'refrescos', 'jugos', 'agua', 'bar') THEN 'bar'
                WHEN p.categoria IN ('carnes', 'parrilla', 'asados', 'pescados', 'mariscos') THEN 'asados'
                ELSE 'cocina'
            END as categoria_impresora
        FROM producto_vendido_mesa pvm
        INNER JOIN productos p ON pvm.productos_id = p.id
        WHERE pvm.pedidos_id = :pedido_id
    ");
    $stmt->bindParam(":pedido_id", $pedido_id, PDO::PARAM_INT);
    $stmt->execute();
    $productos_raw = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo "<p><strong>Productos encontrados:</strong> " . count($productos_raw) . "</p>";

    if (!empty($productos_raw)) {
        echo "<ul style='font-size: 12px;'>";
        foreach ($productos_raw as $prod) {
            echo "<li>{$prod['producto_nombre']} (Categoría: {$prod['categoria_producto']} → {$prod['categoria_impresora']}) x{$prod['cantidad']}</li>";
        }
        echo "</ul>";

        // Mostrar agrupación por categoría de impresora
        $categorias_agrupadas = [];
        foreach ($productos_raw as $prod) {
            $cat = $prod['categoria_impresora'];
            if (!isset($categorias_agrupadas[$cat])) {
                $categorias_agrupadas[$cat] = 0;
            }
            $categorias_agrupadas[$cat] += $prod['cantidad'];
        }

        echo "<p><strong>Agrupación por impresora:</strong></p>";
        echo "<ul style='font-size: 12px;'>";
        foreach ($categorias_agrupadas as $cat => $total) {
            echo "<li><strong>" . strtoupper($cat) . ":</strong> $total productos</li>";
        }
        echo "</ul>";

    } else {
        echo "<p>❌ <strong>No se encontraron productos para este pedido</strong></p>";

        // Verificar si existe la tabla producto_vendido_mesa
        $stmt = $pdo->query("SHOW TABLES LIKE 'producto_vendido_mesa'");
        if (!$stmt->fetch()) {
            echo "<p>❌ <strong>Tabla 'producto_vendido_mesa' no existe</strong></p>";
        } else {
            echo "<p>✅ <strong>Tabla 'producto_vendido_mesa' existe</strong></p>";

            // Verificar si hay productos en general para este pedido
            $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM producto_vendido_mesa WHERE pedidos_id = :pedido_id");
            $stmt->bindParam(":pedido_id", $pedido_id, PDO::PARAM_INT);
            $stmt->execute();
            $total_productos = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            echo "<p>Total productos en producto_vendido_mesa para este pedido: $total_productos</p>";
        }
    }
    echo "</div>";

    echo "</div>";

    // Mostrar el componente de botones
    echo "<div style='background-color: #e7f3ff; padding: 15px; border-left: 4px solid #007bff; margin: 15px 0;'>";
    echo "<h5>🖨️ Botones de Impresión por Categorías</h5>";

    // Obtener datos del pedido para mostrar botones
    $datos_pedido = obtenerCategoriasDelPedidoComponente($pedido_id);

    echo "<p><strong>Debug datos_pedido:</strong></p>";
    echo "<pre style='font-size: 11px; background: #f8f9fa; padding: 10px; border-radius: 3px;'>";
    echo "Total categorías encontradas: " . count($datos_pedido['categorias'] ?? []) . "\n";
    if (isset($datos_pedido['error'])) {
        echo "Error: " . $datos_pedido['error'] . "\n";
    }
    if (!empty($datos_pedido['categorias'])) {
        foreach ($datos_pedido['categorias'] as $cat => $info) {
            echo "- $cat: {$info['total_productos']} productos\n";
        }
    }
    echo "</pre>";

    if (!empty($datos_pedido['categorias'])) {
        echo "<div style='display: flex; flex-wrap: wrap; gap: 10px; margin: 15px 0;'>";

        foreach ($datos_pedido['categorias'] as $categoria => $info) {
            $color_categoria = [
                'bar' => '#17a2b8',
                'cocina' => '#28a745',
                'asados' => '#dc3545'
            ];

            $color = $color_categoria[$categoria] ?? '#6c757d';

            echo "<button onclick='imprimirCategoria(\"$pedido_id\", \"$categoria\", \"$numero_pedido\", \"$mesa_id\", \"$mesero\", this)' ";
            echo "style='background-color: $color; color: white; padding: 10px 16px; border: none; border-radius: 5px; cursor: pointer; font-size: 13px; font-weight: bold;'>";
            echo "🖨️ {$info['nombre']} ({$info['total_productos']})";
            echo "</button>";
        }

        echo "</div>";

        echo "<p style='font-size: 12px; color: #6c757d;'>Haz clic en cualquier botón para imprimir esa categoría específica</p>";

    } else {
        echo "<p>❌ <strong>No se pudieron generar botones</strong> - No hay categorías disponibles</p>";
        if (isset($datos_pedido['error'])) {
            echo "<p><strong>Error:</strong> {$datos_pedido['error']}</p>";
        }
    }

    echo "</div>";

    // JavaScript para manejar la impresión
    echo "<script>
    // Definir función en el scope global
    window.imprimirCategoria = function(pedidoId, categoria, numeroPedido, mesaNumero, mesero, buttonElement) {
        // Si no se pasa el elemento del botón, intentar encontrarlo
        if (!buttonElement) {
            buttonElement = event ? event.target : null;
        }

        // Mostrar loading si tenemos referencia al botón
        if (buttonElement) {
            buttonElement.innerHTML = '⏳ Preparando...';
            buttonElement.disabled = true;
        }

        // Hacer petición AJAX para obtener contenido
        // Usar ruta relativa desde la raíz del sitio
        fetch('./views/modules/ajax_impresion_categoria.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                pedido_id: pedidoId,
                categoria: categoria,
                numero_pedido: numeroPedido,
                mesa_numero: mesaNumero,
                mesero: mesero
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                abrirVentanaImpresion(data.contenido, categoria);

                // Mostrar mensaje de éxito
                var mensaje = document.createElement('div');
                mensaje.style.cssText = 'background-color: #d4edda; color: #155724; padding: 10px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #28a745;';
                mensaje.innerHTML = '✅ <strong>Impresión enviada:</strong> ' + categoria.toUpperCase() + ' - ' + data.total_productos + ' productos';

                if (buttonElement && buttonElement.parentNode) {
                    buttonElement.parentNode.appendChild(mensaje);
                    setTimeout(() => mensaje.remove(), 5000);
                }
            } else {
                alert('Error: ' + data.error);
            }

            // Restaurar botón
            if (buttonElement) {
                var nombreCategoria = categoria.charAt(0).toUpperCase() + categoria.slice(1);
                var iconos = {bar: '🍺', cocina: '🍳', asados: '🥩'};
                buttonElement.innerHTML = '🖨️ ' + (iconos[categoria] || '') + ' ' + nombreCategoria.toUpperCase();
                buttonElement.disabled = false;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error de conexión');

            // Restaurar botón
            if (buttonElement) {
                var nombreCategoria = categoria.charAt(0).toUpperCase() + categoria.slice(1);
                var iconos = {bar: '🍺', cocina: '🍳', asados: '🥩'};
                buttonElement.innerHTML = '🖨️ ' + (iconos[categoria] || '') + ' ' + nombreCategoria.toUpperCase();
                buttonElement.disabled = false;
            }
        });
    }

    window.abrirVentanaImpresion = function(contenido, categoria) {
        const ventana = window.open('', '_blank', 'width=400,height=600,scrollbars=yes');

        ventana.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>Impresión - \${categoria}</title>
                <style>
                    body {
                        font-family: 'Courier New', monospace;
                        font-size: 12px;
                        margin: 20px;
                        white-space: pre-wrap;
                    }
                    @media print {
                        body { margin: 0; }
                    }
                </style>
            </head>
            <body>
                \${contenido}
                <script>
                    window.onload = function() {
                        window.print();
                    };
                </scr\ipt>
            </body>
            </html>
        `);

        ventana.document.close();
    }

    // Verificar que las funciones se definieron correctamente
    console.log('✅ Funciones de impresión definidas:', {
        imprimirCategoria: typeof window.imprimirCategoria,
        abrirVentanaImpresion: typeof window.abrirVentanaImpresion
    });
    </script>";
    
    if (!empty($datos_pedido['categorias'])) {
        echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0;'>";
        echo "<h5>✅ Componente Cargado Exitosamente</h5>";
        echo "<p><strong>Categorías encontradas:</strong> " . count($datos_pedido['categorias']) . "</p>";
        echo "<p><strong>Total productos:</strong> {$datos_pedido['total_productos']}</p>";
        
        echo "<h6>📋 Detalle por categoría:</h6>";
        echo "<ul>";
        foreach ($datos_pedido['categorias'] as $categoria => $info) {
            echo "<li><strong>{$info['nombre']}:</strong> {$info['total_productos']} productos - $" . number_format($info['total_precio'], 0) . "</li>";
        }
        echo "</ul>";
        echo "</div>";
        
        echo "<div style='background-color: #e7f3ff; padding: 15px; border-left: 4px solid #007bff; margin: 15px 0;'>";
        echo "<h5>💡 Instrucciones de Prueba:</h5>";
        echo "<ol>";
        echo "<li>Haz clic en cualquier botón de categoría</li>";
        echo "<li>Se generará el contenido específico para esa categoría</li>";
        echo "<li>Se abrirá una ventana de impresión automáticamente</li>";
        echo "<li>Selecciona tu impresora y confirma</li>";
        echo "</ol>";
        echo "</div>";
        
    } else {
        echo "<div style='background-color: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 15px 0;'>";
        echo "<h5>⚠️ Sin Categorías</h5>";
        echo "<p>Este pedido no tiene productos o no se pudieron agrupar por categorías.</p>";
        if (isset($datos_pedido['error'])) {
            echo "<p><strong>Error:</strong> {$datos_pedido['error']}</p>";
        }
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
    echo "<h4>❌ Error cargando componente</h4>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Archivo:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Línea:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>
