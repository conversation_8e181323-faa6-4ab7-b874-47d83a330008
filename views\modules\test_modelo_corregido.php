<?php

session_start();

if(!isset($_SESSION["validar"]) || !$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "models/crudPedidosCategorias.php";

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Test Modelo Corregido</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
</head>
<body>

<div class="container">
    <h2>🧪 Test: Modelo Corregido</h2>
    <hr>
    
    <div class="panel panel-success">
        <div class="panel-heading">
            <h3 class="panel-title">✅ Cambios Realizados</h3>
        </div>
        <div class="panel-body">
            <p>✅ <strong>Eliminada "SOLUCIÓN TEMPORAL"</strong> para cocina</p>
            <p>✅ <strong>Unificada la lógica</strong> para todas las categorías</p>
            <p>✅ <strong>Corregidas las estadísticas</strong> para consistencia</p>
        </div>
    </div>
    
    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">🍺 Test BAR - Modelo Directo</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                echo "<h4>Llamando DatosPedidosCategorias::obtenerPedidosPendientesModel('bar'):</h4>";
                $pedidos_bar = DatosPedidosCategorias::obtenerPedidosPendientesModel('bar');
                echo "<p><strong>Resultado:</strong> " . count($pedidos_bar) . " pedidos</p>";
                
                if (count($pedidos_bar) > 0) {
                    echo "<table class='table table-striped'>";
                    echo "<thead><tr><th>Pedido</th><th>Mesa</th><th>Productos</th><th>Total</th></tr></thead>";
                    echo "<tbody>";
                    foreach ($pedidos_bar as $pedido) {
                        echo "<tr>";
                        echo "<td>{$pedido['numero_pedido']}</td>";
                        echo "<td>{$pedido['mesa_numero']}</td>";
                        echo "<td>{$pedido['productos_detalle']}</td>";
                        echo "<td>$" . number_format($pedido['total_precio'], 0) . "</td>";
                        echo "</tr>";
                    }
                    echo "</tbody></table>";
                } else {
                    echo "<div class='alert alert-danger'>❌ El modelo corregido aún devuelve 0 resultados para BAR</div>";
                }
                
                echo "<h4>Estadísticas BAR:</h4>";
                $stats_bar = DatosPedidosCategorias::obtenerEstadisticasDiaModel('bar');
                echo "<p><strong>Pendientes:</strong> {$stats_bar['pendientes']}</p>";
                echo "<p><strong>Entregados hoy:</strong> {$stats_bar['entregados_hoy']}</p>";
                echo "<p><strong>Ventas hoy:</strong> $" . number_format($stats_bar['total_ventas_hoy'], 0) . "</p>";
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>❌ Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-warning">
        <div class="panel-heading">
            <h3 class="panel-title">🍳 Test COCINA - Modelo Directo</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                echo "<h4>Llamando DatosPedidosCategorias::obtenerPedidosPendientesModel('cocina'):</h4>";
                $pedidos_cocina = DatosPedidosCategorias::obtenerPedidosPendientesModel('cocina');
                echo "<p><strong>Resultado:</strong> " . count($pedidos_cocina) . " pedidos</p>";
                
                if (count($pedidos_cocina) > 0) {
                    echo "<table class='table table-striped'>";
                    echo "<thead><tr><th>Pedido</th><th>Mesa</th><th>Productos</th><th>Total</th></tr></thead>";
                    echo "<tbody>";
                    foreach ($pedidos_cocina as $pedido) {
                        echo "<tr>";
                        echo "<td>{$pedido['numero_pedido']}</td>";
                        echo "<td>{$pedido['mesa_numero']}</td>";
                        echo "<td>{$pedido['productos_detalle']}</td>";
                        echo "<td>$" . number_format($pedido['total_precio'], 0) . "</td>";
                        echo "</tr>";
                    }
                    echo "</tbody></table>";
                } else {
                    echo "<div class='alert alert-danger'>❌ El modelo corregido aún devuelve 0 resultados para COCINA</div>";
                }
                
                echo "<h4>Estadísticas COCINA:</h4>";
                $stats_cocina = DatosPedidosCategorias::obtenerEstadisticasDiaModel('cocina');
                echo "<p><strong>Pendientes:</strong> {$stats_cocina['pendientes']}</p>";
                echo "<p><strong>Entregados hoy:</strong> {$stats_cocina['entregados_hoy']}</p>";
                echo "<p><strong>Ventas hoy:</strong> $" . number_format($stats_cocina['total_ventas_hoy'], 0) . "</p>";
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>❌ Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-danger">
        <div class="panel-heading">
            <h3 class="panel-title">🔍 Debug: Verificar si el archivo se guardó correctamente</h3>
        </div>
        <div class="panel-body">
            <?php
            echo "<h4>Verificar contenido del archivo crudPedidosCategorias.php:</h4>";
            $archivo_contenido = file_get_contents('models/crudPedidosCategorias.php');
            
            if (strpos($archivo_contenido, 'SOLUCIÓN TEMPORAL') !== false) {
                echo "<div class='alert alert-danger'>❌ El archivo AÚN contiene 'SOLUCIÓN TEMPORAL' - Los cambios no se guardaron</div>";
            } else {
                echo "<div class='alert alert-success'>✅ El archivo NO contiene 'SOLUCIÓN TEMPORAL' - Los cambios se guardaron</div>";
            }
            
            if (strpos($archivo_contenido, 'pr.categoria = :categoria') !== false) {
                echo "<div class='alert alert-success'>✅ El archivo contiene el filtro correcto 'pr.categoria = :categoria'</div>";
            } else {
                echo "<div class='alert alert-danger'>❌ El archivo NO contiene el filtro correcto</div>";
            }
            
            // Mostrar las primeras líneas de la función
            $lineas = explode("\n", $archivo_contenido);
            echo "<h4>Primeras líneas de obtenerPedidosPendientesModel:</h4>";
            echo "<pre style='background: #f5f5f5; padding: 10px; font-size: 12px;'>";
            $encontrado = false;
            $contador = 0;
            foreach ($lineas as $num => $linea) {
                if (strpos($linea, 'obtenerPedidosPendientesModel') !== false) {
                    $encontrado = true;
                }
                if ($encontrado) {
                    echo ($num + 1) . ": " . htmlspecialchars($linea) . "\n";
                    $contador++;
                    if ($contador > 20) break; // Mostrar solo las primeras 20 líneas
                }
            }
            echo "</pre>";
            ?>
        </div>
    </div>
    
    <div class="panel panel-primary">
        <div class="panel-heading">
            <h3 class="panel-title">🔧 Test Controlador</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                require_once "controllers/controllerPedidosCategorias.php";
                $controller = new ControllerPedidosCategorias();
                
                echo "<h4>Test Controlador BAR:</h4>";
                $pedidos_ctrl_bar = $controller->mostrarPedidosPendientesController('bar');
                echo "<p><strong>Resultado del controlador:</strong> " . count($pedidos_ctrl_bar) . " pedidos</p>";
                
                echo "<h4>Test Controlador COCINA:</h4>";
                $pedidos_ctrl_cocina = $controller->mostrarPedidosPendientesController('cocina');
                echo "<p><strong>Resultado del controlador:</strong> " . count($pedidos_ctrl_cocina) . " pedidos</p>";
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>❌ Error en controlador: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <hr>
    <div class="row">
        <div class="col-md-3">
            <a href="index.php?action=debug_consulta_exacta" class="btn btn-primary btn-block">🔙 Debug Anterior</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=pedidosBarPendientes" class="btn btn-info btn-block">🍺 Ir a Bar</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=pedidosCocinaPendientes" class="btn btn-warning btn-block">🍳 Ir a Cocina</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=crear_pedido_prueba" class="btn btn-success btn-block">🧪 Crear Pedido</a>
        </div>
    </div>
</div>

</body>
</html>
