<?php

	#EXTENSIÓN DE CLASES: Los objetos pueden ser extendidos, y pueden heredar propiedades y métodos. Para definir una clase como extensión, debo definir una clase padre, y se utiliza dentro de una clase hija.

require_once "conexion.php";

class <PERSON>tos extends Conexion
{

	#PRODUCTO CON SU LISTA DE SUMINISTROS
	#------------------------------------------
	 public static function productoListaSuministroModel($idproducto)
		{
				//echo "<script>alert('crud de la segunda consulta ');</script>";
		 $consulta="SELECT  s.id AS sid, s.cantidad AS scantidad, sp.cantidades AS spcantidad, s.nombre AS snombre, s.cantidad_minima AS cantidadM,s.codigo AS scodigo, su.cantidad AS sucantidad FROM suministros_productos sp, suministros s, sucursal su WHERE s.id=sp.suministro_id AND s.id=su.suministro_id AND sp.producto_id=:id";
		 $stmt = Conexion::conectar()->prepare($consulta);
		 $stmt->bindParam(":id", $idproducto, PDO::PARAM_INT);
		 if($stmt->execute())
			{	 return $stmt->fetchAll();	}
		 else {	/*echo "<script>alert('crud de la segunda consulta IF no ');</script>";*/ return "error";	}
		 $stmt->close();

		}
	#PRODUCTO CON SU LISTA DE SUMINISTROS 2
	#------------------------------------------
	 public static function productoLista2SuministroModel($idproducto)
		{
				//echo "<script>alert('crud de la segunda consulta ');</script>";
		 $consulta="SELECT  s.id AS sid, s.cantidad AS scantidad, sp.cantidades AS spcantidad, s.nombre AS snombre, s.cantidad_minima AS cantidadM,s.codigo AS scodigo, su.cantidad AS sucantidad FROM suministros_productos sp, suministros s, sucursal su WHERE s.id=sp.suministro_id AND s.id=su.suministro_id AND sp.producto_id=:id";
		 $stmt = Conexion::conectar()->prepare($consulta);
		 $stmt->bindParam(":id", $idproducto, PDO::PARAM_INT);
		 if($stmt->execute())
			{	 return $stmt->fetch();	}
		 else {	/*echo "<script>alert('crud de la segunda consulta IF no ');</script>";*/ return "error";	}
		 $stmt->close();

		}
	#---------------------------------------------------
	#PRODUCTO CON SU LISTA DE SUMINISTROS Devolución
	#------------------------------------------
	 public static function productoListaSuministroDevolucionModel($idproducto)
		{
				//echo "<script>alert('crud de la segunda consulta ');</script>";
		 $consulta="SELECT s.id AS sid, s.cantidad AS scantidad, sp.cantidades AS spcantidad, s.nombre AS snombre, s.cantidad_minima AS cantidadM,s.codigo AS scodigo, su.cantidad AS sucantidad, p.precio as precio FROM suministros_productos sp, suministros s, sucursal su, productos p WHERE p.id=sp.producto_id and s.id=sp.suministro_id AND s.id=su.suministro_id AND sp.producto_id=:id";
		 $stmt = Conexion::conectar()->prepare($consulta);
		 $stmt->bindParam(":id", $idproducto, PDO::PARAM_INT);
		 if($stmt->execute())
			{	 return $stmt->fetch();	}
		 else {	/*echo "<script>alert('crud de la segunda consulta IF no ');</script>";*/ return "error";	}
		 $stmt->close();

		}
	#------------------------------------------
	#CANCELAR PEDIDO
	#------------------------------------
	 public static function cancelar1Model()
	 	{	//$stmt = Conexion::conectar();DELETE FROM
 			$consulta=" TRUNCATE TABLE pendiente_factura ";
	 		$stmt = Conexion::conectar();
	 		$stmt ->exec($consulta);
	 		return "sucess";
	 		$stmt->close();
    	}
	#-----------------------------------
	#CANCELAR PEDIDO MESA
	#------------------------------------
	 public static function cancelarModel($idmesa)
	 	{
	 	 $stmt = Conexion::conectar();
	 	 try
 			{
 			 $stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
			 $stmt->beginTransaction();
 			 $consulta="DELETE FROM producto_vendido_mesa where mesas_id=$idmesa";
		 	 $stmt ->exec($consulta);
		 	 $consulta1 = " UPDATE mesas SET descripcion='', estado = '' WHERE id = $idmesa"; 		 	 
		 	 $stmt ->exec($consulta1);

		 	 // $consulta2 = " DELETE FROM pedidos_entregas_categoria SET descripcion='', estado = '' WHERE id = $idmesa";
		 	 // $stmt ->exec($consulta2);

		 	 $consultaC = "DELETE FROM cocina where mesa_id =" .$idmesa;
			 $stmt->exec($consultaC);
			 $stmt->commit();
			 //echo "<script>alert('fin de cruD try que pasa');</script>";
			 return "success";
			 $stmt->close();
 	    	}
	 	 catch (PDOException $e)
 	 		{
		 	 echo "<script>alert('catch entro')</script>";
			 $stmt->rollBack();
			 print "¡Error!: " . $e->getMessage() . "<br/>";
			 die();
			}
    	}
	#-----------------------------------
	#CANCELAR PEDIDO MESA - VERSIÓN SEGURA SIN ELIMINAR REGISTROS
	#------------------------------------
	 public static function cancelarMesaConPedidosModel($idmesa)
	 	{
	 	 $stmt = Conexion::conectar();
	 	 try
 			{
 			 $stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
			 $stmt->beginTransaction();

			 // Log para debug
			 error_log("cancelarMesaConPedidosModel: Iniciando cancelación SEGURA para mesa {$idmesa}");

			 // 1. Cancelar pedidos activos (cambiar estado, NO eliminar)
			 $consulta_pedidos = "UPDATE pedidos SET estado = 'cancelado', fecha_entrega = NOW() WHERE mesa_id = $idmesa AND estado IN ('borrador', 'enviado')";
			 error_log("cancelarMesaConPedidosModel: Ejecutando - {$consulta_pedidos}");
			 $stmt->exec($consulta_pedidos);
			 error_log("cancelarMesaConPedidosModel: Pedidos cancelados para mesa {$idmesa}");

			 // 2. Cancelar entregas por categoría (cambiar estado, NO eliminar)
			 try {
			     $consulta_check_tabla = "SHOW TABLES LIKE 'pedidos_entregas_categoria'";
			     $check_result = $stmt->query($consulta_check_tabla);
			     if ($check_result->rowCount() > 0) {
			         $consulta_entregas = "UPDATE pedidos_entregas_categoria pec
			                              INNER JOIN pedidos p ON pec.pedido_id = p.id
			                              SET pec.estado = 'cancelado'
			                              WHERE p.mesa_id = $idmesa AND pec.estado = 'pendiente'";
			         error_log("cancelarMesaConPedidosModel: Ejecutando - {$consulta_entregas}");
			         $stmt->exec($consulta_entregas);
			         error_log("cancelarMesaConPedidosModel: Entregas canceladas para mesa {$idmesa}");
			     }
			 } catch (Exception $e) {
			     error_log("cancelarMesaConPedidosModel: Error con pedidos_entregas_categoria: " . $e->getMessage());
			 }

			 // 3. Limpiar mesa (solo cambiar estado)
		 	 $consulta_mesa = "UPDATE mesas SET descripcion = '', estado = '' WHERE id = $idmesa";
		 	 error_log("cancelarMesaConPedidosModel: Ejecutando - {$consulta_mesa}");
		 	 $stmt->exec($consulta_mesa);
		 	 error_log("cancelarMesaConPedidosModel: Mesa limpiada");

			 $stmt->commit();
			 error_log("cancelarMesaConPedidosModel: Cancelación SEGURA exitosa para mesa {$idmesa}");
			 return "success";
			 $stmt->close();
 	    	}
	 	 catch (PDOException $e)
 	 		{
		 	 $stmt->rollBack();
			 error_log("cancelarMesaConPedidosModel: Error PDO - " . $e->getMessage());
			 error_log("cancelarMesaConPedidosModel: Error Code - " . $e->getCode());
			 error_log("cancelarMesaConPedidosModel: SQL State - " . $e->errorInfo[0]);
			 return "error";
		}
		catch (Exception $e)
 	 		{
			 if ($stmt->inTransaction()) {
			     $stmt->rollBack();
			 }
			 error_log("cancelarMesaConPedidosModel: Error general - " . $e->getMessage());
			 return "error";
		}
    	}
	#-----------------------------------
	#ELIMINAR ENTREGAS POR CATEGORÍA DE PEDIDOS DE UNA MESA
	#------------------------------------
	 public static function eliminarEntregasCategoriaModel($mesa_id)
	 	{
	 	 try {
	 	 	 $stmt = Conexion::conectar();
	 	 	 $stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

	 	 	 // Log para debug
	 	 	 error_log("eliminarEntregasCategoriaModel: Iniciando eliminación para mesa {$mesa_id}");

	 	 	 // 1. Obtener todos los pedidos de la mesa
	 	 	 $consulta_pedidos = "SELECT id FROM pedidos WHERE mesa_id = :mesa_id";
	 	 	 $stmt_pedidos = $stmt->prepare($consulta_pedidos);
	 	 	 $stmt_pedidos->bindParam(":mesa_id", $mesa_id, PDO::PARAM_INT);
	 	 	 $stmt_pedidos->execute();
	 	 	 $pedidos = $stmt_pedidos->fetchAll(PDO::FETCH_COLUMN);

	 	 	 error_log("eliminarEntregasCategoriaModel: Encontrados " . count($pedidos) . " pedidos para mesa {$mesa_id}");

	 	 	 // 2. Eliminar entregas por categoría para cada pedido
	 	 	 $total_eliminados = 0;
	 	 	 foreach ($pedidos as $pedido_id) {
	 	 	 	 $consulta_delete = "DELETE FROM pedidos_entregas_categoria WHERE pedido_id = :pedido_id";
	 	 	 	 $stmt_delete = $stmt->prepare($consulta_delete);
	 	 	 	 $stmt_delete->bindParam(":pedido_id", $pedido_id, PDO::PARAM_INT);
	 	 	 	 $stmt_delete->execute();

	 	 	 	 $eliminados = $stmt_delete->rowCount();
	 	 	 	 $total_eliminados += $eliminados;

	 	 	 	 error_log("eliminarEntregasCategoriaModel: Eliminados {$eliminados} registros para pedido {$pedido_id}");
	 	 	 }

	 	 	 error_log("eliminarEntregasCategoriaModel: Total eliminados {$total_eliminados} registros para mesa {$mesa_id}");
	 	 	 return "success";

	 	 } catch (Exception $e) {
	 	 	 error_log("eliminarEntregasCategoriaModel: Error - " . $e->getMessage());
	 	 	 return "error";
	 	 }
	 	}
	#-----------------------------------
	#LISTAS y OTRO
	#---------------------------------------------------------
		#Hallar MAX columna
		#------------------------------------------------------
		 public static function MaximoModel($tabla)
			{ try
				{
					$consulta="SELECT MAX(id) as mayor FROM `$tabla`";
					$stmt = Conexion::conectar()->prepare($consulta);
					if($stmt->execute())
						{	return $stmt->fetch();	/*/return 29;*/
						}
					else {	return "error";	}
					$stmt->close();
				}
			catch (PDOException $e)
	 	 		{
				    print "¡Error!: " . $e->getMessage() . "<br/>";
				    die();
				}

			}
		#---------------------------------------------------------
		#LISTA Roles
		#---------------------------------------
		 public static function mesasModel($tabla)
			{
				$consulta="SELECT * from $tabla ";
				$stmt = Conexion::conectar()->prepare($consulta);
				if($stmt->execute())
					{	return $stmt->fetchAll();	}
				else {	return "error";	}
				$stmt->close();
			}
		#---------------------------------------
		#LISTA Roles
		#---------------------------------------
		 public static function listaRolesModel($tabla)
			{
				$consulta="SELECT id,nombre from $tabla ";
				$stmt = Conexion::conectar()->prepare($consulta);
				if($stmt->execute())
					{	return $stmt->fetchAll();	}
				else {	return "error";	}
				$stmt->close();
			}
		#---------------------------------------
		#LISTA Unidad
		#---------------------------------------
		 public static function listaUnidadesModel($tabla)
			{
				$consulta="SELECT id, nombre from $tabla order by nombre";
				$stmt = Conexion::conectar()->prepare($consulta);
				if($stmt->execute())
					{	return $stmt->fetchAll();	}
				else {	return "error";	}
				$stmt->close();
			}
		#---------------------------------------
		#LISTA Mesero
		#---------------------------------------
			public static function buscarRollModel($datosModel, $tabla)
				{	$consulta="SELECT p.id AS pid, p.nombre as pnombre, p.roles_id as prolesid, r.id as rid, r.nombre as rnombre FROM personas p, roles r WHERE p.roles_id=r.id AND r.nombre=:mesero";
					$stmt = Conexion::conectar()->prepare($consulta);
					$stmt->bindParam(":mesero", $datosModel, PDO::PARAM_STR);

					if($stmt->execute())
						{	return $stmt->fetchAll();	}
					else {	return "error";	}
					$stmt->close();
				}
		#---------------------------------------
		#LISTA Pedido
		#---------------------------------------
			public static function listaPedidosModel($tabla)
				{
					$stmt = Conexion::conectar()->prepare("SELECT id from $tabla  ");
					if($stmt->execute())
						{	return $stmt->fetchAll();	}
					else {	return "error";	}
					$stmt->close();
				}
		#---------------------------------------
		#buscar Productos  SELECT id, nombre, precio FROM productos WHERE id=16
		#---------------------------------------
			public static function buscarProductosModel($datosModel,$tabla)
				{	$consulta="SELECT id,nombre,precio from $tabla WHERE id=:id ";
					$stmt = Conexion::conectar()->prepare($consulta);
					$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);
					if($stmt->execute())
						{	return $stmt->fetch();	}
					else {	return "error";	}
					$stmt->close();
				}
		#---------------------------------------
		#BUSCAR nombre producto autocompletar
		#------------------------------------------
		 public static function turnoActivoModel($nombrep)
			{   //echo "<script>alert('Entro CRUD autocompletar')</script>";
				$consulta = "SELECT id, persona_id, fecha_inicio, fecha_final, cantidad_inicio, cantidad_final FROM turnos_cajeros WHERE persona_id=:nombrep";
				$stmt = Conexion::conectar()->prepare($consulta);
				$stmt->bindParam(":nombrep", $nombrep, PDO::PARAM_INT);
				$stmt->execute();
				$r = $stmt->fetch();
				$c=$stmt->rowCount();
				//echo '<br> C2='.$c2.'<br>';
				if ($c>0)
					{	return $r;	}
				else
					{	return 0;	}
				$stmt->close();
			}
		#------------------------------------------
		#BUSCAR nombre producto autocompletar
		#------------------------------------------
		 public static function buscarNombreSModel($nombrep)
			{   $nombre='%'.$nombrep.'%';
				//echo "<script>alert('Entro CRUD autocompletar')</script>";
				$consulta = "SELECT id, codigo, tipo_suministro, nombre FROM suministros WHERE nombre like (:nombrep) LIMIT 0,50";
				$stmt = Conexion::conectar()->prepare($consulta);
				$stmt->bindParam(":nombrep", $nombre, PDO::PARAM_STR);
				$stmt->execute();
				$r = $stmt->fetchAll();
				$c=$stmt->rowCount();
				//echo '<br> C2='.$c2.'<br>';
				if ($c>0)
					{	return $r;	}
				else
					{	return 0;	}
				$stmt->close();
			}
		#------------------------------------------
		#LISTA Productos
		#---------------------------------------
			public static function listaProductosModel($tabla)
				{
					$stmt = Conexion::conectar()->prepare("SELECT * from $tabla ORDER BY codigo DESC  ");
					if($stmt->execute())
						{	return $stmt->fetchAll();	}
					else {	return "error";	}
					$stmt->close();
				}
		#---------------------------------------
		#LISTA Suministros
		#---------------------------------------
			public static function listaSuministrosModel($tabla)
				{
					$stmt = Conexion::conectar()->prepare("SELECT * from $tabla ORDER BY codigo DESC   ");
					if($stmt->execute())
						{	return $stmt->fetchAll();	}
					else {	return "error";	}
					$stmt->close();
				}
		#---------------------------------------
		#LISTA Departamentos
		#---------------------------------------
			public static function listaDepartamentosModel($tabla)
				{
					$stmt = Conexion::conectar()->prepare("SELECT id,nombre from $tabla  ");
					if($stmt->execute())
						{	return $stmt->fetchAll();	}
					else {	return "error";	}
					$stmt->close();
				}
		#---------------------------------------
		#LISTA Ciudades
		#---------------------------------------
			public static function listaCiudadesModel($tabla)
				{
					$stmt = Conexion::conectar()->prepare("SELECT id, departamento_id, nombre from $tabla  ");
					if($stmt->execute())
						{	return $stmt->fetchAll();	}
					else {	return "error";	}
					$stmt->close();
				}
		#---------------------------------------
		#LISTA Mesas
		#---------------------------------------
			public static function listaMesasModel($tabla)
				{
					$stmt = Conexion::conectar()->prepare("SELECT id,nombre from $tabla  ");
					if($stmt->execute())
						{	return $stmt->fetchAll();	}
					else {	return "error";	}
					$stmt->close();
				}
		#---------------------------------------
		#LISTA PUNTOS O SUCURSALES
		#---------------------------------------
		 public static function listaPuntosModel($tabla)
			{
				$stmt = Conexion::conectar()->prepare("SELECT id,nombre from $tabla  ");
				if($stmt->execute())
					{	return $stmt->fetchAll();	}
				else {	return "error";	}
				$stmt->close();
			}
		#---------------------------------------
	#--------------------------------------------------------
}//Cierre de  class Datos extends Conexion
?>