<?php
 $registro = new controllerSuministro();
 $unidades=$registro->listaUnidadesController();
 $codigo=$registro -> listaSuministros1Controller();
?>
<h1>REGISTRO DE SUMINISTRO</h1>
<form method="post">
	<table>
		<thead>
			<tr > <td colspan="6" ></td> </tr>
		</thead>
		<tr>
			<td> <label> Codigo  : </label></td><td><input type="text"  value="<?=$codigo;?>" name="codigo" required>	 </td>
		</tr>
		<tr>
			<td><label> Nombre  : </label></td>
			<td><input type="text" placeholder="nombre" name="nombreSuministroRegistro" autofocus="autofocus" required></td>
		</tr>
		<!--tr>
			<td><label> Tipo SUMINISTRO  : </label></td>
			<td>
				<select name="tiposuministro" id="tiposuministro" required>
					<option value="1">Cafetería</option>
					<option value="2">Cocina</option>
				</select>
			</td>
		</tr-->
			<input type="hidden" name="tiposuministro" value="1" >
		<tr>
			<td><label> Cantidad  : </label></td>
			<td><input type="text" placeholder="cantidad" name="cantidadSuministroRegistro" required>
				<?php
				 # %%%%%%%%%%%%%%%%%%% Roles  %%%%%%%%%%%%%%%%%%%%%%%%%
					if($unidades=="error")
						{	echo "debe registrar la unidad"; }
					else{
							echo "";
							$result='<select name="unidad_id"  id="unidad_id">';
							$result.=' <option value="-1">Unidad</option>';
							foreach ($unidades as $row => $ite)
						 	 {
						 	 	$result.=' <option value="'.$ite["id"].'">'.$ite["nombre"].'</option>';
						 	 }
							 $result.='</select>';
							 echo $result." <br>";
						 	}
				 # %%%%%%%%%%%%%%%%%%%%%%%%%%%%  End Roles  %%%%%%%%%%%%%%%%%%%%%%%%%%%
				?>
			</td>
		</tr>
		<tr>
			<td><label> Precio Compra : </label></td>
			<td><input type="text" placeholder="precio" name="precioSuministroRegistro" required></td>
		</tr>
		<tr>
			<td><label> IVA : </label></td>
			<td><input type="text" placeholder="IVA" name="iva" value="0"></td>
		</tr>
		<tr>
			<td><label> Minimo stop : </label></td>
			<td><input type="text" placeholder="MINIMO" name="cantidadMSuministroRegistro" required></td>
		</tr>

		</label><input type="hidden" placeholder="MINIMO" value="1" name="tipo" required>
		<thead>
			<tr > <td colspan="6" ></td> </tr>
		</thead>
	</table><br>
	<input type="submit" value="Enviar">
</form>
<?php
 $registro -> registroSuministroSoloController();
 if(isset($_GET["action"]))
	{	if($_GET["action"] == "okp"){	echo "Registro Exitoso";	}	}
?>
