<!DOCTYPE html>
<html>
    <head>
        <meta charset="UTF-8">
        <title>Reporte de Cierre de Turno</title>
        <script type="text/javascript">
            function imprimir() {
                if (window.print) {
                    window.print();
                } else {
                    alert("La función de impresión no está soportada por su navegador.");
                }
            }
        </script>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; font-size: 18px; font-weight: bold; margin-bottom: 20px; }
            .info-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
            .info-table td { padding: 5px; border: 1px solid #ddd; }
            .ventas-table { width: 100%; border-collapse: collapse; font-size: 12px; }
            .ventas-table th, .ventas-table td { padding: 5px; border: 1px solid #ddd; text-align: left; }
            .ventas-table th { background-color: #f5f5f5; font-weight: bold; }
            .total-row { background-color: #e8f4fd; font-weight: bold; }
            .resumen { margin-top: 20px; padding: 10px; background-color: #f9f9f9; border: 1px solid #ddd; }
        </style>
    </head>
    <body onload="imprimir();">

<?php
session_start();

// Verificar que existan las variables de sesión necesarias
if (!isset($_SESSION["turno"]) || !isset($_SESSION["inicio"])) {
    echo "<div class='header'>❌ Error: No hay información de turno disponible</div>";
    echo "<p>Por favor, cierre el turno desde el sistema para generar este reporte.</p>";
    echo "</body></html>";
    exit;
}

$inicio = $_SESSION["inicio"];
$cajero = $_SESSION["turno"];
$reporteTurno = isset($_SESSION["turnoV"]) ? $_SESSION["turnoV"] : array();
$totalVentas = isset($_SESSION["ventas"]) ? $_SESSION["ventas"] : 0;
$efectivoTurno = isset($_SESSION["efectivo_turno"]) ? $_SESSION["efectivo_turno"] : 0;
$ingresos = isset($_SESSION["ingreso"]) ? $_SESSION["ingreso"] : 0;
$egresos = isset($_SESSION["Egreso"]) ? $_SESSION["Egreso"] : 0;
$propinas = isset($_SESSION["propina"]) ? $_SESSION["propina"] : 0;

$nequi=isset($_SESSION["nequi"]) ? $_SESSION["nequi"] : 0;
$daviplata=isset($_SESSION["daviplata"]) ? $_SESSION["daviplata"] : 0;
$datafono=isset($_SESSION["datafono"]) ? $_SESSION["datafono"] : 0;
$bancolombia=isset($_SESSION["bancolombia"]) ? $_SESSION["bancolombia"] : 0;

$efectivoTurno=$efectivoTurno-$nequi-$daviplata-$datafono-$bancolombia;



?>

<div class="header">🏪 REPORTE DE CIERRE DE TURNO</div>

<table class="info-table">
    <tr>
        <td><strong>Turno No:</strong></td>
        <td><?php echo $cajero; ?></td>
        <td><strong>Fecha:</strong></td>
        <td><?php echo date('d/m/Y H:i'); ?></td>
    </tr>
    <tr>
        <td><strong>Inicio:</strong></td>
        <td colspan="3"><?php echo $inicio; ?></td>
    </tr>
</table>

<?php if (!empty($reporteTurno)): ?>
<h3>💰 Detalle de Ventas</h3>
<table class="ventas-table">
    <thead>
        <tr>
            <th>Fecha/Hora</th>
            <th>Factura No</th>
            <th>Total</th>
        </tr>
    </thead>
    <tbody>
        <?php
        $totalFacturas = 0;
        foreach ($reporteTurno as $venta):
            $totalFacturas += $venta["vtotal"];
        ?>
        <tr>
            <td><?php echo $venta["vfecha"]; ?></td>
            <td><?php echo number_format($venta["vid"]); ?></td>
            <td>$<?php echo number_format($venta["vtotal"]); ?></td>
        </tr>
        <?php endforeach; ?>
        <tr class="total-row">
            <td colspan="2"><strong>TOTAL VENTAS:</strong></td>
            <td><strong>$<?php echo number_format($totalFacturas); ?></strong></td>
        </tr>
    </tbody>
</table>
<?php else: ?>
<div class="resumen">
    <p><strong>⚠️ No hay ventas registradas en este turno</strong></p>
</div>
<?php endif; ?>

<div class="resumen">
    <h3>📊 Resumen del Turno</h3>
    <table class="info-table">
        <tr>
            <td><strong>Total Ventas:</strong></td>
            <td align="right">$<?php echo number_format($totalVentas); ?></td>
        </tr>
        <tr>
            <td><strong>Nequi:</strong></td>
            <td align="right">$<?php echo number_format($nequi); ?></td>
        </tr>
        <tr>
            <td><strong>Daviplata:</strong></td>
            <td align="right">$<?php echo number_format($daviplata); ?></td>
        </tr>
        <tr>
            <td><strong>Datafono:</strong></td>
            <td align="right">$<?php echo number_format($datafono); ?></td>
        </tr>
        <tr>
            <td><strong>Bancolombia:</strong></td>
            <td align="right">$<?php echo number_format($bancolombia); ?></td>
        </tr>
        <tr>
            <td><strong>Efectivo Recibido:</strong></td>
            <td align="right">$<?php echo number_format($efectivoTurno); ?></td>
        </tr>
        <?php if ($ingresos > 0): ?>
        <tr>
            <td><strong>Otros Ingresos:</strong></td>
            <td>$<?php echo number_format($ingresos); ?></td>
        </tr>
        <?php endif; ?>
        <?php if ($egresos > 0): ?>
        <tr>
            <td><strong>Egresos:</strong></td>
            <td>$<?php echo number_format($egresos); ?></td>
        </tr>
        <?php endif; ?>
        <?php if ($propinas > 0): ?>
        <tr>
            <td><strong>Propinas:</strong></td>
            <td>$<?php echo number_format($propinas); ?></td>
        </tr>
        <?php endif; ?>
    </table>
</div>

<div style="text-align: center; margin-top: 30px; font-size: 12px; color: #666;">
    <p>Reporte generado automáticamente - Sistema Macarena</p>
    <p><?php echo date('d/m/Y H:i:s'); ?></p>
</div>

    </body>
</html>
