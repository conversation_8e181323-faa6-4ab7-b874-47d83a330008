<?php 
$registro = new controllerSuministro();
$suministros=$registro -> listaSuministroController();

	$suministro='<select name="suministro_id"  id="suministro_id" required>';
 	$suministro.=' <option value="-1">Seleccione un Suministro</option>';

 	foreach ($suministros as $row2 => $item2) {
 		$suministro.=' <option value="'.$item2["id"].'">'.$item2["nombre"].'</option>';
 	}
 	$suministro.='</select>';
 ?>
<h1>REGISTRO DE PERDIDAS DE SUMINISTROS</h1>

<form method="post">

	<table>
		<thead> 	
			<tr > <td colspan="6" ></td> </tr>
		</thead>
		<tr>
			<td><label> Suministro  : </label></td>
			<td>
				<?=$suministro;?>
			</td>
		</tr>
		<tr>
			<td><label> Cantidad  : </label></td>
			<td><input type="text" placeholder="cantidad" name="cantidad" required></td>
		</tr>
		<thead> 	
			<tr > <td colspan="6" ></td> </tr>
		</thead>
		
	</table><br>

	<input type="submit" value="Enviar">

</form>

<?php

	
	$registro -> registroPerdidasController();

	if(isset($_GET["action"]))

		{	if($_GET["action"] == "okp")
				{	echo "Registro Exitoso";	}
		}
		/*<select name="tipo" id="tipo">
				  <option value="1">Suministro</option>
				  <option value="2">Producto</option>
	</select><br>
	<label> precio Venta: </label><input type="text" placeholder="precio" name="precioRegistro"><br>
		
		 */

?>
