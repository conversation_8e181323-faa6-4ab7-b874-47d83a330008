<?php
	require_once "../../models/crud.php";
	require_once "../../models/crudFacturaAja.php";
	require_once "../../controllers/controller.php";
	require_once "../../controllers/controllerFacturaAja.php";

	ini_set("session.cookie_lifetime","28800");
	ini_set("session.gc_maxlifetime","28800");
//echo "<script>alert('Autocompletar: ')</script>";
if (isset($_POST['palabra'])){
//$queryString=$_GET['term'];
$queryString=$_POST['palabra'];
$return_arr = array();

$ajax=new controllerFacturaAja();
$r=$ajax->buscarProductoController($queryString);

	if ($r!=0) {
		foreach($r as $row => $item)
			{
			    // Colocaremos negrita a los textos
			 $pais_nombre = str_replace($queryString, '<b>'.$queryString.'</b>', $item['nombre']);
			 // Aquì, agregaremos opciones
			 $comilla="'";
			 $codigo=$item['codigo'];
			    echo '<li onclick="set_item(\''.str_replace("'", "\'", $item['nombre']).'\','.$comilla.$codigo.$comilla.')">'.$pais_nombre.'</li>';
			}

	/* Codifica el resultado del array en JSON. */
		echo json_encode($return_arr);
	}
	if ($r==0) {echo 'No hay resultados';}
}// Cierre if post