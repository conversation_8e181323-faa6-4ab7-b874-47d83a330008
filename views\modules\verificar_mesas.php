<?php

session_start();

if(!$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "../../models/conexion.php";

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Verificar Mesas</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
</head>
<body>

<div class="container">
    <h2>🔍 Verificar Mesas en Base de Datos</h2>
    <hr>
    
    <div class="panel panel-default">
        <div class="panel-heading">
            <h3 class="panel-title">📊 Estructura de la Tabla Mesas</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                $stmt = Conexion::conectar()->prepare("DESCRIBE mesas");
                $stmt->execute();
                $estructura = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                echo "<table class='table table-striped'>";
                echo "<thead><tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Clave</th><th>Default</th><th>Extra</th></tr></thead>";
                echo "<tbody>";
                foreach ($estructura as $campo) {
                    echo "<tr>";
                    echo "<td><strong>{$campo['Field']}</strong></td>";
                    echo "<td>{$campo['Type']}</td>";
                    echo "<td>{$campo['Null']}</td>";
                    echo "<td>{$campo['Key']}</td>";
                    echo "<td>{$campo['Default']}</td>";
                    echo "<td>{$campo['Extra']}</td>";
                    echo "</tr>";
                }
                echo "</tbody></table>";
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>❌ Error al obtener estructura: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-default">
        <div class="panel-heading">
            <h3 class="panel-title">🏠 Mesas Existentes</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                $stmt = Conexion::conectar()->prepare("SELECT * FROM mesas ORDER BY numero LIMIT 20");
                $stmt->execute();
                $mesas = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (count($mesas) > 0) {
                    echo "<div class='alert alert-success'>✅ Encontradas " . count($mesas) . " mesas</div>";
                    
                    echo "<table class='table table-striped'>";
                    echo "<thead><tr>";
                    
                    // Mostrar headers dinámicamente basado en las columnas que existen
                    $primera_mesa = $mesas[0];
                    foreach (array_keys($primera_mesa) as $columna) {
                        echo "<th>" . ucfirst($columna) . "</th>";
                    }
                    echo "</tr></thead>";
                    echo "<tbody>";
                    
                    foreach ($mesas as $mesa) {
                        echo "<tr>";
                        foreach ($mesa as $valor) {
                            echo "<td>{$valor}</td>";
                        }
                        echo "</tr>";
                    }
                    echo "</tbody></table>";
                    
                } else {
                    echo "<div class='alert alert-warning'>⚠️ No se encontraron mesas en la base de datos</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>❌ Error al consultar mesas: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-default">
        <div class="panel-heading">
            <h3 class="panel-title">🍽️ Productos Existentes</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                $stmt = Conexion::conectar()->prepare("SELECT COUNT(*) as total FROM productos");
                $stmt->execute();
                $total_productos = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
                
                echo "<div class='alert alert-info'>📦 Total de productos: <strong>{$total_productos}</strong></div>";
                
                if ($total_productos > 0) {
                    $stmt = Conexion::conectar()->prepare("SELECT id, nombre, categoria, precio FROM productos ORDER BY categoria, nombre LIMIT 10");
                    $stmt->execute();
                    $productos = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    
                    echo "<h4>Primeros 10 productos:</h4>";
                    echo "<table class='table table-striped'>";
                    echo "<thead><tr><th>ID</th><th>Nombre</th><th>Categoría</th><th>Precio</th></tr></thead>";
                    echo "<tbody>";
                    foreach ($productos as $producto) {
                        echo "<tr>";
                        echo "<td>{$producto['id']}</td>";
                        echo "<td>{$producto['nombre']}</td>";
                        echo "<td>{$producto['categoria']}</td>";
                        echo "<td>$" . number_format($producto['precio'], 0) . "</td>";
                        echo "</tr>";
                    }
                    echo "</tbody></table>";
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>❌ Error al consultar productos: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-default">
        <div class="panel-heading">
            <h3 class="panel-title">📋 Pedidos Existentes</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                $stmt = Conexion::conectar()->prepare("SELECT COUNT(*) as total FROM pedidos");
                $stmt->execute();
                $total_pedidos = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
                
                echo "<div class='alert alert-info'>📋 Total de pedidos: <strong>{$total_pedidos}</strong></div>";
                
                if ($total_pedidos > 0) {
                    $stmt = Conexion::conectar()->prepare("
                        SELECT p.id, p.numero_pedido, p.estado, p.mesa_id, m.numero as mesa_numero 
                        FROM pedidos p 
                        LEFT JOIN mesas m ON p.mesa_id = m.id 
                        ORDER BY p.fecha_pedido DESC 
                        LIMIT 10
                    ");
                    $stmt->execute();
                    $pedidos = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    
                    echo "<h4>Últimos 10 pedidos:</h4>";
                    echo "<table class='table table-striped'>";
                    echo "<thead><tr><th>ID</th><th>Número</th><th>Estado</th><th>Mesa ID</th><th>Mesa Número</th></tr></thead>";
                    echo "<tbody>";
                    foreach ($pedidos as $pedido) {
                        $clase = '';
                        if ($pedido['estado'] == 'enviado') $clase = 'warning';
                        if ($pedido['estado'] == 'entregado') $clase = 'success';
                        
                        echo "<tr class='{$clase}'>";
                        echo "<td>{$pedido['id']}</td>";
                        echo "<td><strong>{$pedido['numero_pedido']}</strong></td>";
                        echo "<td>{$pedido['estado']}</td>";
                        echo "<td>{$pedido['mesa_id']}</td>";
                        echo "<td>{$pedido['mesa_numero']}</td>";
                        echo "</tr>";
                    }
                    echo "</tbody></table>";
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>❌ Error al consultar pedidos: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <hr>
    <div class="row">
        <div class="col-md-3">
            <a href="views/modules/crear_pedido_prueba.php" class="btn btn-success btn-block">🧪 Crear Pedido de Prueba</a>
        </div>
        <div class="col-md-3">
            <a href="views/modules/cambiar_estado_pedido.php" class="btn btn-warning btn-block">🔧 Cambiar Estado Pedidos</a>
        </div>
        <div class="col-md-3">
            <a href="pedidosCocinaPendientes" class="btn btn-info btn-block">🍳 Ver Cocina</a>
        </div>
        <div class="col-md-3">
            <a href="pedidosBarPendientes" class="btn btn-primary btn-block">🍺 Ver Bar</a>
        </div>
    </div>
</div>

</body>
</html>
