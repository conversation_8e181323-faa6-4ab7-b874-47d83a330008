<?php
require_once "conexion.php";
require_once "crudSuministroProducto.php";
require_once "crudProductos.php";
class DatosSuministro extends Conexion
 {
	#Buscar Suministro Nombre Compra
	#----------------------------------------------------
	 public static function  ajaxBuscarSuministroCompraModel($placa)
	 {	//$consulta="SELECT * from suministros where nombre like '%".$placa."%' ";
		$consulta="SELECT s.nombre AS snombre, s.codigo AS scodigo, s.cantidad AS scantidad,
		s.precio AS sprecio, s.cantidad_minima AS scantidad_minima, s.activo AS sactivo, s.tipo As stipo, s.tamano AS stamano, s.color AS scolor,
		s.unidad_id AS sunidad_id,
		p.id AS pid, su.cantidad AS sucantidad, p.precio AS pprecio
		from suministros s, sucursal su, productos p, suministros_productos sp, unidades u where sp.producto_id=p.id AND s.id=sp.suministro_id AND s.id=su.suministro_id AND u.id=s.unidad_id AND s.nombre like '%".$placa."%' ";
		//echo $consulta;
		$stmt = Conexion::conectar()->prepare($consulta);
		$stmt->execute();
		$r=$stmt->fetchAll();
		$c2=$stmt->rowCount();
		//echo '<br> C2='.$c2.'<br>';
		if ($c2>0) 	 {	return $r;	 }
		else  {	return 0;	}
		$stmt->close();
	 }
	#---------------------------------------------	
	#EDITAR SUCURSAL
	#--------------------------------------
	 public static function editarSucusalModel($datosModel, $tabla)
		{
			$consulta="SELECT s.id AS sid, s.codigo AS scodigo, s.nombre AS snombre, s.activo AS sactivo, su.punto_id AS supunto_id, su.suministro_id AS susuministro_id, su.cantidad AS sucantidad, s.nombre AS sudescripcion FROM suministros s, sucursal su where s.activo='s' AND s.id=su.suministro_id AND su.punto_id=:punto_id AND s.id=:suministro";

			// $consulta2 ="SELECT s.id AS sid, s.codigo AS scodigo, s.nombre AS snombre, s.activo AS sactivo, p.precio AS pprecio, p.id AS pid, su.punto_id AS supunto_id, su.suministro_id AS susuministro_id, su.cantidad AS sucantidad, s.nombre AS sudescripcion FROM suministros s, productos p, sucursal su, suministros_productos sp where s.activo='s' AND s.id=su.suministro_id AND sp.suministro_id=su.suministro_id AND sp.producto_id=p.id AND su.punto_id=:punto_id AND s.id=:suministro";

			//echo "<br>".$consulta."<br>-->Punto id:".$datosModel["punto_id"]." Suministro->".$datosModel["suministro"]."<br>";
			//$datosModel["punto_id"]=1;
			$punto=1;
				
			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->bindParam(":suministro", $datosModel["suministro"], PDO::PARAM_INT);	
			$stmt->bindParam(":punto_id", $punto, PDO::PARAM_INT);	
			//echo "<br>".$consulta."<br>";
			$stmt->execute();
			return $stmt->fetch();
			$stmt->close();
		}
	#----------------------------------------------------
	#Buscar Proceso por titulo con ajax
	#----------------------------------------------------
	 public static function  ajaxBuscarSuministroModel($placa)
	 {	//$consulta="SELECT * from suministros where nombre like '%".$placa."%' ";
		$consulta="SELECT s.id as sid, s.nombre AS snombre, s.codigo AS scodigo, s.cantidad AS scantidad, p.id AS pid, su.cantidad AS sucantidad, p.precio AS pprecio from suministros s, sucursal su, productos p, suministros_productos sp where sp.producto_id=p.id AND s.id=sp.suministro_id AND s.id=su.suministro_id AND s.nombre like '%".$placa."%' ";
		//echo $consulta;
		$stmt = Conexion::conectar()->prepare($consulta);
		$stmt->execute();
		$r=$stmt->fetchAll();
		$c2=$stmt->rowCount();
		//echo '<br> C2='.$c2.'<br>';
		if ($c2>0) 	 {	return $r;	 }
		else  {	return 0;	}
		$stmt->close();
	 }
	#----------------------------------------------------
	#Buscar Ventas
	#------------------------------------
	 public static function ventasProducto($id_producto)
	  {
	  	$consulta="SELECT cantidad, valor_productos FROM pedido_productos_mesa WHERE productos_id=".$id_producto;
			//echo $consulta;
			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->execute();
			$r=$stmt->fetch();
			$c2=$stmt->rowCount();
			//echo '<br> C2='.$c2.'<br>';
			if ($c2>0)  {	return $r;	}
			else  {	return 0;	}
			$stmt->close();
	  }
	#-------------------------------------
	#Buscar Codigó Barra ajax  group by s.id
	#---------------------------------------------------------
	 public static function  ajaxBuscarCodigo1Model($placa)
	 {	//$consulta="SELECT * from suministros where nombre like '%".$placa."%' ";
		$consulta="SELECT s.id AS sid, s.nombre AS snombre, s.codigo AS scodigo, s.cantidad AS scantidad,s.precio AS sprecio,  p.id AS pid, su.cantidad AS sucantidad, p.precio AS pprecio
				from suministros s, sucursal su, productos p, suministros_productos sp where sp.producto_id=p.id AND s.id=sp.suministro_id AND s.id=su.suministro_id AND s.codigo like ".$placa." GROUP BY s.id ";
		//echo $consulta;
		$stmt = Conexion::conectar()->prepare($consulta);
		$stmt->execute();
		$r=$stmt->fetch();
		$c2=$stmt->rowCount();
		//echo '<br> C2='.$c2.'<br>';
		if ($c2>0)  {	return $r;	}
		else 	 {	return 0;	}
		$stmt->close();
	 }
	#-----------------------------------------------------------
	#Registro Perdidas
	#-------------------------------------
	 public static function registroPerdidasModel($datosModel)
		{	$stmt = Conexion::conectar();
			date_default_timezone_set("America/Bogota");
			$fecha=strftime("%Y-%m-%d %H:%M:%S");
			$suministro = DatosSuministro::editarSuministroModel($datosModel["suministro_id"], "suministros");
			try
			 {
				$stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
				$stmt->beginTransaction();
				$cantidad=$datosModel["cantidad"];
				$sid=$datosModel["suministro_id"];
				$precio=$suministro["precio"];
				$usuario=$datosModel["usuario"];
				$consulta = "INSERT INTO perdidas(suministros_id, cantidad,precio, fecha, personas_id) VALUES ($sid,$cantidad,$precio, '$fecha',$usuario) ";				//echo "<script>alert('Entro a la transaccion ".$cantidad." ' );</script>";
				//echo "<script>alert('consulta ".$consulta." ' );</script>";
				$stmt->exec($consulta);
				$consulta2 = "UPDATE sucursal set cantidad=cantidad-".$datosModel["cantidad"]." where  suministro_id=".$datosModel["suministro_id"];
				//echo "<script>alert('consulta2 ".$consulta2." ' );</script>";
				$stmt->exec($consulta2);
				$stmt->commit();
				return "success";
				$stmt->close();
			 }
			catch (Exception $e)
				{	$stmt->rollBack();
					print "Error!: ".$e->getMessage()."</br>";
					echo "<script>alert('Error ".$e->getMessage()." ' );</script>";
					return "Error!: ".$e->getMessage()."</br>";
				}
		}
	#----------------------------------
	#----------------------------------------------
	#REGISTRO DE SUMINISTROS PRODUCTO solo
	#-------------------------------------
	 public static function registroSuministroSoloModel($datosModel, $tabla)
		{	//echo "<script>alert('Entro CRUD  Nombre :".$datosModel["nombre"]." ')</script>";
			$suministro = DatosSuministro::buscarSuministroModel($datosModel["codigo"], "suministros");
			$stmt = Conexion::conectar();
			//echo "<script>alert('Buscando codigo Suministro ".$suministro."');</script>";
			if ($suministro!=0)
			 {	 echo "<h2><script>alert('El codigo ".$datosModel["codigo"]." ya existe');</script></h2>";
			 	echo "<h2>El codigo ".$datosModel["codigo"]." ya existe</h2>"; return "error";
			 }
			date_default_timezone_set("America/Bogota");
			$fecha=strftime("%Y-%m-%d %H:%M:%S");
			//echo "<script>alert('Entro crud Usuario ".$fecha."  ');</script>";
			try
			 {	//echo "<script>alert('Entro  try');</script>";
				$stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
				$stmt->beginTransaction();
				$nombre =$datosModel["nombre"];
				//echo "<script>alert('el mesero id=".$mesero." ' );</script>";	 codigo
				$bodega = $datosModel["cantidad"] - $datosModel["cantidad_minima"];
				$consulta = "INSERT INTO $tabla(codigo, tipo_suministro, nombre,  cantidad, precio, cantidad_minima, unidad_id, activo, iva) VALUES ('".$datosModel["codigo"]."',".$datosModel["tiposuministro"].", '".$nombre."',  ".$datosModel["cantidad"].", ".$datosModel["precio"].",  ".$datosModel["cantidad_minima"].", ".$datosModel["unidad_id"].", 's',  ".$datosModel["iva"].")";
				//echo "<script>alert('insertar tabla venta o factura ".$consulta." ' );</script>";
				echo "<br>-- ".$consulta." --<br>";
				$stmt->exec($consulta);
				$ultimoSuministro = $stmt->lastInsertId();	//ultimo id
				$consulta2 = "INSERT INTO sucursal(suministro_id, cantidad)VALUES ( ".$ultimoSuministro.",  0)";
				//echo "<script>alert('insertar tabla venta o factura ".$consulta2." ' );</script>";
				echo "<br>-- ".$consulta2." --<br>";
				$stmt->exec($consulta2);

				$stmt->commit();
				//echo "<script>alert('fin de cruD try todo bn');</script>";
				return "success";
				$stmt->close();
			 }
			catch (PDOException $e)
			 {	echo "<script>alert('Error Grave catch  ')</script>";
				 $stmt->rollBack();
				 echo "-+- ".$e;
				print "Error!: ".$e->getMessage()."</br>";
				return "Error!: ".$e->getMessage()."</br>";
			 }
		}
	#---------------------------------------------
	#REGISTRO DE SUMINISTROS
	#-------------------------------------
	 public static function registroSuministroModel($datosModel, $tabla)
		{	//echo "<script>alert('Entro CRUD  Nombre :".$datosModel["nombre"]." ')</script>";
			$suministro = DatosSuministro::buscarSuministroModel($datosModel["codigo"], "suministros");
			$stmt = Conexion::conectar();
			//echo "<script>alert('Buscando codigo Suministro ".$suministro."');</script>";
		 if ($suministro>0)
			{
			 $respuesta = Datos::listaSuministrosModel("suministros");
			  $f=0;
			 foreach ($respuesta as $row => $item) 
				{
				  if ($f==0) 
				 	{
				 	 $datosModel["codigo"]=$item["codigo"]+1;
				 	 break;
				 	}
				}
				
			}
			date_default_timezone_set("America/Bogota");
			$fecha=strftime("%Y-%m-%d %H:%M:%S");
			//echo "<script>alert('Entro crud Usuario ".$fecha."  ');</script>";
			try
			 {	//echo "<script>alert('Entro  try');</script>";
				$stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
				$stmt->beginTransaction();
				$nombre =$datosModel["nombre"];
				//echo "<script>alert('el mesero id=".$mesero." ' );</script>";	 codigo
				$bodega = $datosModel["cantidad"] - $datosModel["cantidad_minima"];
				//  Insertando en la tabla /
				//echo "<script>alert('consultar ahora');</script>";
				$cantidad=$datosModel["cantidad"];

				$consulta = "INSERT INTO $tabla(codigo, tipo_suministro, nombre, cantidad, precio, cantidad_minima, unidad_id, activo) VALUES (".$datosModel["codigo"].",".$datosModel["tiposuministro"].", '".$nombre."',0, ".$datosModel["precio"].",  ".$datosModel["cantidad_minima"].", '".$datosModel["unidad_id"]."', 's')";
				//echo "<script>alert('insertar tabla venta o factura ".$consulta." ' );</script>";
				echo "<br>-- ".$consulta." --<br>";
				$stmt->exec($consulta);
				$ultimoSuministro = $stmt->lastInsertId();	//ultimo id
				$consulta1 = "INSERT INTO productos (codigo, nombre, precio) VALUES (".$datosModel["codigo"].",'".$nombre."',".$datosModel["precioV"].")";
				echo "<br>-- ".$consulta1." --<br>";
				$stmt->exec($consulta1);
				$ultimoProducto = $stmt->lastInsertId();	//ultimo id
				$consulta2 = "INSERT INTO sucursal(suministro_id, cantidad)VALUES ( ".$ultimoSuministro.", $cantidad)";
				//echo "<script>alert('insertar tabla venta o factura ".$consulta2." ' );</script>";
				echo "<br>-- ".$consulta2." --<br>";
				$stmt->exec($consulta2);

				$consulta3 = "INSERT INTO suministros_productos(producto_id, suministro_id, cantidades)VALUES (".$ultimoProducto.", ".$ultimoSuministro.", 1)";
				//echo "<script>alert('insertar tabla venta o factura ".$consulta2." ' );</script>";
				//echo "<br>-- ".$consulta3." --<br>";
				$stmt->exec($consulta3);
				$stmt->commit();
				//echo "<script>alert('fin de cruD try todo bn');</script>";
				return "success";
				$stmt->close();
			 }
			catch (PDOException $e)
			 {	echo "<script>alert('Error Grave catch  ')</script>";
				 $stmt->rollBack();
				 echo "-+- ".$e;
				print "Error!: ".$e->getMessage()."</br>";
				return "Error!: ".$e->getMessage()."</br>";
			 }
		}
	#---------------------------------------------
	#REGISTRO SUMINISTRO PRODUCTO
	#-------------------------------------
	 public static function registro1SuministroModel($datosModel, $tabla)
		{ //echo "<script>alert('Entro suministro-Producto  Crud".$datosModel["nombre"]." ')</script>";
			$stmt = Conexion::conectar();
			/*$con = "SELECT  codigo FROM productos WHERE codigo=".$datosModel["codigo"];
			$stmt->exec($consulta);
			$c1=$stmt->rowCount();
			//echo "<script>alert('Buscando codigo Suministro ".$c1."');</script>";
			if ($c1>0)
			 {	 echo "<script>alert('El codigo ".$datosModel["codigo"]." ya existe');</script>";
			 	return "error";
			 }	*/
			date_default_timezone_set("America/Bogota");
			$fecha=strftime("%Y-%m-%d %H:%M:%S");
			//echo "<script>alert('Entro crud Usuario ".$fecha."  ');</script>";
			try
			{//echo "<script>alert('Entro  try');</script>";
				$stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
				$stmt->beginTransaction();
				$nombre =$datosModel["nombre"];
				//echo "<script>alert('el mesero id=".$mesero." ' );</script>";	 codigo
				$bodega = $datosModel["cantidad"] - $datosModel["cantidad_minima"];
				//  Insertando en la tabla /
				$consulta = "INSERT INTO $tabla(codigo, nombre,tipo_suministro, cantidad, precio, cantidad_minima) VALUES (".$datosModel["codigo"].", '$nombre',".$datosModel["tiposuministro"].",".$datosModel["cantidad"].", ".$datosModel["precio"].",  ".$datosModel["cantidad_minima"].")";
				//echo "<script>alert('insertar tabla venta o factura ".$consulta." ' );</script>";
				$stmt->exec($consulta);
				$ultimoSuministro = $stmt->lastInsertId();	//ultimo id

				//echo "<script>alert('ultimo id credito ".$ultimoSuministro."  ITEM 1: ".$datosModel["codigo"]." ITEM 2 ".$nombre." ITEM 3 ".$datosModel["precioV"]."' );</script>";

				$consulta1 = "INSERT INTO productos(codigo, nombre, precio) VALUES (".$datosModel["codigo"].", '$nombre',  ".$datosModel["precioV"].")";
				//echo "<script>alert('insertar tabla venta o factura ".$consulta1." ' );</script>";
				$stmt->exec($consulta1);
				$ultimoProducto = $stmt->lastInsertId();	//ultimo id
				echo "--- ".$consulta1;
				//echo "<script>alert('ultimo id credito ".$ultimoSuministro."  ITEM 1: ".$ultimoProducto." ITEM 2 ".$nombre." ITEM 3 ' );</script>";
				/*	$consulta2 = "INSERT INTO sucursal(suministro_id, cantidad)VALUES ( ".$ultimoSuministro.",  0)";
				//echo "<script>alert('insertar tabla venta o factura ".$consulta2." ' );</script>";
				$stmt->exec($consulta2);
			 echo "--- ".$consulta2;

				//echo "<script>alert('ultimo id credito ".$ultimoSuministro."  ITEM 1: ".$ultimoProducto." ITEM 2 ".$nombre." ITEM 3 ' );</script>";
					$consulta3 = "INSERT INTO suministros_productos(producto_id, suministro_id, cantidades) VALUES (".$ultimoProducto.", ".$ultimoSuministro.",  1)";
				//echo "<script>alert('insertar tabla venta o factura ".$consulta3." ' );</script>";
				$stmt->exec($consulta3);
			 echo "--- ".$consulta3;
				*/
				//echo "<script>alert('fin de cruz try')</script>";
				$stmt->commit();
				//echo "<script>alert('fin de cruD try todo bn');</script>";
				return "success";
				$stmt->close();
			}
			catch (PDOException $e)
			{	echo "<script>alert('catch entro error')</script>";
				 $stmt->rollBack();
				 echo "-+- ".$e;
				print "Error!: ".$e->getMessage()."</br>";
				return "Error!: ".$e->getMessage()."</br>";
			}

		}
	#---------------------------------------------
	#VISTA SUMINISTROS  ".$datosModel["codigo"]."
	#-------------------------------------
	 public static function vistaSuministro1Model($tabla)
	  // public static function vistaSuministroModel($tabla)
		{//echo "<script>alert('Entro suministro-Producto  Crud".$ini." noseCrud".$rango." ')</script>";

			$consulta="SELECT s.id AS sid, s.codigo AS scodigo, s.nombre AS snombre, s.cantidad AS scantidad, s.precio AS sprecio, s.cantidad_minima AS scantidad_minima, s.activo AS sactivo, s.tipo As stipo, s.tamano AS stamano, s.color AS scolor, s.unidad_id AS sunidad_id,
			p.precio AS pprecio,
			u.id AS uid, u.nombre As unombre
			FROM suministros s, productos p, unidades u
			where s.activo='s' AND s.codigo=p.codigo AND s.unidad_id=u.id AND s.cantidad>0
			group by p.id ORDER BY p.id  DESC";

			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->execute();
			return $stmt->fetchAll();
			$stmt->close();
		}
	#----------------------------------
	#VISTA SUMINISTROS PAGINACION ".$datosModel["codigo"]."
	#-------------------------------------
	 public static function vistaSuministroModel($tabla, $ini=FALSO, $rango=FALSO)
	  // public static function vistaSuministroModel($tabla)
		{//echo "<script>alert('Entro suministro-Producto  Crud".$ini." noseCrud".$rango." ')</script>";
			if ($ini!==FALSO && $rango!==FALSO)
			 {$limit="LIMIT ".$ini.",".$rango;	 }	else  {$limit=" ";}
			$consulta="SELECT s.id AS sid, s.codigo AS scodigo, s.nombre AS snombre, s.cantidad AS scantidad, s.precio AS sprecio, s.cantidad_minima AS scantidad_minima, s.activo AS sactivo, s.unidad_id AS sunidad_id, s.iva AS siva,
			u.id AS uid, u.nombre As unombre
			FROM suministros s, unidades u
			where s.activo='s' AND u.id=s.unidad_id
			group by s.id ORDER BY s.nombre  ASC ".$limit;

			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->execute();
			return $stmt->fetchAll();
			$stmt->close();
		}
	#----------------------------------
	#Lista SUMINISTROS ".$datosModel["codigo"]."
	#-------------------------------------
	 public static function listaSuministroModel($tabla)
		{
			$consulta="SELECT id, codigo, nombre, cantidad, precio, cantidad_minima, activo FROM $tabla where activo='s' order by nombre";
			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->execute();
			return $stmt->fetchAll();
			$stmt->close();
		}
	#----------------------------------
	#EDITAR SUMINISTROS
	#---------------------------------------------------------------------------------------------------------
	 public static function editarSuministroModel($datosModel, $tabla)
		{//echo "<script>alert('suministro editar ".$datosModel." ');</script>";
		 $consulta="SELECT s.id AS sid, s.codigo AS scodigo, s.nombre AS snombre, s.cantidad AS scantidad, s.precio AS sprecio, s.cantidad_minima AS scantidad_minima, s.activo AS sactivo, s.unidad_id AS sunidad_id, s.iva AS siva,
				u.id AS uid, u.nombre As unombre, p.precio as pprecio, p.id as pid
				FROM suministros s, unidades u, suministros_productos sp, productos p
				where p.id=sp.producto_id AND sp.suministro_id=s.id AND s.activo='s' AND s.unidad_id=u.id AND s.id=:id GROUP by s.id";
			$stmt = Conexion::conectar()->prepare($consulta);
			//echo "<br>".$consulta."<br>";
			$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);
			$stmt->execute();
			return $stmt->fetch();
			$stmt->close();
		}
	#------------------------------------------------------
	#ACTUALIZAR SUMINISTROS
	#-------------------------------------
	 public static function actualizarSuministroModel($datosModel, $tabla)
		{//echo "<script>alert('Entro suministro-Producto  Crud".$datosModel["nombre"]." ')</script>";
			$stmt = Conexion::conectar();
			date_default_timezone_set("America/Bogota");
			$fecha=strftime("%Y-%m-%d %H:%M:%S");
			//echo "<script>alert('Entro crud Usuario ".$fecha."  ');</script>";
			try
			 {	echo "<script>alert('Entro  try');</script>";
				$stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
				$stmt->beginTransaction();
				$consulta = "UPDATE $tabla SET codigo = '".$datosModel["scodigo"]."', nombre = '".$datosModel["snombre"]."', cantidad= ".$datosModel["scantidad"].", precio =".$datosModel["sprecio"]." WHERE id = ".$datosModel["id"];
			//echo "<script>alert('insertar tabla venta o factura ".$consulta." ' );</script>";
				echo "--".$consulta."<br>";
				$stmt->exec($consulta);
				$consulta1 = " UPDATE productos SET codigo = '".$datosModel["scodigo"]."', nombre = '".$datosModel["snombre"]."',  precio =".$datosModel["pprecio"]." WHERE id =".$datosModel["pid"];
			//echo "<script>alert('insertar tabla venta o factura ".$consulta." ' );</script>";
				echo "--".$consulta1."<br>";
				$stmt->exec($consulta1);
				$stmt->commit();
				//echo "<script>alert('fin de cruD try todo bn');</script>";
				return "success";
				$stmt->close();
			 }
			catch (PDOException $e)
			 {	echo "<script>alert('catch entro error')</script>";
				 $stmt->rollBack();
				 echo "-+- ".$e;
				print "Error!: ".$e->getMessage()."</br>";
				return "Error!: ".$e->getMessage()."</br>";
			 }
		}
	#----------------------------------------------------
	#BORRAR SUMINISTROS
	#------------------------------------
	 public static function borrarSuministroModel($datosModel, $tabla)
	 {//echo "<script>alert('entro CRUD".$datosModel["id"]." codigo: ".$datosModel["codigo"]."' );</script>";
		$stmt = Conexion::conectar();
		try
		 {
		 	$stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
			$stmt->beginTransaction();
			//  Insertando en la tabla cargar sucursal/
			$consulta = "DELETE FROM suministros_productos WHERE suministro_id = ".$datosModel["id"];
			//echo "<script>alert('insertar tabla sucursal".$consulta." ' );</script>";
			echo " --".$consulta."<br>";
			$stmt->exec($consulta);
			$consulta1 = "DELETE FROM sucursal WHERE suministro_id = ".$datosModel["id"];
			//echo "<script>alert('insertar tabla sucursal".$consulta." ' );</script>";
			echo " --".$consulta1."<br>";
			$stmt->exec($consulta1);
			$consulta2 = "DELETE FROM productos WHERE codigo = ".$datosModel["codigo"];
			//echo "<script>alert('insertar tabla sucursal".$consulta." ' );</script>";
			echo " --".$consulta2."<br>";
			$stmt->exec($consulta2);
			$consulta3 = "DELETE FROM $tabla WHERE id = ".$datosModel["id"];
			//echo "<script>alert('insertar tabla sucursal".$consulta." ' );</script>";
			echo " --".$consulta3."<br>"	;
			$stmt->exec($consulta3);
			//echo "<script>alert('fin de cruz try')</script>";
			$stmt->commit();
			return "success";
			$stmt->close();
	 	 }
	 	catch (Exception $e)
		 {	$stmt->rollBack();
			print "Error!: ".$e->getMessage()."</br>";
			return "Error!: ".$e->getMessage()."</br>";
		 }
	 }
	#-------------------------------------
	#BUSCAR SUMINISTROS POR CODIGO
	#-------------------------------------
	 public static function buscarSuministroModel($datosModel, $tabla)
		{	//echo "<script>alert('Buscando codigo Suministro Ahora ');</script>";
			$consulta = "SELECT id, codigo, nombre, cantidad, precio, cantidad_minima, activo FROM $tabla WHERE codigo = :codigo";
			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->bindParam(":codigo", $datosModel, PDO::PARAM_STR);
			$stmt->execute();
			$r=$stmt->fetch();
			//echo "<br>". $consulta."<br>";
			//echo "<script>alert('Buscando codigo Suministro ".$consulta." Id ".$r["id"]." ');</script>";
			$c=$stmt->rowCount();
			//echo '<br> C2='.$c2.'<br>';
			//echo "<script>alert('Buscando codigo Suministro ".$c."');</script>";
			if ($c>0)	{	return $r;	}
			else		{	return 0;	}
			$stmt->close();/**/
		}
	#---------------------------------------
	#SUCURSAL SUMINISTROSs
	#-------------------------------------
	 public static function sucursalSuministro1Model($datosModel, $tabla)
		{//echo "<script>alert('suministro sucursal ".$datosModel." ');</script>";
			$consulta = "SELECT punto_id, suministro_id, cantidad FROM sucursal WHERE suministro_id= :id AND punto_id=:punto";
			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);
			$stmt->bindParam(":punto", $tabla, PDO::PARAM_INT);
			$stmt->execute();
			$r=$stmt->fetchAll();
			//echo "<br>". $consulta."<br>";
			$c=$stmt->rowCount();
			//echo "<script>alert('Buscando codigo Suministro ".$c."');</script>";
			if ($c>0)
				{	return $r;	}
			else
				{	return 0;	}
			$stmt->close();/**/
		}
	#---------------------------------------
	#SUCURSAL SUMINISTROS
	#-------------------------------------
	 public static function sucursalSuministroModel($datosModel, $tabla)
		{//echo "<script>alert('suministro sucursal ".$datosModel." ');</script>";
			$consulta = "SELECT punto_id, suministro_id, cantidad FROM $tabla WHERE suministro_id= :id";
			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);
			$stmt->execute();
			$r=$stmt->fetch();
			//echo "<br>". $consulta."<br>";
			$c=$stmt->rowCount();
			//echo "<script>alert('Buscando codigo Suministro ".$c."');</script>";
			if ($c>0)
				{	return $r;	}
			else
				{	return 0;	}
			$stmt->close();/**/
		}
	#---------------------------------------
	#DESCARGAR SUCURASL
	#-------------------------------------
	 public static function descargarSucursalModel($datosModel, $tabla)
	  {	//echo "<script>alert('Buscando codigo Suministro ".$datosModel["codigo"]." ');</script>";
		 $suministro = DatosSuministro::buscarSuministroModel($datosModel["codigo"], "suministros");
		$suministroSucursal = DatosSuministro::sucursalSuministroModel($suministro["id"], "sucursal");
		//echo "<script>alert('datos del suministro id : ".$suministroSucursal["suministro_id"]." cantidad : ".$suministroSucursal["cantidad"]." ');</script>";

		if ($suministroSucursal["cantidad"]<=0)
		 {	echo "<script>alert('No hay suministro que descargarsu cantidad es: ".$suministroSucursal["cantidad"]." ')</script>";
			header("location:index.php?action=descargarSucursal");
		 }
		if ($suministro ==0)
		 {
			echo "<script>alert('Verifique el código, porque, no se encuentra en el sistema')</script>";
		 }
		else
			 { 	$cantidadcaja=$suministro["cantidad"];
			 	if ($datosModel["cantidad"] >$suministroSucursal["cantidad"])
			 	 {
			 		echo "<script>alert('la cantidad maxima a pasar es ".$suministroSucursal["cantidad"]."')</script>";
			 	 }
			 	else
			 	 {
			 	 	$stmt = Conexion::conectar();
			 	 	date_default_timezone_set("America/Bogota");
					$fecha=strftime("%Y-%m-%d %H:%M:%S");
			 	  try
			 	 	 {
			 	 	 	echo "<script>alert('Entro try ".$datosModel["codigo"]." ');</script>";
					 	$stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
						$stmt->beginTransaction();
						//  Insertando en la tabla cargar sucursal/
						$totalCantidad = $cantidadcaja + $datosModel["cantidad"];
						$totalCanti = $suministroSucursal["cantidad"] - $datosModel["cantidad"];

						echo "<script>alert('suma : ".$totalCantidad." = ".$cantidadcaja." +  ".$datosModel["cantidad"]."')</script>";
					 	$consulta = "UPDATE $tabla SET cantidad= ".$totalCanti." WHERE suministro_id = ".$suministro["id"];
						//echo "<script>alert('insertar tabla sucursal".$consulta." ' );</script>";
						echo "<br> --".$consulta." --<br>";
						$stmt->exec($consulta);


						///  Actualizar cantidades de Suministro //
						//$final 	= $suministro["cantidad"] - $cantidadcaja;

						$consulta1="UPDATE suministros SET cantidad= ".$totalCantidad." WHERE id = ".$suministro["id"];
						//echo "<script>alert('insertar tabla venta o factura ".$consulta2." ' );</script>";
						//echo " --".$consulta1;
						$stmt->exec($consulta1);
						//echo "<script>alert('fin de cruz try')</script>";
						$stmt->commit();
						//echo "<script>alert('fin de cruD try que pasa');</script>";
						return "success";
						$stmt->close();
				 	 }
				 	catch (Exception $e)
					 {	$stmt->rollBack();
						print "Error!: ".$e->getMessage()."</br>";
						return "Error!: ".$e->getMessage()."</br>";
					 }
				 }

			}
	  }
	#---------------------------------------------
	#CARGAR SUCURASL por UNIDAD
	#-------------------------------------
	 public static function cargarSucursalModel($datosModel, $tabla)
	 {	//echo "<script>alert('Buscando codigo Suministro ".$datosModel["codigo"]." ');</script>";
		 $suministro = DatosSuministro::buscarSuministroModel($datosModel["codigo"], "suministros");
		//echo "<script>alert('datos del suministro id : ".$suministro["id"]." codigo : ".$suministro["codigo"]." nombre : ".$suministro["nombre"]."cantidad : ".$suministro["cantidad"]." precio : ".$suministro["precio"]."');</script>";
		$suministroSucursal = DatosSuministro::sucursalSuministroModel($suministro["id"], "sucursal");
		//echo "<script>alert('datos del suministro id : ".$suministroSucursal["suministro_id"]." cantidad : ".$suministroSucursal["cantidad"]." ');</script>";
		if ($suministroSucursal==0)	 {	$cantidadcaja= 0; }
		else { 	$cantidadcaja 	= $suministroSucursal["cantidad"];	 }
		if ($suministro ==0)
		 {echo "<script>alert('Verifique el código, porque, no se encuentra en el sistema')</script>";	 }
		else
		 {
		 	if ($datosModel["cantidad"] >$suministro["cantidad"])
		 	 {	echo "<script>alert('la cantidad maxima a pasar es ".$suministro["cantidad"]."')</script>"; 	 }
		 	else
		 	 {
		 	 	$stmt = Conexion::conectar();
		 	 	date_default_timezone_set("America/Bogota");
				$fecha=strftime("%Y-%m-%d %H:%M:%S");
		 		try
		 	 	 {
				 	$stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
					$stmt->beginTransaction();
					//  Insertando en la tabla cargar sucursal/
					$totalCantidad = $cantidadcaja + $datosModel["cantidad"];
					if ($suministroSucursal==0)
					 {
						$consulta = "INSERT INTO $tabla(suministro_id, cantidad) VALUES (".$suministro["id"].",".$totalCantidad.")";
						//echo "<script>alert('insertar tabla sucursal".$consulta." ' );</script>";
						//echo " --".$consulta;
						$stmt->exec($consulta);
					 }
					else
					 {
					 	$consulta = "UPDATE $tabla SET punto_id= ".$datosModel["punto_id"].", cantidad= ".$totalCantidad." WHERE suministro_id = ".$suministro["id"];
						//echo "<script>alert('insertar tabla sucursal".$consulta." ' );</script>";
						//echo " <br>".$consulta." <br>";
						$stmt->exec($consulta);
					 }
					///  Actualizar cantidades de Suministro //
					//$final 	= $suministro["cantidad"] - $cantidadcaja;
					$cantidad_mod=$datosModel["cantidad"];
					$consulta1="UPDATE suministros SET cantidad= cantidad-".$cantidad_mod." WHERE id = ".$suministro["id"];		//echo " <br>".$consulta1." <br>";
					$stmt->exec($consulta1);
					#----------------------------------------------traslado	--------------------------------
						$consulta2="INSERT INTO traslados(suministro_id, personas_id, fecha_creado, cantidad_origen, cantidad_destino, fecha_modificacion, traslado, punto_id) VALUES (".$suministro["id"].",".$datosModel["cajero"].",'".$fecha."',".$suministro["cantidad"]." ,".$suministroSucursal["cantidad"].",'".$fecha."', ".$datosModel["cantidad"].", ".$suministroSucursal["punto_id"].")";
						echo " <br>".$consulta2." <br>";
						$stmt->exec($consulta2);
					#-----------------------------------------------------------------------
					$stmt->commit();
					//echo "<script>alert('fin de cruD try que pasa');</script>";
					return "success";
					$stmt->close();
			 	 }
			 	catch (Exception $e)
				 {	$stmt->rollBack();
					print "Error!: ".$e->getMessage()."</br>";
					return "Error!: ".$e->getMessage()."</br>";
				 }
			 }
		}

	 }
	#---------------------------------------------
	#CARGAR SUCURASL MAXIVO
	#-------------------------------------
	 public static function cargarSucursal1Model($datosModel, $sucursal, $con)
		{	//echo "<script>alert('Con:".$con."')</script>";
		//echo "<script>alert(' i:".$con."CRUD SID:".$datosModel["0"]["sid"]." cantidad: ".$datosModel["0"]["cantidad"]." suid: ".$datosModel["0"]["suid"]."')</script>";
		 date_default_timezone_set("America/Bogota");
		 $fecha=strftime("%Y-%m-%d %H:%M:%S");
		 for ($i=0; $i <$con ; $i++)
			{ echo "<script>alert(' i:".$i."CRUD SID:".$datosModel[$i]["sid"]." cantidad: ".$datosModel[$i]["cantidad"]." suid: ".$datosModel[$i]["suid"]."')</script>";
			 $suministroSucursal = DatosSuministro::sucursalSuministro1Model($datosModel[$i]["sid"], $sucursal);
			 $suministro = DatosSuministro::editarSuministroModel($datosModel[$i]["sid"], $sucursal);
				//echo "<script>alert('suministro id : ".$suministroSucursal["suministro_id"]." from id : ".$datosModel[$i]["suid"]." ');</script>";
			 foreach ($suministroSucursal as $siguiente => $su)
				{//echo "<script>alert('datos del suministro id : ".$su["suministro_id"]." cantidad : ".$su["cantidad"]." ');</script>";
				 if ($suministroSucursal==0)
					{	$cantidadcaja 	=  0;
					 	$sw=0;
					}
				 else
					{
					 $cantidadcaja 	= $su["cantidad"]; $sw=1;
					}
			    }
			 $stmt = Conexion::conectar();
			 try
			 	{
				  $stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
				 $stmt->beginTransaction();
					//  Insertando en la tabla cargar sucursal/
				 $totalCantidad = $cantidadcaja + $datosModel[$i]["cantidad"];
				  if ($suministro['scantidad']<$datosModel[$i]["cantidad"])
					{
						echo "<script>alert('Este producto ".$suministro['scantidad']." ssssss ".$datosModel[$i]["snombre"]."cantidad es mayor :".$datosModel[$i]["cantidad"]." ' );</script>" ;
					}
				 else
				 	{
				 	 if ($sw==0)
						{
						 $consulta = "INSERT INTO sucursal(punto_id, suministro_id, cantidad) VALUES (".$sucursal.", ".$datosModel[$i]["suid"].", ".$totalCantidad.")";
							//echo "<script>alert('insertar tabla sucursal".$consulta." ' );</script>";
						 echo " no existe<br>".$consulta." <br>";
							$stmt->exec($consulta);
						}
					 elseif ($sw==1)
						{
						 $consulta = "UPDATE sucursal SET cantidad= ".$totalCantidad." WHERE punto_id=$sucursal AND suministro_id = ".$datosModel[$i]["suid"];
							//echo "<script>alert('insertar tabla sucursal".$consulta." ' );</script>" ;
						 echo " <br> valor i:".$i." <br>";
						 echo "Actualizar <br>".$consulta." <br>";
							$stmt->exec($consulta);
						}
							///  Actualizar cantidades de Suministro //
					 $cantidad_mod=$datosModel[$i]["cantidad"];
					 $consulta1="UPDATE suministros SET cantidad= cantidad-".$cantidad_mod." WHERE id = ".$datosModel[$i]["suid"];
						//echo "<script>alert('insertar tabla venta o factura ".$consulta2." ' );</script>";
					 echo " <br>".$consulta1." <br>";
					 $stmt->exec($consulta1);
						///  Actualizar cantidades de Suministro //	 scantidad
					 $cantidad_mod=$datosModel[$i]["cantidad"];
					 $consulta2="INSERT INTO traslados(suministro_id, personas_id, fecha_creado, cantidad_origen, cantidad_destino, traslado) VALUES (".$datosModel[$i]["suid"].", ".$_SESSION["usuario"].", '$fecha', ".$suministro["scantidad"].", ".$cantidadcaja.", ".$cantidad_mod.")";
					 echo " <br>".$consulta2." <br>";
							//echo "<script>alert('insertar tabl a venta o factura ".$consulta2." ' );</script>";
							//echo " <br>".$consulta2." <br>cantidad Sucursal".$cantidadcaja." <br>"." <br>cantidad Sucursal22".$suministroSucursal["cantidad"]." <br>";
						$stmt->exec($consulta2);
				}

						//echo "<script>alert('fin de cruz try')</script>";
				 $stmt->commit();
						//echo "<script>alert('fin de cruD try que pasa');</script>";
				 $stmt->close(); return "success";
				}
			 catch (Exception $e)
				{	echo "<script>alert('Entro Catch');</script>";
				 $stmt->rollBack();
				 print "Error!: ".$e->getMessage()."</br>";
				 return "Error!: ".$e->getMessage()."</br>";
				}
			}	
		}
	#---------------------------------------------
	#VISTA SUCURSAL
	#-------------------------------------
	 public static function vistaSucursalModel($tabla)
	  {
		$consulta="SELECT s.id AS sid, s.codigo AS scodigo, s.nombre AS snombre, su.cantidad AS scantidad, su.cantidad AS sucantidad, su.suministro_id AS suid, s.precio AS sprecio, s.cantidad_minima AS sminima,s.precio AS pcosto FROM sucursal su, suministros s WHERE su.suministro_id = s.id and s.tipo_suministro=1 and s.activo='s' ORDER by s.nombre; ";

		//$consultaBase="SELECT s.id AS sid, s.codigo AS scodigo, s.nombre AS snombre, su.cantidad AS scantidad,  su.cantidad AS sucantidad, su.suministro_id AS suid, s.precio AS sprecio, s.cantidad_minima AS sminima, p.precio AS pprecio,s.precio AS pcosto  FROM sucursal su, suministros s, productos p, suministros_productos sp WHERE sp.suministro_id=s.id AND sp.producto_id=p.id AND su.suministro_id = s.id and s.tipo_suministro=1 and s.activo='s' ORDER by s.nombre";

		$stmt = Conexion::conectar()->prepare($consulta);
		$stmt->execute();
		return $stmt->fetchAll();
		$stmt->close();

	  }
	#----------------------------------
	#VISTA SUCURSAL
	#-------------------------------------
	 public static function vistaSucursalCocinaModel($tabla)
		{
			$consulta="SELECT s.id AS sid, s.codigo AS scodigo, s.nombre AS snombre, su.cantidad AS scantidad, su.cantidad AS sucantidad, su.suministro_id AS suid, s.precio AS sprecio, s.cantidad_minima AS sminima FROM sucursal su, suministros s WHERE su.suministro_id = s.id and s.tipo_suministro=2 and s.activo='s'";
			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->execute();
			return $stmt->fetchAll();
			$stmt->close();

		}
	#----------------------------------
	#BOVEDA
	#-------------------------------------
	 public static function bovedaModel($datosModel, $tabla)
	 {	//echo "<script>alert('Buscando codigo Suministro ".$datosModel["codigo"]." ');</script>";
		 $suministro = DatosSuministro::buscarSuministroModel($datosModel["codigo"], "suministros");
		$suministroSucursal = DatosSuministro::sucursalSuministroModel($suministro["id"], "sucursal");
		//echo "<script>alert('datos del suministro id : ".$suministroSucursal["suministro_id"]." cantidad : ".$suministroSucursal["cantidad"]." ');</script>";
		if ($suministroSucursal==0)	 {	$cantidadcaja 	=  0;	 }
		else	 { 	$cantidadcaja 	= $suministroSucursal["cantidad"];		 }
		if ($suministro ==0)
		 {	echo "<script>alert('Verifique el código, porque, no se encuentra en el sistema')</script>"; }
		else
		 {
		 	if ($datosModel["cantidad"] >$suministro["cantidad"])
		 	 {	echo "<script>alert('la cantidad maxima a pasar es ".$suministro["cantidad"]."')</script>";	 }
		 	else
		 	 {
		 	 	$stmt = Conexion::conectar();
		 	 	date_default_timezone_set("America/Bogota");
				$fecha=strftime("%Y-%m-%d %H:%M:%S");
		 		try
		 	 	 {
				 	$stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
					$stmt->beginTransaction();
					//  Insertando en la tabla cargar sucursal/
					$totalCantidad = $cantidadcaja + $datosModel["cantidad"];
					if ($suministroSucursal==0)
					 {
						$consulta = "INSERT INTO $tabla(suministro_id, cantidad) VALUES (".$suministro["id"].",".$totalCantidad.")";
						//echo "<script>alert('insertar tabla sucursal".$consulta." ' );</script>";
						//echo " --".$consulta;
						$stmt->exec($consulta);
					 }
					else
					 {
					 	$consulta = "UPDATE $tabla SET cantidad= ".$totalCantidad." WHERE suministro_id = ".$suministro["id"];
						//echo "<script>alert('insertar tabla sucursal".$consulta." ' );</script>";
						//echo " --".$consulta;
						$stmt->exec($consulta);
					 }
					///  Actualizar cantidades de Suministro //
					$final 	= $suministro["cantidad"] - $cantidadcaja;
					$consulta1="UPDATE suministros SET cantidad= ".$final." WHERE id = ".$suministro["id"];
					//echo "<script>alert('insertar tabla venta o factura ".$consulta2." ' );</script>";
					//echo " --".$consulta1;
					$stmt->exec($consulta1);
					//echo "<script>alert('fin de cruz try')</script>";
					$stmt->commit();
					//echo "<script>alert('fin de cruD try que pasa');</script>";
					return "success";
					$stmt->close();
			 	 }
			 	catch (Exception $e)
				 {	$stmt->rollBack();
					print "Error!: ".$e->getMessage()."</br>";
					return "Error!: ".$e->getMessage()."</br>";
				 }
			 }

		}

	 }
	#------------------------------------------------------
	#ACTUALIZAR SUMINISTROS
	#-------------------------------------
	 public static function actualizarSucursalModel($datosModel, $tabla)
		{//echo "<script>alert('Entro suministro-Producto  Crud".$datosModel["nombre"]." ')</script>";	
		$stmt = Conexion::conectar(); 															
			date_default_timezone_set("America/Bogota");
			$fecha=strftime("%Y-%m-%d %H:%M:%S");
			//echo "<script>alert('Entro crud Usuario ".$fecha."  ');</script>";
			try
			 {	//echo "<script>alert('Entro  try');</script>";								
				$stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);  
				$stmt->beginTransaction();
				/*$consulta = "UPDATE $tabla SET punto_id = '".$datosModel["sudescuentoP"]."', suministro_id = '".$datosModel["sucantidad"]."',  cantidad= ".$datosModel["sudescripcion"].", descuentoP= '".$datosModel["sid"]."', mas_meno =".$datosModel["supunto_id"]." WHERE id = ".$datosModel["id"];*/
				$consulta = "UPDATE $tabla SET cantidad = '".$datosModel["sucantidad"]."' WHERE suministro_id = ".$datosModel["sid"]." and punto_id=".$datosModel["supunto_id"];		
			//echo "<script>alert('insertar tabla venta o factura ".$consulta." ' );</script>";
				echo "--".$consulta."<br>";
				$stmt->exec($consulta);		
				/*$consulta1 = " UPDATE productos SET codigo = '".$datosModel["scodigo"]."', nombre = '".$datosModel["snombre"]."', nombre1 = '".$datosModel["nombre1"]."',  precio =".$datosModel["pprecio"]." ,  precio1 =".$datosModel["pprecio1"]." WHERE id =".$datosModel["pid"];	
			//echo "<script>alert('insertar tabla venta o factura ".$consulta." ' );</script>";
				//echo "--".$consulta1."<br>";
				$stmt->exec($consulta1);*/		
				$stmt->commit();
				//echo "<script>alert('fin de cruD try todo bn');</script>";
				return "success";
				$stmt->close();						
			 }							
			catch (PDOException $e)
			 {	echo "<script>alert('catch entro error')</script>";
				 $stmt->rollBack(); 
				 echo "-+- ".$e;
				print "Error!: ".$e->getMessage()."</br>";
				return "Error!: ".$e->getMessage()."</br>";   
			 }
		}
	#----------------------------------------------------
	#---------------------------------------------

 }