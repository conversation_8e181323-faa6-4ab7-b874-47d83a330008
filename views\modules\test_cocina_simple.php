<?php

session_start();

if(!$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "../../models/crudPedidosCategorias_simple.php";

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Test - Cocina Simple</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
    <style>
        .panel-danger .panel-heading {
            background-color: #d9534f !important;
            border-color: #d43f3a !important;
            color: white !important;
        }
        .panel-warning .panel-heading {
            background-color: #f0ad4e !important;
            border-color: #eea236 !important;
            color: white !important;
        }
        .panel-info .panel-heading {
            background-color: #5bc0de !important;
            border-color: #46b8da !important;
            color: white !important;
        }
        .refresh-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body>

<div class="refresh-btn">
    <button onclick="location.reload()" class="btn btn-info btn-sm">
        <i class="glyphicon glyphicon-refresh"></i> Actualizar
    </button>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <h2>🍳 Test - Cocina Simple (Todos los Pedidos)</h2>
            <hr>
        </div>
    </div>

<?php

// Obtener pedidos usando la versión simple
$pedidos = DatosPedidosCategoriasSimple::obtenerPedidosPendientesModel('cocina');
$estadisticas = DatosPedidosCategoriasSimple::obtenerEstadisticasDiaModel('cocina');

// Mostrar estadísticas
echo "<div class='row' style='margin-bottom: 20px;'>";
echo "<div class='col-md-4'>";
echo "<div class='panel panel-primary'>";
echo "<div class='panel-heading'><h4>📋 Pendientes</h4></div>";
echo "<div class='panel-body text-center'>";
echo "<h2 style='color: #28a745;'>{$estadisticas['pendientes']}</h2>";
echo "</div></div></div>";

echo "<div class='col-md-4'>";
echo "<div class='panel panel-success'>";
echo "<div class='panel-heading'><h4>✅ Entregados Hoy</h4></div>";
echo "<div class='panel-body text-center'>";
echo "<h2 style='color: #28a745;'>{$estadisticas['entregados_hoy']}</h2>";
echo "</div></div></div>";

echo "<div class='col-md-4'>";
echo "<div class='panel panel-info'>";
echo "<div class='panel-heading'><h4>💰 Ventas Hoy</h4></div>";
echo "<div class='panel-body text-center'>";
echo "<h2 style='color: #17a2b8;'>$" . number_format($estadisticas['total_ventas_hoy'], 0) . "</h2>";
echo "</div></div></div>";
echo "</div>";

if (empty($pedidos)) {
    echo "<div class='alert alert-info text-center'>";
    echo "<h3>🍳 ¡Excelente!</h3>";
    echo "<p>No hay pedidos pendientes en Cocina.</p>";
    echo "</div>";
} else {
    echo "<div class='panel panel-default'>";
    echo "<div class='panel-heading'>";
    echo "<h3 class='panel-title'>📋 Pedidos Pendientes - " . count($pedidos) . " pedido(s)</h3>";
    echo "</div>";
    echo "<div class='panel-body'>";
    
    foreach ($pedidos as $pedido) {
        // Calcular tiempo de espera
        $ahora = new DateTime();
        $envio = new DateTime($pedido['fecha_envio']);
        $diferencia = $ahora->diff($envio);
        
        if ($diferencia->h > 0) {
            $tiempo_espera = $diferencia->h . "h " . $diferencia->i . "m";
        } else {
            $tiempo_espera = $diferencia->i . " min";
        }
        
        // Determinar clase de urgencia
        $minutos = $diferencia->h * 60 + $diferencia->i;
        if ($minutos > 30) {
            $clase_urgencia = 'danger';  // Rojo - Muy urgente
        } elseif ($minutos > 15) {
            $clase_urgencia = 'warning'; // Amarillo - Urgente
        } else {
            $clase_urgencia = 'info';    // Azul - Normal
        }
        
        echo "<div class='panel panel-{$clase_urgencia}' style='margin-bottom: 15px;'>";
        echo "<div class='panel-heading'>";
        echo "<div class='row'>";
        echo "<div class='col-md-6'>";
        echo "<h4 style='margin: 0;'>";
        echo "<strong>{$pedido['numero_pedido']}</strong> - Mesa {$pedido['mesa_numero']}";
        echo "</h4>";
        echo "</div>";
        echo "<div class='col-md-3 text-center'>";
        echo "<span class='label label-{$clase_urgencia}' style='font-size: 12px;'>";
        echo "⏱️ {$tiempo_espera}";
        echo "</span>";
        echo "</div>";
        echo "<div class='col-md-3 text-right'>";
        echo "<form method='post' style='display: inline;'>";
        echo "<input type='hidden' name='pedido_id' value='{$pedido['pedido_id']}'>";
        echo "<button type='submit' class='btn btn-success btn-sm'>";
        echo "<i class='glyphicon glyphicon-ok'></i> Marcar Entregado";
        echo "</button>";
        echo "</form>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        
        echo "<div class='panel-body'>";
        echo "<div class='row'>";
        echo "<div class='col-md-8'>";
        echo "<h5><strong>Productos:</strong></h5>";
        echo "<p style='font-size: 14px;'>{$pedido['productos_detalle']}</p>";
        echo "</div>";
        echo "<div class='col-md-4 text-right'>";
        echo "<h5><strong>Total:</strong> $" . number_format($pedido['total_precio'], 0) . "</h5>";
        echo "<small>Enviado: " . date('H:i', strtotime($pedido['fecha_envio'])) . "</small>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    
    echo "</div>";
    echo "</div>";
}

// Procesar marcado como entregado
if (isset($_POST["pedido_id"]) && isset($_SESSION["id"])) {
    require_once "../../models/crudPedidosCategorias.php";
    
    $pedido_id = $_POST["pedido_id"];
    $usuario_id = $_SESSION["id"];
    
    $respuesta = DatosPedidosCategorias::marcarPedidoEntregadoModel($pedido_id, $usuario_id);
    
    if ($respuesta == "success") {
        echo '<script>
            alert("Pedido marcado como entregado exitosamente");
            if(window.history.replaceState) {
                window.history.replaceState(null, null, window.location.href);
            }
            window.location = "' . $_SERVER['REQUEST_URI'] . '";
        </script>';
    } else {
        echo '<script>
            alert("Error al marcar el pedido como entregado");
        </script>';
    }
}

?>

    <div class="row">
        <div class="col-md-12">
            <hr>
            <p><a href="pedidosCocinaPendientes" class="btn btn-primary">← Volver a Pedidos Cocina</a></p>
        </div>
    </div>
</div>

</body>
</html>
