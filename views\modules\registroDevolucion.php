<?php
	if(isset($_GET["action"]))
		{	if($_GET["action"] == "okPm")
			{echo "Registro Exitoso";	}
		}
?>
<script type="text/javascript">
	
	
	// ------FACTURAR--------------
		function buscarfactura() 		
		 {	//alert("Buscar factura funtion"); 
				var factura = document.devolucion.factura.value;
				if (confirm('confirme Facturar Buscar?')) 
									{		//setTimeout('location.href="views/modules/ajaxFactura.php"',500);	
												$("#destino1").load("views/modules/ajaxDevolucion.php", {factura: factura}, function(){
				        						 //alert("recibidos los datos por ajax efectivo");  		 
				     						});	
									}
		 } 
	// ------FACTURAR FIN//		".$_SESSION["mesa"]."   <div id="destino1" name="destino1" ></div>
</script>	
	<div class="row">
	    <div class="col-md-12">	  
	      	<form  method="post" >	
				<label for=""><b>factura : </b></label><input type="text" placeholder="No factura" class="mayuscula"  name="buscarfactura" id="buscarfactura" size="18"   required><br>
				<input type="submit" value="Enviar">
			</form>
	    </div>
	</div>
  	<?php
		$devolucion = new controllerdevolucion();
		$devolucion -> asignarvaribleController();
		//$devolucion -> buscarFacturaController();
		//$devolucion -> actualizarDevolucionController();
	?>