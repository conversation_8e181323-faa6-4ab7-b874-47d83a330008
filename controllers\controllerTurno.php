<?php
ob_start();
class controller<PERSON>urno extends Mvc<PERSON>ontroller
 {
	#DETALLE TURNO
	#------------------------------------
	 public function vistaVentasTController()
	 {		//echo "vista venta turno".$_GET["idDetalle"];
		$turno = $_GET["idDetalle"];
		//$datosController = $_SESSION["usuario"] ;

		$datosTurno = DatosTurno::infoDatosTurnoModel($turno);
		$cantidad_inicio=$datosTurno['cantidad_inicio'];

		setlocale(LC_MONETARY, 'en_US');
		 $formateador2 = new NumberFormatter( 'es_CO', NumberFormatter::CURRENCY );
		 $formateador2->setAttribute( $formateador2::FRACTION_DIGITS, 0 );
		 $formateador = new NumberFormatter( 'en_US', NumberFormatter::DECIMAL );

		if(isset($_GET["idDetalle"]))
		{	//echo "vista venta turno".$turno. '<br>';
				$respuesta = DatosTurno::vistaVentasTModel($turno);

				$ventasPorCategoria=DatosTurno::ventasTurnoCategoriaProductoModel($turno);

				//echo "<script>alert('Entro CONTROl   consulta 1')</script>";class="table table-bordered
				echo '<table border="1" ">
				<caption>Ventas</caption>
				<thead>
					<tr> <td><b>Código</b>
						<td><b>Nombre Producto</b></td>
						<td><b>Cantidad Vendida</b></td>
						<td><b>Precio de venta</b></td>
						<td><b>Total vendido</b></td>';
						if ($_SESSION["tipo_usuario"]==1)
						 {
							echo'<td><b>Costo Unidad</b></td>
							<td><b>Total Costo</b></td>';
						 }
					echo'</tr>
				</thead>';
	          $venta_total=0;
	          $costo_compra_total=0;
	          $utilidad=0;
	          //echo "<script>alert('antes del foreacha ".$item["pnombre"]." ')</script>";
			foreach($respuesta as $row => $item)
			{  //echo "vista venta turno".$item["pNombre"]. '<br>';
				 $costosp= DatosTurno::CostoProducto1Model($item["pid"]);
				 $costototal=$costosp["Valor_Compra_Producto"]*$item["pmCantidad"];
				 $venta_total+=$item["subTproducto"];
				 $costo_compra_total+=$costototal;
				echo'<tr>
						<td>'.$item["pcodigo"].'</td>
						<td>'.$item["pNombre"].'</td>
						<td>'.$item["pmCantidad"].'</td>
						<td>'.$item["pPrecio"].'</td>
						<td>'.$item["subTproducto"].'</td>';
						if ($_SESSION["tipo_usuario"]==1)
						 {
							echo'<td>'.round($costosp["Valor_Compra_Producto"],1).'</td>
						<td>'.round($costototal,1).'</td>';
						 }
					echo'</tr>';
			}
			$valorCierre=$venta_total;

			$utilidad=	$venta_total-$costo_compra_total;
			$utilidad=$utilidad;
			$utilidad=money_format('%(#1n', $utilidad);
			echo '<tr class="filaTotal" >
						<td colspan="2"></td>
						<td><b></b></td>
						<td><h3><b>Total</b></h3></td>
						<td><b>'.money_format('%(#1n', $venta_total).'</b></td>
						<td><b></b></td>
						<td><b>'.money_format('%(#1n', $costo_compra_total).'</b></td>
					</tr>
					<tr class="filaTotal1" >
						<td colspan=2></td>
						<td><b></b></td>
						<td><h3><b>Utilidad</b></h3></td>
						<td><b>'.$utilidad.'</b></td>
						<td><b></b></td>
						<td><b></b></td>
					</tr>
				</table>'	;



		   //---------------------- Star Ventas Por Categoria de Producto ------------------------------
			// Calcular totales generales
			$totalVentas = 0;
			$totalValor = 0;
			foreach ($ventasPorCategoria as $categoria) {
			    $totalVentas += $categoria['total_ventas'];
			    $totalValor += $categoria['valor_dinero_categoria'];
			}

			if (!empty($ventasPorCategoria)): 

		      echo  '<table class="tabla-ventas">
		            <thead>
		                <tr>
		                    <th>Categoría</th>
		                    <th class="texto-centro">Total Ventas</th>
		                    <th class="texto-derecha">Valor en Dinero</th>
		                    <th class="texto-centro">Porcentaje del Total</th>
		                </tr>
		            </thead>
		            <tbody>.';
		                foreach ($ventasPorCategoria as $categoria): 
		                    echo '<tr class="categoria-'.strtolower($categoria['categoria']).' ">
			                        <td>
			                            <strong>'.ucfirst($categoria['categoria']).'</strong>
			                        </td>
			                        <td class="texto-centro">
			                            '.$categoria['total_ventas'].'
			                        </td>
			                        <td class="texto-derecha">
			                            '.$formateador2->format($categoria['valor_dinero_categoria']).'
			                        </td>
			                        <td class="texto-centro">
			                            '.$categoria['porcentaje_del_total'].'%
			                        </td>
			                    </tr>';
		                endforeach; 
		                
		                echo '<!-- Fila de totales -->
		                <tr class="total-row">
		                    <td><strong>TOTAL GENERAL</strong></td>
		                    <td class="texto-centro">
		                        <strong>'.$totalVentas.'</strong>
		                    </td>
		                    <td class="texto-derecha">
		                        <strong>'.$formateador2->format($totalValor).'</strong>
		                    </td>
		                    <td class="texto-centro">
		                        <strong>100.00%</strong>
		                    </td>
		                </tr>
		            </tbody>
		        </table>
        
		        <!-- Resumen adicional -->
		        <div style="margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
		            <h3>Resumen del Turno '.$turno.'</h3>
		            <p><strong>Número de categorías con ventas:</strong> '.count($ventasPorCategoria).'</p>
		            <p><strong>Total de ventas realizadas:</strong> '.$totalVentas.'</p>
		            <p><strong>Valor total generado:</strong> '.$formateador2->format($totalValor).'</p>
		            <!--<p><strong>Promedio por venta:</strong> '.$formateador2->format($totalVentas > 0 ? $totalValor / $totalVentas : 0).'</p>-->
		        </div>';
		        
		      else:
		        echo '<div class="sin-datos">
			            <p>No se encontraron ventas para el turno <?php echo $turno; ?></p>
	        		</div>';
              endif;
		   //----------------------- End Ventas Por Categoria de Producto ------------------------------
		}
		/* Abonos hechos en el turno*/
				$respuestaA = DatosTurno::consultaAbonoModel($turno);
				$abonos = 0;
				if ($respuestaA > 0)
				 {	echo'<table>
				<caption>Abonos</caption>
						<tr>
							<td>Cliente </td>
							<td>Valor de Abono </td><br>
						</tr>';
					foreach ($respuestaA as $rows1 => $ite1)
					 {
						$abonos = $abonos + $ite1["avalor"];
						echo'
						<tr>
							<td>'.$ite1["pernombre"].'</td>
							<td>'.$ite1["avalor"].' </td><br>
						</tr>';
					 }
					echo'<tr>
							<td>Total</td>
							<td>'.$abonos.' </td><br>
						</tr>
						</table>';
				 }//Secho "<script>alert(' if abono".$abonos." ')</script>";
				/* Ingresos hechos en el turno*/
				$respuesta2 = DatosTurno::consultaIngresoModel($turno);
				$ingresos = 0;
				if ($respuesta2 > 0)
				 {// echo "<script>alert(' if Ingreso".$_SESSION["turno"]." ')</script>";
					echo'<table>
						<caption>Ingreso</caption>
						<tr>
							<td>Motivo </td>
							<td>Valor  </td><br>
						</tr>';
					foreach ($respuesta2 as $rows => $ite)
					 {
						$ingresos = $ingresos + $ite["cantidad"];
						echo'
						<tr>
							<td>'.$ite["motivo"].'</td>
							<td>'.$ite["cantidad"].' </td><br>
						</tr>';
					 }
						echo'<tr>
							<td>Total</td>
							<td>'.$ingresos.' </td><br>
						</tr>
						</table>';
				 }
				/* Egresos hechos en el turno*/
				$respuesta3 = DatosTurno::consultaEgresoModel($turno);
				$Egresos = 0;
				if ($respuesta3 > 0)
				 {// echo "<script>alert(' if Egreso".$respuesta3["avalor"]." ')</script>";
					echo'<table>
					<caption>Egreso</caption>
						<tr>
							<td>Motivo</td>
							<td>Valor</td><br>
						</tr>';


					foreach ($respuesta3 as $rows => $item)
					 {
						$Egresos = $Egresos + $item["cantidad"];
						echo'
						<tr>
							<td>'.$ite1["motivo"].'</td>
							<td>'.$ite1["cantidad"].' </td><br>
						</tr>';
					 }
					 echo'<tr>
							<td>Total</td>
							<td>'.$Egresos.' </td><br>
						</tr>
						</table>';
				 }
			$respuestaP = DatosTurno::consultaPropinaModel($turno);
			$propina = 0;

			if ($respuestaP > 0)
			 {// echo "<script>alert(' if Egreso".$respuesta3["avalor"]." ')</script>";
				echo'<table border="1">
				<caption>Propinas</caption>
					<thead>
					 <tr>
						<td>Factura</td>
						<td>Mesero</td>
						<td>Propina</td>
					</thead></tr>';
				foreach ($respuestaP as $rows => $iteP)
				 {
					$propina = $propina + $iteP["vpropina"];
					echo'
					<tr>
						<td>'.$iteP["vid"].'</td>
						<td>'.$iteP["pernombre"].'</td>
						<td align="right">'.money_format('%(#1n', $iteP["vpropina"]).' </td>
					</tr>';
				 }
				 echo'<tr class="filaTotal" >
						<td colspan="2">Total</td>
						<td align="right">'.money_format('%(#1n', $propina).' </td>
					</tr>
					<thead>
						<tr><td colspan="3"></td></tr>
					</thead>
					</table> <br>';
			 }

			 //-------------------- Start tipos de pago En el turnno -------------------------------------
			 $respuestaPagos = DatosTurno::consultaPagosConTarjetaTurnoModel($turno, 'Pagos');

			 if ($respuestaPagos != 0)
			 { 
			 	//$efectivo=$respuestaPagos['efectivo']; 
			 	$nequi=$respuestaPagos['nequi']; 
			 	$daviplata=$respuestaPagos['daviplata']; 
			 	$datafono=$respuestaPagos['datafono'];
			 	$bancolombia=$respuestaPagos['bancolombia'];  
			 }

			 //-------------------- End tipos de pago En el turnno -------------------------------------

				/* Valor total del cierre*/
				$ValorInicio = $cantidad_inicio;
				$valorCierreT = $valorCierre + $ValorInicio + $abonos + $ingresos - $Egresos - $nequi - $daviplata - $datafono - $bancolombia;
				$valorCierreT = $valorCierreT - $propina;

			echo'<table>
					<thead>
						<tr><td colspan="2" > Resumen del Cierre</td></tr>
					</thead>
					<tr>
						<td>Inicio Turno </td>
						<td>'.$_SESSION["inicio"].' </td>
					</tr>
					<tr>
					</tr>
					<tr>
						<td>Valor Inicial</td>
						<td align="right">'.$formateador2->format($ValorInicio).'</td>
					</tr>
					<tr>
						<td> Valor Ventas</td>
						<td align="right">'.$formateador2->format($valorCierre).' </td>
					</tr>
					<tr>
						<td> Valor Nequi</td>
						<td align="right" style="color: red;">-'.$formateador2->format($nequi).' </td>
					</tr>
					<tr>
						<td> Valor Daviplata</td>
						<td align="right" style="color: red;">-'.$formateador2->format($daviplata).' </td>
					</tr>
					<tr>
						<td> Valor Datafono</td>
						<td align="right" style="color: red;">-'.$formateador2->format($datafono).' </td>
					</tr>
					<tr>
						<td> Valor Bancolombia</td>
						<td align="right" style="color: red;">-'.$formateador2->format($bancolombia).' </td>
					</tr>
					<tr>
						<td> Valor Abonos</td>
						<td align="right">'.$formateador2->format($abonos).' </td>
					</tr>
					<tr>
						<td> Valor Ingresos </td>
						<td align="right">'.$formateador2->format($ingresos).'</td>
					</tr>
					<tr>
						<td>Valor Egresos</td>
						<td align="right">-'.$formateador2->format($Egresos).'</td>
					</tr>
					<tr class="filaTotal" >
						<td> Valor Total en Efectivo</td>
						<td align="right"><input type="hidden"  readonly value="'.$valorCierreT.'" name="ValorFinal" required>'.$formateador2->format($valorCierreT).' </td>
					</tr>
					<thead>
						<tr><td colspan="2" ></td></tr>
					</thead>
				</table><br>';	

	 }
	#-------------------------------------
	#BUSCAR TURNO
	#------------------------------------
	 public function buscarTurnoController()
	 {	//session_start();	//echo "<script>alert('Entro CONTROl   Turno')</script>"; persona_id
		$turnoBuscar = DatosTurno::buscarTurno("turnos_cajeros");
		if ($turnoBuscar >0)
		 {
			foreach ($turnoBuscar as $key => $value)
			 {
			 	//echo "<script>alert('Entro    Turno')</script>";
			 	if ($value["persona_id"] == $_SESSION["usuario"])
			 	 {  $_SESSION['turno'] = $value["id"];
			 	 	echo' <script>alert("Tienes un Turno activa");location.href ="mesa";</script>'; }
			 	else $activo=1;
			 }
			 	if ($activo==1)
			 	  {	//echo' <script>alert("Inicie turno ");</script>';
			 		if(isset($_POST["cantidad_inicioTurnoRegistro"]))
					 {	echo' <script>alert("Iniciar turno ");</script>';
						$datosController =array('persona_id'=>$_SESSION['usuario'] ,
												'fecha_final'=>0,
												'cantidad_inicio'=>$_POST["cantidad_inicioTurnoRegistro"],
												'cantidad_final'=>0);
						//echo "<script>alert('Entro Controller ".$datosController['cantidad_finalTurnoRegistro']." no');</script>";
						$respuesta = DatosTurno::registroTurnoModel($datosController, "turnos_cajeros");
						if($respuesta == "success")
							{	header("location:index.php?action=mesa");	}
						else
							{	echo "<script>alert('Inicie el Turno para seguir')</script>";	}
					 }
			 	}
		 }
		else
		 {
			echo' <script>alert("Inicie turno ");</script>';
			if(isset($_POST["cantidad_inicioTurnoRegistro"]))
				 {	echo' <script>alert("Iniciar turno ");</script>';
					$datosController =array('persona_id'=>$_SESSION['usuario'] ,
											'fecha_final'=>0,
											'cantidad_inicio'=>$_POST["cantidad_inicioTurnoRegistro"],
											'cantidad_final'=>0);
					//echo "<script>alert('Entro Controller ".$datosController['cantidad_finalTurnoRegistro']." no');</script>";
					$respuesta = DatosTurno::registroTurnoModel($datosController, "turnos_cajeros");
					if($respuesta == "success")
						{	header("location:index.php?action=mesa");	}
					else
						{	echo "<script>alert('Inicie el Turno para seguir')</script>";	}
				 }
		 }

	 }
	#--------------------------------------------------------
	#REGISTRO DE TURNO
	#------------------------------------
		public function registroTurnoController()
		 {	session_start();
			//echo "<script>alert('Entro CONTROl   Turno')</script>"; persona_id
			if(isset($_POST["cantidad_inicioTurnoRegistro"]))
			 {	echo' <script>alert("Iniciar turno ");</script>';
				$datosController =array('persona_id'=>$_SESSION['usuario'] ,
										'fecha_final'=>0,
										'cantidad_inicio'=>$_POST["cantidad_inicioTurnoRegistro"],
										'cantidad_final'=>0);
				//echo "<script>alert('Entro Controller ".$datosController['cantidad_finalTurnoRegistro']." no');</script>";
				$respuesta = DatosTurno::registroTurnoModel($datosController, "turnos_cajeros");
				if($respuesta == "success")
					{	header("location:index.php?action=okT");	}
				else
					{	echo "<script>alert('Inicie el Turno para seguir')</script>";	}
			 }
		 }
	#--------------------------------------------------------
	#VISTA DE TURNO  tid  tpersona_id tfecha_inicio   tfecha_final  tcantidad_inicio  tcantidad_final  pnombre  papellidos
	#<a href="index.php?action=editarTurno&id='.$item["tid"].'">Cerrar</a>|
	#<a href="index.php?action=turno&idBorrar='.$item["tid"].'">Borrar</a>
	#------------------------------------
	 public function vistaTurnoController()
		{
		 $respuesta = DatosTurno::vistaTurnoModel("turnos_cajeros");
		 foreach($respuesta as $row => $item)
			{
			 if ($item["tpersona_id"]==$_SESSION["usuario"] or $_SESSION["tipo_usuario"]==1)
			 	{
				 echo'<tr>
					<td>'.$item["tid"].'</td>
					<td>'.$item["pnombre"].'&nbsp; '.$item["papellidos"].'</td>
					<td>'.$item["tfecha_inicio"].'</td>
					<td>'.$item["tfecha_final"].'</td>
					<td>'.$item["tcantidad_inicio"].'</td>
					<td>'.$item["tcantidad_final"].'</td>
					<td><a href="index.php?action=turnoVentas&idDetalle='.$item["tid"].'">Detalle</a>
					</td>
				 </tr>';
				}
			 }
		}
	#-------------------------------------------------

	#EDITAR TURNO Ver Cerrar turno
	#------------------------------------
		public function editarTurnoController()
			{	echo "<script>alert(' usu ".$datosModel." ')</script>";
				session_start();
				$datosController = $_SESSION['usuario'] ;
				$respuesta = DatosTurno::editarTurnoModel($datosController, "turnos_cajeros");
				echo' <input type="hidden" value="'.$respuesta["persona_id"].'" name="persona_idTurnoEditar" required>

				 fecha inicial <input type="text" readonly value="'.$respuesta["fecha_inicio"].'" name="fecha_inicioTurnoEditar" required>
				 fecha final <input type="text" value="'.$respuesta["fecha_final"].'" name="fecha_finalTurnoEditar" required>

				Valor Caja inicio <input type="text" readonly value="'.$respuesta["cantidad_inicio"].'" name="cantidad_inicioTurnoEditar" required>
				 Valor Caja final <input type="text" value="'.$respuesta["cantidad_final"].'" name="cantidad_finalTurnoEditar" required>
				 <input type="hidden" value="'.$respuesta["id"].'" name="idTurnoEditar" required>
					 <input type="submit" value="Actualizar">';
			}
	#----------------------------------------
	#ACTUALIZAR TURNO  Guardar Cerrar turno
	#------------------------------------
		public function actualizarTurnoController()
			{	echo "<script>alert('Entro Controller Actualizar Producto')</script>";

				if(isset($_POST["persona_idTurnoEditar"]))
					{
						$datosController = array(  "persona_id"=>$_POST["persona_idTurnoEditar"],
													"fecha_inicio"=>$_POST["fecha_inicioTurnoEditar"],
													"fecha_final"=>$_POST["fecha_finalTurnoEditar"],

													"cantidad_inicio"=>$_POST["cantidad_inicioTurnoEditar"],
													"cantidad_final"=>$_POST["cantidad_finalTurnoEditar"],
													"id"=>$_POST["idTurnoEditar"]);
						$respuesta = DatosTurno::actualizarTurnoModel($datosController, "turnos_cajeros");
						if($respuesta == "success")
							{	header("location:index.php?action=cambioT");	}
						else
							{	echo "error";	}
					}
			}
	#----------------------------------------
	#BORRAR TURNO
	#------------------------------------
		public function borrarTurnoController()
			{
				if(isset($_GET["idBorrar"]))
					{
						$datosController = $_GET["idBorrar"];
						$respuesta = DatosTurno::borrarTurnoModel($datosController, "turnos_cajeros");
						if($respuesta == "success")
							{	header("location:index.php?action=turno");	}
					}
			}
	#---------------------------------------
	#CONSULTAR TURNO Ver Cerrar turno tid  tpersona_id tfecha_inicio   tfecha_final  tcantidad_inicio  tcantidad_final  pnombre
	#------------------------------------
	 public function consultarTurnoController()
		{//session_start();
		 //$datosController = $_SESSION["turno"] ;
		 $datosController = $_SESSION["usuario"] ;

		 setlocale(LC_MONETARY, 'en_US');
		 $formateador2 = new NumberFormatter( 'es_CO', NumberFormatter::CURRENCY );
		 $formateador2->setAttribute( $formateador2::FRACTION_DIGITS, 0 );
		 $formateador = new NumberFormatter( 'en_US', NumberFormatter::DECIMAL );

		 $respuesta = DatosTurno::consultarTurnoModel($datosController);
		 $valorCierre =0;//Venta q tubo el turno
		 $ValorInicio =0;// Inicio de turno
		 $valorCierreT =0; //valor total del turno
		 if ($respuesta > 0)
			{	$i=0;
			 	echo'<table>
					<caption>Ventas</caption>
					<thead>
					<tr>
						<td>Factura </td>
						<td>Cliente </td>
						<td>Valor </td>
					</tr>
					</thead>';

			 	foreach ($respuesta as $rows => $item)
				 {	echo'

					<tr>
						<td align="center">'.$item["vid"].'</td>
						<td align="center">'.$item["tid"].'</td>
						<td align="right">'.$formateador2->format($item["vtotal"]).' </td>
					</tr>';
					if ($valorCierre ==0)
					 {
					 	$ValorInicio = $item["tcantidai"];
					 	$_SESSION["inicio"] = $item["tinicio"];
					 	$_SESSION["turno"] = $item["tid"];
					 	$persona_id = $item["tpersona"];
					 }
					$valorCierre = $valorCierre + $item["vtotal"] ;
					$reporteTurno[$i] =array('vfecha' =>$item["vfecha"],
										  'vid' =>$item["vid"],
										  'vtotal' =>$item["vtotal"] );
					$i++;
				 }
				echo'<tr class="filaTotal" >
						<td colspan="2">Total</td>
						<td>'.money_format('%(#1n', $valorCierre).' </td>
					</tr>
					<thead>
						<tr><td colspan="3" ></td></tr>
					</thead>
					</table>';
				 $_SESSION["turnoV"] = $reporteTurno;
			}
		 else
			{
			 $respuesta1= DatosTurno::sinVentaTurnoModel($datosController);
			 //echo "<script>alert('Cntidad final controles".$respuesta1["cantidad_final"]." ')</script>";
			 if ($respuesta1["cantidad_final"] == 0)
				{ /* sin Ventas o factura*/
				 	// id  persona_id  fecha_inicio   fecha_final   cantidad_inicio   cantidad_final
				 	$_SESSION["inicio"] = $respuesta1["fecha_inicio"];
					$_SESSION["turno"] = $respuesta1["id"];
					$ValorInicio = $respuesta1["cantidad_inicio"];
					// echo "<script>alert('fecha inicio ".$_SESSION["inicio"]." turno ".$_SESSION["turno"]." ')</script>";ho
					echo '<h3> <b> Sin venta</b> </h3>';
				}
			}

			$ventasPorCategoria=DatosTurno::ventasTurnoCategoriaProductoModel($_SESSION["turno"]);
			echo '<h3> <b> Ventas por categoria</b> </h3>';
			//---------------------- Star Ventas Por Categoria de Producto ------------------------------
			// Calcular totales generales
			$totalVentas = 0;
			$totalValor = 0;
			foreach ($ventasPorCategoria as $categoria) {
			    $totalVentas += $categoria['total_ventas'];
			    $totalValor += $categoria['valor_dinero_categoria'];
			}

			if (!empty($ventasPorCategoria)): 

		      echo  '<table class="tabla-ventas">
		            <thead>
		                <tr>
		                    <th>Categoría</th>
		                    <th class="texto-centro">Total Ventas</th>
		                    <th class="texto-derecha">Valor en Dinero</th>
		                    <th class="texto-centro">Porcentaje del Total</th>
		                </tr>
		            </thead>
		            <tbody>.';
		                foreach ($ventasPorCategoria as $categoria): 
		                    echo '<tr class="categoria-'.strtolower($categoria['categoria']).' ">
			                        <td>
			                            <strong>'.ucfirst($categoria['categoria']).'</strong>
			                        </td>
			                        <td class="texto-centro">
			                            '.$categoria['total_ventas'].'
			                        </td>
			                        <td class="texto-derecha">
			                            '.$formateador2->format($categoria['valor_dinero_categoria']).'
			                        </td>
			                        <td class="texto-centro">
			                            '.$categoria['porcentaje_del_total'].'%
			                        </td>
			                    </tr>';
		                endforeach; 
		                
		                echo '<!-- Fila de totales -->
		                <tr class="total-row">
		                    <td><strong>TOTAL GENERAL</strong></td>
		                    <td class="texto-centro">
		                        <strong>'.$totalVentas.'</strong>
		                    </td>
		                    <td class="texto-derecha">
		                        <strong>'.$formateador2->format($totalValor).'</strong>
		                    </td>
		                    <td class="texto-centro">
		                        <strong>100.00%</strong>
		                    </td>
		                </tr>
		            </tbody>
		        </table>
        
		        <!-- Resumen adicional -->
		        <div style="margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
		            <h3>Resumen del Turno '.$turno.'</h3>
		            <p><strong>Número de categorías con ventas:</strong> '.count($ventasPorCategoria).'</p>
		            <p><strong>Total de ventas realizadas:</strong> '.$totalVentas.'</p>
		            <p><strong>Valor total generado:</strong> '.$formateador2->format($totalValor).'</p>
		            <!--<p><strong>Promedio por venta:</strong> '.$formateador2->format($totalVentas > 0 ? $totalValor / $totalVentas : 0).'</p>-->
		        </div>';
		        
		      else:
		        echo '<div class="sin-datos">
			            <p>No se encontraron ventas para el turno <?php echo $turno; ?></p>
	        		</div>';
              endif;
		   //----------------------- End Ventas Por Categoria de Producto ------------------------------

			/* Abonos hechos en el turno*/
		 $respuestaA = DatosTurno::consultaAbonoModel($_SESSION["turno"]);
		 $abonos = 0;
		 if ($respuestaA > 0)
			{
			 echo'<table border="1" class="tabla" align="center">
					<thead>
						<tr > <td colspan="3" >Abonos recibido</td> </tr>

					<tr>
						<td>Cliente </td>
						<td>Valor de Abono </td>
					</tr>
					</thead>';
			 foreach ($respuestaA as $rows1 => $ite1)
				{
					$abonos = $abonos + $ite1["avalor"];
					echo'
					<tr>
						<td>'.$ite1["pernombre"].'</td>
						<td align="right">'.$formateador2->format($ite1["avalor"]).' </td>
					</tr>';
				}
				echo'<tr>
						<td>Total</td>
						<td align="right">'.$formateador2->format($abonos).' </td>
					</tr>
					<thead>
						<tr > <td colspan="3" ></td> </tr>
					<tr>
					</<thead>
						<tr></tr>
					</thead>
					</table>';
			}
			//Secho "<script>alert(' if abono".$abonos." ')</script>";
			/* Ingresos hechos en el turno*/
			$respuesta2 = DatosTurno::consultaIngresoModel($_SESSION["turno"]);
			$ingresos = 0;
			if ($respuesta2 > 0)
			 {// echo "<script>alert(' if Ingreso".$_SESSION["turno"]." ')</script>";
				echo'<table border="1">
				<caption>Ingreso</caption>
					<thead><tr>
						<td>Motivo </td>
						<td>Valor  </td>
					</thead></tr>';
				foreach ($respuesta2 as $rows => $ite)
				 {
					$ingresos = $ingresos + $ite["cantidad"];
					echo'
					<tr>
						<td>'.$ite["motivo"].'</td>
						<td align="right">'.$formateador2->format($ite["cantidad"]).' </td>
					</tr>';
				 }
					echo'<tr>
						<td>Total</td>
						<td align="right">'.$formateador2->format($ingresos).' </td>
					</tr>
					<thead> <tr> <td colspan="3"></td> </tr>
					</thead>
					</table>';
			 }
			/* Egresos hechos en el turno*/
			$respuesta3 = DatosTurno::consultaEgresoModel($_SESSION["turno"]);
			$Egresos = 0;

			if ($respuesta3 > 0)
			 {// echo "<script>alert(' if Egreso".$respuesta3["avalor"]." ')</script>";
				echo'<table border="1">
				<caption>Egreso</caption>
					<thead>
					 <tr>
						<td>Motivo</td>
						<td>Valor</td>
					</thead></tr>';
				foreach ($respuesta3 as $rows => $ite1)
				 {
					$Egresos = $Egresos + $ite1["cantidad"];
					echo'
					<tr>
						<td>'.$ite1["motivo"].'</td>
						<td align="right">'.$formateador2->format($ite1["cantidad"]).' </td>
					</tr>';
				 }
				 echo'<tr>
						<td>Total</td>
						<td align="right">'.$formateador2->format($Egresos).' </td>
					</tr>
					<thead>
						<tr><td colspan="3"></td></tr>
					</thead>
					</table> <br>';
			 }
			/* propina hechos en el turno*/
			$respuestaP = DatosTurno::consultaPropinaModel($_SESSION["turno"]);
			$propina = 0;

			if ($respuestaP > 0)
			 {// echo "<script>alert(' if Egreso".$respuesta3["avalor"]." ')</script>";
				echo'<table border="1">
				<caption>Propinas</caption>
					<thead>
					 <tr>
						<td>Factura</td>
						<td>Mesero</td>
						<td>Propina</td>
					</thead></tr>';
				foreach ($respuestaP as $rows => $iteP)
				 {
					$propina = $propina + $iteP["vpropina"];
					echo'
					<tr>
						<td>'.$iteP["vid"].'</td>
						<td>'.$iteP["pernombre"].'</td>
						<td align="right">'.$formateador2->format($iteP["vpropina"]).' </td>
					</tr>';
				 }
				 echo'<tr class="filaTotal" >
						<td colspan="2">Total</td>
						<td align="right">'.$formateador2->format($propina).' </td>
					</tr>
					<thead>
						<tr><td colspan="3"></td></tr>
					</thead>
					</table> <br>';
			 }


			 //-------------------- Start tipos de pago En el turnno -------------------------------------
			 $turno=$_SESSION["turno"];
			 $respuestaPagos = DatosTurno::consultaPagosConTarjetaTurnoModel($turno, 'Pagos');

			 if ($respuestaPagos != 0)
			 { 
			 	//$efectivo=$respuestaPagos['efectivo']; 
			 	$nequi=$respuestaPagos['nequi']; 
			 	$daviplata=$respuestaPagos['daviplata']; 
			 	$datafono=$respuestaPagos['datafono'];
			 	$bancolombia=$respuestaPagos['bancolombia'];  
			 }

			 //-------------------- End tipos de pago En el turnno -------------------------------------

				/* Valor total del cierre*/
				//$valorCierreT = $valorCierre + $ValorInicio + $abonos + $ingresos - $Egresos - $nequi - $daviplata - $datafono - $bancolombia;
				//$valorCierreT = $valorCierreT - $propina;

			/* Valor total del cierre*/
			$valorCierreT = $valorCierre + $ValorInicio + $abonos + $ingresos - $Egresos  - $nequi - $daviplata - $datafono - $bancolombia;

			$_SESSION["ingreso"]=$ingresos;
			$_SESSION["Egreso"]=$Egresos;
			$_SESSION["propina"]=$propina;
			$_SESSION["ventas"]=$valorCierre;
			$_SESSION["nequi"]=$nequi;
			$_SESSION["daviplata"]=$daviplata;
			$_SESSION["datafono"]=$datafono;
			$_SESSION["bancolombia"]=$bancolombia;

			//$valorCierreT = $valorCierre + $ValorInicio ;+ $abonos
			echo' <input type="hidden" value="'.$persona_id.'" name="persona_idTurnoEditar" required><br>
				<table>
					<thead>
						<tr><td colspan="2" > Resumen del Cierre</td></tr>
					</thead>
					<tr>
						<td>Inicio Turno </td>
						<td>'.$_SESSION["inicio"].' <input type="hidden" align="right" readonly value="'.$_SESSION["turno"] .'" name="idTurnoEditar" required> </td>
					</tr>
					<tr>
					</tr>
					<tr>
						<td>Valor Inicial</td>
						<td align="right"><input type="hidden"  readonly value="'.$ValorInicio.'" name="valorInicial" required>'.$formateador2->format($ValorInicio).'</td>
					</tr>
					<tr>
						<td> Valor Ventas</td>
						<td align="right"><input type="hidden"  readonly value="'.$valorCierre.'" name="ventas" required>'.$formateador2->format($valorCierre).' </td>
					</tr>
					<tr>
						<td> Valor Nequi</td>
						<td align="right" style="color: red;">-'.$formateador2->format($nequi).' </td>
					</tr>
					<tr>
						<td> Valor Daviplata</td>
						<td align="right" style="color: red;">-'.$formateador2->format($daviplata).' </td>
					</tr>
					<tr>
						<td> Valor Datafono</td>
						<td align="right" style="color: red;">-'.$formateador2->format($datafono).' </td>
					</tr>
					<tr>
						<td> Valor Bancolombia</td>
						<td align="right" style="color: red;">-'.$formateador2->format($bancolombia).' </td>
					</tr>
					<tr>
						<td> Valor Abonos</td>
						<td align="right"><input type="hidden" align="right" readonly value="'.$abonos.'" name="abonos" required>'.$formateador2->format($abonos).' </td>
					</tr>
					<tr>
						<td> Valor Ingresos </td>
						<td align="right"><input type="hidden" readonly value="'.$ingresos.'" name="ingresos" required>'.$formateador2->format($ingresos).'</td>
					</tr>
					<tr>
						<td>Valor Egresos</td>
						<td align="right"><input type="hidden" readonly value="'.$Egresos.'" name="egresos" required>-'.$formateador2->format($Egresos).'</td>
					</tr>
					<tr class="filaTotal" >
						<td> Valor Total</td>
						<td align="right"><input type="hidden"  readonly value="'.$valorCierreT.'" name="ValorFinal" required>'.$formateador2->format($valorCierreT).' </td>
					</tr>
					<thead>
						<tr><td colspan="2" ></td></tr>
					</thead>
				</table>


			 <br>
				 <input type="submit" value="Cerrar">';
				 /*
				  Valor Abonos <input type="text" readonly value="'.$abonos.'" name="abonos" required> <br>

				  */

		}
	#----------------------------------------
	#CERRAR TURNO  Guardar Cerrar turno
	#------------------------------------
	 public function cerrarTurnoController()
		{	//echo "<script>alert('Entro Controller Actualizar Producto')</script>";
			if(isset($_POST["persona_idTurnoEditar"]))
				{
					$datosController = array( 	"cantidad_final"=>$_POST["ValorFinal"],
												"id"=>$_POST["idTurnoEditar"]);
					$respuesta = DatosTurno::cerrarTurnoModel($datosController, "turnos_cajeros");
					//echo "<script>alert('Entro Controller Actualizar Producto')</script>";
					if($respuesta == "success")
						{	// Primero mostrar el JavaScript, luego cerrar sesión y redirigir
							echo' <script>
								alert("Turno cerrado exitosamente");
								window.open("pdfTurno","_blank");
								setTimeout(function(){
									// Cerrar sesión y redirigir al login
									window.location.href = "index.php?action=salir";
								}, 3000);
							</script>';

							// También cerrar sesión del lado del servidor después de un delay
							// para dar tiempo a que se genere el PDF
							session_write_close();
						}
					else
						{	echo "error";	}
				}
		}
	#----------------------------------------
	#REPORTE CIERRE    pNombre           pid       pmCantidad         pPrecio      subTproducto
	#------------------------------
		public function reporteTurnoController()
		{//echo "<script>alert('fecha ".$_SESSION["inicio"]." turno ".$_SESSION["final"]." ')</script>";
			$respuesta = DatosTurno::reporteTurno('2018-03-11 00:00:01', '2018-03-11 23:59:59');
			//$respuesta = DatosTurno::reporteTurno($_SESSION["inicio"], $_SESSION["final"]);
			foreach($respuesta as $row => $item)
			{
				echo'<tr>
							<td>'.$item["pid"].'</td>
							<td>'.$item["pNombre"].'</td>
							<td>'.$item["pmCantidad"].'</td>
							<td>'.$item["pPrecio"].'</td>
							<td>'.$item["subTproducto"].'</td>
						</tr>';
			}

		}
	#----------------------------------
	}