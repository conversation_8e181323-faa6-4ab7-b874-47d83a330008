<?php

require_once "conexion.php";

class DatosDiagnostico {
    
    /*=============================================
    VERIFICAR CONEXION A BASE DE DATOS
    =============================================*/
    static public function verificarConexionModel() {
        try {
            $conexion = Conexion::conectar();
            if ($conexion) {
                return array(
                    "status" => "success",
                    "message" => "Conexión exitosa a la base de datos",
                    "database" => $conexion->query("SELECT DATABASE()")->fetchColumn()
                );
            }
        } catch (Exception $e) {
            return array(
                "status" => "error",
                "message" => "Error de conexión: " . $e->getMessage()
            );
        }
        
        return array(
            "status" => "error",
            "message" => "No se pudo establecer conexión"
        );
    }
    
    /*=============================================
    VERIFICAR ESTRUCTURA DE TABLA PEDIDOS
    =============================================*/
    static public function verificarEstructuraPedidosModel() {
        try {
            $stmt = Conexion::conectar()->prepare("DESCRIBE pedidos");
            $stmt->execute();
            $columnas = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $columnasRequeridas = [
                'id', 'mesero_id', 'mesa_id', 'fecha_pedido', 'facturado', 
                'estado', 'fecha_envio', 'fecha_entrega', 'usuario_envio', 
                'usuario_entrega', 'numero_pedido', 'cedula_cliente'
            ];
            
            $columnasExistentes = array_column($columnas, 'Field');
            $columnasFaltantes = array_diff($columnasRequeridas, $columnasExistentes);
            
            if (empty($columnasFaltantes)) {
                return array(
                    "status" => "success",
                    "message" => "Estructura de tabla pedidos correcta",
                    "columnas" => $columnasExistentes
                );
            } else {
                return array(
                    "status" => "error",
                    "message" => "Faltan columnas en tabla pedidos",
                    "columnas_faltantes" => $columnasFaltantes,
                    "columnas_existentes" => $columnasExistentes
                );
            }
            
        } catch (Exception $e) {
            return array(
                "status" => "error",
                "message" => "Error al verificar estructura: " . $e->getMessage()
            );
        }
    }
    
    /*=============================================
    VERIFICAR TABLA IMPRESORAS
    =============================================*/
    static public function verificarTablaImpresorasModel() {
        try {
            // Verificar si la tabla existe
            $stmt = Conexion::conectar()->prepare("SHOW TABLES LIKE 'impresoras'");
            $stmt->execute();
            $tablaExiste = $stmt->fetch();
            
            if (!$tablaExiste) {
                return array(
                    "status" => "error",
                    "message" => "Tabla impresoras no existe"
                );
            }
            
            // Verificar estructura
            $stmt = Conexion::conectar()->prepare("DESCRIBE impresoras");
            $stmt->execute();
            $columnas = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Verificar datos
            $stmt = Conexion::conectar()->prepare("SELECT COUNT(*) as total FROM impresoras");
            $stmt->execute();
            $total = $stmt->fetch()['total'];
            
            return array(
                "status" => "success",
                "message" => "Tabla impresoras configurada correctamente",
                "columnas" => array_column($columnas, 'Field'),
                "total_impresoras" => $total
            );
            
        } catch (Exception $e) {
            return array(
                "status" => "error",
                "message" => "Error al verificar tabla impresoras: " . $e->getMessage()
            );
        }
    }
    
    /*=============================================
    VERIFICAR TABLAS DE HISTORIAL
    =============================================*/
    static public function verificarTablasHistorialModel() {
        try {
            $tablas = ['pedidos_historial', 'pedidos_reimpresiones'];
            $resultados = array();
            
            foreach ($tablas as $tabla) {
                $stmt = Conexion::conectar()->prepare("SHOW TABLES LIKE ?");
                $stmt->execute([$tabla]);
                $existe = $stmt->fetch();
                
                if ($existe) {
                    $stmt = Conexion::conectar()->prepare("SELECT COUNT(*) as total FROM $tabla");
                    $stmt->execute();
                    $total = $stmt->fetch()['total'];
                    
                    $resultados[$tabla] = array(
                        "status" => "success",
                        "message" => "Tabla $tabla existe",
                        "registros" => $total
                    );
                } else {
                    $resultados[$tabla] = array(
                        "status" => "error",
                        "message" => "Tabla $tabla no existe"
                    );
                }
            }
            
            return $resultados;
            
        } catch (Exception $e) {
            return array(
                "status" => "error",
                "message" => "Error al verificar tablas de historial: " . $e->getMessage()
            );
        }
    }
    
    /*=============================================
    VERIFICAR TRIGGERS
    =============================================*/
    static public function verificarTriggersModel() {
        try {
            $stmt = Conexion::conectar()->prepare("
                SELECT TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE 
                FROM information_schema.TRIGGERS 
                WHERE TRIGGER_SCHEMA = DATABASE()
                AND EVENT_OBJECT_TABLE IN ('pedidos', 'pedido_productos_mesa', 'producto_vendido_mesa', 'ventas')
            ");
            $stmt->execute();
            $triggers = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $triggersRequeridos = [
                'generar_numero_pedido',
                'pedidos_historial_insert'
            ];
            
            $triggersExistentes = array_column($triggers, 'TRIGGER_NAME');
            $triggersFaltantes = array_diff($triggersRequeridos, $triggersExistentes);
            
            if (empty($triggersFaltantes)) {
                return array(
                    "status" => "success",
                    "message" => "Triggers configurados correctamente",
                    "triggers" => $triggersExistentes
                );
            } else {
                return array(
                    "status" => "warning",
                    "message" => "Algunos triggers faltan",
                    "triggers_existentes" => $triggersExistentes,
                    "triggers_faltantes" => $triggersFaltantes
                );
            }
            
        } catch (Exception $e) {
            return array(
                "status" => "error",
                "message" => "Error al verificar triggers: " . $e->getMessage()
            );
        }
    }
    
    /*=============================================
    VERIFICAR ARCHIVOS DEL SISTEMA
    =============================================*/
    static public function verificarArchivosModel() {
        $archivosRequeridos = [
            'models/crudEstadoPedidos.php',
            'controllers/controllerEstadoPedidos.php',
            'models/crudImpresionPedidos.php',
            'controllers/controllerImpresionPedidos.php',
            'views/modules/ajaxEstadoPedidos.php'
        ];
        
        $resultados = array();
        
        foreach ($archivosRequeridos as $archivo) {
            if (file_exists($archivo)) {
                $resultados[$archivo] = array(
                    "status" => "success",
                    "message" => "Archivo existe",
                    "size" => filesize($archivo)
                );
            } else {
                $resultados[$archivo] = array(
                    "status" => "error",
                    "message" => "Archivo no encontrado"
                );
            }
        }
        
        return $resultados;
    }
    
    /*=============================================
    VERIFICAR CONFIGURACION DE IMPRESORAS
    =============================================*/
    static public function verificarConfiguracionImpresorasModel() {
        try {
            $stmt = Conexion::conectar()->prepare("
                SELECT categoria, ip, puerto 
                FROM impresoras 
                ORDER BY categoria
            ");
            $stmt->execute();
            $impresoras = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $categoriasRequeridas = ['bar', 'cocina', 'asados'];
            $categoriasExistentes = array_column($impresoras, 'categoria');
            $categoriasFaltantes = array_diff($categoriasRequeridas, $categoriasExistentes);
            
            if (empty($categoriasFaltantes)) {
                return array(
                    "status" => "success",
                    "message" => "Configuración de impresoras completa",
                    "impresoras" => $impresoras
                );
            } else {
                return array(
                    "status" => "warning",
                    "message" => "Faltan configuraciones de impresoras",
                    "impresoras_existentes" => $impresoras,
                    "categorias_faltantes" => $categoriasFaltantes
                );
            }
            
        } catch (Exception $e) {
            return array(
                "status" => "error",
                "message" => "Error al verificar configuración: " . $e->getMessage()
            );
        }
    }
    
    /*=============================================
    VERIFICAR DATOS DE PRUEBA
    =============================================*/
    static public function verificarDatosPruebaModel() {
        try {
            $db = Conexion::conectar();

            $stmt = $db->query("SELECT COUNT(*) as total FROM pedidos");
            $totalPedidos = $stmt->fetch()['total'];

            $stmt = $db->query("SELECT COUNT(*) as total FROM productos");
            $totalProductos = $stmt->fetch()['total'];

            $stmt = $db->query("SELECT COUNT(*) as total FROM mesas");
            $totalMesas = $stmt->fetch()['total'];

            return array(
                "status" => "success",
                "message" => "Datos del sistema verificados",
                "total_pedidos" => $totalPedidos,
                "total_productos" => $totalProductos,
                "total_mesas" => $totalMesas
            );

        } catch (Exception $e) {
            return array(
                "status" => "error",
                "message" => "Error al verificar datos: " . $e->getMessage()
            );
        }
    }

    /*=============================================
    OBTENER RESUMEN COMPLETO DEL SISTEMA
    =============================================*/
    static public function obtenerResumenSistemaModel() {
        return array(
            "conexion" => self::verificarConexionModel(),
            "estructura_pedidos" => self::verificarEstructuraPedidosModel(),
            "tabla_impresoras" => self::verificarTablaImpresorasModel(),
            "tablas_historial" => self::verificarTablasHistorialModel(),
            "triggers" => self::verificarTriggersModel(),
            "archivos" => self::verificarArchivosModel(),
            "configuracion_impresoras" => self::verificarConfiguracionImpresorasModel(),
            "datos_prueba" => self::verificarDatosPruebaModel()
        );
    }
}
