<?php
class Paginas
{
	public static function enlacesPaginasModel($enlaces,$usuario)
		{
			if($enlaces == "ingresar" || $enlaces == "pagina1" || $enlaces == "consultar" || $enlaces == "editar" || $enlaces == "salir" || $enlaces == "editarp" || $enlaces == "buscar" || $enlaces == "inde" || $enlaces == "usuarios" || $enlaces == "pdf" || $enlaces == "pdf1" || $enlaces == "facturaCopia" || $enlaces == "facturacionRapida" || $enlaces == "diagnostico" || $enlaces == "pantallaCocina" || $enlaces == "pedidosCocinaPendientes" || $enlaces == "pedidosCocinaEntregados" || $enlaces == "pedidosBarPendientes" || $enlaces == "pedidosBarEntregados" || $enlaces == "pedidosAsadosPendientes" || $enlaces == "pedidosAsadosEntregados" || $enlaces == "crear_pedido_prueba" || $enlaces == "cambiar_estado_pedido" || $enlaces == "debug_pedidos_creados" || $enlaces == "debug_consultas_pendientes" || $enlaces == "test_consultas_corregidas" || $enlaces == "debug_sql_paso_a_paso" || $enlaces == "debug_producto_vendido_mesa" || $enlaces == "debug_consulta_exacta" || $enlaces == "test_modelo_corregido" || $enlaces == "test_enum_problema" || $enlaces == "debug_marcar_entregado" || $enlaces == "test_ajax_marcar_entregado" || $enlaces == "test_ajax_simple" || $enlaces == "debug_estado_pedidos" || $enlaces == "test_ajax_pedido_8" || $enlaces == "verificar_pedido_8" || $enlaces == "test_sql_directo" || $enlaces == "crear_tabla_entregas_categoria" || $enlaces == "test_cancelar_mesa" || $enlaces == "cancelarMesa" || $enlaces == "cancelarPedido" || $enlaces == "test_eliminar_entregas" || $enlaces == "debug_error_facturacion" || $enlaces == "verificar_estructura_tablas" || $enlaces == "test_facturacion_corregida" || $enlaces == "debug_error_usuario_id" || $enlaces == "test_usuario_id_corregido" || $enlaces == "debug_error_real_usuario_id" || $enlaces == "verificacion_final_facturacion" || $enlaces == "debug_mesa_10" || $enlaces == "test_facturacion_estados" || $enlaces == "debug_problemas_pedidos" || $enlaces == "corregir_trigger_historial" || $enlaces == "solucionar_problemas_pedidos" || $enlaces == "verificar_correcciones" || $enlaces == "limpiar_pedidos_duplicados" || $enlaces == "verificacion_final_limpieza" || $enlaces == "correccion_final_sistema")
						{	$module =  "views/modules/".$enlaces.".php";	}
			else if($enlaces == "index")
						{	$module =  "views/modules/ingresar.php"; }
			else if($enlaces == "buscarProducto")
						{	$module =  "views/modules/buscarProducto.php"; }
					
			//session_start();
			//######################### Start  $Usuario==3  Cajero##################################
			if ($usuario==3 or $usuario==10 or $usuario==4) {
				if($enlaces == "ingresar" || $enlaces == "pagina1" || $enlaces == "consultar" || $enlaces == "editar" || $enlaces == "salir" || $enlaces == "editarp" || $enlaces == "buscar" || $enlaces == "inde" || $enlaces == "usuarios" || $enlaces == "pdf" || $enlaces == "buscarProducto" || $enlaces == "facturacionRapida" || $enlaces == "diagnostico" || $enlaces == "pantallaCocina" || $enlaces == "pedidosCocinaPendientes" || $enlaces == "pedidosCocinaEntregados" || $enlaces == "pedidosBarPendientes" || $enlaces == "pedidosBarEntregados" || $enlaces == "pedidosAsadosPendientes" || $enlaces == "pedidosAsadosEntregados")
						{	$module =  "views/modules/".$enlaces.".php";	}

				#------Turno -----------------------------
				#
				else if($enlaces == "turnos")
						{	$module =  "views/modules/turnos.php"; }
				# TURNO
				#-----------------------------------------
						else if($enlaces == "registroTurno" || $enlaces == "turno" || $enlaces == "editarTurno" || $enlaces == "turnoVentas" )
							{	$module =  "views/modules/".$enlaces.".php";	}
						else if($enlaces == "indexT")
								{	$module =  "views/modules/registroTurno.php";	}

							else if($enlaces == "okT")
								{	$module =  "views/modules/registroPmesa.php";	}

							else if($enlaces == "falloT")
								{	$module =  "views/modules/registroTurno.php";	}

							else if($enlaces == "cambioT")
								{	$module =  "views/modules/ingresar.php";	}
				#-----------------------------------------
				# COCINA
				#-----------------------------------------
						else if($enlaces == "cocina"  )
							{	$module =  "views/modules/".$enlaces.".php";	}

				#-----------------------------------------
				## PEDIDO MESA
				#----------------------
						else if($enlaces == "registroPmesa" || $enlaces == "Pmesa" || $enlaces == "editarPmesa" )
							{	$module =  "views/modules/".$enlaces.".php";	}
						else if($enlaces == "indexPm")
								{	$module =  "views/modules/registroPmesa.php";	}

							else if($enlaces == "okPm")
								{	$module =  "views/modules/registroPmesa.php";	}

							else if($enlaces == "falloPm")
								{	$module =  "views/modules/registroPmesa.php";	}

							else if($enlaces == "cambioPm")
								{	$module =  "views/modules/registroPmesa.php";	}
				#---------------------
				#-------------------- Vista Ventas ----------------------
					else if($enlaces == "vistaVentas")
						{	$module =  "views/modules/vistaVentasDia.php"; }

					else if($enlaces == "inventarioTotal.php")
						{	$module =  "views/modules/inventarioTotal.php"; }

					else if($enlaces == "reporteExcel2" )
						{	$module =  "views/modules/reporteExcel2.php"; }
				#------------------   -----------------------
				# SUMINISTRO
				#----------------------
						else if($enlaces == "registroSuministro" || $enlaces == "suministro" || $enlaces == "suministro1" || $enlaces == "editarSuministro" || $enlaces == "registroFacturaSuministro" ||  $enlaces == "registroFacturaSuministro1" || $enlaces == "pruebaS" || $enlaces == "registroProductoSuministro" || $enlaces == "cargarSucursal" || $enlaces == "sucursal" || $enlaces == "sucursalCocina")
							{	$module =  "views/modules/".$enlaces.".php";	}
				#--------------------------------------------------------------------------------------------------------
				# SUMINISTRO PRODUCTOS
				#----------------------
						else if($enlaces == "registroSuministroP" || $enlaces == "suministroP" || $enlaces == "editarSuministroP" || $enlaces == "suministroPdetalle" )
							{	$module =  "views/modules/".$enlaces.".php";	}
				#------------------------------------------------------------------------------------------------------# PRODUCTOS
				#----------------------
						else if($enlaces == "registroProducto" || $enlaces == "producto" || $enlaces == "editarProducto" || $enlaces == "registroFacturasencilla" )
							{	$module =  "views/modules/".$enlaces.".php";	}
						/*else if($enlaces == "indexP")
								{	$module =  "views/modules/registroProducto.php";	}

							else if($enlaces == "okP")
								{	$module =  "views/modules/producto.php";	}

							else if($enlaces == "falloP")
								{	$module =  "views/modules/producto.php";	}

							else if($enlaces == "cambioP")
								{	$module =  "views/modules/producto.php";	}*/
				#---------------------------------------------------------------------------------------------------
				## FACTURA
				#----------------------
						else if($enlaces == "registroFactura" || $enlaces == "factura" || $enlaces == "editarFactura" )
							{	$module =  "views/modules/".$enlaces.".php";	}
						else if($enlaces == "indexT")
								{	$module =  "views/modules/registroFactura.php";	}

							else if($enlaces == "okF")
								{	$module =  "views/modules/factura.php";	}

							else if($enlaces == "falloF")
								{	$module =  "views/modules/factura.php";	}

							else if($enlaces == "cambioF")
								{	$module =  "views/modules/factura.php";	}
				#---------------------
				# PEDIDO
				#----------------------
					else if($enlaces == "registroPedido" || $enlaces == "pedido" || $enlaces == "editarPedido" )
						{	$module =  "views/modules/".$enlaces.".php";	}
					else if($enlaces == "indexPe")
							{	$module =  "views/modules/registroPedido.php";	}

					else if($enlaces == "okPe")
						{	$module =  "views/modules/pedido.php";	}

					else if($enlaces == "falloPe")
						{	$module =  "views/modules/pedido.php";	}

					else if($enlaces == "cambioPe")
						{	$module =  "views/modules/pedido.php";	}
				#---------------------
				# INGRESO
				#----------------------
						else if($enlaces == "registroIngreso" || $enlaces == "ingreso" || $enlaces == "editarIngreso" )
							{	$module =  "views/modules/".$enlaces.".php";	}
						else if($enlaces == "indexPe")
								{	$module =  "views/modules/registroIngreso.php";	}

							else if($enlaces == "okI")
								{	$module =  "views/modules/ingreso.php";	}

							else if($enlaces == "falloI")
								{	$module =  "views/modules/ingreso.php";	}

							else if($enlaces == "cambioI")
								{	$module =  "views/modules/ingreso.php";	}
				#---------------------
				# EGRESO
				#----------------------
						else if($enlaces == "registroEgreso" || $enlaces == "egreso" || $enlaces == "editarEgreso" )
							{	$module =  "views/modules/".$enlaces.".php";	}
						else if($enlaces == "indexPe")
								{	$module =  "views/modules/registroEgreso.php";	}

							else if($enlaces == "okE")
								{	$module =  "views/modules/egreso.php";	}

							else if($enlaces == "falloE")
								{	$module =  "views/modules/egreso.php";	}

							else if($enlaces == "cambioE")
								{	$module =  "views/modules/egreso.php";	}
				#---------------------
				# COMPRAS SUMINISTRO
				#----------------------
						else if($enlaces == "registroCsuministro" || "registroCsuministroP" ||"registroCsuministro1" || $enlaces == "Csuministro" || $enlaces == "editarCsuministro" || $enlaces == "editarSuministroFactura" )
							{	$module =  "views/modules/".$enlaces.".php";	}
						else if($enlaces == "indexPe")
								{	$module =  "views/modules/registroCsuministro.php";	}

							else if($enlaces == "okCs")
								{	$module =  "views/modules/registroFacturaSuministro.php";	}
							else if($enlaces == "okFs")
								{	$module =  "views/modules/registroFacturaSuministro.php";	}

							else if($enlaces == "falloCs")
								{	$module =  "views/modules/registroCsuministro.php";	}

							else if($enlaces == "falloFs")
								{	$module =  "views/modules/registroFacturaSuministro.php";	}

							else if($enlaces == "cambioCs")
								{	$module =  "views/modules/Csuministro.php";	}
				#---------------------
				# PROPINA
				#----------------------
						else if($enlaces == "registroPropina" || $enlaces == "Propina" || $enlaces == "editarPropina" )
							{	$module =  "views/modules/".$enlaces.".php";	}
						else if($enlaces == "indexPe")
								{	$module =  "views/modules/registroPropina.php";	}

							else if($enlaces == "okPro")
								{	$module =  "views/modules/Propina.php";	}

							else if($enlaces == "falloPro")
								{	$module =  "views/modules/Propina.php";	}

							else if($enlaces == "cambioPro")
								{	$module =  "views/modules/Propina.php";	}
				#---------------------
				## DEVOLUCIONES
				#--------------------------------
						else if($enlaces == "registroDevolucion" || $enlaces == "devolucionT" || $enlaces == "editarDevolucion" || $enlaces == "parcialDevolucion" || $enlaces == "Devolucion" )
							{	$module =  "views/modules/".$enlaces.".php";	}
						else if($enlaces == "indexPe")
								{	$module =  "views/modules/registroDevolucion.php";	}

							else if($enlaces == "okDevolucion")
								{	$module =  "views/modules/devolucion.php";	}

							else if($enlaces == "falloDevolucion")
								{	$module =  "views/modules/registroDevolucion.php";	}

							else if($enlaces == "cambioDevolucion")
								{	$module =  "views/modules/registroDevolucion.php";	}
				#------------------------------
				## PAGOS CLIENTE
				#--------------------------------
						else if($enlaces == "acobonoDeuda" || $enlaces == "registroAbonoProveedor" || $enlaces == "abonosCliente" || $enlaces == "abonosProveedor" || $enlaces == "deudacliente" || $enlaces == "buscarDeudacliente" || $enlaces == "carteraUsuario")
							{	$module =  "views/modules/".$enlaces.".php";	}
						else if($enlaces == "indexPago")
								{	$module =  "views/modules/registroAbonoCliente.php";	}

							else if($enlaces == "okPcliente")
								{	$module =  "views/modules/abonosCliente.php";	}

							else if($enlaces == "falloPcliente")
								{	$module =  "views/modules/registroAbonoCliente.php";	}

							else if($enlaces == "cambioPcliente")
								{	$module =  "views/modules/registroAbonoCliente.php";	}
							else if($enlaces == "indexPagoP")
								{	$module =  "views/modules/registroAbonoProveedor.php";	}

							else if($enlaces == "okPproveedor")
								{	$module =  "views/modules/abonosProveedor.php";	}

							else if($enlaces == "falloPproveedor")
								{	$module =  "views/modules/registroAbonoProveedor.php";	}

							else if($enlaces == "cambioPproveedor")
								{	$module =  "views/modules/registroAbonoProveedor.php";	}
				#------------------------------

				#### PAGOS PROVEEDOR
				#--------------------------------
						else if($enlaces == "acobonoDeuda" || $enlaces == "acobonoDeudaProveedor" || $enlaces == "abonosCliente" || $enlaces == "abonosProveedor" || $enlaces == "deudaProveedor" || $enlaces == "buscarDeudaProveedor" || $enlaces == "carteraProveedor")
							{	$module =  "views/modules/".$enlaces.".php";	}

							else if($enlaces == "okPproveedor")
								{	$module =  "views/modules/carteraProveedor.php";	}

							else if($enlaces == "falloPproveedor")
								{	$module =  "views/modules/registroAbonoProveedor.php";	}

							else if($enlaces == "cambioPproveedor")
								{	$module =  "views/modules/registroAbonoProveedor.php";	}
				#------------------------------

			}
	//############################ End  $Usuario==3  Cajero  ##################################


		if ($usuario==1)
		{
				# code...

					if($enlaces == "ingresar" || $enlaces == "pagina1" || $enlaces == "consultar" || $enlaces == "editar" || $enlaces == "salir" || $enlaces == "editarp" || $enlaces == "buscar" || $enlaces == "inde" || $enlaces == "usuarios" || $enlaces == "pdf" || $enlaces == "pdf1" || $enlaces == "buscarProducto" || $enlaces == "facturacionRapida" || $enlaces == "diagnostico" || $enlaces == "pantallaCocina" || $enlaces == "debug_mesa" || $enlaces == "test_simple_mesa" || $enlaces == "debug_cocina" || $enlaces == "test_flujo_pedidos" || $enlaces == "crear_datos_prueba" || $enlaces == "test_ajax" || $enlaces == "pedidosCocinaPendientes" || $enlaces == "pedidosCocinaEntregados" || $enlaces == "pedidosBarPendientes" || $enlaces == "pedidosBarEntregados" || $enlaces == "pedidosAsadosPendientes" || $enlaces == "pedidosAsadosEntregados")
						{	$module =  "views/modules/".$enlaces.".php";	}

					else if($enlaces == "registroPerdida")
						{	$module =  "views/modules/registroPerdida.php"; }
					else if($enlaces == "vistaPerdidas")
						{	$module =  "views/modules/vistaPerdidas.php"; }

					else if($enlaces == "consultar")
						{	$module =  "views/modules/consultar.php"; }

					else if($enlaces == "index")
						{	$module =  "views/modules/ingresar.php"; }

					else if($enlaces == "turnos")
						{	$module =  "views/modules/turnos.php"; }

					else if($enlaces == "fallo")
						{	$module =  "views/modules/ingresar.php"; }

					else if($enlaces == "vistaVentas")
						{	$module =  "views/modules/vistaVentasDia.php"; }

					else if($enlaces == "inventarioTotal")
						{	$module =  "views/modules/inventarioTotal.php"; }

					else if($enlaces == "reporteExcel2" )
						{	$module =  "views/modules/reporteExcel2.php"; }

			# Nav o Menu----------------------------------------------

			#Contador ------------------------------------------------
					else if($enlaces == "reporteExcel" && $_SESSION["tipo_usuario"]== 3)
						{	$module =  "views/modules/reporteExcel.php"; }

					else if($enlaces == "reporteExcel3" && $_SESSION["tipo_usuario"]== 3)
						{	$module =  "views/modules/reporteExcel2.php"; }

					else if($enlaces == "registroEditarOrdenFecha" && ($_SESSION["tipo_usuario"]== 3 or $_SESSION["tipo_usuario"]== 1) )
						{	$module =  "views/modules/registroEditarOrdenFecha.php"; }

					else if($enlaces == "editarFactura" && ($_SESSION["tipo_usuario"]== 3 or $_SESSION["tipo_usuario"]== 1) )
						{	$module =  "views/modules/editarFactura.php"; }

					else if($enlaces == "depurarPatio" && $_SESSION["tipo_usuario"]== 3)
						{	$module =  "views/modules/depurarPatio.php"; }

					else if($enlaces == "copiaFactura" && $_SESSION["tipo_usuario"]== 3)
						{	$module =  "views/modules/buscarPlacaFactura2.php"; }

					else if($enlaces == "pdf2") //Imprime busqueda por número de factura
						{	$module =  "views/modules/pdf.php"; }

					else if($enlaces == "buscarFactura") //Busca una factura por su número
						{	$module =  "views/modules/buscarFactura.php"; }
			# End Contador --------------------------------------------
			#
			# # COCINA
				#-----------------------------------------
						else if($enlaces == "cocina" || $enlaces == "turno" || $enlaces == "editarTurno" || $enlaces == "turnoVentas" )
							{	$module =  "views/modules/".$enlaces.".php";	}
						else if($enlaces == "indexT")
								{	$module =  "views/modules/registroTurno.php";	}

							else if($enlaces == "okT")
								{	$module =  "views/modules/registroPmesa.php";	}

							else if($enlaces == "falloT")
								{	$module =  "views/modules/registroTurno.php";	}

							else if($enlaces == "cambioT")
								{	$module =  "views/modules/ingresar.php";	}
				#-----------------------------------------

			#Administrador----------------------------
					else if($enlaces == "ok" && $_SESSION["tipo_usuario"]== 1)
						{	$module =  "views/modules/usuarios.php"; }

					else if($enlaces == "listadoConductores")
						{	$module =  "views/modules/listado_conductores.php"; }

					else if($enlaces == "cambio" && $_SESSION["tipo_usuario"]== 1)
						{	$module =  "views/modules/usuarios.php"; }


				# Usuario----------------------------------------------
					else if($enlaces == "registro" && ($_SESSION["tipo_usuario"]== 1 or $_SESSION["tipo_usuario"]== 3) )
						{	$module =  "views/modules/registro.php"; }

					else if($enlaces == "buscarPlacaFactura" )
						{	$module =  "views/modules/buscarPlacaFactura.php"; }

					else if($enlaces == "usuarioE" && ($_SESSION["tipo_usuario"]== 1 or $_SESSION["tipo_usuario"]== 3) )
						{	$module =  "views/modules/usuarios.php"; }

					else if($enlaces == "usuario" && $_SESSION["tipo_usuario"]== 1 or $_SESSION["tipo_usuario"]== 3)
						{	$module =  "views/modules/usuarioC.php"; }
					else if($enlaces == "usuariosL" && $_SESSION["tipo_usuario"]== 3)
						{	$module =  "views/modules/usuarios.php"; }
				# Usuario---------------------------------------------- || $enlaces == "usuarios"
			#fin-------------------------------------------

				# Producto----------------------------------------------
					else if($enlaces == "registroProducto"  || $enlaces == "Producto" )
						{	$module =  "views/modules/registroProducto.php"; }

					else if($enlaces == "ingresarProducto")
						{	$module =  "views/modules/ingresarProducto.php"; }

					else if($enlaces == "productos")
						{	$module =  "views/modules/productos.php"; }

					else if($enlaces == "productoC")
						{	$module =  "views/modules/productosC.php"; }
				# Producto----------------------------------------------
				# DESCUENTO
				#----------------------
							else if($enlaces == "registroDescuento" || $enlaces == "descuento" || $enlaces == "editarDescuento" )
							{	$module =  "views/modules/".$enlaces.".php";}
							else if($enlaces == "okDes")
								{	$module =  "views/modules/descuento.php";	}

							else if($enlaces == "falloDes")
								{	$module =  "views/modules/descuento.php";	}

							else if($enlaces == "cambioDes")
								{	$module =  "views/modules/descuento.php";	}
				#----------------------
				# ROLL
				#----------------------
							else if($enlaces == "registroRoll" || $enlaces == "rol" || $enlaces == "editarRoll" )
							{	$module =  "views/modules/".$enlaces.".php";	}
							else if($enlaces == "indexR")
								{	$module =  "views/modules/registroRoll.php";	}

							else if($enlaces == "okR")
								{	$module =  "views/modules/rol.php";	}

							else if($enlaces == "falloR")
								{	$module =  "views/modules/rol.php";	}

							else if($enlaces == "cambioR")
								{	$module =  "views/modules/rol.php";	}
				#----------------------
				# UNIDAD
				#----------------------
							else if($enlaces == "registroUnidad" || $enlaces == "unidades" || $enlaces == "editarUnidad" )
							{	$module =  "views/modules/".$enlaces.".php";	}
							else if($enlaces == "indexUni")
								{	$module =  "views/modules/registroUnidad.php";	}

							else if($enlaces == "okUni")
								{	$module =  "views/modules/unidades.php";	}

							else if($enlaces == "falloUni")
								{	$module =  "views/modules/unidades.php";	}

							else if($enlaces == "cambioUni")
								{	$module =  "views/modules/unidades.php";	}
				#----------------------
				# ACTIVO
				#----------------------
					else if($enlaces == "registroActivo" || $enlaces == "activo" || $enlaces == "editarActivo" )
					{	$module =  "views/modules/".$enlaces.".php";	}
					else if($enlaces == "indexAc")
						{	$module =  "views/modules/registroActivo.php";	}
					else if($enlaces == "okAc")
						{	$module =  "views/modules/activo.php";	}
					else if($enlaces == "falloAc")
						{	$module =  "views/modules/activo.php";	}
					else if($enlaces == "cambioAc")
						{	$module =  "views/modules/activo.php";	}
				#----------------------
				# MESA
				#----------------------
					else if($enlaces == "registroMesa" || $enlaces == "mesa" || $enlaces == "editarMesa" )
						{	$module =  "views/modules/".$enlaces.".php";	}
					else if($enlaces == "indexM")
						{	$module =  "views/modules/registroMesa.php";	}

					else if($enlaces == "okM")
						{	$module =  "views/modules/mesa.php";	}

					else if($enlaces == "falloM")
						{	$module =  "views/modules/mesa.php";	}

					else if($enlaces == "cambioM")
						{	$module =  "views/modules/mesa.php";	}
				#---------------------

				# DEPARTAMENTO
				#----------------------
						else if($enlaces == "registroDepartamento" || $enlaces == "departamento" || $enlaces == "editarDepartamento" )
							{	$module =  "views/modules/".$enlaces.".php";	}
						else if($enlaces == "indexD")
								{	$module =  "views/modules/registroDepartamento.php";	}

							else if($enlaces == "okD")
								{	$module =  "views/modules/departamento.php";	}

							else if($enlaces == "falloD")
								{	$module =  "views/modules/departamento.php";	}

							else if($enlaces == "cambioD")
								{	$module =  "views/modules/departamento.php";	}
				#---------------------

				# PROVEEDORES
				#----------------------
						else if($enlaces == "registroProveedor" || $enlaces == "proveedor" || $enlaces == "editarProveedor" )
							{	$module =  "views/modules/".$enlaces.".php";	}
						else if($enlaces == "indexPv")
								{	$module =  "views/modules/registroProveedor.php";	}

							else if($enlaces == "okPv")
								{	$module =  "views/modules/proveedor.php";	}

							else if($enlaces == "falloPv")
								{	$module =  "views/modules/proveedor.php";	}

							else if($enlaces == "cambioPv")
								{	$module =  "views/modules/proveedor.php";	}

				#---------------------

				## PEDIDO MESA
				#----------------------
						else if($enlaces == "registroPmesa" || $enlaces == "Pmesa" || $enlaces == "editarPmesa" )
							{	$module =  "views/modules/".$enlaces.".php";	}
						else if($enlaces == "indexPm")
								{	$module =  "views/modules/registroPmesa.php";	}

							else if($enlaces == "okPm")
								{	$module =  "views/modules/registroPmesa.php";	}

							else if($enlaces == "falloPm")
								{	$module =  "views/modules/registroPmesa.php";	}

							else if($enlaces == "cambioPm")
								{	$module =  "views/modules/registroPmesa.php";	}
				#---------------------

				## PEDIDO MESA Producto
				#----------------------
						else if($enlaces == "registroPmesaProducto" || $enlaces == "PmesaProducto" || $enlaces == "editarPmesaProducto" )
							{	$module =  "views/modules/".$enlaces.".php";	}
						else if($enlaces == "indexPmP")
								{	$module =  "views/modules/registroPmesaProducto.php";	}

							else if($enlaces == "okPmP")
								{	$module =  "views/modules/PmesaProducto.php";	}

							else if($enlaces == "falloPmP")
								{	$module =  "views/modules/PmesaProducto.php";	}

							else if($enlaces == "cambioPmP")
								{	$module =  "views/modules/PmesaProducto.php";	}
				#---------------------

				# PRODUCTOS
				#----------------------
						else if($enlaces == "registroProducto" || $enlaces == "producto" || $enlaces == "editarProducto" || $enlaces == "registroFacturasencilla" )
							{	$module =  "views/modules/".$enlaces.".php";	}
						else if($enlaces == "indexP")
								{	$module =  "views/modules/registroProducto.php";	}

							else if($enlaces == "okP")
								{	$module =  "views/modules/producto.php";	}

							else if($enlaces == "falloP")
								{	$module =  "views/modules/producto.php";	}

							else if($enlaces == "cambioP")
								{	$module =  "views/modules/producto.php";	}
				#---------------------

				# SUMINISTRO
				#----------------------
						else if($enlaces == "registroSuministro" || $enlaces == "suministro" || $enlaces == "suministro1" || $enlaces == "editarSuministro" || $enlaces == "registroFacturaSuministro" ||  $enlaces == "registroFacturaSuministro1" || $enlaces == "pruebaS" || $enlaces == "registroProductoSuministro" || $enlaces == "cargarSucursal" || $enlaces == "sucursal" || $enlaces == "sucursalCocina")
							{	$module =  "views/modules/".$enlaces.".php";	}
						else if($enlaces == "indexS")
								{	$module =  "views/modules/registroSuministro.php";	}

							else if($enlaces == "okS")
								{	$module =  "views/modules/suministro1.php";	}
							else if($enlaces == "okcS")
								{	$module =  "views/modules/cargarSucursal.php";	}

							else if($enlaces == "falloS")
								{	$module =  "views/modules/suministro1.php";	}

							else if($enlaces == "cambioS")
								{	$module =  "views/modules/suministro1.php";	}
				#---------------------

				# SUMINISTRO PRODUCTOS
				#----------------------
						else if($enlaces == "registroSuministroP" || $enlaces == "suministroP" || $enlaces == "editarSuministroP" || $enlaces == "suministroPdetalle" )
							{	$module =  "views/modules/".$enlaces.".php";	}
						else if($enlaces == "indexSP")
								{	$module =  "views/modules/registroSuministroP.php";	}

							else if($enlaces == "okSP")
								{	$module =  "views/modules/suministroP.php";	}

							else if($enlaces == "falloSP")
								{	$module =  "views/modules/suministroP.php";	}

							else if($enlaces == "cambioSP")
								{	$module =  "views/modules/suministroP.php";	}
							else if($enlaces == "cambioSPD")
								{	$module =  "views/modules/suministroPdetalle.php";	}
				#---------------------

				# CIUDAD
				#----------------------
						else if($enlaces == "registroCiudad" || $enlaces == "ciudad" || $enlaces == "editarCiudad" )
							{	$module =  "views/modules/".$enlaces.".php";	}

						else if($enlaces == "indexC")
								{	$module =  "views/modules/registroCiudad.php";	}

							else if($enlaces == "okC")
								{	$module =  "views/modules/ciudad.php";	}

							else if($enlaces == "falloC")
								{	$module =  "views/modules/ciudad.php";	}

							else if($enlaces == "cambioC")
								{	$module =  "views/modules/ciudad.php";	}
				#---------------------

				# CIUDAD PROVEEDOR
				#----------------------
						else if($enlaces == "registroCiudadProveedor" || $enlaces == "ciudad_proveedor" || $enlaces == "editarCiudadProveedor" )
							{	$module =  "views/modules/".$enlaces.".php";	}

						else if($enlaces == "indexCPv")
								{	$module =  "views/modules/registroCiudad.php";	}

							else if($enlaces == "okCPv")
								{	$module =  "views/modules/ciudad_proveedor.php";	}

							else if($enlaces == "falloCPv")
								{	$module =  "views/modules/ciudad_proveedor.php";	}

							else if($enlaces == "cambioCPv")
								{	$module =  "views/modules/ciudad_proveedor.php";	}
				#---------------------

				# TURNO
				#-----------------------------------------
						else if($enlaces == "registroTurno" || $enlaces == "turno" || $enlaces == "editarTurno" || $enlaces == "turnoVentas")
							{	$module =  "views/modules/".$enlaces.".php";	}
						else if($enlaces == "indexT")
								{	$module =  "views/modules/registroTurno.php";	}

							else if($enlaces == "okT")
								{	$module =  "views/modules/registroPmesa.php";	}

							else if($enlaces == "falloT")
								{	$module =  "views/modules/registroTurno.php";	}

							else if($enlaces == "cambioT")
								{	$module =  "views/modules/ingresar.php";	}
				#-----------------------------------------
				#
				## FACTURA
				#----------------------
						else if($enlaces == "registroFactura" || $enlaces == "factura" || $enlaces == "editarFactura" )
							{	$module =  "views/modules/".$enlaces.".php";	}
						else if($enlaces == "indexT")
								{	$module =  "views/modules/registroFactura.php";	}

							else if($enlaces == "okF")
								{	$module =  "views/modules/factura.php";	}

							else if($enlaces == "falloF")
								{	$module =  "views/modules/factura.php";	}

							else if($enlaces == "cambioF")
								{	$module =  "views/modules/factura.php";	}
				#---------------------

				# PEDIDO
				#----------------------
					else if($enlaces == "registroPedido" || $enlaces == "pedido" || $enlaces == "editarPedido" )
						{	$module =  "views/modules/".$enlaces.".php";	}
					else if($enlaces == "indexPe")
							{	$module =  "views/modules/registroPedido.php";	}

					else if($enlaces == "okPe")
						{	$module =  "views/modules/pedido.php";	}

					else if($enlaces == "falloPe")
						{	$module =  "views/modules/pedido.php";	}

					else if($enlaces == "cambioPe")
						{	$module =  "views/modules/pedido.php";	}
				#---------------------

				# INGRESO
				#----------------------
						else if($enlaces == "registroIngreso" || $enlaces == "ingreso" || $enlaces == "editarIngreso" )
							{	$module =  "views/modules/".$enlaces.".php";	}
						else if($enlaces == "indexPe")
								{	$module =  "views/modules/registroIngreso.php";	}

							else if($enlaces == "okI")
								{	$module =  "views/modules/ingreso.php";	}

							else if($enlaces == "falloI")
								{	$module =  "views/modules/ingreso.php";	}

							else if($enlaces == "cambioI")
								{	$module =  "views/modules/ingreso.php";	}
				#---------------------

				# EGRESO
				#----------------------
						else if($enlaces == "registroEgreso" || $enlaces == "egreso" || $enlaces == "editarEgreso" )
							{	$module =  "views/modules/".$enlaces.".php";	}
						else if($enlaces == "indexPe")
								{	$module =  "views/modules/registroEgreso.php";	}

							else if($enlaces == "okE")
								{	$module =  "views/modules/egreso.php";	}

							else if($enlaces == "falloE")
								{	$module =  "views/modules/egreso.php";	}

							else if($enlaces == "cambioE")
								{	$module =  "views/modules/egreso.php";	}
				#---------------------

				# COMPRAS SUMINISTRO
				#----------------------
						else if($enlaces == "registroCsuministro" || "registroCsuministroP" || "registroCsuministro1" || $enlaces == "Csuministro" || $enlaces == "editarCsuministro" || $enlaces == "editarSuministroFactura")
							{	$module =  "views/modules/".$enlaces.".php";	}
						else if($enlaces == "indexPe")
								{	$module =  "views/modules/registroCsuministro.php";	}

							else if($enlaces == "okCs")
								{	$module =  "views/modules/registroFacturaSuministro.php";	}
							else if($enlaces == "okFs")
								{	$module =  "views/modules/registroFacturaSuministro.php";	}

							else if($enlaces == "falloCs")
								{	$module =  "views/modules/registroCsuministro.php";	}

							else if($enlaces == "falloFs")
								{	$module =  "views/modules/registroFacturaSuministro.php";	}

							else if($enlaces == "cambioCs")
								{	$module =  "views/modules/Csuministro.php";	}
				#---------------------

				# PROPINA
				#----------------------
						else if($enlaces == "registroPropina" || $enlaces == "Propina" || $enlaces == "editarPropina" )
							{	$module =  "views/modules/".$enlaces.".php";	}
						else if($enlaces == "indexPe")
								{	$module =  "views/modules/registroPropina.php";	}

							else if($enlaces == "okPro")
								{	$module =  "views/modules/Propina.php";	}

							else if($enlaces == "falloPro")
								{	$module =  "views/modules/Propina.php";	}

							else if($enlaces == "cambioPro")
								{	$module =  "views/modules/Propina.php";	}
				#---------------------

				## DEVOLUCIONES
				#--------------------------------
						else if($enlaces == "registroDevolucion" || $enlaces == "devolucionT" || $enlaces == "editarDevolucion" || $enlaces == "parcialDevolucion" || $enlaces == "Devolucion" )
							{	$module =  "views/modules/".$enlaces.".php";	}
						else if($enlaces == "indexPe")
								{	$module =  "views/modules/registroDevolucion.php";	}

							else if($enlaces == "okDevolucion")
								{	$module =  "views/modules/devolucion.php";	}

							else if($enlaces == "falloDevolucion")
								{	$module =  "views/modules/registroDevolucion.php";	}

							else if($enlaces == "cambioDevolucion")
								{	$module =  "views/modules/registroDevolucion.php";	}
				#------------------------------

				## PAGOS CLIENTE
				#--------------------------------
						else if($enlaces == "acobonoDeuda" || $enlaces == "registroAbonoProveedor" || $enlaces == "abonosCliente" || $enlaces == "abonosProveedor" || $enlaces == "deudacliente" || $enlaces == "buscarDeudacliente" || $enlaces == "carteraUsuario")
							{	$module =  "views/modules/".$enlaces.".php";	}
						else if($enlaces == "indexPago")
								{	$module =  "views/modules/registroAbonoCliente.php";	}

							else if($enlaces == "okPcliente")
								{	$module =  "views/modules/abonosCliente.php";	}

							else if($enlaces == "falloPcliente")
								{	$module =  "views/modules/registroAbonoCliente.php";	}

							else if($enlaces == "cambioPcliente")
								{	$module =  "views/modules/registroAbonoCliente.php";	}
							else if($enlaces == "indexPagoP")
								{	$module =  "views/modules/registroAbonoProveedor.php";	}

							else if($enlaces == "okPproveedor")
								{	$module =  "views/modules/abonosProveedor.php";	}

							else if($enlaces == "falloPproveedor")
								{	$module =  "views/modules/registroAbonoProveedor.php";	}

							else if($enlaces == "cambioPproveedor")
								{	$module =  "views/modules/registroAbonoProveedor.php";	}
				#------------------------------

				### PAGOS PROVEEDOR
				#--------------------------------
						else if($enlaces == "acobonoDeuda" || $enlaces == "acobonoDeudaProveedor" || $enlaces == "abonosCliente" || $enlaces == "abonosProveedor" || $enlaces == "deudaProveedor" || $enlaces == "buscarDeudaProveedor" || $enlaces == "carteraProveedor")
							{	$module =  "views/modules/".$enlaces.".php";	}

							else if($enlaces == "okPproveedor")
								{	$module =  "views/modules/carteraProveedor.php";	}

							else if($enlaces == "falloPproveedor")
								{	$module =  "views/modules/abonoDeudaProveedor.php";	}

							else if($enlaces == "cambioPproveedor")
								{	$module =  "views/modules/registroAbonoProveedor.php";	}
				#------------------------------
				# CONFIGURACIÓN FACTURA Tirrilla
				#----------------------
					else if($enlaces == "registroFacturaConf" || $enlaces == "facturaConf" || $enlaces == "editarFacturaConf" )
						{	$module =  "views/modules/".$enlaces.".php";	}
					else if($enlaces == "okFC")
						{	$module =  "views/modules/facturaConf.php";	}
					else if($enlaces == "falloFC")
						{	$module =  "views/modules/facturaConf.php";	}
					else if($enlaces == "cambioFC")
						{	$module =  "views/modules/facturaConf.php";	}
				#---------------------
				# PUNTO DE VENTA
				#----------------------
					else if($enlaces == "registroPuntoVenta" || $enlaces == "puntoVenta" || $enlaces == "editarPuntoVenta" )
						{	$module =  "views/modules/".$enlaces.".php";	}
					else if($enlaces == "indexPV")
							{	$module =  "views/modules/registroPuntoVenta.php";	}

					else if($enlaces == "okPV")
						{	$module =  "views/modules/puntoVenta.php";	}

					else if($enlaces == "falloPV")
						{	$module =  "views/modules/puntoVenta.php";	}

					else if($enlaces == "cambioPV")
						{	$module =  "views/modules/puntoVenta.php";	}
				#---------------------
				# PUNTO DE VENTA
				#----------------------
					else if($enlaces == "registroPuntoVenta" || $enlaces == "puntoVenta" || $enlaces == "editarPuntoVenta" )
						{	$module =  "views/modules/".$enlaces.".php";	}
					else if($enlaces == "indexPV")
							{	$module =  "views/modules/registroPuntoVenta.php";	}

					else if($enlaces == "okPV")
						{	$module =  "views/modules/puntoVenta.php";	}

					else if($enlaces == "falloPV")
						{	$module =  "views/modules/puntoVenta.php";	}

					else if($enlaces == "cambioPV")
						{	$module =  "views/modules/puntoVenta.php";	}
				#---------------------

					else if($enlaces == "registroConductor")
						{	$module =  "views/modules/registroConductor.php"; }

					else if($enlaces == "usuarioEf" /*&& $admin== "1" */)
						{	$module =  "views/modules/usuarios.php"; }


			# fin Nav o Menu----------------------------------------------

					else if($enlaces == "principio")
							{	$module =  "views/modules/pagina.html"; }
					else{	$module =  "views/modules/ingresar.php";	}
		}
				return $module;
	}//Cierre de enlacesPaginasModel($enlaces,$usuario)


}//Cierre de class Paginas

?>