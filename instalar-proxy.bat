@echo off
echo ========================================
echo    INSTALADOR PROXY IMPRESION MACARENA
echo ========================================
echo.

echo 1. Verificando Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js no está instalado
    echo.
    echo Por favor instala Node.js desde: https://nodejs.org
    echo Descarga la version LTS y ejecuta el instalador
    echo.
    pause
    exit /b 1
) else (
    echo ✅ Node.js encontrado
    node --version
)

echo.
echo 2. Instalando dependencias...
npm install

if %errorlevel% neq 0 (
    echo ❌ Error instalando dependencias
    pause
    exit /b 1
) else (
    echo ✅ Dependencias instaladas correctamente
)

echo.
echo 3. Probando configuración...
echo.
echo ========================================
echo    CONFIGURACION DEL PROXY
echo ========================================
echo Puerto: 3000
echo IP: 0.0.0.0 (todas las interfaces)
echo.
echo Impresoras configuradas:
echo   bar    : **************:9100
echo   cocina : **************:9100
echo   asados : **************:9100
echo.
echo ========================================

echo.
echo ✅ Instalación completada
echo.
echo Para iniciar el proxy ejecuta:
echo   npm start
echo.
echo O simplemente ejecuta: iniciar-proxy.bat
echo.
pause
