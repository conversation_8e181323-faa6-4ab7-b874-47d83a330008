
<?php 
	ob_start();
	class controllerCiudadProveedor extends MvcController
		{
			
			#ciudad_proveedor  id, ciudad_id, provedor_id, nombre
			#---------------------------------
				#REGISTRO DE ciudad_proveedor
				#------------------------------------
					public function registroCiudadProveedorController()
						{	//echo "<script>alert('Guardo')</script>";
							if(isset($_POST["nombreRegistro"]) )
								{//echo "<script>alert('Entro Controller IF')</script>";		
									$datosController =array('ciudad_id'=>$_POST["ciudad"],
															'provedor_id'=>$_POST["proveedores"],
															'nombre'=>$_POST["nombreRegistro"]);
									//echo "<script>alert('Entro Controller ".$datosController["nombreRegistro"]." no')</script>";	
									$respuesta = DatosCiudadProveedor::registroCiudadProveedorModel($datosController, "ciudad_provedor");
									if($respuesta == "success")
										{	header("location:index.php?action=okCPv");	}
									else
										{	echo "<script>alert('Entro Controller IF')</script>"; header("location:index.php");	}
								}
						}
				#VISTA DE ciudad_proveedor
				#------------------------------------
					public function vistaCiudadProveedorController()
						{
							$respuesta = DatosCiudadProveedor::vistaCiudadProveedorModel("ciudad_provedor");
							foreach($respuesta as $row => $item)
								{
								echo'<tr>						
										<td>'.$item["ciudad_id"].'</td>						
										<td>'.$item["provedor_id"].'</td>						
										<td>'.$item["nombre"].'</td>						
										<td><a href="index.php?action=editarCiudadProveedor&id='.$item["id"].'">Editar</a>|<a href="index.php?action=ciudad_proveedor&idBorrar='.$item["id"].'">Borrar</a></td>
									</tr>';
								}
						}

				#EDITAR ciudad_proveedor
				#------------------------------------
					public function editarCiudadProveedorController()
						{
							$datosController = $_GET["id"];
							$respuesta = DatosCiudadProveedor::editarCiudadProveedorModel($datosController, "ciudad_provedor");
							echo' <input type="text" value="'.$respuesta["ciudad_id"].'" name="ciudadEditar" required>	
							 <input type="text" value="'.$respuesta["provedor_id"].'" name="proveedorEditar" required> 
							 <input type="text" value="'.$respuesta["nombre"].'" name="nombreEditar" required> 
							 <input type="hidden" value="'.$respuesta["id"].'" name="idEditar" required> 				
								 <input type="submit" value="Actualizar">';
						}

				#ACTUALIZAR ciudad_proveedor
				#------------------------------------
					public function actualizarCiudadProveedorController()
						{echo "<script>alert('Entro Controller Actualizar Producto')</script>";

							if(isset($_POST["nombreEditar"]))
								{
									$datosController = array(  "ciudad_id"=>$_POST["ciudadEditar"],			
																"provedor_id"=>$_POST["proveedorEditar"],				
																"nombre"=>$_POST["nombreEditar"],				
																"id"=>$_POST["idEditar"]);				
									$respuesta = DatosCiudadProveedor::actualizarCiudadProveedorModel($datosController, "ciudad_provedor");
									if($respuesta == "success")
										{	header("location:index.php?action=cambioCPv");	}
									else
										{	echo "error";	}
								}				
						}

				#BORRAR ciudad_proveedor
				#------------------------------------
					public function borrarCiudadProveedorController()
						{
							if(isset($_GET["idBorrar"]))
								{
									$datosController = $_GET["idBorrar"];				
									$respuesta = DatosCiudadProveedor::borrarCiudadProveedorModel($datosController, "ciudad_provedor");
									if($respuesta == "success")
										{	header("location:index.php?action=ciudad_provedor");	}
								}
						}			
			#---------------------------------

		}			
			