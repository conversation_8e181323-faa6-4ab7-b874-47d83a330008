
<?php
ob_start();
class controllerProductos extends Mvc<PERSON>ontroller
{
	#REGISTRO DE PRODUCTOS
	#------------------------------------
	 public function registroProductoController()
		{
		 if(isset($_POST["nombreProductoRegistro"]))
			{
			 $datosController =array('codigo'=>$_POST["codigo"],
									'nombre'=>$_POST["nombreProductoRegistro"],
									'cocina'=>$_POST["cocina"],
									'precio'=>$_POST["precioProductoRegistro"]);
			 //echo "<script>alert('codigo :".$datosController["codigo"]." nombre :".$datosController["nombre"]." precio :".$datosController["precio"]." ')</script>";
				$respuesta = DatosProductos::registroProductoModel($datosController, "productos");
			 if($respuesta == "success")
				{	header("location:producto");	}
			 else
				{	header("location:registroProducto");	}
			}
		}
	#---------------------------------
	#VISTA DE PRODUCTOS
	#------------------------------------
	 public function vistaProductoController()
		{	session_start();
		 $usuario=$_SESSION["tipo_usuario"];

		 	setlocale(LC_MONETARY, 'en_US');
			 $formateador2 = new NumberFormatter( 'es_CO', NumberFormatter::CURRENCY );
			 $formateador2->setAttribute( $formateador2::FRACTION_DIGITS, 0 );
			 $formateador = new NumberFormatter( 'en_US', NumberFormatter::DECIMAL );

		 $respuesta = DatosProductos::vistaProductoModel("productos");
		 foreach($respuesta as $row => $item)
			{
			echo'<tr>
					<td>'.$item["id"].'</td>
					<td>'.$item["codigo"].'</td>
					<td>'.$item["nombre"].'</td>
					<td>'.$item["categoria"].'</td>
					<td align=right>'.$formateador2->format($item["precio"]).'</td>';
				if ($usuario==1) {
					echo '<td><a href="index.php?action=registroReseta&id='.$item["id"].'">Receta</a></td>
					<td><a href="index.php?action=editarProducto&id='.$item["id"].'">Editar</a></td>
					<td><a href="index.php?action=producto&idBorrar='.$item["id"].'">Borrar</a></td>';
				}

			  echo '</tr>';
			}
		}
	#---------------------------------
	#EDITAR PRODUCTOS
	#------------------------------------
	 public function editarProductoController()
		{
			$datosController = $_GET["id"];
			$respuesta = DatosProductos::editarProductoModel($datosController, "productos");

			// Opciones válidas según el ENUM
			$categorias = ['bar', 'cocina', 'asados'];
			// Valor seleccionado actual (si se edita, por ejemplo)
			$categoriaSeleccionada = $respuesta["categoria"];

			echo'
			<label> codigo : </label><input type="text" value="'.$respuesta["codigo"].'" name="codigo" required>
			<label> nombre : </label><input type="text" value="'.$respuesta["nombre"].'" name="nombreEditar" required>
			<label> categoria : </label><select name="categoria" id="categoria" required>
        <option value="">Seleccione una opción</option>';
        foreach ($categorias as $categoria): 
        	if ($categoria == $categoriaSeleccionada) {
        		echo '<option value="'.$categoria.'" selected >'.$categoria.'</option>';
        		//echo '<option value="' . htmlspecialchars($categoria, ENT_QUOTES, 'UTF-8') . '" selected>' . htmlspecialchars($categoria, ENT_QUOTES, 'UTF-8') . '</option>';
        	}else{
        		echo '<option value="'.$categoria.'">'.$categoria.'</option>';
        	}
         endforeach;
    echo '</select>
			<label> precio : </label> <input type="text" value="'.$respuesta["precio"].'" name="precioEditar" required>
			 <input type="hidden" value="'.$respuesta["id"].'" name="idEditar" required>
				 <input type="submit" value="Actualizar">';
		}
	#------------------------------------
	#ACTUALIZAR PRODUCTOS
	#------------------------------------
	 public function actualizarProductoController()
		{
			//echo "<script>alert('Entro Controller Actualizar Producto')</script>";
			if(isset($_POST["nombreEditar"]))
				{
					$datosController = array(
												"codigo"=>$_POST["codigo"],
												"nombre"=>$_POST["nombreEditar"],
												"precio"=>$_POST["precioEditar"],
												"categoria"=>$_POST["categoria"],
												"id"=>$_POST["idEditar"]);
					$respuesta = DatosProductos::actualizarProductoModel($datosController, "productos");
					if($respuesta == "success")
						{	header("location:index.php?action=cambioP");	}
					else
						{	echo "error";	}
				}
		}
	#---------------------------------
	#BORRAR PRODUCTOS
	#------------------------------------ confirm('Eliminar Producto ?')
	 public function borrarProductoController()
		{
			
		 if(isset($_GET["idBorrar"]) )
			{
				echo "<script>confirm('Eliminar Producto ?');</script>";
			 $datosController = $_GET["idBorrar"];
			 $respuesta = DatosProductos::borrarProductoModel($datosController, "productos");
			 if($respuesta == "success")
				{	header("location:index.php?action=producto");	}
			}
		}
	#---------------------------------

}
