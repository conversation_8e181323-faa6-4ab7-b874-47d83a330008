<?php
// Debug específico para pedidos de mesa
require_once "../../models/conexion.php";
require_once "../../models/crudEstadoPedidos.php";
require_once "../../controllers/controllerEstadoPedidos.php";

session_start();
$mesaId = isset($_GET['mesa']) ? $_GET['mesa'] : 2;

echo "<h1>🔍 Debug Pedidos Mesa $mesaId</h1>";

try {
    $db = Conexion::conectar();
    
    echo "<h3>1. 📊 Consulta Directa - Todos los pedidos de la mesa</h3>";
    $stmt = $db->prepare("SELECT * FROM pedidos WHERE mesa_id = ? ORDER BY fecha_pedido DESC");
    $stmt->execute([$mesaId]);
    $pedidosDirectos = $stmt->fetchAll();
    
    echo "<strong>Pedidos encontrados directamente:</strong> " . count($pedidosDirectos) . "<br>";
    if (!empty($pedidosDirectos)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Número</th><th>Estado</th><th>Mesa ID</th><th>Fecha</th></tr>";
        foreach ($pedidosDirectos as $p) {
            echo "<tr>";
            echo "<td>" . $p['id'] . "</td>";
            echo "<td>" . $p['numero_pedido'] . "</td>";
            echo "<td>" . $p['estado'] . "</td>";
            echo "<td>" . $p['mesa_id'] . "</td>";
            echo "<td>" . $p['fecha_pedido'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>2. 🔧 Consulta del Modelo Actual</h3>";
    $controllerEstado = new ControllerEstadoPedidos();
    $pedidosModelo = $controllerEstado->obtenerPedidosMesaController($mesaId);
    
    echo "<strong>Pedidos del modelo:</strong> " . count($pedidosModelo) . "<br>";
    if (!empty($pedidosModelo)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Número</th><th>Estado</th><th>Mesa</th><th>Productos</th><th>Fecha</th></tr>";
        foreach ($pedidosModelo as $p) {
            echo "<tr>";
            echo "<td>" . $p['id'] . "</td>";
            echo "<td>" . $p['numero_pedido'] . "</td>";
            echo "<td>" . $p['estado'] . "</td>";
            echo "<td>" . $p['mesa_numero'] . "</td>";
            echo "<td>" . $p['total_productos'] . "</td>";
            echo "<td>" . $p['fecha_pedido'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>3. 🧪 Consulta Manual del Modelo</h3>";
    $sql = "
        SELECT p.id, p.numero_pedido, p.estado, p.fecha_pedido, p.fecha_envio, 
               p.fecha_entrega, m.numero as mesa_numero, per.nombre as mesero_nombre,
               COUNT(pvm.productos_id) as total_productos
        FROM pedidos p
        LEFT JOIN mesas m ON p.mesa_id = m.id
        LEFT JOIN personas per ON p.mesero_id = per.id
        LEFT JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
        WHERE p.mesa_id = ?
        GROUP BY p.id ORDER BY p.fecha_pedido DESC
    ";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([$mesaId]);
    $pedidosManual = $stmt->fetchAll();
    
    echo "<strong>Consulta manual:</strong> " . count($pedidosManual) . "<br>";
    if (!empty($pedidosManual)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Número</th><th>Estado</th><th>Mesa</th><th>Mesero</th><th>Productos</th><th>Fecha</th></tr>";
        foreach ($pedidosManual as $p) {
            echo "<tr>";
            echo "<td>" . $p['id'] . "</td>";
            echo "<td>" . $p['numero_pedido'] . "</td>";
            echo "<td>" . $p['estado'] . "</td>";
            echo "<td>" . $p['mesa_numero'] . "</td>";
            echo "<td>" . ($p['mesero_nombre'] ?? 'N/A') . "</td>";
            echo "<td>" . $p['total_productos'] . "</td>";
            echo "<td>" . $p['fecha_pedido'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>4. 🔍 Verificar productos de cada pedido</h3>";
    foreach ($pedidosDirectos as $pedido) {
        echo "<h4>Pedido {$pedido['numero_pedido']} (ID: {$pedido['id']})</h4>";
        
        $stmtProductos = $db->prepare("
            SELECT p.nombre, pvm.cantidad 
            FROM producto_vendido_mesa pvm 
            JOIN productos p ON pvm.productos_id = p.id 
            WHERE pvm.pedidos_id = ?
        ");
        $stmtProductos->execute([$pedido['id']]);
        $productos = $stmtProductos->fetchAll();
        
        echo "Productos: " . count($productos) . "<br>";
        if (!empty($productos)) {
            foreach ($productos as $prod) {
                echo "- {$prod['nombre']} (x{$prod['cantidad']})<br>";
            }
        } else {
            echo "<span style='color: red;'>⚠️ Este pedido no tiene productos asociados</span><br>";
        }
        echo "<br>";
    }
    
    echo "<h3>5. 📋 Información de la Mesa</h3>";
    $stmtMesa = $db->prepare("SELECT * FROM mesas WHERE id = ?");
    $stmtMesa->execute([$mesaId]);
    $mesa = $stmtMesa->fetch();
    
    if ($mesa) {
        echo "Mesa encontrada: Número {$mesa['numero']}, Estado: {$mesa['estado']}<br>";
    } else {
        echo "<span style='color: red;'>❌ Mesa no encontrada</span><br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage();
}

echo "<br><br>";
echo "<a href='../../index.php?action=registroPmesa&ida=$mesaId' style='background: #007bff; color: white; padding: 10px; text-decoration: none;'>🔙 Volver a Mesa $mesaId</a>";
echo "<a href='test_ajax.php' style='background: #28a745; color: white; padding: 10px; text-decoration: none; margin-left: 10px;'>🧪 Test AJAX</a>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}

table {
    width: 100%;
    max-width: 800px;
    margin: 10px 0;
}

th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #f0f0f0;
}

h3 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 5px;
}

h4 {
    color: #666;
    margin-top: 15px;
}
</style>
