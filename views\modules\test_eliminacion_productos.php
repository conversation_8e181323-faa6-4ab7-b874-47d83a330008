<?php
// Test de eliminación de productos
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
if (!isset($_SESSION["usuario"])) {
    $_SESSION["usuario"] = 1;
    $_SESSION["tipo_usuario"] = 1;
    $_SESSION["perfil"] = "administrador";
}

require_once "../../models/conexion.php";
require_once "../../controllers/controllerEstadoPedidos.php";
require_once "../../models/crudEstadoPedidos.php";
require_once "../../models/crudPedidoMesaVendido.php";

$mesaId = isset($_GET['mesa']) ? $_GET['mesa'] : 1;

echo "<h1>🧪 Test Eliminación de Productos - Mesa $mesaId</h1>";

try {
    $controller = new ControllerEstadoPedidos();
    $db = Conexion::conectar();
    
    echo "<h3>1. 📋 Productos en la Mesa</h3>";
    
    // Obtener productos de la mesa
    $productos = DatosPedidoMesaVendido::vistaPmesaModel($mesaId, "producto_vendido_mesa");
    
    if (!empty($productos)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Producto</th><th>Cantidad</th><th>Fecha</th><th>Estado Pedido</th><th>Pedido ID</th><th>Acción</th></tr>";
        
        foreach ($productos as $producto) {
            echo "<tr>";
            echo "<td>{$producto['nombrepr']}</td>";
            echo "<td>{$producto['cantidadpvm']}</td>";
            echo "<td>{$producto['fechapvm']}</td>";
            echo "<td>{$producto['estado']}</td>";
            echo "<td>" . (isset($producto['pedido_id']) ? $producto['pedido_id'] : 'N/A') . "</td>";
            echo "<td>";
            
            if ($producto['estado'] == 'borrador') {
                echo "<button onclick='eliminarProducto({$producto['idpr']}, \"{$producto['fechapvm']}\")' style='background: #dc3545; color: white; padding: 5px;'>🗑️ Eliminar</button>";
            } else {
                echo "<span style='color: #6c757d;'>No eliminable ({$producto['estado']})</span>";
            }
            
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "❌ No hay productos en la mesa<br>";
    }
    
    echo "<h3>2. 📊 Estado de Pedidos</h3>";
    $pedidos = $controller->obtenerPedidosMesaController($mesaId);
    
    if (!empty($pedidos)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Número</th><th>Estado</th><th>Productos</th></tr>";
        foreach ($pedidos as $p) {
            echo "<tr>";
            echo "<td>{$p['id']}</td>";
            echo "<td>{$p['numero_pedido']}</td>";
            echo "<td>{$p['estado']}</td>";
            echo "<td>{$p['total_productos']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}

echo "<br><a href='index.php?action=registroPmesa&ida=$mesaId' style='background: #007bff; color: white; padding: 10px; text-decoration: none;'>🔙 Volver a Mesa $mesaId</a>";
echo "<br><a href='test_eliminacion_productos.php?mesa=$mesaId' style='background: #6c757d; color: white; padding: 10px; text-decoration: none; margin: 5px;'>🔄 Actualizar</a>";
?>

<script>
function eliminarProducto(productoId, fechaHora) {
    if (confirm('¿Eliminar producto ' + productoId + '?')) {
        console.log('🗑️ Eliminando producto:', productoId, 'fecha:', fechaHora);
        
        // Simular la eliminación como lo hace el sistema original
        const url = 'index.php?action=registroPmesa&fecha_horaBorrar=' + encodeURIComponent(fechaHora) + '&idBorrar=' + productoId + '&ida=<?=$mesaId?>';
        
        console.log('🔗 URL de eliminación:', url);
        
        // Redirigir a la URL de eliminación
        window.location.href = url;
    }
}

// Función alternativa para eliminar via AJAX (si fuera necesario)
function eliminarProductoAjax(productoId, fechaHora) {
    if (confirm('¿Eliminar producto ' + productoId + ' via AJAX?')) {
        console.log('🗑️ Eliminando via AJAX:', productoId, 'fecha:', fechaHora);
        
        // Crear formulario para simular POST
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '../../controllers/controllerPedidoMesaVendido.php';
        
        const inputProducto = document.createElement('input');
        inputProducto.type = 'hidden';
        inputProducto.name = 'idBorrar';
        inputProducto.value = productoId;
        
        const inputFecha = document.createElement('input');
        inputFecha.type = 'hidden';
        inputFecha.name = 'fecha_horaBorrar';
        inputFecha.value = fechaHora;
        
        form.appendChild(inputProducto);
        form.appendChild(inputFecha);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
