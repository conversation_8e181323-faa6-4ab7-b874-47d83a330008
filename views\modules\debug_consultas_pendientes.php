<?php

session_start();

if(!isset($_SESSION["validar"]) || !$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "models/conexion.php";

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Debug Consultas Pendientes</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
</head>
<body>

<div class="container">
    <h2>🔍 Debug: Consultas de Pedidos Pendientes</h2>
    <hr>

    <div class="panel panel-default">
        <div class="panel-heading">
            <h3 class="panel-title">📊 Análisis de Datos Base</h3>
        </div>
        <div class="panel-body">
            <?php
            // 1. Verificar pedidos con estado 'enviado'
            $stmt = Conexion::conectar()->prepare("SELECT id, numero_pedido, estado, mesa_id FROM pedidos WHERE estado = 'enviado'");
            $stmt->execute();
            $pedidos_enviados = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "<strong>Pedidos con estado 'enviado':</strong> " . count($pedidos_enviados) . "<br>";
            foreach ($pedidos_enviados as $p) {
                echo "- {$p['numero_pedido']} (ID: {$p['id']}, Mesa: {$p['mesa_id']})<br>";
            }
            echo "<br>";

            // 2. Verificar productos del pedido P000004 específicamente
            $stmt = Conexion::conectar()->prepare("
                SELECT p.numero_pedido, pr.nombre, pr.categoria, pvm.cantidad
                FROM pedidos p
                JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
                JOIN productos pr ON pvm.productos_id = pr.id
                WHERE p.numero_pedido = 'P000004'
            ");
            $stmt->execute();
            $productos_p000004 = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "<strong>Productos del pedido P000004:</strong> " . count($productos_p000004) . "<br>";
            foreach ($productos_p000004 as $prod) {
                echo "- {$prod['nombre']} (Categoría: '{$prod['categoria']}') x{$prod['cantidad']}<br>";
            }
            echo "<br>";

            // 3. Verificar todas las categorías únicas en productos
            $stmt = Conexion::conectar()->prepare("SELECT DISTINCT categoria FROM productos WHERE categoria IS NOT NULL AND categoria != '' ORDER BY categoria");
            $stmt->execute();
            $categorias = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "<strong>Todas las categorías de productos:</strong><br>";
            foreach ($categorias as $cat) {
                echo "- '{$cat['categoria']}'<br>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">🍺 Consulta BAR</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                $stmt = Conexion::conectar()->prepare("
                    SELECT DISTINCT
                        p.id as pedido_id,
                        p.numero_pedido,
                        p.fecha_envio,
                        p.mesa_id,
                        m.nombre as mesa_numero,
                        COUNT(pvm.id) as total_productos,
                        GROUP_CONCAT(
                            CONCAT(pr.nombre, ' x', pvm.cantidad)
                            ORDER BY pr.nombre
                            SEPARATOR ', '
                        ) as productos_detalle,
                        SUM(pvm.cantidad * pr.precio) as total_precio
                    FROM pedidos p
                    INNER JOIN mesas m ON p.mesa_id = m.id
                    INNER JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
                    INNER JOIN productos pr ON pvm.productos_id = pr.id
                    WHERE p.estado = 'enviado'
                    AND pr.categoria = 'bar'
                    GROUP BY p.id, p.numero_pedido, p.fecha_envio, p.mesa_id, m.nombre
                    ORDER BY p.fecha_envio ASC
                ");
                $stmt->execute();
                $pedidos_bar = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                echo "<strong>Pedidos encontrados para BAR:</strong> " . count($pedidos_bar) . "<br><br>";
                
                if (count($pedidos_bar) > 0) {
                    echo "<table class='table table-striped'>";
                    echo "<thead><tr><th>Pedido</th><th>Mesa</th><th>Productos</th><th>Total</th></tr></thead>";
                    echo "<tbody>";
                    foreach ($pedidos_bar as $pedido) {
                        echo "<tr>";
                        echo "<td>{$pedido['numero_pedido']}</td>";
                        echo "<td>{$pedido['mesa_numero']}</td>";
                        echo "<td>{$pedido['productos_detalle']}</td>";
                        echo "<td>$" . number_format($pedido['total_precio'], 0) . "</td>";
                        echo "</tr>";
                    }
                    echo "</tbody></table>";
                } else {
                    echo "<div class='alert alert-warning'>No se encontraron pedidos para BAR</div>";
                }
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error en consulta BAR: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-warning">
        <div class="panel-heading">
            <h3 class="panel-title">🍳 Consulta COCINA</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                $stmt = Conexion::conectar()->prepare("
                    SELECT DISTINCT
                        p.id as pedido_id,
                        p.numero_pedido,
                        p.fecha_envio,
                        p.mesa_id,
                        m.nombre as mesa_numero,
                        COUNT(pvm.id) as total_productos,
                        GROUP_CONCAT(
                            CONCAT(pr.nombre, ' x', pvm.cantidad)
                            ORDER BY pr.nombre
                            SEPARATOR ', '
                        ) as productos_detalle,
                        SUM(pvm.cantidad * pr.precio) as total_precio
                    FROM pedidos p
                    INNER JOIN mesas m ON p.mesa_id = m.id
                    INNER JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
                    INNER JOIN productos pr ON pvm.productos_id = pr.id
                    WHERE p.estado = 'enviado'
                    AND pr.categoria = 'cocina'
                    GROUP BY p.id, p.numero_pedido, p.fecha_envio, p.mesa_id, m.nombre
                    ORDER BY p.fecha_envio ASC
                ");
                $stmt->execute();
                $pedidos_cocina = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                echo "<strong>Pedidos encontrados para COCINA:</strong> " . count($pedidos_cocina) . "<br><br>";
                
                if (count($pedidos_cocina) > 0) {
                    echo "<table class='table table-striped'>";
                    echo "<thead><tr><th>Pedido</th><th>Mesa</th><th>Productos</th><th>Total</th></tr></thead>";
                    echo "<tbody>";
                    foreach ($pedidos_cocina as $pedido) {
                        echo "<tr>";
                        echo "<td>{$pedido['numero_pedido']}</td>";
                        echo "<td>{$pedido['mesa_numero']}</td>";
                        echo "<td>{$pedido['productos_detalle']}</td>";
                        echo "<td>$" . number_format($pedido['total_precio'], 0) . "</td>";
                        echo "</tr>";
                    }
                    echo "</tbody></table>";
                } else {
                    echo "<div class='alert alert-warning'>No se encontraron pedidos para COCINA</div>";
                }
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error en consulta COCINA: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-danger">
        <div class="panel-heading">
            <h3 class="panel-title">🥩 Consulta ASADOS</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                $stmt = Conexion::conectar()->prepare("
                    SELECT DISTINCT
                        p.id as pedido_id,
                        p.numero_pedido,
                        p.fecha_envio,
                        p.mesa_id,
                        m.nombre as mesa_numero,
                        COUNT(pvm.id) as total_productos,
                        GROUP_CONCAT(
                            CONCAT(pr.nombre, ' x', pvm.cantidad)
                            ORDER BY pr.nombre
                            SEPARATOR ', '
                        ) as productos_detalle,
                        SUM(pvm.cantidad * pr.precio) as total_precio
                    FROM pedidos p
                    INNER JOIN mesas m ON p.mesa_id = m.id
                    INNER JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
                    INNER JOIN productos pr ON pvm.productos_id = pr.id
                    WHERE p.estado = 'enviado'
                    AND pr.categoria = 'asados'
                    GROUP BY p.id, p.numero_pedido, p.fecha_envio, p.mesa_id, m.nombre
                    ORDER BY p.fecha_envio ASC
                ");
                $stmt->execute();
                $pedidos_asados = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                echo "<strong>Pedidos encontrados para ASADOS:</strong> " . count($pedidos_asados) . "<br><br>";
                
                if (count($pedidos_asados) > 0) {
                    echo "<table class='table table-striped'>";
                    echo "<thead><tr><th>Pedido</th><th>Mesa</th><th>Productos</th><th>Total</th></tr></thead>";
                    echo "<tbody>";
                    foreach ($pedidos_asados as $pedido) {
                        echo "<tr>";
                        echo "<td>{$pedido['numero_pedido']}</td>";
                        echo "<td>{$pedido['mesa_numero']}</td>";
                        echo "<td>{$pedido['productos_detalle']}</td>";
                        echo "<td>$" . number_format($pedido['total_precio'], 0) . "</td>";
                        echo "</tr>";
                    }
                    echo "</tbody></table>";
                } else {
                    echo "<div class='alert alert-warning'>No se encontraron pedidos para ASADOS</div>";
                }
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error en consulta ASADOS: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <hr>
    <div class="row">
        <div class="col-md-3">
            <a href="index.php?action=pedidosBarPendientes" class="btn btn-info btn-block">🍺 Ir a Bar</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=pedidosCocinaPendientes" class="btn btn-warning btn-block">🍳 Ir a Cocina</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=pedidosAsadosPendientes" class="btn btn-danger btn-block">🥩 Ir a Asados</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=debug_pedidos_creados" class="btn btn-success btn-block">🔍 Ver Pedidos</a>
        </div>
    </div>
</div>

</body>
</html>
