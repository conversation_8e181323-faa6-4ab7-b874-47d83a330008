<?php
// Test simple para identificar el problema
echo "<h1>🔧 Test Simple Categorías</h1>";
echo "<p><strong>Identificar exactamente dónde falla</strong></p>";

$pedido_id = 475;

echo "<div style='background-color: #d1ecf1; padding: 15px; border-left: 4px solid #17a2b8; margin-bottom: 20px;'>";
echo "<h4>🧪 Probando Pedido ID: $pedido_id</h4>";
echo "</div>";

// Paso 1: Verificar conexión
echo "<h2>Paso 1: Verificar Conexión</h2>";
try {
    require_once '../../models/conexion.php';
    $conexion = new Conexion();
    $pdo = $conexion->conectar();
    echo "<p>✅ Conexión exitosa</p>";
} catch (Exception $e) {
    echo "<p>❌ Error de conexión: " . $e->getMessage() . "</p>";
    exit;
}

// Paso 2: Verificar que el archivo del componente existe
echo "<h2>Paso 2: Verificar Archivo del Componente</h2>";
$archivo_componente = 'componente_impresion_categorias.php';
if (file_exists($archivo_componente)) {
    echo "<p>✅ Archivo existe: $archivo_componente</p>";
} else {
    echo "<p>❌ Archivo no existe: $archivo_componente</p>";
    exit;
}

// Paso 3: Incluir el archivo
echo "<h2>Paso 3: Incluir Archivo</h2>";
try {
    require_once $archivo_componente;
    echo "<p>✅ Archivo incluido correctamente</p>";
} catch (Exception $e) {
    echo "<p>❌ Error incluyendo archivo: " . $e->getMessage() . "</p>";
    exit;
}

// Paso 4: Verificar que la función existe
echo "<h2>Paso 4: Verificar Función</h2>";
if (function_exists('obtenerCategoriasDelPedidoComponente')) {
    echo "<p>✅ Función existe</p>";
} else {
    echo "<p>❌ Función no existe</p>";
    exit;
}

// Paso 5: Probar consulta SQL directa primero
echo "<h2>Paso 5: Consulta SQL Directa</h2>";
try {
    $sql = "
        SELECT 
            p.id as producto_id,
            p.nombre as producto_nombre,
            p.categoria as categoria_producto,
            pvm.cantidad
        FROM producto_vendido_mesa pvm
        INNER JOIN productos p ON pvm.productos_id = p.id
        WHERE pvm.pedidos_id = :pedido_id
        LIMIT 5
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(":pedido_id", $pedido_id, PDO::PARAM_INT);
    $stmt->execute();
    $productos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>✅ Consulta SQL exitosa</p>";
    echo "<p><strong>Productos encontrados:</strong> " . count($productos) . "</p>";
    
    if (!empty($productos)) {
        echo "<ul>";
        foreach ($productos as $prod) {
            echo "<li>{$prod['producto_nombre']} (Cat: {$prod['categoria_producto']}) x{$prod['cantidad']}</li>";
        }
        echo "</ul>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error en consulta SQL: " . $e->getMessage() . "</p>";
    exit;
}

// Paso 6: Probar la función con timeout
echo "<h2>Paso 6: Probar Función (con timeout)</h2>";

// Configurar timeout
set_time_limit(10); // 10 segundos máximo

try {
    echo "<p>🔄 Llamando a obtenerCategoriasDelPedidoComponente($pedido_id)...</p>";
    
    $inicio = microtime(true);
    $resultado = obtenerCategoriasDelPedidoComponente($pedido_id);
    $tiempo = round((microtime(true) - $inicio) * 1000, 2);
    
    echo "<p>✅ Función ejecutada en {$tiempo}ms</p>";
    
    if (is_array($resultado)) {
        echo "<p><strong>Tipo:</strong> Array</p>";
        echo "<p><strong>Claves:</strong> " . implode(', ', array_keys($resultado)) . "</p>";
        echo "<p><strong>Categorías:</strong> " . count($resultado['categorias'] ?? []) . "</p>";
        
        if (isset($resultado['error'])) {
            echo "<p style='color: red;'><strong>Error en resultado:</strong> {$resultado['error']}</p>";
        }
        
        if (!empty($resultado['categorias'])) {
            echo "<p style='color: green;'>✅ <strong>Categorías encontradas:</strong></p>";
            echo "<ul>";
            foreach ($resultado['categorias'] as $cat => $info) {
                echo "<li><strong>$cat:</strong> {$info['total_productos']} productos</li>";
            }
            echo "</ul>";
        } else {
            echo "<p style='color: orange;'>⚠️ <strong>Array de categorías vacío</strong></p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ <strong>Resultado no es array:</strong> " . gettype($resultado) . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ <strong>Error en función:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Archivo:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Línea:</strong> " . $e->getLine() . "</p>";
} catch (Error $e) {
    echo "<p style='color: red;'>❌ <strong>Error fatal:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Archivo:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Línea:</strong> " . $e->getLine() . "</p>";
}

// Paso 7: Información del sistema
echo "<h2>Paso 7: Información del Sistema</h2>";
echo "<p><strong>PHP Version:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>Memory Limit:</strong> " . ini_get('memory_limit') . "</p>";
echo "<p><strong>Max Execution Time:</strong> " . ini_get('max_execution_time') . "</p>";
echo "<p><strong>Error Reporting:</strong> " . error_reporting() . "</p>";

// Mostrar errores de PHP si los hay
if (function_exists('error_get_last')) {
    $last_error = error_get_last();
    if ($last_error) {
        echo "<h3>🚨 Último Error de PHP:</h3>";
        echo "<pre style='background-color: #f8d7da; padding: 10px; border-radius: 3px;'>";
        print_r($last_error);
        echo "</pre>";
    }
}

echo "<h2>✅ Test Completado</h2>";
echo "<p>Si llegaste hasta aquí, el problema está identificado en los pasos anteriores.</p>";

// Botones de navegación
echo "<p style='margin-top: 30px;'>";
echo "<a href='test_funcion_categorias.php?pedido_id=475' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🔧 Test Completo</a>";
echo "<a href='debug_pedido_especifico.php?pedido=P000475' style='background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🔍 Debug P000475</a>";
echo "<a href='../../index.php?action=registroPmesa&ida=3' style='background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🪑 Mesa 3</a>";
echo "</p>";
?>
