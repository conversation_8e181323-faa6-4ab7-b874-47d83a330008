<?php
session_start();

// Configuración para facturación rápida
ini_set('max_execution_time', 300); // 5 minutos
ini_set('memory_limit', '512M');

try {
    error_log("FACTURACIÓN RÁPIDA INICIADA - Usuario: " . ($_SESSION["usuario"] ?? 'N/A') . " - Timestamp: " . date('Y-m-d H:i:s'));

    // Verificar sesión
    if (!isset($_SESSION["usuario"])) {
        throw new Exception("Sesión no válida");
    }

    // Incluir archivos necesarios - Detectar rutas automáticamente
    if (file_exists("models/conexion.php")) {
        require_once "models/conexion.php";
        require_once "models/crudFacturaAja.php";
        require_once "models/crud.php"; // Para descuento de inventario
    } elseif (file_exists("../../models/conexion.php")) {
        require_once "../../models/conexion.php";
        require_once "../../models/crudFacturaAja.php";
        require_once "../../models/crud.php"; // Para descuento de inventario
    } else {
        die("Error: No se pueden encontrar los archivos de modelo");
    }

    // Validar datos POST
    if (!isset($_POST['productos']) || !isset($_POST['total'])) {
        throw new Exception("Datos incompletos");
    }

    $productos = json_decode($_POST['productos'], true);
    $total = (float)$_POST['total'];
    $efectivo = (float)($_POST['efectivo'] ?? 0);
    $tarjeta = (float)($_POST['tarjeta'] ?? 0);
    $nequi = (float)($_POST['nequi'] ?? 0);
    $daviplata = (float)($_POST['daviplata'] ?? 0);
    $bancolombia = (float)($_POST['bancolombia'] ?? 0);
    $propina = (float)($_POST['propina'] ?? 0);
    $cedula = $_POST['cedula'] ?? 'FACTURA_RAPIDA';

    error_log("FACTURACIÓN RÁPIDA - Productos: " . count($productos) . ", Total: $total");

    // Validar pago
    $totalPagado = $efectivo + $tarjeta + $nequi + $daviplata + $bancolombia;
    
    if ($totalPagado < $total) {
        $faltante = $total - $totalPagado;
        throw new Exception("Pago insuficiente. Falta: $" . number_format($faltante));
    }

    // Conectar a la base de datos
    $db = Conexion::conectar();
    $db->beginTransaction();

    try {
        error_log("FACTURACIÓN RÁPIDA - Iniciando transacción");
        
        // 1. Crear pedido con estado facturado directamente
        $mesero = $_SESSION["usuario"];
        $fecha = date('Y-m-d H:i:s');
        
        $sql = "INSERT INTO pedidos (mesero_id, facturado, cedula_cliente, fecha_pedido, estado) VALUES (?, 'n', ?, ?, 'facturado')";
        $stmt = $db->prepare($sql);
        $stmt->execute([$mesero, $cedula, $fecha]);
        $pedidoId = $db->lastInsertId();
        
        error_log("FACTURACIÓN RÁPIDA - Pedido creado: $pedidoId");
        
        // 2. Insertar productos directamente en pedido_productos_mesa
        $sqlProducto = "INSERT INTO pedido_productos_mesa (productos_id, mesas_id, pedidos_id, cantidad, precio, valor_productos, descuento, codigo_descuento) VALUES (?, 0, ?, ?, ?, ?, 0, '')";
        $stmtProducto = $db->prepare($sqlProducto);

        $totalCalculado = 0;
        foreach ($productos as $producto) {
            $subtotal = $producto['precio'] * $producto['cantidad'];
            $totalCalculado += $subtotal;

            $stmtProducto->execute([
                $producto['id'],
                $pedidoId,
                $producto['cantidad'],
                $producto['precio'],
                $producto['precio'] // valor_productos = precio (sin descuentos en facturación rápida)
            ]);
        }
        
        error_log("FACTURACIÓN RÁPIDA - Productos insertados, Total calculado: $totalCalculado");

        // 2.5. NUEVO: Descontar inventario de suministros
        error_log("FACTURACIÓN RÁPIDA - Iniciando descuento de inventario");
        $listaActualizar = array();
        $j = 0;

        foreach ($productos as $producto) {
            try {
                // Obtener suministros del producto
                $consultaSuministro = Datos::productoListaSuministroModel($producto['id']);
                error_log("INVENTARIO RÁPIDA - Producto {$producto['nombre']} (ID: {$producto['id']}) tiene " . count($consultaSuministro) . " suministros");

                foreach ($consultaSuministro as $suministro) {
                    // Calcular cantidad total a descontar
                    $cantidadTotalS = $suministro["spcantidad"] * $producto['cantidad'];

                    error_log("INVENTARIO RÁPIDA - Suministro ID: {$suministro['sid']}, Cantidad actual: {$suministro['sucantidad']}, A descontar: $cantidadTotalS");

                    // Buscar si el suministro ya está en la lista
                    $encontrado = false;
                    for ($c = 0; $c < $j; $c++) {
                        if ($suministro["sid"] == $listaActualizar[$c]["sid"]) {
                            $listaActualizar[$c]["cantidadTotalS"] -= $cantidadTotalS;
                            $encontrado = true;
                            break;
                        }
                    }

                    // Si no se encontró, agregar nuevo suministro a la lista
                    if (!$encontrado) {
                        $listaActualizar[$j]["sid"] = $suministro["sid"];
                        $listaActualizar[$j]["cantidadTotalS"] = $suministro["sucantidad"] - $cantidadTotalS;
                        $j++;
                    }
                }
            } catch (Exception $e) {
                error_log("INVENTARIO RÁPIDA ERROR - Producto {$producto['nombre']}: " . $e->getMessage());
            }
        }

        // Actualizar cantidades en la tabla sucursal
        for ($c = 0; $c < $j; $c++) {
            try {
                $idSuministro = $listaActualizar[$c]['sid'];
                $cantidad = max(0, $listaActualizar[$c]['cantidadTotalS']); // No permitir cantidades negativas

                $consultaInventario = "UPDATE sucursal SET cantidad = ? WHERE suministro_id = ?";
                $stmtInventario = $db->prepare($consultaInventario);
                $stmtInventario->execute([$cantidad, $idSuministro]);

                error_log("INVENTARIO RÁPIDA ACTUALIZADO - Suministro ID: $idSuministro, Nueva cantidad: $cantidad");
            } catch (Exception $e) {
                error_log("INVENTARIO RÁPIDA ERROR UPDATE - Suministro ID: {$listaActualizar[$c]['sid']}: " . $e->getMessage());
            }
        }

        error_log("FACTURACIÓN RÁPIDA - Inventario actualizado, $j suministros procesados");

        // 3. Crear venta
        $turnoSql = "SELECT id FROM turnos_cajeros WHERE persona_id = ? AND fecha_final = '0000-00-00 00:00:00' ORDER BY id DESC LIMIT 1";
        $turnoStmt = $db->prepare($turnoSql);
        $turnoStmt->execute([$_SESSION["usuario"]]);
        $turno = $turnoStmt->fetchColumn() ?: 1;
        
        $iva = 8;
        $totalFinal = $totalCalculado + $propina;
        $cambio = $totalPagado - $totalFinal;
        
        $ventaSql = "INSERT INTO ventas (pedidos_id, turno_cajero_id, valor, fecha_venta, subtotal, iva, total, efectivo, bancolombia, nequi, daviplata, valortarjeta, cambio, propina, mesa) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0)";
        $ventaStmt = $db->prepare($ventaSql);
        $ventaStmt->execute([
            $pedidoId, $turno, $totalCalculado, $fecha, $totalCalculado, $iva, $totalFinal,
            $efectivo, $bancolombia, $nequi, $daviplata, $tarjeta, $cambio, $propina
        ]);
        
        $facturaId = $db->lastInsertId();
        error_log("FACTURACIÓN RÁPIDA - Venta creada: $facturaId");
        
        // 4. Marcar pedido como facturado
        $updatePedido = "UPDATE pedidos SET facturado = 's', fecha_entrega = NOW() WHERE id = ?";
        $updateStmt = $db->prepare($updatePedido);
        $updateStmt->execute([$pedidoId]);
        
        // 5. Configurar variables de sesión para PDF
        $_SESSION["pedidos_id"] = $pedidoId;
        $_SESSION["turno_cajero_id"] = $turno;
        $_SESSION["subtotal"] = $totalCalculado;
        $_SESSION["total"] = $totalFinal;
        $_SESSION['idFactura'] = $facturaId;
        $_SESSION['propina'] = $propina;
        $_SESSION['mesaFactura'] = 'FACTURA_RAPIDA';
        $_SESSION["efectivo"] = $efectivo;
        $_SESSION["tarjeta"] = $tarjeta;
        $_SESSION["bancolombia"] = $bancolombia;
        $_SESSION["nequi"] = $nequi;
        $_SESSION["daviplata"] = $daviplata;
        $_SESSION["cambio"] = $cambio;
        
        // 6. Configurar productos para PDF
        $listaProductos = array();
        $i = 0;
        foreach ($productos as $producto) {
            $totalProducto = $producto['precio'] * $producto['cantidad'];
            
            $listaProductos[$i] = array(
                'codigo' => $producto['id'],
                'nombre' => $producto['nombre'],
                'precio' => $producto['precio'],
                'cantidad' => $producto['cantidad'],
                'descuento' => 0,
                'total' => $totalProducto
            );
            $i++;
        }
        $_SESSION["productos"] = $listaProductos;
        
        error_log("FACTURACIÓN RÁPIDA - Productos configurados para PDF: " . count($listaProductos));

        $db->commit();
        
        error_log("FACTURACIÓN RÁPIDA COMPLETADA - Factura: $facturaId");
        
        // Respuesta de éxito
        echo '<script>alert("✅ Facturación rápida completada exitosamente\\n\\nFactura: ' . $facturaId . '");window.open("pdf","_blank");</script>';
        echo "success_rapida_factura_$facturaId";
        
    } catch (Exception $e) {
        $db->rollBack();
        error_log("FACTURACIÓN RÁPIDA ERROR: " . $e->getMessage());
        echo "<script>alert('Error en facturación rápida: " . addslashes($e->getMessage()) . "')</script>";
        echo "error_rapida: " . $e->getMessage();
    }
    
} catch (Exception $e) {
    error_log("FACTURACIÓN RÁPIDA ERROR GENERAL: " . $e->getMessage());
    echo "<script>alert('Error general: " . addslashes($e->getMessage()) . "')</script>";
    echo "error_general: " . $e->getMessage();
}
?>
