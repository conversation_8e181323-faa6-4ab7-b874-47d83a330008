<?php 
ob_start();
class controller<PERSON>unt<PERSON><PERSON><PERSON><PERSON> extends Mvc<PERSON><PERSON>roller
 {
	#REGISTRO DE PROVEEDOR
	#------------------------------------
	 public function registroPuntoVentaController()
		{
			if(isset($_POST["nombrePuntoVenta"]))
			 {	
				$datosController =array('nombre'=>$_POST["nombrePuntoVenta"],
										'direccion'=>$_POST["direccionPuntoVenta"],
										'telefono'=>$_POST["telefonosPuntoVenta"]);
				echo "<script>alert('Entro Controller ".$datosController." no')</script>";	
				$respuesta = DatosPuntoVenta::registroPuntoVentaModel($datosController, "punto");
				if($respuesta == "success")
					{	header("location:puntoVenta");	}
				else
					{	header("location:index.php");	}
			 }
		}
	#-------------------------------------
	#VISTA DE PROVEEDOR
	#------------------------------------
	 public function vistaPuntoVentaController()
		{
			$respuesta = DatosPuntoVenta::vistaPuntoVentaModel("punto");
			foreach($respuesta as $row => $item)
			 {
				echo'<tr>						
						<td>'.$item["nombre"].'</td>												
						<td>'.$item["telefono"].'</td>						
						<td>'.$item["direccion"].'</td>						
						<td><a href="index.php?action=editarPuntoVenta&id='.$item["id"].'">Editar</a></td>
						<td><a href="index.php?action=puntoVenta&idBorrar='.$item["id"].'">Borrar</a></td>
					</tr>';
			 }
		}
	#--------------------------------------------

	#EDITAR PROVEEDOR
	#------------------------------------
	 public function editarPuntoVentaController()
		{
			$datosController = $_GET["id"];
			$respuesta = DatosPuntoVenta::editarPuntoVentaModel($datosController, "punto");
			echo'<table>		
					 <tr>
						<td align="right"><label> Nombre : </label></td>
						<td><input type="text" value="'.$respuesta["nombre"].'" name="nombrePuntoVenta" required></td>
					 </tr>	 	
					 <tr>
						<td align="right"><label> Sucursal telefono : </label></td>
						<td><input type="text" value="'.$respuesta["telefono"].'" name="telefonoPuntoVenta" required></td>
					 </tr>
					 <tr>
						<td align="right"><label> Sucursal Dirección : </label></td>
						<td><input type="text" value="'.$respuesta["direccion"].'" name="direccionPuntoVenta" required></td>
					 </tr>		
					</table><br>
			
			 <input type="hidden" value="'.$respuesta["id"].'" name="idPuntoVentaEditar" required> 				
				 <input type="submit" value="Actualizar">';
		}
	#--------------------------------------------
	#ACTUALIZAR PROVEEDOR
	#------------------------------------
	 public function actualizarPuntoVentaController()
		{ echo "<script>alert('Entro Controller Actualizar Poveedor')</script>";
			if(isset($_POST["nombrePuntoVenta"]))
			 {
				$datosController = array(  "nombre"=>$_POST["nombrePuntoVenta"],			
											"telefono"=>$_POST["telefonoPuntoVenta"],						
											"direccion"=>$_POST["direccionPuntoVenta"],				
															
											"id"=>$_POST["idPuntoVentaEditar"]);				
				$respuesta = DatosPuntoVenta::actualizarPuntoVentaModel($datosController, "punto");
				if($respuesta == "success")
					{	header("location:index.php?action=cambioPV");	}
							else
					{	echo "error";	}
			 }				
		}
	#--------------------------------------------

	#BORRAR PROVEEDOR
	#------------------------------------
	 public function borrarPuntoVentaController()
		{
			if(isset($_GET["idBorrar"]))
			 {
				$datosController = $_GET["idBorrar"];				
				$respuesta = DatosPuntoVenta::borrarPuntoVentaModel($datosController, "punto");
				if($respuesta == "success")
					{	header("location:index.php?action=puntoVenta");	}
			 }
		}			
   #---------------------------------

 }			
	