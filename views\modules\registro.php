<?php

	$registro = new ControllerUsuario();
	$roles=$registro->listaRolesController();
	//$registro -> registroUsuarioController();
	if(isset($_GET["action"]))
		{	if($_GET["action"] == "ok")
			{	echo "Registro Exitoso";	}		}
?>
<h1>REGISTRO DE USUARIO</h1>
<form method="post">
<input type="hidden" placeholder="idciudad" name="ciudad" value="47001" >
 <table>
 	<thead>
		<tr > <td colspan="2" ></td> </tr>
	</thead>
	<tr>
		<td align="right"><label>Roles :</label></td>
		<td>
			<?php
			 # %%%%%%%%%%%%%%%%%%% Roles  %%%%%%%%%%%%%%%%%%%%%%%%%
				if($roles=="error")
					{	echo "debe registrar el Roll"; }
				else{
						echo "";
						$result='<select name="roles"  id="roles" autofocus="autofocus">';
						$result.=' <option value="-1">Seleccione una Rol</option>';
						foreach ($roles as $row => $ite)
					 	 {
					 	 	if ($_SESSION["tipo_usuario"]==1)
					 	 	{
					 	 		$result.=' <option value="'.$ite["id"].'">'.$ite["nombre"].'</option>';
					 	 	}
					 	 	else
					 	 	 {
					 	 	 	if ($ite["id"]==11)
					 	 	 	 {		$result.=' <option value="'.$ite["id"].'">'.$ite["nombre"].'</option>'; 	 }
					 	 	 }

					 	 }
						 $result.='</select>';
						 echo $result." <br>";
					 	}

			 # %%%%%%%%%%%%%%%%%%%%%%%%%%%%  End Roles  %%%%%%%%%%%%%%%%%%%%%%%%%%%
			?>
		</td>
	</tr>
	<tr>
		<td align="right"><label> Cedula : </label></td>
		<td><input type="text" placeholder="Cedula" name="cedulaRegistro" required></td>
	</tr>
	<tr>
		<td align="right"><label> Nombres : </label></td>
		<td><input type="text" placeholder="Nombres" name="nombresRegistro" required></td>
	</tr>
	<tr>
		<td align="right"><label> Apellidos: </label></td>
		<td><input type="text" placeholder="Apellidos" name="apellidosRegistro" required></td>
	</tr>
	<tr>
		<td align="right"><label> Fecha de Nacimiento : </label></td>
		<td><input type="date" placeholder="fechaNacimiento" max="2018-12-31" name="fechanacimientoRegistro"></td>
	</tr>
	<tr>
		<td align="right"><label> Dirección actuamente  : </label></td>
		<td><input type="text" placeholder="direccion" name="direccionRegistro" required></td>
	</tr>
	<tr>
		<td align="right"><label>Telefono/Whatsapp</label></td>
		<td><input type="text" placeholder="Numero-telefo" name="telefonoRegistro" required></td>
	</tr>
	<tr>
		<td align="right"><label> email : </label></td>
		<td><input type="text" placeholder="email" name="emailRegistro" ></td>
	</tr>
	<tr>
		<td align="right"><label> Nombre Usuario :</label></td>
		<td><input type="text" placeholder="Usuario" name="usuarioRegistr" ></td>
	</tr>
	<tr>
		<td align="right"><label> Contraseña :</label></td>
		<td><input type="password" placeholder="Contraseña" name="passwordRegistr" ></td>
	</tr>
	<thead>
		<tr > <td colspan="2" ></td> </tr>
	</thead>

 </table>

	<?php
		/*#	%%%%%%%%%%%%%%%%%%%%%%%%%  ciudad  %%%%%%%%%%%%%%%%
			if($ciudad=="error")
				{	echo "debe registrar el ciudad"; }
			else{
					echo "";
					$resultt='<select name="ciudad"  id="ciudad">';
					$resultt.=' <option value="-1">Seleccione una ciudad</option>';
					foreach ($ciudad as $row => $item)
					 	{	$resultt.=' <option value="'.$item["id"].'">'.$item["nombre"].'</option>';	}
					 $resultt.='</select>';
					 echo $resultt." <br> ";
			}
		# %%%%%%%%%%%%%%%%%%%%  End ciudad  %%%%%%%%%%%%%%%%%%%%%%%%%%%
			<!--div>
		<input type="radio"name="tipoUsuario" value="1"> <label> Admin &nbsp; &nbsp; &nbsp; </label>
		<input type="radio"name="tipoUsuario" value="2"> <label> Cajero &nbsp; &nbsp; &nbsp; </label>
		</div>
		<br-->
		*/

 	?>
	<input type="hidden"  name="activoRegistro"  value="s" required><br>
	<input type="hidden" placeholder="pregunta" value="ee" name="preguntaRegistro" >
	<input type="hidden" placeholder="respuesta" value="ee" name="respuestaRegistro">
	<!--
		<div>
			<input type="radio"name="activoUsuario" value="s"> <label> Activo &nbsp; &nbsp; &nbsp; </label>
			<input type="radio"name="activoUsuario" value="n"> <label> Desactivado &nbsp; &nbsp; &nbsp; </label>
		</div>
		<br>
		<label> Pregunta Sercreta: </label>
		<br>

		<label> Respuesta : </label>
		<br>
	-->
	<input type="submit" value="Enviar">

</form>
</div>
<?php

//$registro = new controllerUsuario();
$registro -> registroUsuarioController();
//$roles=$registro->listaRolesController();
?>
<!--
	id : auto no null
	tipo usuario : no null
	nombres : no null
	apellidos : no null
	cedula : no null
	celular : no null
	usuario : null
	pass : null

-->