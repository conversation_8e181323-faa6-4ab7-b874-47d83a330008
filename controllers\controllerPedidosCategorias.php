<?php

require_once __DIR__ . "/../models/crudPedidosCategorias.php";

class ControllerPedidosCategorias {
    
    /*=============================================
    MOSTRAR PEDIDOS PENDIENTES POR CATEGORÍA
    =============================================*/
    public function mostrarPedidosPendientesController($categoria) {
        $respuesta = DatosPedidosCategorias::obtenerPedidosPendientesModel($categoria);
        return $respuesta;
    }
    
    /*=============================================
    MOSTRAR PEDIDOS ENTREGADOS DEL DÍA POR CATEGORÍA
    =============================================*/
    public function mostrarPedidosEntregadosDiaController($categoria) {
        $respuesta = DatosPedidosCategorias::obtenerPedidosEntregadosDiaModel($categoria);
        return $respuesta;
    }
    
    /*=============================================
    MARCAR PEDIDO COMO ENTREGADO
    =============================================*/
    public function marcarPedidoEntregadoController() {
        if (isset($_POST["pedido_id"]) && isset($_SESSION["usuario"])) {
            $pedido_id = $_POST["pedido_id"];
            $usuario_id = $_SESSION["usuario"];
            
            $respuesta = DatosPedidosCategorias::marcarPedidoEntregadoModel($pedido_id, $usuario_id);
            
            if ($respuesta == "success") {
                echo '<script>
                    if(window.history.replaceState) {
                        window.history.replaceState(null, null, window.location.href);
                    }
                    window.location = "' . $_SERVER['REQUEST_URI'] . '";
                </script>';
            } else {
                echo '<script>
                    alert("Error al marcar el pedido como entregado");
                    if(window.history.replaceState) {
                        window.history.replaceState(null, null, window.location.href);
                    }
                </script>';
            }
        }
    }
    
    /*=============================================
    OBTENER PRODUCTOS DE UN PEDIDO POR CATEGORÍA
    =============================================*/
    public function obtenerProductosPedidoCategoriaController($pedido_id, $categoria) {
        $respuesta = DatosPedidosCategorias::obtenerProductosPedidoCategoriaModel($pedido_id, $categoria);
        return $respuesta;
    }
    
    /*=============================================
    OBTENER ESTADÍSTICAS DEL DÍA
    =============================================*/
    public function obtenerEstadisticasDiaController($categoria) {
        $respuesta = DatosPedidosCategorias::obtenerEstadisticasDiaModel($categoria);
        return $respuesta;
    }
    
    /*=============================================
    VISTA DE PEDIDOS PENDIENTES
    =============================================*/
    public function vistaPedidosPendientesController($categoria) {
        $pedidos = $this->mostrarPedidosPendientesController($categoria);
        $estadisticas = $this->obtenerEstadisticasDiaController($categoria);
        
        // Configuración por categoría
        $config = [
            'cocina' => [
                'titulo' => 'Cocina',
                'icono' => '🍳',
                'color' => '#28a745'
            ],
            'bar' => [
                'titulo' => 'Bar',
                'icono' => '🍺',
                'color' => '#17a2b8'
            ],
            'asados' => [
                'titulo' => 'Asados',
                'icono' => '🥩',
                'color' => '#dc3545'
            ]
        ];
        
        $cat_config = $config[$categoria] ?? $config['cocina'];
        
        echo "<div class='container-fluid'>";
        echo "<div class='row'>";
        echo "<div class='col-md-12'>";
        
        // Encabezado
        echo "<div class='page-header'>";
        echo "<h1>{$cat_config['icono']} {$cat_config['titulo']} - Pedidos Pendientes</h1>";
        echo "</div>";
        
        // Estadísticas
        echo "<div class='row' style='margin-bottom: 20px;'>";
        echo "<div class='col-md-4'>";
        echo "<div class='panel panel-primary'>";
        echo "<div class='panel-heading'><h4>📋 Pendientes</h4></div>";
        echo "<div class='panel-body text-center'>";
        echo "<h2 style='color: {$cat_config['color']};'>{$estadisticas['pendientes']}</h2>";
        echo "</div></div></div>";
        
        echo "<div class='col-md-4'>";
        echo "<div class='panel panel-success'>";
        echo "<div class='panel-heading'><h4>✅ Entregados Hoy</h4></div>";
        echo "<div class='panel-body text-center'>";
        echo "<h2 style='color: #28a745;'>{$estadisticas['entregados_hoy']}</h2>";
        echo "</div></div></div>";
        
        echo "<div class='col-md-4'>";
        echo "<div class='panel panel-info'>";
        echo "<div class='panel-heading'><h4>💰 Ventas Hoy</h4></div>";
        echo "<div class='panel-body text-center'>";
        echo "<h2 style='color: #17a2b8;'>$" . number_format($estadisticas['total_ventas_hoy'], 0) . "</h2>";
        echo "</div></div></div>";
        echo "</div>";
        
        // Botón de actualizar
        echo "<div class='row' style='margin-bottom: 15px;'>";
        echo "<div class='col-md-12'>";
        echo "<button onclick='location.reload()' class='btn btn-info'>";
        echo "<i class='glyphicon glyphicon-refresh'></i> Actualizar";
        echo "</button>";
        echo "<a href='pedidos{$categoria}Entregados' class='btn btn-success' style='margin-left: 10px;'>";
        echo "<i class='glyphicon glyphicon-list'></i> Ver Entregados del Día";
        echo "</a>";
        echo "</div></div>";
        
        if (empty($pedidos)) {
            echo "<div class='alert alert-info text-center'>";
            echo "<h3>{$cat_config['icono']} ¡Excelente!</h3>";
            echo "<p>No hay pedidos pendientes en {$cat_config['titulo']}.</p>";
            echo "</div>";
        } else {
            echo "<div class='panel panel-default'>";
            echo "<div class='panel-heading'>";
            echo "<h3 class='panel-title'>📋 Pedidos Pendientes - " . count($pedidos) . " pedido(s)</h3>";
            echo "</div>";
            echo "<div class='panel-body'>";
            
            foreach ($pedidos as $pedido) {
                $tiempo_espera = $this->calcularTiempoEspera($pedido['fecha_envio']);
                $clase_urgencia = $this->obtenerClaseUrgencia($tiempo_espera);
                
                echo "<div class='panel panel-{$clase_urgencia}' style='margin-bottom: 15px;'>";
                echo "<div class='panel-heading'>";
                echo "<div class='row'>";
                echo "<div class='col-md-6'>";
                echo "<h4 style='margin: 0;'>";
                echo "<strong>{$pedido['numero_pedido']}</strong> - Mesa {$pedido['mesa_numero']}";
                echo "</h4>";
                echo "</div>";
                echo "<div class='col-md-3 text-center'>";
                echo "<span class='label label-{$clase_urgencia}' style='font-size: 12px;'>";
                echo "⏱️ {$tiempo_espera}";
                echo "</span>";
                echo "</div>";
                echo "<div class='col-md-3 text-right'>";
                echo "<button type='button' class='btn btn-success btn-sm' onclick='marcarEntregado({$pedido['pedido_id']}, this)'>";
                echo "<i class='glyphicon glyphicon-ok'></i> Marcar Entregado";
                echo "</button>";
                echo "</div>";
                echo "</div>";
                echo "</div>";
                
                echo "<div class='panel-body'>";
                echo "<div class='row'>";
                echo "<div class='col-md-8'>";
                echo "<h5><strong>Productos:</strong></h5>";
                echo "<p style='font-size: 14px;'>{$pedido['productos_detalle']}</p>";
                echo "</div>";
                echo "<div class='col-md-4 text-right'>";
                echo "<h5><strong>Total:</strong> $" . number_format($pedido['total_precio'], 0) . "</h5>";
                echo "<small>Enviado: " . date('H:i', strtotime($pedido['fecha_envio'])) . "</small>";
                echo "</div>";
                echo "</div>";
                echo "</div>";
                echo "</div>";
            }
            
            echo "</div>";
            echo "</div>";
        }
        
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    
    /*=============================================
    VISTA DE PEDIDOS ENTREGADOS DEL DÍA
    =============================================*/
    public function vistaPedidosEntregadosDiaController($categoria) {
        $pedidos = $this->mostrarPedidosEntregadosDiaController($categoria);
        $estadisticas = $this->obtenerEstadisticasDiaController($categoria);
        
        // Configuración por categoría
        $config = [
            'cocina' => [
                'titulo' => 'Cocina',
                'icono' => '🍳',
                'color' => '#28a745'
            ],
            'bar' => [
                'titulo' => 'Bar',
                'icono' => '🍺',
                'color' => '#17a2b8'
            ],
            'asados' => [
                'titulo' => 'Asados',
                'icono' => '🥩',
                'color' => '#dc3545'
            ]
        ];
        
        $cat_config = $config[$categoria] ?? $config['cocina'];
        
        echo "<div class='container-fluid'>";
        echo "<div class='row'>";
        echo "<div class='col-md-12'>";
        
        // Encabezado
        echo "<div class='page-header'>";
        echo "<h1>{$cat_config['icono']} {$cat_config['titulo']} - Pedidos Entregados Hoy</h1>";
        echo "<p class='text-muted'>Fecha: " . date('d/m/Y') . "</p>";
        echo "</div>";
        
        // Estadísticas
        echo "<div class='row' style='margin-bottom: 20px;'>";
        echo "<div class='col-md-4'>";
        echo "<div class='panel panel-success'>";
        echo "<div class='panel-heading'><h4>✅ Total Entregados</h4></div>";
        echo "<div class='panel-body text-center'>";
        echo "<h2 style='color: #28a745;'>{$estadisticas['entregados_hoy']}</h2>";
        echo "</div></div></div>";
        
        echo "<div class='col-md-4'>";
        echo "<div class='panel panel-info'>";
        echo "<div class='panel-heading'><h4>💰 Total Ventas</h4></div>";
        echo "<div class='panel-body text-center'>";
        echo "<h2 style='color: #17a2b8;'>$" . number_format($estadisticas['total_ventas_hoy'], 0) . "</h2>";
        echo "</div></div></div>";
        
        echo "<div class='col-md-4'>";
        echo "<div class='panel panel-warning'>";
        echo "<div class='panel-heading'><h4>📋 Pendientes</h4></div>";
        echo "<div class='panel-body text-center'>";
        echo "<h2 style='color: #f0ad4e;'>{$estadisticas['pendientes']}</h2>";
        echo "</div></div></div>";
        echo "</div>";
        
        // Botones de navegación
        echo "<div class='row' style='margin-bottom: 15px;'>";
        echo "<div class='col-md-12'>";
        echo "<a href='pedidos{$categoria}Pendientes' class='btn btn-primary'>";
        echo "<i class='glyphicon glyphicon-arrow-left'></i> Volver a Pendientes";
        echo "</a>";
        echo "<button onclick='location.reload()' class='btn btn-info' style='margin-left: 10px;'>";
        echo "<i class='glyphicon glyphicon-refresh'></i> Actualizar";
        echo "</button>";
        echo "</div></div>";
        
        if (empty($pedidos)) {
            echo "<div class='alert alert-info text-center'>";
            echo "<h3>{$cat_config['icono']} Sin entregas hoy</h3>";
            echo "<p>No se han entregado pedidos de {$cat_config['titulo']} el día de hoy.</p>";
            echo "</div>";
        } else {
            echo "<div class='panel panel-default'>";
            echo "<div class='panel-heading'>";
            echo "<h3 class='panel-title'>✅ Pedidos Entregados Hoy - " . count($pedidos) . " pedido(s)</h3>";
            echo "</div>";
            echo "<div class='panel-body'>";
            
            foreach ($pedidos as $pedido) {
                echo "<div class='panel panel-success' style='margin-bottom: 15px;'>";
                echo "<div class='panel-heading'>";
                echo "<div class='row'>";
                echo "<div class='col-md-6'>";
                echo "<h4 style='margin: 0;'>";
                echo "<strong>{$pedido['numero_pedido']}</strong> - Mesa {$pedido['mesa_numero']}";
                echo "</h4>";
                echo "</div>";
                echo "<div class='col-md-6 text-right'>";
                echo "<span class='label label-success' style='font-size: 12px;'>";
                echo "✅ Entregado: " . date('H:i', strtotime($pedido['fecha_entrega']));
                echo "</span>";
                echo "</div>";
                echo "</div>";
                echo "</div>";
                
                echo "<div class='panel-body'>";
                echo "<div class='row'>";
                echo "<div class='col-md-8'>";
                echo "<h5><strong>Productos:</strong></h5>";
                echo "<p style='font-size: 14px;'>{$pedido['productos_detalle']}</p>";
                if ($pedido['usuario_entrega']) {
                    echo "<small><strong>Entregado por:</strong> {$pedido['usuario_entrega']}</small>";
                }
                echo "</div>";
                echo "<div class='col-md-4 text-right'>";
                echo "<h5><strong>Total:</strong> $" . number_format($pedido['total_precio'], 0) . "</h5>";
                echo "<small><strong>Enviado:</strong> " . date('H:i', strtotime($pedido['fecha_envio'])) . "</small><br>";
                echo "<small><strong>Entregado:</strong> " . date('H:i', strtotime($pedido['fecha_entrega'])) . "</small>";
                echo "</div>";
                echo "</div>";
                echo "</div>";
                echo "</div>";
            }
            
            echo "</div>";
            echo "</div>";
        }
        
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    
    /*=============================================
    CALCULAR TIEMPO DE ESPERA
    =============================================*/
    private function calcularTiempoEspera($fecha_envio) {
        $ahora = new DateTime();
        $envio = new DateTime($fecha_envio);
        $diferencia = $ahora->diff($envio);
        
        if ($diferencia->h > 0) {
            return $diferencia->h . "h " . $diferencia->i . "m";
        } else {
            return $diferencia->i . " min";
        }
    }
    
    /*=============================================
    OBTENER CLASE DE URGENCIA SEGÚN TIEMPO
    =============================================*/
    private function obtenerClaseUrgencia($tiempo_espera) {
        $minutos = 0;
        
        if (strpos($tiempo_espera, 'h') !== false) {
            $partes = explode('h', $tiempo_espera);
            $horas = intval($partes[0]);
            $minutos = $horas * 60;
            if (isset($partes[1])) {
                $minutos += intval(str_replace(['m', ' '], '', $partes[1]));
            }
        } else {
            $minutos = intval(str_replace([' min', 'm'], '', $tiempo_espera));
        }
        
        if ($minutos > 30) {
            return 'danger';  // Rojo - Muy urgente
        } elseif ($minutos > 15) {
            return 'warning'; // Amarillo - Urgente
        } else {
            return 'info';    // Azul - Normal
        }
    }
}

?>
