<?php

session_start();

if(!isset($_SESSION["validar"]) || !$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "models/conexion.php";

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Debug Consulta Exacta</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
</head>
<body>

<div class="container">
    <h2>🔍 Debug: Consulta Exacta del Modelo</h2>
    <hr>
    
    <div class="panel panel-success">
        <div class="panel-heading">
            <h3 class="panel-title">✅ Datos Confirmados</h3>
        </div>
        <div class="panel-body">
            <p>✅ <strong>Los productos SÍ están en producto_vendido_mesa</strong></p>
            <p>✅ <strong>Pedido P000004:</strong> 7 productos con pedidos_id = 4</p>
            <p>✅ <strong>Pedido P006046:</strong> 4 productos con pedidos_id = 8</p>
            <p>❌ <strong>Pero las consultas del modelo devuelven 0 resultados</strong></p>
        </div>
    </div>
    
    <div class="panel panel-danger">
        <div class="panel-heading">
            <h3 class="panel-title">🔍 Replicar consulta EXACTA del modelo crudPedidosCategorias.php</h3>
        </div>
        <div class="panel-body">
            <?php
            echo "<h4>Consulta para BAR (línea 15-35 del modelo):</h4>";
            
            // Esta es la consulta EXACTA del modelo crudPedidosCategorias.php líneas 15-35
            $stmt = Conexion::conectar()->prepare("
                SELECT DISTINCT
                    p.id as pedido_id,
                    p.numero_pedido,
                    p.fecha_envio,
                    p.mesa_id,
                    m.nombre as mesa_numero,
                    COUNT(pvm.productos_id) as total_productos,
                    GROUP_CONCAT(
                        CONCAT(pr.nombre, ' x', pvm.cantidad)
                        ORDER BY pr.nombre
                        SEPARATOR ', '
                    ) as productos_detalle,
                    SUM(pvm.cantidad * pr.precio) as total_precio
                FROM pedidos p
                INNER JOIN mesas m ON p.mesa_id = m.id
                INNER JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
                INNER JOIN productos pr ON pvm.productos_id = pr.id
                WHERE p.estado = 'enviado'
                AND pr.categoria = :categoria
                GROUP BY p.id, p.numero_pedido, p.fecha_envio, p.mesa_id, m.nombre
                ORDER BY p.fecha_envio ASC
            ");
            
            $stmt->bindParam(':categoria', $categoria_bar);
            $categoria_bar = 'bar';
            $stmt->execute();
            $resultado_bar = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<p><strong>Resultado para BAR:</strong> " . count($resultado_bar) . " pedidos</p>";
            
            if (count($resultado_bar) > 0) {
                echo "<table class='table table-striped'>";
                echo "<thead><tr><th>Pedido</th><th>Mesa</th><th>Productos</th><th>Total</th></tr></thead>";
                echo "<tbody>";
                foreach ($resultado_bar as $pedido) {
                    echo "<tr>";
                    echo "<td>{$pedido['numero_pedido']}</td>";
                    echo "<td>{$pedido['mesa_numero']}</td>";
                    echo "<td>{$pedido['productos_detalle']}</td>";
                    echo "<td>$" . number_format($pedido['total_precio'], 0) . "</td>";
                    echo "</tr>";
                }
                echo "</tbody></table>";
            } else {
                echo "<div class='alert alert-danger'>❌ La consulta exacta del modelo devuelve 0 resultados para BAR</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-warning">
        <div class="panel-heading">
            <h3 class="panel-title">🔍 Debug paso a paso de la consulta BAR</h3>
        </div>
        <div class="panel-body">
            <?php
            echo "<h4>Paso 1: Verificar productos con categoría 'bar'</h4>";
            $stmt = Conexion::conectar()->prepare("
                SELECT pvm.pedidos_id, pr.id, pr.nombre, pr.categoria
                FROM producto_vendido_mesa pvm
                INNER JOIN productos pr ON pvm.productos_id = pr.id
                WHERE pvm.pedidos_id IN (4, 8)
                AND pr.categoria = 'bar'
            ");
            $stmt->execute();
            $productos_bar = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<p><strong>Productos con categoría 'bar' en pedidos 4 y 8:</strong> " . count($productos_bar) . "</p>";
            foreach ($productos_bar as $prod) {
                echo "<p>- Pedido {$prod['pedidos_id']}: {$prod['nombre']} (ID: {$prod['id']}, Categoría: '{$prod['categoria']}')</p>";
            }
            
            echo "<h4>Paso 2: Verificar JOIN completo sin GROUP BY</h4>";
            $stmt = Conexion::conectar()->prepare("
                SELECT p.id, p.numero_pedido, p.estado, m.nombre as mesa_nombre, pr.nombre as producto_nombre, pr.categoria
                FROM pedidos p
                INNER JOIN mesas m ON p.mesa_id = m.id
                INNER JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
                INNER JOIN productos pr ON pvm.productos_id = pr.id
                WHERE p.estado = 'enviado'
                AND pr.categoria = 'bar'
                AND p.id IN (4, 8)
            ");
            $stmt->execute();
            $join_completo = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<p><strong>JOIN completo sin GROUP BY:</strong> " . count($join_completo) . " registros</p>";
            foreach ($join_completo as $reg) {
                echo "<p>- {$reg['numero_pedido']} | Mesa: {$reg['mesa_nombre']} | Producto: {$reg['producto_nombre']} | Categoría: {$reg['categoria']}</p>";
            }
            
            echo "<h4>Paso 3: Verificar si el problema está en el GROUP BY</h4>";
            $stmt = Conexion::conectar()->prepare("
                SELECT p.id, p.numero_pedido, m.nombre as mesa_nombre, COUNT(*) as total
                FROM pedidos p
                INNER JOIN mesas m ON p.mesa_id = m.id
                INNER JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
                INNER JOIN productos pr ON pvm.productos_id = pr.id
                WHERE p.estado = 'enviado'
                AND pr.categoria = 'bar'
                AND p.id IN (4, 8)
                GROUP BY p.id, p.numero_pedido, m.nombre
            ");
            $stmt->execute();
            $con_group_by = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<p><strong>Con GROUP BY:</strong> " . count($con_group_by) . " pedidos</p>";
            foreach ($con_group_by as $pedido) {
                echo "<p>- {$pedido['numero_pedido']} | Mesa: {$pedido['mesa_nombre']} | Total productos: {$pedido['total']}</p>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">🔍 Verificar categorías exactas de productos</h3>
        </div>
        <div class="panel-body">
            <?php
            echo "<h4>Categorías de productos en pedidos 4 y 8:</h4>";
            $stmt = Conexion::conectar()->prepare("
                SELECT pvm.pedidos_id, pr.id, pr.nombre, pr.categoria, 
                       CHAR_LENGTH(pr.categoria) as longitud_categoria,
                       ASCII(SUBSTRING(pr.categoria, 1, 1)) as primer_char,
                       ASCII(SUBSTRING(pr.categoria, -1, 1)) as ultimo_char
                FROM producto_vendido_mesa pvm
                INNER JOIN productos pr ON pvm.productos_id = pr.id
                WHERE pvm.pedidos_id IN (4, 8)
                ORDER BY pvm.pedidos_id, pr.categoria
            ");
            $stmt->execute();
            $categorias_detalle = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<table class='table table-condensed'>";
            echo "<thead><tr><th>Pedido</th><th>Producto</th><th>Categoría</th><th>Longitud</th><th>Primer Char</th><th>Último Char</th></tr></thead>";
            echo "<tbody>";
            foreach ($categorias_detalle as $cat) {
                $clase = ($cat['categoria'] == 'bar') ? 'success' : (($cat['categoria'] == 'cocina') ? 'warning' : 'danger');
                echo "<tr class='{$clase}'>";
                echo "<td>{$cat['pedidos_id']}</td>";
                echo "<td>{$cat['nombre']}</td>";
                echo "<td>'{$cat['categoria']}'</td>";
                echo "<td>{$cat['longitud_categoria']}</td>";
                echo "<td>{$cat['primer_char']}</td>";
                echo "<td>{$cat['ultimo_char']}</td>";
                echo "</tr>";
            }
            echo "</tbody></table>";
            
            echo "<p><strong>Nota:</strong> ASCII 98 = 'b', ASCII 114 = 'r' para 'bar'. ASCII 99 = 'c' para 'cocina'.</p>";
            ?>
        </div>
    </div>
    
    <hr>
    <div class="row">
        <div class="col-md-4">
            <a href="index.php?action=debug_producto_vendido_mesa" class="btn btn-primary btn-block">🔙 Debug Anterior</a>
        </div>
        <div class="col-md-4">
            <a href="index.php?action=pedidosBarPendientes" class="btn btn-info btn-block">🍺 Ir a Bar</a>
        </div>
        <div class="col-md-4">
            <a href="index.php?action=pedidosCocinaPendientes" class="btn btn-warning btn-block">🍳 Ir a Cocina</a>
        </div>
    </div>
</div>

</body>
</html>
