<?php
session_start();

// Simular sesión de administrador para pruebas
if (!isset($_SESSION["usuario"])) {
    $_SESSION["usuario"] = 1;
    $_SESSION["tipo_usuario"] = 1; // Administrador
    $_SESSION["mesa"] = 47; // Mesa de prueba
}

echo "<h2>🧪 Test de Botones de Cancelación en Mesa Real</h2>";
echo "<p><strong>Mesa:</strong> " . $_SESSION["mesa"] . "</p>";
echo "<p><strong>Usuario:</strong> " . $_SESSION["usuario"] . " (Tipo: " . $_SESSION["tipo_usuario"] . ")</p>";

// Incluir archivos necesarios
require_once "../../models/conexion.php";
require_once "../../controllers/controllerEstadoPedidos.php";
require_once "../../models/crudEstadoPedidos.php";
require_once "../../controllers/controllerPedidoMesaVendido.php";
require_once "../../models/crudPedidoMesaVendido.php";

echo "<h3>1. 🔍 Verificar Datos que Ve la Mesa Real</h3>";

try {
    // Simular lo que hace registroPmesa.php
    $vistaPmesa = new controllerPedidoMesaVendido();
    $mesa = $_SESSION["mesa"];
    
    echo "<h4>1.1 Productos en la tabla principal (vistaPmesaModel)</h4>";
    $productos = DatosPedidoMesaVendido::vistaPmesaModel($mesa, "producto_vendido_mesa");
    
    if (!empty($productos)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Producto</th><th>Cantidad</th><th>Precio</th><th>Estado</th><th>Pedido ID</th><th>Número Pedido</th><th>Botón Cancelar</th></tr>";
        
        foreach ($productos as $item) {
            echo "<tr>";
            echo "<td>" . $item['nombrepr'] . "</td>";
            echo "<td>" . $item['cantidadpvm'] . "</td>";
            echo "<td>$" . number_format($item['preciopr']) . "</td>";
            echo "<td>" . $item['estado'] . "</td>";
            echo "<td>" . (isset($item['pedido_id']) ? $item['pedido_id'] : 'NO DISPONIBLE') . "</td>";
            echo "<td>" . (isset($item['numero_pedido']) ? $item['numero_pedido'] : 'NO DISPONIBLE') . "</td>";
            echo "<td>";
            
            if (isset($item['pedido_id'])) {
                echo "<button onclick='testCancelarProducto(" . $item['idpr'] . ", " . $item['pedido_id'] . ")' style='background: #ffc107; color: black; padding: 5px;'>🗑️ Cancelar</button>";
            } else {
                echo "<span style='color: red;'>❌ Sin pedido_id</span>";
            }
            
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "❌ No hay productos en la mesa<br>";
    }

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}

echo "<h4>1.2 Pedidos de la mesa (controllerEstadoPedidos)</h4>";

try {
    $controllerEstado = new ControllerEstadoPedidos();
    
    // Pedido borrador
    $pedidoBorrador = $controllerEstado->obtenerPedidoBorradorController($_SESSION["mesa"]);
    if ($pedidoBorrador) {
        echo "✅ <strong>Pedido Borrador:</strong> " . $pedidoBorrador['numero_pedido'] . " (ID: " . $pedidoBorrador['id'] . ")<br>";
        echo "<button onclick='testCancelarPedido(" . $pedidoBorrador['id'] . ")' style='background: #dc3545; color: white; padding: 5px; margin: 5px;'>🗑️ Cancelar Borrador</button><br>";
    } else {
        echo "❌ No hay pedido borrador<br>";
    }
    
    // Todos los pedidos
    $pedidosMesa = $controllerEstado->obtenerPedidosMesaController($_SESSION["mesa"]);
    echo "<br><strong>Todos los pedidos:</strong> " . count($pedidosMesa) . "<br>";
    
    if (!empty($pedidosMesa)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Número</th><th>Estado</th><th>Productos</th><th>Fecha</th><th>Botón Cancelar</th></tr>";
        
        foreach ($pedidosMesa as $pedido) {
            if ($pedido['estado'] != 'cancelado' && $pedido['estado'] != 'facturado') {
                echo "<tr>";
                echo "<td>" . $pedido['id'] . "</td>";
                echo "<td>" . $pedido['numero_pedido'] . "</td>";
                echo "<td>" . $pedido['estado'] . "</td>";
                echo "<td>" . $pedido['total_productos'] . "</td>";
                echo "<td>" . $pedido['fecha_pedido'] . "</td>";
                echo "<td>";
                
                if ($pedido['estado'] == 'borrador' || $pedido['estado'] == 'enviado') {
                    echo "<button onclick='testCancelarPedido(" . $pedido['id'] . ")' style='background: #dc3545; color: white; padding: 5px;'>🗑️ Cancelar</button>";
                } else {
                    echo "<span style='color: #6c757d;'>No cancelable</span>";
                }
                
                echo "</td>";
                echo "</tr>";
            }
        }
        echo "</table>";
    }

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}

?>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
function testCancelarPedido(pedidoId) {
    if (confirm('🧪 TEST: ¿Cancelar pedido ' + pedidoId + '?')) {
        $.post("ajaxEstadoPedidos.php", {
            cancelar_pedido: true,
            pedido_id: pedidoId
        }, function(data) {
            try {
                var resultado = JSON.parse(data);
                alert('✅ Resultado: ' + resultado.status + '\n📝 Mensaje: ' + resultado.message);
                if (resultado.status === "success") {
                    location.reload();
                }
            } catch (e) {
                alert('❌ Error parseando respuesta: ' + data);
                console.error(e, data);
            }
        }).fail(function(xhr, status, error) {
            alert('❌ Error AJAX: ' + error);
        });
    }
}

function testCancelarProducto(productoId, pedidoId) {
    if (confirm('🧪 TEST: ¿Cancelar producto ' + productoId + ' del pedido ' + pedidoId + '?')) {
        $.post("ajaxPedidoVentas.php", {
            cancelarProducto: productoId,
            pedido_id: pedidoId
        }, function(data) {
            if (data.trim() === 'success') {
                alert('✅ Producto cancelado correctamente');
                location.reload();
            } else {
                alert('❌ Error al cancelar producto: ' + data);
            }
        }).fail(function(xhr, status, error) {
            alert('❌ Error AJAX: ' + error);
        });
    }
}
</script>

<div style="margin: 20px 0; padding: 15px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px;">
    <h4>🔧 Enlaces de Prueba</h4>
    <a href="../../index.php?action=registroPmesa&ida=<?=$_SESSION['mesa']?>" style="background: #007bff; color: white; padding: 10px; margin: 5px; text-decoration: none; display: inline-block;">📋 Ir a Mesa Real <?=$_SESSION['mesa']?></a>
    <button onclick="location.reload()" style="background: #28a745; color: white; padding: 10px; margin: 5px;">🔄 Recargar Test</button>
</div>

<div style="margin: 20px 0; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;">
    <h4>📝 Verificaciones</h4>
    <ul>
        <li><strong>✅ pedido_id disponible:</strong> Los productos deben mostrar el pedido_id</li>
        <li><strong>✅ Botones de cancelar:</strong> Deben aparecer para pedidos borrador y enviado</li>
        <li><strong>✅ Permisos:</strong> Solo administradores pueden cancelar</li>
        <li><strong>✅ AJAX funcionando:</strong> Las peticiones deben devolver respuestas correctas</li>
    </ul>
</div>
