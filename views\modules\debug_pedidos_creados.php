<?php

session_start();

if(!isset($_SESSION["validar"]) || !$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "models/conexion.php";

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Debug Pedidos Creados</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
</head>
<body>

<div class="container">
    <h2>🔍 Debug: Pedidos Creados Recientemente</h2>
    <hr>
    
    <div class="panel panel-default">
        <div class="panel-heading">
            <h3 class="panel-title">📋 Últimos 10 Pedidos Creados</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                $stmt = Conexion::conectar()->prepare("
                    SELECT p.*, m.nombre as mesa_nombre,
                           COUNT(pvm.id) as total_productos
                    FROM pedidos p
                    LEFT JOIN mesas m ON p.mesa_id = m.id
                    LEFT JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
                    GROUP BY p.id
                    ORDER BY p.id DESC
                    LIMIT 10
                ");
                $stmt->execute();
                $pedidos = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (count($pedidos) > 0) {
                    echo "<table class='table table-striped'>";
                    echo "<thead>";
                    echo "<tr>";
                    echo "<th>ID</th>";
                    echo "<th>Número</th>";
                    echo "<th>Mesa</th>";
                    echo "<th>Estado</th>";
                    echo "<th>Productos</th>";
                    echo "<th>Fecha Creación</th>";
                    echo "<th>Mesero ID</th>";
                    echo "<th>Facturado</th>";
                    echo "</tr>";
                    echo "</thead>";
                    echo "<tbody>";
                    
                    foreach ($pedidos as $pedido) {
                        $clase = '';
                        switch ($pedido['estado']) {
                            case 'borrador': $clase = 'info'; break;
                            case 'enviado': $clase = 'warning'; break;
                            case 'entregado': $clase = 'success'; break;
                            case 'facturado': $clase = 'primary'; break;
                            default: $clase = ''; break;
                        }
                        
                        echo "<tr class='{$clase}'>";
                        echo "<td>{$pedido['id']}</td>";
                        echo "<td><strong>{$pedido['numero_pedido']}</strong></td>";
                        echo "<td>{$pedido['mesa_nombre']}</td>";
                        echo "<td><span class='label label-default'>{$pedido['estado']}</span></td>";
                        echo "<td>{$pedido['total_productos']}</td>";
                        echo "<td>" . date('d/m H:i:s', strtotime($pedido['fecha_pedido'])) . "</td>";
                        echo "<td>{$pedido['mesero_id']}</td>";
                        echo "<td>{$pedido['facturado']}</td>";
                        echo "</tr>";
                    }
                    
                    echo "</tbody>";
                    echo "</table>";
                } else {
                    echo "<div class='alert alert-warning'>No se encontraron pedidos.</div>";
                }
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">🍽️ Productos en producto_vendido_mesa (Últimos 20)</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                $stmt = Conexion::conectar()->prepare("
                    SELECT pvm.*, pr.nombre as producto_nombre, p.numero_pedido, p.estado as pedido_estado
                    FROM producto_vendido_mesa pvm
                    LEFT JOIN productos pr ON pvm.productos_id = pr.id
                    LEFT JOIN pedidos p ON pvm.pedidos_id = p.id
                    ORDER BY pvm.id DESC
                    LIMIT 20
                ");
                $stmt->execute();
                $productos = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (count($productos) > 0) {
                    echo "<table class='table table-striped table-condensed'>";
                    echo "<thead>";
                    echo "<tr>";
                    echo "<th>ID</th>";
                    echo "<th>Producto</th>";
                    echo "<th>Pedido</th>";
                    echo "<th>Estado Pedido</th>";
                    echo "<th>Mesa ID</th>";
                    echo "<th>Cantidad</th>";
                    echo "<th>Fecha</th>";
                    echo "</tr>";
                    echo "</thead>";
                    echo "<tbody>";
                    
                    foreach ($productos as $producto) {
                        echo "<tr>";
                        echo "<td>{$producto['id']}</td>";
                        echo "<td>{$producto['producto_nombre']}</td>";
                        echo "<td>{$producto['numero_pedido']}</td>";
                        echo "<td><span class='label label-info'>{$producto['pedido_estado']}</span></td>";
                        echo "<td>{$producto['mesas_id']}</td>";
                        echo "<td>{$producto['cantidad']}</td>";
                        echo "<td>" . date('d/m H:i', strtotime($producto['fecha_hora'])) . "</td>";
                        echo "</tr>";
                    }
                    
                    echo "</tbody>";
                    echo "</table>";
                } else {
                    echo "<div class='alert alert-warning'>No se encontraron productos vendidos.</div>";
                }
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <hr>
    <div class="row">
        <div class="col-md-4">
            <a href="index.php?action=crear_pedido_prueba" class="btn btn-success btn-block">🧪 Crear Nuevo Pedido</a>
        </div>
        <div class="col-md-4">
            <a href="index.php?action=cambiar_estado_pedido" class="btn btn-warning btn-block">🔧 Cambiar Estados</a>
        </div>
        <div class="col-md-4">
            <a href="index.php?action=pedidosCocinaPendientes" class="btn btn-info btn-block">🍳 Ver Cocina</a>
        </div>
    </div>
</div>

</body>
</html>
