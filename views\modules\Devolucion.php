<?php
	if(isset($_GET["action"]))
		{	if($_GET["action"] == "okPm")
			{echo "Registro Exitoso";	}
		}
?>
<script type="text/javascript">
	// ------CANCELA TODO LOS PRODUCTOS PEDIDO EN LA MESA
		function cancelarPedido() 
			{	
				if (confirm('confirme si elimina el pedido?')) 
					{		setTimeout('location.href="views/modules/ajaxCancelarPedido.php"',500);		}			
			//alert("Entro en el else de placa");
				/*$("#destino").load("views/modules/ajaxPermiso.php", function()
					{  	alert("recibidos los datos por ajax Monte");    		 }); */		
			} 
	// ------CANCELA TODO LOS PRODUCTOS PEDIDO EN LA MESA fin 	
		function calcular()
			{
				var efectivo = document.devolucion.cantidadM.value;
				var total = document.devolucion.valorU.value;								
				var devolucion = efectivo*total;
				/*alert ('El cambio: ' + calculo);*/
				document.devolucion.total.value = devolucion;
			}
	// ------FACTURAR--------------
		function buscarfactura() 		
			{	
				//alert("Buscar factura funtion"); 
				var factura = document.devolucion.factura.value;
				if (confirm('confirme Facturar Buscar?')) 
									{		//setTimeout('location.href="views/modules/ajaxFactura.php"',500);	
												$("#destino1").load("views/modules/ajaxDevolucion.php", {factura: factura}, function(){
				        						 //alert("recibidos los datos por ajax efectivo"); 		 
				     						});	
									}
			} 
	// ------FACTURAR FIN//		".$_SESSION["mesa"]."   <div id="destino1" name="destino1" ></div><label for=""><b>factura</b></label><input type="text" placeholder="No factura" class="mayuscula"  name="factura" id="factura" size="38"   required><br>
</script>	
	<div class="row">
	    <div class="col-md-12">	  
	      	<form name="devolucion" method="post" >		
				<h2> Devolución </h2>
				<?php
					$factura = new controllerdevolucion();
					$factura -> buscarFacturaController();
					$factura -> parcialDevolucionController();
					//$factura -> totalDevolucionController();				
				?>
			</form>
	    </div>
	</div>
<div></div>
	<div class="row">
	    <div class="col-md-12">	  
	      	<form  method="post" >		
				<?php
					$devolucion = new controllerdevolucion();
					$devolucion -> buscarDevolucionController();
					$devolucion -> eliminarDevolucionController();					
				?>
			</form>
	    </div>
	</div>
  	