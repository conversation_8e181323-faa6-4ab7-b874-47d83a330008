<?php

session_start();

if(!isset($_SESSION["validar"]) || !$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "models/conexion.php";

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Test: Facturación y Estados de Pedidos</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
</head>
<body>

<div class="container">
    <h2>✅ Test: Facturación y Estados de Pedidos</h2>
    <hr>
    
    <div class="alert alert-success">
        <h4>🎉 ¡Cambios Implementados!</h4>
        <p>Se han implementado los cambios solicitados para el manejo de estados de pedidos en la facturación.</p>
    </div>
    
    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">🔧 Cambios Implementados</h3>
        </div>
        <div class="panel-body">
            <div class="row">
                <div class="col-md-6">
                    <h5>✅ 1. Estado de Pedidos al Facturar:</h5>
                    <ul>
                        <li><strong>Antes:</strong> Solo se marcaba facturado = 's'</li>
                        <li><strong>Ahora:</strong> Se actualiza estado = 'facturado'</li>
                        <li><strong>Consulta:</strong> UPDATE pedidos SET estado = 'facturado'</li>
                        <li><strong>Fecha:</strong> Se actualiza fecha_entrega = NOW()</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h5>✅ 2. Exclusión de Pedidos Facturados:</h5>
                    <ul>
                        <li><strong>Antes:</strong> Solo excluía estado = 'borrador'</li>
                        <li><strong>Ahora:</strong> Excluye 'facturado' y 'cancelado'</li>
                        <li><strong>Consulta:</strong> WHERE estado NOT IN ('facturado', 'cancelado')</li>
                        <li><strong>Resultado:</strong> Mesas limpias después de facturar</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <div class="panel panel-primary">
        <div class="panel-heading">
            <h3 class="panel-title">📊 Estado Actual del Sistema</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                echo "<h5>🪑 Mesas con Productos (Solo Activos):</h5>";
                $stmt_mesas = Conexion::conectar()->prepare("
                    SELECT 
                        m.id,
                        m.nombre,
                        m.estado as mesa_estado,
                        COUNT(pvm.productos_id) as productos_activos,
                        GROUP_CONCAT(DISTINCT p.estado) as estados_pedidos
                    FROM mesas m
                    LEFT JOIN producto_vendido_mesa pvm ON m.id = pvm.mesas_id
                    LEFT JOIN pedidos p ON pvm.pedidos_id = p.id
                    WHERE p.estado NOT IN ('facturado', 'cancelado') OR p.estado IS NULL
                    GROUP BY m.id, m.nombre, m.estado
                    HAVING productos_activos > 0
                    ORDER BY productos_activos DESC
                ");
                $stmt_mesas->execute();
                $mesas_activas = $stmt_mesas->fetchAll(PDO::FETCH_ASSOC);
                
                if (count($mesas_activas) > 0) {
                    echo "<table class='table table-striped'>";
                    echo "<thead>";
                    echo "<tr>";
                    echo "<th>Mesa</th>";
                    echo "<th>Estado Mesa</th>";
                    echo "<th>Productos Activos</th>";
                    echo "<th>Estados Pedidos</th>";
                    echo "<th>Acciones</th>";
                    echo "</tr>";
                    echo "</thead>";
                    echo "<tbody>";
                    
                    foreach ($mesas_activas as $mesa) {
                        echo "<tr>";
                        echo "<td><strong>{$mesa['nombre']}</strong></td>";
                        echo "<td><span class='label label-info'>{$mesa['mesa_estado']}</span></td>";
                        echo "<td><span class='badge badge-warning'>{$mesa['productos_activos']}</span></td>";
                        echo "<td><small>{$mesa['estados_pedidos']}</small></td>";
                        echo "<td>";
                        echo "<a href='index.php?action=registroPmesa&ida={$mesa['id']}' class='btn btn-xs btn-primary'>Ver</a> ";
                        echo "<a href='index.php?action=registrarDetalleFactura&ida={$mesa['id']}' class='btn btn-xs btn-success'>Facturar</a>";
                        echo "</td>";
                        echo "</tr>";
                    }
                    
                    echo "</tbody></table>";
                } else {
                    echo "<div class='alert alert-success'>";
                    echo "<h6>✅ ¡Perfecto! No hay mesas con productos activos</h6>";
                    echo "<p>Esto significa que todas las mesas han sido facturadas o no tienen pedidos pendientes.</p>";
                    echo "</div>";
                }
                
                echo "<h5>📋 Pedidos por Estado:</h5>";
                $stmt_estados = Conexion::conectar()->prepare("
                    SELECT 
                        estado,
                        COUNT(*) as cantidad,
                        COUNT(DISTINCT mesa_id) as mesas_afectadas
                    FROM pedidos 
                    WHERE fecha_pedido >= DATE_SUB(NOW(), INTERVAL 1 DAY)
                    GROUP BY estado
                    ORDER BY cantidad DESC
                ");
                $stmt_estados->execute();
                $estados = $stmt_estados->fetchAll(PDO::FETCH_ASSOC);
                
                if (count($estados) > 0) {
                    echo "<table class='table table-condensed'>";
                    echo "<thead>";
                    echo "<tr>";
                    echo "<th>Estado</th>";
                    echo "<th>Cantidad Pedidos</th>";
                    echo "<th>Mesas Afectadas</th>";
                    echo "<th>Descripción</th>";
                    echo "</tr>";
                    echo "</thead>";
                    echo "<tbody>";
                    
                    foreach ($estados as $estado) {
                        $clase = '';
                        $descripcion = '';
                        switch ($estado['estado']) {
                            case 'borrador':
                                $clase = 'info';
                                $descripcion = 'Pedidos en proceso de creación';
                                break;
                            case 'enviado':
                                $clase = 'warning';
                                $descripcion = 'Pedidos enviados a cocina';
                                break;
                            case 'entregado':
                                $clase = 'primary';
                                $descripcion = 'Pedidos entregados, listos para facturar';
                                break;
                            case 'facturado':
                                $clase = 'success';
                                $descripcion = 'Pedidos facturados (no aparecen en mesas)';
                                break;
                            case 'cancelado':
                                $clase = 'danger';
                                $descripcion = 'Pedidos cancelados';
                                break;
                        }
                        
                        echo "<tr class='{$clase}'>";
                        echo "<td><strong>{$estado['estado']}</strong></td>";
                        echo "<td>{$estado['cantidad']}</td>";
                        echo "<td>{$estado['mesas_afectadas']}</td>";
                        echo "<td><small>{$descripcion}</small></td>";
                        echo "</tr>";
                    }
                    
                    echo "</tbody></table>";
                } else {
                    echo "<div class='alert alert-info'>No hay pedidos en las últimas 24 horas</div>";
                }
                
                echo "<h5>📈 Últimas Facturaciones:</h5>";
                $stmt_facturados = Conexion::conectar()->prepare("
                    SELECT 
                        p.id,
                        p.numero_pedido,
                        p.mesa_id,
                        m.nombre as mesa_nombre,
                        p.estado,
                        p.fecha_entrega,
                        v.total,
                        v.fecha_venta
                    FROM pedidos p
                    LEFT JOIN mesas m ON p.mesa_id = m.id
                    LEFT JOIN ventas v ON p.id = v.pedidos_id
                    WHERE p.estado = 'facturado'
                    ORDER BY p.fecha_entrega DESC
                    LIMIT 10
                ");
                $stmt_facturados->execute();
                $facturados = $stmt_facturados->fetchAll(PDO::FETCH_ASSOC);
                
                if (count($facturados) > 0) {
                    echo "<table class='table table-condensed'>";
                    echo "<thead>";
                    echo "<tr>";
                    echo "<th>Pedido</th>";
                    echo "<th>Mesa</th>";
                    echo "<th>Estado</th>";
                    echo "<th>Total</th>";
                    echo "<th>Fecha Facturación</th>";
                    echo "</tr>";
                    echo "</thead>";
                    echo "<tbody>";
                    
                    foreach ($facturados as $facturado) {
                        echo "<tr class='success'>";
                        echo "<td>{$facturado['numero_pedido']}</td>";
                        echo "<td>{$facturado['mesa_nombre']}</td>";
                        echo "<td><span class='label label-success'>{$facturado['estado']}</span></td>";
                        echo "<td>$" . number_format($facturado['total'], 0) . "</td>";
                        echo "<td><small>{$facturado['fecha_entrega']}</small></td>";
                        echo "</tr>";
                    }
                    
                    echo "</tbody></table>";
                } else {
                    echo "<div class='alert alert-info'>No hay pedidos facturados recientemente</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-warning">
        <div class="panel-heading">
            <h3 class="panel-title">🧪 Cómo Probar los Cambios</h3>
        </div>
        <div class="panel-body">
            <h5>📝 Pasos para verificar:</h5>
            <ol>
                <li><strong>Crear un pedido en una mesa</strong>
                    <ul>
                        <li>Ir a una mesa vacía</li>
                        <li>Agregar algunos productos</li>
                        <li>Verificar que aparecen en la mesa</li>
                    </ul>
                </li>
                <li><strong>Facturar la mesa</strong>
                    <ul>
                        <li>Ir a "Facturar" en la mesa</li>
                        <li>Completar el proceso de facturación</li>
                        <li>Verificar que no hay errores</li>
                    </ul>
                </li>
                <li><strong>Verificar que la mesa queda limpia</strong>
                    <ul>
                        <li>Volver a la mesa</li>
                        <li>Verificar que no aparecen productos</li>
                        <li>Confirmar que el pedido cambió a estado "facturado"</li>
                    </ul>
                </li>
                <li><strong>Verificar en esta página</strong>
                    <ul>
                        <li>Refrescar esta página</li>
                        <li>Ver que la mesa no aparece en "Mesas con Productos Activos"</li>
                        <li>Ver que el pedido aparece en "Últimas Facturaciones"</li>
                    </ul>
                </li>
            </ol>
            
            <div class="alert alert-success">
                <h6>✅ Resultado Esperado:</h6>
                <p>Después de facturar, la mesa debe quedar completamente limpia y no mostrar productos, mientras que el pedido debe aparecer como "facturado" en el historial.</p>
            </div>
        </div>
    </div>
    
    <hr>
    <div class="row">
        <div class="col-md-3">
            <a href="index.php?action=mesa" class="btn btn-primary btn-block">🪑 Ver Mesas</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=registroPmesa&ida=5" class="btn btn-warning btn-block">🧪 Test Mesa 5</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=pantallaCocina" class="btn btn-info btn-block">👨‍🍳 Pantalla Cocina</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=diagnostico" class="btn btn-success btn-block">📊 Diagnóstico</a>
        </div>
    </div>
</div>

</body>
</html>
