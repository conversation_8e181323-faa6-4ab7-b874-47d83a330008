<?php
ob_start();

$ajax=new controllerVistaVentas();
$respuesta=$ajax->vistaVentasController($_SESSION['fecha1'],$_SESSION['fecha2']);
$html='';
if ($respuesta>0) {
	$html.='<table cellspacing="0" cellpadding="0"  border="1">	
		  <tr>
		  	<td style="text-align: center;" colspan="6"><h3><b>Ventas realizadas desde '.substr($_SESSION['fecha1'], 0,10).' hasta '.substr($_SESSION['fecha2'], 0,10).'</b></h3> </td>
		  </tr>
		  <tr>
				<td><b>Nombre Producto</b></td>
				<td><b>Cantidad Vendida</b></td>
				<td><b>Precio de venta</b></td>
				<td><b>Total vendido</b></td>
				<td><b>Costo Unidad</b></td>
				<td><b>Total Costo</b></td>
		  </tr>';
		  $venta_total=0;
          $costo_compra_total=0;
          $utilidad=0;
	foreach ($respuesta as $row => $item){
		$costosp=$ajax->CostoProductoController($item["idp"]);
		 $costototal=$costosp["Valor_Compra_Producto"]*$item["cantp"];
		 $venta_total+=$item["Subtotal_por_producto"];
		 $costo_compra_total+=$costototal;
		$html.='<tr>
					<td>'.$item["nombre"].'</td>						
					<td>'.$item["cantp"].'</td>						
					<td>'.$item["precio"].'</td>						
					<td>'.$item["Subtotal_por_producto"].'</td>	
					<td style="text-align: right;">'.round($costosp["Valor_Compra_Producto"],1).'</td>						
					<td style="text-align: right;">'.round($costototal,1).'</td>						
				</tr>';				 		
 	}
 		setlocale(LC_MONETARY, 'en_US');
		$utilidad=	$venta_total-$costo_compra_total;
		$utilidad=money_format('%(#1n', $utilidad);
 		$html.='	<tr> 
						<td></td>
						<td><b></b></td>
						<td><h3><b>Total</b></h3></td>
						<td><h4><b>'.money_format('%(#1n', $venta_total).'</b></h4></td>
						<td><b></b></td>
						<td><h4><b>'.money_format('%(#1n', $costo_compra_total).'</b></h4></td>
					</tr>
					<tr> 
						<td></td>
						<td><b></b></td>
						<td><h3><b>Utilidad</b></h3></td>
						<td><h4><b>'.$utilidad.'</b></h4></td>
						<td><b></b></td>
						<td><b></b></td>
					</tr> 				
 				</table>';
 		date_default_timezone_set("America/Bogota");
		$fecha_exit=strftime("%Y-%m-%d %H:%M:%S");
		$nombre=$fecha_exit."";
		$comilla='"';

		header('Content-type: application/vnd.ms-excel');
		header("Content-Disposition: attachment; filename=".$comilla."Reporte_de_ventas--".$nombre.".XLS".$comilla.";");
		header("Pragma: no-cache");
		header("Expires: 0");

 		/*header("Content-type: application/vnd.ms-excel");
			header("Content-Disposition: attachment; filename=".$comilla."X2003".$nombre.".XLS".$comilla.";");*/
			echo $html;
}else{
	//echo '<h1>No se encontrarón resultados</h1>';
}


ob_flush();
?>