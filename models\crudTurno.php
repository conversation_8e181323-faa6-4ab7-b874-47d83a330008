<?php
#EXTENSIÓN DE CLASES: Los objetos pueden ser extendidos, y pueden heredar propiedades y métodos. Para definir una clase como extensión, debo definir una clase padre, y se utiliza dentro de una clase hija.
require_once "conexion.php";
//require_once "controllerVistaVenta.php";
class DatosTurno extends Conexion
 {
	#BUSCAR TURNO ABIERTO
	#-------------------------------------
	 public static function buscarTurno($table)
	 	{
	 		$consulta = "SELECT id, persona_id, fecha_inicio, fecha_final,  cantidad_inicio, cantidad_final FROM $table WHERE cantidad_final=0 AND fecha_final='0000-00-00 00:00:00'";
	 		$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->execute();
			$r = $stmt->fetchAll();
			$c = $stmt->rowCount();
			if ($c >0)
			 {	return $r;	 }
			else
			 { 	echo "<script>alert('no hay turno activo ')</script>";
			 	return 0;
			 }

			$stmt->close();
 		}
 	#-----------------------------------------------------------------
		#Pagos con tarjeta debito y crédito ene el turno
		#-------------------------------------------------------------
		 public static function consultaPagosConTarjetaTurnoModel($datosModel, $tipoTarjeta)
			{
				$stmt = Conexion::conectar()->prepare("SELECT sum(valortarjeta) as datafono, SUM(efectivo) as efectivo, SUM(nequi) as nequi, SUM(daviplata) as daviplata, SUM(bancolombia) as bancolombia FROM `ventas` WHERE turno_cajero_id=:turno_id  ");
				$stmt->bindParam(":turno_id", $datosModel, PDO::PARAM_INT);
				$stmt->execute();
				$c = $stmt->rowCount();
				if ($c>0)
				 { return $stmt->fetch(); }
				else { return 0; }

				$stmt->close();
			}
	#---------------------------
	#ULTIMO TURNO
	#-------------------------------------
	 public static function ultimoTurno($datosModel)
	 	{
	 		//echo "<script>alert('hhhhhhh usuario ".$_SESSION["usuario"]." ')</script>";
	 		$consulta = "SELECT id, persona_id, fecha_inicio, fecha_final,  cantidad_inicio, cantidad_final FROM turnos_cajeros WHERE cantidad_final=0 AND fecha_final='0000-00-00 00:00:00' AND persona_id = :persona_id";
	 		$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->bindParam(":persona_id", $datosModel, PDO::PARAM_INT);
			$stmt->execute();
			//$stmt->rowCount();
			return $stmt->fetch();
			$stmt->close();
 	 	}
	#---------------------------
	#REGISTRO DE TURNO
	#-------------------------------------
	 public static function registroTurnoModel($datosModel, $tabla)
		{ //echo "<script>alert('Entro CRUD   inicio usuario ".$_SESSION["usuario"]." ')</script>";
			date_default_timezone_set("America/Bogota");
			$fecha_inicio=strftime("%Y-%m-%d %H:%M:%S");
			$stmt = Conexion::conectar();
			$consulta="INSERT INTO turnos_cajeros (persona_id, fecha_inicio, fecha_final, cantidad_inicio, cantidad_final) VALUES  (".$datosModel['persona_id'].", '".$fecha_inicio."','".$datosModel['cantidad_final']."',  ".$datosModel['cantidad_inicio'].",".$datosModel['cantidad_final'].")";
			//echo "<script>alert('insertar tabla venta o factura ".$consulta2." ' );</script>";
			$stmt->exec($consulta);
			//$ultimo=$stmt->lastInsertId();
			$ultimo =DatosTurno::ultimoTurno($_SESSION["usuario"]);
			$_SESSION["turno"] = $ultimo["id"];
			//echo "<script>alert('guarda el turno  ".$ultimo["id"]."')</script>";
			return  "success";

			$stmt->close();
		}
	#-------------------------------------------
	#VISTA TURNO
	#-------------------------------------
	 public static function vistaTurnoModel($tabla)
		{
			$stmt = Conexion::conectar()->prepare("SELECT t.id AS tid, t.persona_id AS tpersona_id, t.fecha_inicio AS tfecha_inicio, t.fecha_final AS tfecha_final,  t.cantidad_inicio AS tcantidad_inicio, t.cantidad_final AS tcantidad_final, p.nombre AS pnombre,p.apellidos AS papellidos FROM $tabla t, personas p WHERE t.persona_id = p.id ORDER BY tid DESC ");
		$stmt->execute();
			$stmt->execute();
			#fetchAll(): Obtiene todas las filas de un conjunto de resultados asociado al objeto PDOStatement.
			return $stmt->fetchAll();
			$stmt->close();
		}
	#--------------------------------------
	#VERIFICAR TURNO
	#-------------------------------------
	 public static function verificarTurnoModel($datosModel, $tabla)
		{	//echo "<script>alert('CRUD turno')</script>";
			//$stmt =
			$stmt = Conexion::conectar()->prepare("SELECT id, persona_id, fecha_inicio, fecha_final,  cantidad_inicio, cantidad_final FROM $tabla WHERE persona_id = :persona_id AND fecha_final='0000-00-00 00:00:00' ");
			$stmt->bindParam(":persona_id", $datosModel, PDO::PARAM_INT);
			$stmt->execute();
			return$stmt->rowCount();
			//return $stmt->fetch();
			$stmt->close();
		}
	#--------------------------------
	#EDITAR TURNO
	#-------------------------------------
	 public static function editarTurnoModel($datosModel, $tabla)
		{	//echo "<script>alert(' usu ".$datosModel." ')</script>";

			$stmt = Conexion::conectar()->prepare("SELECT id, persona_id, fecha_inicio, fecha_final,  cantidad_inicio, cantidad_final FROM $tabla WHERE persona_id = :persona_id AND fecha_final='0000-00-00 00:00:00' ");
			$stmt->bindParam(":persona_id", $datosModel, PDO::PARAM_INT);
			$stmt->execute();
			//$stmt->rowCount();
			return $stmt->fetch();
			$stmt->close();
		}
	#-------------------------------------------
	#ACTUALIZAR TURNO
	#-------------------------------------
	 public static function actualizarTurnoModel($datosModel, $tabla)
		{
			$stmt = Conexion::conectar()->prepare("UPDATE $tabla SET persona_id = :persona_id, fecha_inicio= :fecha_inicio, fecha_final= :fecha_final,  cantidad_inicio= :cantidad_inicio, cantidad_final= :cantidad_final WHERE id = :id");
			$stmt->bindParam(":persona_id", $datosModel["persona_id"], PDO::PARAM_INT);
			$stmt->bindParam(":fecha_inicio", $datosModel["fecha_inicio"], PDO::PARAM_STR);
			$stmt->bindParam(":fecha_final", $datosModel["fecha_final"], PDO::PARAM_STR);
			$stmt->bindParam(":cantidad_inicio", $datosModel["cantidad_inicio"], PDO::PARAM_INT);
			$stmt->bindParam(":cantidad_final", $datosModel["cantidad_final"], PDO::PARAM_INT);
			$stmt->bindParam(":id", $datosModel["id"], PDO::PARAM_INT);
			if($stmt->execute())
			{	return "success";	}

			else{	return "error";			}
			$stmt->close();
		}
	#-----------------------------------------
	#BORRAR TURNO
	#------------------------------------
	 public static function borrarTurnoModel($datosModel, $tabla)
	 {
		$stmt = Conexion::conectar()->prepare("DELETE FROM $tabla WHERE id = :id");
		$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);
		if($stmt->execute())	{	return "success";	}
		else{	return "error";		}
		$stmt->close();
	 }
	#----------------------------------------------
	#CONSULTAR TURNO
	#-------------------------------------
		#Venta
		 public static function consultarTurnoModel($datosModel)
			{	//echo "<script>alert('CRUD turno')</script>";
				date_default_timezone_set("America/Bogota");
				$fecha = strftime("%Y-%m-%d %H:%M:%S");
				$stmt = Conexion::conectar()->prepare("SELECT t.id AS tid, t.persona_id AS tpersona, t.fecha_inicio AS tinicio, t.cantidad_inicio AS tcantidai, v.total AS vtotal, v.id AS vid, v.fecha_venta AS vfecha
					FROM turnos_cajeros t, ventas v
					WHERE v.turno_cajero_id=t.id  AND t.fecha_final = '0000-00-00 00:00:00' AND t.persona_id=:persona GROUP by v.id ");
				$stmt->bindParam(":persona", $datosModel, PDO::PARAM_INT);
				$stmt->execute();
				$c = $stmt->rowCount();
				if ($c>0)
				 { return $stmt->fetchAll(); }
				else { return 0; }
			}

		#Sin venta
		 public static function sinVentaTurnoModel($datosModel)
			{	//echo "<script>alert('CRUD turno fffffff".$datosModel." ')</script>";
				$stmt = Conexion::conectar()->prepare("SELECT id, persona_id, fecha_inicio, fecha_final,  cantidad_inicio, cantidad_final FROM turnos_cajeros WHERE fecha_final= '0000-00-00 00:00:00' AND persona_id = :persona  ");
				$stmt->bindParam(":persona", $datosModel, PDO::PARAM_INT);
				$stmt->execute();
				//$stmt->rowCount();
				$r=$stmt->fetch();
				//echo "<script>alert('Cntidad final".$r["cantidad_final"]." ')</script>";
				return 	$r;
			    $stmt->close();
			}


		#Abonos
		 public static function consultaAbonoModel($datosModel)
			{  $consulta ="SELECT a.id AS aid, a.turnos_cajeros_id AS aturno, a.fecha_bono AS afecha, a.valor AS avalor, t.fecha_inicio AS tfecha, per.nombre AS pernombre
					FROM abonos a, turnos_cajeros t, deudas d, personas per
					WHERE a.turnos_cajeros_id=t.id AND d.abonos_id=a.id AND d.cliente_id=per.id AND a.turnos_cajeros_id=:turno_id GROUP by a.id";
				$stmt = Conexion::conectar()->prepare($consulta);
				$stmt->bindParam(":turno_id", $datosModel, PDO::PARAM_INT);
				$stmt->execute();
				$r=$stmt->fetchAll();
				$c = $stmt->rowCount();
				//echo "<script>alert(' CRUD abono".$c." ')</script>";
				foreach ($r as $key => $value) {
					//echo "<script>alert(' CRUD valor : ".$value["avalor"]." ')</script>";
					# code...
				}
				if ($c>0)
				 { return $r; }
				else { return 0; }

				$stmt->close();
			}
		#Ingresos
		 public static function consultaIngresoModel($datosModel)
			{
				$stmt = Conexion::conectar()->prepare("SELECT turno_cajero_id, motivo, cantidad FROM ingresos WHERE turno_cajero_id=:turno_id ");
				$stmt->bindParam(":turno_id", $datosModel, PDO::PARAM_INT);
				$stmt->execute();
				$c = $stmt->rowCount();
				if ($c>0)
				 { return $stmt->fetchAll(); }
				else { return 0; }

				$stmt->close();
			}
		#Egresos
		 public static function consultaEgresoModel($datosModel)
			{
				$stmt = Conexion::conectar()->prepare("SELECT turno_cajero_id, motivo, cantidad FROM egresos WHERE turno_cajero_id=:turno_id ");
				$stmt->bindParam(":turno_id", $datosModel, PDO::PARAM_INT);
				$stmt->execute();
				$c = $stmt->rowCount();
				if ($c>0)
				 { return $stmt->fetchAll(); }
				else { return 0; }
				$stmt->close();
			}

		 public static function empleado ()
			{
				$stmt = Conexion::conectar()->prepare("SELECT r.id AS rid, r.nombre AS rnombre, per.nombre FROM roles r, personas per WHERE r.id<11 AND r.id=per.roles_id ");
				//$stmt->bindParam(":turno_id", $datosModel, PDO::PARAM_INT);
				$stmt->execute();
				$c = $stmt->rowCount();
				if ($c>0)
				 { return $stmt->fetchAll(); }
				else { return 0; }

				$stmt->close();
			}
		#Propina
		 public static function consultaPropinaModel($datosModel)
			{
				$stmt = Conexion::conectar()->prepare("SELECT v.id AS vid, v.propina AS vpropina, per.nombre AS pernombre FROM ventas v, pedidos pe, personas per  WHERE v.pedidos_id=pe.id AND pe.facturado='s' AND pe.mesero_id=per.id AND v.turno_cajero_id=:turno_id ");
				$stmt->bindParam(":turno_id", $datosModel, PDO::PARAM_INT);
				$stmt->execute();
				$c = $stmt->rowCount();
				if ($c>0)
				 { return $stmt->fetchAll(); }
				else { return 0; }

				$stmt->close();
			}
	#----------------------------------
	#CERRAR TURNO  
	#-------------------------------------
	 public static function cerrarTurnoModel($datosModel, $tabla)
		{  // echo "<script>alert('Entro crud Actualizar Producto')</script>";
			// $respuesta = DatosTurno::consultarTurnoModel($datosModel["id"]);
			date_default_timezone_set("America/Bogota");
			$fecha = strftime("%Y-%m-%d %H:%M:%S");
			$stmt = Conexion::conectar()->prepare("UPDATE $tabla SET fecha_final= :fecha_final, cantidad_final= :cantidad_final WHERE id = :id");
			$stmt->bindParam(":fecha_final", $fecha, PDO::PARAM_STR);
			$stmt->bindParam(":cantidad_final", $datosModel["cantidad_final"], PDO::PARAM_INT);
			$stmt->bindParam(":id", $datosModel["id"], PDO::PARAM_INT);
			$_SESSION["final"] = $fecha;
			if($stmt->execute())
				{ 	return "success";	}
			else{	return "error";	}
			$stmt->close();
		}
	#-----------------------------------------
	#REPORTE POR PRODUCTO
	#----------------------------------------------
	 public static function reporteTurno($inicio, $final)
	 {
	 	$consulta = "SELECT p.id as pid,p.nombre as pNombre, sum(pm.cantidad) as pmCantidad,p.precio as pPrecio,sum(pm.cantidad)*p.precio as subTproducto
				FROM ventas v,pedidos pd,pedido_productos_mesa pm,productos p
				WHERE v.pedidos_id=pd.id and pd.id=pm.pedidos_id and pm.productos_id=p.id
				and v.fecha_venta BETWEEN ':fecha_inicial' and ':cantidad_final' group by pm.productos_id ";
		$stmt = Conexion::conectar()->prepare($consulta);
		$stmt->bindParam(":fecha_inicial", $inicio, PDO::PARAM_STR);
		$stmt->bindParam(":cantidad_final", $final, PDO::PARAM_STR);
		//$stmt->bindParam(":id", $datosModel["id"], PDO::PARAM_INT);
		$stmt->execute();
	 }
	#-------------------------------------------
	#  CALCULO DE PRODUCTO
	#----------------------------------------------
	 public static function calculoProducto()
	 {
	 	$consulta = "SELECT sum(sp.cantidades*s.precio) as 'Valor Compra Producto' FROM productos p, suministros_productos sp, suministros s WHERE p.id=sp.producto_id and s.id=sp.suministro_id and p.id=20";
		$stmt = Conexion::conectar()->prepare($consulta);
		$stmt->bindParam(":fecha_inicial", $fecha, PDO::PARAM_STR);
		$stmt->bindParam(":cantidad_final", $datosModel["cantidad_final"], PDO::PARAM_STR);
		//$stmt->bindParam(":id", $datosModel["id"], PDO::PARAM_INT);
		$stmt->execute();
	 }
	#-------------------------------------------
 	#vista Ventas Model verificar and v.idfactura#   AND v.turno_cajero_id=8
	#-------------------------------------
	 public static function vistaVentasTModel($turno)
		{ //echo "<script>alert('Entro crud turno producto".$turno." ')</script>";
			/*$fechah1=$fecha1.' 00:00:01'; 	$fechah2=$fecha2.' 23:59:01';*/
			$consultar="SELECT p.id as pid, p.codigo as pcodigo, p.nombre as pNombre, sum(pm.cantidad) as pmCantidad,p.precio as pPrecio, sum(pm.cantidad)*p.precio as subTproducto, v.turno_cajero_id
						FROM ventas v,pedidos pd,pedido_productos_mesa pm,productos p
						WHERE v.pedidos_id=pd.id and pd.id=pm.pedidos_id and pm.productos_id=p.id and v.turno_cajero_id=$turno group by pm.productos_id";
			$stmt = Conexion::conectar()->prepare($consultar);
				//echo $consultar;
			$stmt->execute();
			$v=$stmt->fetchAll();
			return $v;
			$stmt->close();
		}
	#----------------------------------------------
	# Costo Producto Model
	#-------------------------------------
	 public static function CostoProducto1Model($idproducto)
	  {
	  	$consulta="SELECT sum(sp.cantidades*s.precio) as Valor_Compra_Producto
					FROM productos p, suministros_productos sp, suministros s
					WHERE p.id=sp.producto_id and s.id=sp.suministro_id and p.id=$idproducto";
			//echo $consulta;
			$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->execute();
			return $stmt->fetch();
			$stmt->close();
	  }
	#---------------------------------------------------------
 }