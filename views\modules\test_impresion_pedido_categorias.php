<?php
// Test del nuevo sistema de impresión por categorías
echo "<h1>🧪 Test Sistema de Impresión por Categorías</h1>";
echo "<p><strong>Probar el nuevo sistema de botones dinámicos por categoría</strong></p>";

require_once '../../models/conexion.php';
require_once '../../models/crudPedidoImpresion.php';

// Crear pedido de prueba si se solicita
if (isset($_POST['crear_pedido_prueba'])) {
    echo "<div style='border: 1px solid #007bff; padding: 20px; margin: 15px 0; border-radius: 8px;'>";
    echo "<h4>🔄 Creando pedido de prueba...</h4>";
    
    try {
        $conexion = new Conexion();
        $pdo = $conexion->conectar();
        
        // Verificar que las tablas existen
        $stmt = $pdo->query("SHOW TABLES LIKE 'pedidos'");
        if (!$stmt->fetch()) {
            throw new Exception("La tabla 'pedidos' no existe en la base de datos");
        }

        $stmt = $pdo->query("SHOW TABLES LIKE 'detalle_pedidos'");
        if (!$stmt->fetch()) {
            throw new Exception("La tabla 'detalle_pedidos' no existe en la base de datos");
        }

        $stmt = $pdo->query("SHOW TABLES LIKE 'productos'");
        if (!$stmt->fetch()) {
            throw new Exception("La tabla 'productos' no existe en la base de datos");
        }

        echo "<p>✅ <strong>Tablas verificadas:</strong> pedidos, detalle_pedidos, productos</p>";

        // Crear pedido
        $numero_pedido = 'TEST' . str_pad(rand(100, 999), 6, '0', STR_PAD_LEFT);

        $stmt = $pdo->prepare("
            INSERT INTO pedidos (numero_pedido, mesa_id, estado, fecha_pedido)
            VALUES (:numero, 1, 'enviado', NOW())
        ");
        $stmt->bindParam(":numero", $numero_pedido, PDO::PARAM_STR);
        $stmt->execute();

        $pedido_id = $pdo->lastInsertId();

        echo "<p>✅ <strong>Pedido creado:</strong> ID $pedido_id, Número $numero_pedido</p>";
        
        // Productos de ejemplo (diferentes categorías)
        $productos_ejemplo = [
            // Productos de BAR (bebidas)
            ['nombre' => 'Cerveza Corona', 'categoria' => 'bebidas', 'precio' => 8000, 'cantidad' => 2],
            ['nombre' => 'Whisky Etiqueta Negra', 'categoria' => 'licores', 'precio' => 15000, 'cantidad' => 1],
            
            // Productos de COCINA (comidas)
            ['nombre' => 'Bandeja Paisa', 'categoria' => 'comidas', 'precio' => 25000, 'cantidad' => 1],
            ['nombre' => 'Sancocho de Gallina', 'categoria' => 'sopas', 'precio' => 18000, 'cantidad' => 2],
            
            // Productos de ASADOS (carnes)
            ['nombre' => 'Churrasco', 'categoria' => 'carnes', 'precio' => 35000, 'cantidad' => 1],
            ['nombre' => 'Costillas BBQ', 'categoria' => 'parrilla', 'precio' => 28000, 'cantidad' => 1]
        ];
        
        foreach ($productos_ejemplo as $prod) {
            // Buscar o crear producto
            $stmt = $pdo->prepare("SELECT id FROM productos WHERE nombre = :nombre LIMIT 1");
            $stmt->bindParam(":nombre", $prod['nombre'], PDO::PARAM_STR);
            $stmt->execute();
            $producto_existente = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($producto_existente) {
                $producto_id = $producto_existente['id'];
            } else {
                // Crear producto
                $stmt = $pdo->prepare("
                    INSERT INTO productos (nombre, precio, categoria, activo) 
                    VALUES (:nombre, :precio, :categoria, 's')
                ");
                $stmt->bindParam(":nombre", $prod['nombre'], PDO::PARAM_STR);
                $stmt->bindParam(":precio", $prod['precio'], PDO::PARAM_INT);
                $stmt->bindParam(":categoria", $prod['categoria'], PDO::PARAM_STR);
                $stmt->execute();
                
                $producto_id = $pdo->lastInsertId();
            }
            
            // Agregar al pedido
            $subtotal = $prod['precio'] * $prod['cantidad'];
            
            $stmt = $pdo->prepare("
                INSERT INTO detalle_pedidos (pedido_id, producto_id, cantidad, precio_unitario, subtotal) 
                VALUES (:pedido_id, :producto_id, :cantidad, :precio, :subtotal)
            ");
            $stmt->bindParam(":pedido_id", $pedido_id, PDO::PARAM_INT);
            $stmt->bindParam(":producto_id", $producto_id, PDO::PARAM_INT);
            $stmt->bindParam(":cantidad", $prod['cantidad'], PDO::PARAM_INT);
            $stmt->bindParam(":precio", $prod['precio'], PDO::PARAM_INT);
            $stmt->bindParam(":subtotal", $subtotal, PDO::PARAM_INT);
            $stmt->execute();
        }
        
        echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0;'>";
        echo "<h5>✅ Pedido de Prueba Creado</h5>";
        echo "<p><strong>Pedido ID:</strong> $pedido_id</p>";
        echo "<p><strong>Número:</strong> $numero_pedido</p>";
        echo "<p><strong>Productos:</strong> " . count($productos_ejemplo) . "</p>";
        echo "<p><strong>Categorías:</strong> BAR, COCINA, ASADOS</p>";
        echo "</div>";
        
        echo "<div style='text-align: center; margin: 20px 0;'>";
        echo "<a href='impresion_pedido.php?pedido_id=$pedido_id' style='background-color: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; font-size: 16px;'>🖨️ PROBAR IMPRESIÓN DEL PEDIDO</a>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
        echo "<h5>❌ Error</h5>";
        echo "<p>" . $e->getMessage() . "</p>";
        echo "</div>";
    }
    
    echo "</div>";
}

// Mostrar pedidos existentes para probar
echo "<h2>📋 Pedidos Disponibles para Probar</h2>";

try {
    $conexion = new Conexion();
    $pdo = $conexion->conectar();

    echo "<div style='background-color: #d4edda; padding: 10px; border-left: 4px solid #28a745; margin: 10px 0;'>";
    echo "<p>✅ <strong>Conexión a base de datos exitosa</strong></p>";
    echo "</div>";

    // Verificar si existen las tablas necesarias
    $stmt = $pdo->query("SHOW TABLES LIKE 'pedidos'");
    $tabla_pedidos = $stmt->fetch();

    $stmt = $pdo->query("SHOW TABLES LIKE 'detalle_pedidos'");
    $tabla_detalle = $stmt->fetch();

    if (!$tabla_pedidos) {
        echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
        echo "<h4>❌ Tabla 'pedidos' no existe</h4>";
        echo "<p>La tabla de pedidos no está creada en la base de datos</p>";
        echo "</div>";
        return;
    }

    if (!$tabla_detalle) {
        echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
        echo "<h4>❌ Tabla 'detalle_pedidos' no existe</h4>";
        echo "<p>La tabla de detalle de pedidos no está creada en la base de datos</p>";
        echo "</div>";
        return;
    }

    echo "<div style='background-color: #d4edda; padding: 10px; border-left: 4px solid #28a745; margin: 10px 0;'>";
    echo "<p>✅ <strong>Tablas necesarias encontradas</strong></p>";
    echo "</div>";

    $stmt = $pdo->query("
        SELECT
            p.id,
            p.numero_pedido,
            p.mesa_id,
            p.estado,
            p.fecha_pedido,
            COUNT(dp.id) as total_productos,
            SUM(dp.subtotal) as total_pedido
        FROM pedidos p
        LEFT JOIN detalle_pedidos dp ON p.id = dp.pedido_id
        GROUP BY p.id
        ORDER BY p.fecha_pedido DESC
        LIMIT 10
    ");

    $pedidos = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo "<div style='background-color: #e7f3ff; padding: 10px; border-left: 4px solid #007bff; margin: 10px 0;'>";
    echo "<p>📊 <strong>Pedidos encontrados:</strong> " . count($pedidos) . "</p>";
    echo "</div>";

    if ($pedidos) {
        echo "<div style='background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<table style='width: 100%; border-collapse: collapse;'>";
        echo "<tr style='background-color: #ffeaa7;'>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>ID</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Número</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Mesa</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Estado</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Productos</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Total</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Acción</th>";
        echo "</tr>";
        
        foreach ($pedidos as $pedido) {
            echo "<tr>";
            echo "<td style='border: 1px solid #ddd; padding: 8px; font-weight: bold;'>{$pedido['id']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$pedido['numero_pedido']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$pedido['mesa_id']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$pedido['estado']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$pedido['total_productos']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>$" . number_format($pedido['total_pedido'], 0) . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>";
            
            if ($pedido['total_productos'] > 0) {
                echo "<a href='impresion_pedido.php?pedido_id={$pedido['id']}' style='background-color: #28a745; color: white; padding: 6px 12px; text-decoration: none; border-radius: 3px; font-size: 12px;'>🖨️ Imprimir</a>";
            } else {
                echo "<span style='color: #6c757d;'>Sin productos</span>";
            }
            
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
    } else {
        echo "<div style='background-color: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 15px 0;'>";
        echo "<h4>⚠️ No hay pedidos disponibles</h4>";
        echo "<p>No se encontraron pedidos en la base de datos. Puedes crear un pedido de prueba usando el botón de abajo.</p>";
        echo "</div>";
    }

} catch (Exception $e) {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
    echo "<h4>❌ Error obteniendo pedidos</h4>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Archivo:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Línea:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

// Formulario para crear pedido de prueba
echo "<h2>🧪 Crear Pedido de Prueba</h2>";
echo "<form method='POST' style='background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>🎯 Generar Pedido con Múltiples Categorías</h4>";
echo "<p>Crea un pedido de prueba con productos de BAR, COCINA y ASADOS para probar el sistema de botones dinámicos</p>";

echo "<div style='background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h5>📦 Productos que se incluirán:</h5>";
echo "<ul>";
echo "<li><strong>🍺 BAR:</strong> Cerveza Corona x2, Whisky Etiqueta Negra x1</li>";
echo "<li><strong>🍳 COCINA:</strong> Bandeja Paisa x1, Sancocho de Gallina x2</li>";
echo "<li><strong>🥩 ASADOS:</strong> Churrasco x1, Costillas BBQ x1</li>";
echo "</ul>";
echo "<p><strong>Resultado esperado:</strong> 3 botones de impresión (uno por cada categoría)</p>";
echo "</div>";

echo "<button type='submit' name='crear_pedido_prueba' style='background-color: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold; width: 100%;'>🧪 Crear Pedido de Prueba</button>";
echo "</form>";

// Información del sistema
echo "<h2>💡 Información del Nuevo Sistema</h2>";
echo "<div style='background-color: #e7f3ff; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>🔧 Cómo Funciona:</h4>";
echo "<ol>";
echo "<li><strong>Análisis automático:</strong> El sistema analiza los productos del pedido</li>";
echo "<li><strong>Agrupación por categoría:</strong> Separa productos por BAR, COCINA, ASADOS</li>";
echo "<li><strong>Botones dinámicos:</strong> Solo muestra botones para categorías que tienen productos</li>";
echo "<li><strong>Impresión manual:</strong> El operario selecciona qué categoría imprimir</li>";
echo "<li><strong>Ventana de impresión:</strong> Se abre ventana optimizada para imprimir</li>";
echo "</ol>";

echo "<h4>✅ Ventajas del Nuevo Sistema:</h4>";
echo "<ul>";
echo "<li>🎯 <strong>Control total:</strong> El operario decide cuándo y qué imprimir</li>";
echo "<li>🚀 <strong>Simplicidad:</strong> Solo botones, sin configuraciones complejas</li>";
echo "<li>🔒 <strong>Confiabilidad:</strong> Usa el sistema de impresión del navegador</li>";
echo "<li>📱 <strong>Compatibilidad:</strong> Funciona en móviles, tablets y PC</li>";
echo "<li>⚡ <strong>Velocidad:</strong> Sin dependencias de red o proxies</li>";
echo "<li>🔄 <strong>Flexibilidad:</strong> Puede reimprimir cualquier categoría</li>";
echo "</ul>";

echo "<h4>🎯 Casos de Uso:</h4>";
echo "<ul>";
echo "<li><strong>Pedido completo:</strong> Imprimir todas las categorías una por una</li>";
echo "<li><strong>Pedido parcial:</strong> Solo imprimir categorías específicas</li>";
echo "<li><strong>Reimpresión:</strong> Volver a imprimir una categoría si es necesario</li>";
echo "<li><strong>Control de flujo:</strong> Imprimir en el momento adecuado</li>";
echo "</ul>";
echo "</div>";

// Botones de navegación
echo "<p style='margin-top: 30px;'>";
echo "<a href='test_impresion_socket.php' style='background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>← Tests Anteriores</a>";
echo "<a href='index.php?action=registroPmesa&ida=1' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🪑 Mesa Real</a>";
echo "<a href='pantallaCocina?categoria=cocina' style='background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🍳 Pantalla Cocina</a>";
echo "</p>";
?>
