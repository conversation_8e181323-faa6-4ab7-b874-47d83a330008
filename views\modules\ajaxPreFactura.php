<?php
require_once "../../models/crud.php";
require_once "../../models/crudFacturaAja.php";
require_once "../../controllers/controller.php";
require_once "../../controllers/controllerFacturaAja.php";
	ini_set("session.cookie_lifetime","28800");
	ini_set("session.gc_maxlifetime","28800");
//echo "<script>alert('entro al ajax   tipo de propina: ".$_POST['propina']." totalDescuento: ".$_POST['totalDescuento']." total: ".$_POST['total']."Cedula: ".$_POST['pcedula']."')</script>";

$pcedula = $_POST['pcedula'];// # cedula del cliente
$queryString3 = $_POST['total'];//valor total de la cuenta
$totalDescuento = $_POST['totalDescuento'];//valor total de la cuenta
$propina = $_POST['propina'];//valor total de la cuenta
$mesa = $_POST['mesa'];//Mesa de la Factura
$ajax=new controllerFacturaAja();
$ajax->preFacturaajaxController($pcedula, $propina, $mesa);
