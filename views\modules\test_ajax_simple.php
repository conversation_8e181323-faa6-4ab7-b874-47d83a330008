<?php

session_start();

if(!isset($_SESSION["validar"]) || !$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Test AJAX Simple</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
</head>
<body>

<div class="container">
    <h2>🔍 Test: AJAX Simple</h2>
    <hr>
    
    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">🧪 Test 1: Acceso directo al archivo AJAX</h3>
        </div>
        <div class="panel-body">
            <p><strong>Vamos a probar acceder directamente al archivo AJAX:</strong></p>
            <a href="views/modules/ajaxPedidosCategorias.php?accion=ping" target="_blank" class="btn btn-info">
                🔗 Abrir AJAX directamente
            </a>
            <p style="margin-top: 10px;"><small>Esto debería abrir una nueva pestaña con la respuesta JSON</small></p>
        </div>
    </div>
    
    <div class="panel panel-warning">
        <div class="panel-heading">
            <h3 class="panel-title">🧪 Test 2: Verificar rutas</h3>
        </div>
        <div class="panel-body">
            <?php
            $ruta_ajax = __DIR__ . '/ajaxPedidosCategorias.php';
            $ruta_controller = __DIR__ . '/../../controllers/controllerPedidosCategorias.php';
            $ruta_model = __DIR__ . '/../../models/crudPedidosCategorias.php';
            
            echo "<p><strong>Verificando rutas:</strong></p>";
            echo "<p>📁 Archivo AJAX: " . ($ruta_ajax) . "</p>";
            echo "<p>✅ Existe: " . (file_exists($ruta_ajax) ? 'SÍ' : 'NO') . "</p>";
            
            echo "<p>📁 Controlador: " . ($ruta_controller) . "</p>";
            echo "<p>✅ Existe: " . (file_exists($ruta_controller) ? 'SÍ' : 'NO') . "</p>";
            
            echo "<p>📁 Modelo: " . ($ruta_model) . "</p>";
            echo "<p>✅ Existe: " . (file_exists($ruta_model) ? 'SÍ' : 'NO') . "</p>";
            ?>
        </div>
    </div>
    
    <div class="panel panel-success">
        <div class="panel-heading">
            <h3 class="panel-title">🧪 Test 3: Test con fetch nativo</h3>
        </div>
        <div class="panel-body">
            <button onclick="testFetch()" class="btn btn-success">🚀 Test con Fetch</button>
            <div id="fetch-result" style="margin-top: 10px;"></div>
        </div>
    </div>
    
    <div class="panel panel-primary">
        <div class="panel-heading">
            <h3 class="panel-title">🧪 Test 4: Test con XMLHttpRequest</h3>
        </div>
        <div class="panel-body">
            <button onclick="testXHR()" class="btn btn-primary">📡 Test con XHR</button>
            <div id="xhr-result" style="margin-top: 10px;"></div>
        </div>
    </div>
    
    <div class="panel panel-danger">
        <div class="panel-heading">
            <h3 class="panel-title">🧪 Test 5: Crear archivo AJAX mínimo</h3>
        </div>
        <div class="panel-body">
            <p>Si los tests anteriores fallan, vamos a crear un archivo AJAX mínimo para probar:</p>
            <button onclick="testMinimal()" class="btn btn-danger">🔧 Test AJAX Mínimo</button>
            <div id="minimal-result" style="margin-top: 10px;"></div>
        </div>
    </div>
    
    <hr>
    <div class="row">
        <div class="col-md-6">
            <a href="index.php?action=test_ajax_marcar_entregado" class="btn btn-primary btn-block">🔙 Test Anterior</a>
        </div>
        <div class="col-md-6">
            <a href="index.php?action=pedidosBarPendientes" class="btn btn-info btn-block">🍺 Volver a Bar</a>
        </div>
    </div>
</div>

<script>
function testFetch() {
    document.getElementById('fetch-result').innerHTML = '<div class="alert alert-info">🔄 Probando con fetch...</div>';
    
    fetch('views/modules/ajaxPedidosCategorias.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            accion: 'ping'
        })
    })
    .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);
        return response.text();
    })
    .then(text => {
        console.log('Response text:', text);
        document.getElementById('fetch-result').innerHTML = 
            '<div class="alert alert-success">✅ Respuesta recibida:<br><pre>' + text + '</pre></div>';
    })
    .catch(error => {
        console.error('Fetch error:', error);
        document.getElementById('fetch-result').innerHTML = 
            '<div class="alert alert-danger">❌ Error: ' + error.message + '</div>';
    });
}

function testXHR() {
    document.getElementById('xhr-result').innerHTML = '<div class="alert alert-info">🔄 Probando con XMLHttpRequest...</div>';
    
    var xhr = new XMLHttpRequest();
    xhr.open('POST', 'views/modules/ajaxPedidosCategorias.php', true);
    xhr.setRequestHeader('Content-Type', 'application/json');
    
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            console.log('XHR status:', xhr.status);
            console.log('XHR response:', xhr.responseText);
            
            if (xhr.status === 200) {
                document.getElementById('xhr-result').innerHTML = 
                    '<div class="alert alert-success">✅ Respuesta recibida:<br><pre>' + xhr.responseText + '</pre></div>';
            } else {
                document.getElementById('xhr-result').innerHTML = 
                    '<div class="alert alert-danger">❌ Error HTTP ' + xhr.status + ':<br><pre>' + xhr.responseText + '</pre></div>';
            }
        }
    };
    
    xhr.onerror = function() {
        document.getElementById('xhr-result').innerHTML = 
            '<div class="alert alert-danger">❌ Error de red</div>';
    };
    
    xhr.send(JSON.stringify({
        accion: 'ping'
    }));
}

function testMinimal() {
    document.getElementById('minimal-result').innerHTML = '<div class="alert alert-info">🔄 Probando AJAX mínimo...</div>';
    
    fetch('views/modules/ajax_minimal_test.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            test: 'ping'
        })
    })
    .then(response => response.text())
    .then(text => {
        document.getElementById('minimal-result').innerHTML = 
            '<div class="alert alert-success">✅ AJAX mínimo funciona:<br><pre>' + text + '</pre></div>';
    })
    .catch(error => {
        document.getElementById('minimal-result').innerHTML = 
            '<div class="alert alert-danger">❌ Error en AJAX mínimo: ' + error.message + '</div>';
    });
}
</script>

</body>
</html>
