<?php

	#EXTENSIÓN DE CLASES: Los objetos pueden ser extendidos, y pueden heredar propiedades y métodos. Para definir una clase como extensión, debo definir una clase padre, y se utiliza dentro de una clase hija.

	require_once "conexion.php";

	class DatosCiudadProveedor extends Conexion
		{
			
			##ciudad_proveedor  id, ciudad_id, provedor_id, nombre
			#----------------------------------------------
				#REGISTRO DE ciudad_proveedor
				#-------------------------------------
					public static function registroCiudadProveedorModel($datosModel, $tabla)
						{
							echo "<script>alert('Entro CRUD  no')</script>";	
							$consulta="INSERT INTO $tabla (ciudad_id, provedor_id, nombre) VALUES (:ciudad_id, :provedor_id, :nombre)";
							//echo "<script>alert('Entro CRUD ".$consulta." no')</script>";	".$datosModel['nombre']."
							$stmt = Conexion::conectar()->prepare($consulta);
							//$stmt->execute();			
							$stmt->bindParam(":ciudad_id", $datosModel['ciudad_id'], PDO::PARAM_INT);
							$stmt->bindParam(":provedor_id", $datosModel['provedor_id'], PDO::PARAM_INT);
							$stmt->bindParam(":nombre", $datosModel['nombre'], PDO::PARAM_STR);
							echo "<script>alert('Guardo')</script>";			

							if($stmt->execute())
								{	return "success";	}
							else{ return "error";	}
							$stmt->close();
						}

				#VISTA ciudad_proveedor
				#-------------------------------------

					public static function vistaCiudadProveedorModel($tabla)
						{
							$stmt = Conexion::conectar()->prepare("SELECT id, ciudad_id, provedor_id, nombre FROM $tabla");	
							$stmt->execute();
							
							return $stmt->fetchAll();
							$stmt->close();
						}

				#EDITAR ciudad_proveedor
				#-------------------------------------

					public static function editarCiudadProveedorModel($datosModel, $tabla)
						{
							$stmt = Conexion::conectar()->prepare("SELECT id, ciudad_id, provedor_id, nombre FROM $tabla WHERE id = :id");
							$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);	
							$stmt->execute();
							return $stmt->fetch();
							$stmt->close();
						}

				#ACTUALIZAR ciudad_proveedor
				#-------------------------------------

					public static function actualizarCiudadProveedorModel($datosModel, $tabla)
						{ echo "<script>alert('Entro Actualizar Producto')</script>";	
							$stmt = Conexion::conectar()->prepare("UPDATE $tabla SET ciudad_id =:ciudad_id, provedor_id =:provedor_id, nombre = :nombre WHERE id = :id");			
							$stmt->bindParam(":ciudad_id", $datosModel["ciudad_id"], PDO::PARAM_INT);
							$stmt->bindParam(":provedor_id", $datosModel["provedor_id"], PDO::PARAM_INT);
							$stmt->bindParam(":nombre", $datosModel["nombre"], PDO::PARAM_STR);
							$stmt->bindParam(":id", $datosModel["id"], PDO::PARAM_INT);
							if($stmt->execute())
								{echo "<script>alert('Guardo Actualizar Producto')</script>";return "success";	}
							else{	return "error";			}
							$stmt->close();
						}

				#BORRAR ciudad_proveedor
				#------------------------------------
					public static function borrarCiudadProveedorModel($datosModel, $tabla)
					{
						$stmt = Conexion::conectar()->prepare("DELETE FROM $tabla WHERE id = :id");
						$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);
						if($stmt->execute())
							{	return "success";	}
						else{	return "error";		}
						$stmt->close();
					}
			#----------------------------------------------		
		}