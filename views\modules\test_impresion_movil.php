<?php
// Test de impresión optimizado para dispositivos móviles
echo "<h1>📱 Test de Impresión Móvil</h1>";
echo "<p><strong>Sistema optimizado para celulares Android (80% de uso)</strong></p>";

// Detectar si es móvil
$es_movil = preg_match('/(android|iphone|ipad|mobile)/i', $_SERVER['HTTP_USER_AGENT']);
$ip_cliente = $_SERVER['REMOTE_ADDR'];

if ($es_movil) {
    echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0;'>";
    echo "<h4>📱 Dispositivo Móvil Detectado</h4>";
    echo "<p>✅ Optimizado para impresión directa desde Android</p>";
    echo "</div>";
} else {
    echo "<div style='background-color: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 15px 0;'>";
    echo "<h4>💻 Dispositivo de Escritorio Detectado</h4>";
    echo "<p>⚠️ Para mejores resultados, usar desde celular Android</p>";
    echo "</div>";
}

// Verificar red
$en_red_local = strpos($ip_cliente, '192.168.68.') === 0;

echo "<div style='background-color: " . ($en_red_local ? '#d4edda' : '#f8d7da') . "; padding: 15px; border-left: 4px solid " . ($en_red_local ? '#28a745' : '#dc3545') . "; margin: 15px 0;'>";
echo "<h4>🌐 Estado de Red</h4>";
echo "<p><strong>Tu IP:</strong> $ip_cliente</p>";
if ($en_red_local) {
    echo "<p>✅ <strong>En red local</strong> - Impresión directa disponible</p>";
} else {
    echo "<p>❌ <strong>Fuera de red local</strong> - Se usará proxy</p>";
}
echo "</div>";

try {
    require_once '../../models/conexion.php';
    $conexion = new Conexion();
    $pdo = $conexion->conectar();
} catch (Exception $e) {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
    echo "<h4>❌ Error de conexión a base de datos</h4>";
    echo "</div>";
    exit;
}

// JavaScript para impresión directa desde móvil
echo "<script>
// Función para impresión directa desde JavaScript
async function imprimirDirecto(ip, puerto, contenido, categoria) {
    const datos = {
        impresora: categoria,
        contenido: contenido,
        timestamp: Date.now(),
        dispositivo: 'movil'
    };
    
    console.log('📱 Intentando impresión directa:', datos);
    
    try {
        // Método 1: Intentar WebSocket (si está disponible)
        if (window.WebSocket) {
            console.log('🔌 Probando WebSocket...');
            // Aquí iría la lógica de WebSocket si implementamos
        }
        
        // Método 2: Fetch API con timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000);
        
        const response = await fetch('http://' + ip + ':' + puerto + '/print', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(datos),
            signal: controller.signal
        });
        
        clearTimeout(timeoutId);
        
        if (response.ok) {
            return { success: true, metodo: 'directo' };
        } else {
            throw new Error('HTTP ' + response.status);
        }
        
    } catch (error) {
        console.log('❌ Impresión directa falló:', error.message);
        
        // Fallback: Usar proxy del sistema
        return await imprimirViaProxy(categoria, contenido);
    }
}

// Fallback al proxy
async function imprimirViaProxy(categoria, contenido) {
    console.log('🔄 Usando fallback al proxy...');
    
    try {
        const response = await fetch(window.location.origin + '/api/imprimir.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                categoria: categoria,
                contenido: contenido,
                dispositivo: 'movil'
            })
        });
        
        const result = await response.json();
        return result;
        
    } catch (error) {
        console.log('❌ Error en proxy:', error.message);
        return { success: false, error: error.message };
    }
}

// Función principal de impresión
async function imprimir(categoria) {
    const contenido = document.getElementById('contenido_' + categoria).value;
    const boton = document.getElementById('btn_' + categoria);
    const resultado = document.getElementById('resultado_' + categoria);
    
    boton.disabled = true;
    boton.innerHTML = '🔄 Imprimiendo...';
    resultado.innerHTML = '<p>⏳ Procesando...</p>';
    
    // Configuración de impresoras
    const impresoras = {
        'bar': { ip: '**************', puerto: 9100 },
        'cocina': { ip: '**************', puerto: 9100 },
        'asados': { ip: '**************', puerto: 9100 }
    };
    
    const config = impresoras[categoria];
    if (!config) {
        resultado.innerHTML = '<p style=\"color: red;\">❌ Categoría no válida</p>';
        boton.disabled = false;
        boton.innerHTML = '🖨️ Imprimir';
        return;
    }
    
    try {
        const result = await imprimirDirecto(config.ip, config.puerto, contenido, categoria);
        
        if (result.success) {
            resultado.innerHTML = '<div style=\"background-color: #d4edda; padding: 10px; border-left: 4px solid #28a745;\"><p>✅ Impresión exitosa (' + result.metodo + ')</p></div>';
        } else {
            resultado.innerHTML = '<div style=\"background-color: #f8d7da; padding: 10px; border-left: 4px solid #dc3545;\"><p>❌ Error: ' + (result.error || 'Desconocido') + '</p></div>';
        }
        
    } catch (error) {
        resultado.innerHTML = '<div style=\"background-color: #f8d7da; padding: 10px; border-left: 4px solid #dc3545;\"><p>❌ Error: ' + error.message + '</p></div>';
    }
    
    boton.disabled = false;
    boton.innerHTML = '🖨️ Imprimir';
}
</script>";

// Interfaz de prueba para móviles
echo "<h2>📱 Test de Impresión Móvil</h2>";

// Obtener impresoras
try {
    $stmt = $pdo->query("SELECT categoria, ip, puerto FROM impresoras ORDER BY categoria");
    $impresoras = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($impresoras as $impresora) {
        $categoria = $impresora['categoria'];
        
        echo "<div style='background-color: #f8f9fa; padding: 20px; margin: 15px 0; border-radius: 8px; border: 1px solid #dee2e6;'>";
        echo "<h4>🖨️ {$impresora['categoria']} ({$impresora['ip']}:{$impresora['puerto']})</h4>";
        
        echo "<div style='margin: 15px 0;'>";
        echo "<label><strong>Contenido a imprimir:</strong></label><br>";
        echo "<textarea id='contenido_{$categoria}' rows='4' style='width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 3px; font-family: monospace;'>";
        echo "Mesa 1\n";
        echo "Producto: Cerveza Corona\n";
        echo "Cantidad: 2\n";
        echo "Precio: \$8.000\n";
        echo "Total: \$16.000";
        echo "</textarea>";
        echo "</div>";
        
        echo "<button id='btn_{$categoria}' onclick='imprimir(\"{$categoria}\")' style='background-color: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold; width: 100%;'>🖨️ Imprimir en {$categoria}</button>";
        
        echo "<div id='resultado_{$categoria}' style='margin-top: 15px;'></div>";
        
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error obteniendo impresoras: " . $e->getMessage() . "</p>";
}

// Test de conectividad desde JavaScript
echo "<h2>🔍 Test de Conectividad</h2>";
echo "<button onclick='testConectividad()' style='background-color: #28a745; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold; width: 100%;'>🔍 Probar Conectividad</button>";
echo "<div id='resultado_conectividad' style='margin-top: 15px;'></div>";

echo "<script>
async function testConectividad() {
    const resultado = document.getElementById('resultado_conectividad');
    resultado.innerHTML = '<p>⏳ Probando conectividad...</p>';
    
    const impresoras = {
        'bar': { ip: '**************', puerto: 9100 },
        'cocina': { ip: '**************', puerto: 9100 },
        'asados': { ip: '**************', puerto: 9100 }
    };
    
    let html = '<div style=\"background-color: #e7f3ff; padding: 15px; border-radius: 5px;\">';
    html += '<h5>📡 Resultados de Conectividad:</h5>';
    
    for (const [nombre, config] of Object.entries(impresoras)) {
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000);
            
            const start = Date.now();
            const response = await fetch('http://' + config.ip + ':' + config.puerto, {
                method: 'GET',
                signal: controller.signal
            });
            const tiempo = Date.now() - start;
            
            clearTimeout(timeoutId);
            
            html += '<p>✅ <strong>' + nombre + '</strong>: Online (' + tiempo + 'ms)</p>';
            
        } catch (error) {
            html += '<p>❌ <strong>' + nombre + '</strong>: ' + error.message + '</p>';
        }
    }
    
    html += '</div>';
    resultado.innerHTML = html;
}
</script>";

// Información específica para móviles
echo "<h2>📱 Información para Dispositivos Móviles</h2>";
echo "<div style='background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h5>🔧 Configuración Recomendada:</h5>";
echo "<ul>";
echo "<li>✅ <strong>Conectar a WiFi:</strong> Red 192.168.68.x</li>";
echo "<li>✅ <strong>Navegador:</strong> Chrome o Firefox</li>";
echo "<li>✅ <strong>Permisos:</strong> Permitir conexiones de red</li>";
echo "<li>✅ <strong>JavaScript:</strong> Debe estar habilitado</li>";
echo "</ul>";

echo "<h5>⚡ Ventajas de Impresión Móvil:</h5>";
echo "<ul>";
echo "<li>🚀 <strong>Más rápida:</strong> Conexión directa a impresoras</li>";
echo "<li>🔒 <strong>Más confiable:</strong> Sin dependencia del servidor</li>";
echo "<li>📱 <strong>Optimizada:</strong> Interfaz táctil amigable</li>";
echo "<li>🔄 <strong>Fallback automático:</strong> Si falla, usa proxy</li>";
echo "</ul>";
echo "</div>";

// Botones de navegación
echo "<p style='margin-top: 30px;'>";
echo "<a href='test_impresion_completo.php' style='background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>← Test Completo</a>";
echo "<a href='index.php?action=registroPmesa&ida=1' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>📱 Mesa desde Móvil</a>";
echo "<a href='pantallaCocina?categoria=cocina' style='background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🍳 Pantalla Cocina</a>";
echo "</p>";
?>
