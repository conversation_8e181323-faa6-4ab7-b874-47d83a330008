# Instrucciones de Instalación - Sistema de Gestión de Pedidos

## Pasos de Instalación

### 1. **Ejecutar Diagnóstico**
Primero, verifica el estado actual del sistema:
```
https://macarena.anconclub.com/diagnostico
```

### 2. **Ejecutar Script de Migración**
En tu panel de control de base de datos (phpMyAdmin), ejecuta:
```sql
USE ewogjwfm_macarena;
SOURCE sql/migracion_segura.sql;
```

O ejecuta paso a paso las consultas del archivo `sql/migracion_segura.sql`

### 3. **Verificar Archivos Subidos**
Asegúrate de que estos archivos estén en el servidor:

**Modelos:**
- `models/crudEstadoPedidos.php`
- `models/crudImpresionPedidos.php`

**Controladores:**
- `controllers/controllerEstadoPedidos.php`
- `controllers/controllerImpresionPedidos.php`

**Vistas:**
- `views/modules/ajaxEstadoPedidos.php`
- `views/modules/pantallaCocina.php`
- `views/modules/diagnostico.php`

**Archivos Modificados:**
- `index.php` (actualizado)
- `models/enlaces.php` (actualizado)
- `views/modules/registroPmesa.php` (actualizado)
- `controllers/controllerPedidoMesaVendido.php` (actualizado)
- `models/crudPedidoMesaVendido.php` (actualizado)

### 4. **Configurar Impresoras**
Verifica que la tabla `impresoras` tenga la configuración correcta:
```sql
SELECT * FROM impresoras;
```

Debe mostrar:
- bar: 192.168.68.121:9100
- cocina: 192.168.68.120:9100
- asados: 192.168.68.122:9100

### 5. **Probar Funcionalidad**

#### Acceso a Pantallas:
- **Diagnóstico**: `https://macarena.anconclub.com/diagnostico`
- **Pantalla Bar**: `https://macarena.anconclub.com/pantallaCocina?categoria=bar`
- **Pantalla Cocina**: `https://macarena.anconclub.com/pantallaCocina?categoria=cocina`
- **Pantalla Asados**: `https://macarena.anconclub.com/pantallaCocina?categoria=asados`
- **Mesa 1**: `https://macarena.anconclub.com/registroPmesa&ida=1`

#### Flujo de Prueba:
1. Acceder a una mesa
2. Agregar productos al pedido
3. Hacer clic en "Enviar Pedido a Cocina"
4. Verificar que aparezca en la pantalla de cocina correspondiente
5. Marcar como entregado desde la pantalla de cocina
6. Facturar desde la mesa

### 6. **Solución de Problemas Comunes**

#### Error: "Tabla no existe"
```sql
-- Ejecutar script de emergencia
USE ewogjwfm_macarena;
SOURCE sql/emergencia_basico.sql;
```

#### Error: "Archivo no encontrado"
- Verificar que todos los archivos estén subidos
- Verificar permisos de archivos (644)

#### Error: "No se puede conectar a impresora"
- Verificar IPs en tabla `impresoras`
- Verificar conectividad de red
- Probar desde el diagnóstico

#### Error: "Permisos denegados"
- Verificar tipo_usuario en sesión
- Solo administradores pueden modificar pedidos enviados

### 7. **Configuración de Permisos**

**Tipos de Usuario:**
- `1` = Administrador (acceso completo)
- `2` = Mesero (solo sus pedidos en borrador)
- `3` = Cajero (solo pedidos en borrador)
- `4` = Cocina (pantalla de cocina, marcar entregados)

### 8. **Verificación Final**

Ejecuta nuevamente el diagnóstico para confirmar que todo esté funcionando:
```
https://macarena.anconclub.com/diagnostico
```

Debe mostrar ✅ en todos los elementos críticos.

## Funcionalidades Implementadas

### ✅ **Estados de Pedidos**
- Borrador → Enviado → Entregado → Facturado

### ✅ **Impresión Automática**
- Por categorías (bar, cocina, asados)
- En impresoras TCP/IP específicas

### ✅ **Control de Permisos**
- Restricciones por rol de usuario
- Solo administradores pueden modificar pedidos enviados

### ✅ **Pantallas de Cocina**
- Vista en tiempo real de pedidos pendientes
- Marcar como entregado
- Reimpresión de pedidos

### ✅ **Gestión de Mesa**
- Múltiples pedidos por mesa
- Pedidos independientes
- Lista de pedidos pendientes

### ✅ **Descuento de Inventario**
- Automático al marcar como entregado
- Basado en relaciones suministros_productos

## Soporte

Si encuentras problemas:

1. **Ejecutar diagnóstico** primero
2. **Revisar logs** de errores del servidor
3. **Verificar conectividad** de impresoras
4. **Comprobar permisos** de archivos

Para soporte adicional, proporciona:
- Resultado completo del diagnóstico
- Mensaje de error específico
- Pasos para reproducir el problema
