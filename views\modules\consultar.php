<!DOCTYPE html>
<html lang="es">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>Bootstrap 3, from LayoutIt!</title>

    <meta name="description" content="Source code generated using layoutit.com">
    <meta name="author" content="LayoutIt!">

    

    <style>
    #estilo0{color:	#FFF;background:#000	}
		#estilo5{color:	#FFFF00;background:#0000FF;height:50px	}	
	#estilo1{color:#FF0000;background:#000;height:100px	}	
	#estilo2{color:#000;background:#FAD6A5;height:100px	}
	#estilo3{color:#000;background:#ccc;height:40px	}
	#estilo4{color:#000;background:#eee;height:40px	}

	#destino{
		width: 100% !important;
		margin: auto;
		background-color: #fff;
		color: #000; 
		height: 600px;
		overflow-y: scroll;
	}
	#destino2{
		width: 100% !important;
		margin: auto;
		background-color: #e5e5e5;
		/*height: 250px;*/
		overflow-y: scroll;
		height: 470px;
	}
	#estiloMula{color:#000;background:#fff;/*height:40px*/;	}
	.mula{color:#000;background:#fff;font-size: 12px;}
	.er{background:#ffff26;} /*ff7a4d - 00d9a3*/
	.programado{background:#00d9a3;}
	.listap{background:#e5e5e5 }

	.placagm{color:#2ECC71 !important;}

	a{text-decoration:none;color:#fff;} 
	a:hover{text-decoration:none;color:#ffff26;} 

	/*css busqueda*/
	 #estilo0{color:#FFF;background:#000	}

	#estiloMula{color:#000;background:#fff;/*height:40px*/;	}
	.mula{color:#000;background:#fff;font-size: 12px;}
	.er{background:#ffff26;} /*ff7a4d - 00d9a3*/
	.programado{background:#00d9a3;}
	.listap{background:#e5e5e5; }
	.estees{background:#F7CBD9; }
   /*Color amarillo en input*/
	.amarillo {
		background-color: #FFFF00;
		border: 2px solid #A8A8A8;
		color: #000;
		font-family: Verdana, Geneva, Arial, Helvetica, sans-serif;
		font-size: 14px;
	}

	::-webkit-input-placeholder { color: #000; font-weight: bold;font-size:15px; } /* WebKit */
	:-moz-placeholder { color: #000; font-weight: bold;font-size:15px; } /* Firefox 18- */
	::-moz-placeholder { color: #000; font-weight: bold;font-size:15px; } /* Firefox 19+ */
	:-ms-input-placeholder { color: #000;font-weight: bold; font-size:15px; } /* IE 10+ */

    </style>

    <script type="text/javascript">
	

	//=====================  Buscar placa para orden de carga  =================================
	function buscarPunto(variable) {
				alert("Entro en el else de placa");
			$.post("views/modules/ajaxVista.php",{cod:$('#cod').val() // Valor seleccionado
									},function(data){
										alert("Entro en function data");
													$("#destino").html(data);	//Tomo el resultado de pagina e inserto los datos en el combo indicado																				
													})
		
	} // placa	

//---------------------------------------------------------------------
function programacion(inputString,producto) {
		//var inputString=5;
				//alert("Entro a programacion");
		/*$("#destino").load("views/modules/ajaxVista.php", {cod: $("#cod").val()}, function(){
         //alert("recibidos los datos por ajax");		 
     		 });*/			
		$.post("views/modules/ajaxVista.php",{cod:+inputString,producto:+producto},function(data){
																$("#listado").html(data);	//Tomo el resultado de pagina e inserto los datos en el combo indicado																				
																})
	} // Cierre function buscarPunto()
//----------------------------------------------------------------
     		 
function ajaxget(){

    	var conexion;
    	if (window.XMLHttpRequest) {
    		conexion=new XMLHttpRequest();
    	}else{
    		conexion= new ActiveXObject("Microsoft.XMLHTTP");
    	}
    	conexion.onreadystatechange=function(){
    		if (conexion.readyState==4 && conexion.status==200)
    		{
    			document.getElementById("destino").innerHTML=conexion.responseText;
    		}
    	}

    	conexion.open("GET","views/modules/ajaxVista.php",true);
    	conexion.send();
    }

//--------------------------------------------------------------------
function cargarDiv() {
		var inputString=5;
		
		if(inputString < 0) {
			alert("Seleccione una zona valida");
			//$('#suggestions').hide();
		} else {
				alert("Vista valida"+inputString);
			$.post("views/modules/ajaxVista.php",{cod:+inputString},function(data){
																$("#destino").html(data);	//Tomo el resultado de pagina e inserto los datos en el combo indicado																				
																})		
			
		}
	}

	//setTimeout ("ajaxget()", 2000);
	//setTimeout ("programacion('5')", 2000);
	//setTimeout ("cargarDiv()", 2000);
	//setInterval(buscarPunto, 3000);
	function eliminar() {
		document.getElementById('destino').innerHTML='';
	}

	function recargar(){ // Funcion que recarga la pagina
		location.reload();
	}


</script>

  </head>
  <body>
<div class="contaniner">
			<div class="row">
				<div class="col-sm-12" >
					<!--
				  	<iframe cols="15%,*" src="https://www.google.com.co/" name="izquieda"height="660" align="left"	></iframe> -->
				  	    
					 <div class="" style=" align:center;">
					 	<!--<img src="../../img/Logo-FCSB.png"  class="img-responsive" alt="Imagen responsive"/>-->
					 	<form method="post">.

							<table class="" width="350" align="center"     style="border-spacing:  0px;">
								<tr align="center" valign="middle">
									<td colspan="3" align="center">
										<div  style=" align:center;">
											<span class="text"> <b> <h3>Buscar Placa Estado Tractomula</h3> </b></span>
										</div>
									</td>
								</tr>
								<tr >
									<td align="center"><label >Digite la placa:</label></td> 
									 <td >
									 	<input  placeholder="AAA123" class="mayuscula amarillo"  name="placamula" id="placamula" style="width:140px;height:35px "  required>
										<!--<img src="../../img/placa.png"  class="img-responsive" alt="Imagen responsive"/>-->
									 </td>  
									 <td align="center"><input type="submit" value="Buscar" type="text" style="width:80px;height:30px text-align:center;"  ></td>
								</tr>
							</table>	
										
						</form>
						<?php
						$registro = new ControllerUsuario();
						$registro -> buscarMulaGMController();
						?>
				</div>	
								
					
					 <div style="background:#000; align:Right; " > <h3> <a href="ingresar">Iniciar Sesion</a>	</h3></div>
					 		
				</div>
		</div>
	
</div>

  
<div class="row">
	<div class="col-xs-12" id="estilo0"  align="center">
			<h4><b>CAMINOS SIN BARRERAS </b></h4> 
	  </div>
	</div>
 
  </body>
</html>