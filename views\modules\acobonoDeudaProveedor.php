<?php
 if(!$_SESSION["validar"])
	{	header("location:index.php?action=ingresar");	exit();	}
?>
<script type="text/javascript">		
	// ------FACTURAR--------------
	 function buscardeudacliente() 		
		{//alert("Buscar factura funtion"); 
		 var cedula = document.deuda.buscarDeudaCliente.value;
		 if (confirm('confirme  Buscar cedula?')) 
			{//setTimeout('location.href="views/modules/ajaxFactura.php"',500);	
				$("#destino1").load("views/modules/ajaxdeudacliente.php", {cedula: cedula}, function(){});	
						 //alert("recibidos los datos por ajax efectivo"); 						
			}
		} 
	// ------FACTURAR FIN//
 // // ------CAMBIO O VUELTOS 
	function funcion_calcular()
		{
			var valorAbono = document.calculo.valorAbono.value;
			var total = document.calculo.total.value;
			/*					
				var precio = document.calculo.precio.value;
				var cantid = document.calculo.cantid.value;
				var importe = document.calculo.importe.value;
			*/
			var calculo = efectivo-total;
			//alert ('El cambio: ' + calculo);
			document.calculo.cambio.value = calculo;		}  
 // ------CANCELA TODO LOS PRODUCTOS PEDIDO EN LA MESA fin     onkeyup="funcion_calcular();"
</script>	
<div class="row">
    <div class="col-md-12">	  
      	<form method="post" name="calculo">	
			<h1>Abono</h1>
			<table border="1">				
				<tr>
					<th align="right"><label> Valor: </label></th>
					<th><input type="text"  name="valorAbono" autofocus="autofocus" required></th>
				</tr>
					<tr>
					<th>	<label> Descripsion: </label>	</th>
					<th>	<textarea name="descripcion" type="text" rows="3" cols="13"></textarea>	</th>
				</tr>							
			</table>					
			<input type="submit" value="Enviar">
		</form>
    </div>
</div>
<?php 
	$abono = new controllerAbonosProveedor();
	$abono -> abonoProveedorController();
	$tabla=$abono -> buscarAbonosController();
?>