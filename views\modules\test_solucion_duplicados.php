<?php

session_start();

if(!isset($_SESSION["validar"]) || !$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "models/conexion.php";

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Test: Solución Duplicados Implementada</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
</head>
<body>

<div class="container">
    <h2>✅ Solución Permanente Implementada</h2>
    <hr>
    
    <div class="alert alert-success">
        <h4>🎉 ¡Solución ON DUPLICATE KEY UPDATE implementada!</h4>
        <p>Se ha implementado la solución permanente para evitar errores de clave duplicada en <code>producto_vendido_mesa</code>.</p>
    </div>
    
    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">📋 Funciones Modificadas</h3>
        </div>
        <div class="panel-body">
            <h5>✅ Archivos actualizados:</h5>
            <ul>
                <li><strong>models/crudPedidoMesaVendido.php</strong>
                    <ul>
                        <li>✅ <code>registroPmesaModel()</code> - Usa ON DUPLICATE KEY UPDATE</li>
                        <li>✅ <code>addPmesaModel()</code> - Usa ON DUPLICATE KEY UPDATE</li>
                    </ul>
                </li>
                <li><strong>models/crudPedidoMesaProducto.php</strong>
                    <ul>
                        <li>✅ <code>registroPmesaProductoModel()</code> - Usa ON DUPLICATE KEY UPDATE</li>
                    </ul>
                </li>
            </ul>
            
            <h5>🔧 Cambio implementado:</h5>
            <div class="well">
                <code>
                INSERT INTO producto_vendido_mesa (...) VALUES (...)<br>
                <strong style="color: green;">ON DUPLICATE KEY UPDATE</strong><br>
                &nbsp;&nbsp;cantidad = cantidad + VALUES(cantidad),<br>
                &nbsp;&nbsp;fecha_hora = VALUES(fecha_hora),<br>
                &nbsp;&nbsp;mesero = VALUES(mesero)
                </code>
            </div>
        </div>
    </div>
    
    <div class="panel panel-success">
        <div class="panel-heading">
            <h3 class="panel-title">🎯 Beneficios de la Solución</h3>
        </div>
        <div class="panel-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>✅ Antes (Problemático):</h6>
                    <ul>
                        <li>❌ Error de clave duplicada</li>
                        <li>❌ Facturación fallaba</li>
                        <li>❌ Registros duplicados</li>
                        <li>❌ Proceso interrumpido</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>✅ Ahora (Solucionado):</h6>
                    <ul>
                        <li>✅ No más errores de duplicados</li>
                        <li>✅ Facturación fluida</li>
                        <li>✅ Cantidades se suman automáticamente</li>
                        <li>✅ Proceso continuo</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <div class="panel panel-warning">
        <div class="panel-heading">
            <h3 class="panel-title">🧪 Pruebas Recomendadas</h3>
        </div>
        <div class="panel-body">
            <h5>Para verificar que la solución funciona:</h5>
            <ol>
                <li><strong>Agregar productos a una mesa</strong> - Verificar que no hay errores</li>
                <li><strong>Agregar el mismo producto varias veces</strong> - Verificar que suma cantidades</li>
                <li><strong>Intentar facturar</strong> - Verificar que no hay errores de duplicados</li>
                <li><strong>Revisar logs</strong> - Verificar mensajes de ON DUPLICATE KEY UPDATE</li>
            </ol>
            
            <div class="alert alert-info">
                <h6>💡 Cómo funciona ahora:</h6>
                <p>Si intentas agregar un producto que ya existe en el pedido:</p>
                <ul>
                    <li>🔄 <strong>Antes:</strong> Error de clave duplicada</li>
                    <li>✅ <strong>Ahora:</strong> Suma la cantidad al producto existente</li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="panel panel-primary">
        <div class="panel-heading">
            <h3 class="panel-title">📊 Estado Actual del Sistema</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                // Verificar si hay registros en producto_vendido_mesa
                $stmt = Conexion::conectar()->prepare("
                    SELECT 
                        COUNT(*) as total_registros,
                        COUNT(DISTINCT CONCAT(productos_id, '-', mesas_id, '-', pedidos_id)) as combinaciones_unicas
                    FROM producto_vendido_mesa
                ");
                $stmt->execute();
                $stats = $stmt->fetch(PDO::FETCH_ASSOC);
                
                echo "<div class='row'>";
                echo "<div class='col-md-6'>";
                echo "<h6>📊 Estadísticas de producto_vendido_mesa:</h6>";
                echo "<p><strong>Total registros:</strong> {$stats['total_registros']}</p>";
                echo "<p><strong>Combinaciones únicas:</strong> {$stats['combinaciones_unicas']}</p>";
                
                if ($stats['total_registros'] == $stats['combinaciones_unicas']) {
                    echo "<div class='alert alert-success'>";
                    echo "<small>✅ No hay duplicados detectados</small>";
                    echo "</div>";
                } else {
                    echo "<div class='alert alert-warning'>";
                    echo "<small>⚠️ Posibles duplicados detectados</small>";
                    echo "</div>";
                }
                echo "</div>";
                
                // Verificar mesas activas
                echo "<div class='col-md-6'>";
                echo "<h6>🪑 Mesas con productos:</h6>";
                $stmt_mesas = Conexion::conectar()->prepare("
                    SELECT 
                        m.nombre,
                        COUNT(pvm.productos_id) as productos
                    FROM mesas m
                    LEFT JOIN producto_vendido_mesa pvm ON m.id = pvm.mesas_id
                    WHERE pvm.productos_id IS NOT NULL
                    GROUP BY m.id, m.nombre
                    ORDER BY productos DESC
                    LIMIT 5
                ");
                $stmt_mesas->execute();
                $mesas_activas = $stmt_mesas->fetchAll(PDO::FETCH_ASSOC);
                
                if (count($mesas_activas) > 0) {
                    echo "<ul>";
                    foreach ($mesas_activas as $mesa) {
                        echo "<li>{$mesa['nombre']}: {$mesa['productos']} productos</li>";
                    }
                    echo "</ul>";
                } else {
                    echo "<p><em>No hay mesas con productos actualmente</em></p>";
                }
                echo "</div>";
                echo "</div>";
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-default">
        <div class="panel-heading">
            <h3 class="panel-title">🔍 Monitoreo de Logs</h3>
        </div>
        <div class="panel-body">
            <p>Para verificar que la solución está funcionando, revisa los logs del servidor para mensajes como:</p>
            <div class="well">
                <code>
                registroPmesaModel: Ejecutando consulta con ON DUPLICATE KEY UPDATE<br>
                addPmesaModel: Usando INSERT ... ON DUPLICATE KEY UPDATE
                </code>
            </div>
            <p><small>Estos mensajes confirman que las funciones están usando la nueva lógica.</small></p>
        </div>
    </div>
    
    <hr>
    <div class="row">
        <div class="col-md-3">
            <a href="index.php?action=debug_error_facturacion" class="btn btn-primary btn-block">🔙 Debug Error</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=mesa" class="btn btn-info btn-block">🪑 Ir a Mesas</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=registroPmesa&ida=5" class="btn btn-warning btn-block">🧪 Test Mesa 5</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=diagnostico" class="btn btn-success btn-block">📊 Diagnóstico</a>
        </div>
    </div>
</div>

</body>
</html>
