<?php

	#EXTENSIÓN DE CLASES: Los objetos pueden ser extendidos, y pueden heredar propiedades y métodos. Para definir una clase como extensión, debo definir una clase padre, y se utiliza dentro de una clase hija.

	require_once "conexion.php";

	class Datos extends Conexion
	{

			#REGISTRO DE USUARIOS
			#-------------------------------------
				public function registroUsuarioModel($datosModel, $tabla){

					#prepare() Prepara una sentencia SQL para ser ejecutada por el método PDOStatement::execute(). La sentencia SQL puede contener cero o más marcadores de parámetros con nombre (:name) o signos de interrogación (?) por los cuales los valores reales serán sustituidos cuando la sentencia sea ejecutada. Ayuda a prevenir inyecciones SQL eliminando la necesidad de entrecomillar manualmente los parámetros.

					$stmt = Conexion::conectar()->prepare("INSERT INTO $tabla (tipo_persona_id, nombres, apellidos, cedula, celular, usuario, pass) 
						VALUES (:tipo_persona_id,:nombres,:apellidos,:cedula,:celular,:usuario,:password)");	

					#bindParam() Vincula una variable de PHP a un parámetro de sustitución con nombre o de signo de interrogación correspondiente de la sentencia SQL que fue usada para preparar la sentencia.

					$stmt->bindParam(":tipo_persona_id", $datosModel["tipo_persona_id"], PDO::PARAM_INT);
					$stmt->bindParam(":nombres", $datosModel["nombres"], PDO::PARAM_STR);
					$stmt->bindParam(":apellidos", $datosModel["apellidos"], PDO::PARAM_STR);
					$stmt->bindParam(":cedula", $datosModel["cedula"], PDO::PARAM_STR);
					$stmt->bindParam(":celular", $datosModel["celular"], PDO::PARAM_STR);
					$stmt->bindParam(":usuario", $datosModel["usuario"], PDO::PARAM_STR);
					$stmt->bindParam(":password", $datosModel["pass"], PDO::PARAM_STR);

					if($stmt->execute())
						{	return "success"; }

					else{	return "error";	}
					$stmt->close();
				}
			#-------------------------------------

			#REGISTRO DE Orden ingreso de mulas al parqueadero;
			#-------------------------------------
				public function registroOrdenModel($datosModel, $tabla)
				{						
						$stmt = Conexion::conectar();
					try 
						{
							#$fecha_in=date("Y-m-d H:i:s");America/Bogota
							//setlocale(LC_TIME,"es_CO");
							date_default_timezone_set("America/Bogota");
							$fecha_in=strftime("%Y-%m-%d %H:%M:%S");
							#prepare() Prepara una sentencia SQL para ser ejecutada por el método PDOStatement::execute(). La sentencia SQL puede contener cero o más marcadores de parámetros con nombre (:name) o signos de interrogación (?) por los cuales los valores reales serán sustituidos cuando la sentencia sea ejecutada. Ayuda a prevenir inyecciones SQL eliminando la necesidad de entrecomillar manualmente los parámetros.
							$consulta="INSERT INTO $tabla(personas_usuario,cedula,placa_mula,fecha_in,nombre,telefono) VALUES(".$datosModel["usuario"].",'".$datosModel["cedula"]."','".strtoupper($datosModel["placa"])."','$fecha_in','".$datosModel["nombre"]."','".$datosModel["celular"]."')";
							//echo $consulta;							
							$stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
							$stmt->beginTransaction();
							$stmt->exec($consulta);
							$ultimo_id=$stmt->lastInsertId();
							$consulta2="INSERT INTO parqueos_ordenes2(parqueos_ordenes_id,personas_usuario,cedula,placa_mula,fecha_in,nombre,telefono) VALUES(".$ultimo_id.",".$datosModel["usuario"].",'".$datosModel["cedula"]."','".strtoupper($datosModel["placa"])."','$fecha_in','".$datosModel["nombre"]."','".$datosModel["celular"]."')";
							//echo $consulta2;
				  			$stmt->exec($consulta2);
							$stmt->commit();
							return "success";
							$stmt->close();						
						} 
					catch (Exception $e)
						 {
							$stmt->rollBack(); 
							print "Error!: ".$e->getMessage()."</br>";
							return "Error!: ".$e->getMessage()."</br>";
						}			

				}
			#-------------------------------------

			#REGISTRO DE Orden de carga;
			#-------------------------------------
				public function registroOrdenCargaModel($datosModel, $tabla){

						$stmt = Conexion::conectar();
					try {

						$consulta="UPDATE $tabla set transportadoras_id=".$datosModel["transportadoras"].", punto_carga_id=".$datosModel["punto_carga"].", productos_id=".$datosModel["productos"].", fecha_orden_carga='".$datosModel["fecha_orden"]."', persona_orden=".$datosModel["usuario"]." where id=".$datosModel["id"];
						echo $consulta;
						
						$stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
						$stmt->beginTransaction();
						$stmt->exec($consulta);
						$ultimo_id=$stmt->lastInsertId();
						$consulta2="UPDATE parqueos_ordenes2 set transportadoras_id=".$datosModel["transportadoras"].", punto_carga_id=".$datosModel["punto_carga"].", productos_id=".$datosModel["productos"].", fecha_orden_carga='".$datosModel["fecha_orden"]."', persona_orden=".$datosModel["usuario"]." where parqueos_ordenes_id=".$datosModel["id"];
						echo $consulta2;
			  			$stmt->exec($consulta2);
						$stmt->commit();

						return "success";

						$stmt->close();

						
					} catch (Exception $e) {
						$stmt->rollBack(); 
						print "Error!: ".$e->getMessage()."</br>";
						return "Error!: ".$e->getMessage()."</br>";
					}			

				}
			#-------------------------------------
			#REGISTRO DE Programacion Tractomula;
			#-------------------------------------
				public function registroProgramacionModel($datosModel, $tabla){

						$stmt = Conexion::conectar();
					try {

						$consulta="UPDATE $tabla set fecha_programacion='".$datosModel["fecha_programacion"]."', persona_programacion=".$datosModel["usuario"]." where id=".$datosModel["id"];
						echo $consulta;
						
						$stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
						$stmt->beginTransaction();
						$stmt->exec($consulta);
						$ultimo_id=$stmt->lastInsertId();
						$consulta2="UPDATE parqueos_ordenes2 set fecha_programacion='".$datosModel["fecha_programacion"]."', persona_programacion=".$datosModel["usuario"]." where parqueos_ordenes_id=".$datosModel["id"];
						echo $consulta2;
			  			$stmt->exec($consulta2);
						$stmt->commit();

						return "success";

						$stmt->close();

						
					} catch (Exception $e) {
						$stmt->rollBack(); 
						print "Error!: ".$e->getMessage()."</br>";
						return "Error!: ".$e->getMessage()."</br>";
					}			

				}
			#-------------------------------------
			#REGISTRO DE Pago Tractomula;
			#-------------------------------------
				public function registroPagoModel($datosModel, $tabla){

						$stmt = Conexion::conectar();
					try {

						$consulta="UPDATE $tabla set fecha_exit='".$datosModel["fecha_exit"]."', pago=".$datosModel["pago"].", persona_pago=".$datosModel["usuario"]." where id=".$datosModel["id"];
						//echo $consulta;
						
						$stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
						$stmt->beginTransaction();
						$stmt->exec($consulta);
						$ultimo_id=$stmt->lastInsertId();
						$consulta2="UPDATE parqueos_ordenes2 set fecha_exit='".$datosModel["fecha_exit"]."', pago=".$datosModel["pago"].", persona_pago=".$datosModel["usuario"]." where parqueos_ordenes_id=".$datosModel["id"];
						//echo $consulta2;
			  			$stmt->exec($consulta2);
						$stmt->commit();

						return "success";

						$stmt->close();

						
					} catch (Exception $e) {
						$stmt->rollBack(); 
						print "Error!: ".$e->getMessage()."</br>";
						return "Error!: ".$e->getMessage()."</br>";
					}			

				}
			#-------------------------------------

			#INGRESO USUARIO
			#-------------------------------------
				public function ingresoUsuarioModel($datosModel, $tabla){

					$stmt = Conexion::conectar()->prepare("SELECT id, tipo_persona_id,nombres, apellidos, usuario, pass FROM $tabla WHERE usuario = :usuario");	
					$stmt->bindParam(":usuario", $datosModel["usuario"], PDO::PARAM_STR);
					$stmt->execute();

					#fetch(): Obtiene una fila de un conjunto de resultados asociado al objeto PDOStatement. 
					return $stmt->fetch();

					$stmt->close();

				}
			#-------------------------------------
			#VISTA USUARIOS
			#-------------------------------------
				public function vistaUsuariosModel($tabla)
					{

						$stmt = Conexion::conectar()->prepare("SELECT p.id, tp.nombre as usu, p.nombres, p.apellidos, p.cedula, p.celular, p.usuario, p.pass FROM $tabla p, tipo_persona tp where p.tipo_persona_id=tp.id");	
						$stmt->execute();

						#fetchAll(): Obtiene todas las filas de un conjunto de resultados asociado al objeto PDOStatement. 
						return $stmt->fetchAll();

						$stmt->close();

					}
			#-------------------------------------
			#VISTA USUARIOS CONSULTAR
			#-------------------------------------
					public function vistaUsuarioModel($tabla)
					{

						$stmt = Conexion::conectar()->prepare("SELECT p.id, tp.nombre as usu, p.nombres, p.apellidos, p.cedula, p.celular, p.usuario, p.pass FROM $tabla p, tipo_persona tp where p.tipo_persona_id=tp.id");	
						$stmt->execute();

						#fetchAll(): Obtiene todas las filas de un conjunto de resultados asociado al objeto PDOStatement. 
						return $stmt->fetchAll();

						$stmt->close();

					}
			#-------------------------------------
			#EDITAR USUARIO
			#-------------------------------------

				public function editarUsuarioModel($datosModel, $tabla)
					{

						$stmt = Conexion::conectar()->prepare("SELECT p.id, tp.nombre as 'tipo_usuario', p.nombres, p.apellidos, p.cedula, p.celular, p.usuario, p.pass FROM $tabla p, tipo_persona tp WHERE p.id = :id  and p.tipo_persona_id=tp.id");
						$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);	
						$stmt->execute();

						return $stmt->fetch();

						$stmt->close();

					}
			#-------------------------------------
			#Buscar tipo persona
			#-------------------------------------
				public function buscarTipoPersonaModel($tipo_persona,$tabla){

					$consulta=("SELECT id,nombre from $tabla where  nombre<>'conductor'");
					//echo $consulta;
					$stmt = Conexion::conectar()->prepare($consulta);
					$stmt->bindParam(":nombre", $tipo_persona, PDO::PARAM_STR);
					$stmt->execute();

					return $stmt->fetchAll();

					$stmt->close();

				}
			#-------------------------------------
			#Buscar conductor con ajax
			#-------------------------------------------------------
				public function buscarConductorModel($cedula)
					{
						$consulta=("SELECT id,cedula,placa_mula,nombre,telefono from parqueos_ordenes where  cedula= :cedula order by id DESC limit 1");
						//echo $consulta;
						$stmt = Conexion::conectar()->prepare($consulta);
						$stmt->bindParam(":cedula", $cedula, PDO::PARAM_STR);
						$stmt->execute();

						return $stmt->fetch();

						$stmt->close();
					}
			#-------------------------------------------------------------------------
			#Buscar Placa con ajax
			#-------------------------------------------------------
				public function buscarPlacaModel($placa)
					{
						$consulta=("SELECT id,cedula,placa_mula,nombre,telefono from parqueos_ordenes where  placa_mula= :placa order by id DESC limit 1");
						//echo $consulta;
						$stmt = Conexion::conectar()->prepare($consulta);
						$stmt->bindParam(":placa", $placa, PDO::PARAM_STR);
						$stmt->execute();

						return $stmt->fetch();

						$stmt->close();
					}
			#-----------------------------------------------------------------
			#Buscar Placa con ajax para orden de carga
			#-------------------------------------------------------
				public function buscarPlacaOrdenModel($placa)
					{
						$consulta=("SELECT id,cedula,placa_mula,nombre,telefono,fecha_in from parqueos_ordenes where  placa_mula= :placa and fecha_orden_carga IS NULL  order by id DESC limit 1");
						//echo $consulta;
						$stmt = Conexion::conectar()->prepare($consulta);
						$stmt->bindParam(":placa", $placa, PDO::PARAM_STR);
						$stmt->execute();

						return $stmt->fetch();

						$stmt->close();
					}
			#-----------------------------------------------------------------
			#Buscar Placa con ajax para Programacion
			#-------------------------------------------------------
				public function buscarPlacaProgramModel($placa)
					{
						$consulta=("SELECT id,cedula,placa_mula,nombre,telefono,fecha_in from parqueos_ordenes where  placa_mula= :placa and fecha_orden_carga IS NOT NULL and fecha_programacion IS NULL order by id DESC limit 1");
						//echo $consulta;
						$stmt = Conexion::conectar()->prepare($consulta);
						$stmt->bindParam(":placa", $placa, PDO::PARAM_STR);
						$stmt->execute();

						return $stmt->fetch();

						$stmt->close();
					}
			#-----------------------------------------------------------------
			#Buscar Placa con ajax para Pagar parqueo
			#-------------------------------------------------------
				public function buscarPlacaPagarModel($placa)
					{
						$consulta=("SELECT id,cedula,placa_mula,nombre,telefono,fecha_in from parqueos_ordenes where  placa_mula= :placa and fecha_exit IS  NULL and pago IS NULL order by id DESC limit 1");
						//echo $consulta;
						$stmt = Conexion::conectar()->prepare($consulta);
						$stmt->bindParam(":placa", $placa, PDO::PARAM_STR);
						$stmt->execute();

						return $stmt->fetch();

						$stmt->close();
					}
			#-----------------------------------------------------------------
			#Buscar Zona con ajax
			#-------------------------------------------------------
				public function buscarPuntoZonaModel($zona)
					{
						$consulta=("SELECT id,nombre from punto_carga where  zonas_id= :zona ");
						$consulta2=("SELECT id,nombre from punto_carga where  zonas_id= $zona ");
						//echo $consulta;

						$stmt = Conexion::conectar()->prepare($consulta);
						$stmt->bindParam(":zona", $zona, PDO::PARAM_INT);
						$stmt->execute();
						$cant=$stmt->rowCount();
						if ($cant>0) {
							return $stmt->fetchAll();
						}else{
							return 0;
						}						

						$stmt->close();
					}
			#-----------------------------------------------------------------
			#Buscar Punto con ajax para la vista principal
			#-------------------------------------------------------
				public function buscarZonaPuntoModel()
					{
						$consulta=("SELECT pc.id as idp, pc.nombre as punto, pc.zonas_id as idz, z.nombre as zona  FROM parqueos_ordenes po, zonas z, punto_carga pc WHERE po.punto_carga_id = pc.id AND pc.zonas_id = z.id AND po.pago IS NULL AND po.fecha_exit IS NULL  GROUP by  pc.id ");
						//echo $consulta;

						$stmt = Conexion::conectar()->prepare($consulta);
						$stmt->execute();
						$cant=$stmt->rowCount();
						if ($cant>0) {
							return $stmt->fetchAll();
						}else{
							return 0;
						}						

						$stmt->close();
					}
			#----------------------------------------------------------------- 
			#Buscar Mulas por Punto de carga con ajax para la vista principal
			#-----------------------------------------------------------------
				public function buscarPuntoMulaModel($punto)
					{
						$consulta=("SELECT  id, placa_mula as placa,fecha_orden_carga as foc, transportadoras_id as tr  FROM parqueos_ordenes  WHERE  pago IS NULL AND fecha_exit IS NULL and  punto_carga_id=".$punto);
						//echo $consulta;

						$stmt = Conexion::conectar()->prepare($consulta);
						$stmt->execute();
						$cant=$stmt->rowCount();
						if ($cant>0) {
							return $stmt->fetchAll();
						}else{
							return 0;
						}						

						$stmt->close();
					}
			#-----------------------------------------------------------------
			#ACTUALIZAR USUARIO
			#-------------------------------------
				public function actualizarUsuarioModel($datosModel, $tabla){

					$stmt = Conexion::conectar()->prepare("UPDATE $tabla SET tipo_persona_id = :tipo_persona_id, nombres = :nombres, apellidos = :apellidos, cedula = :cedula, celular = :celular, usuario = :usuario, pass = :password WHERE id = :id");

					$stmt->bindParam(":tipo_persona_id", $datosModel["tipo_usuario"], PDO::PARAM_INT);
					$stmt->bindParam(":nombres", $datosModel["nombres"], PDO::PARAM_STR);
					$stmt->bindParam(":apellidos", $datosModel["apellidos"], PDO::PARAM_STR);
					$stmt->bindParam(":cedula", $datosModel["cedula"], PDO::PARAM_STR);
					$stmt->bindParam(":celular", $datosModel["celular"], PDO::PARAM_STR);
					$stmt->bindParam(":usuario", $datosModel["usuario"], PDO::PARAM_STR);
					$stmt->bindParam(":password", $datosModel["pass"], PDO::PARAM_STR);
					$stmt->bindParam(":id", $datosModel["id"], PDO::PARAM_INT);
					if($stmt->execute()){

						return "success";

					}

					else{

						return "error";

					}

					$stmt->close();

				}
			#-------------------------------------
			#BORRAR USUARIO
			#------------------------------------
				public function borrarUsuarioModel($datosModel, $tabla){

					$stmt = Conexion::conectar()->prepare("DELETE FROM $tabla WHERE id = :id");
					$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);

					if($stmt->execute()){

						return "success";

					}

					else{

						return "error";

					}

					$stmt->close();

				}
			#-------------------------------------

			#REGISTRO DE PRODUCTO
			#-------------------------------------
				public function registroProductoModel($datosModel, $tabla)
				{

					#prepare() Prepara una sentencia SQL para ser ejecutada por el método PDOStatement::execute(). La sentencia SQL puede contener cero o más marcadores de parámetros con nombre (:name) o signos de interrogación (?) por los cuales los valores reales serán sustituidos cuando la sentencia sea ejecutada. Ayuda a prevenir inyecciones SQL eliminando la necesidad de entrecomillar manualmente los parámetros.

					$stmt = Conexion::conectar()->prepare("INSERT INTO $tabla (nombre, descripcion) VALUES (:nombres,:descripcion)");	

					#bindParam() Vincula una variable de PHP a un parámetro de sustitución con nombre o de signo de interrogación correspondiente de la sentencia SQL que fue usada para preparar la sentencia.
					
					$stmt->bindParam(":nombres", $datosModel["nombres"], PDO::PARAM_STR);
					$stmt->bindParam(":descripcion", $datosModel["descripcion"], PDO::PARAM_STR);
						

					if($stmt->execute()){

						return "success";

					}

					else{

						return "error";

					}

					$stmt->close();

				}
			#-------------------------------------		
			#VISTA PRODUCTO
			#-------------------------------------
				public function vistaProductosModel($tabla){

					$stmt = Conexion::conectar()->prepare("SELECT id, nombre, descripcion FROM $tabla");	
					$stmt->execute();

					#fetchAll(): Obtiene todas las filas de un conjunto de resultados asociado al objeto PDOStatement. 
					return $stmt->fetchAll();

					$stmt->close();

				}
			#-------------------------------------
			#VISTA PRODUCTO CONSULTA
			#-------------------------------------
				public function vistaProductoModel($tabla){

					$stmt = Conexion::conectar()->prepare("SELECT id, nombre, descripcion FROM $tabla order by nombre");	
					$stmt->execute();

					#fetchAll(): Obtiene todas las filas de un conjunto de resultados asociado al objeto PDOStatement. 
					return $stmt->fetchAll();

					$stmt->close();

				}
			#-------------------------------------
			#EDITAR PRODUCTO
			#-------------------------------------

				public function editarProductoModel($datosModel, $tabla)
					{

						$stmt = Conexion::conectar()->prepare("SELECT id, nombre, descripcion FROM productos WHERE id");
						$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);	
						$stmt->execute();

						return $stmt->fetch();

						$stmt->close();

					}
			#-------------------------------------
			
			#ACTUALIZAR PRODUCTO
			#-------------------------------------
				public function actualizarProductoModel($datosModel, $tabla){

					$stmt = Conexion::conectar()->prepare("UPDATE $tabla SET nombre = :nombre, descripcion = :descripcion WHERE id = :id");

					$stmt->bindParam(":nombre", $datosModel["nombre"], PDO::PARAM_STR);
					$stmt->bindParam(":descripcion", $datosModel["descripcion"], PDO::PARAM_STR);
					$stmt->bindParam(":id", $datosModel["id"], PDO::PARAM_INT);
					if($stmt->execute()){

						return "success";

					}

					else{

						return "error";

					}

					$stmt->close();

				}
			#-------------------------------------

			#REGISTRO DE TRANSPORTADORA
			#-------------------------------------
				public function registroTranspotadoraModel($datosModel, $tabla)
					{
						#prepare() Prepara una sentencia SQL para ser ejecutada por el método PDOStatement::execute(). La sentencia SQL puede contener cero o más marcadores de parámetros con nombre (:name) o signos de interrogación (?) por los cuales los valores reales serán sustituidos cuando la sentencia sea ejecutada. Ayuda a prevenir inyecciones SQL eliminando la necesidad de entrecomillar manualmente los parámetros.

						$stmt = Conexion::conectar()->prepare("INSERT INTO $tabla (nombre, descripcion) VALUES (:nombres,:descripcion)");	

						#bindParam() Vincula una variable de PHP a un parámetro de sustitución con nombre o de signo de interrogación correspondiente de la sentencia SQL que fue usada para preparar la sentencia.
						
						$stmt->bindParam(":nombres", $datosModel["nombre"], PDO::PARAM_STR);
						$stmt->bindParam(":descripcion", $datosModel["descripcion"], PDO::PARAM_STR);
							

						if($stmt->execute())
							{	return "success";	}

						else
							{	return "error";		}

						$stmt->close();

					}
			#--------------------------------------------------------
			#VISTA TRANSPORTADORA
			#-------------------------------------
				public function vistaTransportadorasModel($tabla)
				{
					$stmt = Conexion::conectar()->prepare("SELECT id, nombre, descripcion FROM $tabla");	
					$stmt->execute();
					#fetchAll(): Obtiene todas las filas de un conjunto de resultados asociado al objeto PDOStatement. 
					return $stmt->fetchAll();
					$stmt->close();

				}
			#-------------------------------------
			#VISTA PRODUCTO TRANSPORTADORA
			#-------------------------------------
				public function vistaTransportadoraModel($tabla){

					$stmt = Conexion::conectar()->prepare("SELECT id, nombre, descripcion FROM $tabla");	
					$stmt->execute();

					#fetchAll(): Obtiene todas las filas de un conjunto de resultados asociado al objeto PDOStatement. 
					return $stmt->fetchAll();

					$stmt->close();

				}
			#-------------------------------------
			#EDITAR TRANSPORTADORA
			#-------------------------------------
				public function editarTransportadoraModel($datosModel, $tabla)
					{
						$stmt = Conexion::conectar()->prepare("SELECT id, nombre, descripcion FROM transportadoras WHERE id");
						$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);	
						$stmt->execute();

						return $stmt->fetch();

						$stmt->close();

					}
			#-------------------------------------			
			#ACTUALIZAR TRANSPORTADORA
			#-------------------------------------
				public function actualizarTransportadoraModel($datosModel, $tabla){

					$stmt = Conexion::conectar()->prepare("UPDATE $tabla SET nombre = :nombre, descripcion = :descripcion WHERE id = :id");

					$stmt->bindParam(":nombre", $datosModel["nombre"], PDO::PARAM_STR);
					$stmt->bindParam(":descripcion", $datosModel["descripcion"], PDO::PARAM_STR);
					$stmt->bindParam(":id", $datosModel["id"], PDO::PARAM_INT);
					if($stmt->execute()){

						return "success";

					}

					else{

						return "error";

					}

					$stmt->close();

				}
			

			
			#REGISTRO DE ZONA
			#-------------------------------------			
				public function registroZonaModel($datosModel, $tabla)
					{

						#prepare() Prepara una sentencia SQL para ser ejecutada por el método PDOStatement::execute(). La sentencia SQL puede contener cero o más marcadores de parámetros con nombre (:name) o signos de interrogación (?) por los cuales los valores reales serán sustituidos cuando la sentencia sea ejecutada. Ayuda a prevenir inyecciones SQL eliminando la necesidad de entrecomillar manualmente los parámetros.

						$stmt = Conexion::conectar()->prepare("INSERT INTO $tabla (nombre, descripcion) VALUES (:nombres,:descripcion)");	

						#bindParam() Vincula una variable de PHP a un parámetro de sustitución con nombre o de signo de interrogación correspondiente de la sentencia SQL que fue usada para preparar la sentencia.
						
						$stmt->bindParam(":nombres", $datosModel["nombre"], PDO::PARAM_STR);
						$stmt->bindParam(":descripcion", $datosModel["descripcion"], PDO::PARAM_STR);
							

						if($stmt->execute()){

							return "success";

						}

						else{

							return "error";

						}

						$stmt->close();

					}
			#--------------------------------------------------------
			#VISTA ZONA
			#-------------------------------------
				public function vistaZonasModel($tabla){

					$stmt = Conexion::conectar()->prepare("SELECT id, nombre, descripcion FROM $tabla");	
					$stmt->execute();

					#fetchAll(): Obtiene todas las filas de un conjunto de resultados asociado al objeto PDOStatement. 
					return $stmt->fetchAll();

					$stmt->close();

				}
			#-------------------------------------

			#VISTA CONSULTA ZONA
			#-------------------------------------
				public function vistaZonaModel($tabla){

					$stmt = Conexion::conectar()->prepare("SELECT id, nombre, descripcion FROM $tabla");	
					$stmt->execute();

					#fetchAll(): Obtiene todas las filas de un conjunto de resultados asociado al objeto PDOStatement. 
					return $stmt->fetchAll();

					$stmt->close();

				}
			#-------------------------------------

			#EDITAR ZONA
			#-------------------------------------
				public function editarZonaModel($datosModel, $tabla)
					{
						$stmt = Conexion::conectar()->prepare("SELECT id, nombre, descripcion FROM zonas WHERE id");
						$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);	
						$stmt->execute();

						return $stmt->fetch();

						$stmt->close();

					}
			#-------------------------------------			
			#ACTUALIZAR ZONA
			#-------------------------------------
				public function actualizarZonaModel($datosModel, $tabla){

					$stmt = Conexion::conectar()->prepare("UPDATE $tabla SET nombre = :nombre, descripcion = :descripcion WHERE id = :id");

					$stmt->bindParam(":nombre", $datosModel["nombre"], PDO::PARAM_STR);
					$stmt->bindParam(":descripcion", $datosModel["descripcion"], PDO::PARAM_STR);
					$stmt->bindParam(":id", $datosModel["id"], PDO::PARAM_INT);
					
					if($stmt->execute())
						{	return "success";	}
					else
						{	return "error";		}
					$stmt->close();
				}
			#---------------------------------------------------			
			
			
			#VISTA ORDEN
			#-------------------------------------
				public function vistaOrdensModel($tabla){

					$stmt = Conexion::conectar()->prepare("SELECT id, personas_id, productos_id, punto_carga_id, fecha_in, fecha_orden_carga,fecha_exit FROM $tabla");	
					$stmt->execute();

					#fetchAll(): Obtiene todas las filas de un conjunto de resultados asociado al objeto PDOStatement. 
					return $stmt->fetchAll();

					$stmt->close();

				}
			#-------------------------------------
			#VISTA CONSULTA ORDEN
			#-------------------------------------
				public function vistaOrdenModel($tabla){

					$stmt = Conexion::conectar()->prepare("SELECT id, personas_id, productos_id, punto_carga_id, fecha_in, fecha_orden_carga,fecha_exit FROM $tabla");	
					$stmt->execute();

					#fetchAll(): Obtiene todas las filas de un conjunto de resultados asociado al objeto PDOStatement. 
					return $stmt->fetchAll();

					$stmt->close();

				}
			#-------------------------------------
			#EDITAR ORDEN
			#-------------------------------------
				public function editarTOrdenModel($datosModel, $tabla){

					$stmt = Conexion::conectar()->prepare("SELECT id, personas_id, productos_id, punto_carga_id, fecha_in, fecha_orden_carga, fecha_exit FROM $tabla WHERE id = :id");
					$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);	
					$stmt->execute();

					return $stmt->fetch();

					$stmt->close();

				}
			#-------------------------------------
			#ACTUALIZAR ORDEN
			#-------------------------------------
				public function actualizarOrdenModel($datosModel, $tabla){

					$stmt = Conexion::conectar()->prepare("UPDATE $tabla SET  id=:id, personas_id=:personas_id, productos_id=:productos_id, punto_carga_id=:punto_carga_id, fecha_in=:fecha_in, fecha_orden_carga=:fecha_orden_carga, fecha_exit=:fecha_exit WHERE id = :id");
					
					#$stmt->bindParam(":id", $datosModel["id"], PDO::PARAM_STR);
					$stmt->bindParam(":personas_id", $datosModel["personas_id"], PDO::PARAM_STR);
					$stmt->bindParam(":productos_id", $datosModel["productos_id"], PDO::PARAM_STR);
					$stmt->bindParam(":punto_carga_id", $datosModel["punto_carga_id"], PDO::PARAM_STR);
					$stmt->bindParam(":fecha_in", $datosModel["fecha_in"], PDO::PARAM_STR);
					$stmt->bindParam(":fecha_orden_carga", $datosModel["fecha_orden_carga"], PDO::PARAM_STR);
					$stmt->bindParam(":fecha_exit", $datosModel["fecha_exit"], PDO::PARAM_STR);
					
					if($stmt->execute()){

						return "success";

					}

					else{

						return "error";

					}

					$stmt->close();

				}
			#-------------------------------------		

			#REGISTRO DE MULA
			#-------------------------------------
				public function registroMulaModel($datosModel, $tabla)
					{
						#prepare() Prepara una sentencia SQL para ser ejecutada por el método PDOStatement::execute(). La sentencia SQL puede contener cero o más marcadores de parámetros con nombre (:name) o signos de interrogación (?) por los cuales los valores reales serán sustituidos cuando la sentencia sea ejecutada. Ayuda a prevenir inyecciones SQL eliminando la necesidad de entrecomillar manualmente los parámetros.
						$consulta="INSERT INTO $tabla (transportadoras_id,placa) VALUES (:transportadora_id,:placa)";
						$stmt = Conexion::conectar()->prepare($consulta);	
						#bindParam() Vincula una variable de PHP a un parámetro de sustitución con nombre o de signo de interrogación correspondiente de la sentencia SQL que fue usada para preparar la sentencia.

						$stmt->bindParam(":transportadora_id", $datosModel["transportadora_id"], PDO::PARAM_INT);
						$stmt->bindParam(":placa", $datosModel["placa"], PDO::PARAM_STR);		

						if($stmt->execute())
							{	return "success";	}
						else{	return "error";		}

						$stmt->close();
					}

			#INGRESO MULA
			#-------------------------------------
				public function ingresoMulaModel($datosModel, $tabla)
				{
					$stmt = Conexion::conectar()->prepare("SELECT placa FROM $tabla WHERE placa = :placa");	
					$stmt->bindParam(":placa", $datosModel["placa"], PDO::PARAM_STR);
					$stmt->execute();

					#fetch(): Obtiene una fila de un conjunto de resultados asociado al objeto PDOStatement. 
					return $stmt->fetch();

					$stmt->close();

				}

			#VISTA MULA
			#-------------------------------------
				public function vistaMulasModel($tabla)
					{
						$consulta="SELECT m.id, m.transportadoras_id, t.nombre as transportadora, m.placa FROM $tabla m, transportadoras t where m.transportadoras_id=t.id ";
						//echo $consulta;
						$stmt = Conexion::conectar()->prepare($consulta);
						$stmt->execute();
						return $stmt->fetchAll();
						$stmt->close();

					}

			#EDITAR MULA
			#-------------------------------------
				public function editarMulaModel( $datosMula,$tabla)
					{
						$consulta="SELECT id,transportadoras_id,placa FROM $tabla WHERE id= :id ";
						//echo $consulta;
						$stmt = Conexion::conectar()->prepare($consulta);
						$stmt->bindParam(":id", $datosMula, PDO::PARAM_INT);	
						$stmt->execute();

						return $stmt->fetch();

						$stmt->close();
					}
				#-------------------------------------------------------

			# Buscar Transportadoras ---------------------------------
				public function buscarTransportadoraModel($tabla)
					{
						$consulta=("SELECT id,nombre from $tabla");
						$stmt = Conexion::conectar()->prepare($consulta);
						$stmt->execute();
						return $stmt->fetchAll();
						$stmt->close();
					}
			#-------------------------------------------------------

			#ACTUALIZAR MULA
			#-------------------------------------
				public function actualizarMulaModel($datosModel, $tabla){

					$consulta="UPDATE $tabla SET transportadoras_id = :transportadora, placa = :placa  WHERE id = :id";
					//echo $consulta;
					$stmt = Conexion::conectar()->prepare($consulta);

					$stmt->bindParam(":transportadora", $datosModel["transportadora"], PDO::PARAM_INT);
					$stmt->bindParam(":placa", $datosModel["placa"], PDO::PARAM_STR);
					$stmt->bindParam(":id", $datosModel["id"], PDO::PARAM_INT);
					
					if($stmt->execute())
						{	return "success";	}

					else
						{	return "error";	}

					$stmt->close();
				}
			#----------------------------------------
			

			#Lista Zona de carga
			#-------------------------------------
				public function listaZonaCargaModel($tabla){

					$stmt = Conexion::conectar()->prepare("SELECT id,nombre from $tabla  ");
					
					if($stmt->execute()){

						return $stmt->fetchAll();
					}else{

						return "error";
					}

					$stmt->close();

				}
			#-----------------------------------------------------------------Transportadoras
			#Lista de Productos
			#-------------------------------------
				public function listaProductosModel($tabla){

					$stmt = Conexion::conectar()->prepare("SELECT id,nombre from $tabla  ");
					
					if($stmt->execute()){

						return $stmt->fetchAll();
					}else{

						return "error";
					}

					$stmt->close();

				}
			#-----------------------------------------------------------------
			#Lista de Transportadoras
			#-------------------------------------
				public function listaTransportadorasModel($tabla){

					$stmt = Conexion::conectar()->prepare("SELECT id,nombre from $tabla order by nombre ");
					
					if($stmt->execute()){

						return $stmt->fetchAll();
					}else{

						return "error";
					}

					$stmt->close();

				}
			#-----------------------------------------------------------------
			#Buscar zonas de carga carga
			#-------------------------------------
				public function buscarZonasModel($tabla){

					$consulta=("SELECT id,nombre from $tabla");
					$stmt = Conexion::conectar()->prepare($consulta);
					$stmt->execute();

					return $stmt->fetchAll();

					$stmt->close();

				}
			#------------------------------------------------------------------------
			#Buscar zonas por puntos de carga
			#-------------------------------------
				public function buscarZonasVistaModel($id){

					$consulta=("SELECT id,nombre from zonas where id=".$id);
					$stmt = Conexion::conectar()->prepare($consulta);
					$stmt->execute();

					$result=$stmt->fetch();

					return $result;

					$stmt->close();

				}
			#------------------------------------------------------------------------

			#Registro Punto de carga
			#------------------------------------
				public function registroPuntoCargaModel($datos,$tabla){
					
					$consulta="INSERT INTO $tabla (zonas_id, nombre,direccion, descripcion) VALUES (:zona,:nombre,:direccion,:descripcion)";
					$stmt = Conexion::conectar()->prepare($consulta);	

					#bindParam() Vincula una variable de PHP a un parámetro de sustitución con nombre o de signo de interrogación correspondiente de la sentencia SQL que fue usada para preparar la sentencia.
					
					$stmt->bindParam(":zona", $datos["zona"], PDO::PARAM_INT);
					$stmt->bindParam(":nombre", $datos["nombre"], PDO::PARAM_STR);
					$stmt->bindParam(":direccion", $datos["direccion"], PDO::PARAM_STR);
					$stmt->bindParam(":descripcion", $datos["descripcion"], PDO::PARAM_STR);
					
					if($stmt->execute()){

						return "success";
						
					}else{

						return "error";
					}

					$stmt->close();
				}

			#VISTA puntos de carga
			#-------------------------------------

				public function vistaPuntosModel($tabla){

					$stmt = Conexion::conectar()->prepare("SELECT pc.id ,z.nombre as zona,pc.zonas_id as zonaid, pc.nombre,pc.direccion, pc.descripcion FROM $tabla pc, zonas z where pc.zonas_id=z.id");	
					$stmt->execute();

					#fetchAll(): Obtiene todas las filas de un conjunto de resultados asociado al objeto PDOStatement. 
					return $stmt->fetchAll();

					$stmt->close();

				}

			#actualizar Punto model
			#-------------------------------------
				public function actualizarPuntoModel($datosModel,$tabla){
					$stmt = Conexion::conectar()->prepare("UPDATE $tabla SET zonas_id = :zonas_id, nombre = :nombre, direccion = :direccion, descripcion = :descripcion WHERE id = :id");

					$stmt->bindParam(":zonas_id", $datosModel["zona_id"], PDO::PARAM_INT);
					$stmt->bindParam(":nombre", $datosModel["nombre"], PDO::PARAM_STR);
					$stmt->bindParam(":direccion", $datosModel["direccion"], PDO::PARAM_STR);
					$stmt->bindParam(":descripcion", $datosModel["descripcion"], PDO::PARAM_STR);
					$stmt->bindParam(":id", $datosModel["id"], PDO::PARAM_INT);

					if($stmt->execute()){
						return "success";
					}else{
						return "error";
					}

					$stmt->close();
				}

				#VISTA Editar puntos
			#-------------------------------------	
				public function editarPuntoModel($datosModel, $tabla){
					$consulta="SELECT id,zonas_id,nombre,direccion,descripcion FROM $tabla WHERE id= :id ";
					//echo $consulta;
					$stmt = Conexion::conectar()->prepare($consulta);
					$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);	
					$stmt->execute();

					return $stmt->fetch();
					$stmt->close();
				}			


			

			#factura
		#-----------------------------------------------
			public function facturasModel($tabla)
				{	$stmt = Conexion::conectar()->prepare("SELECT id,nombre,precio,hora_inicio,hora_final from $tabla ");	
					$stmt->execute();
					#fetchAll(): Obtiene todas las filas de un conjunto de resultados asociado al objeto PDOStatement. 
					return $stmt->fetchAll();

					$stmt->close();						
				}
		#------------------------------------------------------------------
           public function buscarTarifasModel($tabla)
                   {
                           $consulta=("SELECT id,nombre, precio from $tabla");
                           $stmt = Conexion::conectar()->prepare($consulta);
                           $stmt->execute();
                           return $stmt->fetchAll();
                           $stmt->close();
                   }
  		 #-------------------------------------------	

		#-----------------------------------------------


	}
?>