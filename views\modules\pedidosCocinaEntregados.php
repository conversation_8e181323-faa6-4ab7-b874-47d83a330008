<?php

session_start();

if(!$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

// Verificar permisos (administradores y personal de cocina)
if($_SESSION["tipo_usuario"] != 1 && $_SESSION["tipo_usuario"] != 4) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>Acceso Denegado</h4>";
    echo "<p>No tiene permisos para acceder a esta sección.</p>";
    echo "</div>";
    exit();
}

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Cocina - Pedidos Entregados Hoy</title>
    <style>
        .panel-success .panel-heading {
            background-color: #5cb85c !important;
            border-color: #4cae4c !important;
            color: white !important;
        }
        .panel-info .panel-heading {
            background-color: #5bc0de !important;
            border-color: #46b8da !important;
            color: white !important;
        }
        .panel-warning .panel-heading {
            background-color: #f0ad4e !important;
            border-color: #eea236 !important;
            color: white !important;
        }
        .estadisticas-header {
            margin-bottom: 30px;
        }
        .pedido-entregado {
            border-left: 5px solid #5cb85c;
            margin-bottom: 15px;
        }
        .refresh-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        .resumen-dia {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .hora-entrega {
            font-weight: bold;
            color: #5cb85c;
        }
        .productos-detalle {
            background-color: #f9f9f9;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>

<div class="refresh-btn">
    <button onclick="location.reload()" class="btn btn-info btn-sm">
        <i class="glyphicon glyphicon-refresh"></i> Actualizar
    </button>
</div>

<?php

require_once "controllers/controllerPedidosCategorias.php";

$controller = new ControllerPedidosCategorias();

// Mostrar vista de pedidos entregados del día
$controller->vistaPedidosEntregadosDiaController('cocina');

?>

<script>
// Función para exportar reporte del día
function exportarReporte() {
    var fecha = new Date().toLocaleDateString('es-ES');
    var contenido = document.querySelector('.container-fluid').innerHTML;

    var ventana = window.open('', '_blank');
    ventana.document.write('<!DOCTYPE html>');
    ventana.document.write('<html>');
    ventana.document.write('<head>');
    ventana.document.write('<title>Reporte Cocina - ' + fecha + '</title>');
    ventana.document.write('<style>');
    ventana.document.write('body { font-family: Arial, sans-serif; margin: 20px; }');
    ventana.document.write('.panel { border: 1px solid #ddd; margin: 10px 0; }');
    ventana.document.write('.panel-heading { background: #f5f5f5; padding: 10px; font-weight: bold; }');
    ventana.document.write('.panel-body { padding: 10px; }');
    ventana.document.write('@media print { .btn { display: none; } .refresh-btn { display: none; } }');
    ventana.document.write('</style>');
    ventana.document.write('</head>');
    ventana.document.write('<body>');
    ventana.document.write('<h1>🍳 Reporte de Cocina - ' + fecha + '</h1>');
    ventana.document.write(contenido);
    ventana.document.write('</body>');
    ventana.document.write('</html>');
    ventana.document.close();

    // Imprimir después de un pequeño delay
    setTimeout(function() {
        ventana.print();
    }, 500);
}

// Agregar botón de exportar si hay pedidos
document.addEventListener('DOMContentLoaded', function() {
    var panelBody = document.querySelector('.panel-default .panel-body');
    if (panelBody && panelBody.children.length > 0) {
        var exportBtn = document.createElement('button');
        exportBtn.className = 'btn btn-primary';
        exportBtn.innerHTML = '<i class="glyphicon glyphicon-download"></i> Exportar Reporte';
        exportBtn.onclick = exportarReporte;
        
        var btnContainer = document.querySelector('.row .col-md-12');
        if (btnContainer) {
            btnContainer.appendChild(exportBtn);
        }
    }
});
</script>
