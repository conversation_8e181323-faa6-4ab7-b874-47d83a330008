-- Script de migración segura para ewogjwfm_macarena
-- Ejecutar paso a paso para verificar cada cambio

USE ewogjwfm_macarena;

-- Paso 1: Verificar estructura actual de la tabla pedidos
SELECT 'Verificando estructura actual de pedidos...' as mensaje;
DESCRIBE pedidos;

-- Paso 2: Agregar columnas una por una (solo si no existen)

-- Agregar estado si no existe
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM information_schema.columns 
     WHERE table_schema = 'ewogjwfm_macarena' 
     AND table_name = 'pedidos' 
     AND column_name = 'estado') = 0,
    'ALTER TABLE pedidos ADD COLUMN estado ENUM(''borrador'', ''enviado'', ''entregado'', ''facturado'') DEFAULT ''borrador'' AFTER facturado',
    'SELECT ''Columna estado ya existe'' as mensaje'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Agregar fecha_envio si no existe
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM information_schema.columns 
     WHERE table_schema = 'ewogjwfm_macarena' 
     AND table_name = 'pedidos' 
     AND column_name = 'fecha_envio') = 0,
    'ALTER TABLE pedidos ADD COLUMN fecha_envio DATETIME NULL AFTER estado',
    'SELECT ''Columna fecha_envio ya existe'' as mensaje'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Agregar fecha_entrega si no existe
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM information_schema.columns 
     WHERE table_schema = 'ewogjwfm_macarena' 
     AND table_name = 'pedidos' 
     AND column_name = 'fecha_entrega') = 0,
    'ALTER TABLE pedidos ADD COLUMN fecha_entrega DATETIME NULL AFTER fecha_envio',
    'SELECT ''Columna fecha_entrega ya existe'' as mensaje'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Agregar usuario_envio si no existe
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM information_schema.columns 
     WHERE table_schema = 'ewogjwfm_macarena' 
     AND table_name = 'pedidos' 
     AND column_name = 'usuario_envio') = 0,
    'ALTER TABLE pedidos ADD COLUMN usuario_envio BIGINT(20) NULL AFTER fecha_entrega',
    'SELECT ''Columna usuario_envio ya existe'' as mensaje'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Agregar usuario_entrega si no existe
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM information_schema.columns 
     WHERE table_schema = 'ewogjwfm_macarena' 
     AND table_name = 'pedidos' 
     AND column_name = 'usuario_entrega') = 0,
    'ALTER TABLE pedidos ADD COLUMN usuario_entrega BIGINT(20) NULL AFTER usuario_envio',
    'SELECT ''Columna usuario_entrega ya existe'' as mensaje'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Agregar numero_pedido si no existe
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM information_schema.columns 
     WHERE table_schema = 'ewogjwfm_macarena' 
     AND table_name = 'pedidos' 
     AND column_name = 'numero_pedido') = 0,
    'ALTER TABLE pedidos ADD COLUMN numero_pedido VARCHAR(20) NULL AFTER usuario_entrega',
    'SELECT ''Columna numero_pedido ya existe'' as mensaje'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Agregar mesa_id si no existe
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM information_schema.columns 
     WHERE table_schema = 'ewogjwfm_macarena' 
     AND table_name = 'pedidos' 
     AND column_name = 'mesa_id') = 0,
    'ALTER TABLE pedidos ADD COLUMN mesa_id BIGINT(20) NULL AFTER mesero_id',
    'SELECT ''Columna mesa_id ya existe'' as mensaje'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Agregar fecha_pedido si no existe
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM information_schema.columns 
     WHERE table_schema = 'ewogjwfm_macarena' 
     AND table_name = 'pedidos' 
     AND column_name = 'fecha_pedido') = 0,
    'ALTER TABLE pedidos ADD COLUMN fecha_pedido DATETIME DEFAULT CURRENT_TIMESTAMP AFTER mesa_id',
    'SELECT ''Columna fecha_pedido ya existe'' as mensaje'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Paso 3: Agregar índices si no existen
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM information_schema.statistics 
     WHERE table_schema = 'ewogjwfm_macarena' 
     AND table_name = 'pedidos' 
     AND index_name = 'idx_estado') = 0,
    'ALTER TABLE pedidos ADD INDEX idx_estado (estado)',
    'SELECT ''Índice idx_estado ya existe'' as mensaje'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM information_schema.statistics 
     WHERE table_schema = 'ewogjwfm_macarena' 
     AND table_name = 'pedidos' 
     AND index_name = 'idx_mesa_id') = 0,
    'ALTER TABLE pedidos ADD INDEX idx_mesa_id (mesa_id)',
    'SELECT ''Índice idx_mesa_id ya existe'' as mensaje'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Paso 4: Verificar estructura final
SELECT 'Estructura final de la tabla pedidos:' as mensaje;
DESCRIBE pedidos;

-- Paso 5: Crear tabla de historial si no existe
CREATE TABLE IF NOT EXISTS pedidos_historial (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  pedido_id BIGINT(20) NOT NULL,
  estado_anterior ENUM('borrador', 'enviado', 'entregado', 'facturado') NULL,
  estado_nuevo ENUM('borrador', 'enviado', 'entregado', 'facturado') NOT NULL,
  usuario_id BIGINT(20) NOT NULL,
  fecha_cambio DATETIME DEFAULT CURRENT_TIMESTAMP,
  observaciones TEXT NULL,
  PRIMARY KEY (id),
  INDEX idx_pedido_id (pedido_id),
  INDEX idx_fecha_cambio (fecha_cambio)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

-- Paso 6: Crear tabla de reimpresiones si no existe
CREATE TABLE IF NOT EXISTS pedidos_reimpresiones (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  pedido_id BIGINT(20) NOT NULL,
  categoria ENUM('bar', 'cocina', 'asados') NOT NULL,
  usuario_id BIGINT(20) NOT NULL,
  fecha_reimpresion DATETIME DEFAULT CURRENT_TIMESTAMP,
  motivo VARCHAR(255) NULL,
  PRIMARY KEY (id),
  INDEX idx_pedido_id (pedido_id),
  INDEX idx_fecha_reimpresion (fecha_reimpresion)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

-- Paso 7: Verificar si la tabla impresoras existe, si no, crearla
CREATE TABLE IF NOT EXISTS impresoras (
  id INT(11) NOT NULL AUTO_INCREMENT,
  categoria ENUM('bar','cocina','asados') NOT NULL,
  ip VARCHAR(20) NOT NULL,
  puerto INT(11) NOT NULL,
  PRIMARY KEY (id),
  UNIQUE KEY categoria (categoria)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;

-- Insertar datos de impresoras si no existen
INSERT IGNORE INTO impresoras (categoria, ip, puerto) VALUES
('bar', '**************', 9100),
('cocina', '**************', 9100),
('asados', '**************', 9100);

-- Paso 8: Migrar datos existentes
UPDATE pedidos SET estado = 'facturado' WHERE facturado = 's' AND estado IS NULL;
UPDATE pedidos SET estado = 'borrador' WHERE facturado = 'n' AND estado IS NULL;

-- Generar números de pedido para registros existentes
UPDATE pedidos SET numero_pedido = CONCAT('P', LPAD(id, 6, '0')) WHERE numero_pedido IS NULL;

-- Migrar mesa_id desde producto_vendido_mesa si está vacío
UPDATE pedidos p 
SET mesa_id = (
    SELECT DISTINCT pvm.mesas_id 
    FROM producto_vendido_mesa pvm 
    WHERE pvm.pedidos_id = p.id 
    LIMIT 1
) 
WHERE p.mesa_id IS NULL;

SELECT 'Migración completada exitosamente' as resultado;
