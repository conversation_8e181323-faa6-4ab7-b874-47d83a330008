<?php

session_start();

if(!isset($_SESSION["validar"]) || !$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "models/conexion.php";

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Crear Tabla Entregas por Categoría</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
</head>
<body>

<div class="container">
    <h2>🔧 Crear: Tabla de Entregas por Categoría</h2>
    <hr>
    
    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">📋 Nueva Funcionalidad: Entregas Independientes por Área</h3>
        </div>
        <div class="panel-body">
            <p><strong>Problema actual:</strong> Cuando BAR marca un pedido como entregado, también marca los productos de COCINA como entregados.</p>
            <p><strong>Solución:</strong> Crear una tabla para rastrear entregas por categoría independientemente.</p>
            
            <h4>Nueva tabla: pedidos_entregas_categoria</h4>
            <ul>
                <li><strong>pedido_id:</strong> ID del pedido</li>
                <li><strong>categoria:</strong> bar, cocina, asados</li>
                <li><strong>estado:</strong> pendiente, entregado</li>
                <li><strong>fecha_entrega:</strong> Cuándo se entregó</li>
                <li><strong>usuario_entrega:</strong> Quién lo entregó</li>
            </ul>
        </div>
    </div>
    
    <div class="panel panel-warning">
        <div class="panel-heading">
            <h3 class="panel-title">🧪 Crear Tabla</h3>
        </div>
        <div class="panel-body">
            <?php
            if (isset($_POST['crear_tabla'])) {
                try {
                    $sql = "
                    CREATE TABLE IF NOT EXISTS pedidos_entregas_categoria (
                        id bigint(20) NOT NULL AUTO_INCREMENT,
                        pedido_id bigint(20) NOT NULL,
                        categoria enum('bar','cocina','asados') NOT NULL,
                        estado enum('pendiente','entregado') DEFAULT 'pendiente',
                        fecha_entrega datetime DEFAULT NULL,
                        usuario_entrega bigint(20) DEFAULT NULL,
                        fecha_creacion datetime DEFAULT current_timestamp(),
                        PRIMARY KEY (id),
                        UNIQUE KEY unique_pedido_categoria (pedido_id, categoria),
                        KEY idx_pedido_id (pedido_id),
                        KEY idx_categoria (categoria),
                        KEY idx_estado (estado),
                        FOREIGN KEY (pedido_id) REFERENCES pedidos(id) ON DELETE CASCADE,
                        FOREIGN KEY (usuario_entrega) REFERENCES personas(id)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci
                    ";
                    
                    $stmt = Conexion::conectar()->prepare($sql);
                    $resultado = $stmt->execute();
                    
                    if ($resultado) {
                        echo "<div class='alert alert-success'>";
                        echo "<h4>✅ Tabla creada exitosamente</h4>";
                        echo "<p>La tabla 'pedidos_entregas_categoria' ha sido creada correctamente.</p>";
                        echo "</div>";
                        
                        // Verificar la estructura
                        $stmt_desc = Conexion::conectar()->prepare("DESCRIBE pedidos_entregas_categoria");
                        $stmt_desc->execute();
                        $estructura = $stmt_desc->fetchAll(PDO::FETCH_ASSOC);
                        
                        echo "<h4>Estructura de la tabla:</h4>";
                        echo "<table class='table table-condensed'>";
                        echo "<thead><tr><th>Campo</th><th>Tipo</th><th>Null</th><th>Key</th><th>Default</th></tr></thead>";
                        echo "<tbody>";
                        foreach ($estructura as $campo) {
                            echo "<tr>";
                            echo "<td><strong>{$campo['Field']}</strong></td>";
                            echo "<td>{$campo['Type']}</td>";
                            echo "<td>{$campo['Null']}</td>";
                            echo "<td>{$campo['Key']}</td>";
                            echo "<td>{$campo['Default']}</td>";
                            echo "</tr>";
                        }
                        echo "</tbody></table>";
                        
                    } else {
                        echo "<div class='alert alert-danger'>";
                        echo "<h4>❌ Error al crear la tabla</h4>";
                        echo "<p>No se pudo crear la tabla. Revisar permisos de base de datos.</p>";
                        echo "</div>";
                    }
                    
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>";
                    echo "<h4>❌ Excepción capturada</h4>";
                    echo "<p>Error: " . $e->getMessage() . "</p>";
                    echo "</div>";
                }
            }
            ?>
            
            <form method="POST">
                <button type="submit" name="crear_tabla" class="btn btn-warning btn-lg">
                    🔧 Crear Tabla pedidos_entregas_categoria
                </button>
            </form>
        </div>
    </div>
    
    <div class="panel panel-success">
        <div class="panel-heading">
            <h3 class="panel-title">🧪 Poblar Tabla con Datos Existentes</h3>
        </div>
        <div class="panel-body">
            <?php
            if (isset($_POST['poblar_tabla'])) {
                try {
                    // Verificar que la tabla existe
                    $stmt_check = Conexion::conectar()->prepare("SHOW TABLES LIKE 'pedidos_entregas_categoria'");
                    $stmt_check->execute();
                    $tabla_existe = $stmt_check->fetch();
                    
                    if (!$tabla_existe) {
                        echo "<div class='alert alert-danger'>❌ Primero debe crear la tabla</div>";
                    } else {
                        // Obtener todos los pedidos enviados con sus categorías
                        $stmt = Conexion::conectar()->prepare("
                            SELECT DISTINCT p.id as pedido_id, pr.categoria
                            FROM pedidos p
                            INNER JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
                            INNER JOIN productos pr ON pvm.productos_id = pr.id
                            WHERE p.estado = 'enviado'
                            ORDER BY p.id, pr.categoria
                        ");
                        $stmt->execute();
                        $pedidos_categorias = $stmt->fetchAll(PDO::FETCH_ASSOC);
                        
                        $insertados = 0;
                        $errores = 0;
                        
                        foreach ($pedidos_categorias as $pc) {
                            try {
                                $stmt_insert = Conexion::conectar()->prepare("
                                    INSERT IGNORE INTO pedidos_entregas_categoria 
                                    (pedido_id, categoria, estado) 
                                    VALUES (?, ?, 'pendiente')
                                ");
                                $resultado = $stmt_insert->execute([$pc['pedido_id'], $pc['categoria']]);
                                
                                if ($resultado && $stmt_insert->rowCount() > 0) {
                                    $insertados++;
                                }
                            } catch (Exception $e) {
                                $errores++;
                            }
                        }
                        
                        echo "<div class='alert alert-success'>";
                        echo "<h4>✅ Tabla poblada exitosamente</h4>";
                        echo "<p><strong>Registros insertados:</strong> {$insertados}</p>";
                        echo "<p><strong>Errores:</strong> {$errores}</p>";
                        echo "</div>";
                        
                        // Mostrar resumen
                        $stmt_resumen = Conexion::conectar()->prepare("
                            SELECT categoria, COUNT(*) as total
                            FROM pedidos_entregas_categoria
                            GROUP BY categoria
                        ");
                        $stmt_resumen->execute();
                        $resumen = $stmt_resumen->fetchAll(PDO::FETCH_ASSOC);
                        
                        echo "<h4>Resumen por categoría:</h4>";
                        echo "<table class='table table-striped'>";
                        echo "<thead><tr><th>Categoría</th><th>Total Registros</th></tr></thead>";
                        echo "<tbody>";
                        foreach ($resumen as $r) {
                            echo "<tr><td><span class='label label-info'>{$r['categoria']}</span></td><td>{$r['total']}</td></tr>";
                        }
                        echo "</tbody></table>";
                    }
                    
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>";
                    echo "<h4>❌ Error al poblar la tabla</h4>";
                    echo "<p>Error: " . $e->getMessage() . "</p>";
                    echo "</div>";
                }
            }
            ?>
            
            <form method="POST">
                <button type="submit" name="poblar_tabla" class="btn btn-success btn-lg">
                    📊 Poblar Tabla con Datos Existentes
                </button>
            </form>
            <p><small>Esto creará registros pendientes para todos los pedidos enviados actuales.</small></p>
        </div>
    </div>
    
    <hr>
    <div class="row">
        <div class="col-md-4">
            <a href="index.php?action=pedidosBarPendientes" class="btn btn-info btn-block">🔙 Volver a Bar</a>
        </div>
        <div class="col-md-4">
            <a href="index.php?action=debug_estado_pedidos" class="btn btn-primary btn-block">📊 Debug Estado</a>
        </div>
        <div class="col-md-4">
            <a href="index.php?action=pedidosCocinaPendientes" class="btn btn-warning btn-block">🍳 Ir a Cocina</a>
        </div>
    </div>
</div>

</body>
</html>
