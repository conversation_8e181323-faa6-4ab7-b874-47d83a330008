<?php
require_once "../../models/crud.php";
require_once "../../models/crudFacturaAja.php";
require_once "../../models/crudCocina.php";
require_once "../../controllers/controller.php";
require_once "../../controllers/controllerFacturaAja.php";
require_once "../../controllers/controllerCocina.php";
	ini_set("session.cookie_lifetime","28800");
	ini_set("session.gc_maxlifetime","28800");
//echo "<script>alert('Aja llego ')</script>";
session_start();
		//$queryString = $_POST['mesa'];
		$queryString = $_SESSION["mesa"];
		//echo "<script>alert('Si entro al ajax factura  efectivo ".$queryString."pago".$queryString1." cliente cedula ".$queryString2."')</script>";
		//echo $queryString."----";
		$ajax=new controllerCocina();
		//$ajax=new MvcController();
		$ajax->registroCocinaController($queryString);

/*$		//echo "<script>alert('Pago registrado')</script>";
	}
else
	{	echo "<script>alert('No hay productos, que enviar ')</script>";		}

