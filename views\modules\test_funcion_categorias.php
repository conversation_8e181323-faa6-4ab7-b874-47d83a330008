<?php
// Test directo de la función obtenerCategoriasDelPedidoComponente
echo "<h1>🔧 Test Función Categorías</h1>";
echo "<p><strong>Probar directamente la función obtenerCategoriasDelPedidoComponente</strong></p>";

$pedido_id = $_GET['pedido_id'] ?? 475; // ID del pedido P000475

try {
    require_once 'componente_impresion_categorias.php';
    
    echo "<div style='background-color: #d1ecf1; padding: 15px; border-left: 4px solid #17a2b8; margin-bottom: 20px;'>";
    echo "<h4>🧪 Probando Pedido ID: $pedido_id</h4>";
    echo "</div>";
    
    // Llamar directamente a la función
    echo "<h2>1. 🔍 Llamada Directa a la Función</h2>";
    
    $inicio = microtime(true);
    $resultado = obtenerCategoriasDelPedidoComponente($pedido_id);
    $tiempo = round((microtime(true) - $inicio) * 1000, 2);
    
    echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h4>📊 Resultado de la Función</h4>";
    echo "<p><strong>Tiempo de ejecución:</strong> {$tiempo}ms</p>";
    echo "<p><strong>Tipo de resultado:</strong> " . gettype($resultado) . "</p>";
    
    if (is_array($resultado)) {
        echo "<p><strong>Claves del array:</strong> " . implode(', ', array_keys($resultado)) . "</p>";
        echo "<p><strong>Total categorías:</strong> " . count($resultado['categorias'] ?? []) . "</p>";
        echo "<p><strong>Total productos:</strong> " . ($resultado['total_productos'] ?? 0) . "</p>";
        
        if (isset($resultado['error'])) {
            echo "<p style='color: red;'><strong>Error:</strong> {$resultado['error']}</p>";
        }
        
        if (!empty($resultado['categorias'])) {
            echo "<h5>📂 Categorías Encontradas:</h5>";
            echo "<ul>";
            foreach ($resultado['categorias'] as $categoria => $info) {
                echo "<li><strong>$categoria:</strong> {$info['total_productos']} productos - {$info['nombre']}</li>";
            }
            echo "</ul>";
        } else {
            echo "<p style='color: orange;'><strong>⚠️ No se encontraron categorías</strong></p>";
        }
    }
    
    echo "<h5>🔍 Resultado Completo (JSON):</h5>";
    echo "<pre style='background-color: #e9ecef; padding: 10px; border-radius: 3px; font-size: 11px; overflow-x: auto;'>";
    echo json_encode($resultado, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    echo "</pre>";
    echo "</div>";
    
    // Verificar manualmente la consulta SQL
    echo "<h2>2. 🗄️ Verificación Manual de la Consulta</h2>";
    
    require_once '../../models/conexion.php';
    $conexion = new Conexion();
    $pdo = $conexion->conectar();
    
    $sql = "
        SELECT 
            p.id as producto_id,
            p.nombre as producto_nombre,
            p.precio,
            p.categoria as categoria_producto,
            pvm.cantidad,
            p.precio as precio_unitario,
            (pvm.cantidad * p.precio) as subtotal,
            CASE 
                WHEN p.categoria IN ('bebidas', 'cervezas', 'licores', 'vinos', 'cocteles', 'refrescos', 'jugos', 'agua', 'bar') THEN 'bar'
                WHEN p.categoria IN ('carnes', 'parrilla', 'asados', 'pescados', 'mariscos') THEN 'asados'
                ELSE 'cocina'
            END as categoria_impresora
        FROM producto_vendido_mesa pvm
        INNER JOIN productos p ON pvm.productos_id = p.id
        WHERE pvm.pedidos_id = :pedido_id
        ORDER BY categoria_impresora, p.nombre
    ";
    
    echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h4>📋 Consulta SQL Manual</h4>";
    echo "<pre style='background-color: #e9ecef; padding: 10px; border-radius: 3px; font-size: 11px;'>";
    echo htmlspecialchars($sql);
    echo "</pre>";
    
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(":pedido_id", $pedido_id, PDO::PARAM_INT);
    $stmt->execute();
    $productos_manual = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p><strong>Productos encontrados manualmente:</strong> " . count($productos_manual) . "</p>";
    
    if (!empty($productos_manual)) {
        echo "<table style='width: 100%; border-collapse: collapse; font-size: 12px;'>";
        echo "<tr style='background-color: #e9ecef;'>";
        echo "<th style='border: 1px solid #ddd; padding: 6px;'>Producto</th>";
        echo "<th style='border: 1px solid #ddd; padding: 6px;'>Cat. Original</th>";
        echo "<th style='border: 1px solid #ddd; padding: 6px;'>Cat. Impresora</th>";
        echo "<th style='border: 1px solid #ddd; padding: 6px;'>Cantidad</th>";
        echo "<th style='border: 1px solid #ddd; padding: 6px;'>Subtotal</th>";
        echo "</tr>";
        
        foreach ($productos_manual as $prod) {
            echo "<tr>";
            echo "<td style='border: 1px solid #ddd; padding: 6px;'>{$prod['producto_nombre']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 6px;'>{$prod['categoria_producto']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 6px; font-weight: bold;'>{$prod['categoria_impresora']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 6px; text-align: center;'>{$prod['cantidad']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 6px; text-align: right;'>$" . number_format($prod['subtotal'], 0) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Agrupar manualmente
        echo "<h5>📂 Agrupación Manual:</h5>";
        $categorias_manual = [];
        foreach ($productos_manual as $prod) {
            $cat = $prod['categoria_impresora'];
            if (!isset($categorias_manual[$cat])) {
                $categorias_manual[$cat] = ['productos' => 0, 'total' => 0];
            }
            $categorias_manual[$cat]['productos'] += $prod['cantidad'];
            $categorias_manual[$cat]['total'] += $prod['subtotal'];
        }
        
        echo "<ul>";
        foreach ($categorias_manual as $cat => $info) {
            echo "<li><strong>" . strtoupper($cat) . ":</strong> {$info['productos']} productos - $" . number_format($info['total'], 0) . "</li>";
        }
        echo "</ul>";
        
    } else {
        echo "<p style='color: red;'><strong>❌ No se encontraron productos con la consulta manual</strong></p>";
    }
    echo "</div>";
    
    // Comparar resultados
    echo "<h2>3. ⚖️ Comparación de Resultados</h2>";
    
    $productos_funcion = count($resultado['categorias'] ?? []);
    $productos_manual_count = count($categorias_manual ?? []);
    
    echo "<div style='background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h4>📊 Comparación</h4>";
    echo "<p><strong>Función obtenerCategoriasDelPedidoComponente:</strong> $productos_funcion categorías</p>";
    echo "<p><strong>Consulta manual:</strong> $productos_manual_count categorías</p>";
    
    if ($productos_funcion === $productos_manual_count && $productos_funcion > 0) {
        echo "<p style='color: green;'>✅ <strong>Los resultados coinciden</strong></p>";
    } elseif ($productos_manual_count > 0 && $productos_funcion === 0) {
        echo "<p style='color: red;'>❌ <strong>La función no está devolviendo resultados pero la consulta manual sí</strong></p>";
        echo "<p>Posible problema en la función obtenerCategoriasDelPedidoComponente</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ <strong>Los resultados no coinciden</strong></p>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
    echo "<h4>❌ Error</h4>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Archivo:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Línea:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

// Formulario para cambiar pedido
echo "<h2>🔄 Cambiar Pedido</h2>";
echo "<form method='GET' style='background-color: #fff3cd; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<label><strong>Pedido ID:</strong></label><br>";
echo "<input type='number' name='pedido_id' value='$pedido_id' style='padding: 8px; border: 1px solid #ccc; border-radius: 3px; width: 200px;'><br><br>";
echo "<button type='submit' style='background-color: #ffc107; color: #212529; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold;'>🔧 Probar Pedido</button>";
echo "</form>";

// Botones de navegación
echo "<p style='margin-top: 30px;'>";
echo "<a href='debug_pedido_especifico.php?pedido=P000475' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🔍 Debug P000475</a>";
echo "<a href='test_registroPmesa_integrado.php' style='background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>✅ Test Integración</a>";
echo "<a href='../../index.php?action=registroPmesa&ida=3' style='background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🪑 Mesa 3</a>";
echo "</p>";
?>
