<?php
	$registro1 = new controllerSuministro();
	//$registro1 -> vistaSuministro1Controller();
	$registro1 -> cargarSucursalController();
	$puntos = $registro1 -> listaPuntosController();
	//$punto1 = $registro1 -> suministroController();
	
	//print_r($punto1);
	if(isset($_GET["action"]))
	 {	if($_GET["action"] == "okcS") 	{	echo "Registro Exitoso";	}	 }
?>
<script type="text/javascript">
	//------------------------- Nombre Buscar------------------------
		function buscarPlaca(inputString) 
		 {
			if(inputString.length == 0) 
			 {	// Hide the suggestion box.
				$('#suggestions').hide();
			 } 
			else
			  {	//alert("Entro en el else de placa");
				$("#dos").load("views/modules/ajaxBuscarSuministroC.php", {placa: $("#referencia").val()}, function(){	         	 
	     		 });
			 }
		 } 
	//------------------ Nombre cerrar--------------------
	//------------------------- Nombre Buscar------------------------
		function buscarPlaca1(inputString) 
		 {
			if(inputString.length == 0) 
			 {	// Hide the suggestion box.
				$('#suggestions').hide();
			 } 
			else
			  {	//alert("Entro en el else de placa");
				$("#dos").load("views/modules/ajaxBuscarSuministroCBo.php", {placa: $("#dedo").val()}, function(){
	         	 
	     		 });
			 }
		 } 
	//------------------ Nombre cerrar--------------------
	//------------------------- Nombre Buscar------------------------
		function buscarPlaca2(inputString) 
		 {
			if(inputString.length == 0) 
			 {	// Hide the suggestion box.
				$('#suggestions').hide();
			 } 
			else
			  {	//alert("Entro en el else de placa");
				$("#dos").load("views/modules/ajaxBuscarSuministroCo.php", {placa: $("#codigo1").val()}, function(){
	         	 
	     		 });
			 }
		 } 
	//------------------ Nombre cerrar--------------------
	
	//-------------- Convierte a Mayusculas  -------------------------------------
		function aMayusculas(obj,id)
		 {
		    obj = obj.toUpperCase();
		    document.getElementById(id).value = obj;
		 }
	//------------------cierre-------------------- style="width: 350px;"
</script>
<h1>CARGAR LA TIENDA 1 DE PRODUCTOS	</h1>
<form method="post">
	<table>		
		<tr>		
			<td><label> Cantidad  : </label></td>
			<td><input type="text" placeholder="cantidad" name="cantidadSuministroRegistro" value="1" required></td>
			</td>
		</tr>
		<tr>
			<td> <label> Codigo  : </label></td>
			<td><input type="text" placeholder="codigo" name="codigo" autofocus="autofocus" required>	 </td>
		</tr>
		<tr>
			<td><label> Sucursal  : </label></td>
			<td><?php  
			# # %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  puntos  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
				if($puntos=="error")
					{	echo "debe registrar el Sucursal"; }
				else{
						echo "<label></label>";
						$result='<select name="puntos"  id="puntos" autofocus="autofocus" required>';
						$result.=' <option value="-1">Sucursal</option>';
						foreach ($puntos as $row => $item)
						 {	
						  if ($_SESSION["tipo_usuario"]==1)
						   {  $result.=' <option value="'.$item["id"].'">'.$item["nombre"].'</option>';   }
						 elseif ($_SESSION["punto_id"]==$item["id"]) 
						  {	$result.=' <option value="'.$item["id"].'">'.$item["nombre"].'</option>'; }	
						  }	
						 $result.='</select>';
						 echo $result;
					}  
			# %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  End puntos  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%  max="<?=$_SESSION["fecha"];
			 ?><!--&laquo;<a class="enlace" href="registroPuntoVenta" target="_blank"> Registrar Sucursal</a>&raquo;<br>--></td>
		</tr>
				
	</table><br>
	<input type="submit" value="Cargar">
<h1>BUSCAR Código PRODUCTO</h1>

	<label for=""><b>Nombre :</b></label><input type="text" placeholder="Nombre Suministros" class="mayuscula"  name="dedo" id="dedo" size="38" onkeyup="buscarPlaca1(this.value);"  >&nbsp;&nbsp;&nbsp;&nbsp;
	
	<label for=""><b>Referencia :</b></label><input type="text" placeholder="0345" class="mayuscula"  name="referencia" id="referencia" size="38" onkeyup="buscarPlaca(this.value);"   >&nbsp;&nbsp;&nbsp;&nbsp;
	
	<label for=""><b>Codigo Barra :</b></label><input type="text" placeholder="0345" class="mayuscula"  name="codigo1" id="codigo1" size="38" onkeyup="buscarPlaca2(this.value);"   >

	<br>
</form>
<br>
<div id="dos"></div>


