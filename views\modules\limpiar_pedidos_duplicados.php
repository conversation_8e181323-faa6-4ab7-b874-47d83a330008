<?php

session_start();

if(!isset($_SESSION["validar"]) || !$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "models/conexion.php";

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Limpiar: Pedidos Duplicados</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
</head>
<body>

<div class="container">
    <h2>🧹 Limpiar: Pedidos Duplicados</h2>
    <hr>
    
    <div class="alert alert-warning">
        <h4>⚠️ Problema Detectado:</h4>
        <p>Se encontraron <strong>9 pedidos borrador</strong> en una mesa, lo cual indica un problema en la creación de pedidos.</p>
    </div>
    
    <?php
    if (isset($_POST['limpiar_duplicados'])) {
        try {
            $stmt = Conexion::conectar();
            $stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $stmt->beginTransaction();
            
            echo "<div class='alert alert-info'>";
            echo "<h5>🔄 Limpiando pedidos duplicados...</h5>";
            echo "</div>";
            
            // 1. Identificar mesas con pedidos borrador duplicados
            $consulta_duplicados = "
                SELECT mesa_id, COUNT(*) as cantidad, GROUP_CONCAT(id ORDER BY fecha_pedido DESC) as pedidos_ids
                FROM pedidos 
                WHERE estado = 'borrador'
                GROUP BY mesa_id
                HAVING cantidad > 1
            ";
            $stmt_duplicados = $stmt->prepare($consulta_duplicados);
            $stmt_duplicados->execute();
            $duplicados = $stmt_duplicados->fetchAll(PDO::FETCH_ASSOC);
            
            $total_cancelados = 0;
            
            foreach ($duplicados as $duplicado) {
                $mesa_id = $duplicado['mesa_id'];
                $pedidos_ids = explode(',', $duplicado['pedidos_ids']);
                $pedido_mas_reciente = $pedidos_ids[0]; // El primero es el más reciente
                
                echo "<p><strong>Mesa {$mesa_id}:</strong> {$duplicado['cantidad']} pedidos borrador</p>";
                echo "<p>Manteniendo pedido más reciente: {$pedido_mas_reciente}</p>";
                
                // Cancelar todos excepto el más reciente
                for ($i = 1; $i < count($pedidos_ids); $i++) {
                    $pedido_a_cancelar = $pedidos_ids[$i];
                    
                    // Cancelar el pedido
                    $stmt_cancelar = $stmt->prepare("
                        UPDATE pedidos 
                        SET estado = 'cancelado', fecha_entrega = NOW() 
                        WHERE id = ?
                    ");
                    $stmt_cancelar->execute([$pedido_a_cancelar]);
                    
                    // Eliminar productos asociados a este pedido
                    $stmt_productos = $stmt->prepare("
                        DELETE FROM producto_vendido_mesa 
                        WHERE pedidos_id = ?
                    ");
                    $stmt_productos->execute([$pedido_a_cancelar]);
                    
                    echo "<p class='text-warning'>⚠️ Cancelado pedido {$pedido_a_cancelar} y eliminados sus productos</p>";
                    $total_cancelados++;
                }
            }
            
            // 2. Limpiar productos huérfanos (asociados a pedidos facturados/cancelados)
            echo "<p><strong>Limpiando productos huérfanos...</strong></p>";
            $consulta_huerfanos = "
                DELETE pvm FROM producto_vendido_mesa pvm
                INNER JOIN pedidos p ON pvm.pedidos_id = p.id
                WHERE p.estado IN ('facturado', 'cancelado')
            ";
            $stmt->exec($consulta_huerfanos);
            $huerfanos_eliminados = $stmt->rowCount();
            
            echo "<p class='text-success'>✅ {$huerfanos_eliminados} productos huérfanos eliminados</p>";
            
            $stmt->commit();
            
            echo "<div class='alert alert-success'>";
            echo "<h5>🎉 ¡Limpieza completada exitosamente!</h5>";
            echo "<p><strong>Pedidos cancelados:</strong> {$total_cancelados}</p>";
            echo "<p><strong>Productos huérfanos eliminados:</strong> {$huerfanos_eliminados}</p>";
            echo "</div>";
            
        } catch (Exception $e) {
            if ($stmt->inTransaction()) {
                $stmt->rollBack();
            }
            echo "<div class='alert alert-danger'>";
            echo "<h5>❌ Error en la limpieza:</h5>";
            echo "<p>" . $e->getMessage() . "</p>";
            echo "</div>";
        }
    }
    ?>
    
    <div class="panel panel-danger">
        <div class="panel-heading">
            <h3 class="panel-title">🔍 Análisis de Pedidos Duplicados</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                echo "<h5>📊 Mesas con pedidos borrador duplicados:</h5>";
                $stmt_duplicados = Conexion::conectar()->prepare("
                    SELECT 
                        mesa_id,
                        COUNT(*) as cantidad,
                        GROUP_CONCAT(id ORDER BY fecha_pedido DESC) as pedidos_ids,
                        GROUP_CONCAT(numero_pedido ORDER BY fecha_pedido DESC) as numeros_pedidos,
                        GROUP_CONCAT(fecha_pedido ORDER BY fecha_pedido DESC) as fechas
                    FROM pedidos 
                    WHERE estado = 'borrador'
                    GROUP BY mesa_id
                    HAVING cantidad > 1
                    ORDER BY cantidad DESC
                ");
                $stmt_duplicados->execute();
                $duplicados = $stmt_duplicados->fetchAll(PDO::FETCH_ASSOC);
                
                if (count($duplicados) > 0) {
                    echo "<table class='table table-striped'>";
                    echo "<thead>";
                    echo "<tr>";
                    echo "<th>Mesa</th>";
                    echo "<th>Cantidad</th>";
                    echo "<th>IDs de Pedidos</th>";
                    echo "<th>Números de Pedidos</th>";
                    echo "<th>Problema</th>";
                    echo "</tr>";
                    echo "</thead>";
                    echo "<tbody>";
                    
                    foreach ($duplicados as $duplicado) {
                        echo "<tr class='danger'>";
                        echo "<td><strong>Mesa {$duplicado['mesa_id']}</strong></td>";
                        echo "<td><span class='badge badge-danger'>{$duplicado['cantidad']}</span></td>";
                        echo "<td><small>{$duplicado['pedidos_ids']}</small></td>";
                        echo "<td><small>{$duplicado['numeros_pedidos']}</small></td>";
                        echo "<td>⚠️ Múltiples pedidos borrador</td>";
                        echo "</tr>";
                    }
                    
                    echo "</tbody></table>";
                    
                    echo "<div class='alert alert-warning'>";
                    echo "<h6>⚠️ Problema identificado:</h6>";
                    echo "<p>Tener múltiples pedidos borrador en la misma mesa causa confusión y errores en el sistema.</p>";
                    echo "<p><strong>Solución:</strong> Mantener solo el pedido más reciente y cancelar los demás.</p>";
                    echo "</div>";
                    
                } else {
                    echo "<div class='alert alert-success'>";
                    echo "<h6>✅ No hay pedidos borrador duplicados</h6>";
                    echo "<p>Cada mesa tiene máximo un pedido borrador.</p>";
                    echo "</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-warning">
        <div class="panel-heading">
            <h3 class="panel-title">🔍 Productos Huérfanos</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                echo "<h5>📊 Productos asociados a pedidos problemáticos:</h5>";
                $stmt_huerfanos = Conexion::conectar()->prepare("
                    SELECT 
                        pvm.productos_id,
                        pvm.mesas_id,
                        pvm.pedidos_id,
                        p.estado as pedido_estado,
                        p.numero_pedido,
                        pr.nombre as producto_nombre,
                        pvm.cantidad
                    FROM producto_vendido_mesa pvm
                    LEFT JOIN pedidos p ON pvm.pedidos_id = p.id
                    LEFT JOIN productos pr ON pvm.productos_id = pr.id
                    WHERE p.estado IN ('facturado', 'cancelado') OR p.id IS NULL
                    ORDER BY pvm.mesas_id, pvm.pedidos_id
                    LIMIT 20
                ");
                $stmt_huerfanos->execute();
                $huerfanos = $stmt_huerfanos->fetchAll(PDO::FETCH_ASSOC);
                
                if (count($huerfanos) > 0) {
                    echo "<div class='alert alert-warning'>";
                    echo "<h6>⚠️ Se encontraron " . count($huerfanos) . " productos huérfanos</h6>";
                    echo "<p>Estos productos están asociados a pedidos facturados, cancelados o inexistentes.</p>";
                    echo "</div>";
                    
                    echo "<table class='table table-condensed'>";
                    echo "<thead>";
                    echo "<tr>";
                    echo "<th>Producto</th>";
                    echo "<th>Mesa</th>";
                    echo "<th>Pedido</th>";
                    echo "<th>Estado</th>";
                    echo "<th>Cantidad</th>";
                    echo "</tr>";
                    echo "</thead>";
                    echo "<tbody>";
                    
                    foreach ($huerfanos as $huerfano) {
                        $clase = '';
                        if ($huerfano['pedido_estado'] == 'facturado') $clase = 'success';
                        elseif ($huerfano['pedido_estado'] == 'cancelado') $clase = 'danger';
                        else $clase = 'warning';
                        
                        echo "<tr class='{$clase}'>";
                        echo "<td>{$huerfano['producto_nombre']}</td>";
                        echo "<td>Mesa {$huerfano['mesas_id']}</td>";
                        echo "<td>{$huerfano['numero_pedido']}</td>";
                        echo "<td><span class='label label-{$clase}'>" . ($huerfano['pedido_estado'] ?: 'NULL') . "</span></td>";
                        echo "<td>{$huerfano['cantidad']}</td>";
                        echo "</tr>";
                    }
                    
                    echo "</tbody></table>";
                    
                } else {
                    echo "<div class='alert alert-success'>";
                    echo "<h6>✅ No hay productos huérfanos</h6>";
                    echo "<p>Todos los productos están correctamente asociados a pedidos activos.</p>";
                    echo "</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">📊 Estadísticas del Sistema</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                echo "<h5>📋 Resumen de pedidos:</h5>";
                $stmt_stats = Conexion::conectar()->prepare("
                    SELECT 
                        estado,
                        COUNT(*) as cantidad
                    FROM pedidos 
                    WHERE fecha_pedido >= DATE_SUB(NOW(), INTERVAL 1 DAY)
                    GROUP BY estado
                    ORDER BY cantidad DESC
                ");
                $stmt_stats->execute();
                $stats = $stmt_stats->fetchAll(PDO::FETCH_ASSOC);
                
                echo "<table class='table table-condensed'>";
                echo "<thead><tr><th>Estado</th><th>Cantidad</th><th>Descripción</th></tr></thead>";
                echo "<tbody>";
                
                foreach ($stats as $stat) {
                    $clase = '';
                    $descripcion = '';
                    switch ($stat['estado']) {
                        case 'borrador':
                            $clase = 'info';
                            $descripcion = 'Pedidos en creación';
                            break;
                        case 'enviado':
                            $clase = 'warning';
                            $descripcion = 'Enviados a cocina';
                            break;
                        case 'entregado':
                            $clase = 'primary';
                            $descripcion = 'Listos para facturar';
                            break;
                        case 'facturado':
                            $clase = 'success';
                            $descripcion = 'Facturados correctamente';
                            break;
                        case 'cancelado':
                            $clase = 'danger';
                            $descripcion = 'Pedidos cancelados';
                            break;
                    }
                    
                    echo "<tr class='{$clase}'>";
                    echo "<td><strong>{$stat['estado']}</strong></td>";
                    echo "<td><span class='badge'>{$stat['cantidad']}</span></td>";
                    echo "<td><small>{$descripcion}</small></td>";
                    echo "</tr>";
                }
                
                echo "</tbody></table>";
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-success">
        <div class="panel-heading">
            <h3 class="panel-title">🛠️ Ejecutar Limpieza</h3>
        </div>
        <div class="panel-body">
            <h5>🧹 Limpieza Automática:</h5>
            <p>Esta acción realizará una limpieza completa del sistema:</p>
            
            <ul>
                <li>✅ <strong>Cancelar pedidos borrador duplicados</strong> (mantiene solo el más reciente)</li>
                <li>✅ <strong>Eliminar productos huérfanos</strong> (asociados a pedidos facturados/cancelados)</li>
                <li>✅ <strong>Limpiar datos inconsistentes</strong> en el sistema</li>
            </ul>
            
            <form method="POST">
                <button type="submit" name="limpiar_duplicados" class="btn btn-warning btn-lg">
                    🧹 Ejecutar Limpieza Completa
                </button>
            </form>
            
            <div class="alert alert-info" style="margin-top: 15px;">
                <h6>💡 Qué hace esta limpieza:</h6>
                <ol>
                    <li>Identifica mesas con múltiples pedidos borrador</li>
                    <li>Mantiene solo el pedido más reciente por mesa</li>
                    <li>Cancela los pedidos duplicados más antiguos</li>
                    <li>Elimina productos asociados a pedidos problemáticos</li>
                    <li>Deja el sistema limpio y funcional</li>
                </ol>
            </div>
        </div>
    </div>
    
    <hr>
    <div class="row">
        <div class="col-md-3">
            <a href="index.php?action=solucionar_problemas_pedidos" class="btn btn-primary btn-block">🔙 Volver a Soluciones</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=mesa" class="btn btn-info btn-block">🪑 Ver Mesas</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=verificar_correcciones" class="btn btn-warning btn-block">✅ Verificar Correcciones</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=diagnostico" class="btn btn-success btn-block">📊 Diagnóstico</a>
        </div>
    </div>
</div>

</body>
</html>
