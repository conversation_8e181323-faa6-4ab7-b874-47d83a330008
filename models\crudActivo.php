<?php
require_once "conexion.php";
class DatosActivo extends Conexion
 {
	#REGISTRO DE Activo
	#-------------------------------------
	 public static function registroActivoModel($datosModel, $tabla)
		{
			date_default_timezone_set("America/Bogota");
			$fecha_creado=strftime("%Y-%m-%d %H:%M:%S");
			//echo "<script>alert('Entro CRUD ".$datosModel['nombre']." no')</script>";	
				$consulta="INSERT INTO $tabla (cantidad, nombre,  descripcion, fecha_creado, fecha_vencimiento, persona_id) VALUES (:cantidad, :nombre,  :descripcion, :fecha_creado, :fecha_vencimiento, :persona_id)";
				//echo "<script>alert('Entro CRUD ".$consulta." no')</script>";	
				$stmt = Conexion::conectar()->prepare($consulta);
				$stmt->execute();			
				$stmt->bindParam(":nombre", $datosModel['nombre'], PDO::PARAM_STR);				
				$stmt->bindParam(":descripcion", $datosModel['descripcion'], PDO::PARAM_STR);				
				$stmt->bindParam(":fecha_creado", $fecha_creado, PDO::PARAM_STR);				
				$stmt->bindParam(":fecha_vencimiento", $datosModel['fecha_vencimiento'], PDO::PARAM_STR);				
				$stmt->bindParam(":cantidad", $datosModel['cantidad'], PDO::PARAM_INT);				
				$stmt->bindParam(":persona_id", $datosModel['persona_id'], PDO::PARAM_INT);				
				//echo "<script>alert('Guardo')</script>";			
				if($stmt->execute())
					{	return "success";	}
				else{ return "error";	}
				$stmt->close();			
		}
	#------------------------------------------
	#VISTA Activo
	#-------------------------------------
	 public static function vistaActivoModel($tabla)
		{
			try 
			 {		
		 		$stmt = Conexion::conectar()->prepare("SELECT id, cantidad, nombre, descripcion, fecha_vencimiento FROM $tabla");	
				$stmt->execute();					
				return $stmt->fetchAll();
				$stmt->close();
			 }
			catch (Exception $e) 
				{					//$stmt->rollBack(); 
					print "Error!: ".$e->getMessage()."</br>";
					return "Error!: ".$e->getMessage()."</br>";
				}
		}
	#-----------------------------------------
	#EDITAR Activo
	#-------------------------------------
	 public static function editarActivoModel($datosModel, $tabla)
		{
			try 
			 {
				$stmt = Conexion::conectar()->prepare("SELECT id, nombre, cantidad, descripcion, fecha_vencimiento FROM $tabla WHERE id = :id");
				$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);	
				$stmt->execute();
				return $stmt->fetch();
				$stmt->close();
		 	 }
			catch (Exception $e) 
			 {					//$stmt->rollBack(); 
				print "Error!: ".$e->getMessage()."</br>";
				return "Error!: ".$e->getMessage()."</br>";
			 }
		}
	#--------------------------------------
	#ACTUALIZAR Activo
	#-------------------------------------
	 public static function actualizarActivoModel($datosModel, $tabla)
		{ date_default_timezone_set("America/Bogota");
			$fecha_modificacion=strftime("%Y-%m-%d %H:%M:%S");
			try 
		 	 {	echo "<script>alert('Entro Actualizar Producto')</script>";	
				$stmt = Conexion::conectar()->prepare("UPDATE $tabla SET cantidad = :cantidad, nombre = :nombre,    descripcion = :descripcion,   fecha_vencimiento = :fecha_vencimiento,   fecha_modificacion = :fecha_modificacion WHERE id = :id");			
				$stmt->bindParam(":nombre", $datosModel["nombre"], PDO::PARAM_STR);				
				$stmt->bindParam(":cantidad", $datosModel["cantidad"], PDO::PARAM_INT);				
				$stmt->bindParam(":descripcion", $datosModel["descripcion"], PDO::PARAM_STR);				
				$stmt->bindParam(":fecha_vencimiento", $datosModel["fecha_vencimiento"], PDO::PARAM_STR);				
				$stmt->bindParam(":fecha_modificacion", $fecha_modificacion, PDO::PARAM_STR);				
				$stmt->bindParam(":id", $datosModel["id"], PDO::PARAM_INT);
				if($stmt->execute())
					{echo "<script>alert('Guardo Actualizar Activo')</script>";
						return "success";	}
				else{	return "error";			}
				$stmt->close();
			}
			catch (Exception $e) 
			 {					//$stmt->rollBack(); 
				print "Error!: ".$e->getMessage()."</br>";
				return "Error!: ".$e->getMessage()."</br>";
			 }
		}
	#--------------------------------------

		#BORRAR Activo
		#------------------------------------
			public static function borrarActivoModel($datosModel, $tabla)
				{
					try {		$stmt = Conexion::conectar()->prepare("DELETE FROM $tabla WHERE id = :id");
							$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);
							if($stmt->execute())
								{	return "success";	}
							else{	return "error";		}
							$stmt->close();

						}
					catch (Exception $e) 
						{					//$stmt->rollBack(); 
							print "Error!: ".$e->getMessage()."</br>";
							return "Error!: ".$e->getMessage()."</br>";
						}
				}
	#----------------------------------------------
 }