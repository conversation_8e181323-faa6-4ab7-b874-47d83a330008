<?php

date_default_timezone_set("America/Bogota");
//$fecha_actual=strftime("%Y-%m-%d %H:%M:%S");
$cadena = array("January", "February","March","April", "May", "June","July","August","September", "October","November","December");
$reemplazo = array("Enero", "Febrero","Marzo","Abril", "Mayo", "Junio","Julio","Agosto","Septiembre", "Octubre","Noviembre","Diciembre");
$fecha_actual=strftime("%Y-%B-%d %H:%M:%S"); //%B Nombre completo del mes en Ingles <?=$fecha_final;
$fecha_final=str_replace($cadena, $reemplazo, $fecha_actual);
?>

<h1>Productos </h1>
	<table border="1" class="table table-hover">		
		<thead>			
			<tr>				
				<th>No</th>
				<th>CODIGO</th>
				<th>NOMBRES</th>
				<th>CANTIDAD</th>
				<?php 
					if ($_SESSION["usuario"]==1) 
					 {
						echo '<th>PRECIO COMPRA</th>';
					 }					
				 ?>
				<th>PRECIO VENTA</th>
				<th>REFERENCIA</th>
				<th>GENERO</th>				
				<th>TALLA</th>
				<th>MARCA</th>
				<th>COLOR</th>
				<th>DISEÑO</th>
				<th>SELEC</th>
				<?php 
					if ($_SESSION["usuario"]==1) 
					 {
						echo '<th></th>';
					 }					
				 ?>
				

			</tr>

		</thead>

		<tbody>
			
			<?php

			$vistaUsuario = new controllerSuministro();
			$vistaUsuario -> vistaSuministroController();
			$vistaUsuario -> borrarSuministroController();

			?>

	

<?php

if(isset($_GET["action"]))
	{	if($_GET["action"] == "cambio")
			{	echo "Cambio Exitoso";	}
	}

?>




