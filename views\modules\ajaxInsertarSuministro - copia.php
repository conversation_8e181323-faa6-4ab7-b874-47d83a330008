<?php
require_once "../../models/crud.php";
require_once "../../models/crudSuministro.php";
require_once "../../models/crudComprasuministro.php";
require_once "../../controllers/controller.php";
require_once "../../controllers/controllerSuministro.php";
require_once "../../controllers/controllerCompraSuministro.php";
	ini_set("session.cookie_lifetime","28800");
	ini_set("session.gc_maxlifetime","28800");
		//echo'<br> <script>alert("Entro al ajax Buscar Suministros '.$_POST['placa'].'");</script> <br>';
if($_POST['proveedores']>-1)
	{
	 $datosController =array('proveedor_id'=>$_POST["proveedores"],
							 'valor_compra'=>$_POST["valor_compraCsuministroRegistro"],
							 'fecha_hora'=>$_POST["fecha_horaCsuministroRegistro"],
							 'pago'=>$_POST["pago"],
							 'abono'=>$_POST["abono"],
							 'numero_factura_compra'=>$_POST["numero_factura_compra"],
							 'tipo'=>$_POST["tipo"],
							 'tiposuministro'=>$_POST["tiposuministro"],
							 'codigo'=>$_POST["codigo"],
							 'nombre'=>$_POST["nombre"],
							 'cantidad'=>$_POST["cantidad"],
							 'precioC'=>$_POST["precioC"]/$_POST["cantidad"],
							 'sid'=>$_POST["sid"],
							 'precioV'=>$_POST["precioV"]
										);
	 $registroCsuministro = new controllerCompraSuministro();
	 $r=$registroCsuministro -> registroCompraSuministroAjaController($datosController);
	 if ($r>0)
	 	{	//echo' <script>alert("Se encontraron resultados");</script>';
		 echo'
		 	<table class="table">
				<thead>
					<tr>
						<th>Codigo</th>
						<th>Nombre</th>
						<th>Cantidad</th>
						<th>Precio Compra</th>
						<th></th>
					</tr>
				</thead>
				<tbody>';
				$total=0;
				foreach ($r as $key => $value)
					{
						$totalS=$value['precio']*$value['cantidad'];
						$total=+$totalS;
					 echo '
						<tr>
							<td>'.$value['suministro_id'].'</td>
							<td>'.$value['compra_suministro_id'].'</td>
							<td>'.$value['cantidad'].'</td>
							<td>'.$value['precio'].'</td>
							<td>
								<a href="index.php?action=editarSuministroFactura&idEditar='.$value["compra_suministro_id"].'">Editar</a>
								<a href="index.php?action=registroFacturaSuministro&idBorrar='.$value["compra_suministro_id"].'">Borrar</a>
							</td>
						</tr>
						<<tr> <td colspan="3" > Total</td>
								<td> '.$total.' </td>
						</tr>';
					}
				echo '</tbody>
			</table>
		 	'; //	registroFacturaSuministro&id
		}
 	}
else
	{	echo'<script>alert("Escoje un proveedor verifica");'; }



?>