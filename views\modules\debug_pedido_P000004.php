<?php

session_start();

if(!$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "../../models/conexion.php";

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Debug - Pedido P000004</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
    <style>
        .debug-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .sql-query {
            background-color: #f8f8f8;
            padding: 10px;
            border-left: 4px solid #007bff;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 5px;
            border-radius: 3px;
        }
    </style>
</head>
<body>

<div class="container">
    <h2>🔍 Debug Específico - Pedido P000004</h2>
    <hr>
    
    <div class="debug-section">
        <h3>1. Información del Pedido P000004</h3>
        <?php
        $stmt = Conexion::conectar()->prepare("
            SELECT p.*, m.numero as mesa_numero
            FROM pedidos p 
            LEFT JOIN mesas m ON p.mesa_id = m.id
            WHERE p.numero_pedido = 'P000004'
        ");
        $stmt->execute();
        $pedido = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($pedido) {
            echo "<div class='highlight'>";
            echo "<h4>📋 Datos del Pedido:</h4>";
            echo "<p><strong>ID:</strong> {$pedido['id']}</p>";
            echo "<p><strong>Número:</strong> {$pedido['numero_pedido']}</p>";
            echo "<p><strong>Estado:</strong> <span style='color: red; font-weight: bold;'>{$pedido['estado']}</span></p>";
            echo "<p><strong>Mesa ID:</strong> {$pedido['mesa_id']}</p>";
            echo "<p><strong>Mesa Número:</strong> {$pedido['mesa_numero']}</p>";
            echo "<p><strong>Fecha Pedido:</strong> {$pedido['fecha_pedido']}</p>";
            echo "<p><strong>Fecha Envío:</strong> {$pedido['fecha_envio']}</p>";
            echo "<p><strong>Fecha Entrega:</strong> {$pedido['fecha_entrega']}</p>";
            echo "<p><strong>Facturado:</strong> {$pedido['facturado']}</p>";
            echo "</div>";
            
            $pedido_id = $pedido['id'];
        } else {
            echo "<div class='alert alert-danger'>❌ No se encontró el pedido P000004</div>";
            $pedido_id = null;
        }
        ?>
    </div>
    
    <?php if ($pedido_id): ?>
    <div class="debug-section">
        <h3>2. Productos del Pedido P000004</h3>
        <?php
        $stmt = Conexion::conectar()->prepare("
            SELECT pvm.*, pr.nombre, pr.categoria, pr.precio
            FROM producto_vendido_mesa pvm
            INNER JOIN productos pr ON pvm.productos_id = pr.id
            WHERE pvm.pedidos_id = ?
        ");
        $stmt->execute([$pedido_id]);
        $productos = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($productos) > 0) {
            echo "<table class='table table-striped'>";
            echo "<thead><tr><th>Producto</th><th>Categoría</th><th>Cantidad</th><th>Precio</th><th>Clasificación</th></tr></thead>";
            echo "<tbody>";
            foreach ($productos as $producto) {
                $clasificacion = 'COCINA';
                if (in_array($producto['categoria'], ['bebidas', 'cervezas', 'licores', 'vinos', 'cocteles', 'refrescos', 'jugos', 'agua', 'bar'])) {
                    $clasificacion = 'BAR';
                } elseif (in_array($producto['categoria'], ['carnes', 'parrilla', 'asados', 'pescados', 'mariscos'])) {
                    $clasificacion = 'ASADOS';
                }
                
                $clase = '';
                if ($clasificacion == 'BAR') $clase = 'info';
                if ($clasificacion == 'ASADOS') $clase = 'danger';
                if ($clasificacion == 'COCINA') $clase = 'warning';
                
                echo "<tr class='{$clase}'>";
                echo "<td>{$producto['nombre']}</td>";
                echo "<td>{$producto['categoria']}</td>";
                echo "<td>{$producto['cantidad']}</td>";
                echo "<td>$" . number_format($producto['precio'], 0) . "</td>";
                echo "<td><strong>{$clasificacion}</strong></td>";
                echo "</tr>";
            }
            echo "</tbody></table>";
        } else {
            echo "<div class='alert alert-warning'>⚠️ No se encontraron productos para este pedido</div>";
        }
        ?>
    </div>
    
    <div class="debug-section">
        <h3>3. Test de Consultas por Categoría para este Pedido</h3>
        
        <h4>¿Aparece en BAR?</h4>
        <?php
        $stmt = Conexion::conectar()->prepare("
            SELECT DISTINCT p.id as pedido_id, p.numero_pedido
            FROM pedidos p
            INNER JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
            INNER JOIN productos pr ON pvm.productos_id = pr.id
            WHERE p.numero_pedido = 'P000004'
            AND p.estado = 'enviado'
            AND pr.categoria IN ('bebidas', 'cervezas', 'licores', 'vinos', 'cocteles', 'refrescos', 'jugos', 'agua', 'bar')
        ");
        $stmt->execute();
        $en_bar = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($en_bar) > 0) {
            echo "<div class='alert alert-success'>✅ SÍ aparece en BAR</div>";
        } else {
            echo "<div class='alert alert-danger'>❌ NO aparece en BAR</div>";
        }
        ?>
        
        <h4>¿Aparece en COCINA?</h4>
        <?php
        $stmt = Conexion::conectar()->prepare("
            SELECT DISTINCT p.id as pedido_id, p.numero_pedido
            FROM pedidos p
            INNER JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
            INNER JOIN productos pr ON pvm.productos_id = pr.id
            WHERE p.numero_pedido = 'P000004'
            AND p.estado = 'enviado'
            AND (pr.categoria NOT IN ('bebidas', 'cervezas', 'licores', 'vinos', 'cocteles', 'refrescos', 'jugos', 'agua', 'bar', 'carnes', 'parrilla', 'asados', 'pescados', 'mariscos')
                 OR pr.categoria IS NULL
                 OR pr.categoria = ''
                 OR pr.categoria = 'cocina'
                 OR pr.categoria = 'comida'
                 OR pr.categoria = 'platos'
                 OR pr.categoria = 'entradas'
                 OR pr.categoria = 'principales'
                 OR pr.categoria = 'postres')
        ");
        $stmt->execute();
        $en_cocina = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($en_cocina) > 0) {
            echo "<div class='alert alert-success'>✅ SÍ aparece en COCINA</div>";
        } else {
            echo "<div class='alert alert-danger'>❌ NO aparece en COCINA</div>";
        }
        ?>
        
        <h4>¿Aparece en ASADOS?</h4>
        <?php
        $stmt = Conexion::conectar()->prepare("
            SELECT DISTINCT p.id as pedido_id, p.numero_pedido
            FROM pedidos p
            INNER JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
            INNER JOIN productos pr ON pvm.productos_id = pr.id
            WHERE p.numero_pedido = 'P000004'
            AND p.estado = 'enviado'
            AND pr.categoria IN ('carnes', 'parrilla', 'asados', 'pescados', 'mariscos')
        ");
        $stmt->execute();
        $en_asados = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($en_asados) > 0) {
            echo "<div class='alert alert-success'>✅ SÍ aparece en ASADOS</div>";
        } else {
            echo "<div class='alert alert-danger'>❌ NO aparece en ASADOS</div>";
        }
        ?>
    </div>
    
    <div class="debug-section">
        <h3>4. Test de Consulta Completa del Modelo</h3>
        
        <h4>Consulta BAR (como en el modelo):</h4>
        <?php
        $stmt = Conexion::conectar()->prepare("
            SELECT DISTINCT
                p.id as pedido_id,
                p.numero_pedido,
                p.fecha_envio,
                p.mesa_id,
                m.numero as mesa_numero,
                COUNT(pvm.id) as total_productos,
                GROUP_CONCAT(
                    CONCAT(pr.nombre, ' x', pvm.cantidad)
                    ORDER BY pr.nombre
                    SEPARATOR ', '
                ) as productos_detalle,
                SUM(pvm.cantidad * pr.precio) as total_precio
            FROM pedidos p
            INNER JOIN mesas m ON p.mesa_id = m.id
            INNER JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
            INNER JOIN productos pr ON pvm.productos_id = pr.id
            WHERE p.estado = 'enviado'
            AND CASE
                WHEN 'bar' = 'bar' THEN pr.categoria IN ('bebidas', 'cervezas', 'licores', 'vinos', 'cocteles', 'refrescos', 'jugos', 'agua', 'bar')
                WHEN 'bar' = 'asados' THEN pr.categoria IN ('carnes', 'parrilla', 'asados', 'pescados', 'mariscos')
                ELSE 1=0
            END
            GROUP BY p.id, p.numero_pedido, p.fecha_envio, p.mesa_id, m.numero
            ORDER BY p.fecha_envio ASC
        ");
        $stmt->execute();
        $resultado_bar = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div class='sql-query'>Consulta exacta del modelo para BAR</div>";
        if (count($resultado_bar) > 0) {
            echo "<div class='alert alert-success'>✅ Encontrados " . count($resultado_bar) . " pedidos</div>";
            foreach ($resultado_bar as $ped) {
                if ($ped['numero_pedido'] == 'P000004') {
                    echo "<div class='highlight'><strong>P000004 ENCONTRADO:</strong> {$ped['productos_detalle']}</div>";
                }
            }
        } else {
            echo "<div class='alert alert-danger'>❌ No se encontraron pedidos para BAR</div>";
        }
        ?>
        
        <h4>Consulta COCINA (como en el modelo):</h4>
        <?php
        $stmt = Conexion::conectar()->prepare("
            SELECT DISTINCT
                p.id as pedido_id,
                p.numero_pedido,
                p.fecha_envio,
                p.mesa_id,
                m.numero as mesa_numero,
                COUNT(pvm.id) as total_productos,
                GROUP_CONCAT(
                    CONCAT(pr.nombre, ' x', pvm.cantidad)
                    ORDER BY pr.nombre
                    SEPARATOR ', '
                ) as productos_detalle,
                SUM(pvm.cantidad * pr.precio) as total_precio
            FROM pedidos p
            INNER JOIN mesas m ON p.mesa_id = m.id
            INNER JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
            INNER JOIN productos pr ON pvm.productos_id = pr.id
            WHERE p.estado = 'enviado'
            AND (pr.categoria NOT IN ('bebidas', 'cervezas', 'licores', 'vinos', 'cocteles', 'refrescos', 'jugos', 'agua', 'bar', 'carnes', 'parrilla', 'asados', 'pescados', 'mariscos')
                 OR pr.categoria IS NULL
                 OR pr.categoria = ''
                 OR pr.categoria = 'cocina'
                 OR pr.categoria = 'comida'
                 OR pr.categoria = 'platos'
                 OR pr.categoria = 'entradas'
                 OR pr.categoria = 'principales'
                 OR pr.categoria = 'postres')
            GROUP BY p.id, p.numero_pedido, p.fecha_envio, p.mesa_id, m.numero
            ORDER BY p.fecha_envio ASC
        ");
        $stmt->execute();
        $resultado_cocina = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div class='sql-query'>Consulta exacta del modelo para COCINA</div>";
        if (count($resultado_cocina) > 0) {
            echo "<div class='alert alert-success'>✅ Encontrados " . count($resultado_cocina) . " pedidos</div>";
            foreach ($resultado_cocina as $ped) {
                if ($ped['numero_pedido'] == 'P000004') {
                    echo "<div class='highlight'><strong>P000004 ENCONTRADO:</strong> {$ped['productos_detalle']}</div>";
                }
            }
        } else {
            echo "<div class='alert alert-danger'>❌ No se encontraron pedidos para COCINA</div>";
        }
        ?>
    </div>
    <?php endif; ?>
    
    <div class="debug-section">
        <h3>5. Verificar Estado de Todos los Pedidos Enviados</h3>
        <?php
        $stmt = Conexion::conectar()->prepare("
            SELECT p.id, p.numero_pedido, p.estado, p.mesa_id, m.numero as mesa_numero
            FROM pedidos p
            LEFT JOIN mesas m ON p.mesa_id = m.id
            WHERE p.estado = 'enviado'
            ORDER BY p.fecha_envio DESC
        ");
        $stmt->execute();
        $todos_enviados = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($todos_enviados) > 0) {
            echo "<div class='alert alert-info'>📋 Encontrados " . count($todos_enviados) . " pedidos con estado 'enviado'</div>";
            echo "<table class='table table-striped'>";
            echo "<thead><tr><th>ID</th><th>Número</th><th>Estado</th><th>Mesa</th></tr></thead>";
            echo "<tbody>";
            foreach ($todos_enviados as $ped) {
                $clase = ($ped['numero_pedido'] == 'P000004') ? 'warning' : '';
                echo "<tr class='{$clase}'>";
                echo "<td>{$ped['id']}</td>";
                echo "<td><strong>{$ped['numero_pedido']}</strong></td>";
                echo "<td>{$ped['estado']}</td>";
                echo "<td>{$ped['mesa_numero']}</td>";
                echo "</tr>";
            }
            echo "</tbody></table>";
        } else {
            echo "<div class='alert alert-danger'>❌ No hay pedidos con estado 'enviado'</div>";
        }
        ?>
    </div>
    
    <hr>
    <p><a href="pedidosCocinaPendientes" class="btn btn-warning">🍳 Ir a Cocina</a></p>
    <p><a href="pedidosBarPendientes" class="btn btn-info">🍺 Ir a Bar</a></p>
    <p><a href="views/modules/test_consulta_directa.php" class="btn btn-success">🧪 Test Consulta Directa</a></p>
</div>

</body>
</html>
