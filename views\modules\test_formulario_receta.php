<?php
// Test del formulario de recetas
echo "<h1>🧪 Test Formulario de Recetas</h1>";
echo "<p><strong>Verificar que los datos del formulario se envían correctamente</strong></p>";

// Mostrar datos POST si existen
if ($_POST) {
    echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0;'>";
    echo "<h4>✅ Datos POST Recibidos:</h4>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
    echo "</div>";
    
    // Verificar campos específicos
    $campos_requeridos = ['productos', 'suministros', 'cantidades'];
    $campos_faltantes = [];
    
    foreach ($campos_requeridos as $campo) {
        if (!isset($_POST[$campo]) || empty($_POST[$campo])) {
            $campos_faltantes[] = $campo;
        }
    }
    
    if (empty($campos_faltantes)) {
        echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0;'>";
        echo "<h4>✅ Todos los campos requeridos están presentes</h4>";
        echo "<ul>";
        echo "<li><strong>Producto ID:</strong> " . $_POST['productos'] . "</li>";
        echo "<li><strong>Código Suministro:</strong> " . $_POST['suministros'] . "</li>";
        echo "<li><strong>Cantidad:</strong> " . $_POST['cantidades'] . "</li>";
        echo "</ul>";
        echo "</div>";
        
        // Simular el proceso del controlador
        echo "<div style='border: 1px solid #007bff; padding: 20px; margin: 15px 0; border-radius: 8px;'>";
        echo "<h4>🔄 Simulando proceso del controlador:</h4>";
        
        try {
            require_once '../../models/conexion.php';
            require_once '../../models/crudSuministroProducto.php';
            
            $cantp = str_replace(",", ".", $_POST["cantidades"]);
            $producto_id = intval($_POST["productos"]);
            $codigo_suministro = trim($_POST["suministros"]);
            
            echo "<p><strong>Datos procesados:</strong></p>";
            echo "<ul>";
            echo "<li><strong>Producto ID:</strong> $producto_id</li>";
            echo "<li><strong>Código Suministro:</strong> '$codigo_suministro'</li>";
            echo "<li><strong>Cantidad:</strong> $cantp</li>";
            echo "</ul>";
            
            if ($producto_id > 0 && !empty($codigo_suministro) && $cantp > 0) {
                
                $datosController = array(
                    'producto_id' => $producto_id,
                    'codigo' => $codigo_suministro,
                    'cantidades' => $cantp
                );
                
                echo "<p><strong>Datos para el modelo:</strong></p>";
                echo "<pre>" . print_r($datosController, true) . "</pre>";
                
                $respuesta = DatosSuministroProducto::registroSuministroPModel($datosController, "suministros_productos");
                
                echo "<p><strong>Respuesta del modelo:</strong> '$respuesta'</p>";
                
                switch($respuesta) {
                    case "success":
                        echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0;'>";
                        echo "<h5>✅ ¡Éxito! Suministro asignado correctamente</h5>";
                        echo "</div>";
                        break;
                    case "error_suministro_no_encontrado":
                        echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
                        echo "<h5>❌ Error: No se encontró el suministro con código: $codigo_suministro</h5>";
                        echo "</div>";
                        break;
                    default:
                        echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
                        echo "<h5>❌ Error: $respuesta</h5>";
                        echo "</div>";
                        break;
                }
                
            } else {
                echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
                echo "<h5>❌ Error: Datos inválidos después del procesamiento</h5>";
                echo "<p>Producto ID: $producto_id, Código: '$codigo_suministro', Cantidad: $cantp</p>";
                echo "</div>";
            }
            
        } catch (Exception $e) {
            echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
            echo "<h5>❌ Excepción: " . $e->getMessage() . "</h5>";
            echo "</div>";
        }
        
        echo "</div>";
        
    } else {
        echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
        echo "<h4>❌ Campos faltantes:</h4>";
        echo "<ul>";
        foreach ($campos_faltantes as $campo) {
            echo "<li>$campo</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
} else {
    echo "<div style='background-color: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 15px 0;'>";
    echo "<h4>⚠️ No se han enviado datos POST</h4>";
    echo "<p>Use el formulario de abajo para probar</p>";
    echo "</div>";
}

// Formulario de test
echo "<h2>🧪 Formulario de Test:</h2>";
echo "<form method='POST' style='background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>🎯 Simular envío desde registroReseta:</h4>";

echo "<div style='margin: 15px 0;'>";
echo "<label><strong>Producto ID:</strong></label><br>";
echo "<input type='number' name='productos' value='78' required style='padding: 8px; border: 1px solid #ccc; border-radius: 3px; width: 100px;'>";
echo "<small style='display: block; color: #666;'>ID del producto (normalmente viene de sesión)</small>";
echo "</div>";

echo "<div style='margin: 15px 0;'>";
echo "<label><strong>Código Suministro:</strong></label><br>";
echo "<input type='text' name='suministros' placeholder='Ej: 123456' required style='padding: 8px; border: 1px solid #ccc; border-radius: 3px; width: 200px;'>";
echo "<small style='display: block; color: #666;'>Código del suministro</small>";
echo "</div>";

echo "<div style='margin: 15px 0;'>";
echo "<label><strong>Cantidad:</strong></label><br>";
echo "<input type='text' name='cantidades' value='1' required style='padding: 8px; border: 1px solid #ccc; border-radius: 3px; width: 100px;'>";
echo "<small style='display: block; color: #666;'>Cantidad del suministro</small>";
echo "</div>";

echo "<button type='submit' style='background-color: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold;'>🧪 Probar Envío</button>";
echo "</form>";

// Mostrar suministros disponibles
echo "<h2>📦 Suministros Disponibles:</h2>";
try {
    require_once '../../models/conexion.php';
    $conexion = new Conexion();
    $pdo = $conexion->conectar();
    
    $stmt = $pdo->query("SELECT id, codigo, nombre FROM suministros WHERE activo = 's' ORDER BY codigo LIMIT 10");
    $suministros = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($suministros) {
        echo "<div style='background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<table style='width: 100%; border-collapse: collapse;'>";
        echo "<tr style='background-color: #ffeaa7;'>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Código</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Nombre</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>Acción</th>";
        echo "</tr>";
        
        foreach ($suministros as $suministro) {
            echo "<tr>";
            echo "<td style='border: 1px solid #ddd; padding: 8px; font-weight: bold;'>{$suministro['codigo']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$suministro['nombre']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>";
            echo "<button onclick=\"document.querySelector('input[name=suministros]').value='{$suministro['codigo']}'\" style='background-color: #28a745; color: white; padding: 4px 8px; border: none; border-radius: 3px; cursor: pointer; font-size: 12px;'>Usar</button>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p>⚠️ Error al obtener suministros: " . $e->getMessage() . "</p>";
}

// Información de debug
echo "<h2>🔍 Información de Debug:</h2>";
echo "<div style='background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>📋 Variables de entorno:</h4>";
echo "<ul>";
echo "<li><strong>Método de solicitud:</strong> " . $_SERVER['REQUEST_METHOD'] . "</li>";
echo "<li><strong>URL actual:</strong> " . $_SERVER['REQUEST_URI'] . "</li>";
echo "<li><strong>User Agent:</strong> " . substr($_SERVER['HTTP_USER_AGENT'], 0, 100) . "...</li>";
echo "</ul>";

if (isset($_SESSION)) {
    echo "<h4>📋 Variables de sesión relevantes:</h4>";
    echo "<ul>";
    if (isset($_SESSION['producto'])) {
        echo "<li><strong>Producto en sesión:</strong> " . $_SESSION['producto'] . "</li>";
    } else {
        echo "<li><strong>Producto en sesión:</strong> No definido</li>";
    }
    echo "</ul>";
}
echo "</div>";

// Botones de navegación
echo "<p style='margin-top: 30px;'>";
echo "<a href='index.php?action=registroReseta&id=78' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🧪 Ir a Registro Real</a>";
echo "<a href='debug_suministros.php' style='background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🐛 Debug Avanzado</a>";
echo "</p>";
?>
