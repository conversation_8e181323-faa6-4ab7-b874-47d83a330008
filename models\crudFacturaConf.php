<?php
require_once "conexion.php";
class DatosFacturaConf extends Conexion
{
	#REGISTRO DE FacturaConf
	#-------------------------------------
	 public static function registroFacturaConfModel($datosModel, $tabla)
		{//echo "<script>alert('Entro CRUD ".$datosModel['nombre']." no')</script>";
		 date_default_timezone_set("America/Bogota");
		 $fecha_creado=strftime("%Y-%m-%d %H:%M:%S");
		 $stmt = Conexion::conectar();
		 try
			{
			 $stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
			 $stmt->beginTransaction();
			 $consultar="INSERT INTO $tabla (nombre, nit, regimen, ciudad, direccion_fisica, web, email, telefono, dian) VALUES ('".$datosModel["nombre"]."', '".$datosModel["nit"]."', '".$datosModel["regimen"]."', '".$datosModel["ciudad"]."', '".$datosModel["direccion_fisica"]."', '".$datosModel["web"]."', '".$datosModel["email"]."', '".$datosModel["telefono"]."', '".$datosModel["dian"]."')";
			 //echo "<br>".$consultar."<br>";
				$stmt->exec($consultar);
				$idTablatura = $stmt->lastInsertId();	//ultimo id
				$cambio=$idTablatura.'-Nombre'.$datosModel["nombre"].'-nit'.$datosModel["nit"].'-regimen'.$datosModel["regimen"].'-ciudad'.$datosModel["ciudad"].'-direccion_fisica'.$datosModel["direccion_fisica"].'-email'.$datosModel["email"].'-telefono'.$datosModel["telefono"].'-dian'.$datosModel["dian"];
				$consultarA="INSERT INTO auditoria(tabla, accion, persona_id, fecha_accion, cambio)
				VALUES ('".$tabla."', 'INSERT', ".$_SESSION["usuario"].", '$fecha_creado', '$cambio')";
				//echo "<br>".$consultarA."<br>";
				$stmt->exec($consultarA);
				/**/$stmt->commit();
				//echo "<script>alert('fin de cruD try que pasa');</script>";
				return "success";
				$stmt->close();
			 }
			catch (Exception $e)
			 {
			 	echo "<script>alert('catch error');</script>";
			 	$stmt->rollBack();
				print "Error!: ".$e->getMessage()."</br>";
				return "Error!: ".$e->getMessage()."</br>";
			 }

		}
	#-------------------------------------
	#VISTA FacturaConf
	#-------------------------------------
	 public static function vistaFacturaConfModel($tabla)
		{
			$consulta = "SELECT * FROM $tabla";
			$stmt = Conexion::conectar()->prepare($consulta);
			//echo "<br>".$consulta."<br>";
			$stmt->execute();
			return $stmt->fetchAll();
			$stmt->close();
		}
	#------------------------------------
	#EDITAR FacturaConf
	#-------------------------------------
	 public static function editarFacturaConfModel($datosModel, $tabla)
		{
			$stmt = Conexion::conectar()->prepare("SELECT * FROM $tabla WHERE id = :id");
			$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);
			$stmt->execute();
			$r=$stmt->fetch();
			return $r;
			$stmt->close();

		}
	#-------------------------------------
	#ACTUALIZAR FacturaConf $_SESSION["erol"]
	#-------------------------------------
	 public static function actualizarFacturaConfModel($datosModel, $tabla)
		{ 	//echo "<script>alert('Entro Actualizar Producto')</script>";
			date_default_timezone_set("America/Bogota");
			$fecha_creado=strftime("%Y-%m-%d %H:%M:%S");
			$stmt = Conexion::conectar();
		 try {	//echo '<script>alert("entro CRUZ");</script>';
				$stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
				$stmt->beginTransaction();
				$consultar = "UPDATE $tabla SET nombre = '".$datosModel["nombre"]."', nit = '".$datosModel["nit"]."', regimen = '".$datosModel["regimen"]."', ciudad = '".$datosModel["ciudad"]."', direccion_fisica = '".$datosModel["direccion_fisica"]."', web = '".$datosModel["web"]."', email = '".$datosModel["email"]."', telefono = '".$datosModel["telefono"]."', dian = '".$datosModel["dian"]."' WHERE id = ".$datosModel["id"];
			echo "<br".$consultar."<br";
				if ($_SESSION["erol"]["nombre"]!=$datosModel["nombre"])
					{	$camb='perfil:'.$_SESSION["erol"]["nombre"].'='.$datosModel["nit"].'='.$datosModel["regimen"].'='.$datosModel["ciudad"].'='.$datosModel["direccion_fisica"].'='.$datosModel["web"].'='.$datosModel["email"].'='.$datosModel["telefono"].'='.$datosModel["dian"];	} else{$camb=0;}
			$stmt->exec($consultar);
				$idTablatura = $stmt->lastInsertId();	//ultimo id
				$cambio=$datosModel["idEditar"].'-'.$camb;
				$consultarA="INSERT INTO auditoria(tabla, accion, persona_id, fecha_accion, cambio)
				VALUES ('".$tabla."', 'UPDATE', ".$_SESSION["usuario"].", '$fecha_creado', '$cambio')";
				echo "<br".$consultarA."<br";
				$stmt->exec($consultarA);
				$stmt->commit();
				//echo "<script>alert('fin de cruD try que pasa');</script>";
				return "success";
				$stmt->close();
			 }
			catch (Exception $e)
			 {
			 	echo "<script>alert('catch error');</script>";
			 	$stmt->rollBack();
				echo "Error!: ".$e->getMessage()."</br>";
				return "Error!: ".$e->getMessage()."</br>";
			 }

		}
	#-------------------------------------
	#BORRAR FacturaConf
	#------------------------------------
	 public static function borrarFacturaConfModel($datosModel, $tabla)
		{
			date_default_timezone_set("America/Bogota");
			$fecha_creado=strftime("%Y-%m-%d %H:%M:%S");
			$stmt = Conexion::conectar();
		 try
			{//echo '<script>alert("entro CRUD");</script>';
			 	$stmt->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
				$stmt->beginTransaction();
			$consultar = "DELETE FROM $tabla WHERE id =".$datosModel;
			    echo "<br".$consultar."<br";
				$stmt->exec($consultar);
				$cambio=$datosModel;
				$consultarA="INSERT INTO auditoria(tabla, accion, persona_id, fecha_accion, cambio)
				VALUES ('".$tabla."', 'DELETE', ".$_SESSION["usuario"].", '$fecha_creado', '$cambio')";
				//echo "<br".$consultarA."<br";
				$stmt->exec($consultarA);
				$stmt->commit();
				//echo "<script>alert('fin de cruD try que pasa');</script>";
				return "success";
				$stmt->close();
			 }
			catch (Exception $e)
			 {
			 	echo "<script>alert('catch error');</script>";
			 	$stmt->rollBack();
				echo "Error!: ".$e->getMessage()."</br>";
				return "Error!: ".$e->getMessage()."</br>";
			 }
		}
	#----------------------------------------------
}