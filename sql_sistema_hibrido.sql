-- =====================================================
-- SQL para Sistema de Impresión Híbrida - Macarena
-- Base de datos: ewogjwfm_macarena
-- =====================================================

-- 1. Tabla de configuración del sistema híbrido
-- =====================================================
CREATE TABLE IF NOT EXISTS config_impresion (
    id INT AUTO_INCREMENT PRIMARY KEY,
    clave VARCHAR(100) NOT NULL UNIQUE,
    valor TEXT NOT NULL,
    descripcion TEXT,
    fecha_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insertar configuraciones por defecto
INSERT IGNORE INTO config_impresion (clave, valor, descripcion) VALUES
('modo_impresion', 'hibrido', 'Modo de impresión: directo, proxy, hibrido'),
('ip_proxy', '***********', 'IP del proxy (tu portátil)'),
('puerto_proxy', '3000', 'Puerto del proxy'),
('timeout_conexion', '5', 'Timeout en segundos para conexiones'),
('reintentos_max', '3', 'Número máximo de reintentos'),
('red_local', '192.168.68', 'Prefijo de red local'),
('impresion_directa_habilitada', '1', 'Permitir impresión directa desde red local');

-- =====================================================
-- 2. Tabla de mapeo categoría → impresora
-- =====================================================
CREATE TABLE IF NOT EXISTS categoria_impresora (
    id INT AUTO_INCREMENT PRIMARY KEY,
    categoria VARCHAR(100) NOT NULL,
    impresora VARCHAR(50) NOT NULL,
    activo BOOLEAN DEFAULT TRUE,
    fecha_creacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    fecha_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_categoria (categoria),
    INDEX idx_categoria (categoria),
    INDEX idx_impresora (impresora),
    INDEX idx_activo (activo)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Mapeo automático por defecto (ajustar según tus categorías)
-- Nota: Las impresoras en la tabla usan 'categoria' como identificador: bar, cocina, asados
INSERT IGNORE INTO categoria_impresora (categoria, impresora) VALUES
('Bebidas', 'bar'),
('Cervezas', 'bar'),
('Licores', 'bar'),
('Vinos', 'bar'),
('Cocteles', 'bar'),
('Refrescos', 'bar'),
('Jugos', 'bar'),
('Comidas', 'cocina'),
('Platos', 'cocina'),
('Entradas', 'cocina'),
('Sopas', 'cocina'),
('Ensaladas', 'cocina'),
('Postres', 'cocina'),
('Desayunos', 'cocina'),
('Carnes', 'asados'),
('Parrilla', 'asados'),
('Asados', 'asados'),
('Pescados', 'asados'),
('Mariscos', 'asados');

-- =====================================================
-- 3. Tabla de logs de impresión
-- =====================================================
CREATE TABLE IF NOT EXISTS logs_impresion (
    id INT AUTO_INCREMENT PRIMARY KEY,
    impresora VARCHAR(50) NOT NULL,
    metodo VARCHAR(20) NOT NULL COMMENT 'directo, proxy',
    success BOOLEAN NOT NULL,
    tiempo_ms DECIMAL(10,2) DEFAULT NULL COMMENT 'Tiempo de respuesta en milisegundos',
    error TEXT DEFAULT NULL,
    ip_cliente VARCHAR(45) DEFAULT NULL,
    user_agent TEXT DEFAULT NULL,
    datos_enviados TEXT DEFAULT NULL COMMENT 'Datos que se intentaron imprimir',
    categoria VARCHAR(100) DEFAULT NULL COMMENT 'Categoría del producto si aplica',
    pedido_id INT DEFAULT NULL COMMENT 'ID del pedido si aplica',
    fecha_hora TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_fecha (fecha_hora),
    INDEX idx_impresora (impresora),
    INDEX idx_metodo (metodo),
    INDEX idx_success (success),
    INDEX idx_ip_cliente (ip_cliente),
    INDEX idx_categoria (categoria),
    INDEX idx_pedido (pedido_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 4. Verificar y mostrar configuración actual
-- =====================================================

-- Mostrar configuración del sistema
SELECT 
    'CONFIGURACION SISTEMA' as tipo,
    clave,
    valor,
    descripcion,
    fecha_actualizacion
FROM config_impresion 
ORDER BY clave;

-- Mostrar mapeo de categorías
SELECT 
    'MAPEO CATEGORIAS' as tipo,
    categoria,
    impresora,
    CASE WHEN activo = 1 THEN 'Activo' ELSE 'Inactivo' END as estado,
    fecha_creacion
FROM categoria_impresora 
ORDER BY categoria;

-- Mostrar impresoras disponibles
SELECT
    'IMPRESORAS DISPONIBLES' as tipo,
    categoria as nombre,
    ip,
    puerto,
    'Activa' as estado
FROM impresoras
ORDER BY categoria;

-- Mostrar categorías de productos existentes
SELECT 
    'CATEGORIAS PRODUCTOS' as tipo,
    categoria,
    COUNT(*) as total_productos
FROM productos 
WHERE categoria IS NOT NULL AND categoria != ''
GROUP BY categoria 
ORDER BY categoria;

-- =====================================================
-- 5. Consultas útiles para administración
-- =====================================================

-- Ver logs recientes de impresión
-- SELECT * FROM logs_impresion ORDER BY fecha_hora DESC LIMIT 50;

-- Ver estadísticas de éxito por impresora
-- SELECT 
--     impresora,
--     metodo,
--     COUNT(*) as total_intentos,
--     SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as exitosos,
--     ROUND(AVG(CASE WHEN success = 1 THEN tiempo_ms END), 2) as tiempo_promedio_ms
-- FROM logs_impresion 
-- WHERE fecha_hora >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
-- GROUP BY impresora, metodo
-- ORDER BY impresora, metodo;

-- Ver errores recientes
-- SELECT 
--     fecha_hora,
--     impresora,
--     metodo,
--     error,
--     ip_cliente
-- FROM logs_impresion 
-- WHERE success = 0 AND fecha_hora >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
-- ORDER BY fecha_hora DESC;

-- =====================================================
-- 6. Procedimientos para mantenimiento
-- =====================================================

-- Limpiar logs antiguos (ejecutar periódicamente)
-- DELETE FROM logs_impresion WHERE fecha_hora < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- Actualizar configuración (ejemplo)
-- UPDATE config_impresion SET valor = 'nueva_ip' WHERE clave = 'ip_proxy';

-- Cambiar mapeo de categoría
-- UPDATE categoria_impresora SET impresora = 'NUEVA_IMPRESORA' WHERE categoria = 'Bebidas';

-- Desactivar mapeo de categoría
-- UPDATE categoria_impresora SET activo = 0 WHERE categoria = 'categoria_no_usada';

-- =====================================================
-- 7. Triggers para auditoría (opcional)
-- =====================================================

-- Trigger para registrar cambios en configuración
DELIMITER $$
CREATE TRIGGER IF NOT EXISTS tr_config_impresion_audit
    AFTER UPDATE ON config_impresion
    FOR EACH ROW
BEGIN
    INSERT INTO logs_impresion (
        impresora, 
        metodo, 
        success, 
        error, 
        ip_cliente, 
        user_agent,
        datos_enviados
    ) VALUES (
        'SISTEMA',
        'CONFIG_CHANGE',
        1,
        CONCAT('Cambio: ', OLD.clave, ' de "', OLD.valor, '" a "', NEW.valor, '"'),
        @user_ip,
        @user_agent,
        CONCAT('Clave: ', NEW.clave, ', Valor anterior: ', OLD.valor, ', Valor nuevo: ', NEW.valor)
    );
END$$
DELIMITER ;

-- =====================================================
-- 8. Índices adicionales para optimización
-- =====================================================

-- Índice compuesto para consultas frecuentes
CREATE INDEX IF NOT EXISTS idx_logs_fecha_impresora_success 
ON logs_impresion (fecha_hora, impresora, success);

-- Índice para búsquedas por rango de tiempo
CREATE INDEX IF NOT EXISTS idx_logs_fecha_metodo 
ON logs_impresion (fecha_hora, metodo);

-- =====================================================
-- RESUMEN DE TABLAS CREADAS:
-- =====================================================
-- 
-- 1. config_impresion: Configuración del sistema híbrido
--    - Modo de impresión (directo/proxy/híbrido)
--    - IP y puerto del proxy
--    - Timeouts y reintentos
--    - Configuración de red local
--
-- 2. categoria_impresora: Mapeo categoría → impresora
--    - Qué impresora usar para cada categoría
--    - Estado activo/inactivo
--    - Fechas de creación y actualización
--
-- 3. logs_impresion: Registro de todas las impresiones
--    - Éxito/fallo de cada intento
--    - Tiempo de respuesta
--    - Errores detallados
--    - IP del cliente y user agent
--    - Datos enviados para debugging
--
-- =====================================================
