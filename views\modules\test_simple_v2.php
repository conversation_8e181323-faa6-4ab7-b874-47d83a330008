<!DOCTYPE html>
<html>
<head>
    <title>Test Simple V2 - Mesa 10</title>
</head>
<body>

<h1>🔧 Test Simple V2 - Mesa 10</h1>

<p>Esta versión ultra-simplificada evita el problema de la columna faltante 'Valor_Compra_Producto'.</p>

<div style="background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;">
    <h4>🔧 Correcciones V2 y V3:</h4>
    <ul>
        <li><strong>❌ Problema 1:</strong> Column 'Valor_Compra_Producto' not found</li>
        <li><strong>✅ Solución V2:</strong> Eliminada consulta problemática del costo</li>
        <li><strong>❌ Problema 2:</strong> Duplicate entry '82-10-274' for key 'PRIMARY'</li>
        <li><strong>✅ Solución V2:</strong> ON DUPLICATE KEY UPDATE</li>
        <li><strong>✅ Solución V3:</strong> Limpieza previa de duplicados</li>
    </ul>
</div>

<div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;">
    <h4>📊 Estado Mesa 10:</h4>
    <p><strong>Pedidos para facturar:</strong> 21 pedidos</p>
    <p><strong>Error 1 (resuelto):</strong> Columna de base de datos faltante</p>
    <p><strong>Error 2 (actual):</strong> Duplicate entry '82-10-274' for key 'PRIMARY'</p>
    <p><strong>Soluciones disponibles:</strong> ON DUPLICATE KEY UPDATE o limpieza previa</p>
</div>

<button onclick="testSimpleV2()" style="background: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;">
🔧 Test Simple V2 (Con ON DUPLICATE KEY)
</button>

<button onclick="testCleanV3()" style="background: #dc3545; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; margin-left: 10px;">
🧹 Test Clean V3 (Limpia Duplicados)
</button>

<button onclick="verificarColumnas()" style="background: #6c757d; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; margin-left: 10px;">
🔍 Verificar Columnas DB
</button>

<div id="resultado" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border: 1px solid #ddd; border-radius: 5px;"></div>

<script>
function testSimpleV2() {
    if (!confirm('🔧 FACTURACIÓN SIMPLE V2 PARA MESA 10\n\n¿Proceder con la versión simplificada?\n\nEsta versión evita el problema de la columna faltante.')) {
        return;
    }
    
    document.getElementById('resultado').innerHTML = '<div style="color: blue; font-weight: bold;">⏳ Ejecutando facturación simple V2...</div><div style="margin-top: 10px; color: #666;">Procesando sin campos problemáticos de base de datos.</div>';
    
    const startTime = Date.now();
    
    fetch('ajaxFactura_simple_v2.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            efectivo: 50000,
            bancolombia: 0,
            nequi: 0,
            daviplata: 0,
            tarjeta: 0,
            pago: 1,
            pcedula: 'MARCO',
            totalDescuento: 0,
            total: 50000,
            propina: 0,
            mesa: 10,
            tipoTarjeta: 'credito',
            optimizada: true
        }),
        signal: AbortSignal.timeout(600000) // 10 minutos
    })
    .then(response => {
        const endTime = Date.now();
        const duration = Math.round((endTime - startTime) / 1000);
        
        console.log('Status:', response.status);
        console.log('Status Text:', response.statusText);
        console.log('Duración:', duration, 'segundos');
        
        return response.text();
    })
    .then(text => {
        const endTime = Date.now();
        const duration = Math.round((endTime - startTime) / 1000);
        
        console.log('Respuesta completa:', text);
        
        let html = '<h4>🔧 Resultado Simple V2:</h4>';
        html += '<p><strong>Duración:</strong> ' + duration + ' segundos</p>';
        html += '<p><strong>Status:</strong> ' + (text ? 'Respuesta recibida' : 'Sin respuesta') + '</p>';
        
        // Buscar alertas JavaScript
        if (text.includes('alert(')) {
            html += '<div style="background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;">';
            html += '<h5 style="color: #155724;">✅ Mensajes del Sistema:</h5>';
            
            const alertMatches = text.match(/alert\('([^']+)'\)/g);
            if (alertMatches) {
                alertMatches.forEach(alert => {
                    const mensaje = alert.replace(/alert\('/, '').replace(/'\)/, '');
                    html += '<p style="color: #155724;"><strong>' + mensaje.replace(/\\n/g, '<br>') + '</strong></p>';
                });
            }
            html += '</div>';
        }
        
        // Buscar errores específicos
        if (text.includes('error_simple_v2') || text.includes('error_general_v2')) {
            html += '<div style="background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;">';
            html += '<h5 style="color: #721c24;">❌ Error Detectado:</h5>';
            
            // Extraer mensajes de error
            const errorMatches = text.match(/error_[^:]*: [^<\n]+/g);
            if (errorMatches) {
                errorMatches.forEach(error => {
                    html += '<p style="color: #721c24;"><strong>' + error + '</strong></p>';
                });
            }
            html += '</div>';
        }
        
        html += '<h5>📄 Respuesta Completa:</h5>';
        html += '<pre style="background: #fff; padding: 10px; border: 1px solid #ddd; border-radius: 3px; white-space: pre-wrap; max-height: 300px; overflow-y: auto;">' + text + '</pre>';
        
        if (text.includes('success_simple_v2')) {
            html += '<div style="color: green; font-weight: bold; font-size: 18px;">✅ ÉXITO: Facturación Simple V2 completada</div>';
            html += '<p style="color: green;">¡La mesa 10 ha sido facturada exitosamente con la versión simplificada!</p>';
            html += '<p><a href="index.php?action=registroPmesa&ida=10" style="background: #28a745; color: white; padding: 10px; text-decoration: none; border-radius: 3px;">🔄 Verificar Mesa 10</a></p>';
        } else if (text.includes('Facturación V2 completada exitosamente')) {
            html += '<div style="color: green; font-weight: bold; font-size: 18px;">✅ ÉXITO: Facturación completada</div>';
            html += '<p style="color: green;">¡La mesa 10 ha sido facturada exitosamente!</p>';
        } else if (text.includes('error') || text.includes('Error')) {
            html += '<div style="color: red; font-weight: bold; font-size: 18px;">❌ ERROR en facturación simple V2</div>';
            html += '<p>Revisa los detalles del error arriba.</p>';
        } else {
            html += '<div style="color: orange; font-weight: bold; font-size: 18px;">⚠️ Respuesta inesperada</div>';
        }
        
        document.getElementById('resultado').innerHTML = html;
    })
    .catch(error => {
        const endTime = Date.now();
        const duration = Math.round((endTime - startTime) / 1000);
        
        console.error('Error completo:', error);
        
        let html = '<h4>❌ Error en Simple V2:</h4>';
        html += '<p><strong>Duración antes del error:</strong> ' + duration + ' segundos</p>';
        html += '<p><strong>Error:</strong> ' + error.message + '</p>';
        
        if (error.name === 'TimeoutError') {
            html += '<div style="background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;">';
            html += '<h5 style="color: #856404;">⏰ Timeout después de 10 minutos</h5>';
            html += '<p style="color: #856404;">La facturación puede haberse completado en el servidor.</p>';
            html += '<p style="color: #856404;">Verifica manualmente el estado de la mesa 10.</p>';
            html += '</div>';
        }
        
        document.getElementById('resultado').innerHTML = html;
    });
}

function testCleanV3() {
    if (!confirm('🧹 FACTURACIÓN CLEAN V3 PARA MESA 10\n\n¿Proceder con la versión que limpia duplicados?\n\nEsta versión elimina duplicados antes de insertar.')) {
        return;
    }

    document.getElementById('resultado').innerHTML = '<div style="color: blue; font-weight: bold;">⏳ Ejecutando facturación clean V3...</div><div style="margin-top: 10px; color: #666;">Limpiando duplicados y procesando productos.</div>';

    const startTime = Date.now();

    fetch('ajaxFactura_clean_v3.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            efectivo: 50000,
            bancolombia: 0,
            nequi: 0,
            daviplata: 0,
            tarjeta: 0,
            pago: 1,
            pcedula: 'MARCO',
            totalDescuento: 0,
            total: 50000,
            propina: 0,
            mesa: 10,
            tipoTarjeta: 'credito',
            clean: true
        }),
        signal: AbortSignal.timeout(600000) // 10 minutos
    })
    .then(response => {
        const endTime = Date.now();
        const duration = Math.round((endTime - startTime) / 1000);

        console.log('Status:', response.status);
        console.log('Status Text:', response.statusText);
        console.log('Duración:', duration, 'segundos');

        return response.text();
    })
    .then(text => {
        const endTime = Date.now();
        const duration = Math.round((endTime - startTime) / 1000);

        console.log('Respuesta completa:', text);

        let html = '<h4>🧹 Resultado Clean V3:</h4>';
        html += '<p><strong>Duración:</strong> ' + duration + ' segundos</p>';
        html += '<p><strong>Status:</strong> ' + (text ? 'Respuesta recibida' : 'Sin respuesta') + '</p>';

        // Buscar alertas JavaScript
        if (text.includes('alert(')) {
            html += '<div style="background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;">';
            html += '<h5 style="color: #155724;">✅ Mensajes del Sistema:</h5>';

            const alertMatches = text.match(/alert\('([^']+)'\)/g);
            if (alertMatches) {
                alertMatches.forEach(alert => {
                    const mensaje = alert.replace(/alert\('/, '').replace(/'\)/, '');
                    html += '<p style="color: #155724;"><strong>' + mensaje.replace(/\\n/g, '<br>') + '</strong></p>';
                });
            }
            html += '</div>';
        }

        // Buscar errores específicos
        if (text.includes('error_clean_v3') || text.includes('error_general_v3')) {
            html += '<div style="background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;">';
            html += '<h5 style="color: #721c24;">❌ Error Detectado:</h5>';

            // Extraer mensajes de error
            const errorMatches = text.match(/error_[^:]*: [^<\n]+/g);
            if (errorMatches) {
                errorMatches.forEach(error => {
                    html += '<p style="color: #721c24;"><strong>' + error + '</strong></p>';
                });
            }
            html += '</div>';
        }

        html += '<h5>📄 Respuesta Completa:</h5>';
        html += '<pre style="background: #fff; padding: 10px; border: 1px solid #ddd; border-radius: 3px; white-space: pre-wrap; max-height: 300px; overflow-y: auto;">' + text + '</pre>';

        if (text.includes('success_clean_v3')) {
            html += '<div style="color: green; font-weight: bold; font-size: 18px;">✅ ÉXITO: Facturación Clean V3 completada</div>';
            html += '<p style="color: green;">¡La mesa 10 ha sido facturada exitosamente con limpieza de duplicados!</p>';
            html += '<p><a href="index.php?action=registroPmesa&ida=10" style="background: #28a745; color: white; padding: 10px; text-decoration: none; border-radius: 3px;">🔄 Verificar Mesa 10</a></p>';
        } else if (text.includes('Facturación CLEAN V3 completada exitosamente')) {
            html += '<div style="color: green; font-weight: bold; font-size: 18px;">✅ ÉXITO: Facturación completada</div>';
            html += '<p style="color: green;">¡La mesa 10 ha sido facturada exitosamente!</p>';
        } else if (text.includes('error') || text.includes('Error')) {
            html += '<div style="color: red; font-weight: bold; font-size: 18px;">❌ ERROR en facturación clean V3</div>';
            html += '<p>Revisa los detalles del error arriba.</p>';
        } else {
            html += '<div style="color: orange; font-weight: bold; font-size: 18px;">⚠️ Respuesta inesperada</div>';
        }

        document.getElementById('resultado').innerHTML = html;
    })
    .catch(error => {
        const endTime = Date.now();
        const duration = Math.round((endTime - startTime) / 1000);

        console.error('Error completo:', error);

        let html = '<h4>❌ Error en Clean V3:</h4>';
        html += '<p><strong>Duración antes del error:</strong> ' + duration + ' segundos</p>';
        html += '<p><strong>Error:</strong> ' + error.message + '</p>';

        if (error.name === 'TimeoutError') {
            html += '<div style="background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;">';
            html += '<h5 style="color: #856404;">⏰ Timeout después de 10 minutos</h5>';
            html += '<p style="color: #856404;">La facturación puede haberse completado en el servidor.</p>';
            html += '<p style="color: #856404;">Verifica manualmente el estado de la mesa 10.</p>';
            html += '</div>';
        }

        document.getElementById('resultado').innerHTML = html;
    });
}

function verificarColumnas() {
    window.open('verificar_columnas_productos.php', '_blank');
}
</script>

<h3>📋 Diferencias V2:</h3>
<div style="background: #e9ecef; padding: 15px; border-radius: 5px;">
    <h4>🔧 Cambios en Simple V2:</h4>
    <ul>
        <li><strong>❌ Eliminado:</strong> Consulta SELECT Valor_Compra_Producto</li>
        <li><strong>✅ Simplificado:</strong> INSERT sin campo valor_productos</li>
        <li><strong>✅ Mantenido:</strong> Toda la lógica esencial de facturación</li>
        <li><strong>✅ Optimizado:</strong> Configuración para mesas pesadas</li>
    </ul>
</div>

<br><a href="index.php?action=registroPmesa&ida=10" style="background: #007bff; color: white; padding: 10px; text-decoration: none;">🔙 Volver a Mesa 10</a>

</body>
</html>
