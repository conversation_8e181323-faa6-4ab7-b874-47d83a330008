<?php
 if ($_SESSION["turno"]==null && $_SESSION["tipo_usuario"]<4)
	{
	 echo "<script>alert('Inicie turno')</script>";
	 header("location:registroTurno");
	}
 else
	{
	 if ($_SESSION["tipo_usuario"]==4)
	 	{
	 	 echo "<script>alert('Usted no tiene permiso')</script>";
		 header("location:cocina");
	 	}
	 }

 if(isset($_GET["action"]))
	{	if($_GET["action"] =="listo" )
		{echo "Registro Exitoso";	}
	}
 if ($_GET["ida"]!=null)
 	{
 	 $_SESSION["mesa"]= $_GET["ida"];
 	}

// Incluir el componente de impresión por categorías
require_once 'views/modules/componente_impresion_categorias.php';

// Incluir controladores necesarios para el nuevo sistema
$pedidoBorrador = null;
$pedidosMesa = array();

try {
    // Los archivos ya están incluidos desde index.php, solo crear el controlador
    if (class_exists('ControllerEstadoPedidos')) {
        // Obtener información del pedido actual y pedidos de la mesa
        $mesaId = $_SESSION["mesa"];
        $controllerEstado = new ControllerEstadoPedidos();
        $pedidoBorrador = $controllerEstado->obtenerPedidoBorradorController($mesaId);
        $pedidosMesa = $controllerEstado->obtenerPedidosMesaController($mesaId);

        // Debug: Verificar qué se está obteniendo
        error_log("DEBUG Mesa $mesaId: Pedido borrador = " . ($pedidoBorrador ? $pedidoBorrador['numero_pedido'] : 'null'));
        error_log("DEBUG Mesa $mesaId: Total pedidos = " . count($pedidosMesa));
    } else {
        error_log("Clase ControllerEstadoPedidos no disponible");
        $pedidosMesa = array();
    }

} catch (Exception $e) {
    // Si hay error, continuar sin las nuevas funcionalidades
    error_log("Error cargando sistema de estados: " . $e->getMessage());
    $pedidosMesa = array(); // Asegurar que la variable existe
}

$vistaPmesa = new controllerPedidoMesaVendido();

$datos_mesas = $vistaPmesa -> vistaMesaIdController($_SESSION["mesa"]);
$mesa_mesero=$datos_mesas["descripcion"];
//$_SESSION["mesero"]=$mesa_mesero;
if ($_GET["mesero"]!=null)
 	{
 	 $_SESSION["mesero"]= $_GET["mesero"];
 	}
?>
<style>
        .table > thead > tr > th, .table > tbody > tr > th, .table > tfoot > tr > th, .table > thead > tr > td, .table > tbody > tr > td, .table > tfoot > tr > td {
		    padding: 0px;
		    line-height: 1.42857143;
		    vertical-align: top;
		    border-top: 1px solid #ddd;
		}

        body {
            background-color: #f8f9fa;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .compact-table {
            width: 100%;
            font-size: 0.85rem;
            border-collapse: separate;
            border-spacing: 0;
        }
        
        .compact-table th {
            background-color: #e9ecef;
            padding: 4px 8px !important;
            font-weight: 600;
        }
        
        .compact-table td {
            padding: 4px 8px !important;
            vertical-align: middle;
        }
        
        .compact-table input[type="text"] {
            width: 100%;
            padding: 3px 6px;
            font-size: 0.85rem;
            border: 1px solid #ced4da;
            border-radius: 3px;
        }
        
        .compact-table input[type="radio"] {
            margin-right: 3px;
        }
        
        .compact-table h5 {
            margin: 0;
            font-size: 0.9rem;
            font-weight: 600;
        }
        
        .total-row {
            background-color: #d4edda !important;
            font-weight: 600;
        }
        
        .section-title {
            background-color: #f1f3f5;
        }
        
        .payment-section td {
            padding-top: 8px !important;
            padding-bottom: 8px !important;
        }
        
        .radio-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .radio-group label {
            margin: 0;
            font-size: 0.85rem;
        }
        
        .container-box {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .header h2 {
            color: #2c3e50;
            font-size: 1.5rem;
            margin: 0;
        }
        
        .header p {
            color: #7f8c8d;
            margin: 5px 0 0;
        }
        
        .footer {
            text-align: center;
            margin-top: 20px;
            color: #7f8c8d;
            font-size: 0.85rem;
        }
        
        @media (max-width: 576px) {
            .compact-table {
                font-size: 0.8rem;
            }
            
            .compact-table input[type="text"] {
                font-size: 0.8rem;
            }
            
            .radio-group {
                flex-direction: column;
                align-items: flex-start;
                gap: 3px;
            }
        }
    </style>
<script type="text/javascript">

function formatCurrency(input) {
  if (!input || !input.value) {
    return 0;
  }

  let cleanValue = input.value.replace(/\D/g, '');
  let value = parseInt(cleanValue);

  // Si no es un número válido, usar 0
  if (isNaN(value)) {
    value = 0;
  }

  let options = { style: 'currency', currency: 'COP', minimumFractionDigits: 0, maximumFractionDigits: 0 };
  let formatter = new Intl.NumberFormat('es-CO', options);
  input.value = formatter.format(value);

  // Devolver el valor numérico sin formato
  return value;
}

document.addEventListener('DOMContentLoaded', function() {

// Otras acciones a realizar
  if(document.getElementById('propina')) {
	  document.getElementById('propina').addEventListener('input', function() {
		  funcion_calcular();
	  });
  }

  // Código que se ejecutará cuando el DOM esté listo
  console.log("El DOM está listo");

  // Ejecutar cálculo inicial después de un pequeño delay para asegurar que todos los campos estén cargados
  setTimeout(function() {
  	funcion_calcular();
  }, 500);

  // Cargar pedidos de la mesa al iniciar
  setTimeout(function() {
  	cargarPedidosMesa();
  }, 1000);

});
	// Función autocompletar --------------------------
	   function autocompletar() {
			 var minimo_letras = 2; // minimo letras visibles en el autocompletar
			 var palabra = $('#nombrepr').val();
			 //Contamos el valor del input mediante una condicional
			 if (palabra.length >= minimo_letras) {
			 $.ajax({
			 url: 'views/modules/ajaxAutocomplete.php',
			 type: 'POST',
			 data: {palabra:palabra},
			 success:function(data){
			 $('#lista_id').show();
			 $('#lista_id').html(data);
			 }
			 });
			 } else {
			 //ocultamos la lista
			 $('#lista_id').hide();
			 }
		}

		// Funcion Mostrar valores
		function set_item(opciones,id) {
		 // Cambiar el valor del formulario input
		 $('#nombrepr').val(opciones);
		 $('#codigo').val(id);
		 // ocultar lista de proposiciones
		 $('#lista_id').hide();
		}
	// -------------------------------------------------
	// ------CANCELA TODO LOS PRODUCTOS PEDIDO EN LA MESA (MEJORADO CON RUTA AMIGABLE)
		function cancelarPedido()
			{
				if (confirm('⚠️ ¿Confirma que desea cancelar TODOS los pedidos de esta mesa?\n\n' +
				           '✅ Esto cancelará todos los pedidos pendientes\n' +
				           '✅ Eliminará todos los productos del pedido\n' +
				           '✅ Dejará la mesa lista para nuevos pedidos\n\n' +
				           '❌ Esta acción NO se puede deshacer'))
					{
						setTimeout('location.href="index.php?action=cancelarPedido"', 500);
					}
			}
	// ------CANCELA TODO LOS PRODUCTOS PEDIDO EN LA MESA fin
	// // ------CAMBIO O VUELTOS
	 function funcion_calcular()
		{
			// CORREGIDO: Lógica original de cálculo de cambio
			let efectivo = document.getElementById('efectivo') ? formatCurrency(document.getElementById('efectivo')) : 0;
			let propina = document.getElementById('propina') ? formatCurrency(document.getElementById('propina')) : 0;
			let tarjeta = document.getElementById('tarjeta') ? formatCurrency(document.getElementById('tarjeta')) : 0;
			let nequi = document.getElementById('nequi') ? formatCurrency(document.getElementById('nequi')) : 0;
			let daviplata = document.getElementById('daviplata') ? formatCurrency(document.getElementById('daviplata')) : 0;
			let bancolombia = document.getElementById('bancolombia') ? formatCurrency(document.getElementById('bancolombia')) : 0;

			var totalDescuento = document.calculo && document.calculo.totalDescuento ? parseInt(document.calculo.totalDescuento.value) || 0 : 0;
			var total = document.calculo && document.calculo.total ? parseInt(document.calculo.total.value) || 0 : 0;

			// Asegurar que todos los valores sean números válidos
			efectivo = isNaN(efectivo) ? 0 : efectivo;
			propina = isNaN(propina) ? 0 : propina;
			tarjeta = isNaN(tarjeta) ? 0 : tarjeta;
			nequi = isNaN(nequi) ? 0 : nequi;
			daviplata = isNaN(daviplata) ? 0 : daviplata;
			bancolombia = isNaN(bancolombia) ? 0 : bancolombia;
			totalDescuento = isNaN(totalDescuento) ? 0 : totalDescuento;
			total = isNaN(total) ? 0 : total;

			// CORREGIDO: Cálculo del cambio SIN sobrescribir el campo "pagar"
			// El campo "pagar" ya tiene el valor correcto del controlador, NO lo sobrescribimos
			var totalPagado = efectivo + tarjeta + nequi + daviplata + bancolombia + totalDescuento;

			// Obtener el total real del campo "pagar" (que ya tiene el valor correcto)
			var totalAPagar = document.getElementById('pagar') ?
				parseInt(document.getElementById('pagar').value.replace(/[^0-9]/g, '')) || 0 :
				(total + propina);

			var cambio = totalPagado - totalAPagar;

			// NO actualizar campo "Total a Pagar" - ya tiene el valor correcto del controlador
			// Solo actualizar el campo hidden si existe (para compatibilidad)
			if (document.calculo && document.calculo.pagar && !document.getElementById('pagar')) {
				document.calculo.pagar.value = totalAPagar;
			}

			// Actualizar campo "Cambio"
			if (document.calculo && document.calculo.cambio) {
				document.calculo.cambio.value = cambio;
				if (document.getElementById('cambio')) {
					document.getElementById('cambio').value = cambio;
					formatCurrency(document.getElementById('cambio'));
				}
			}
		}
	// ------CAMBIO O VUELTOS
	// ------ENVIAR PEDIDO A COCINA--------------
	 function enviarPedido()
		{
		 var pedidoId = document.getElementById('pedido_actual_id').value;

		 if (!pedidoId) {
		 	alert('No hay pedido activo para enviar');
		 	return;
		 }

		 if (confirm('¿Confirma enviar el pedido a cocina?'))
		 	{
			 $.post("views/modules/ajaxEstadoPedidos.php", {
			 	enviar_pedido: true,
			 	pedido_id: pedidoId
			 }, function(data) {
			 	var resultado = JSON.parse(data);
			 	if (resultado.status === 'success') {
			 		alert(resultado.message);
			 		location.reload();
			 	} else {
			 		alert('Error: ' + resultado.message);
			 	}
			 });
		 	}
		}
	// ------ENVIAR PEDIDO FIN//

	// ------IMPRESIÓN POR CATEGORÍAS--------------
	function cargarBotonesImpresion(pedidoId, numeroPedido) {
		console.log('🖨️ Cargando botones de impresión para pedido:', pedidoId);

		$.post("views/modules/ajaxEstadoPedidos.php", {
			obtener_productos_pedido: true,
			pedido_id: pedidoId
		}, function(data) {
			console.log('📋 Productos del pedido:', data);
			try {
				var productos = JSON.parse(data);
				mostrarBotonesCategoria(pedidoId, numeroPedido, productos);
			} catch (e) {
				console.error('Error parseando productos:', e);
				alert('Error obteniendo productos del pedido');
			}
		}).fail(function(xhr, status, error) {
			console.error('Error AJAX:', error);
			alert('Error de conexión al obtener productos');
		});
	}

	function mostrarBotonesCategoria(pedidoId, numeroPedido, productos) {
		// Agrupar productos por categoría
		var categorias = {};
		productos.forEach(function(producto) {
			var cat = producto.categoria || 'cocina';
			if (!categorias[cat]) {
				categorias[cat] = [];
			}
			categorias[cat].push(producto);
		});

		// Crear botones para cada categoría que tenga productos
		var html = '<h6 style="color: #495057; margin-bottom: 10px; font-size: 12px;">🖨️ Impresión por Categorías - ' + numeroPedido + '</h6>';
		html += '<div style="display: flex; flex-wrap: wrap; gap: 8px;">';

		Object.keys(categorias).forEach(function(categoria) {
			var count = categorias[categoria].length;
			var color = getColorCategoria(categoria);
			var icono = getIconoCategoria(categoria);

			html += '<button onclick="imprimirCategoria(\'' + categoria + '\', ' + pedidoId + ', \'' + numeroPedido + '\')" ';
			html += 'style="background-color: ' + color + '; color: white; padding: 6px 12px; border: none; border-radius: 3px; cursor: pointer; font-size: 11px; font-weight: bold;">';
			html += icono + ' ' + categoria.toUpperCase() + ' (' + count + ')';
			html += '</button>';
		});

		html += '</div>';

		document.getElementById('impresion_categorias_' + pedidoId).innerHTML = html;
	}

	function getColorCategoria(categoria) {
		switch(categoria.toLowerCase()) {
			case 'cocina': return '#28a745';
			case 'bar': return '#17a2b8';
			case 'asados': return '#dc3545';
			default: return '#6c757d';
		}
	}

	function getIconoCategoria(categoria) {
		switch(categoria.toLowerCase()) {
			case 'cocina': return '🍳';
			case 'bar': return '🍺';
			case 'asados': return '🔥';
			default: return '🖨️';
		}
	}

	function imprimirCategoria(categoria, pedidoId, numeroPedido) {
		console.log('🖨️ Imprimiendo categoría:', categoria, 'del pedido:', pedidoId);

		if (confirm('¿Imprimir productos de ' + categoria.toUpperCase() + ' del pedido ' + numeroPedido + '?')) {
			// Aquí iría la lógica de impresión específica por categoría
			// Por ahora, mostrar mensaje de confirmación
			alert('🖨️ Enviando a imprimir productos de ' + categoria.toUpperCase() + ' del pedido ' + numeroPedido);

			// TODO: Implementar la impresión real por categoría
			// $.post("views/modules/ajaxImpresion.php", {
			//     categoria: categoria,
			//     pedido_id: pedidoId,
			//     numero_pedido: numeroPedido
			// }, function(response) {
			//     console.log('Respuesta impresión:', response);
			// });
		}
	}
	// ------IMPRESIÓN POR CATEGORÍAS FIN//

	// ------COCINA (MANTENER COMPATIBILIDAD)--------------
	 function cocina()
		{
		 var mesa = document.calculo.mesa.value;
		 if (confirm('confirme Pedido Cocina?'))
		 	{
			 $("#destino").load("views/modules/ajaxCocina.php", {mesa: mesa}, function(){ });
		 	}
		}
	// ------COCINA FIN//
	// ------PRE-FACTURAR--------------
		function preFacturar()
		 {
			//var propina = document.calculo.propina.value;
			let propina=formatCurrency(document.getElementById('propina'));
			var total = document.calculo.total.value;
			var totalDescuento = document.calculo.totalDescuento.value;
			var pcedula = document.calculo.pcedula.value;
			let mesa = document.calculo.mesa.value;
			//alert(total);
			if (confirm('confirme Pre-Facturar ?'))
			 {
				//alert("if");
				$("#destino").load("views/modules/ajaxPreFactura.php", { pcedula: pcedula, totalDescuento:totalDescuento, total:total, propina:propina, mesa:mesa}, function(){ });
			 }
		 }
	// ------ PRE-FACTURAR FIN//
		 function toNumber(valor) {
		    if (!valor) return 0;
		    return parseInt(
		        valor.toString().replace(/\./g, '').replace(',', '.').replace(/[^0-9.-]/g, '')
		    ) || 0;
		}
	// ------FACTURAR--------------
		function facturar()
		 {
			// SIMPLIFICADO: Obtener valores directamente de los campos
			function getFieldValue(fieldId) {
				let field = document.getElementById(fieldId);
				if (!field || !field.value) return 0;
				// Limpiar valor: quitar todo excepto números
				let cleanValue = field.value.toString().replace(/[^0-9]/g, '');
				return parseInt(cleanValue) || 0;
			}

			let efectivo = getFieldValue('efectivo');
			let tarjeta = getFieldValue('tarjeta');
			let nequi = getFieldValue('nequi');
			let daviplata = getFieldValue('daviplata');
			let bancolombia = getFieldValue('bancolombia');
			let propina = getFieldValue('propina');

			// CORREGIDO: Usar el campo 'pagar' que contiene el total real a pagar
			let total = getFieldValue('pagar');

			var tipoTarjeta = document.calculo.tipoTarjeta ? document.calculo.tipoTarjeta.value : 'credito';

			var totalDescuento = document.calculo.totalDescuento.value;
			var pcedula = document.calculo.pcedula.value;
			let mesa = document.calculo.mesa.value;
			let pago = 1; // Siempre efectivo

			// VALIDACIÓN: Verificar que la suma de pagos sea igual o mayor al total
			let totalPagado = efectivo + tarjeta + nequi + daviplata + bancolombia;
			//let totalPagado = parseInt(efectivo) + parseInt(tarjeta) + parseInt(nequi) + parseInt(daviplata) + parseInt(bancolombia);

			if (totalPagado <= 0) {
				alert("❌ No se ha ingresado ninguna forma de pago. total=" + total);
				return;
			}


			// DEBUG: Mostrar valores para diagnóstico
			console.log('🔧 DEBUG VALIDACIÓN PAGOS:');
			console.log('Campo total (original):', getFieldValue('total'));
			console.log('Campo pagar (correcto):', getFieldValue('pagar'));
			console.log('Total usado para validación:', total);
			console.log('Efectivo:', efectivo);
			console.log('Tarjeta:', tarjeta);
			console.log('Nequi:', nequi);
			console.log('Daviplata:', daviplata);
			console.log('Bancolombia:', bancolombia);
			console.log('Total Pagado:', totalPagado);
			console.log('Diferencia:', totalPagado - total);

			if (totalPagado < total) {
				let faltante = total - totalPagado;
				alert('❌ Pago insuficiente\n\n' +
					  'Total a pagar: $' + total.toLocaleString() + '\n' +
					  'Total pagado: $' + totalPagado.toLocaleString() + '\n' +
					  'Falta: $' + faltante.toLocaleString() + '\n\n' +
					  'Por favor, complete el pago en las 5 formas disponibles:\n' +
					  '• Efectivo: $' + efectivo.toLocaleString() + '\n' +
					  '• Tarjeta: $' + tarjeta.toLocaleString() + '\n' +
					  '• Nequi: $' + nequi.toLocaleString() + '\n' +
					  '• Daviplata: $' + daviplata.toLocaleString() + '\n' +
					  '• Bancolombia: $' + bancolombia.toLocaleString());
				return;
			}

			// Calcular cambio
			let cambio = totalPagado - total;
			let mensajeConfirmacion = '¿Confirmar facturación?\n\n' +
									  'Total: $' + total.toLocaleString() + '\n' +
									  'Pagado: $' + totalPagado.toLocaleString() + '\n' +
									  'Cambio: $' + cambio.toLocaleString() + '\n\n' +
									  'Formas de pago:\n';

			if (efectivo > 0) mensajeConfirmacion += '• Efectivo: $' + efectivo.toLocaleString() + '\n';
			if (tarjeta > 0) mensajeConfirmacion += '• Tarjeta: $' + tarjeta.toLocaleString() + '\n';
			if (nequi > 0) mensajeConfirmacion += '• Nequi: $' + nequi.toLocaleString() + '\n';
			if (daviplata > 0) mensajeConfirmacion += '• Daviplata: $' + daviplata.toLocaleString() + '\n';
			if (bancolombia > 0) mensajeConfirmacion += '• Bancolombia: $' + bancolombia.toLocaleString() + '\n';

			if (confirm(mensajeConfirmacion))
			 {
			 	// Mostrar indicador de carga para mesas pesadas
			 	$("#destino").html('<div style="background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;"><h5>⏳ Procesando facturación...</h5><p>Para mesas con muchos pedidos esto puede tomar unos minutos.<br><strong>Mesa ' + mesa + ':</strong> Procesando pedidos con sistema optimizado.</p></div>');

			 	// Usar ajaxFactura.php corregido
				$.ajax({
					url: "views/modules/ajaxFactura.php",
					type: "POST",
					data: {
						efectivo: efectivo,
						bancolombia: bancolombia,
						nequi: nequi,
						daviplata: daviplata,
						tarjeta: tarjeta,
						pago: pago,
						pcedula: pcedula,
						totalDescuento: totalDescuento,
						total: total,
						propina: propina,
						mesa: mesa,
						tipoTarjeta: tipoTarjeta,
						optimizada: true
					},
					timeout: 600000, // 10 minutos para mesas muy pesadas
					success: function(response) {
						console.log('Respuesta facturación optimizada:', response);

						// Limpiar indicador de carga
						$("#destino").html('');

						// NUEVO: Ejecutar script de impresión si existe en la respuesta
						const scriptMatch = response.match(/<script>(.*?)<\/script>/);
						if (scriptMatch) {
							console.log('🖨️ Ejecutando script de impresión:', scriptMatch[1]);
							eval(scriptMatch[1]);
						}

						if (response.includes('success_optimizada') || response.includes('Facturación completada exitosamente') || response.includes('success_corregida')) {
							alert('✅ Facturación completada exitosamente\n\nLa mesa ' + mesa + ' ha sido liberada.\n\nPedidos procesados correctamente.\n\n📄 Abriendo factura para impresión...');

							// NUEVO: Abrir PDF como respaldo si el script no funcionó
							setTimeout(function() {
								window.open('pdf', '_blank');
							}, 1000);

							setTimeout(() => location.reload(), 3000);
						} else if (response.includes('error_optimizada') || response.includes('Error')) {
							alert('❌ Error en la facturación optimizada:\n\n' + response);
						} else if (response.includes('alert')) {
							// Extraer mensaje del alert
							const alertMatch = response.match(/alert\('([^']+)'\)/);
							const mensaje = alertMatch ? alertMatch[1] : response;
							alert('⚠️ ' + mensaje);
						} else {
							alert('⚠️ Respuesta inesperada del servidor:\n\n' + response);
						}
					},
					error: function(xhr, status, error) {
						$("#destino").html('');
						console.error('Error AJAX optimizado:', error, 'Status:', status, 'XHR:', xhr);

						let mensaje = '❌ Error en la facturación:\n\n';
						if (status === 'timeout') {
							mensaje += 'Timeout: La facturación está tomando más tiempo del esperado.\n\nEsto es normal para mesas con muchos pedidos.\n\nPor favor, verifica manualmente si se completó.';
						} else if (xhr.status === 500) {
							mensaje += 'Error interno del servidor (500).\n\nRevisa los logs del servidor para más detalles.\n\nLa mesa puede haberse facturado parcialmente.';
						} else {
							mensaje += 'Error: ' + error + '\nStatus: ' + status + '\n\nVerifica el estado de la mesa manualmente.';
						}

						alert(mensaje);
					}
				});
			 }
		 }
	// ------FACTURAR FIN//
	// ------buscarCliente--------------
	 function buscarCliente()
	  {	//alert("Buscar factura funtion");
		var pcedula = document.calculo.pcedula.value;
		/*if (confirm('confirme Facturar Buscar?'))
		 {*/		//setTimeout('location.href="views/modules/ajaxFactura.php"',500);
			$("#cliente").load("views/modules/ajaxBuscarCedula.php", {pcedula: pcedula}, function(){
			 //alert("recibidos los datos por ajax efectivo");
			});
		 //}
	  }
	// ------buscarCliente FIN//							onkeypress="buscarCliente();"

	// ------FUNCIONES PARA GESTIÓN DE PEDIDOS------
	function marcarEntregado(pedidoId) {
		if (confirm('¿Marcar pedido como entregado?')) {
			$.post("views/modules/ajaxEstadoPedidos.php", {
				entregar_pedido: true,
				pedido_id: pedidoId
			}, function(data) {
				var resultado = JSON.parse(data);
				if (resultado.status === 'success') {
					alert(resultado.message);
					cargarPedidosMesa();
				} else {
					alert('Error: ' + resultado.message);
				}
			});
		}
	}

	function reimprimirPedido(pedidoId, categoria) {
		var motivo = prompt('Motivo de la reimpresión:', 'Reimpresión solicitada');
		if (motivo) {
			$.post("views/modules/ajaxEstadoPedidos.php", {
				reimprimir_pedido: true,
				pedido_id: pedidoId,
				categoria: categoria,
				motivo: motivo
			}, function(data) {
				var resultado = JSON.parse(data);
				alert(resultado.message);
			});
		}
	}

	function cargarPedidosMesa() {
		var mesaId = <?=$_SESSION["mesa"];?>;
		let mesero='<?=$_GET["mesero"]?>';
		console.log('Cargando pedidos para mesa:', mesaId);

		// Cargar pedidos borrador (para agregar productos)
		$.post("views/modules/ajaxEstadoPedidos.php", {
			obtener_pedidos_mesa: true,
			mesa_id: mesaId
		}, function(data) {
			console.log('Respuesta pedidos borrador:', data);
			try {
				var pedidosBorrador = JSON.parse(data);
				console.log('Pedidos borrador parseados:', pedidosBorrador);

				// Cargar pedidos para facturar
				$.post("views/modules/ajaxEstadoPedidos.php", {
					obtener_pedidos_facturar: true,
					mesa_id: mesaId
				}, function(dataFacturar) {
					console.log('Respuesta pedidos facturar:', dataFacturar);
					try {
						var pedidosFacturar = JSON.parse(dataFacturar);
						console.log('Pedidos facturar parseados:', pedidosFacturar);
						mostrarPedidosMesaCompleto(pedidosBorrador, pedidosFacturar, mesero);
					} catch (e) {
						console.error('Error parseando pedidos facturar:', e);
						mostrarPedidosMesa(pedidosBorrador);
					}
				}).fail(function() {
					console.log('Error cargando pedidos facturar, mostrando solo borradores');
					mostrarPedidosMesa(pedidosBorrador);
				});

			} catch (e) {
				console.error('Error parseando JSON:', e);
				console.error('Datos recibidos:', data);
				document.getElementById('lista_pedidos_mesa').innerHTML = '<h4>Pedidos de la Mesa</h4><p style="color: red;">Error cargando pedidos: ' + e.message + '</p>';
			}
		}).fail(function(xhr, status, error) {
			console.error('Error en petición AJAX:', error);
			document.getElementById('lista_pedidos_mesa').innerHTML = '<h4>Pedidos de la Mesa</h4><p style="color: red;">Error de conexión: ' + error + '</p>';
		});
	}

	function mostrarPedidosMesa(pedidos) {
		console.log('Mostrando pedidos (solo borradores):', pedidos);
		var html = '<h4>📝 Pedido Actual</h4>';

		if (!pedidos || pedidos.length === 0) {
			html += '<div class="alert alert-success">✅ Mesa lista para nuevos pedidos</div>';
		} else {
			html += '<div class="alert alert-info">Agregando productos al pedido actual</div>';
			html += '<table class="table table-sm table-striped">';
			html += '<thead class="thead-light"><tr><th>Pedido</th><th>Estado</th><th>Productos</th><th>Fecha</th><th>Acciones</th></tr></thead>';
			html += '<tbody>';

			pedidos.forEach(function(pedido) {
				html += '<tr>';
				html += '<td><strong>' + (pedido.numero_pedido || 'N/A') + '</strong></td>';
				html += '<td><span class="badge badge-' + getBadgeClass(pedido.estado) + '">' + (pedido.estado || 'N/A') + '</span></td>';
				html += '<td>' + (pedido.total_productos || 0) + '</td>';
				html += '<td>' + (pedido.fecha_pedido || 'N/A') + '</td>';
				html += '<td>';
				if (pedido.total_productos > 0) {
					html += '<button class="btn btn-sm btn-primary" onclick="enviarPedido(' + pedido.id + ')">📤 Enviar</button>';
				}
				html += '</td>';
				html += '</tr>';
			});

			html += '</tbody></table>';
		}

		var elemento = document.getElementById('lista_pedidos_mesa');
		if (elemento) {
			elemento.innerHTML = html;
		} else {
			console.error('Elemento lista_pedidos_mesa no encontrado');
		}
	}

	function mostrarPedidosMesaCompleto(pedidosBorrador, pedidosFacturar, mesero) {
		console.log('Mostrando vista completa - Borradores:', pedidosBorrador, 'Facturar:', pedidosFacturar);
		var html = '';

		// Sección de pedido actual (borrador)
		html += '<h4>📝 Pedido Actual</h4>';
		if (!pedidosBorrador || pedidosBorrador.length === 0) {
			html += '<div class="alert alert-success">✅ Mesa lista para nuevos pedidos</div>';
		} else {
			html += '<div class="alert alert-info">Agregando productos al pedido actual</div>';
			html += '<table class="table table-sm table-striped">';
			html += '<thead class="thead-light"><tr><th>Pedido</th><th>Estado</th><th>Productos</th><th>Fecha</th><th>Acciones</th></tr></thead>';
			html += '<tbody>';

			pedidosBorrador.forEach(function(pedido) {
				html += '<tr>';
				html += '<td><strong>' + (pedido.numero_pedido || 'N/A') + '</strong></td>';
				html += '<td><span class="badge badge-' + getBadgeClass(pedido.estado) + '">' + (pedido.estado || 'N/A') + '</span></td>';
				html += '<td>' + (pedido.total_productos || 0) + '</td>';
				html += '<td>' + (pedido.fecha_pedido || 'N/A') + '</td>';
				html += '<td>';
				if (pedido.total_productos > 0) {
					html += '<button class="btn btn-sm btn-primary" onclick="enviarPedido(' + pedido.id + ')">📤 Enviar</button>';
				}
				html += '</td>';
				html += '</tr>';
			});

			html += '</tbody></table>';
		}

		// Sección de pedidos para facturar
		if (pedidosFacturar && pedidosFacturar.length > 0) {
			html += '<h4 class="mt-4">💰 Pedidos Listos para Facturar</h4>';
			html += '<div class="alert alert-warning">Pedidos enviados/entregados listos para facturar</div>';
			html += '<table class="table table-sm table-striped">';
			html += '<thead class="thead-dark"><tr><th>Pedido</th><th>Estado</th><th>Productos</th><th>Fecha</th><th>Acciones</th></tr></thead>';
			html += '<tbody>';

			pedidosFacturar.forEach(function(pedido) {
				html += '<tr>';
				html += '<td><strong>' + (pedido.numero_pedido || 'N/A') + '</strong></td>';
				html += '<td><span class="badge badge-' + getBadgeClass(pedido.estado) + '">' + (pedido.estado || 'N/A') + '</span></td>';
				html += '<td>' + (pedido.total_productos || 0) + '</td>';
				html += '<td>' + (pedido.fecha_pedido || 'N/A') + '</td>';
				html += '<td>';

				if (pedido.estado === 'enviado') {
					html += '<button class="btn btn-sm btn-success" onclick="marcarEntregado(' + pedido.id + ')">✅ Entregado</button> ';
				}

				html += '<button class="btn btn-sm btn-info" onclick="reimprimirPedido(' + pedido.id + ')">🖨️ Reimprimir</button>';
				html += '</td>';
				html += '</tr>';

				// RESTAURADO: Agregar botones de impresión por categorías para pedidos enviados
				if (pedido.estado === 'enviado') {
					html += '<tr>';
					html += '<td colspan="5" style="padding: 0; border-top: none;">';
					html += '<div id="impresion_categorias_' + pedido.id + '" style="background-color: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 5px; border: 1px solid #dee2e6;">';
					html += '<h6 style="color: #495057; margin-bottom: 10px; font-size: 12px;">🖨️ Impresión por Categorías - ' + (pedido.numero_pedido || 'N/A') + '</h6>';
					html += '<div style="display: flex; flex-wrap: wrap; gap: 8px;">';
					html += '<button onclick="cargarBotonesImpresion(' + pedido.id + ', \'' + (pedido.numero_pedido || 'N/A') + '\', \'' + mesero + '\')" ';
					html += 'style="background-color: #007bff; color: white; padding: 6px 12px; border: none; border-radius: 3px; cursor: pointer; font-size: 11px; font-weight: bold;">🔄 Cargar Botones</button>';
					html += '</div>';
					html += '</div>';
					html += '</td>';
					html += '</tr>';
				}
			});

			html += '</tbody></table>';
		}

		var elemento = document.getElementById('lista_pedidos_mesa');
		if (elemento) {
			elemento.innerHTML = html;
		} else {
			console.error('Elemento lista_pedidos_mesa no encontrado');
		}
	}

	function getBadgeClass(estado) {
		switch(estado) {
			case 'borrador': return 'secondary';
			case 'enviado': return 'warning';
			case 'entregado': return 'success';
			case 'facturado': return 'primary';
			default: return 'light';
		}
	}

	// INTEGRACIÓN: Función para cargar botones de impresión por categorías
	function cargarBotonesImpresion(pedidoId, numeroPedido, mesero) {
		var mesaId = <?=$_SESSION["mesa"]?>;
		var contenedor = document.getElementById('impresion_categorias_' + pedidoId);

		if (!contenedor) {
			console.error('Contenedor no encontrado para pedido:', pedidoId);
			return;
		}

		contenedor.innerHTML = '<h6 style="color: #495057; margin-bottom: 10px; font-size: 12px;">🔄 Cargando botones...</h6>';

		console.log('🔄 Iniciando carga de componente para pedido:', pedidoId);

		// Hacer petición AJAX para cargar el componente
		fetch('./views/modules/ajax_cargar_componente_impresion.php', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({
				pedido_id: pedidoId,
				numero_pedido: numeroPedido,
				mesa_id: mesaId,
				mesero: mesero
			})
		})
		.then(response => {
			console.log('📡 Respuesta recibida, status:', response.status);
			return response.text();
		})
		.then(html => {
			console.log('📄 HTML recibido (primeros 200 chars):', html.substring(0, 200));
			contenedor.innerHTML = html;
			console.log('✅ Componente cargado en el DOM');

			// Ejecutar scripts que están dentro del HTML cargado
			const scripts = contenedor.querySelectorAll('script');
			scripts.forEach(script => {
				console.log('🔧 Ejecutando script...');
				const newScript = document.createElement('script');
				newScript.textContent = script.textContent;
				document.head.appendChild(newScript);
				document.head.removeChild(newScript);
			});

			console.log('🔍 Verificando funciones después de ejecutar scripts:', {
				imprimirCategoria: typeof window.imprimirCategoria,
				abrirVentanaImpresion: typeof window.abrirVentanaImpresion
			});
		})
		.catch(error => {
			console.error('❌ Error cargando componente:', error);
			contenedor.innerHTML = '<h6 style="color: #dc3545;">❌ Error cargando botones: ' + error.message + '</h6>';
		});
	}

// Funcion Aumentar cantidad de producto en la mesa
	 function aumentar(producto_id, mesa_id, fecha_hora)
		{
		 // Verificar permisos antes de permitir modificación
		 var pedidoId = document.getElementById('pedido_actual_id').value;

		 if (!pedidoId) {
		 	alert('No hay pedido activo');
		 	return;
		 }

		 // Verificar si el usuario puede modificar este pedido
		 $.post("views/modules/ajaxEstadoPedidos.php", {
		 	verificar_permisos: true,
		 	pedido_id: pedidoId
		 }, function(data) {
		 	var resultado = JSON.parse(data);
		 	if (!resultado.puede_modificar) {
		 		alert('No tiene permisos para modificar este pedido. Solo los administradores pueden modificar pedidos enviados.');
		 		return;
		 	}

		 	// Si tiene permisos, proceder con el aumento
		 	$.post("views/modules/ajaxPedidoVentas.php",{producto_id:producto_id,mesa_id:mesa_id,fecha_hora:fecha_hora  },function(data)
			{
                if (data.trim()==='success') {
                    location.reload()
                }else{
                	alert("Cantidad no aumentada "+data);
                }
		    });
		 });
		}

// Funcion Aumentar cantidad de producto en la mesa
	 function aumentarCantidad(producto_id, mesa_id, fecha_hora)
		{
		 // Cambiar el valor del formulario input para busqueda de producto addCantidad
		  //alert("Aumentar producto: "+producto_id+" mesa_id: "+mesa_id+" fecha Hora: "+fecha_hora);

		  $.post("views/modules/ajaxPedidoVentas.php",{producto_id:producto_id,mesa_id:mesa_id,fecha_hora:fecha_hora  },function(data)
			{
                if (data.trim()==='success') {
                    //alert("Cantidad "+producto_id+" aumentada Correctamente "+data);
                    location.reload()
                    //displayBancos();
                }else{
                	alert("Cantidad no aumentada "+data);
                }
		    });
		}

// Funcion Disminuir cantidad de producto en la mesa
	 function disminuir(producto_id, mesa_id, fecha_hora, cantidad)
		{
		 // Verificar permisos antes de permitir modificación
		 var pedidoId = document.getElementById('pedido_actual_id').value;

		 if (!pedidoId) {
		 	alert('No hay pedido activo');
		 	return;
		 }

		 // Verificar si el usuario puede modificar este pedido
		 $.post("views/modules/ajaxEstadoPedidos.php", {
		 	verificar_permisos: true,
		 	pedido_id: pedidoId
		 }, function(data) {
		 	var resultado = JSON.parse(data);
		 	if (!resultado.puede_modificar) {
		 		alert('No tiene permisos para modificar este pedido. Solo los administradores pueden modificar pedidos enviados.');
		 		return;
		 	}

		 	// Si tiene permisos y cantidad > 0, proceder con la disminución
		 	if(cantidad>0){
				  $.post("views/modules/ajaxPedidoVentas.php",{reducirProducto:producto_id,mesa_id:mesa_id,fecha_hora:fecha_hora  },function(data)
					{
		                if (data.trim()==='success') {
		                    location.reload()
		                }else{
		                	alert("Cantidad no disminuida "+data);
		                }
				    });
				}
		 });
		}

// Obtener mesa seleccionada
var mesa_id = <?=$_SESSION["mesa"]?>;

// Buscar si hay un pedido abierto (no facturado) para la mesa
var pedido_actual = null;
if (mesa_id) {
    var xhr = new XMLHttpRequest();
    xhr.open("GET", "views/modules/ajaxBuscarPedidoAbierto.php?mesa_id=" + mesa_id, false);
    xhr.send();
    if (xhr.status === 200) {
        pedido_actual = JSON.parse(xhr.responseText);
    }
}

// Si no hay pedido abierto, crear uno nuevo al agregar productos
if (!pedido_actual && document.querySelector('form[name="product"]')) {
    var formData = new FormData(document.querySelector('form[name="product"]'));
    formData.append('mesa_id', mesa_id);

    var xhr = new XMLHttpRequest();
    xhr.open("POST", "views/modules/ajaxCrearPedido.php", false);
    xhr.send(formData);

    if (xhr.status === 200) {
        pedido_actual = JSON.parse(xhr.responseText);
    }
}

// Al agregar productos, siempre asociar al pedido actual
if (pedido_actual) {
    var inputs_producto = document.querySelectorAll('input[name="producto_id"]');
    inputs_producto.forEach(function(input) {
        input.value = pedido_actual.id;
    });
}

// Mostrar solo productos del pedido actual
if (pedido_actual) {
    var xhr = new XMLHttpRequest();
    xhr.open("GET", "views/modules/ajaxProductosPorPedido.php?pedido_id=" + pedido_actual.id, false);
    xhr.send();

    if (xhr.status === 200) {
        var productos_pedido = JSON.parse(xhr.responseText);
        // Aquí puedes mostrar los productos en tu tabla o donde sea necesario
    }
}

// Función para cancelar un pedido individual
function cancelarPedidoIndividual(pedidoId) {
    if (confirm('⚠️ ¿Confirma que desea cancelar este pedido?\n\n' +
               '✅ Esto cancelará solo este pedido específico\n' +
               '❌ Esta acción NO se puede deshacer')) {
        
        // Verificar si el usuario puede modificar este pedido
        $.post("views/modules/ajaxEstadoPedidos.php", {
            cancelar_pedido: true,
            pedido_id: pedidoId
        }, function(data) {
            try {
                var resultado = JSON.parse(data);
                if (resultado.status === "success") {
                    alert("Pedido cancelado correctamente");
                    location.reload();
                } else {
                    alert("Error al cancelar el pedido: " + resultado.message);
                }
            } catch (e) {
                alert("Error en la respuesta del servidor");
                console.error(e, data);
            }
        });
    }
}

// Función para cancelar un producto específico
function cancelarProducto(productoId, pedidoId) {
    if (confirm('⚠️ ¿Confirma que desea cancelar este producto?\n\n' +
               '✅ Esto eliminará solo este producto del pedido\n' +
               '❌ Esta acción NO se puede deshacer')) {
        
        $.post("views/modules/ajaxPedidoVentas.php", {
            cancelarProducto: productoId,
            pedido_id: pedidoId
        }, function(data) {
            if (data.trim() === 'success') {
                location.reload();
            } else {
                alert("Error al cancelar el producto: " + data);
            }
        });
    }
}
</script>
<style type="text/css">
	.etiqueta {
	 width: 120px;
	 float: left;
	 line-height: 28px;
	 font-size: 20px;
	}
	.input_container {
	 height: 30px;
	 float: left;
	}
	.input_container input {
	 height: 20px;
	 width: 260px;
	 padding: 3px;
	 border: 1px solid #cccccc;
	 border-radius: 0;
	}
	.input_container ul {
	 width: 270px;
	 border: 1px solid #eaeaea;
	 position: absolute;
	 z-index: 9;
	 background: #f3f3f3;
	 list-style: none;
	 margin-left: 5px;
	margin-top: -3px;
	}
	.input_container ul li {
	 padding: 2px;
	}
	.input_container ul li:hover {
	 background: #eaeaea;
	}
	#country_list_id {
	 display: none;
	}
</style>
 <div class="row">
    <div class="col-md-9">
      	<form name="calculo" method="post" >
		  	<div>
		  		<br>
		  		<h3>Facturar Venta -> Mesa <?=$_SESSION["mesa"]?></h3>
		  		<?php if($pedidoBorrador): ?>
		  			<div class="alert alert-info">
		  				<strong>Pedido Actual: <?=$pedidoBorrador['numero_pedido']?></strong>
		  				<span class="badge badge-secondary">Borrador</span>
		  				<button type="button" class="btn btn-sm btn-primary float-right" onclick="enviarPedido()">
		  					Enviar Pedido
		  				</button>
		  			</div>
		  			<input type="hidden" id="pedido_actual_id" value="<?=$pedidoBorrador['id']?>">
		  		<?php else: ?>
		  			<div class="alert alert-warning">
		  				No hay pedido activo. Se creará uno nuevo al agregar productos.
		  			</div>
		  			<input type="hidden" id="pedido_actual_id" value="">
		  		<?php endif; ?>
		  	</div><br>
			 Cliente No Cedula:  <input type="text" name="pcedula" onkeyup="buscarCliente();" value="<?=$_SESSION["clientep"];?>" id="pcedula" >&nbsp;
			<a href="registro" target="_blank"> Registrar Cliente &raquo;</a>	<span id="cliente"></span><!--<br>-->
			--> <?=$_GET["mesero"]?>
		  	<input type="hidden" id="mesa"  name="mesa" value="<?=$_SESSION["mesa"];?>" readonly >
		  	<input type="hidden" name="totalDescuento" id="totalDescuento" value="0">
		  	<input type="hidden" name="total" id="total" value="0">
		  	<!-- <input type="hidden"  name="pago" id="pago" value="1">-->
		  	<!-- SIMPLIFICADO: Solo pago en efectivo -->
		  	<input type="hidden" name="pago" id="pago" value="1">
		  	<div align="right">
	  		 <h5><b>💰 Pago en Efectivo</b></h5>
		  	</div>
			<table border="1" class="table table-hover">
				<thead>
					<tr>
						<th>COD. B</th>
						<th>NOMBRE</th>
						<th>FECHA</th>
						<th>PRECIO</th>
						<th>DESC</th>
						<th>CANT</th>
						<th>TOTAL</th>
						<th></th>
					</tr>
				</thead>
				<tbody>
					<?php
						//$vistaPmesa = new controllerPedidoMesaVendido();
						$vistaPmesa -> vistaPmesaController();
						$vistaPmesa -> borrarPmesaController();
						$datos_mesas = $vistaPmesa -> vistaMesaIdController($_SESSION["mesa"]);
						$mesa_mesero=$datos_mesas["descripcion"];
					?>

					<tr>
							<td colspan="6"><h5><b>Total a Pagar </b></h5></td>
						<td colspan="3"><input type="text" placeholder="$" name="pagar" id="pagar" onkeyup="funcion_calcular();" value="0"></td>
					</tr>
					<tr>
							<td colspan="6"><h5><b>Recibo en efectivo </b> </h5></td>
						<td colspan="3"><input type="text" placeholder="$" name="efectivo" id="efectivo" onkeyup="funcion_calcular();" value="0"> </td>
					</tr>
					<tr>
						<td colspan="3"><b>Tarjeta</b> <input type="text" placeholder="$" name="tarjeta" id="tarjeta" onkeyup="funcion_calcular();"  value="0"> </td>
						<td colspan="3">
							<div>	<input type="radio" name="tipoTarjeta" value="Credito" checked>
									<label> Cred &nbsp; &nbsp; &nbsp; </label>
									<input type="radio" name="tipoTarjeta" value="Debito">
									<label> Deb </label>
							</div>
						</td>
						<td colspan="2">
							<label> <b>Bancolombia</b> </label>
							<input type="text" placeholder="$" name="bancolombia" id="bancolombia" onkeyup="funcion_calcular();"  value="0">
						</td>
					</tr>
					<tr>
						<td colspan="4"><b>Nequi</b> <input type="text" placeholder="$" name="nequi" id="nequi" onkeyup="funcion_calcular();" value="0"> </td>
						<td colspan="2">
							<label> Daviplata </label>
						</td>
						<td colspan="3"><input type="text" placeholder="$" name="daviplata" id="daviplata" onkeyup="funcion_calcular();" value="0">
						</td>
					</tr>
					<tr>
						<td colspan="6"><h5><b>Propina </b> </h5></td>
						<td colspan="3"><input type="text" placeholder="$" name="propina" id="propina" onkeyup="funcion_calcular();" value="0"></td>
					</tr>
					<tr>
						<td colspan="6"><h5><b>Cambio </b> </h5></td>
						<td colspan="2"><input type="text" placeholder="$" name="cambio" id="cambio" readonly></td>
					</tr>
				</tbody>
			</table>
			<span id="destino" > </span>
			<p><a class="btn btn-primary btn-lg"  role="button" onclick="cancelarPedido();" name="cancelar" value="cancelar">Cancelar &raquo;</a>
			<?php
			 if ($_SESSION["tipo_usuario"]==1 or $_SESSION["tipo_usuario"]==3)
				{
				echo'<a class="btn btn-primary btn-lg"  role="button" onclick="facturar();" name="factura" value="factura">Facturar &raquo;</a>
					<a class="btn btn-primary btn-lg"  role="button" onclick="preFacturar();" name="prefactura" value="prefactura">PreFacturar &raquo;</a>';
				}
			 ?>

			<!--<a class="btn btn-primary btn-lg"  role="button" onclick="cocina();" name="cocina" value="cocina">Cocina &raquo;</a>--></p>
			<!--input type="submit"  value="Facturar">-->
		 </form>

			 <!-- Sección de pedidos de la mesa -->
			 <div class="mt-4">
			 	<div id="lista_pedidos_mesa">
			 		<h4>Pedidos de la Mesa --</h4>

			 		<!-- DEBUG: Mostrar información de debug -->
			 		<div style="background: #f0f0f0; padding: 10px; margin: 10px 0; border: 1px solid #ccc;">
			 			<strong>DEBUG:</strong><br>
			 			Mesa ID: <?=$_SESSION["mesa"]?><br>
			 			Pedidos encontrados: <?=count($pedidosMesa)?><br>
			 			Pedido borrador: <?=$pedidoBorrador ? $pedidoBorrador['numero_pedido'] : 'No hay'?><br>
			 			<?php if(!empty($pedidosMesa)): ?>
			 				Pedidos:
			 				<?php foreach($pedidosMesa as $p): ?>
			 					[<?=$p['numero_pedido']?> - <?=$p['estado']?>]
			 				<?php endforeach; ?>
			 			<?php endif; ?>
			 		</div>

			 		<?php if(!empty($pedidosMesa)): ?>
			 			<table class="table table-sm">
			 				<thead>
			 					<tr>
			 						<th>Pedido--</th>
			 						<th>Estado</th>
			 						<th>Productos</th>
			 						<th>Fecha</th>
			 						<th>Acciones</th>
			 					</tr>
			 				</thead>
			 				<tbody>
			 					<?php foreach($pedidosMesa as $pedido): ?>
			 					  <?php if($pedido['estado'] != 'cancelado' && $pedido['estado'] != 'facturado'): ?>
			 						<tr>
			 							<td><?=$pedido['numero_pedido']?><br><?=$pedido['estado']?></td>
			 							<td>
			 								<span class="badge badge-<?php
			 									echo $pedido['estado'] == 'borrador' ? 'secondary' :
			 										($pedido['estado'] == 'enviado' ? 'warning' :
			 										($pedido['estado'] == 'entregado' ? 'success' : 'primary'));
			 								?>"><?=$pedido['estado']?></span>
			 							</td>
			 							<td><?=$pedido['total_productos']?></td>
			 							<td><?=date('d/m/Y H:i', strtotime($pedido['fecha_pedido']))?></td>
			 							<td>
			 								<?php if($pedido['estado'] == 'enviado'): ?>
			 									<button class="btn btn-sm btn-success" onclick="marcarEntregado(<?=$pedido['id']?>)">
			 										Entregado
			 									</button>
			 								<?php endif; ?>

			 								<?php if($pedido['estado'] != 'borrador'): ?>
			 									<div class="btn-group">
			 										<button class="btn btn-sm btn-info dropdown-toggle" data-toggle="dropdown">
			 											Reimprimir
			 										</button>
			 										<div class="dropdown-menu">
			 											<a class="dropdown-item" href="#" onclick="reimprimirPedido(<?=$pedido['id']?>, 'bar')">Bar</a>
			 											<a class="dropdown-item" href="#" onclick="reimprimirPedido(<?=$pedido['id']?>, 'cocina')">Cocina</a>
			 											<a class="dropdown-item" href="#" onclick="reimprimirPedido(<?=$pedido['id']?>, 'asados')">Asados</a>
			 											<a class="dropdown-item" href="#" onclick="reimprimirPedido(<?=$pedido['id']?>)">Todo</a>
			 										</div>
			 									</div>
			 								<?php endif; ?>

			 								<!-- Botón Cancelar Pedido Individual -->
			 								<?php if($pedido['estado'] == 'borrador' || $pedido['estado'] == 'enviado'): ?>
			 									<button class="btn btn-sm btn-danger" onclick="cancelarPedidoIndividual(<?=$pedido['id']?>)" title="Cancelar este pedido">
			 										🗑️ Cancelar
			 									</button>
			 								<?php endif; ?>
			 							</td>
			 						</tr>
			 						<?php
			 						// INTEGRACIÓN: Mostrar botones de impresión por categorías para pedidos enviados
			 						if($pedido['estado'] == 'enviado'): ?>
			 							<tr>
			 								<td colspan="5" style="padding: 0; border-top: none;">
			 									<?php
			 									mostrarBotonesImpresionCategorias(
			 										$pedido['id'],
			 										$pedido['numero_pedido'],
			 										$_SESSION["mesa"],
			 										$mesa_mesero
			 									);
			 									?>
			 								</td>
			 							</tr>
			 						<?php endif; ?>
			 					  <?php endif; // If que evalua si no esta cancelado el pedido?> 
			 					<?php endforeach; ?>
			 				</tbody>
			 			</table>
			 		<?php else: ?>
			 			<p style="color: red; font-weight: bold;">No hay pedidos para esta mesa (Mesa ID: <?=$_SESSION["mesa"]?>)</p>
			 		<?php endif; ?>
			 	</div>
			 </div>
    </div>
	<div class="col-md-3" style="background-color:#ACADB4; border-radius: 15px 15px 15px 15px; padding:12px"></p>
		<form name="product" method="post">
			<div class="ui-widget">
			 	<div ><h4>Producto <?php echo 'Pedido : '.$_SESSION["mesa"];?></h4></div><br><br><br>

				<label>  CANT : </label> <input type="text" placeholder="1" name="cantidadPmesaRegistro" value="1"  required><br>
				<label>  Codi : </label> <input autocomplete="off" type="text" placehlder="PRODUCTO" id="codigo" name="codigo" autofocus="autofocus"  required><br>
				<label> CDes :</label> <input type="text" placeholder="10" name="descuentoP" value="0" ><br>
				<input type="hidden" placeholder="MESA" name="mesas_idPmesaRegistro" value="<?=$_SESSION["mesa"];?>" >
				<label>  Nota : </label> <textarea name="nota">-</textarea> <br>
				<!-- Campo de categoría para productos -->
				<div class="form-group">
				    <label for="categoria">Categoría</label>
				    <select name="categoria" id="categoria" class="form-control" required>
				        <option value="bar">Bar</option>
				        <option value="cocina">Cocina</option>
				        <option value="asados">Asados</option>
				    </select>
				</div>
				<input type="submit" value="Enviar">
				<br><br>
			<div class="input_container">
				<label>Nombre:</label>
				<input type="text" autocomplete="off" id="nombrepr" placeholder="PRODUCTO" name="nombrepr" onkeyup="autocompletar()" >
				<ul id="lista_id"></ul><br>
			</div>
	    	<br><br><br><br><br><br>
				<?php
			 		$registroPmesa = new controllerPedidoMesaVendido();
					$registroPmesa -> registroPmesaController();
			 	?>
				<br><br><br><br><br><br>
				<br><br><br><br><br><br>
				<br><br><br><br><br><br>
				</div>
			</div>
		</form>
	</div>
</div>
