<?php
require_once "../../models/crud.php";
require_once "../../models/crudSuministro.php";
require_once "../../controllers/controller.php";
require_once "../../controllers/controllerSuministro.php";
	ini_set("session.cookie_lifetime","28800");
	ini_set("session.gc_maxlifetime","28800");
		//echo'<br> <script>alert("Entro al ajax Buscar Suministros '.$_POST['placa'].'");</script> <br>';
if(isset($_POST['placa']))
	{
		$queryString = $_POST['placa'];
		//echo'<br> <script>alert("Entro al ajax");</script> <br>';
		//echo'<br> <script>alert("Entro al ajax Buscar Suministros '.$_POST['placa'].'");</script> <br>';
		$ajax=new controllerSuministro();
		$r=$ajax->ajaxBuscarCodigo1Controller($queryString);
	 if ($r>0)
		{	//echo' <script>alert("Se encontraron resultados");</script>';
		 echo'<b>
		 Precio Compra:<input type="text"  name="precioC" id="precioC" value="'.$r['sprecio'].'" ><br>
		 IVA Compra:<input type="text"  name="iva" id="iva" value="'.$r['siva'].'" style="width: 40px;"><br>
					<input type="hidden"  name="sid" id="sid" value="'.$r['sid'].'" ><br>
					<input type="hidden"  name="costo" id="costo" value="'.$r['sprecio'].'" >
		 Descuento:<input type="text"  name="descuento" id="descuento"  value="0" style="width: 40px;">&nbsp;&nbsp;&nbsp;&nbsp;1<br>
		 Nombre:&nbsp;&nbsp;&nbsp;<input type="text"  name="nombre" id="nombre" value="'.$r['snombre'].'" readonly>	 	<b>';
		}
	 else
		{	echo"No existe Codigo barra Digitado, registrero o verifique";
		 echo '<div style="background-color:#fff; padding:12px"> <b>
		 No existe Codigo barra Digitado, registrero o verifique <b>		</div>';
		}
	} //window.open("registroProductoSuministro","_blank")'




?>