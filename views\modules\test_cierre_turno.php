<?php
// Test para verificar el sistema de cierre de turno
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
if (!isset($_SESSION["usuario"])) {
    $_SESSION["usuario"] = 15;
    $_SESSION["tipo_usuario"] = 1;
    $_SESSION["perfil"] = "administrador";
}

require_once "../../models/conexion.php";
require_once "../../models/crudTurno.php";
require_once "../../controllers/controllerTurno.php";

echo "<h1>🔄 Test Sistema de Cierre de Turno</h1>";
echo "<p>Verificando el funcionamiento del cierre de turno y generación de PDF</p>";

try {
    $db = Conexion::conectar();
    $usuario = $_SESSION["usuario"];
    
    echo "<h3>1. 👤 Usuario Actual</h3>";
    echo "<div class='alert alert-info'>";
    echo "<p><strong>Usuario ID:</strong> $usuario</p>";
    echo "<p><strong>Tipo:</strong> {$_SESSION['tipo_usuario']}</p>";
    echo "</div>";
    
    echo "<h3>2. 🔍 Verificar Turno Activo</h3>";
    
    // Buscar turno activo
    $sqlTurnoActivo = "SELECT id, persona_id, fecha_inicio, fecha_final, cantidad_inicio, cantidad_final 
                       FROM turnos_cajeros 
                       WHERE persona_id = ? AND fecha_final = '0000-00-00 00:00:00'
                       ORDER BY id DESC LIMIT 1";
    $stmtTurno = $db->prepare($sqlTurnoActivo);
    $stmtTurno->execute([$usuario]);
    $turnoActivo = $stmtTurno->fetch(PDO::FETCH_ASSOC);
    
    if ($turnoActivo) {
        echo "<div class='alert alert-success'>";
        echo "<h4>✅ Turno Activo Encontrado</h4>";
        echo "<p><strong>ID Turno:</strong> {$turnoActivo['id']}</p>";
        echo "<p><strong>Fecha Inicio:</strong> {$turnoActivo['fecha_inicio']}</p>";
        echo "<p><strong>Cantidad Inicial:</strong> $" . number_format($turnoActivo['cantidad_inicio']) . "</p>";
        echo "</div>";
        
        $turnoId = $turnoActivo['id'];
        
        echo "<h3>3. 💰 Ventas del Turno</h3>";
        
        // Obtener ventas del turno
        $sqlVentas = "SELECT v.id, v.fecha_venta, v.total, v.efectivo, v.valortarjeta, v.bancolombia, v.nequi, v.daviplata, v.mesa
                      FROM ventas v 
                      WHERE v.turno_cajero_id = ? 
                      ORDER BY v.fecha_venta DESC";
        $stmtVentas = $db->prepare($sqlVentas);
        $stmtVentas->execute([$turnoId]);
        $ventas = $stmtVentas->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($ventas)) {
            echo "<div class='alert alert-success'>";
            echo "<h4>✅ Ventas Encontradas: " . count($ventas) . "</h4>";
            echo "<table class='table table-striped'>";
            echo "<thead><tr><th>ID</th><th>Fecha</th><th>Mesa</th><th>Total</th><th>Efectivo</th><th>Otros</th></tr></thead>";
            echo "<tbody>";
            
            $totalVentas = 0;
            $totalEfectivo = 0;
            foreach ($ventas as $venta) {
                $otros = $venta['valortarjeta'] + $venta['bancolombia'] + $venta['nequi'] + $venta['daviplata'];
                $totalVentas += $venta['total'];
                $totalEfectivo += $venta['efectivo'];
                
                echo "<tr>";
                echo "<td>{$venta['id']}</td>";
                echo "<td>{$venta['fecha_venta']}</td>";
                echo "<td>{$venta['mesa']}</td>";
                echo "<td>$" . number_format($venta['total']) . "</td>";
                echo "<td>$" . number_format($venta['efectivo']) . "</td>";
                echo "<td>$" . number_format($otros) . "</td>";
                echo "</tr>";
            }
            
            echo "</tbody>";
            echo "<tfoot><tr class='info'>";
            echo "<th colspan='3'>TOTALES:</th>";
            echo "<th>$" . number_format($totalVentas) . "</th>";
            echo "<th>$" . number_format($totalEfectivo) . "</th>";
            echo "<th>$" . number_format($totalVentas - $totalEfectivo) . "</th>";
            echo "</tr></tfoot>";
            echo "</table>";
            echo "</div>";
            
            // Configurar variables de sesión para el reporte
            $_SESSION["inicio"] = $turnoActivo['fecha_inicio'];
            $_SESSION["turno"] = $turnoId;
            $_SESSION["ventas"] = $totalVentas;
            $_SESSION["efectivo_turno"] = $totalEfectivo;
            
            // Crear array de reporte para el PDF
            $reporteTurno = array();
            foreach ($ventas as $index => $venta) {
                $reporteTurno[$index] = array(
                    'vfecha' => $venta['fecha_venta'],
                    'vid' => $venta['id'],
                    'vtotal' => $venta['total']
                );
            }
            $_SESSION["turnoV"] = $reporteTurno;
            
        } else {
            echo "<div class='alert alert-warning'>";
            echo "<h4>⚠️ No hay ventas en este turno</h4>";
            echo "</div>";
        }
        
        echo "<h3>4. 📊 Resumen de Cierre</h3>";
        echo "<div class='alert alert-info'>";
        echo "<p><strong>Cantidad Inicial:</strong> $" . number_format($turnoActivo['cantidad_inicio']) . "</p>";
        echo "<p><strong>Total Ventas:</strong> $" . number_format($totalVentas ?? 0) . "</p>";
        echo "<p><strong>Efectivo Recibido:</strong> $" . number_format($totalEfectivo ?? 0) . "</p>";
        echo "<p><strong>Cantidad Final Esperada:</strong> $" . number_format($turnoActivo['cantidad_inicio'] + ($totalEfectivo ?? 0)) . "</p>";
        echo "</div>";
        
        echo "<h3>5. 🧪 Test de PDF</h3>";
        echo "<div class='text-center'>";
        echo "<a href='pdfTurno' target='_blank' class='btn btn-primary btn-lg'>📄 Ver PDF de Turno</a>";
        echo "<p class='mt-2'><small>Este enlace abrirá el PDF con los datos actuales del turno</small></p>";
        echo "</div>";
        
        echo "<h3>6. 🔧 Problemas Detectados en pdfTurno.php</h3>";
        echo "<div class='alert alert-danger'>";
        echo "<h4>❌ Errores encontrados:</h4>";
        echo "<ul>";
        echo "<li><strong>Variable \$i no definida:</strong> En línea 41-43</li>";
        echo "<li><strong>Código duplicado:</strong> Líneas 55-59 repiten el contenido</li>";
        echo "<li><strong>Falta bucle:</strong> No itera sobre todas las ventas</li>";
        echo "<li><strong>Falta información:</strong> No muestra totales ni resumen</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<h3>7. ✅ Solución Recomendada</h3>";
        echo "<div class='alert alert-success'>";
        echo "<p>Se necesita corregir el archivo <code>pdfTurno.php</code> para:</p>";
        echo "<ul>";
        echo "<li>Iterar correctamente sobre las ventas</li>";
        echo "<li>Mostrar totales y resumen</li>";
        echo "<li>Eliminar código duplicado</li>";
        echo "<li>Mejorar el formato del reporte</li>";
        echo "</ul>";
        echo "</div>";
        
    } else {
        echo "<div class='alert alert-warning'>";
        echo "<h4>⚠️ No hay turno activo</h4>";
        echo "<p>Para probar el cierre de turno, primero debe iniciar un turno.</p>";
        echo "</div>";
        
        echo "<h3>3. 📋 Turnos Recientes</h3>";
        
        // Mostrar turnos recientes
        $sqlTurnos = "SELECT t.id, t.persona_id, t.fecha_inicio, t.fecha_final, t.cantidad_inicio, t.cantidad_final,
                             p.nombre, p.apellidos
                      FROM turnos_cajeros t
                      LEFT JOIN personas p ON t.persona_id = p.id
                      WHERE t.persona_id = ?
                      ORDER BY t.id DESC LIMIT 5";
        $stmtTurnos = $db->prepare($sqlTurnos);
        $stmtTurnos->execute([$usuario]);
        $turnos = $stmtTurnos->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($turnos)) {
            echo "<table class='table table-striped'>";
            echo "<thead><tr><th>ID</th><th>Usuario</th><th>Inicio</th><th>Fin</th><th>Cant. Inicial</th><th>Cant. Final</th><th>Estado</th></tr></thead>";
            echo "<tbody>";
            
            foreach ($turnos as $turno) {
                $estado = ($turno['fecha_final'] == '0000-00-00 00:00:00') ? 'Activo' : 'Cerrado';
                $estadoClass = ($estado == 'Activo') ? 'success' : 'default';
                
                echo "<tr>";
                echo "<td>{$turno['id']}</td>";
                echo "<td>{$turno['nombre']} {$turno['apellidos']}</td>";
                echo "<td>{$turno['fecha_inicio']}</td>";
                echo "<td>" . (($turno['fecha_final'] == '0000-00-00 00:00:00') ? 'N/A' : $turno['fecha_final']) . "</td>";
                echo "<td>$" . number_format($turno['cantidad_inicio']) . "</td>";
                echo "<td>$" . number_format($turno['cantidad_final']) . "</td>";
                echo "<td><span class='label label-$estadoClass'>$estado</span></td>";
                echo "</tr>";
            }
            
            echo "</tbody></table>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>❌ Error en el test</h4>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>

<style>
.table { margin-top: 10px; }
.alert { margin: 15px 0; }
.btn { margin: 10px 5px; }
.label-success { background-color: #5cb85c; }
.label-default { background-color: #777; }
.label { padding: 3px 6px; border-radius: 3px; color: white; font-size: 11px; }
</style>
