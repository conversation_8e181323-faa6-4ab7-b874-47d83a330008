<?php

require_once "conexion.php";

class DatosImpresionPedidos {
    
    /*=============================================
    OBTENER IMPRESORAS POR CATEGORIA
    =============================================*/
    static public function obtenerImpresorasModel($tabla) {
        $stmt = Conexion::conectar()->prepare("SELECT categoria, ip, puerto FROM $tabla");
        $stmt->execute();
        return $stmt->fetchAll();
        //$stmt->close();
        $stmt = null;
    }
    
    /*=============================================
    OBTENER PRODUCTOS DEL PEDIDO CON CATEGORIA
    =============================================*/
    static public function obtenerProductosPedidoModel($pedidoId) {
        $stmt = Conexion::conectar()->prepare("
            SELECT p.nombre, pvm.cantidad, p.categoria, pvm.nota, m.numero as mesa_numero
            FROM producto_vendido_mesa pvm
            JOIN productos p ON pvm.productos_id = p.id
            JOIN mesas m ON pvm.mesas_id = m.id
            WHERE pvm.pedidos_id = ?
            ORDER BY p.categoria, p.nombre
        ");
        $stmt->bindParam(1, $pedidoId, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
        //$stmt->close();
        $stmt = null;
    }
    
    /*=============================================
    OBTENER ULTIMO PEDIDO DE UNA MESA
    =============================================*/
    static public function obtenerUltimoPedidoMesaModel($mesaId) {
        $stmt = Conexion::conectar()->prepare("
            SELECT MAX(pedidos_id) as ultimo_pedido 
            FROM producto_vendido_mesa 
            WHERE mesas_id = ?
        ");
        $stmt->bindParam(1, $mesaId, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetch();
        //$stmt->close();
        $stmt = null;
    }
    
    /*=============================================
    OBTENER PRODUCTOS DEL PEDIDO PARA FACTURACION
    =============================================*/
    static public function obtenerProductosFacturacionModel($pedidoId) {
        $stmt = Conexion::conectar()->prepare("
            SELECT p.nombre, ppm.cantidad, p.categoria
            FROM pedido_productos_mesa ppm
            JOIN productos p ON ppm.productos_id = p.id
            WHERE ppm.pedidos_id = ?
            ORDER BY p.categoria, p.nombre
        ");
        $stmt->bindParam(1, $pedidoId, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
        //$stmt->close();
        $stmt = null;
    }

    /*=============================================
    OBTENER TODOS LOS PEDIDOS DE UNA MESA PARA FACTURAR
    =============================================*/
    static public function obtenerTodosPedidosMesaModel($mesaId) {
        $stmt = Conexion::conectar()->prepare("
            SELECT DISTINCT p.id as pedido_id, p.fecha_pedido
            FROM pedidos p
            WHERE p.mesa_id = ? AND p.facturado = 'n'
            ORDER BY p.fecha_pedido ASC
        ");
        $stmt->bindParam(1, $mesaId, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
        //$stmt->close();
        $stmt = null;
    }

    /*=============================================
    OBTENER PRODUCTOS DE TODOS LOS PEDIDOS DE UNA MESA
    =============================================*/
    static public function obtenerProductosTodosPedidosMesaModel($mesaId) {
        $stmt = Conexion::conectar()->prepare("
            SELECT p.nombre, pvm.cantidad, p.categoria, pvm.nota, 
                   m.numero as mesa_numero, ped.id as pedido_id
            FROM producto_vendido_mesa pvm
            JOIN productos p ON pvm.productos_id = p.id
            JOIN mesas m ON pvm.mesas_id = m.id
            JOIN pedidos ped ON pvm.pedidos_id = ped.id
            WHERE ped.mesa_id = ? AND ped.facturado = 'n'
            ORDER BY ped.fecha_pedido ASC, p.categoria, p.nombre
        ");
        $stmt->bindParam(1, $mesaId, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
        //$stmt->close();
        $stmt = null;
    }
}

