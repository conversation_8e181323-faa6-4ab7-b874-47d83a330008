<?php

	#EXTENSIÓN DE CLASES: Los objetos pueden ser extendidos, y pueden heredar propiedades y métodos. Para definir una clase como extensión, debo definir una clase padre, y se utiliza dentro de una clase hija.

	require_once "conexion.php";

	class DatosPedido extends Conexion
		{
			
			##PEDIDO  id, mesero_id, facturado
			#----------------------------------------------
				#REGISTRO DE PEDIDO
				#-------------------------------------
					public static function registroPedidoModel($datosModel, $tabla)
						{
							echo "<script>alert('Entro CRUD  es')</script>";

							$consulta="INSERT INTO $tabla (mesero_id, facturado) VALUES (:mesero_id, :facturado)";
							echo "<script>alert('Entro CRUD ".$consulta." no')</script>";	
							$stmt = Conexion::conectar()->prepare($consulta);
							$stmt->execute();			
							$stmt->bindParam(":mesero_id", $datosModel["mesero_id"], PDO::PARAM_INT);						
							$stmt->bindParam(":facturado", $datosModel["facturado"], PDO::PARAM_STR);			
							
							echo "<script>alert('Guardo exito')</script>";			

							if($stmt->execute())
								{	return "success";	}
							else{ return "error";	}
							$stmt->close();
						}

				#VISTA PEDIDO
				#-------------------------------------
					public static function vistaPedidoModel($tabla)
						{
							$consulta = "SELECT id, mesero_id, facturado FROM $tabla";
							$stmt = Conexion::conectar()->prepare($consulta);	
							$stmt->execute();				
							return $stmt->fetchAll();
							$stmt->close();
						}

				#EDITAR PEDIDO
				#-------------------------------------
					public static function editarPedidoModel($datosModel, $tabla)
						{
							$stmt = Conexion::conectar()->prepare("SELECT id, mesero_id, facturado FROM $tabla WHERE id = :id");
							$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);	
							$stmt->execute();
							return $stmt->fetch();
							$stmt->close();
						}

				#ACTUALIZAR PEDIDO 
				#-------------------------------------
					public static function actualizarPedidoModel($datosModel, $tabla)
						{
							echo "<script>alert('entro cruz Suministro')</script>";
							$consulta = "UPDATE $tabla SET mesero_id = :mesero_id, facturado= :facturado WHERE  id = :id";
							echo "<script>alert('entro cruz Suministro ".$consulta."')</script>";

							$stmt = Conexion::conectar()->prepare($consulta);			
							$stmt->bindParam(":mesero_id", $datosModel["mesero_id"], PDO::PARAM_INT);							
							$stmt->bindParam(":facturado", $datosModel["facturado"], PDO::PARAM_STR);				
							$stmt->bindParam(":id", $datosModel["id"], PDO::PARAM_INT);

							echo "<script>alert('centro proceso')</script>";
							if($stmt->execute())
								{echo "<script>alert('Guardo Actualizar Proveedor')</script>";return "success";	}
							else{echo "<script>alert('Error base de dato serve')</script>";	return "error";			}
							$stmt->close();
						}

				#BORRAR PEDIDO
				#------------------------------------
					public static function borrarPedidoModel($datosModel, $tabla)
					{
						$stmt = Conexion::conectar()->prepare("DELETE FROM $tabla WHERE id = :id");
						$stmt->bindParam(":id", $datosModel, PDO::PARAM_INT);
						if($stmt->execute())
							{	return "success";	}
						else{	return "error";		}

						$stmt->close();
					}
			#----------------------------------------------

		}