<?php
// Solución para cPanel - Alternativas de Impresión
echo "<h1>🌐 Solución para cPanel</h1>";
echo "<p><strong>Alternativas cuando no se puede usar Node.js en el hosting</strong></p>";

echo "<div style='background-color: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 20px 0;'>";
echo "<h3>⚠️ Limitaciones de cPanel/Hosting Compartido:</h3>";
echo "<ul>";
echo "<li>❌ <strong>No permite Node.js</strong> en la mayoría de casos</li>";
echo "<li>❌ <strong>No permite servidores personalizados</strong></li>";
echo "<li>❌ <strong>Bloquea conexiones TCP</strong> a puertos no estándar (9100)</li>";
echo "<li>❌ <strong>No permite abrir puertos</strong> personalizados</li>";
echo "</ul>";
echo "</div>";

echo "<h2>💡 Soluciones Alternativas:</h2>";

// Solución 1: Proxy en tu computadora
echo "<div style='border: 1px solid #007bff; padding: 20px; margin: 15px 0; border-radius: 8px;'>";
echo "<h3>🖥️ Solución 1: Proxy en tu Computadora (RECOMENDADA)</h3>";
echo "<p><strong>El proxy se ejecuta en tu portátil, no en cPanel</strong></p>";

echo "<h4>✅ Ventajas:</h4>";
echo "<ul>";
echo "<li>✅ <strong>Control total</strong> sobre el proxy</li>";
echo "<li>✅ <strong>Acceso directo</strong> a las impresoras (192.168.68.x)</li>";
echo "<li>✅ <strong>Funciona desde celulares</strong> en la misma red</li>";
echo "<li>✅ <strong>No depende del hosting</strong></li>";
echo "</ul>";

echo "<h4>📋 Configuración:</h4>";
echo "<ol>";
echo "<li><strong>En tu portátil:</strong></li>";
echo "<ul>";
echo "<li>Instalar Node.js</li>";
echo "<li>Ejecutar: <code>node proxy-macarena-mobile.js</code></li>";
echo "<li>Proxy disponible en: <code>http://***********:3000</code></li>";
echo "</ul>";
echo "<li><strong>En Macarena (cPanel):</strong></li>";
echo "<ul>";
echo "<li>Configurar IPs de impresoras → <code>***********:3000</code></li>";
echo "<li>Usar rutas: <code>/cocina</code>, <code>/bar</code>, <code>/asados</code></li>";
echo "</ul>";
echo "<li><strong>Desde celulares:</strong></li>";
echo "<ul>";
echo "<li>Conectar a la misma WiFi</li>";
echo "<li>Acceder a: <code>http://***********:3000</code></li>";
echo "</ul>";
echo "</ol>";
echo "</div>";

// Solución 2: PHP Proxy
echo "<div style='border: 1px solid #28a745; padding: 20px; margin: 15px 0; border-radius: 8px;'>";
echo "<h3>🐘 Solución 2: Proxy PHP (En cPanel)</h3>";
echo "<p><strong>Crear un proxy simple en PHP que funcione en cPanel</strong></p>";

echo "<h4>⚠️ Limitaciones:</h4>";
echo "<ul>";
echo "<li>⚠️ <strong>Puede no funcionar</strong> si el hosting bloquea conexiones TCP</li>";
echo "<li>⚠️ <strong>Menos eficiente</strong> que Node.js</li>";
echo "<li>⚠️ <strong>Timeouts más cortos</strong></li>";
echo "</ul>";

echo "<h4>📝 Código PHP Proxy:</h4>";
echo "<pre style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; font-size: 11px; max-height: 300px;'>";
echo htmlspecialchars('<?php
// proxy_impresoras.php - Proxy PHP para cPanel
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header("Content-Type: application/json");

if ($_SERVER["REQUEST_METHOD"] === "OPTIONS") {
    exit(0);
}

// Configuración de impresoras
$impresoras = [
    "cocina" => ["ip" => "**************", "puerto" => 9100],
    "bar" => ["ip" => "**************", "puerto" => 9100],
    "asados" => ["ip" => "**************", "puerto" => 9100]
];

// Obtener ruta solicitada
$ruta = $_GET["ruta"] ?? "";
$datos = file_get_contents("php://input");

function enviarAImpresora($ip, $puerto, $datos) {
    $socket = @fsockopen($ip, $puerto, $errno, $errstr, 5);
    
    if (!$socket) {
        return [
            "success" => false,
            "error" => "No se pudo conectar: $errstr ($errno)"
        ];
    }
    
    if (!empty($datos)) {
        fwrite($socket, $datos);
    }
    
    fclose($socket);
    
    return [
        "success" => true,
        "mensaje" => "Enviado correctamente",
        "ip" => $ip,
        "puerto" => $puerto
    ];
}

// Procesar solicitud
if (isset($impresoras[$ruta])) {
    $config = $impresoras[$ruta];
    $resultado = enviarAImpresora($config["ip"], $config["puerto"], $datos);
    echo json_encode($resultado);
} else {
    echo json_encode([
        "success" => false,
        "error" => "Ruta no válida",
        "rutas_disponibles" => array_keys($impresoras)
    ]);
}
?>');
echo "</pre>";

echo "<h4>📋 Uso del proxy PHP:</h4>";
echo "<ul>";
echo "<li><strong>Cocina:</strong> <code>proxy_impresoras.php?ruta=cocina</code></li>";
echo "<li><strong>Bar:</strong> <code>proxy_impresoras.php?ruta=bar</code></li>";
echo "<li><strong>Asados:</strong> <code>proxy_impresoras.php?ruta=asados</code></li>";
echo "</ul>";
echo "</div>";

// Solución 3: Servicio externo
echo "<div style='border: 1px solid #6f42c1; padding: 20px; margin: 15px 0; border-radius: 8px;'>";
echo "<h3>☁️ Solución 3: Servicio VPS/Cloud</h3>";
echo "<p><strong>Usar un VPS económico para el proxy</strong></p>";

echo "<h4>💰 Opciones económicas:</h4>";
echo "<ul>";
echo "<li><strong>DigitalOcean:</strong> $5/mes - VPS básico</li>";
echo "<li><strong>Vultr:</strong> $2.50/mes - VPS mínimo</li>";
echo "<li><strong>Linode:</strong> $5/mes - VPS básico</li>";
echo "<li><strong>AWS EC2:</strong> Capa gratuita 12 meses</li>";
echo "</ul>";

echo "<h4>✅ Ventajas:</h4>";
echo "<ul>";
echo "<li>✅ <strong>Control total</strong> del servidor</li>";
echo "<li>✅ <strong>Node.js completo</strong></li>";
echo "<li>✅ <strong>IP fija</strong></li>";
echo "<li>✅ <strong>Disponible 24/7</strong></li>";
echo "</ul>";
echo "</div>";

// Solución 4: Impresión directa desde celular
echo "<div style='border: 1px solid #fd7e14; padding: 20px; margin: 15px 0; border-radius: 8px;'>";
echo "<h3>📱 Solución 4: Impresión Directa desde Celular</h3>";
echo "<p><strong>Los celulares se conectan directamente a las impresoras</strong></p>";

echo "<h4>📋 Configuración:</h4>";
echo "<ol>";
echo "<li><strong>Aplicación móvil personalizada</strong> que se conecte directamente</li>";
echo "<li><strong>PWA (Progressive Web App)</strong> con JavaScript</li>";
echo "<li><strong>Configuración manual</strong> de IPs en cada dispositivo</li>";
echo "</ol>";

echo "<h4>⚠️ Limitaciones:</h4>";
echo "<ul>";
echo "<li>⚠️ <strong>Solo funciona en la red local</strong></li>";
echo "<li>⚠️ <strong>Configuración por dispositivo</strong></li>";
echo "<li>⚠️ <strong>No funciona desde internet</strong></li>";
echo "</ul>";
echo "</div>";

// Recomendación final
echo "<div style='background-color: #d4edda; padding: 20px; border-left: 4px solid #28a745; margin: 20px 0;'>";
echo "<h3>🎯 Recomendación Final:</h3>";
echo "<p><strong>Usar Solución 1: Proxy en tu Portátil</strong></p>";

echo "<h4>📋 Plan de implementación:</h4>";
echo "<ol>";
echo "<li><strong>Ejecutar proxy en tu portátil</strong> (Node.js)</li>";
echo "<li><strong>Configurar Macarena</strong> para usar tu IP como proxy</li>";
echo "<li><strong>Celulares acceden</strong> a través de tu portátil</li>";
echo "<li><strong>Tu portátil</strong> reenvía a las impresoras locales</li>";
echo "</ol>";

echo "<h4>🔄 Flujo de datos:</h4>";
echo "<p><code>Celular → WiFi → Tu Portátil (Proxy) → Impresoras</code></p>";
echo "<p><code>Servidor cPanel → Internet → Tu Portátil (Proxy) → Impresoras</code></p>";

echo "<h4>✅ Ventajas:</h4>";
echo "<ul>";
echo "<li>✅ <strong>Funciona inmediatamente</strong></li>";
echo "<li>✅ <strong>No requiere cambios en cPanel</strong></li>";
echo "<li>✅ <strong>Soporte completo para celulares</strong></li>";
echo "<li>✅ <strong>Impresión por categoría</strong></li>";
echo "<li>✅ <strong>Costo: $0</strong></li>";
echo "</ul>";
echo "</div>";

// Configuración automática
echo "<h2>⚡ Configuración Automática:</h2>";

if (isset($_POST['configurar_proxy_local'])) {
    try {
        require_once '../../models/conexion.php';
        $conexion = new Conexion();
        $pdo = $conexion->conectar();
        
        // Actualizar configuración para usar el proxy local
        $ip_proxy = "***********";
        $puerto_proxy = "3000";
        
        $stmt = $pdo->prepare("UPDATE impresoras SET ip = ?, puerto = ? WHERE nombre IN ('COCINA', 'BAR', 'ASADOS')");
        $stmt->execute([$ip_proxy, $puerto_proxy]);
        
        echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0;'>";
        echo "<h4>✅ Configuración Actualizada</h4>";
        echo "<p>Todas las impresoras ahora apuntan a tu proxy local: <strong>$ip_proxy:$puerto_proxy</strong></p>";
        echo "<p><strong>Siguiente paso:</strong> Ejecutar el proxy en tu portátil</p>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div style='background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
        echo "<h4>❌ Error</h4>";
        echo "<p>Error: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
}

echo "<form method='POST' style='background-color: #e7f3ff; padding: 20px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>🔧 Configurar Proxy Local Automáticamente:</h4>";
echo "<p>Esto configurará todas las impresoras para usar tu portátil como proxy</p>";
echo "<button type='submit' name='configurar_proxy_local' style='background-color: #007bff; color: white; padding: 15px 30px; border: none; border-radius: 5px; font-weight: bold; cursor: pointer;'>⚡ Configurar Proxy Local</button>";
echo "</form>";

// Botones de navegación
echo "<p style='margin-top: 30px;'>";
echo "<a href='sistema_impresion_completo.php' style='background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>← Sistema Completo</a>";
echo "<a href='proxy_dinamico.php' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔄 Proxy Dinámico</a>";
echo "</p>";
?>
