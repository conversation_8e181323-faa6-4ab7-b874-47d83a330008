-- =====================================================
-- SQL CORREGIDO para Sistema de Impresión Híbrida - Macarena
-- Base de datos: ewogjwfm_macarena
-- Estructura real de tabla impresoras: id, categoria, ip, puerto
-- =====================================================

-- 1. Tabla de configuración del sistema híbrido
-- =====================================================
CREATE TABLE IF NOT EXISTS config_impresion (
    id INT AUTO_INCREMENT PRIMARY KEY,
    clave VARCHAR(100) NOT NULL UNIQUE,
    valor TEXT NOT NULL,
    descripcion TEXT,
    fecha_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insertar configuraciones por defecto
INSERT IGNORE INTO config_impresion (clave, valor, descripcion) VALUES
('modo_impresion', 'hibrido', 'Modo de impresión: directo, proxy, hibrido'),
('ip_proxy', '***********', 'IP del proxy (tu portátil)'),
('puerto_proxy', '3000', 'Puerto del proxy'),
('timeout_conexion', '5', 'Timeout en segundos para conexiones'),
('reintentos_max', '3', 'Número máximo de reintentos'),
('red_local', '192.168.68', 'Prefijo de red local'),
('impresion_directa_habilitada', '1', 'Permitir impresión directa desde red local');

-- =====================================================
-- 2. Tabla de mapeo categoría → impresora
-- =====================================================
CREATE TABLE IF NOT EXISTS categoria_impresora (
    id INT AUTO_INCREMENT PRIMARY KEY,
    categoria VARCHAR(100) NOT NULL,
    impresora VARCHAR(50) NOT NULL COMMENT 'Debe coincidir con impresoras.categoria',
    activo BOOLEAN DEFAULT TRUE,
    fecha_creacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    fecha_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_categoria (categoria),
    INDEX idx_categoria (categoria),
    INDEX idx_impresora (impresora),
    INDEX idx_activo (activo)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Mapeo automático por defecto
-- IMPORTANTE: Los valores de 'impresora' deben coincidir con impresoras.categoria
INSERT IGNORE INTO categoria_impresora (categoria, impresora) VALUES
('Bebidas', 'bar'),
('Cervezas', 'bar'),
('Licores', 'bar'),
('Vinos', 'bar'),
('Cocteles', 'bar'),
('Refrescos', 'bar'),
('Jugos', 'bar'),
('Agua', 'bar'),
('Comidas', 'cocina'),
('Platos', 'cocina'),
('Entradas', 'cocina'),
('Sopas', 'cocina'),
('Ensaladas', 'cocina'),
('Postres', 'cocina'),
('Desayunos', 'cocina'),
('Almuerzos', 'cocina'),
('Carnes', 'asados'),
('Parrilla', 'asados'),
('Asados', 'asados'),
('Pescados', 'asados'),
('Mariscos', 'asados'),
('Pollo', 'asados');

-- =====================================================
-- 3. Tabla de logs de impresión
-- =====================================================
CREATE TABLE IF NOT EXISTS logs_impresion (
    id INT AUTO_INCREMENT PRIMARY KEY,
    impresora VARCHAR(50) NOT NULL COMMENT 'categoria de la impresora',
    metodo VARCHAR(20) NOT NULL COMMENT 'directo, proxy',
    success BOOLEAN NOT NULL,
    tiempo_ms DECIMAL(10,2) DEFAULT NULL COMMENT 'Tiempo de respuesta en milisegundos',
    error TEXT DEFAULT NULL,
    ip_cliente VARCHAR(45) DEFAULT NULL,
    user_agent TEXT DEFAULT NULL,
    datos_enviados TEXT DEFAULT NULL COMMENT 'Datos que se intentaron imprimir',
    categoria VARCHAR(100) DEFAULT NULL COMMENT 'Categoría del producto si aplica',
    pedido_id INT DEFAULT NULL COMMENT 'ID del pedido si aplica',
    fecha_hora TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_fecha (fecha_hora),
    INDEX idx_impresora (impresora),
    INDEX idx_metodo (metodo),
    INDEX idx_success (success),
    INDEX idx_ip_cliente (ip_cliente),
    INDEX idx_categoria (categoria),
    INDEX idx_pedido (pedido_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 4. Verificar estructura actual de impresoras
-- =====================================================

-- Mostrar estructura de tabla impresoras
DESCRIBE impresoras;

-- Mostrar impresoras disponibles (estructura real)
SELECT 
    'IMPRESORAS DISPONIBLES' as tipo,
    id,
    categoria as nombre_impresora,
    ip,
    puerto,
    'Activa' as estado
FROM impresoras 
ORDER BY categoria;

-- =====================================================
-- 5. Verificar configuración del sistema
-- =====================================================

-- Mostrar configuración del sistema
SELECT 
    'CONFIGURACION SISTEMA' as tipo,
    clave,
    valor,
    descripcion,
    fecha_actualizacion
FROM config_impresion 
ORDER BY clave;

-- Mostrar mapeo de categorías
SELECT 
    'MAPEO CATEGORIAS' as tipo,
    categoria,
    impresora,
    CASE WHEN activo = 1 THEN 'Activo' ELSE 'Inactivo' END as estado,
    fecha_creacion
FROM categoria_impresora 
ORDER BY categoria;

-- Mostrar categorías de productos existentes
SELECT 
    'CATEGORIAS PRODUCTOS' as tipo,
    categoria,
    COUNT(*) as total_productos
FROM productos 
WHERE categoria IS NOT NULL AND categoria != ''
GROUP BY categoria 
ORDER BY categoria;

-- =====================================================
-- 6. Verificar compatibilidad con sistema existente
-- =====================================================

-- Verificar que las impresoras en mapeo existen
SELECT 
    'VERIFICACION MAPEO' as tipo,
    ci.categoria as categoria_producto,
    ci.impresora as impresora_asignada,
    CASE 
        WHEN i.categoria IS NOT NULL THEN 'Impresora existe'
        ELSE 'ERROR: Impresora no existe'
    END as estado_verificacion,
    i.ip,
    i.puerto
FROM categoria_impresora ci
LEFT JOIN impresoras i ON ci.impresora = i.categoria
ORDER BY ci.categoria;

-- =====================================================
-- 7. Consultas de mantenimiento
-- =====================================================

-- Ver logs recientes (últimas 24 horas)
-- SELECT 
--     fecha_hora,
--     impresora,
--     metodo,
--     CASE WHEN success = 1 THEN 'ÉXITO' ELSE 'FALLO' END as resultado,
--     tiempo_ms,
--     error,
--     ip_cliente
-- FROM logs_impresion 
-- WHERE fecha_hora >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
-- ORDER BY fecha_hora DESC
-- LIMIT 50;

-- Estadísticas de éxito por impresora
-- SELECT 
--     impresora,
--     metodo,
--     COUNT(*) as total_intentos,
--     SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as exitosos,
--     ROUND(100 * SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) / COUNT(*), 2) as porcentaje_exito,
--     ROUND(AVG(CASE WHEN success = 1 THEN tiempo_ms END), 2) as tiempo_promedio_ms
-- FROM logs_impresion 
-- WHERE fecha_hora >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
-- GROUP BY impresora, metodo
-- ORDER BY impresora, metodo;

-- =====================================================
-- 8. Actualizar configuración (ejemplos)
-- =====================================================

-- Cambiar IP del proxy
-- UPDATE config_impresion SET valor = '*************' WHERE clave = 'ip_proxy';

-- Cambiar modo de impresión
-- UPDATE config_impresion SET valor = 'directo' WHERE clave = 'modo_impresion';

-- Asignar nueva categoría a impresora
-- INSERT INTO categoria_impresora (categoria, impresora) VALUES ('Nueva_Categoria', 'cocina')
-- ON DUPLICATE KEY UPDATE impresora = 'cocina', activo = 1;

-- Desactivar mapeo de categoría
-- UPDATE categoria_impresora SET activo = 0 WHERE categoria = 'categoria_no_usada';

-- =====================================================
-- 9. Limpiar datos antiguos
-- =====================================================

-- Limpiar logs antiguos (más de 30 días)
-- DELETE FROM logs_impresion WHERE fecha_hora < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- =====================================================
-- 10. Verificación final
-- =====================================================

-- Contar registros en cada tabla
SELECT 'config_impresion' as tabla, COUNT(*) as registros FROM config_impresion
UNION ALL
SELECT 'categoria_impresora' as tabla, COUNT(*) as registros FROM categoria_impresora
UNION ALL
SELECT 'logs_impresion' as tabla, COUNT(*) as registros FROM logs_impresion
UNION ALL
SELECT 'impresoras' as tabla, COUNT(*) as registros FROM impresoras;

-- =====================================================
-- NOTAS IMPORTANTES:
-- =====================================================
-- 
-- 1. La tabla 'impresoras' tiene estructura:
--    - id: INT AUTO_INCREMENT
--    - categoria: ENUM('bar','cocina','asados') 
--    - ip: VARCHAR(20)
--    - puerto: INT(11)
--
-- 2. En categoria_impresora.impresora se debe usar:
--    - 'bar' para impresora del bar
--    - 'cocina' para impresora de cocina  
--    - 'asados' para impresora de asados
--
-- 3. El sistema híbrido usará estos valores para:
--    - Buscar la IP correcta en impresoras.ip
--    - Conectarse al puerto correcto
--    - Hacer fallback entre métodos
--
-- =====================================================
