
<?php 
	ob_start();
	class controllerFactura extends MvcController
		{
			
			####### FACTURA
			#---------------------------------
				#REGISTRO DE FACTURA pedidos_id, turno_cajero_id, valor, fecha_venta, subtotal, iva, total, efectivo, debito, credito, cambio Factura factura
				#------------------------------------
					public function registroFacturaController()
						{	//echo "<script>alert('Entro CONTROl   q no')</script>";	
							/*$iva=$_POST["ivaFacturaRegistro"];
							$subtotal=$_POST["subtotalFacturaRegistro"];
							$total= calculoFacturaTotal($iva,$subtotal);
							$cambio=calculoFacturaCambio($_POST["efectivoFacturaRegistro"],$total);*/
							if(isset($_POST["pedidos_idFacturaRegistro"]) &&  isset($_POST["turno_cajero_idFacturaRegistro"]) && isset($_POST["valorFacturaRegistro"]))
								{	
									$datosController =array('pedidos_id'=>$_POST["pedidos_idFacturaRegistro"],
															'turno_cajero_id'=>$_POST["turno_cajero_idFacturaRegistro"],
															'valor'=>$_POST["valorFacturaRegistro"],
															'fecha_venta'=>$_POST["fecha_ventaFacturaRegistro"],
															'subtotal'=>$_POST["subtotalFacturaRegistro"],
															'iva'=>$_POST["ivaFacturaRegistro"],
															'total'=>$_POST["totalFacturaRegistro"],
															//'total'=>$total,
															'efectivo'=>$_POST["efectivoFacturaRegistro"],
															'debito'=>$_POST["debitoFacturaRegistro"],
															'credito'=>$_POST["creditoFacturaRegistro"],
															'cambio'=>$_POST["cambioFacturaRegistro"]);
									//echo "<script>alert('Entro CONTROl  IF')</script>";
															
															//'cambio'=>$cambio
									//echo "<script>alert('Entro Controller ".$datosController['cantidad_finalTurnoRegistro']." no');</script>";	
									$respuesta = DatosFactura::registroFacturaModel($datosController, "ventas");
									if($respuesta == "success")
										{	header("location:index.php?action=okF");	}
									else
										{	header("location:index.php");	}
								}
						}

				#CALCULO FACTURA
				#-----------------------------
					public function calculoFacturaTotal($iva,$subtotal)
						{
							$total = $iva*$subtotal + $subtotal;
							return $total;
						}
					public function calculoFacturaCambio($efectivo, $total)
						{
							$cambio = $efectivo -$total;
							return $cambio;
						}
					 
				#----------------------------

				#VISTA DE FACTURA
				#------------------------------------
					public function vistaFacturaController()
						{
							$respuesta = DatosFactura::vistaFacturaModel("ventas");
							foreach($respuesta as $row => $item)
								{
								echo'<tr>						
										<td>'.$item["id"].'</td>						
										<td>'.$item["pedidos_id"].'</td>						
										<td>'.$item["turno_cajero_id"].'</td>						
															
										<td>'.$item["fecha_venta"].'</td>						
															
										<td>'.$item["total"].'</td>						
										<td>'.$item["efectivo"].'</td>	
																
										<td>'.$item["cambio"].'</td>						
										<td><a href="index.php?action=editarFactura&id='.$item["id"].'">Editar</a></td>
										<td><a href="index.php?action=factura&idBorrar='.$item["id"].'">Borrar</a></td>
									</tr>';
								}
						}

				#EDITAR FACTURA
				#------------------------------------
					public function editarFacturaController()
						{
							$datosController = $_GET["id"];
							$respuesta = DatosFactura::editarFacturaModel($datosController, "ventas");
							echo' <input type="text" value="'.$respuesta["pedidos_id"].'" name="pedidos_idFacturaEditar" required>	
							 <input type="text" value="'.$respuesta["turno_cajero_id"].'" name="turno_cajero_idFacturaEditar" required> 
							 <input type="text" value="'.$respuesta["valor"].'" name="valorFacturaEditar" required> 
							 <input type="text" value="'.$respuesta["fecha_venta"].'" name="fecha_ventaFacturaEditar" required> 
							 <input type="text" value="'.$respuesta["subtotal"].'" name="subtotalFacturaEditar" required> 
							 <input type="text" value="'.$respuesta["iva"].'" name="ivaFacturaEditar" required> 
							 <input type="text" value="'.$respuesta["total"].'" name="totalFacturaEditar" required> 
							 <input type="text" value="'.$respuesta["efectivo"].'" name="efectivoFacturaEditar" required> 
							 <input type="text" value="'.$respuesta["debito"].'" name="debitoFacturaEditar" required> 
							 <input type="text" value="'.$respuesta["credito"].'" name="creditoFacturaEditar" required> 
							 <input type="text" value="'.$respuesta["cambio"].'" name="cambioFacturaEditar" required> 						 
							
							 <input type="hidden" value="'.$respuesta["id"].'" name="idFacturaEditar" required> 				
								 <input type="submit" value="Actualizar">';
						}

				#ACTUALIZAR FACTURA
				#------------------------------------
					public function actualizarFacturaController()
						{//echo "<script>alert('Entro Controller Actualizar Producto')</script>";

							if(isset($_POST["persona_idTurnoEditar"]))
								{
									$datosController = array(  "pedidos_id"=>$_POST["pedidos_idFacturaEditar"],			
																"turno_cajero_id"=>$_POST["turno_cajero_idFacturaEditar"],				
																"valor"=>$_POST["valorFacturaEditar"],				
																"fecha_venta"=>$_POST["fecha_ventaFacturaEditar"],				
																"subtotal"=>$_POST["subtotalFacturaEditar"],				
																"iva"=>$_POST["ivaFacturaEditar"],				
																"total"=>$_POST["totalFacturaEditar"],				
																"efectivo"=>$_POST["efectivoFacturaEditar"],				
																"debito"=>$_POST["debitoFacturaEditar"],				
																"credito"=>$_POST["creditoFacturaEditar"],				
																"cambio"=>$_POST["cambioFacturaEditar"],
																"id"=>$_POST["idFacturaEditar"]);				
									$respuesta = DatosFactura::actualizarFacturaModel($datosController, "ventas");
									if($respuesta == "success")
										{	header("location:index.php?action=cambioF");	}
									else
										{	echo "error";	}
								}				
						}

				#BORRAR FACTURA
				#------------------------------------
					public function borrarFacturaController()
						{
							if(isset($_GET["idBorrar"]))
								{
									$datosController = $_GET["idBorrar"];				
									$respuesta = DatosFactura::borrarFacturaModel($datosController, "ventas");
									if($respuesta == "success")
										{	header("location:index.php?action=factura");	}
								}
						}			
			#----------------------------------------------
		}			
			