<?php
session_start();

echo "<!DOCTYPE html>";
echo "<html><head><title>Test Productos DB</title></head><body>";
echo "<h2>📦 Test Directo de Productos en Base de Datos</h2>";

// Test de conexión y productos
try {
    // Probar ambas rutas
    if (file_exists("../../models/conexion.php")) {
        require_once "../../models/conexion.php";
        echo "<p style='color: green;'>✅ Usando ruta: ../../models/conexion.php</p>";
    } elseif (file_exists("models/conexion.php")) {
        require_once "models/conexion.php";
        echo "<p style='color: green;'>✅ Usando ruta: models/conexion.php</p>";
    } else {
        throw new Exception("No se puede encontrar conexion.php");
    }
    
    $db = Conexion::conectar();
    echo "<p style='color: green;'>✅ Conexión a base de datos exitosa</p>";
    
    // Verificar tabla productos
    echo "<h3>📊 Información de la Tabla Productos:</h3>";
    
    // Contar productos
    $sql = "SELECT COUNT(*) as total FROM productos";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<p><strong>Total de productos:</strong> " . $result['total'] . "</p>";
    
    if ($result['total'] > 0) {
        // Mostrar estructura de la tabla
        echo "<h4>🔧 Estructura de la tabla:</h4>";
        $sql = "DESCRIBE productos";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Campo</th><th>Tipo</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Mostrar algunos productos
        echo "<h4>📦 Productos de ejemplo:</h4>";
        $sql = "SELECT id, nombre, precio, codigo FROM productos LIMIT 10";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $productos = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Código</th><th>Nombre</th><th>Precio</th></tr>";
        foreach ($productos as $producto) {
            echo "<tr>";
            echo "<td>" . $producto['id'] . "</td>";
            echo "<td>" . ($producto['codigo'] ?? 'N/A') . "</td>";
            echo "<td>" . $producto['nombre'] . "</td>";
            echo "<td>$" . number_format($producto['precio']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Test de búsqueda
        echo "<h4>🔍 Test de Búsqueda:</h4>";
        $terminos_test = ['a', 'e', 'o', 'comida', 'bebida', 'pollo'];
        
        foreach ($terminos_test as $termino) {
            $sql = "SELECT COUNT(*) as total FROM productos WHERE nombre LIKE :termino OR codigo LIKE :termino";
            $stmt = $db->prepare($sql);
            $stmt->execute([':termino' => "%$termino%"]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            echo "<p><strong>Búsqueda '$termino':</strong> " . $result['total'] . " resultados</p>";
            
            if ($result['total'] > 0 && $result['total'] <= 5) {
                // Mostrar resultados si son pocos
                $sql = "SELECT id, nombre, precio FROM productos WHERE nombre LIKE :termino OR codigo LIKE :termino LIMIT 5";
                $stmt = $db->prepare($sql);
                $stmt->execute([':termino' => "%$termino%"]);
                $productos = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                echo "<ul>";
                foreach ($productos as $producto) {
                    echo "<li>" . $producto['nombre'] . " - $" . number_format($producto['precio']) . "</li>";
                }
                echo "</ul>";
            }
        }
        
    } else {
        echo "<p style='color: red;'>❌ No hay productos en la base de datos</p>";
        echo "<p>Necesitas agregar productos para que funcione la búsqueda.</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    echo "<p><strong>Detalles del error:</strong></p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

?>

<h3>🧪 Test AJAX Simulado</h3>
<p>Simulando la misma consulta que hace el AJAX:</p>

<?php
if (isset($db)) {
    try {
        $termino = "a"; // Término de prueba
        
        echo "<h4>Consulta SQL:</h4>";
        echo "<code>SELECT id, nombre, precio FROM productos WHERE nombre LIKE '%$termino%' OR codigo LIKE '%$termino%' ORDER BY nombre LIMIT 10</code>";
        
        $sql = "SELECT id, nombre, precio FROM productos WHERE nombre LIKE :termino OR codigo LIKE :termino ORDER BY nombre LIMIT 10";
        $stmt = $db->prepare($sql);
        $stmt->execute([':termino' => "%$termino%"]);
        $productos = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h4>Resultado (como lo vería el AJAX):</h4>";
        echo "<div style='border: 1px solid #ccc; padding: 10px; background: #f8f9fa;'>";
        
        if (count($productos) > 0) {
            foreach ($productos as $producto) {
                echo '<div class="producto-sugerencia" onclick="alert(\'ID: ' . $producto['id'] . ', Nombre: ' . addslashes($producto['nombre']) . ', Precio: ' . $producto['precio'] . '\')" style="padding: 10px; cursor: pointer; border-bottom: 1px solid #eee;">';
                echo '<strong>' . htmlspecialchars($producto['nombre']) . '</strong><br>';
                echo '<small>$' . number_format($producto['precio']) . '</small>';
                echo '</div>';
            }
        } else {
            echo '<div style="color: #666;">No se encontraron productos</div>';
        }
        
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error en simulación AJAX: " . $e->getMessage() . "</p>";
    }
}
?>

<br><br>
<a href="test_ajax_productos.php" style="background: #007bff; color: white; padding: 10px; text-decoration: none; border-radius: 5px;">🧪 Test AJAX</a>
<a href="../../index.php?action=facturacionRapida" style="background: #28a745; color: white; padding: 10px; text-decoration: none; border-radius: 5px;">🚀 Facturación Rápida</a>

</body>
</html>
