<?php
session_start();
require_once "../../models/conexion.php";
require_once "../../models/crudFacturaAja.php";

// Verificar si se recibió mesa por GET
$mesaId = $_GET['mesa'] ?? 1;

echo "<!DOCTYPE html>";
echo "<html><head><title>Test Mesa Correcta</title></head><body>";
echo "<h2>🧪 Test de Mesa Correcta</h2>";
echo "<p><strong>Mesa ID recibida:</strong> $mesaId</p>";

// Verificar productos para facturar en esta mesa
try {
    $productos = DatosFacturaAja::obtenerProductosParaFacturar($mesaId);
    $totalProductos = count($productos);
    
    echo "<h3>📊 Productos para Facturar:</h3>";
    echo "<p><strong>Total productos:</strong> $totalProductos</p>";
    
    if ($totalProductos > 0) {
        $totalReal = 0;
        foreach ($productos as $producto) {
            $totalReal += $producto['valor_productos'];
        }
        
        echo "<p><strong>Total a facturar:</strong> $" . number_format($totalReal) . "</p>";
        
        echo "<h3>🚀 Test de Facturación</h3>";
        echo "<button onclick='testFacturacion()' style='background: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;'>";
        echo "Facturar Mesa $mesaId";
        echo "</button>";
        
        echo "<div id='resultado' style='margin-top: 20px; padding: 15px; border-radius: 5px;'></div>";
        
    } else {
        echo "<p style='color: orange;'>⚠️ No hay productos para facturar en esta mesa</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}

?>

<script>
function testFacturacion() {
    const mesaId = <?=$mesaId?>;
    const totalReal = <?=$totalReal ?? 50000?>;
    
    const datos = {
        efectivo: totalReal,
        bancolombia: 0,
        nequi: 0,
        daviplata: 0,
        tarjeta: 0,
        pago: 1,
        pcedula: 'TEST_MESA_' + mesaId,
        totalDescuento: 0,
        total: totalReal,
        propina: 0,
        mesa: mesaId,
        tipoTarjeta: 'credito'
    };
    
    console.log('🔍 Datos enviados para mesa ' + mesaId + ':', datos);
    
    document.getElementById('resultado').innerHTML = '<div style="background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px;">⏳ Procesando facturación para mesa ' + mesaId + '...</div>';
    
    fetch('ajaxFactura.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams(datos)
    })
    .then(response => {
        console.log('Status:', response.status);
        return response.text();
    })
    .then(text => {
        console.log('Respuesta completa:', text);
        
        let html = '<h4>✅ Respuesta del Servidor:</h4>';
        html += '<div style="background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto;">';
        html += text;
        html += '</div>';
        
        // Verificar si la facturación fue exitosa
        if (text.includes('success') || text.includes('Pago realizado exitosamente')) {
            html += '<div style="background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin-top: 10px;">';
            html += '<strong>✅ Facturación exitosa para mesa ' + mesaId + '</strong>';
            html += '</div>';
        } else if (text.includes('error') || text.includes('Error')) {
            html += '<div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin-top: 10px;">';
            html += '<strong>❌ Error en facturación para mesa ' + mesaId + '</strong>';
            html += '</div>';
        }
        
        document.getElementById('resultado').innerHTML = html;
    })
    .catch(error => {
        console.error('Error:', error);
        document.getElementById('resultado').innerHTML = '<div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;">❌ Error de conexión: ' + error.message + '</div>';
    });
}
</script>

<br><br>
<a href="index.php?action=registroPmesa&ida=<?=$mesaId?>" style="background: #007bff; color: white; padding: 10px; text-decoration: none; border-radius: 5px;">🔙 Volver a Mesa <?=$mesaId?></a>

</body>
</html>
