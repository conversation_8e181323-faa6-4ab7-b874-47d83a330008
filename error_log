
[12-Jul-2025 12:51:30 America/Bogota] DEBUG Mesa 9: Pedido borrador = P000525
[12-Jul-2025 12:51:30 America/Bogota] DEBUG Mesa 9: Total pedidos = 0
[12-Jul-2025 12:51:54 America/Bogota] DEBUG Mesa 9: Pedido borrador = P000525
[12-Jul-2025 12:51:54 America/Bogota] DEBUG Mesa 9: Total pedidos = 0
[12-Jul-2025 12:51:54 America/Bogota] registroPmesaModel: Ejecutando consulta con ON DUPLICATE KEY UPDATE
[12-Jul-2025 12:51:56 America/Bogota] DEBUG Mesa 9: Pedido borrador = P000525
[12-Jul-2025 12:51:56 America/Bogota] DEBUG Mesa 9: Total pedidos = 0
[12-Jul-2025 12:52:03 America/Bogota] DEBUG Mesa 9: Pedido borrador = P000540
[12-Jul-2025 12:52:03 America/Bogota] DEBUG Mesa 9: Total pedidos = 0
[12-Jul-2025 13:12:48 America/Bogota] DEBUG Mesa 6: Pedido borrador = P000539
[12-Jul-2025 13:12:48 America/Bogota] DEBUG Mesa 6: Total pedidos = 0
[12-Jul-2025 13:14:52 America/Bogota] DEBUG Mesa 6: Pedido borrador = P000539
[12-Jul-2025 13:14:52 America/Bogota] DEBUG Mesa 6: Total pedidos = 0
[12-Jul-2025 13:17:22 America/Bogota] DEBUG Mesa 9: Pedido borrador = P000540
[12-Jul-2025 13:17:22 America/Bogota] DEBUG Mesa 9: Total pedidos = 0
[12-Jul-2025 13:18:00 America/Bogota] DEBUG Mesa 5: Pedido borrador = P000538
[12-Jul-2025 13:18:00 America/Bogota] DEBUG Mesa 5: Total pedidos = 0
[12-Jul-2025 13:21:05 America/Bogota] DEBUG Mesa 5: Pedido borrador = P000538
[12-Jul-2025 13:21:05 America/Bogota] DEBUG Mesa 5: Total pedidos = 0
[12-Jul-2025 13:22:25 America/Bogota] DEBUG Mesa 10: Pedido borrador = P000537
[12-Jul-2025 13:22:25 America/Bogota] DEBUG Mesa 10: Total pedidos = 0
[12-Jul-2025 13:23:53 America/Bogota] DEBUG Mesa 10: Pedido borrador = P000537
[12-Jul-2025 13:23:53 America/Bogota] DEBUG Mesa 10: Total pedidos = 0
[12-Jul-2025 13:26:41 America/Bogota] DEBUG Mesa 8: Pedido borrador = P000518
[12-Jul-2025 13:26:41 America/Bogota] DEBUG Mesa 8: Total pedidos = 0
[12-Jul-2025 13:27:26 America/Bogota] DEBUG Mesa 8: Pedido borrador = P000518
[12-Jul-2025 13:27:26 America/Bogota] DEBUG Mesa 8: Total pedidos = 0
[12-Jul-2025 13:27:35 America/Bogota] DEBUG Mesa 8: Pedido borrador = P000518
[12-Jul-2025 13:27:35 America/Bogota] DEBUG Mesa 8: Total pedidos = 0
[12-Jul-2025 13:27:35 America/Bogota] registroPmesaModel: Ejecutando consulta con ON DUPLICATE KEY UPDATE
[12-Jul-2025 13:27:36 America/Bogota] DEBUG Mesa 8: Pedido borrador = P000518
[12-Jul-2025 13:27:36 America/Bogota] DEBUG Mesa 8: Total pedidos = 0
[12-Jul-2025 13:27:48 America/Bogota] DEBUG Mesa 8: Pedido borrador = P000518
[12-Jul-2025 13:27:48 America/Bogota] DEBUG Mesa 8: Total pedidos = 0
[12-Jul-2025 13:27:48 America/Bogota] registroPmesaModel: Ejecutando consulta con ON DUPLICATE KEY UPDATE
[12-Jul-2025 13:27:49 America/Bogota] DEBUG Mesa 8: Pedido borrador = P000518
[12-Jul-2025 13:27:49 America/Bogota] DEBUG Mesa 8: Total pedidos = 0
[12-Jul-2025 13:27:54 America/Bogota] DEBUG Mesa 8: Pedido borrador = P000544
[12-Jul-2025 13:27:54 America/Bogota] DEBUG Mesa 8: Total pedidos = 0
[12-Jul-2025 13:28:28 America/Bogota] DEBUG Mesa 10: Pedido borrador = P000537
[12-Jul-2025 13:28:28 America/Bogota] DEBUG Mesa 10: Total pedidos = 0
[12-Jul-2025 13:29:08 America/Bogota] DEBUG Mesa 10: Pedido borrador = P000537
[12-Jul-2025 13:29:08 America/Bogota] DEBUG Mesa 10: Total pedidos = 0
[12-Jul-2025 13:30:08 America/Bogota] DEBUG Mesa 9: Pedido borrador = P000540
[12-Jul-2025 13:30:08 America/Bogota] DEBUG Mesa 9: Total pedidos = 0
[12-Jul-2025 13:33:47 America/Bogota] DEBUG Mesa 10: Pedido borrador = P000537
[12-Jul-2025 13:33:47 America/Bogota] DEBUG Mesa 10: Total pedidos = 0
[12-Jul-2025 13:35:40 America/Bogota] DEBUG Mesa 10: Pedido borrador = P000537
[12-Jul-2025 13:35:40 America/Bogota] DEBUG Mesa 10: Total pedidos = 0
[12-Jul-2025 13:35:53 America/Bogota] DEBUG Mesa 10: Pedido borrador = P000537
[12-Jul-2025 13:35:53 America/Bogota] DEBUG Mesa 10: Total pedidos = 0
[12-Jul-2025 13:38:12 America/Bogota] DEBUG Mesa 7: Pedido borrador = P000526
[12-Jul-2025 13:38:12 America/Bogota] DEBUG Mesa 7: Total pedidos = 0
[12-Jul-2025 13:39:54 America/Bogota] DEBUG Mesa 10: Pedido borrador = P000537
[12-Jul-2025 13:39:54 America/Bogota] DEBUG Mesa 10: Total pedidos = 0
[12-Jul-2025 13:41:05 America/Bogota] DEBUG Mesa 10: Pedido borrador = P000537
[12-Jul-2025 13:41:05 America/Bogota] DEBUG Mesa 10: Total pedidos = 0
[12-Jul-2025 13:42:38 America/Bogota] DEBUG Mesa 7: Pedido borrador = P000526
[12-Jul-2025 13:42:38 America/Bogota] DEBUG Mesa 7: Total pedidos = 0
[12-Jul-2025 14:06:13 America/Bogota] DEBUG Mesa 7: Pedido borrador = P000526
[12-Jul-2025 14:06:13 America/Bogota] DEBUG Mesa 7: Total pedidos = 0
[12-Jul-2025 14:06:20 America/Bogota] DEBUG Mesa 7: Pedido borrador = P000526
[12-Jul-2025 14:06:20 America/Bogota] DEBUG Mesa 7: Total pedidos = 0
[12-Jul-2025 14:39:56 America/Bogota] DEBUG Mesa 9: Pedido borrador = P000540
[12-Jul-2025 14:39:56 America/Bogota] DEBUG Mesa 9: Total pedidos = 0
[12-Jul-2025 14:40:20 America/Bogota] DEBUG Mesa 9: Pedido borrador = P000540
[12-Jul-2025 14:40:20 America/Bogota] DEBUG Mesa 9: Total pedidos = 0
[12-Jul-2025 14:40:20 America/Bogota] registroPmesaModel: Ejecutando consulta con ON DUPLICATE KEY UPDATE
[12-Jul-2025 14:40:20 America/Bogota] DEBUG Mesa 9: Pedido borrador = P000540
[12-Jul-2025 14:40:20 America/Bogota] DEBUG Mesa 9: Total pedidos = 0
[12-Jul-2025 14:40:27 America/Bogota] DEBUG Mesa 9: Pedido borrador = P000551
[12-Jul-2025 14:40:27 America/Bogota] DEBUG Mesa 9: Total pedidos = 0
[12-Jul-2025 14:48:03 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000554
[12-Jul-2025 14:48:03 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 14:48:10 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000554
[12-Jul-2025 14:48:10 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 14:48:11 America/Bogota] registroPmesaModel: Ejecutando consulta con ON DUPLICATE KEY UPDATE
[12-Jul-2025 14:48:11 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000554
[12-Jul-2025 14:48:11 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 14:48:28 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000554
[12-Jul-2025 14:48:28 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 14:48:28 America/Bogota] registroPmesaModel: Ejecutando consulta con ON DUPLICATE KEY UPDATE
[12-Jul-2025 14:48:28 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000554
[12-Jul-2025 14:48:28 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 14:48:39 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000554
[12-Jul-2025 14:48:39 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 14:48:39 America/Bogota] registroPmesaModel: Ejecutando consulta con ON DUPLICATE KEY UPDATE
[12-Jul-2025 14:48:39 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000554
[12-Jul-2025 14:48:39 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 14:50:21 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000554
[12-Jul-2025 14:50:21 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 14:50:21 America/Bogota] registroPmesaModel: Ejecutando consulta con ON DUPLICATE KEY UPDATE
[12-Jul-2025 14:50:21 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000554
[12-Jul-2025 14:50:21 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 14:52:49 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000555
[12-Jul-2025 14:52:49 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 14:54:11 America/Bogota] DEBUG Mesa 10: Pedido borrador = P000537
[12-Jul-2025 14:54:11 America/Bogota] DEBUG Mesa 10: Total pedidos = 0
[12-Jul-2025 14:54:22 America/Bogota] DEBUG Mesa 10: Pedido borrador = P000537
[12-Jul-2025 14:54:22 America/Bogota] DEBUG Mesa 10: Total pedidos = 0
[12-Jul-2025 14:54:22 America/Bogota] registroPmesaModel: Ejecutando consulta con ON DUPLICATE KEY UPDATE
[12-Jul-2025 14:54:22 America/Bogota] DEBUG Mesa 10: Pedido borrador = P000537
[12-Jul-2025 14:54:22 America/Bogota] DEBUG Mesa 10: Total pedidos = 0
[12-Jul-2025 14:54:27 America/Bogota] DEBUG Mesa 10: Pedido borrador = P000557
[12-Jul-2025 14:54:27 America/Bogota] DEBUG Mesa 10: Total pedidos = 0
[12-Jul-2025 14:54:44 America/Bogota] DEBUG Mesa 8: Pedido borrador = P000544
[12-Jul-2025 14:54:44 America/Bogota] DEBUG Mesa 8: Total pedidos = 0
[12-Jul-2025 14:54:53 America/Bogota] DEBUG Mesa 8: Pedido borrador = P000544
[12-Jul-2025 14:54:53 America/Bogota] DEBUG Mesa 8: Total pedidos = 0
[12-Jul-2025 14:54:53 America/Bogota] registroPmesaModel: Ejecutando consulta con ON DUPLICATE KEY UPDATE
[12-Jul-2025 14:54:53 America/Bogota] DEBUG Mesa 8: Pedido borrador = P000544
[12-Jul-2025 14:54:53 America/Bogota] DEBUG Mesa 8: Total pedidos = 0
[12-Jul-2025 14:54:56 America/Bogota] DEBUG Mesa 8: Pedido borrador = P000558
[12-Jul-2025 14:54:56 America/Bogota] DEBUG Mesa 8: Total pedidos = 0
[12-Jul-2025 14:57:26 America/Bogota] DEBUG Mesa 9: Pedido borrador = P000551
[12-Jul-2025 14:57:26 America/Bogota] DEBUG Mesa 9: Total pedidos = 0
[12-Jul-2025 14:57:53 America/Bogota] DEBUG Mesa 9: Pedido borrador = P000551
[12-Jul-2025 14:57:53 America/Bogota] DEBUG Mesa 9: Total pedidos = 0
[12-Jul-2025 14:57:53 America/Bogota] registroPmesaModel: Ejecutando consulta con ON DUPLICATE KEY UPDATE
[12-Jul-2025 14:57:53 America/Bogota] DEBUG Mesa 9: Pedido borrador = P000551
[12-Jul-2025 14:57:53 America/Bogota] DEBUG Mesa 9: Total pedidos = 0
[12-Jul-2025 14:58:05 America/Bogota] DEBUG Mesa 9: Pedido borrador = P000551
[12-Jul-2025 14:58:05 America/Bogota] DEBUG Mesa 9: Total pedidos = 0
[12-Jul-2025 14:58:05 America/Bogota] registroPmesaModel: Ejecutando consulta con ON DUPLICATE KEY UPDATE
[12-Jul-2025 14:58:05 America/Bogota] DEBUG Mesa 9: Pedido borrador = P000551
[12-Jul-2025 14:58:05 America/Bogota] DEBUG Mesa 9: Total pedidos = 0
[12-Jul-2025 14:58:12 America/Bogota] DEBUG Mesa 9: Pedido borrador = P000559
[12-Jul-2025 14:58:12 America/Bogota] DEBUG Mesa 9: Total pedidos = 0
[12-Jul-2025 15:05:15 America/Bogota] DEBUG Mesa 10: Pedido borrador = P000557
[12-Jul-2025 15:05:15 America/Bogota] DEBUG Mesa 10: Total pedidos = 0
[12-Jul-2025 15:05:40 America/Bogota] DEBUG Mesa 10: Pedido borrador = P000557
[12-Jul-2025 15:05:40 America/Bogota] DEBUG Mesa 10: Total pedidos = 0
[12-Jul-2025 15:06:04 America/Bogota] DEBUG Mesa 10: Pedido borrador = P000557
[12-Jul-2025 15:06:04 America/Bogota] DEBUG Mesa 10: Total pedidos = 0
[12-Jul-2025 15:08:45 America/Bogota] DEBUG Mesa 8: Pedido borrador = P000558
[12-Jul-2025 15:08:45 America/Bogota] DEBUG Mesa 8: Total pedidos = 0
[12-Jul-2025 15:08:58 America/Bogota] DEBUG Mesa 8: Pedido borrador = P000558
[12-Jul-2025 15:08:58 America/Bogota] DEBUG Mesa 8: Total pedidos = 0
[12-Jul-2025 15:09:38 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000555
[12-Jul-2025 15:09:38 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 15:09:53 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000555
[12-Jul-2025 15:09:53 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 15:09:53 America/Bogota] registroPmesaModel: Ejecutando consulta con ON DUPLICATE KEY UPDATE
[12-Jul-2025 15:09:53 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000555
[12-Jul-2025 15:09:53 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 15:10:01 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000555
[12-Jul-2025 15:10:01 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 15:10:01 America/Bogota] registroPmesaModel: Ejecutando consulta con ON DUPLICATE KEY UPDATE
[12-Jul-2025 15:10:01 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000555
[12-Jul-2025 15:10:01 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 15:10:05 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000562
[12-Jul-2025 15:10:05 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 15:10:47 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000562
[12-Jul-2025 15:10:47 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 15:11:36 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000562
[12-Jul-2025 15:11:36 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 15:17:47 America/Bogota] DEBUG Mesa 8: Pedido borrador = P000558
[12-Jul-2025 15:17:47 America/Bogota] DEBUG Mesa 8: Total pedidos = 0
[12-Jul-2025 15:31:03 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000566
[12-Jul-2025 15:31:03 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 15:31:14 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000566
[12-Jul-2025 15:31:14 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 15:31:14 America/Bogota] registroPmesaModel: Ejecutando consulta con ON DUPLICATE KEY UPDATE
[12-Jul-2025 15:31:14 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000566
[12-Jul-2025 15:31:14 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 15:31:20 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000566
[12-Jul-2025 15:31:20 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 15:31:20 America/Bogota] registroPmesaModel: Ejecutando consulta con ON DUPLICATE KEY UPDATE
[12-Jul-2025 15:31:21 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000566
[12-Jul-2025 15:31:21 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 15:31:29 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000566
[12-Jul-2025 15:31:29 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 15:31:29 America/Bogota] registroPmesaModel: Ejecutando consulta con ON DUPLICATE KEY UPDATE
[12-Jul-2025 15:31:29 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000566
[12-Jul-2025 15:31:29 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 15:31:35 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000567
[12-Jul-2025 15:31:35 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 15:40:17 America/Bogota] DEBUG Mesa 5: Pedido borrador = P000538
[12-Jul-2025 15:40:17 America/Bogota] DEBUG Mesa 5: Total pedidos = 0
[12-Jul-2025 15:41:08 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000567
[12-Jul-2025 15:41:08 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 15:41:21 America/Bogota] DEBUG Mesa 5: Pedido borrador = P000538
[12-Jul-2025 15:41:21 America/Bogota] DEBUG Mesa 5: Total pedidos = 0
[12-Jul-2025 15:41:31 America/Bogota] DEBUG Mesa 9: Pedido borrador = P000559
[12-Jul-2025 15:41:31 America/Bogota] DEBUG Mesa 9: Total pedidos = 0
[12-Jul-2025 15:48:45 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000567
[12-Jul-2025 15:48:45 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 15:50:08 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000567
[12-Jul-2025 15:50:08 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 15:50:23 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000567
[12-Jul-2025 15:50:23 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 15:50:38 America/Bogota] DEBUG Mesa 2: Pedido borrador = P000568
[12-Jul-2025 15:50:38 America/Bogota] DEBUG Mesa 2: Total pedidos = 0
[12-Jul-2025 15:50:49 America/Bogota] DEBUG Mesa 2: Pedido borrador = P000568
[12-Jul-2025 15:50:49 America/Bogota] DEBUG Mesa 2: Total pedidos = 0
[12-Jul-2025 15:50:49 America/Bogota] registroPmesaModel: Ejecutando consulta con ON DUPLICATE KEY UPDATE
[12-Jul-2025 15:50:49 America/Bogota] DEBUG Mesa 2: Pedido borrador = P000568
[12-Jul-2025 15:50:49 America/Bogota] DEBUG Mesa 2: Total pedidos = 0
[12-Jul-2025 15:50:58 America/Bogota] DEBUG Mesa 2: Pedido borrador = P000568
[12-Jul-2025 15:50:58 America/Bogota] DEBUG Mesa 2: Total pedidos = 0
[12-Jul-2025 15:50:58 America/Bogota] registroPmesaModel: Ejecutando consulta con ON DUPLICATE KEY UPDATE
[12-Jul-2025 15:50:58 America/Bogota] DEBUG Mesa 2: Pedido borrador = P000568
[12-Jul-2025 15:50:58 America/Bogota] DEBUG Mesa 2: Total pedidos = 0
[12-Jul-2025 15:51:03 America/Bogota] DEBUG Mesa 2: Pedido borrador = P000568
[12-Jul-2025 15:51:03 America/Bogota] DEBUG Mesa 2: Total pedidos = 0
[12-Jul-2025 15:51:03 America/Bogota] registroPmesaModel: Ejecutando consulta con ON DUPLICATE KEY UPDATE
[12-Jul-2025 15:51:03 America/Bogota] DEBUG Mesa 2: Pedido borrador = P000568
[12-Jul-2025 15:51:03 America/Bogota] DEBUG Mesa 2: Total pedidos = 0
[12-Jul-2025 15:51:07 America/Bogota] DEBUG Mesa 2: Pedido borrador = P000569
[12-Jul-2025 15:51:07 America/Bogota] DEBUG Mesa 2: Total pedidos = 0
[12-Jul-2025 15:51:25 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000567
[12-Jul-2025 15:51:25 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 15:51:34 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000567
[12-Jul-2025 15:51:34 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 15:51:34 America/Bogota] registroPmesaModel: Ejecutando consulta con ON DUPLICATE KEY UPDATE
[12-Jul-2025 15:51:34 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000567
[12-Jul-2025 15:51:34 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 15:52:29 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000567
[12-Jul-2025 15:52:29 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 15:52:30 America/Bogota] registroPmesaModel: Ejecutando consulta con ON DUPLICATE KEY UPDATE
[12-Jul-2025 15:52:30 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000567
[12-Jul-2025 15:52:30 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 15:53:45 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000570
[12-Jul-2025 15:53:45 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 15:54:10 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000570
[12-Jul-2025 15:54:10 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 15:54:19 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000570
[12-Jul-2025 15:54:19 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 15:54:19 America/Bogota] registroPmesaModel: Ejecutando consulta con ON DUPLICATE KEY UPDATE
[12-Jul-2025 15:54:19 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000570
[12-Jul-2025 15:54:19 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 15:54:25 America/Bogota] DEBUG Mesa 1: Pedido borrador = P000571
[12-Jul-2025 15:54:25 America/Bogota] DEBUG Mesa 1: Total pedidos = 0
[12-Jul-2025 15:56:53 America/Bogota] DEBUG Mesa 2: Pedido borrador = P000569
[12-Jul-2025 15:56:53 America/Bogota] DEBUG Mesa 2: Total pedidos = 0
[12-Jul-2025 15:57:10 America/Bogota] DEBUG Mesa 2: Pedido borrador = P000569
[12-Jul-2025 15:57:10 America/Bogota] DEBUG Mesa 2: Total pedidos = 0
[12-Jul-2025 15:57:10 America/Bogota] registroPmesaModel: Ejecutando consulta con ON DUPLICATE KEY UPDATE
[12-Jul-2025 15:57:10 America/Bogota] DEBUG Mesa 2: Pedido borrador = P000569
[12-Jul-2025 15:57:10 America/Bogota] DEBUG Mesa 2: Total pedidos = 0
[12-Jul-2025 15:57:22 America/Bogota] DEBUG Mesa 2: Pedido borrador = P000573
[12-Jul-2025 15:57:22 America/Bogota] DEBUG Mesa 2: Total pedidos = 0
[12-Jul-2025 16:14:15 America/Bogota] DEBUG Mesa 5: Pedido borrador = P000538
[12-Jul-2025 16:14:15 America/Bogota] DEBUG Mesa 5: Total pedidos = 0
[12-Jul-2025 16:17:00 America/Bogota] DEBUG Mesa 9: Pedido borrador = P000559
[12-Jul-2025 16:17:00 America/Bogota] DEBUG Mesa 9: Total pedidos = 0
[12-Jul-2025 16:29:04 America/Bogota] DEBUG Mesa 5: Pedido borrador = P000538
[12-Jul-2025 16:29:04 America/Bogota] DEBUG Mesa 5: Total pedidos = 0
[12-Jul-2025 16:40:16 America/Bogota] DEBUG Mesa 9: Pedido borrador = P000559
[12-Jul-2025 16:40:16 America/Bogota] DEBUG Mesa 9: Total pedidos = 0
[12-Jul-2025 16:55:08 America/Bogota] DEBUG Mesa 2: Pedido borrador = P000573
[12-Jul-2025 16:55:08 America/Bogota] DEBUG Mesa 2: Total pedidos = 0
[12-Jul-2025 16:55:19 America/Bogota] DEBUG Mesa 2: Pedido borrador = P000573
[12-Jul-2025 16:55:19 America/Bogota] DEBUG Mesa 2: Total pedidos = 0
[12-Jul-2025 16:55:19 America/Bogota] registroPmesaModel: Ejecutando consulta con ON DUPLICATE KEY UPDATE
[12-Jul-2025 16:55:19 America/Bogota] DEBUG Mesa 2: Pedido borrador = P000573
[12-Jul-2025 16:55:19 America/Bogota] DEBUG Mesa 2: Total pedidos = 0
[12-Jul-2025 16:55:26 America/Bogota] DEBUG Mesa 2: Pedido borrador = P000573
[12-Jul-2025 16:55:26 America/Bogota] DEBUG Mesa 2: Total pedidos = 0
[12-Jul-2025 16:55:26 America/Bogota] registroPmesaModel: Ejecutando consulta con ON DUPLICATE KEY UPDATE
[12-Jul-2025 16:55:26 America/Bogota] DEBUG Mesa 2: Pedido borrador = P000573
[12-Jul-2025 16:55:26 America/Bogota] DEBUG Mesa 2: Total pedidos = 0
[12-Jul-2025 16:55:39 America/Bogota] DEBUG Mesa 2: Pedido borrador = P000573
[12-Jul-2025 16:55:39 America/Bogota] DEBUG Mesa 2: Total pedidos = 0
[12-Jul-2025 16:55:39 America/Bogota] registroPmesaModel: Ejecutando consulta con ON DUPLICATE KEY UPDATE
[12-Jul-2025 16:55:40 America/Bogota] DEBUG Mesa 2: Pedido borrador = P000573
[12-Jul-2025 16:55:40 America/Bogota] DEBUG Mesa 2: Total pedidos = 0
[12-Jul-2025 16:55:47 America/Bogota] DEBUG Mesa 2: Pedido borrador = P000581
[12-Jul-2025 16:55:47 America/Bogota] DEBUG Mesa 2: Total pedidos = 0
[12-Jul-2025 17:03:46 America/Bogota] DEBUG Mesa 9: Pedido borrador = P000559
[12-Jul-2025 17:03:46 America/Bogota] DEBUG Mesa 9: Total pedidos = 0
[12-Jul-2025 17:22:33 America/Bogota] DEBUG Mesa 2: Pedido borrador = P000581
[12-Jul-2025 17:22:33 America/Bogota] DEBUG Mesa 2: Total pedidos = 0
[12-Jul-2025 17:54:41 America/Bogota] DEBUG Mesa 2: Pedido borrador = P000581
[12-Jul-2025 17:54:41 America/Bogota] DEBUG Mesa 2: Total pedidos = 0
[12-Jul-2025 17:55:05 America/Bogota] DEBUG Mesa 2: Pedido borrador = P000581
[12-Jul-2025 17:55:05 America/Bogota] DEBUG Mesa 2: Total pedidos = 0
[12-Jul-2025 17:55:05 America/Bogota] registroPmesaModel: Ejecutando consulta con ON DUPLICATE KEY UPDATE
[12-Jul-2025 17:55:06 America/Bogota] DEBUG Mesa 2: Pedido borrador = P000581
[12-Jul-2025 17:55:06 America/Bogota] DEBUG Mesa 2: Total pedidos = 0
[12-Jul-2025 17:55:11 America/Bogota] DEBUG Mesa 2: Pedido borrador = P000581
[12-Jul-2025 17:55:11 America/Bogota] DEBUG Mesa 2: Total pedidos = 0
[12-Jul-2025 17:55:11 America/Bogota] registroPmesaModel: Ejecutando consulta con ON DUPLICATE KEY UPDATE
[12-Jul-2025 17:55:11 America/Bogota] DEBUG Mesa 2: Pedido borrador = P000581
[12-Jul-2025 17:55:11 America/Bogota] DEBUG Mesa 2: Total pedidos = 0
[12-Jul-2025 17:55:17 America/Bogota] DEBUG Mesa 2: Pedido borrador = P000586
[12-Jul-2025 17:55:17 America/Bogota] DEBUG Mesa 2: Total pedidos = 0
[12-Jul-2025 17:56:33 America/Bogota] DEBUG Mesa 2: Pedido borrador = P000586
[12-Jul-2025 17:56:33 America/Bogota] DEBUG Mesa 2: Total pedidos = 0
[12-Jul-2025 22:39:20 America/Bogota] PHP Warning:  Invalid argument supplied for foreach() in /home/<USER>/public_html/macarena/controllers/controllerdevolucion.php on line 511
[12-Jul-2025 22:41:10 America/Bogota] PHP Warning:  Invalid argument supplied for foreach() in /home/<USER>/public_html/macarena/controllers/controllerdevolucion.php on line 511
[13-Jul-2025 08:41:07 UTC] PHP Parse error:  syntax error, unexpected '>' in /home/<USER>/public_html/macarena/models/cruddevolucion.php on line 73
[13-Jul-2025 03:49:28 America/Bogota] DEBUG Mesa 47: Pedido borrador = P000588
[13-Jul-2025 03:49:28 America/Bogota] DEBUG Mesa 47: Total pedidos = 0
[13-Jul-2025 03:49:50 America/Bogota] DEBUG Mesa 47: Pedido borrador = P000588
[13-Jul-2025 03:49:50 America/Bogota] DEBUG Mesa 47: Total pedidos = 0
[13-Jul-2025 03:49:50 America/Bogota] registroPmesaModel: Ejecutando consulta con ON DUPLICATE KEY UPDATE
[13-Jul-2025 03:49:50 America/Bogota] DEBUG Mesa 47: Pedido borrador = P000588
[13-Jul-2025 03:49:50 America/Bogota] DEBUG Mesa 47: Total pedidos = 0
[13-Jul-2025 03:49:56 America/Bogota] DEBUG Mesa 47: Pedido borrador = P000589
[13-Jul-2025 03:49:56 America/Bogota] DEBUG Mesa 47: Total pedidos = 0
[13-Jul-2025 03:54:03 America/Bogota] DEBUG Mesa 47: Pedido borrador = P000589
[13-Jul-2025 03:54:03 America/Bogota] DEBUG Mesa 47: Total pedidos = 0
[13-Jul-2025 04:02:51 America/Bogota] DEBUG Mesa 47: Pedido borrador = P000589
[13-Jul-2025 04:02:51 America/Bogota] DEBUG Mesa 47: Total pedidos = 0
[13-Jul-2025 04:02:57 America/Bogota] DEBUG Mesa 47: Pedido borrador = P000589
[13-Jul-2025 04:02:57 America/Bogota] DEBUG Mesa 47: Total pedidos = 0
[13-Jul-2025 04:07:30 America/Bogota] DEBUG Mesa 47: Pedido borrador = P000589
[13-Jul-2025 04:07:30 America/Bogota] DEBUG Mesa 47: Total pedidos = 0
