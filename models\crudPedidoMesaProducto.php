<?php

	#EXTENSIÓN DE CLASES: Los objetos pueden ser extendidos, y pueden heredar propiedades y métodos. Para definir una clase como extensión, debo definir una clase padre, y se utiliza dentro de una clase hija.

	require_once "conexion.php";

	class DatosPedidoMesaProducto extends Conexion
		{
			
			##PEDIDO MESA PRODUCTO  productos_id, mesas_id, pedidos_id, cantidad
			#----------------------------------------------
				#REGISTRO DE PEDIDO MESA PRODUCTO 
				#-------------------------------------
					public static function registroPmesaProductoModel($datosModel, $tabla){
								echo "<script>alert('Entro CRUD ".$datosModel["cantidad"]." es')</script>";

						// SOLUCIÓN PERMANENTE: Usar INSERT ... ON DUPLICATE KEY UPDATE
						$consulta="INSERT INTO $tabla (productos_id, mesas_id, pedidos_id, cantidad)
						          VALUES (:productos_id, :mesas_id, :pedidos_id, :cantidad)
						          ON DUPLICATE KEY UPDATE
						          cantidad = cantidad + VALUES(cantidad)";

						//echo "<script>alert('Entro CRUD ".$consulta." no')</script>";
						$stmt = Conexion::conectar()->prepare($consulta);
						$stmt->bindParam(":productos_id", $datosModel["productos_id"], PDO::PARAM_INT);
						$stmt->bindParam(":mesas_id", $datosModel["mesas_id"], PDO::PARAM_INT);
						$stmt->bindParam(":pedidos_id", $datosModel["pedidos_id"], PDO::PARAM_INT);
						$stmt->bindParam(":cantidad", $datosModel["cantidad"], PDO::PARAM_INT);

						echo "<script>alert('Guardo con ON DUPLICATE KEY UPDATE')</script>";

						if($stmt->execute())
							{	return "success";	}
						else{ return "error";	}
						$stmt->close();
					}

				#VISTA PEDIDO MESA PRODUCTO 
				#-------------------------------------
					public static function vistaPmesaProductoModel($tabla)
						{
							$consulta = "SELECT productos_id, mesas_id, pedidos_id, cantidad FROM $tabla";
							$stmt = Conexion::conectar()->prepare($consulta);	
							$stmt->execute();				
							return $stmt->fetchAll();
							$stmt->close();
						}

				#EDITAR PEDIDO MESA PRODUCTO 
				#-------------------------------------
					public static function editarPmesaProductoModel($datosModel, $tabla)
						{
							$stmt = Conexion::conectar()->prepare("SELECT productos_id, mesas_id, pedidos_id, cantidad FROM $tabla WHERE productos_id=:productos_id and mesas_id = :mesas_id and pedidos_id=:pedidos_id");
							$stmt->bindParam(":productos_id", $datosModel, PDO::PARAM_INT);	
							$stmt->bindParam(":mesas_id", $datosModel, PDO::PARAM_INT);	
							$stmt->bindParam(":pedidos_id", $datosModel, PDO::PARAM_INT);	
							$stmt->execute();
							return $stmt->fetch();
							$stmt->close();
						}

				#ACTUALIZAR PEDIDO MESA  PRODUCTO 
				#-------------------------------------
					public static function actualizarPmesaProductoModel($datosModel, $tabla)
						{
							echo "<script>alert('entro cruz Suministro')</script>";
							$consulta = "UPDATE $tabla SET productos_id = :productos_id, mesas_id= :mesas_id, pedidos_id =:pedidos_id, cantidad = :cantidad WHERE productos_id = :productos_id and mesas_id = :mesas_id and pedidos_id = :pedidos_id";
							echo "<script>alert('entro cruz Suministro ".$consulta."')</script>";

							$stmt = Conexion::conectar()->prepare($consulta);			
							$stmt->bindParam(":productos_id", $datosModel["productos_id"], PDO::PARAM_INT);
							$stmt->bindParam(":mesas_id", $datosModel["mesas_id"], PDO::PARAM_INT);
							$stmt->bindParam(":pedidos_id", $datosModel["pedidos_id"], PDO::PARAM_INT);
							$stmt->bindParam(":cantidad", $datosModel["cantidad"], PDO::PARAM_INT);				
							

							echo "<script>alert('centro proceso')</script>";
							if($stmt->execute())
								{echo "<script>alert('Guardo Actualizar Proveedor')</script>";return "success";	}
							else{echo "<script>alert('Error base de dato serve')</script>";	return "error";			}
							$stmt->close();
						}


				#BORRAR PEDIDO MESA PRODUCTO 
				#------------------------------------
					public static function borrarPmesaProductoModel($datosModel, $tabla)
					{
						$stmt = Conexion::conectar()->prepare("DELETE FROM $tabla WHERE mesas_id = :mesas_id");
						$stmt->bindParam(":mesas_id", $datosModel, PDO::PARAM_INT);
						if($stmt->execute())
							{	return "success";	}
						else{	return "error";		}
						$stmt->close();
					}
			#----------------------------------------------

		}