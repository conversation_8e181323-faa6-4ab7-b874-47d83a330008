<?php
require_once "conexion.php";
class DatosSuministroProducto extends Conexion
{
	#BUSCAR SUMINISTRO
	#------------------------------
	 public static function buscarSuministro($datosModel, $tabla)
		{
		 	$consulta = "SELECT id, codigo, nombre FROM suministros WHERE codigo=:codigo";
		 	//echo "<script>alert('Busqueda del Producto ".$consulta." ')</script>";
		 	$stmt = Conexion::conectar()->prepare($consulta);
			$stmt->bindParam(":codigo", $datosModel, PDO::PARAM_INT);
			echo "<br> ".$consulta."<br>";
			$stmt->execute();
			$r = $stmt->fetch();
			//echo "<script>alert('Producto id  --".$datosModel["id"]."')</script>";
				$c=$stmt->rowCount();
				//echo '<br> C2='.$c2.'<br>';
				if ($c>0)
					{	return $r;	}
				else
					{	return 0;	}
				$stmt->close();
		}
	#--------------------------------
	#SUMINISTROS por Codigo
	#-------------------------------------
	 public static function suministroCodigoModel($codigo)
		{
		 try {
			 $consulta = "SELECT * FROM suministros WHERE codigo = :codigo AND activo = 's'";
			 $stmt = Conexion::conectar()->prepare($consulta);
			 $stmt->bindParam(":codigo", $codigo, PDO::PARAM_STR);
			 $stmt->execute();
			 $resultado = $stmt->fetch();
			 $stmt = null;

			 if ($resultado) {
				 return $resultado;
			 } else {
				 // Si no encuentra por código exacto, buscar por código que contenga el valor
				 $consulta2 = "SELECT * FROM suministros WHERE codigo LIKE :codigo AND activo = 's' LIMIT 1";
				 $stmt2 = Conexion::conectar()->prepare($consulta2);
				 $codigoLike = "%".$codigo."%";
				 $stmt2->bindParam(":codigo", $codigoLike, PDO::PARAM_STR);
				 $stmt2->execute();
				 $resultado2 = $stmt2->fetch();
				 $stmt2 = null;
				 return $resultado2;
			 }
		 } catch (Exception $e) {
			 error_log("Error en suministroCodigoModel: " . $e->getMessage());
			 return false;
		 }
		}
	#-------------------------------------
	#REGISTRO DE SUMINISTROS PRODUCTOS
	#-------------------------------------
	 public static function registroSuministroPModel($datosModel, $tabla)
		{
		 try {
			 // Validar datos de entrada
			 if (!isset($datosModel["producto_id"]) || !isset($datosModel["codigo"]) || !isset($datosModel["cantidades"])) {
				 return "error_datos_incompletos";
			 }

			 // Buscar el suministro por código
			 $suministro = DatosSuministroProducto::suministroCodigoModel($datosModel["codigo"]);

			 if (!$suministro || !isset($suministro["id"])) {
				 return "error_suministro_no_encontrado";
			 }

			 // Verificar si ya existe la relación
			 $stmt_check = Conexion::conectar()->prepare("SELECT id FROM $tabla WHERE producto_id = :producto_id AND suministro_id = :suministro_id");
			 $stmt_check->bindParam(":producto_id", $datosModel["producto_id"], PDO::PARAM_INT);
			 $stmt_check->bindParam(":suministro_id", $suministro["id"], PDO::PARAM_INT);
			 $stmt_check->execute();

			 if ($stmt_check->fetch()) {
				 // Si ya existe, actualizar la cantidad
				 $consulta = "UPDATE $tabla SET cantidades = :cantidades WHERE producto_id = :producto_id AND suministro_id = :suministro_id";
				 $stmt = Conexion::conectar()->prepare($consulta);
				 $stmt->bindParam(":cantidades", $datosModel["cantidades"], PDO::PARAM_STR);
				 $stmt->bindParam(":producto_id", $datosModel["producto_id"], PDO::PARAM_INT);
				 $stmt->bindParam(":suministro_id", $suministro["id"], PDO::PARAM_INT);
			 } else {
				 // Si no existe, insertar nuevo registro
				 $consulta = "INSERT INTO $tabla (producto_id, suministro_id, cantidades) VALUES (:producto_id, :suministro_id, :cantidades)";
				 $stmt = Conexion::conectar()->prepare($consulta);
				 $stmt->bindParam(":producto_id", $datosModel["producto_id"], PDO::PARAM_INT);
				 $stmt->bindParam(":suministro_id", $suministro["id"], PDO::PARAM_INT);
				 $stmt->bindParam(":cantidades", $datosModel["cantidades"], PDO::PARAM_STR);
			 }

			 if ($stmt->execute()) {
				 $stmt = null;
				 return "success";
			 } else {
				 $stmt = null;
				 return "error_ejecucion";
			 }

		 } catch (Exception $e) {
			 error_log("Error en registroSuministroPModel: " . $e->getMessage());
			 return "error_excepcion";
		 }
		}
	#-------------------------------------
	#VISTA SUMINISTROS PRODUCTOS
	#-------------------------------------
		public static function vistaSuministroPModel($tabla)
			{
				$consulta = "SELECT id,codigo, nombre FROM $tabla";
				//$stmt = Conexion::conectar()->prepare("SELECT producto_id, suministro_id, cantidades FROM $tabla");

				$stmt = Conexion::conectar()->prepare($consulta);
				$stmt->execute();
				return $stmt->fetchAll();
				$stmt->close();
			}
	#----------------------------------
	#DEtalle
	#-------------------------------------
	 public static function detalleSuministroPModel($datosModel)
		{	//echo "<script>alert('Entro CRUD ".$datosModel." pro')</script>";
			$consulta1 = "SELECT p.nombre as producto, p.id as idp, s.nombre as suministro, s.id as ids, sp.cantidades as cantidad, s.codigo as scodigo  FROM suministros_productos sp, productos p,suministros s WHERE p.id=sp.producto_id and s.id=sp.suministro_id and p.id=:producto_id";
			//echo "<script>alert('Entro CRUD ".$consulta2." Cantidad')</script>";
			$stmt = Conexion::conectar()->prepare($consulta1);
			$stmt->bindParam(":producto_id", $datosModel, PDO::PARAM_INT);
			$stmt->execute();
			return $stmt->fetchAll();
			$stmt->close();
		}
	#--------------------------------------
	#EDITAR SUMINISTROS PRODUCTOS
	#-------------------------------------
		public static function editarSuministroPModel($datosModel, $tabla)
			{	echo "<script>alert('Entro CRUD ".$datosModel["producto_id"]." Suministro p')</script>";

				$consulta1 = "SELECT producto_id, suministro_id, cantidades   FROM $tabla WHERE producto_id=:producto_id and suministro_id=:suministro_id";
				//$consulta = "SELECT producto_id, suministro_id, cantidades FROM $tabla WHERE producto_id = "$datosModel["producto_id"] AND suministro_id = $datosModel["suministro_id"];

				echo "<script>alert('Entro CRUD ".$consulta1." Cantidad')</script>";

				$stmt = Conexion::conectar()->prepare($consulta1);
				$stmt->bindParam(":producto_id", $datosModel["producto_id"], PDO::PARAM_INT);
				$stmt->bindParam(":suministro_id", $datosModel["suministro_id"], PDO::PARAM_INT);
				$stmt->execute();
				return $stmt->fetch();
				$stmt->close();
			}
	#-----------------------------------
	#ACTUALIZAR SUMINISTROS PRODUCTOS
	#-------------------------------------
		public static function actualizarSuministroPModel($datosModel, $tabla)
			{
				echo "<script>alert('entro cruz Suministro')</script>";
				$consulta ="UPDATE $tabla SET cantidades= :cantidades WHERE producto_id = :producto_id and suministro_id = :suministro_id" ;
				$stmt = Conexion::conectar()->prepare($consulta);
				echo "<script>alert('Entro CRUD ".$consulta." Cantidad')</script>";

				$stmt->bindParam(":producto_id", $datosModel["producto_id"], PDO::PARAM_INT);
				$stmt->bindParam(":suministro_id", $datosModel["suministro_id"], PDO::PARAM_INT);
				$stmt->bindParam(":cantidades", $datosModel["cantidades"], PDO::PARAM_INT);

				echo "<script>alert('centro proceso')</script>";
				if($stmt->execute())
					{echo "<script>alert('Guardo Actualizar Producto')</script>";return "success";	}
				else{echo "<script>alert('Error base de dato serve')</script>";	return "error";			}
				$stmt->close();
			}
	#-----------------------------------
	#BORRAR SUMINISTROS PRODUCTOS
	#------------------------------------
	 public static function borrarSuministroPModel($datosModel, $tabla)
		{
			$stmt = Conexion::conectar()->prepare("DELETE FROM $tabla WHERE producto_id = :producto_id AND suministro_id = :suministro_id");
			$stmt->bindParam(":producto_id", $datosModel["producto_id"], PDO::PARAM_INT);
			$stmt->bindParam(":suministro_id", $datosModel["suministro_id"], PDO::PARAM_INT);
			if($stmt->execute())
				{	return "success";	}
			else{	return "error";		}
			$stmt->close();
		}
	#------------------------------------
}