-- Script de verificación del sistema para ewogjwfm_macarena
-- Ejecutar para diagnosticar problemas

USE ewogjwfm_macarena;

-- 1. Verificar estructura de tablas principales
SELECT 'VERIFICANDO ESTRUCTURA DE TABLAS...' as mensaje;

SELECT 'Tabla: pedidos' as tabla;
DESCRIBE pedidos;

SELECT 'Tabla: productos' as tabla;
DESCRIBE productos;

SELECT 'Tabla: producto_vendido_mesa' as tabla;
DESCRIBE producto_vendido_mesa;

SELECT 'Tabla: mesas' as tabla;
DESCRIBE mesas;

-- 2. Verificar si existen las nuevas tablas
SELECT 'VERIFICANDO NUEVAS TABLAS...' as mensaje;

SELECT 
    table_name,
    CASE 
        WHEN table_name IS NOT NULL THEN 'EXISTE'
        ELSE 'NO EXISTE'
    END as estado
FROM information_schema.tables 
WHERE table_schema = 'ewogjwfm_macarena' 
AND table_name IN ('pedidos_historial', 'pedidos_reimpresiones', 'impresoras');

-- 3. Verificar columnas de la tabla pedidos
SELECT 'VERIFICANDO COLUMNAS DE PEDIDOS...' as mensaje;

SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'ewogjwfm_macarena' 
AND table_name = 'pedidos'
ORDER BY ordinal_position;

-- 4. Verificar datos de ejemplo
SELECT 'VERIFICANDO DATOS DE EJEMPLO...' as mensaje;

SELECT 'Conteo de pedidos:' as descripcion, COUNT(*) as cantidad FROM pedidos;
SELECT 'Conteo de productos:' as descripcion, COUNT(*) as cantidad FROM productos;
SELECT 'Conteo de mesas:' as descripcion, COUNT(*) as cantidad FROM mesas;

-- 5. Verificar configuración de impresoras
SELECT 'VERIFICANDO IMPRESORAS...' as mensaje;

SELECT * FROM impresoras;

-- 6. Verificar relaciones
SELECT 'VERIFICANDO RELACIONES...' as mensaje;

SELECT 
    'Pedidos con mesa_id NULL' as problema,
    COUNT(*) as cantidad
FROM pedidos 
WHERE mesa_id IS NULL;

SELECT 
    'Productos sin categoría' as problema,
    COUNT(*) as cantidad
FROM productos 
WHERE categoria IS NULL OR categoria = '';

-- 7. Verificar permisos de usuario
SELECT 'VERIFICANDO TIPOS DE USUARIO...' as mensaje;

SELECT 
    tipo_usuario,
    COUNT(*) as cantidad
FROM personas 
GROUP BY tipo_usuario;

-- 8. Verificar productos por categoría
SELECT 'VERIFICANDO PRODUCTOS POR CATEGORÍA...' as mensaje;

SELECT 
    categoria,
    COUNT(*) as cantidad
FROM productos 
GROUP BY categoria;

-- 9. Verificar estado actual del sistema
SELECT 'ESTADO ACTUAL DEL SISTEMA...' as mensaje;

SELECT 
    'Pedidos por estado' as descripcion,
    estado,
    COUNT(*) as cantidad
FROM pedidos 
GROUP BY estado;

-- 10. Verificar errores comunes
SELECT 'VERIFICANDO ERRORES COMUNES...' as mensaje;

-- Verificar si hay productos sin ID en producto_vendido_mesa
SELECT 
    'Productos vendidos sin pedidos_id' as problema,
    COUNT(*) as cantidad
FROM producto_vendido_mesa 
WHERE pedidos_id IS NULL;

-- Verificar si hay pedidos sin productos
SELECT 
    'Pedidos sin productos' as problema,
    COUNT(*) as cantidad
FROM pedidos p
LEFT JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
WHERE pvm.pedidos_id IS NULL;

SELECT 'VERIFICACIÓN COMPLETADA' as mensaje;
