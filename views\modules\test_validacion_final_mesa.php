<!DOCTYPE html>
<html>
<head>
    <title>🎯 Test Final Validación Mesa</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1000px; margin: 0 auto; }
        .card { background: #f8f9fa; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #007bff; }
        .success { border-left-color: #28a745; background: #d4edda; }
        .warning { border-left-color: #ffc107; background: #fff3cd; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .btn { padding: 15px 30px; margin: 10px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold; font-size: 16px; cursor: pointer; border: none; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        h1 { color: #007bff; text-align: center; }
        h2 { color: #495057; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .debug-info { background: #e9ecef; padding: 10px; border-radius: 3px; font-family: monospace; margin: 10px 0; }
    </style>
</head>
<body>

<div class="container">
    <h1>🎯 Test Final - Validación de Pagos Mesa</h1>
    <p style="text-align: center; font-size: 18px; color: #6c757d;">
        Verificación final de la validación de pagos en registroPmesa.php
    </p>

    <div class="card success">
        <h2>✅ Corrección Final Implementada</h2>
        <p>He implementado una solución simplificada y robusta:</p>
        
        <ul>
            <li>✅ <strong>Función getFieldValue():</strong> Obtiene valores de campos de forma segura</li>
            <li>✅ <strong>Limpieza de valores:</strong> Remueve todo excepto números</li>
            <li>✅ <strong>Manejo de errores:</strong> Devuelve 0 si el campo no existe o está vacío</li>
            <li>✅ <strong>Debug mejorado:</strong> Muestra todos los valores en la consola</li>
            <li>✅ <strong>Validación robusta:</strong> Compara suma vs total correctamente</li>
        </ul>
    </div>

    <div class="card">
        <h2>🔧 Nueva Lógica Implementada</h2>
        <p>La nueva función <code>getFieldValue()</code> funciona así:</p>
        
        <div class="debug-info">
function getFieldValue(fieldId) {
    let field = document.getElementById(fieldId);
    if (!field || !field.value) return 0;
    // Limpiar valor: quitar todo excepto números
    let cleanValue = field.value.toString().replace(/[^0-9]/g, '');
    return parseInt(cleanValue) || 0;
}

// Uso:
let efectivo = getFieldValue('efectivo');     // $30,000 → 30000
let tarjeta = getFieldValue('tarjeta');       // $15,000 → 15000
let total = getFieldValue('total');           // $50,000 → 50000
        </div>
        
        <p><strong>Esta función maneja todos los casos:</strong> campos vacíos, valores formateados, campos inexistentes, etc.</p>
    </div>

    <div class="card warning">
        <h2>🧪 Instrucciones de Prueba</h2>
        <p>Para probar la validación corregida:</p>
        
        <ol>
            <li><strong>Ve a Mesa 5:</strong> Haz clic en el botón abajo</li>
            <li><strong>Agrega productos:</strong> Si no hay productos en la mesa</li>
            <li><strong>Abre la consola:</strong> Presiona F12 → pestaña "Console"</li>
            <li><strong>Configura pago insuficiente:</strong>
                <ul>
                    <li>Si el total es $50,000</li>
                    <li>Pon efectivo: $30,000</li>
                    <li>Pon tarjeta: $15,000</li>
                    <li>Deja otros en $0</li>
                </ul>
            </li>
            <li><strong>Haz clic en "Facturar"</strong></li>
            <li><strong>Verifica en consola:</strong> Deberías ver los valores de debug</li>
            <li><strong>Verifica el error:</strong> Debería aparecer "Falta: $5,000"</li>
        </ol>
    </div>

    <div class="card">
        <h2>📊 Valores Esperados en Consola</h2>
        <p>Cuando hagas clic en "Facturar", deberías ver en la consola:</p>
        
        <div class="debug-info">
🔧 DEBUG VALIDACIÓN PAGOS:
Total: 50000
Efectivo: 30000
Tarjeta: 15000
Nequi: 0
Daviplata: 0
Bancolombia: 0
Total Pagado: 45000
Diferencia: -5000
        </div>
        
        <p><strong>Si ves estos valores, la validación debería funcionar perfectamente.</strong></p>
    </div>

    <div class="card error">
        <h2>❌ Si Aún No Funciona</h2>
        <p>Si la validación todavía no funciona, puede ser por:</p>
        
        <ul>
            <li>🔍 <strong>Campos con IDs diferentes:</strong> Los campos pueden tener IDs distintos a 'efectivo', 'tarjeta', etc.</li>
            <li>🔍 <strong>JavaScript deshabilitado:</strong> Los alerts pueden estar bloqueados</li>
            <li>🔍 <strong>Caché del navegador:</strong> Puede estar usando una versión anterior del archivo</li>
            <li>🔍 <strong>Error de sintaxis:</strong> Puede haber un error que impida la ejecución</li>
        </ul>
        
        <h4>🔧 Pasos de Diagnóstico:</h4>
        <ol>
            <li>Abre la consola del navegador (F12)</li>
            <li>Busca errores de JavaScript en rojo</li>
            <li>Intenta facturar y revisa si aparecen los valores de debug</li>
            <li>Si no aparecen, reporta qué errores ves en la consola</li>
        </ol>
    </div>

    <div class="card">
        <h2>🔗 Enlaces de Prueba</h2>
        <p>Usa estos enlaces para probar:</p>
        
        <a href="../../index.php?action=registroPmesa&ida=5" class="btn btn-primary" target="_blank">
            🏠 Mesa 5 (Test Principal)
        </a>
        
        <a href="../../index.php?action=facturacionRapida" class="btn btn-success" target="_blank">
            🚀 Facturación Rápida (Comparar)
        </a>
        
        <a href="test_debug_validacion_mesa.php" class="btn btn-danger" target="_blank">
            🔧 Debug Simulado
        </a>
    </div>

    <div class="card">
        <h2>📋 Casos de Prueba</h2>
        
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div>
                <h4>❌ Caso 1: Pago Insuficiente</h4>
                <div style="background: #f8d7da; padding: 10px; border-radius: 3px;">
                    <strong>Total:</strong> $50,000<br>
                    <strong>Efectivo:</strong> $30,000<br>
                    <strong>Tarjeta:</strong> $15,000<br>
                    <strong>Otros:</strong> $0<br>
                    <strong>Total Pagado:</strong> $45,000<br>
                    <strong>Resultado:</strong> ❌ Error "Falta: $5,000"
                </div>
            </div>
            
            <div>
                <h4>✅ Caso 2: Pago Correcto</h4>
                <div style="background: #d4edda; padding: 10px; border-radius: 3px;">
                    <strong>Total:</strong> $50,000<br>
                    <strong>Efectivo:</strong> $30,000<br>
                    <strong>Nequi:</strong> $20,000<br>
                    <strong>Otros:</strong> $0<br>
                    <strong>Total Pagado:</strong> $50,000<br>
                    <strong>Resultado:</strong> ✅ Éxito "Cambio: $0"
                </div>
            </div>
        </div>
    </div>

    <div class="card success">
        <h2>🎯 Objetivo Final</h2>
        <p style="font-size: 18px;">
            <strong>La validación debe funcionar exactamente igual que en facturación rápida:</strong>
        </p>
        
        <ul>
            <li>✅ <strong>Bloquear facturación</strong> si pago < total</li>
            <li>✅ <strong>Mostrar mensaje detallado</strong> con el faltante</li>
            <li>✅ <strong>Permitir facturación</strong> si pago ≥ total</li>
            <li>✅ <strong>Mostrar confirmación</strong> con resumen de pagos</li>
            <li>✅ <strong>Imprimir automáticamente</strong> después de facturar</li>
        </ul>
        
        <div style="text-align: center; margin-top: 20px;">
            <button class="btn btn-success" onclick="mostrarInstrucciones()">
                📋 Mostrar Instrucciones Rápidas
            </button>
        </div>
    </div>

</div>

<script>
function mostrarInstrucciones() {
    alert('📋 INSTRUCCIONES RÁPIDAS:\n\n' +
          '1. Ve a Mesa 5\n' +
          '2. Agrega productos (si no hay)\n' +
          '3. Abre consola (F12)\n' +
          '4. Configura pago insuficiente:\n' +
          '   • Total: $50,000\n' +
          '   • Efectivo: $30,000\n' +
          '   • Tarjeta: $15,000\n' +
          '5. Haz clic en "Facturar"\n' +
          '6. Verifica debug en consola\n' +
          '7. Debe aparecer error "Falta: $5,000"\n\n' +
          '¡Si funciona, la validación está corregida!');
}

// Test automático al cargar
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎯 Test final de validación de mesa cargado');
    console.log('📋 Sigue las instrucciones para probar la validación');
});
</script>

</body>
</html>
