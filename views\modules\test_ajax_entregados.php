<?php

session_start();

if(!$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Test - AJAX Marcar Entregados</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
    <style>
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 3px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>

<div class="container">
    <h2>🧪 Test - AJAX <PERSON>ar <PERSON>tregados</h2>
    <hr>
    
    <div class="test-section">
        <h3>1. Test de Conectividad AJAX</h3>
        <button onclick="testPing()" class="btn btn-info">Test Ping</button>
        <div id="ping-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>2. Test de Permisos por Categoría</h3>
        <button onclick="testPermisos('cocina')" class="btn btn-warning">Test Cocina</button>
        <button onclick="testPermisos('bar')" class="btn btn-primary">Test Bar</button>
        <button onclick="testPermisos('asados')" class="btn btn-danger">Test Asados</button>
        <div id="permisos-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. Test de Obtener Pedidos Pendientes</h3>
        <button onclick="testPendientes('cocina')" class="btn btn-warning">Pendientes Cocina</button>
        <button onclick="testPendientes('bar')" class="btn btn-primary">Pendientes Bar</button>
        <button onclick="testPendientes('asados')" class="btn btn-danger">Pendientes Asados</button>
        <div id="pendientes-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>4. Simulación de Marcar Entregado</h3>
        <p><strong>Nota:</strong> Este test NO marca realmente ningún pedido como entregado, solo verifica la estructura de la petición.</p>
        <input type="number" id="pedido-test" placeholder="ID del pedido (ej: 123)" class="form-control" style="width: 200px; display: inline-block;">
        <button onclick="testMarcarEntregado()" class="btn btn-success">Simular Marcar Entregado</button>
        <div id="marcar-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>5. Información de Sesión</h3>
        <div class="info">
            <strong>Usuario:</strong> <?php echo $_SESSION["persona"] ?? 'No definido'; ?><br>
            <strong>ID:</strong> <?php echo $_SESSION["id"] ?? 'No definido'; ?><br>
            <strong>Tipo:</strong> <?php echo $_SESSION["tipo"] ?? 'No definido'; ?><br>
            <strong>Validar:</strong> <?php echo $_SESSION["validar"] ? 'Sí' : 'No'; ?>
        </div>
    </div>
    
    <hr>
    <p><a href="pedidosCocinaPendientes" class="btn btn-primary">← Volver a Pedidos Cocina</a></p>
</div>

<script>
function testPing() {
    var resultDiv = document.getElementById('ping-result');
    resultDiv.innerHTML = '<i class="glyphicon glyphicon-refresh glyphicon-spin"></i> Probando conectividad...';
    
    var xhr = new XMLHttpRequest();
    xhr.open('POST', 'views/modules/ajaxPedidosCategorias.php', true);
    xhr.setRequestHeader('Content-Type', 'application/json');
    
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                try {
                    var response = JSON.parse(xhr.responseText);
                    if (response.success) {
                        resultDiv.className = 'result success';
                        resultDiv.innerHTML = '✅ Conectividad OK<br>' +
                            '<strong>Timestamp:</strong> ' + response.timestamp + '<br>' +
                            '<strong>Usuario:</strong> ' + response.usuario;
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.innerHTML = '❌ Error: ' + response.error;
                    }
                } catch (e) {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '❌ Error al parsear respuesta: ' + e.message;
                }
            } else {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '❌ Error HTTP: ' + xhr.status;
            }
        }
    };
    
    xhr.send(JSON.stringify({
        accion: 'ping'
    }));
}

function testPermisos(categoria) {
    var resultDiv = document.getElementById('permisos-result');
    resultDiv.innerHTML = '<i class="glyphicon glyphicon-refresh glyphicon-spin"></i> Verificando permisos para ' + categoria + '...';
    
    var xhr = new XMLHttpRequest();
    xhr.open('POST', 'views/modules/ajaxPedidosCategorias.php', true);
    xhr.setRequestHeader('Content-Type', 'application/json');
    
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                try {
                    var response = JSON.parse(xhr.responseText);
                    if (response.success) {
                        resultDiv.className = 'result success';
                        resultDiv.innerHTML = '✅ Permisos para ' + categoria + ':<br>' +
                            '<strong>Puede ver:</strong> ' + (response.permisos.puede_ver ? 'Sí' : 'No') + '<br>' +
                            '<strong>Puede marcar entregado:</strong> ' + (response.permisos.puede_marcar_entregado ? 'Sí' : 'No') + '<br>' +
                            '<strong>Tipo de usuario:</strong> ' + response.usuario_tipo;
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.innerHTML = '❌ Error: ' + response.error;
                    }
                } catch (e) {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '❌ Error al parsear respuesta: ' + e.message;
                }
            } else {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '❌ Error HTTP: ' + xhr.status;
            }
        }
    };
    
    xhr.send(JSON.stringify({
        accion: 'verificar_permisos',
        categoria: categoria
    }));
}

function testPendientes(categoria) {
    var resultDiv = document.getElementById('pendientes-result');
    resultDiv.innerHTML = '<i class="glyphicon glyphicon-refresh glyphicon-spin"></i> Obteniendo pedidos pendientes de ' + categoria + '...';
    
    var xhr = new XMLHttpRequest();
    xhr.open('POST', 'views/modules/ajaxPedidosCategorias.php', true);
    xhr.setRequestHeader('Content-Type', 'application/json');
    
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                try {
                    var response = JSON.parse(xhr.responseText);
                    if (response.success) {
                        resultDiv.className = 'result success';
                        resultDiv.innerHTML = '✅ Pedidos pendientes en ' + categoria + ': ' + response.total + '<br>' +
                            '<strong>Datos:</strong> ' + JSON.stringify(response.pedidos, null, 2).substring(0, 200) + '...';
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.innerHTML = '❌ Error: ' + response.error;
                    }
                } catch (e) {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '❌ Error al parsear respuesta: ' + e.message;
                }
            } else {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '❌ Error HTTP: ' + xhr.status;
            }
        }
    };
    
    xhr.send(JSON.stringify({
        accion: 'obtener_pendientes',
        categoria: categoria
    }));
}

function testMarcarEntregado() {
    var pedidoId = document.getElementById('pedido-test').value;
    var resultDiv = document.getElementById('marcar-result');
    
    if (!pedidoId) {
        resultDiv.className = 'result error';
        resultDiv.innerHTML = '❌ Por favor ingrese un ID de pedido';
        return;
    }
    
    resultDiv.innerHTML = '<i class="glyphicon glyphicon-refresh glyphicon-spin"></i> Simulando marcar pedido ' + pedidoId + ' como entregado...';
    
    // NOTA: Este es solo un test de estructura, no marca realmente el pedido
    var xhr = new XMLHttpRequest();
    xhr.open('POST', 'views/modules/ajaxPedidosCategorias.php', true);
    xhr.setRequestHeader('Content-Type', 'application/json');
    
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                try {
                    var response = JSON.parse(xhr.responseText);
                    if (response.success) {
                        resultDiv.className = 'result success';
                        resultDiv.innerHTML = '✅ Estructura de petición correcta<br>' +
                            '<strong>Mensaje:</strong> ' + response.message + '<br>' +
                            '<strong>Nota:</strong> En producción, esto marcaría el pedido como entregado';
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.innerHTML = '❌ Error: ' + response.error;
                    }
                } catch (e) {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '❌ Error al parsear respuesta: ' + e.message;
                }
            } else {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '❌ Error HTTP: ' + xhr.status;
            }
        }
    };
    
    xhr.send(JSON.stringify({
        accion: 'marcar_entregado',
        categoria: 'cocina',
        pedido_id: pedidoId
    }));
}

// Auto-ejecutar test de ping al cargar la página
window.onload = function() {
    testPing();
};
</script>

</body>
</html>
