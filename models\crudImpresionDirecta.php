<?php
/**
 * Clase para impresión directa a impresoras de red
 * Optimizada para máxima eficiencia sin verificaciones
 */
class ImpresionDirecta {
    
    // Configuración fija de impresoras
    private static $IMPRESORAS = [
        // CONFIGURACIÓN TEMPORAL PARA PRUEBAS (Red 192.168.18.x)
        'test' => ['ip' => '*************', 'puerto' => 9100],

        // CONFIGURACIÓN PRODUCCIÓN (Red 192.168.68.x)
        'bar' => ['ip' => '**************', 'puerto' => 9100],
        'cocina' => ['ip' => '**************', 'puerto' => 9100],
        'asados' => ['ip' => '**************', 'puerto' => 9100]
    ];
    
    /**
     * Imprimir directamente en una impresora específica
     * @param string $categoria - bar, cocina, asados
     * @param string $contenido - Texto a imprimir
     * @return array - Resultado de la impresión
     */
    public static function imprimir($categoria, $contenido) {
        $categoria = strtolower(trim($categoria));
        
        if (!isset(self::$IMPRESORAS[$categoria])) {
            return [
                'success' => false,
                'error' => "Categoría '$categoria' no válida"
            ];
        }
        
        $impresora = self::$IMPRESORAS[$categoria];
        return self::enviarAImpresora($impresora['ip'], $impresora['puerto'], $contenido, $categoria);
    }
    
    /**
     * Imprimir un pedido completo distribuyendo por categorías
     * @param array $productos - Array de productos con categorías
     * @param string $mesa - Número de mesa
     * @param string $numero_pedido - Número del pedido
     * @return array - Resultados de todas las impresiones
     */
    public static function imprimirPedido($productos, $mesa, $numero_pedido = null) {
        if (!$numero_pedido) {
            $numero_pedido = 'P' . str_pad(rand(1000, 9999), 6, '0', STR_PAD_LEFT);
        }
        
        // Agrupar productos por categoría de impresora
        $productos_por_impresora = self::agruparPorImpresora($productos);
        
        $resultados = [];
        
        foreach ($productos_por_impresora as $categoria_impresora => $items) {
            $contenido = self::generarTicket($mesa, $numero_pedido, $items, $categoria_impresora);
            $resultado = self::imprimir($categoria_impresora, $contenido);
            
            $resultados[$categoria_impresora] = $resultado;
        }
        
        return $resultados;
    }
    
    /**
     * Enviar datos directamente a una impresora
     * @param string $ip
     * @param int $puerto
     * @param string $contenido
     * @param string $categoria
     * @return array
     */
    private static function enviarAImpresora($ip, $puerto, $contenido, $categoria) {
        try {
            // Conexión directa sin timeout largo
            $socket = @fsockopen($ip, $puerto, $errno, $errstr, 3);
            
            if (!$socket) {
                return [
                    'success' => false,
                    'error' => "No conecta a $ip:$puerto - $errstr",
                    'categoria' => $categoria
                ];
            }
            
            // Comandos ESC/POS optimizados
            $datos = "\x1B\x40"; // Reset impresora
            $datos .= "\x1B\x61\x01"; // Centrar texto
            $datos .= "=== MACARENA RESTAURANT ===\n";
            $datos .= "\x1B\x61\x00"; // Alinear izquierda
            $datos .= strtoupper($categoria) . " - " . date('H:i:s') . "\n";
            $datos .= "--------------------------------\n";
            $datos .= $contenido;
            $datos .= "\n--------------------------------\n\n\n";
            $datos .= "\x1D\x56\x42\x00"; // Cortar papel
            
            // Enviar datos
            $bytes_enviados = fwrite($socket, $datos);
            fclose($socket);
            
            if ($bytes_enviados > 0) {
                return [
                    'success' => true,
                    'categoria' => $categoria,
                    'ip' => $ip,
                    'bytes_enviados' => $bytes_enviados,
                    'timestamp' => date('Y-m-d H:i:s')
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'No se pudieron enviar datos',
                    'categoria' => $categoria
                ];
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'categoria' => $categoria
            ];
        }
    }
    
    /**
     * Agrupar productos por categoría de impresora
     * @param array $productos
     * @return array
     */
    private static function agruparPorImpresora($productos) {
        $agrupados = [];
        
        foreach ($productos as $producto) {
            $categoria_impresora = self::obtenerCategoriaImpresora($producto['categoria'] ?? '');
            
            if (!isset($agrupados[$categoria_impresora])) {
                $agrupados[$categoria_impresora] = [];
            }
            
            $agrupados[$categoria_impresora][] = $producto;
        }
        
        return $agrupados;
    }
    
    /**
     * Mapear categoría de producto a categoría de impresora
     * @param string $categoria_producto
     * @return string
     */
    private static function obtenerCategoriaImpresora($categoria_producto) {
        $mapeo = [
            // Bebidas -> Bar
            'bebidas' => 'bar',
            'cervezas' => 'bar',
            'licores' => 'bar',
            'vinos' => 'bar',
            'cocteles' => 'bar',
            'refrescos' => 'bar',
            'jugos' => 'bar',
            'agua' => 'bar',
            
            // Carnes -> Asados
            'carnes' => 'asados',
            'parrilla' => 'asados',
            'asados' => 'asados',
            'pescados' => 'asados',
            'mariscos' => 'asados',
            'pollo' => 'asados',
            
            // Todo lo demás -> Cocina
        ];
        
        $categoria_lower = strtolower($categoria_producto);
        return $mapeo[$categoria_lower] ?? 'cocina';
    }
    
    /**
     * Generar contenido del ticket
     * @param string $mesa
     * @param string $numero_pedido
     * @param array $productos
     * @param string $categoria_impresora
     * @return string
     */
    private static function generarTicket($mesa, $numero_pedido, $productos, $categoria_impresora) {
        $contenido = "MESA: $mesa\n";
        $contenido .= "PEDIDO: $numero_pedido\n";
        $contenido .= "HORA: " . date('H:i:s') . "\n\n";
        
        $total = 0;
        
        foreach ($productos as $producto) {
            $cantidad = $producto['cantidad'] ?? 1;
            $precio = $producto['precio'] ?? 0;
            $subtotal = $precio * $cantidad;
            $total += $subtotal;
            
            $nombre = substr($producto['nombre'] ?? 'Producto', 0, 25);
            
            $contenido .= sprintf("%-25s %2d\n", $nombre, $cantidad);
            if ($precio > 0) {
                $contenido .= sprintf("  $%s\n", number_format($subtotal, 0));
            }
        }
        
        if ($total > 0) {
            $contenido .= "\nTOTAL: $" . number_format($total, 0) . "\n";
        }
        
        return $contenido;
    }
    
    /**
     * Test rápido de conectividad
     * @return array
     */
    public static function testConectividad() {
        $resultados = [];
        
        foreach (self::$IMPRESORAS as $categoria => $config) {
            $socket = @fsockopen($config['ip'], $config['puerto'], $errno, $errstr, 2);
            
            if ($socket) {
                fclose($socket);
                $resultados[$categoria] = [
                    'status' => 'online',
                    'ip' => $config['ip'],
                    'puerto' => $config['puerto']
                ];
            } else {
                $resultados[$categoria] = [
                    'status' => 'offline',
                    'ip' => $config['ip'],
                    'puerto' => $config['puerto'],
                    'error' => $errstr
                ];
            }
        }
        
        return $resultados;
    }
    
    /**
     * Obtener configuración de impresoras
     * @return array
     */
    public static function obtenerImpresoras() {
        return self::$IMPRESORAS;
    }
}
?>
