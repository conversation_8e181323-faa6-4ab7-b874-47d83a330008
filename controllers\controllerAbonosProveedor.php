<?php 
ob_start();
class controllerAbonosProveedor extends <PERSON>v<PERSON><PERSON><PERSON>roller
{
	#BUSCAR TODOS LAS DUEDA DE LOS ProveedorS
	#----------------------------------				 
	 public function deudaTotalController()
		{	//echo "<script>alert('entro controle buscar abono')</script>";
		 $respuesta =DatosAbonosProveedor::deudaProveedorModel();
		 $con =0;
		 $control =0;
		   foreach($respuesta as $row => $item)
			{	if ($control==0) 
				{
					$deuda =DatosAbonosProveedor::buscardeudaFacturaPModel($item["csid"]);	
				 $Proveedor[$con]= array('cprid' => $item["cprid"],
										 'cprcompras_suministro_id' => $item["cprcompras_suministro_id"] ,
										 'cprabonos_proveedores_id' => $item["cprabonos_proveedores_id"],	
										 'cprcuenta' => $deuda["cprcuenta"] ,	
										 'csid' => $item["csid"] ,	
										 'csproveedor_id' => $item["csproveedor_id"] ,	
										 'csvalor_compra' => $item["csvalor_compra"] ,	
										 'csfecha_hora' => $item["csfecha_hora"] ,	
										 'csnumero_factura_compra' => $item["csnumero_factura_compra"] ,	
										 'csfecha_modificacion' => $item["csfecha_modificacion"] ,	
										 'prid' => $item["prid"] ,	
										 'prnombre' => $item["prnombre"] ,	
										 'prnit' => $item["prnit"] ,	
										 'prrepresentante' => $item["prrepresentante"] ,	
										 'prdireccion' => $item["prdireccion"] ,	
										 'prtelefonos' => $item["prtelefonos"]);
				}
			 else
				{								
				 if ($Proveedor[$con]['csproveedor_id'] == $item["csproveedor_id"] ) 
					{
				 	 //echo "<script>alert(' if deuda: ".$Proveedor[$con]['csid']."')</script>";	
				 	 $deuda =DatosAbonosProveedor::buscardeudaFacturaPModel($item["csid"]);
					 $Proveedor[$con]['cprcuenta']= $Proveedor[$con]['cprcuenta'] + $deuda["cprcuenta"];	
					}
			 	 else
					{//echo "<script>alert('else deuda: ".$item['csid']."')</script>";
					 $deuda =DatosAbonosProveedor::buscardeudaFacturaPModel($item["csid"]);		 	
					 $con++;	
					 $Proveedor[$con]= array('cprid' => $item["cprid"],
											 'cprcompras_suministro_id' => $item["cprcompras_suministro_id"] ,
											 'cprabonos_proveedores_id' => $item["cprabonos_proveedores_id"],	
											 'cprcuenta' => $deuda["cprcuenta"] ,	
											 'csid' => $item["csid"] ,	
											 'csproveedor_id' => $item["csproveedor_id"] ,	
											 'csvalor_compra' => $item["csvalor_compra"] ,	
											 'csfecha_hora' => $item["csfecha_hora"] ,	
											 'csnumero_factura_compra' => $item["csnumero_factura_compra"] ,	
											 'csfecha_modificacion' => $item["csfecha_modificacion"] ,	
											 'prid' => $item["prid"] ,	
											 'prnombre' => $item["prnombre"] ,	
											 'prnit' => $item["prnit"] ,	
											 'prrepresentante' => $item["prrepresentante"] ,	
											 'prdireccion' => $item["prdireccion"] ,	
											 'prtelefonos' => $item["prtelefonos"]);
					}
				} 
				
			 }
		 for ($i=0; $i <=$con ; $i++) 
			{ 	//echo '<br>';		//echo " deuda: ". $cliente[$i]['dcuenta'];
			 $s=$i+1;
			 echo'
			 	<tr>
					<td>'.$s.'</td>
					<td>'.$Proveedor[$i]["prnombre"].'</td>											
					<td>'.$Proveedor[$i]["prnit"].'</td>	
					<td>'.$Proveedor[$i]["cprcuenta"].'</td>
					<td><a href="index.php?action=deudaProveedor&Proveedor='.$Proveedor[$i]["prnit"].'"><button>Ver</button></a> </td>			
				</tr>';
				$_SESSION["prnit"]=$Proveedor[$i]["prnit"];
			}
		}
	#---------------------------------
	##ASIGNAR Proveedor
	#------------------------------------
	 public function proveedorController()
		{	//echo "<script>alert('entro controle')</script>";
		 if(isset($_GET["prnit"]))
			{
				$_SESSION["prnit"] = $_GET["prnit"];
				header("location:index.php?action=deudaProveedor");	
			}
		}			
	#---------------------------------
	#ABONOS  id, id_personas, turnos_cajeros_id, fecha_bono, descripsion, valor
	#--------------------------------
	 public function abonoProveedorController()
		{//echo " <script>  alert('Controles Abonos descripsion: ".$_POST["descripsion"]." ')  </script> ";
			//session_start();			
		 if (isset($_POST["valorAbono"])) 
			{
			 $datosController= array('descripcion' => $_POST["descripcion"],
			 					 'turno' => $_SESSION["turno"] ,
			 					 'deuda' => $_GET["id_deuda"] ,
			 					 'valorAbono' => $_POST["valorAbono"]);
			 //echo " <script>  alert('  turno: ".$datosController["turno"]."  deuda: ".$datosController["deuda"]."  valorAbono: ".$datosController["valorAbono"]."')  </script> ";
			 $respuesta =DatosAbonosProveedor::abonoProveedorModel($datosController, "abonos_proveedores");
			 if($respuesta == "success")
				{	//$vaciarMesa = DatosFacturaAja::cancelarController();	//header("location:mesa");
					echo' <script>alert("Pago realizado exitosamente");window.open("pdf1","_blank");location.href ="index.php?action=deudaProveedor";</script>';	
				}
			 else
				{	echo' <script>alert("No se guardo, intente nuevamente ");</script>';
					//header("location:index.php?action=okPm");	
				}		
			}
		 //else echo' <script>alert("error");</script>';
		}
	#---------------------------------
	#BUSCAR DEUDA Proveedor
	#------------------------------------
	 public function buscardeudaProveedorController()
		{	//echo "<script>alert('entro Controller Deuda del Proveedor ".$datosController."')</script>";
		 $datosController = $_SESSION["prnit"];
		 //$datosController = "57461800"; 				
		 $respuesta = DatosAbonosProveedor::buscardeudaProveedorModel($datosController);
		 if ($respuesta==0) 
		 {echo' <script>alert("No aparece deuda, verificar nit");location.href ="index.php?action=buscarDeudaProveedor";</script>';}				
		 echo' Sr/a: '.$_SESSION ["Proveedor"]. ' <br>
		 <table border="1" class="table table-hover">		
		  <thead>											
		  	<tr>										
		  		<th>No Factura</th>
		  		<th>Fecha</th>
		  		<th>Valor Factura</th>	
		  		<th>Abonos</th>	
		  		<th>Debe</th>	
		  		<th>Pago</th>
		  	</tr>
		  </thead>
		  <tbody>';
		 $cont = 0;
	 	 $abonos=0;     // Guardar total de todos los abonos
		 $ValorF=0;     // Guardar total de todos los valor de factura
		 $debes=0;      // Guardar total de todos los lo q deben
		 foreach ($respuesta as $rows => $item) 
			{	//echo "<script>alert('foreach".$item["cprcompras_suministro_id"]."')</script>";
			 $abono = DatosAbonosProveedor::abonoPModel($item["cprcompras_suministro_id"]);
			 $abonoTotal=0; // si tiene abono								
		 	 if ($abono>0)
				{//	echo "<script>alert('if de abono ".$item["cprcompras_suministro_id"]."')</script>";
				 foreach ($abono as $rows => $value) 
					{ $abonoTotal= $abonoTotal + $value["avalor"]; }
				}
			 $debe = $item["csvalor_compra"] -$abonoTotal; //lo q se debe por factura
			 echo'
				<tr>	
					<td>'.$item["csnumero_factura_compra"].'</td>					
					<td>'.$item["csfecha_hora"].'</td>						
					<td>'.$item["csvalor_compra"].'</td>
					<td>'.$abonoTotal.'</td>
					<td>'.$debe.'</td>
					<td><a href="index.php?action=acobonoDeudaProveedor&id_deuda='.$item["csid"].'">Abonar</a></td>	
				</tr>';												
			 $abonos = $abonos + $abonoTotal;
			 $ValorF = $ValorF + $item["csvalor_compra"];
			 $debes = $debes + $debe;
			 $_SESSION["deudas"][$cont] = array('id_credito' =>$item["cprid"], 
											'csvalor_compra' =>$item["csvalor_compra"]);
						}
			 $_SESSION ["Proveedor"]= $item ["prnombre"];
			 echo' 
				<tr class="filaTotal"> 
					<td colspan="2">Total</td> 
					<td> '.$ValorF.' </td> 
					<td>'.$abonos.'</td> 
					<td colspan="2">'.$debes .'</td> 
				</tr>
			  </tbody>
			</table> ';
			 $cont++;
			 if ($abono>0) 
				{									
			 	 echo'
				 	<table border="1" class="table table-hover">		
						<caption>Abono hecho a la factura</caption>
						<thead>											
							<tr>										
								<th>No </th>
								<th>aturnos_cajeros_id</th>
								<th>afecha_bono </th>	
								<th>adescripsion</th>	
								<th>avalor</th>											
							</tr>
						</thead>
						<tbody> ';
						 $c=1;
						 foreach ($abono as $rows => $ite) 
			 		 		{
			 		 		 echo'
			 		 			<tr>											
									<td> '.$c.' </td> 
									<td>'.$ite["aturnos_cajeros_id"].'</td> 
									<td>'.$ite["afecha_abono"].'</td> 
									<td>'.$ite["adescripcion"].'</td> 
									<td>'.$ite["avalor"].'</td> 												
								</tr>';
							 $c++;
			 		 		}
						 echo'
							<tr class="filaTotal"> 
									<td colspan="2">Total</td> 
									<td> '.$ValorF.' </td> 
									<td>'.$abonos.'</td> 
									<td colspan="2">'.$debes .'</td> 
							</tr>
						  </tbody>
						 </table> ';							
			 		 }	
		}
	#---------------------------------------------<input type="submit" value="Abono">
	#BUSCAR ABONOS
	#----------------------------------				 
	 public function buscarAbonosController()
		{//	echo "<script>alert('entro controle buscar abono')</script>";
		 	//session_start();
		 if(isset($_POST["buscarDeudaProveedor"]))
			{
			 	$_SESSION["prnit"]= $_POST['buscarDeudaProveedor'];
			 	header("location:deudaProveedor");
			}
		}
	#---------------------------------
	#BORRAR PEDIDO MESA VENTA
	#------------------------------------
	 public function borrarAbonosProveedorController()
		{	//echo "<script>alert('entro controle')</script>";
		 if(isset($_GET["idBorrar"]))
			{//	echo "<script>alert('entro controle IF')</script>";
				$datosController =array('fecha_hora' => $_GET["fecha_horaBorrar"], 
									'productos_id' => $_GET["idBorrar"]);				
				$respuesta = DatosAbonosProveedor::borrarAbonosProveedorModel($datosController, "producto_vendido_mesa");
				if($respuesta == "success")
					{	header("location:index.php?action=registroPmesa");	}
			}
		}			
	#--------------------------------- 
	#ASIGNAR Proveedor
	#----------------------------------	 
	 public function asignarProveedorController()
		{	//echo "<script>alert('entro controle')</script>";
		 	//session_start();
		 if(isset($_POST["buscarDeudaProveedor"]))
			{
			 	$_SESSION["prnit"]= $_POST['buscarDeudaProveedor'];
			 	header("location:deudaProveedor");
			}
		}
	#---------------------------------

}			
	