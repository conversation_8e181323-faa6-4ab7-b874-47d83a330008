<?php

session_start();

if(!isset($_SESSION["validar"]) || !$_SESSION["validar"]) {
    header("location:index.php?action=ingresar");
    exit();
}

require_once "models/crudPedidosCategorias.php";

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Test Consultas Corregidas</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
</head>
<body>

<div class="container">
    <h2>🧪 Test: Consultas Corregidas</h2>
    <hr>
    
    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">🍺 Test BAR - Modelo Directo</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                $pedidos_bar = DatosPedidosCategorias::obtenerPedidosPendientesModel('bar');
                echo "<strong>Pedidos encontrados para BAR:</strong> " . count($pedidos_bar) . "<br><br>";
                
                if (count($pedidos_bar) > 0) {
                    echo "<table class='table table-striped'>";
                    echo "<thead><tr><th>Pedido</th><th>Mesa</th><th>Productos</th><th>Total</th></tr></thead>";
                    echo "<tbody>";
                    foreach ($pedidos_bar as $pedido) {
                        echo "<tr>";
                        echo "<td>{$pedido['numero_pedido']}</td>";
                        echo "<td>{$pedido['mesa_numero']}</td>";
                        echo "<td>{$pedido['productos_detalle']}</td>";
                        echo "<td>$" . number_format($pedido['total_precio'], 0) . "</td>";
                        echo "</tr>";
                    }
                    echo "</tbody></table>";
                } else {
                    echo "<div class='alert alert-warning'>❌ No se encontraron pedidos para BAR</div>";
                }
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>❌ Error en consulta BAR: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-warning">
        <div class="panel-heading">
            <h3 class="panel-title">🍳 Test COCINA - Modelo Directo</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                $pedidos_cocina = DatosPedidosCategorias::obtenerPedidosPendientesModel('cocina');
                echo "<strong>Pedidos encontrados para COCINA:</strong> " . count($pedidos_cocina) . "<br><br>";
                
                if (count($pedidos_cocina) > 0) {
                    echo "<table class='table table-striped'>";
                    echo "<thead><tr><th>Pedido</th><th>Mesa</th><th>Productos</th><th>Total</th></tr></thead>";
                    echo "<tbody>";
                    foreach ($pedidos_cocina as $pedido) {
                        echo "<tr>";
                        echo "<td>{$pedido['numero_pedido']}</td>";
                        echo "<td>{$pedido['mesa_numero']}</td>";
                        echo "<td>{$pedido['productos_detalle']}</td>";
                        echo "<td>$" . number_format($pedido['total_precio'], 0) . "</td>";
                        echo "</tr>";
                    }
                    echo "</tbody></table>";
                } else {
                    echo "<div class='alert alert-warning'>❌ No se encontraron pedidos para COCINA</div>";
                }
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>❌ Error en consulta COCINA: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-success">
        <div class="panel-heading">
            <h3 class="panel-title">🎯 Test Controlador</h3>
        </div>
        <div class="panel-body">
            <?php
            try {
                require_once "controllers/controllerPedidosCategorias.php";
                $controller = new ControllerPedidosCategorias();
                
                echo "<h4>BAR (Controlador):</h4>";
                $pedidos_bar_ctrl = $controller->mostrarPedidosPendientesController('bar');
                echo "<p><strong>Resultado:</strong> " . count($pedidos_bar_ctrl) . " pedidos encontrados</p>";
                
                echo "<h4>COCINA (Controlador):</h4>";
                $pedidos_cocina_ctrl = $controller->mostrarPedidosPendientesController('cocina');
                echo "<p><strong>Resultado:</strong> " . count($pedidos_cocina_ctrl) . " pedidos encontrados</p>";
                
                echo "<h4>Estadísticas BAR:</h4>";
                $stats_bar = $controller->obtenerEstadisticasDiaController('bar');
                echo "<p><strong>Pendientes:</strong> {$stats_bar['pendientes']}</p>";
                echo "<p><strong>Entregados hoy:</strong> {$stats_bar['entregados_hoy']}</p>";
                
                echo "<h4>Estadísticas COCINA:</h4>";
                $stats_cocina = $controller->obtenerEstadisticasDiaController('cocina');
                echo "<p><strong>Pendientes:</strong> {$stats_cocina['pendientes']}</p>";
                echo "<p><strong>Entregados hoy:</strong> {$stats_cocina['entregados_hoy']}</p>";
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>❌ Error en controlador: " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
    </div>
    
    <div class="panel panel-primary">
        <div class="panel-heading">
            <h3 class="panel-title">🔧 Test Vista Completa</h3>
        </div>
        <div class="panel-body">
            <p>Simulando la vista completa de pedidos pendientes:</p>
            
            <h4>Vista BAR:</h4>
            <div style="border: 1px solid #ddd; padding: 10px; margin: 10px 0;">
                <?php
                try {
                    ob_start();
                    $controller->vistaPedidosPendientesController('bar');
                    $vista_bar = ob_get_clean();
                    echo $vista_bar;
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>❌ Error en vista BAR: " . $e->getMessage() . "</div>";
                }
                ?>
            </div>
            
            <h4>Vista COCINA:</h4>
            <div style="border: 1px solid #ddd; padding: 10px; margin: 10px 0;">
                <?php
                try {
                    ob_start();
                    $controller->vistaPedidosPendientesController('cocina');
                    $vista_cocina = ob_get_clean();
                    echo $vista_cocina;
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>❌ Error en vista COCINA: " . $e->getMessage() . "</div>";
                }
                ?>
            </div>
        </div>
    </div>
    
    <div class="panel panel-danger">
        <div class="panel-heading">
            <h3 class="panel-title">🔍 Debug Detallado</h3>
        </div>
        <div class="panel-body">
            <h4>Consulta SQL Directa - BAR:</h4>
            <?php
            try {
                $stmt = Conexion::conectar()->prepare("
                    SELECT DISTINCT
                        p.id as pedido_id,
                        p.numero_pedido,
                        p.fecha_envio,
                        p.mesa_id,
                        m.nombre as mesa_numero,
                        COUNT(pvm.id) as total_productos,
                        GROUP_CONCAT(
                            CONCAT(pr.nombre, ' x', pvm.cantidad)
                            ORDER BY pr.nombre
                            SEPARATOR ', '
                        ) as productos_detalle,
                        SUM(pvm.cantidad * pr.precio) as total_precio
                    FROM pedidos p
                    INNER JOIN mesas m ON p.mesa_id = m.id
                    INNER JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
                    INNER JOIN productos pr ON pvm.productos_id = pr.id
                    WHERE p.estado = 'enviado'
                    AND pr.categoria = 'bar'
                    GROUP BY p.id, p.numero_pedido, p.fecha_envio, p.mesa_id, m.nombre
                    ORDER BY p.fecha_envio ASC
                ");
                $stmt->execute();
                $debug_bar = $stmt->fetchAll(PDO::FETCH_ASSOC);

                echo "<p><strong>Resultados:</strong> " . count($debug_bar) . " pedidos</p>";
                if (count($debug_bar) > 0) {
                    foreach ($debug_bar as $pedido) {
                        echo "<p>- {$pedido['numero_pedido']} | Mesa: {$pedido['mesa_numero']} | Productos: {$pedido['productos_detalle']}</p>";
                    }
                } else {
                    echo "<p style='color: red;'>❌ No se encontraron pedidos con productos de BAR</p>";
                }
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
            }
            ?>

            <h4>Consulta SQL Directa - COCINA:</h4>
            <?php
            try {
                $stmt = Conexion::conectar()->prepare("
                    SELECT DISTINCT
                        p.id as pedido_id,
                        p.numero_pedido,
                        p.fecha_envio,
                        p.mesa_id,
                        m.nombre as mesa_numero,
                        COUNT(pvm.id) as total_productos,
                        GROUP_CONCAT(
                            CONCAT(pr.nombre, ' x', pvm.cantidad)
                            ORDER BY pr.nombre
                            SEPARATOR ', '
                        ) as productos_detalle,
                        SUM(pvm.cantidad * pr.precio) as total_precio
                    FROM pedidos p
                    INNER JOIN mesas m ON p.mesa_id = m.id
                    INNER JOIN producto_vendido_mesa pvm ON p.id = pvm.pedidos_id
                    INNER JOIN productos pr ON pvm.productos_id = pr.id
                    WHERE p.estado = 'enviado'
                    AND pr.categoria = 'cocina'
                    GROUP BY p.id, p.numero_pedido, p.fecha_envio, p.mesa_id, m.nombre
                    ORDER BY p.fecha_envio ASC
                ");
                $stmt->execute();
                $debug_cocina = $stmt->fetchAll(PDO::FETCH_ASSOC);

                echo "<p><strong>Resultados:</strong> " . count($debug_cocina) . " pedidos</p>";
                if (count($debug_cocina) > 0) {
                    foreach ($debug_cocina as $pedido) {
                        echo "<p>- {$pedido['numero_pedido']} | Mesa: {$pedido['mesa_numero']} | Productos: {$pedido['productos_detalle']}</p>";
                    }
                } else {
                    echo "<p style='color: red;'>❌ No se encontraron pedidos con productos de COCINA</p>";
                }
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
            }
            ?>

            <h4>Verificación de Datos Base:</h4>
            <?php
            // Verificar pedidos enviados
            $stmt = Conexion::conectar()->prepare("SELECT id, numero_pedido, estado FROM pedidos WHERE estado = 'enviado'");
            $stmt->execute();
            $pedidos_enviados = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "<p><strong>Pedidos con estado 'enviado':</strong> " . count($pedidos_enviados) . "</p>";
            foreach ($pedidos_enviados as $p) {
                echo "<p>- {$p['numero_pedido']} (ID: {$p['id']})</p>";
            }

            // Verificar productos por pedido
            foreach ($pedidos_enviados as $p) {
                $stmt = Conexion::conectar()->prepare("
                    SELECT pr.nombre, pr.categoria, pvm.cantidad
                    FROM producto_vendido_mesa pvm
                    JOIN productos pr ON pvm.productos_id = pr.id
                    WHERE pvm.pedidos_id = ?
                ");
                $stmt->execute([$p['id']]);
                $productos = $stmt->fetchAll(PDO::FETCH_ASSOC);
                echo "<p><strong>Productos del pedido {$p['numero_pedido']}:</strong></p>";
                foreach ($productos as $prod) {
                    echo "<p>&nbsp;&nbsp;- {$prod['nombre']} (Categoría: '{$prod['categoria']}') x{$prod['cantidad']}</p>";
                }
            }
            ?>
        </div>
    </div>

    <hr>
    <div class="row">
        <div class="col-md-3">
            <a href="index.php?action=pedidosBarPendientes" class="btn btn-info btn-block">🍺 Ir a Bar Real</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=pedidosCocinaPendientes" class="btn btn-warning btn-block">🍳 Ir a Cocina Real</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=debug_consultas_pendientes" class="btn btn-success btn-block">🔍 Debug SQL</a>
        </div>
        <div class="col-md-3">
            <a href="index.php?action=debug_pedidos_creados" class="btn btn-primary btn-block">📋 Ver Pedidos</a>
        </div>
    </div>
</div>

</body>
</html>
