<?php
	if(!$_SESSION["validar"])
		{
			header("location:index.php?action=ingresar");
			exit();
		}
?>
<script type="text/javascript">
	// ------Cliente--------------
		function buscarPlaca(inputString) 
		 {
			$.post("views/modules/ajaxdeudacliente.php",{ idEtapaProceso:inputString },function(data){$("#actuaciones").html(data);})
		 }
	// ------Cliente fin--------------		
	// ------FACTURAR--------------
		function buscardeudacliente() 		
			{	
				//alert("Buscar factura funtion"); 
				var cedula = document.deuda.buscarDeudaCliente.value;
				if (confirm('confirme  Buscar cedula?')) 
									{		//setTimeout('location.href="views/modules/ajaxFactura.php"',500);	
												$("#destino1").load("views/modules/ajaxdeudacliente.php", {cedula: cedula}, function(){
				        						 //alert("recibidos los datos por ajax efectivo"); 		onchange="buscarPlaca(this.value);" 
				     						});	
									}
			} 
	// ------FACTURAR FIN//		".$_SESSION["mesa"]."   <div id="destino1" name="destino1" ></div><label for=""><b>factura</b></label><input type="text" placeholder="No factura" class="mayuscula"  name="factura" id="factura" size="38"   required><br>
</script>	
<div class="row">
	    <div class="col-md-12">	  
	      	<form  method="post" name="deuda">	
				<label for=""><b>Cedula : </b></label><input type="text" placeholder="No Cedula" class="mayuscula"  name="buscarDeudaProveedor" id="buscarDeudaProveedor" size="18" autofocus="autofocus"  required><br>
				<input type="submit" value="Enviar" >
			</form>
	    </div>
	</div>
  	<?php
	$devolucion = new controllerAbonosProveedor();
	$devolucion -> asignarProveedorController();
	//$devolucion -> buscardeudaClienteController();
	
	//$devolucion -> actualizarDevolucionController();  buscardeudaClienteController
	?>