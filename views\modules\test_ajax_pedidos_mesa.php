<?php
// Test directo del AJAX para obtener pedidos de mesa
session_start();
if (!isset($_SESSION["usuario"])) {
    $_SESSION["usuario"] = 1;
    $_SESSION["tipo_usuario"] = 1;
    $_SESSION["perfil"] = "administrador";
}

$mesaId = isset($_GET['mesa']) ? $_GET['mesa'] : 2;

echo "<h1>🧪 Test AJAX - Obtener Pedidos Mesa $mesaId</h1>";

echo "<h3>1. 📡 Test Directo con cURL</h3>";

// Simular la petición AJAX
$postData = http_build_query([
    'obtener_pedidos_mesa' => true,
    'mesa_id' => $mesaId
]);

$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => 'Content-Type: application/x-www-form-urlencoded',
        'content' => $postData
    ]
]);

$url = 'https://macarena.anconclub.com/views/modules/ajaxEstadoPedidos.php';
$response = file_get_contents($url, false, $context);

echo "<strong>Respuesta del AJAX:</strong><br>";
echo "<div style='background: #f8f9fa; padding: 10px; border: 1px solid #ddd; margin: 10px 0; font-family: monospace;'>";
echo htmlspecialchars($response);
echo "</div>";

echo "<h3>2. 🔍 Análisis de la Respuesta</h3>";
$json = json_decode($response, true);
if ($json !== null) {
    echo "✅ JSON válido<br>";
    echo "<strong>Datos parseados:</strong><br>";
    echo "<pre>" . print_r($json, true) . "</pre>";
    
    if (is_array($json)) {
        echo "<strong>Número de pedidos:</strong> " . count($json) . "<br>";
    }
} else {
    echo "❌ No es JSON válido<br>";
    echo "<strong>Error JSON:</strong> " . json_last_error_msg() . "<br>";
}

echo "<h3>3. 🧪 Test con JavaScript</h3>";
?>

<button onclick="testAjaxJS()" style="background: #007bff; color: white; padding: 10px; border: none; cursor: pointer;">🔄 Probar con JavaScript</button>

<div id="resultado-js" style="background: #f8f9fa; padding: 10px; border: 1px solid #ddd; margin: 10px 0; min-height: 100px;">
    Haz clic en el botón para probar...
</div>

<h3>4. 📊 Comparación con Consulta Directa</h3>
<?php
require_once "../../models/conexion.php";
require_once "../../models/crudEstadoPedidos.php";
require_once "../../controllers/controllerEstadoPedidos.php";

try {
    $controller = new ControllerEstadoPedidos();
    $pedidosDirectos = $controller->obtenerPedidosMesaController($mesaId);
    
    echo "<strong>Consulta directa del controlador:</strong><br>";
    echo "Pedidos encontrados: " . count($pedidosDirectos) . "<br>";
    
    if (!empty($pedidosDirectos)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Número</th><th>Estado</th><th>Mesa</th><th>Productos</th></tr>";
        foreach ($pedidosDirectos as $p) {
            echo "<tr>";
            echo "<td>" . $p['id'] . "</td>";
            echo "<td>" . $p['numero_pedido'] . "</td>";
            echo "<td>" . $p['estado'] . "</td>";
            echo "<td>" . $p['mesa_numero'] . "</td>";
            echo "<td>" . $p['total_productos'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "❌ Error en consulta directa: " . $e->getMessage();
}

echo "<br><a href='debug_mesa_pedidos.php?mesa=$mesaId' style='background: #28a745; color: white; padding: 10px; text-decoration: none;'>🔍 Debug Completo</a>";
echo "<a href='../../index.php?action=registroPmesa&ida=$mesaId' style='background: #007bff; color: white; padding: 10px; text-decoration: none; margin-left: 10px;'>🔙 Volver a Mesa</a>";
?>

<script>
function testAjaxJS() {
    const mesaId = <?=$mesaId?>;
    const resultadoDiv = document.getElementById('resultado-js');
    
    resultadoDiv.innerHTML = '⏳ Enviando petición AJAX...';
    
    fetch('ajaxEstadoPedidos.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'obtener_pedidos_mesa=true&mesa_id=' + mesaId
    })
    .then(response => {
        console.log('Response status:', response.status);
        return response.text();
    })
    .then(text => {
        console.log('Response text:', text);
        
        let html = '<strong>📡 Respuesta recibida:</strong><br>';
        html += '<div style="background: #fff; padding: 5px; border: 1px solid #ccc; margin: 5px 0; font-family: monospace; font-size: 12px;">';
        html += text.substring(0, 500) + (text.length > 500 ? '...' : '');
        html += '</div>';
        
        try {
            const data = JSON.parse(text);
            html += '<strong>✅ JSON válido:</strong><br>';
            html += '<div style="background: #d4edda; padding: 5px; border: 1px solid #c3e6cb; margin: 5px 0;">';
            html += 'Tipo: ' + typeof data + '<br>';
            html += 'Longitud: ' + (Array.isArray(data) ? data.length : 'N/A') + '<br>';
            html += 'Contenido: ' + JSON.stringify(data, null, 2);
            html += '</div>';
        } catch (e) {
            html += '<strong>❌ Error JSON:</strong><br>';
            html += '<div style="background: #f8d7da; padding: 5px; border: 1px solid #f5c6cb; margin: 5px 0;">';
            html += e.message;
            html += '</div>';
        }
        
        resultadoDiv.innerHTML = html;
    })
    .catch(error => {
        resultadoDiv.innerHTML = '<strong>❌ Error de red:</strong><br>' + error.message;
    });
}
</script>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}

table {
    width: 100%;
    max-width: 600px;
}

th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #f0f0f0;
}

pre {
    background: #f8f9fa;
    padding: 10px;
    border: 1px solid #ddd;
    overflow-x: auto;
    max-height: 300px;
}
</style>
